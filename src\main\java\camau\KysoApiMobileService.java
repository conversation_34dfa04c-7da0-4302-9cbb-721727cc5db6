package camau;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.gson.Gson;
import com.google.gson.JsonArray;
import com.google.gson.JsonObject;
import com.google.gson.JsonParser;
import com.itextpdf.text.DocumentException;
import com.itextpdf.text.pdf.*;
import com.vnpt.vnptkyso.pdf.PdfHashSigner;
import com.vnpt.vnptkyso.pdf.PdfSignatureComment;
import com.vnpt.vnptkyso.pdf.PdfSignatureView;
import com.vnpt.vnptkyso.signer.DocumentType;
import com.vnpt.vnptkyso.signer.HashSignerFactory;
import com.vnpt.vnptkyso.signer.IHashSginer;
import com.vnpt.vnptkyso.signer.SignatureParameter;
import com.vnpt.vnptkyso.utils.MessageDigestAlgorithm;
import net.sf.json.JSONObject;
import okhttp3.*;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;
import org.bouncycastle.util.encoders.Hex;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.w3c.dom.Document;
import org.w3c.dom.Node;
import org.w3c.dom.NodeList;
import smartca.SmartCAContants;
import smartca.model.*;
import smartca.model.request.SmartCAQD769ConfirmRequestDTO;
import smartca.model.request.SmartCAQD769GetCertificateRequestDTO;
import smartca.model.request.SmartCAQD769SignRequestDTO;
import smartca.model.request.SmartCAQD769SignV1RequestDTO;
import smartca.model.response.*;
import smartca.request.CertificateRequest;
import smartca.request.GetTranInfoRequest;
import smartca.service.OTPService;
import smartca.service.SignHashESeal;
import smartca.service.SmartCADAO;
import smartca.service.SmartCaESealService;
import smartca.ultils.SmartCALogUtils;
import smartca.ultils.Ultils;

import javax.imageio.ImageIO;
import javax.xml.parsers.ParserConfigurationException;
import javax.xml.transform.OutputKeys;
import javax.xml.transform.Transformer;
import javax.xml.transform.TransformerException;
import javax.xml.transform.TransformerFactory;
import javax.xml.transform.dom.DOMSource;
import javax.xml.transform.stream.StreamResult;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.StringWriter;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.logging.Level;
import java.util.logging.Logger;

import static com.fasterxml.jackson.databind.DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES;

@Component
public class KysoApiMobileService {
    @Autowired
    SmartCADAO smartCADAO;

    @Autowired
    SmartCALogUtils smartCALogUtils;

    @Autowired
    private dmthamsodonvi.thamsodonviDAO thamsodonviDAO;


    public TranInfoResponse getTranInfo(CMUSmartCADetail smartCADetail, String tranId) {
        String respContent = "";
        Gson gson = new Gson();
//        CloseableHttpClient client = Ultils.initSecureClient();
        CloseableHttpClient client = HttpClients.createDefault();
        GetTranInfoRequest tranInfoReq = new GetTranInfoRequest(tranId);
        String para = gson.toJson(tranInfoReq);
        TranInfoResponse tranInfoRes = new TranInfoResponse();

        try {
            HttpPost request = new HttpPost(smartCADetail.getUrl() + "/csc/credentials/gettraninfo");
            StringEntity params = new StringEntity(para);
            request.addHeader("content-type", "application/json");
            String oauth = "Bearer" + " " + smartCADetail.getAccessToken();
            request.addHeader("Authorization", oauth);
            request.setEntity(params);
            CloseableHttpResponse response = client.execute(request);
            respContent = EntityUtils.toString(response.getEntity());
            int code = response.getStatusLine().getStatusCode();
            if (200 != code) {
                System.out.println(respContent);
                return null;
            }
            tranInfoRes = gson.fromJson(respContent, TranInfoResponse.class);
            return tranInfoRes;
        } catch (Exception ex) {
            ex.printStackTrace();
        } finally {
            try {
                client.close();
            } catch (IOException e) {
                e.printStackTrace();
            }
        }

        return null;
    }

    public  SmartCACredential getCredential(CMUSmartCADetail smartCADetail) {
        String respContent = "";
        Gson gson = new Gson();
//        CloseableHttpClient client = Ultils.initSecureClient();
        CloseableHttpClient client = HttpClients.createDefault();
        String para = "{}";
        SmartCACredential smartCACredential = new SmartCACredential();
        try {
            HttpPost request = new HttpPost(smartCADetail.getUrl() + "/csc/credentials/list");
            StringEntity params = new StringEntity(para);
            request.addHeader("content-type", "application/json");
            String oauth = "Bearer" + " " + smartCADetail.getAccessToken();
            request.addHeader("Authorization", oauth);
            request.setEntity(params);
            CloseableHttpResponse response = client.execute(request);
            respContent = EntityUtils.toString(response.getEntity());
            int code = response.getStatusLine().getStatusCode();
            if (200 != code) {
//                System.out.println(respContent);
                smartCACredential.setCode(code);
                return smartCACredential;
            } else {
                smartCACredential = gson.fromJson(respContent, SmartCACredential.class);
            }
            return smartCACredential;
        } catch (Exception ex) {
            ex.printStackTrace();
        } finally {
            try {
                client.close();
            } catch (IOException e) {
                e.printStackTrace();
            }
        }

        return null;
    }

    Credential getCredential(SmartCADetail smartCADetail) {
        String respContent = "";
        Gson gson = new Gson();
        CloseableHttpClient client = Ultils.initSecureClient();
        String para = "{}";
        Credential credential = new Credential();
        try {
            HttpPost request = new HttpPost(smartCADetail.getUrlESeal() + SmartCAContants.SmartCAEsealUrl.GET_CREDENTIALS.getUrl());
            StringEntity params = new StringEntity(para);
            request.addHeader("content-type", "application/json");
            String oauth = Ultils.BEARER + " " + smartCADetail.getAccessToken();
            request.addHeader(Ultils.AUTHORIZATION, oauth);
            request.setEntity(params);
            CloseableHttpResponse response = client.execute(request);
            respContent = EntityUtils.toString(response.getEntity());
            int code = response.getStatusLine().getStatusCode();
            System.out.println(respContent);
            Logger logger = Logger.getLogger(SmartCaESealService.class.getName());
            logger.log(Level.SEVERE, request.toString());
            logger.log(Level.SEVERE, response.toString());
            logger.log(Level.SEVERE, respContent);
            if (200 != code) {
                System.out.println("---STATUS CODE != 200---");
                System.out.println(respContent);
                return null;
            }
            credential = gson.fromJson(respContent, Credential.class);
            return credential;
        } catch (Exception ex) {
            ex.printStackTrace();
        } finally {
            try {
                client.close();
            } catch (IOException e) {
            }
        }

        return null;
    }

    Certificate getCertificate(SmartCADetail smartCADetail) {
        String respContent = "";
        Gson gson = new Gson();
        CloseableHttpClient client = Ultils.initSecureClient();
        CertificateRequest certificateRequest = new CertificateRequest(smartCADetail.getCredentialId(), "single", true, true);
        String para = gson.toJson(certificateRequest);
        Certificate certificate = new Certificate();
        try {
            HttpPost request = new HttpPost(smartCADetail.getUrlESeal() + SmartCAContants.SmartCAEsealUrl.GET_INFO.getUrl());
            StringEntity params = new StringEntity(para);
            request.addHeader("content-type", "application/json");
            String oauth = Ultils.BEARER + " " + smartCADetail.getAccessToken();
            request.addHeader(Ultils.AUTHORIZATION, oauth);
            request.setEntity(params);
            CloseableHttpResponse response = client.execute(request);
            respContent = EntityUtils.toString(response.getEntity());
            int code = response.getStatusLine().getStatusCode();
            if (200 != code) {
                System.out.println(respContent);
                return null;
            }
            certificate = gson.fromJson(respContent, Certificate.class);
            return certificate;
        } catch (Exception ex) {
            ex.printStackTrace();
        } finally {
            try {
                client.close();
            } catch (IOException e) {
            }
        }

        return null;
    }

    String getHexFromHash(String hashValue) {
        byte[] decoded = Base64.getDecoder().decode(hashValue);
        return Hex.toHexString(decoded);
    }

    Response sendSignRequest(SmartCADetail smartCADetail,
                             String otp, String description,
                             ArrayList<SmartCAQD769SignRequestDTO.FileSignInfo> listFileToSign,
                             String serialNumber,
                             String timestamp,
                             String urlQD769,
                             String dvtt, String userId) throws IOException {
        try {
            String transactionId = UUID.randomUUID().toString();
            SmartCAQD769SignRequestDTO smartCAQD769SignRequestDTO = new SmartCAQD769SignRequestDTO(
                    smartCADetail.getClientId(),
                    smartCADetail.getClientSecret(),
                    smartCADetail.getUsername(),
                    smartCADetail.getPassword(),
                    otp,
                    description,
                    listFileToSign,
                    serialNumber,
                    timestamp,
                    transactionId
            );

            OkHttpClient client = new OkHttpClient().newBuilder().build();
            MediaType mediaType = MediaType.parse("application/json");
            ObjectMapper mapper = new ObjectMapper();
            String jsonString = mapper.writeValueAsString(smartCAQD769SignRequestDTO);
            RequestBody requestBody = RequestBody.create(jsonString, mediaType);
            Request request = new Request
                    .Builder()
                    .url(urlQD769 + SmartCAContants.SmartCAQD769Url.SIGN.getUrl())
                    .method("POST", requestBody)
                    .addHeader("Content-Type", "application/json")
                    .build();
            Response response = client.newCall(request).execute();
            smartCALogUtils.smartcaGhiLogChiTiet(dvtt, userId, SmartCAContants.SmartCALogType.CALL_SMARTCA.ordinal(), "Thực hiện ký số từ smartCA V2", jsonString, response, request);
            return response;
        }catch (Exception ex){
            ex.printStackTrace();
            String msg = "SMARTCA: Lỗi không thể ký số V1!";
            smartCADAO.smartcaCapNhatLog(dvtt, userId, SmartCAContants.SmartCALogType.CALL_SMARTCA.ordinal(), msg, SmartCAContants.SmartCAQD769Url.SIGN.getUrl(), smartCADetail.toString(), ex.getMessage());
            return null;
        }
    }



    Response sendConfirmSignToCA(SmartCADetail smartCADetail, String sad, String transactionId) throws IOException {
        SmartCAQD769ConfirmRequestDTO smartCAQD769SignRequestDTO = new SmartCAQD769ConfirmRequestDTO(
                smartCADetail.getClientId(),
                smartCADetail.getClientSecret(),
                smartCADetail.getUsername(),
                smartCADetail.getPassword(),
                sad,
                transactionId
        );

        OkHttpClient client = new OkHttpClient().newBuilder().build();
        MediaType mediaType = MediaType.parse("application/json");
        ObjectMapper mapper = new ObjectMapper();
        String jsonString = mapper.writeValueAsString(smartCAQD769SignRequestDTO);
        RequestBody requestBody = RequestBody.create(jsonString, mediaType);
        Request request = new Request
                .Builder()
                .url(smartCADetail.getUrlQD769() + SmartCAContants.SmartCAQD769Url.CONFIRM.getUrl())
                .method("POST", requestBody)
                .addHeader("Content-Type", "application/json")
                .build();
        Response response = client.newCall(request).execute();
        return response;
    }

    /*---------------------------------------------------------V1*/
    SmartCAQD769GetCertificateResponseDTO sendGetCertificateV1Request(SmartCADetail smartCADetail, String serialNumber) throws IOException {
        SmartCAQD769GetCertificateRequestDTO smartCAQD769SignRequestDTO = new SmartCAQD769GetCertificateRequestDTO(
                smartCADetail.getClientId(),
                smartCADetail.getClientSecret(),
                smartCADetail.getUsername(),
                serialNumber,
                UUID.randomUUID().toString()
        );

        OkHttpClient client = new OkHttpClient().newBuilder().build();
        MediaType mediaType = MediaType.parse("application/json");
        ObjectMapper mapper = new ObjectMapper();
        String jsonString = mapper.writeValueAsString(smartCAQD769SignRequestDTO);
        RequestBody requestBody = RequestBody.create(jsonString, mediaType);
        Request request = new Request
                .Builder()
                .url(smartCADetail.getUrlQD769() + "/" + SmartCAContants.SmartCAQD769Url.GET_CERTIFICATE_V1.getUrl())
                .method("POST", requestBody)
                .addHeader("Content-Type", "application/json")
                .build();
        Response response = client.newCall(request).execute();
        if (response.code() == 200) {
            ObjectMapper objectMapper = new ObjectMapper();
            objectMapper.disable(FAIL_ON_UNKNOWN_PROPERTIES);
            String responseStr = Objects.requireNonNull(response.body()).string();
            SmartCAQD769GetCertificateResponseDTO smartCAQD769GetCertificateResponseDTO = objectMapper.readValue(responseStr, SmartCAQD769GetCertificateResponseDTO.class);
            if (smartCAQD769GetCertificateResponseDTO != null) {
                return smartCAQD769GetCertificateResponseDTO;
            }
        }
        return null;
    }

    SmartCAQD769SignV1ResponseDTO sendSignV1Request(SmartCADetail smartCADetail, String hexString, String serialNumber, String timestamp,
                                                    String fileName, String dvtt, String userId) throws IOException {
        List<SmartCAQD769SignV1RequestDTO.FileSignInfo> fileSignInfoList = new ArrayList<>();
        fileSignInfoList.add(new SmartCAQD769SignV1RequestDTO.FileSignInfo(
                hexString,
                fileName,
                SmartCAContants.SmartCAFileType.PDF.getFileType(),
                SmartCAContants.SmartCASignType.HASH.getSignType()
        ));
        SmartCAQD769SignV1RequestDTO smartCAQD769SignV1RequestDTO = new SmartCAQD769SignV1RequestDTO(
                smartCADetail.getClientId(),
                smartCADetail.getClientSecret(),
                smartCADetail.getUsername(),
                "VNPT Sign Hash",
                fileSignInfoList,
                serialNumber,
                timestamp,
                UUID.randomUUID().toString()
        );

        try {
            OkHttpClient client = new OkHttpClient().newBuilder().build();
            MediaType mediaType = MediaType.parse("application/json");
            ObjectMapper mapper = new ObjectMapper();
            String jsonString = mapper.writeValueAsString(smartCAQD769SignV1RequestDTO);
            RequestBody requestBody = RequestBody.create(jsonString, mediaType);
            Request request = new Request
                    .Builder()
                    .url(smartCADetail.getUrlQD769() + "/" + SmartCAContants.SmartCAQD769Url.SIGN_V1.getUrl())
                    .method("POST", requestBody)
                    .addHeader("Content-Type", "application/json")
                    .build();
            Response response = client.newCall(request).execute();
            smartCALogUtils.smartcaGhiLogChiTiet(dvtt, userId, SmartCAContants.SmartCALogType.CALL_SMARTCA.ordinal(), "Thực hiện ký số từ smartCA V1", jsonString, response, request);
            if (response.code() == 200) {
                ObjectMapper objectMapper = new ObjectMapper();
                objectMapper.disable(FAIL_ON_UNKNOWN_PROPERTIES);
                String responseStr = Objects.requireNonNull(response.body()).string();
                SmartCAQD769SignV1ResponseDTO smartCAQD769SignV1ResponseDTO = objectMapper.readValue(responseStr, SmartCAQD769SignV1ResponseDTO.class);
                if (smartCAQD769SignV1ResponseDTO != null) {
                    return smartCAQD769SignV1ResponseDTO;
                }
            } else {
                ObjectMapper objectMapper = new ObjectMapper();
                objectMapper.disable(FAIL_ON_UNKNOWN_PROPERTIES);
                String responseStr = Objects.requireNonNull(response.body()).string();
                SmartCAQD769SignV1ResponseDTO smartCAQD769SignV1ResponseDTO = objectMapper.readValue(responseStr, SmartCAQD769SignV1ResponseDTO.class);
                return smartCAQD769SignV1ResponseDTO;
            }
        }catch (Exception ex){
            ex.printStackTrace();
            String msg = "SMARTCA: Lỗi không thể ký số V1!";
            smartCADAO.smartcaCapNhatLog(dvtt, userId, SmartCAContants.SmartCALogType.CALL_SMARTCA.ordinal(), msg, SmartCAContants.SmartCAQD769Url.SIGN_V1.getUrl(), smartCADetail.toString(), ex.getMessage());
        }
        return null;
    }

    SmartCAQD769GetTranStatusV1ResponseDTO sendGetTranStatusV1Request(String tranId, String urlQD769) throws IOException {

        OkHttpClient client = new OkHttpClient().newBuilder().build();
        RequestBody requestBody = RequestBody.create(new byte[0]);
        Request request = new Request
                .Builder()
                .url(urlQD769 +"/" + SmartCAContants.SmartCAQD769Url.GET_TRAN_STATUS_V1.getUrl().replaceAll("TRANS_ID", tranId))
                .method("POST", requestBody)
                .addHeader("Content-Type", "application/json")
                .build();
        Response response = client.newCall(request).execute();
        if (response.code() == 200) {
            ObjectMapper objectMapper = new ObjectMapper();
            objectMapper.disable(FAIL_ON_UNKNOWN_PROPERTIES);
            String responseStr = Objects.requireNonNull(response.body()).string();
            SmartCAQD769GetTranStatusV1ResponseDTO smartCAQD769GetTranStatusV1ResponseDTO = objectMapper.readValue(responseStr, SmartCAQD769GetTranStatusV1ResponseDTO.class);
            if (smartCAQD769GetTranStatusV1ResponseDTO != null) {
                return smartCAQD769GetTranStatusV1ResponseDTO;
            }
        }
        return null;
    }
    public  String addImageBoxToPdfFromBase64Itext(String base64Source, String imageSource,
                                                   List<SignObj.SignTemplate> signTemplateList) throws IOException, DocumentException {
        byte[] bytes = org.apache.axis.encoding.Base64.decode(base64Source);
        PdfReader reader = new PdfReader(bytes);

        ByteArrayOutputStream output = new ByteArrayOutputStream();
        PdfStamper pdfStamper = new PdfStamper(reader, output, '\0', true);

        byte[] byteImage = Files.readAllBytes(Paths.get(imageSource));
        ByteArrayInputStream imgStream = new ByteArrayInputStream(byteImage);
        com.itextpdf.text.Image img = com.itextpdf.text.Image.getInstance(ImageIO.read(imgStream), null);


        for (int i = 0; i < signTemplateList.size(); i++) {
            SignObj.SignTemplate template = signTemplateList.get(i);
            String [] position = template.getRectangle().split(",");
            float x = Float.parseFloat(position[0]);
            float y = Float.parseFloat(position[1]) + 25;
            float w = Float.parseFloat(position[2]);
            float h = Float.parseFloat(position[3]);

            float w1 = img.getWidth();
            float h1 = img.getHeight();

            com.itextpdf.text.Rectangle rectImage = new com.itextpdf.text.Rectangle(x, y, x-w1, y+h1);
            PdfAnnotation imageAnnot = PdfAnnotation.createStamp(pdfStamper.getWriter(), rectImage, null, "ITEXT");
            img.setAbsolutePosition(0, 0);
            PdfAppearance pdfAppearance = PdfAppearance.createAppearance(pdfStamper.getWriter(), w1+5, h1+5);

            pdfAppearance.addImage(img);
            pdfAppearance.fill();
            imageAnnot.setAppearance(PdfName.N, pdfAppearance);
            imageAnnot.setFlags(PdfAnnotation.FLAGS_READONLY | PdfAnnotation.FLAGS_PRINT);


            pdfStamper.addAnnotation(imageAnnot, template.getPageNumber());

        }
        pdfStamper.close();
        reader.close();
        return org.apache.axis.encoding.Base64.encode(output.toByteArray());
    }

    public Map<String, Object> signHashPdfV1(SmartCADetail smartCADetail, byte[] fileContent,
                                             String imageSrc, String visibleType, String fullName,
                                             String fontSize, String comment, List<SignObj.SignTemplate> signTemplateList,
                                             String fileName, String dvtt, String keySign, String userId, String kyhieuphieu) throws IOException {
        Map<String, Object> result = new HashMap<String, Object>();
        String errorCode = "0";
        String errorMessage = "Wait";
        String certBase64 = "";
        String rect = "", hashValue = "";
        SmartCAQD769GetCertificateResponseDTO.Data.Certificate certificate = null;
        SmartCAQD769GetCertificateResponseDTO smartCAQD769GetCertificateResponseDTO = sendGetCertificateV1Request(smartCADetail, smartCADetail.getSerialSmartCA());
        if (smartCAQD769GetCertificateResponseDTO != null) {
            for (SmartCAQD769GetCertificateResponseDTO.Data.Certificate cer : smartCAQD769GetCertificateResponseDTO.getData().getCertificateList()) {
                if(cer.getSerialNumber().equals(smartCADetail.getSerialSmartCA())) {

                    certificate = cer;
                    break;
                }
            }
            if(certificate != null) {
                certBase64 = certificate.getCertData().replace("-----BEGIN CERTIFICATE-----", "")
                        .replace("\r\n", "").replace("-----END CERTIFICATE-----", "");
            }
            SignatureParameter param = new SignatureParameter(fileContent, certBase64);
            String nameSign = "";
            String uid = "";
            try {
                IdentityPerson identityPerson = IdentityPerson.parseFromString(smartCAQD769GetCertificateResponseDTO.getData().getCertificateList().get(0).getCertSubject());
                nameSign = identityPerson.getCn();
                uid = identityPerson.getUid();
            } catch (Exception ex) {
                nameSign = fullName;
            }
            IHashSginer signer = null;
            try {
                signer = HashSignerFactory.genenrateSigner(DocumentType.PDF, param);
                PdfHashSigner pdfSigner = (PdfHashSigner) signer;
                pdfSigner.setHashAlgorithm(MessageDigestAlgorithm.SHA256);
                for (int i = 0; i < signTemplateList.size(); i++) {
                    SignObj.SignTemplate template = signTemplateList.get(i);
                    pdfSigner.addSignatureViews(new PdfSignatureView(template.getPageNumber(), template.getRectangle()));
                }
                if(visibleType.equals("3") || visibleType.equals("4")) {
                    List<Map<String, Object>> ls = smartCADAO.smartcagetchuKy(dvtt, userId, kyhieuphieu);
                    if(ls != null && ls.size() > 0) {
                        Map<String, Object> chuky = ls.get(0);
                        String imageSigned = chuky.get("SIGNED").toString().replaceAll("data:image/png;base64,","");
                        pdfSigner.setImage(Base64.getDecoder().decode(imageSigned));
                    }

                }
                if(visibleType.equals("6") || visibleType.equals("7") || visibleType.equals("5")) {
                    pdfSigner.setImage(Base64.getDecoder().decode(imageSrc));
                }
                Date signedDate = new Date();
                SimpleDateFormat simpleDateFormat = new SimpleDateFormat("dd/MM/yyyy HH:mm:ss");
                String signedDateString = simpleDateFormat.format(signedDate);
                String khonghienthithoigianky = thamsodonviDAO.laythamso_donvi_motthamso(dvtt, "960595");
                switch (visibleType) {
                    case "1":
                        pdfSigner.setRenderMode(PdfHashSigner.RenderMode.TEXT_ONLY);
                        if(khonghienthithoigianky.equals("1")) {
                            pdfSigner.setSignatureText("   Ký bởi: " + nameSign);
                        } else {
                            pdfSigner.setSignatureText("   Ký bởi: " + nameSign + "\n   Ngày ký: " + signedDateString );
                        }
                        break;
                    case "2":
                        pdfSigner.setRenderMode(PdfHashSigner.RenderMode.TEXT_WITH_LOGO_LEFT);
                        if(khonghienthithoigianky.equals("1")) {
                            pdfSigner.setSignatureText("   Ký bởi: " + nameSign);
                        } else {
                            pdfSigner.setSignatureText("   Ký bởi: " + nameSign + "\n   Ngày ký: " + signedDateString );
                        }
                        break;
                    case "3":
                        pdfSigner.setRenderMode(PdfHashSigner.RenderMode.LOGO_ONLY);
                        break;
                    case "4":
                        pdfSigner.setRenderMode(PdfHashSigner.RenderMode.TEXT_WITH_LOGO_TOP);
                        pdfSigner.setSignatureText("Ngày ký: " + signedDateString );
                        break;
                    case "5":
                        pdfSigner.setRenderMode(PdfHashSigner.RenderMode.TEXT_WITH_BACKGROUND);
                        break;
                    default:
                        pdfSigner.setRenderMode(PdfHashSigner.RenderMode.LOGO_ONLY);
                        break;
                }
                pdfSigner.setFontColor("000000");
                pdfSigner.setFontName(PdfHashSigner.FontName.Times_New_Roman);
                pdfSigner.setFontSize(Integer.parseInt(fontSize));
                pdfSigner.setFontStyle(PdfHashSigner.FontStyle.Normal);
                PdfSignatureComment com = new PdfSignatureComment();
                if (comment != null && !comment.isEmpty() && !"".equals(comment)) {
                    JsonParser jsonParser = new JsonParser();
                    JSONObject jsonLineItem = null;
                    if (!"[]".equals(comment) && !"".equals(comment)) {
                        JsonArray json = jsonParser.parse(comment).getAsJsonArray();
                        for (Object o : json) {
                            jsonLineItem = new JSONObject(o.toString());
                            if (jsonLineItem.getString("rectangle") != null && !jsonLineItem.getString("rectangle").isEmpty() && !"".equals(jsonLineItem.getString("rectangle"))) {
                                rect = jsonLineItem.getString("rectangle");
                            } else {
                                rect = jsonLineItem.getString("x") + "," + jsonLineItem.getString("y") + "," + (Integer.parseInt(jsonLineItem.getString("x"))) + "," + (Integer.parseInt(jsonLineItem.getString("y")));
                            }
                            com = new PdfSignatureComment(PdfSignatureComment.Types.TEXT, rect, jsonLineItem.getString("text"), Integer.parseInt(jsonLineItem.getString("page")), PdfHashSigner.FontName.Times_New_Roman, PdfHashSigner.FontStyle.Normal, Integer.parseInt(jsonLineItem.getString("fontsize")), "", "");
                            pdfSigner.addSignatureComment(com);
                        }
                    }
                }
                hashValue = signer.getSecondHashAsBase64();
                String hexString = getHexFromHash(hashValue);

                String serialNumber = "";
                if (certificate != null) {
                    serialNumber = certificate.getSerialNumber();
                }

                SmartCAQD769SignV1ResponseDTO smartCAQD769SignV1ResponseDTO = sendSignV1Request(
                        smartCADetail,
                        hexString,
                        serialNumber,
                        null,
                        fileName,
                        dvtt,
                        userId);
                if (smartCAQD769SignV1ResponseDTO  != null && smartCAQD769SignV1ResponseDTO.getStatusCode() == 200) {
                    String tranId = smartCAQD769SignV1ResponseDTO.getData().getTransactionId();

                    try {
                        smartCADAO.smartcaTransactionAdd(keySign, dvtt, tranId, "V1", "");
                    } catch (Exception exception) {
                        exception.printStackTrace();
                    }

                    int count = 0;
                    boolean isConfirm = false;
                    String signatureStr = "";
                    SmartCAQD769GetTranStatusV1ResponseDTO smartCAQD769GetTranStatusV1ResponseDTO = null;

                    while (count < 90 && !isConfirm) {
                        smartCAQD769GetTranStatusV1ResponseDTO = sendGetTranStatusV1Request(tranId, smartCADetail.getUrlQD769());
                        if (smartCAQD769GetTranStatusV1ResponseDTO != null && smartCAQD769GetTranStatusV1ResponseDTO.getMessage().equalsIgnoreCase("SUCCESS")) { // Người dùng confirm
                            try {
                                smartCADAO.smartcaTransactionDelete(keySign, dvtt);
                            } catch (Exception exception) {
                                exception.printStackTrace();
                            }
                            isConfirm = true;
                            signatureStr = smartCAQD769GetTranStatusV1ResponseDTO.getData().getSignatureList().get(0).getSignatureValue();
                        } else if (smartCAQD769GetTranStatusV1ResponseDTO != null && smartCAQD769GetTranStatusV1ResponseDTO.getMessage().equalsIgnoreCase("REJECTED")) { // Người dùng cancel
                            try {
                                smartCADAO.smartcaTransactionDelete(keySign, dvtt);
                            } catch (Exception exception) {
                                exception.printStackTrace();
                            }
                            isConfirm = true;
                        } else if (smartCAQD769GetTranStatusV1ResponseDTO != null && smartCAQD769GetTranStatusV1ResponseDTO.getMessage().equalsIgnoreCase("EXPIRED")) { // Hết thời gian
                            try {
                                smartCADAO.smartcaTransactionDelete(keySign, dvtt);
                            } catch (Exception exception) {
                                exception.printStackTrace();
                            }
                            isConfirm = true;
                            errorCode = "-998";
                            errorMessage = "Hết thời gian xác thực yêu cầu ký số. Vui lòng thực hiện ký số lại";
                            break;
                        } else { // Đợi confirm
                            count = count + 1;
                            try {
                                Thread.sleep(4000);
                            } catch (InterruptedException e) {
                                e.printStackTrace();
                            }
                        }
                    }

                    if(signer.checkHashSignature(signatureStr)) {
                        byte[] signedData = signer.sign(signatureStr);
                        result.put("signDate", signedDate);
                        result.put("signData", signedData);
                        result.put("certificate", smartCAQD769GetCertificateResponseDTO.getData().getCertificateList().get(0));
                    }
                } else {
                    errorCode = "-2";
                    errorMessage = "Không gửi được yêu cầu xác thực. Status: " + (smartCAQD769SignV1ResponseDTO  != null ? smartCAQD769SignV1ResponseDTO.getStatusCode() + ", Message: " + smartCAQD769SignV1ResponseDTO.getMessage() : "NULL");
                }
            } catch (Exception ex) {
                ex.printStackTrace();
                errorCode = "-999";
                errorMessage = "Lỗi ngoại lệ: " + ex.getMessage();
            }
        } else {
            errorCode = "-1";
            errorMessage = "Không lấy được certificate. Status: " + (smartCAQD769GetCertificateResponseDTO  != null ? smartCAQD769GetCertificateResponseDTO.getStatusCode() + ", Message: " + smartCAQD769GetCertificateResponseDTO.getMessage() : "NULL");
        }

        result.put("errorCode", errorCode);
        result.put("errorMessage", errorMessage);

        return result;
    }


    public Map<String, Object> signHashPdfV2(SmartCADetail smartCADetail, byte[] fileContent, String imageSrc,
                                             String visibleType, String fullName, String fontSize, String comment,
                                             List<SignObj.SignTemplate> signTemplateList, String fileName, String dvtt,
                                             String keySign, String userId, String kyhieuphieu) throws IOException {
        Map<String, Object> result = new HashMap<String, Object>();
        String errorCode = "0";
        String errorMessage = "Wait";
        String certBase64 = "";
        String rect = "", hashValue = "";
        SignHashESeal signHash = null;
        SmartCAQD769GetCertificateResponseDTO.Data.Certificate certificate = null;
        SmartCAQD769GetCertificateResponseDTO smartCAQD769GetCertificateResponseDTO = sendGetCertificateV1Request(smartCADetail, smartCADetail.getSerialSmartCA());
        if (smartCAQD769GetCertificateResponseDTO != null) {
            for (SmartCAQD769GetCertificateResponseDTO.Data.Certificate cer : smartCAQD769GetCertificateResponseDTO.getData().getCertificateList()) {
                if(cer.getSerialNumber().equals(smartCADetail.getSerialSmartCA())) {

                    certificate = cer;
                    break;
                }
            }
            if(certificate != null) {
                certBase64 = certificate.getCertData().replace("-----BEGIN CERTIFICATE-----", "")
                        .replace("\r\n", "").replace("-----END CERTIFICATE-----", "");
            }
            SignatureParameter param = new SignatureParameter(fileContent, certBase64);
            String nameSign = "";
            String uid = "";
            try {
                IdentityPerson identityPerson = IdentityPerson.parseFromString(smartCAQD769GetCertificateResponseDTO.getData().getCertificateList().get(0).getCertSubject());
                nameSign = identityPerson.getCn();
                uid = identityPerson.getUid();
            } catch (Exception ex) {
                nameSign = fullName;
            }
            Credential credential = getCredential(smartCADetail);
            IHashSginer signer = null;

            try {
                signer = HashSignerFactory.genenrateSigner(DocumentType.PDF, param);
                PdfHashSigner pdfSigner = (PdfHashSigner) signer;
                pdfSigner.setHashAlgorithm(MessageDigestAlgorithm.SHA256);
                for (int i = 0; i < signTemplateList.size(); i++) {
                    SignObj.SignTemplate template = signTemplateList.get(i);
                    pdfSigner.addSignatureViews(new PdfSignatureView(template.getPageNumber(), template.getRectangle()));
                }
                if (visibleType.equals("3") || visibleType.equals("4")) {
                    List<Map<String, Object>> ls = smartCADAO.smartcagetchuKy(dvtt, userId, kyhieuphieu);
                    if (ls != null && ls.size() > 0) {
                        Map<String, Object> chuky = ls.get(0);
                        String imageSigned = chuky.get("SIGNED").toString().replaceAll("data:image/png;base64,", "");
                        pdfSigner.setImage(Base64.getDecoder().decode(imageSigned));
                    }

                }
                if (visibleType.equals("6") || visibleType.equals("7") || visibleType.equals("5")) {
                    pdfSigner.setImage(Base64.getDecoder().decode(imageSrc));
                }
                Date signedDate = new Date();
                SimpleDateFormat simpleDateFormat = new SimpleDateFormat("dd/MM/yyyy HH:mm:ss");
                String signedDateString = simpleDateFormat.format(signedDate);
                String khonghienthithoigianky = thamsodonviDAO.laythamso_donvi_motthamso(dvtt, "960595");
                switch (visibleType) {
                    case "1":
                        pdfSigner.setRenderMode(PdfHashSigner.RenderMode.TEXT_ONLY);
                        if (khonghienthithoigianky.equals("1")) {
                            pdfSigner.setSignatureText("   Ký bởi: " + nameSign);
                        } else {
                            pdfSigner.setSignatureText("   Ký bởi: " + nameSign + "\n   Ngày ký: " + signedDateString);
                        }
                        break;
                    case "2":
                        pdfSigner.setRenderMode(PdfHashSigner.RenderMode.TEXT_WITH_LOGO_LEFT);
                        if (khonghienthithoigianky.equals("1")) {
                            pdfSigner.setSignatureText("   Ký bởi: " + nameSign);
                        } else {
                            pdfSigner.setSignatureText("   Ký bởi: " + nameSign + "\n   Ngày ký: " + signedDateString);
                        }
                        break;
                    case "3":
                        pdfSigner.setRenderMode(PdfHashSigner.RenderMode.LOGO_ONLY);
                        break;
                    case "4":
                        pdfSigner.setRenderMode(PdfHashSigner.RenderMode.TEXT_WITH_LOGO_TOP);
                        pdfSigner.setSignatureText("Ngày ký: " + signedDateString);
                        break;
                    case "5":
                        pdfSigner.setRenderMode(PdfHashSigner.RenderMode.TEXT_WITH_BACKGROUND);
                        break;
                    default:
                        pdfSigner.setRenderMode(PdfHashSigner.RenderMode.LOGO_ONLY);
                        break;
                }
                pdfSigner.setFontName(PdfHashSigner.FontName.Times_New_Roman);
                pdfSigner.setFontSize(Integer.parseInt(fontSize));
                pdfSigner.setFontStyle(PdfHashSigner.FontStyle.Normal);
                PdfSignatureComment com = new PdfSignatureComment();
                if (comment != null && !comment.isEmpty() && !"".equals(comment)) {
                    JsonParser jsonParser = new JsonParser();
                    JSONObject jsonLineItem = null;
                    if (!"[]".equals(comment) && !"".equals(comment)) {
                        JsonArray json = jsonParser.parse(comment).getAsJsonArray();
                        for (Object o : json) {
                            jsonLineItem = new JSONObject(o.toString());
                            if (jsonLineItem.getString("rectangle") != null && !jsonLineItem.getString("rectangle").isEmpty() && !"".equals(jsonLineItem.getString("rectangle"))) {
                                rect = jsonLineItem.getString("rectangle");
                            } else {
                                rect = jsonLineItem.getString("x") + "," + jsonLineItem.getString("y") + "," + (Integer.parseInt(jsonLineItem.getString("x"))) + "," + (Integer.parseInt(jsonLineItem.getString("y")));
                            }
                            com = new PdfSignatureComment(PdfSignatureComment.Types.TEXT, rect, jsonLineItem.getString("text"), Integer.parseInt(jsonLineItem.getString("page")), PdfHashSigner.FontName.Times_New_Roman, PdfHashSigner.FontStyle.Normal, Integer.parseInt(jsonLineItem.getString("fontsize")), "", "");
                            pdfSigner.addSignatureComment(com);
                        }
                    }
                }
                hashValue = signer.getSecondHashAsBase64();
                String hexString = getHexFromHash(hashValue);

                SmartCAQD769SignRequestDTO.FileSignInfo fileSignInfo = new SmartCAQD769SignRequestDTO.FileSignInfo(hexString,
                        fileName,
                        SmartCAContants.SmartCAFileType.PDF.getFileType(),
                        SmartCAContants.SmartCASignType.HASH.getSignType());
                ArrayList<SmartCAQD769SignRequestDTO.FileSignInfo> listFileToSign = new ArrayList<>();
                listFileToSign.add(fileSignInfo);
                OTPService otpService = new OTPService(smartCADetail.gettOtp());
                String otp = otpService.genOtp();

                Response responseSign = sendSignRequest(
                        smartCADetail,
                        otp,
                        "Ký số SignHash",
                        listFileToSign,
                        smartCADetail.getSerialSmartCA(),
                        null,
                        smartCADetail.getUrlQD769(), dvtt, userId);
                if (responseSign.code() == 200) {
                    String responseSignStr = Objects.requireNonNull(responseSign.body()).string();
                    ObjectMapper objectMapper = new ObjectMapper();
                    objectMapper.disable(FAIL_ON_UNKNOWN_PROPERTIES);
                    SmartCAQD769SignResponseDTO smartCAQD769SignResponseDTO = objectMapper.readValue(responseSignStr, SmartCAQD769SignResponseDTO.class);
                    if (smartCAQD769SignResponseDTO != null && smartCAQD769SignResponseDTO.getStatusCode() == 200) {
                        if (smartCAQD769SignResponseDTO.getData() != null) {
                            if (smartCAQD769SignResponseDTO.getData().getSad() != null && smartCAQD769SignResponseDTO.getData().getTransactionId() != null) {
                                try {
                                    smartCADAO.smartcaTransactionAdd(keySign, dvtt, smartCAQD769SignResponseDTO.getData().getTransactionId(), "V2", "");
                                } catch (Exception exception) {
                                    exception.printStackTrace();
                                }
                                Response responseConfirm = sendConfirmSignToCA(smartCADetail, smartCAQD769SignResponseDTO.getData().getSad(), smartCAQD769SignResponseDTO.getData().getTransactionId());
                                if (responseConfirm.code() == 200) {
                                    String responseConfirmStr = Objects.requireNonNull(responseConfirm.body()).string();
                                    SmartCAQD769ConfirmResponseDTO smartCAQD769ConfirmResponseDTO = objectMapper.readValue(responseConfirmStr, SmartCAQD769ConfirmResponseDTO.class);
                                    if (smartCAQD769ConfirmResponseDTO != null && smartCAQD769ConfirmResponseDTO.getStatusCode() == 200) {
                                        try {
                                            smartCADAO.smartcaTransactionDelete(keySign, dvtt);
                                        } catch (Exception exception) {
                                            exception.printStackTrace();
                                        }
                                        SmartCAQD769ConfirmResponseDTO.Data dataConfirm = smartCAQD769ConfirmResponseDTO.getData();
                                        if (dataConfirm != null) {
                                            ArrayList<SmartCAQD769ConfirmResponseDTO.Data.SignatureData> signatureDataArrayList = dataConfirm.getSignatureDataArrayList();
                                            if (signatureDataArrayList.size() > 0) {
                                                SmartCAQD769ConfirmResponseDTO.Data.SignatureData signatureData = signatureDataArrayList.get(0);
                                                String signatureStr = signatureData.getSignatureValue();

                                                if (signer.checkHashSignature(signatureStr)) {
                                                    byte[] signedData = signer.sign(signatureStr);
                                                    result.put("signDate", signedDate);
                                                    result.put("signData", signedData);
                                                    result.put("certificate", certificate);
                                                }
                                            } else {
                                                errorCode = "-8";
                                                errorMessage = "Confirm: Không có dữ liệu ký nào được xác nhận";
                                            }
                                        } else {
                                            errorCode = "-7";
                                            errorMessage = "Confirm: Không tìm thấy Data trả về từ QD769";
                                        }
                                    } else {
                                        errorCode = "-6";
                                        errorMessage = "Lỗi xác nhận tới QD769: " + (smartCAQD769ConfirmResponseDTO != null ? smartCAQD769ConfirmResponseDTO.getMessage() : "null");
                                    }
                                } else {
                                    errorCode = "-5";
                                    errorMessage = "Lỗi gọi request xác nhận tới QD769 Status code: " + responseConfirm.code() + "): " + responseConfirm.message();
                                }
                            } else {
                                errorCode = "-4";
                                errorMessage = "Sign: Không tìm thấy sad trả về từ QD769: Data = " + smartCAQD769SignResponseDTO.getData().toString();
                            }
                        } else {
                            errorCode = "-3";
                            errorMessage = "Sign: Không tìm thấy Data trả về từ QD769";
                        }
                    } else {
                        errorCode = "-2";
                        errorMessage = "Lỗi ký số QD769: " + (smartCAQD769SignResponseDTO != null ? smartCAQD769SignResponseDTO.getMessage() : "null");
                    }
                } else {
                    errorCode = String.valueOf(responseSign.code() * -1);
                    errorMessage = "Lỗi gọi request ký số sang QD769 (Status code: " + responseSign.code() + "): " + responseSign.message();
                }
            } catch (Exception ex) {
                ex.printStackTrace();
                errorCode = "-1";
                errorMessage = "Lỗi ngoại lệ: " + ex.getMessage();
            }
        } else {
            errorCode = "-1";
            errorMessage = "Không lấy được certificate. Status: " + (smartCAQD769GetCertificateResponseDTO  != null ? smartCAQD769GetCertificateResponseDTO.getStatusCode() + ", Message: " + smartCAQD769GetCertificateResponseDTO.getMessage() : "NULL");
        }

        result.put("errorCode", errorCode);
        result.put("errorMessage", errorMessage);

        return result;
    }

    public Boolean checkTransactionStatusV1(String transactionId, String urlQD769) throws IOException {
        SmartCAQD769GetTranStatusV1ResponseDTO smartCAQD769GetTranStatusV1ResponseDTO = sendGetTranStatusV1Request(transactionId, urlQD769);
        String messageGetTranStatusV1 = smartCAQD769GetTranStatusV1ResponseDTO.getMessage().toUpperCase();
        return messageGetTranStatusV1.equalsIgnoreCase("SUCCESS") || messageGetTranStatusV1.equalsIgnoreCase("REJECTED")|| messageGetTranStatusV1.equalsIgnoreCase("EXPIRED");
    }

    public List<SmartCAQD769GetCertificateResponseDTO.Data.Certificate> getListCertificate(SmartCADetail smartCADetail, String serialNumber) throws IOException {
        SmartCAQD769GetCertificateResponseDTO smartCAQD769GetCertificateResponseDTO = sendGetCertificateV1Request(smartCADetail, serialNumber);
        if (smartCAQD769GetCertificateResponseDTO.getData() != null) {
            List<SmartCAQD769GetCertificateResponseDTO.Data.Certificate> certificateList = smartCAQD769GetCertificateResponseDTO.getData().getCertificateList();
            return certificateList;
        }
        return Collections.emptyList();
    }

    public  Map<String, Object> signFakeXml(String dvtt, SignObj.SignXML signXML, Map certificate)  {
        Map<String, Object> mapResult = new HashMap<>();
        String sttChuKy = certificate.containsKey("CHUC_VU") ? certificate.get("CHUC_VU").toString() : "1";
        String[] splitChuKy = sttChuKy.split(";");
        String xmlSign = this.XmlSignature(certificate);
        if (!signXML.getXmlFile().isEmpty()) {
            Document docFile = null;
            Document docSign = null;
            try {
                docFile = Ultils.convertStringToXMLDocument(signXML.getXmlFile());
                docSign = Ultils.convertStringToXMLDocument(xmlSign);
                Node nodeData = docFile.getElementsByTagName("DATA").item(0);
                NodeList ndListFile = nodeData.getChildNodes();
                for (int i = 0; i < ndListFile.getLength(); i++) {
                    nodeData = ndListFile.item(i);
                    if ("HEADER".equalsIgnoreCase(nodeData.getNodeName())) {
                        NodeList listHeader = nodeData.getChildNodes();
                        Node nHeader = null;
                        for (int h = 0; h < listHeader.getLength(); h++) {
                            nHeader = listHeader.item(h);
                            if ("DS_CHUKY".equalsIgnoreCase(nHeader.getNodeName())) {
                                String dDs_chuky = nHeader.getTextContent();
                                if (dDs_chuky.isEmpty()) {
                                    nHeader.setTextContent(sttChuKy);
                                } else {
                                    if (!sttChuKy.isEmpty()) {
                                        dDs_chuky = dDs_chuky + ";" + sttChuKy;
                                    }
                                    nHeader.setTextContent(dDs_chuky);
                                }
                            }
                        }
                    }
                }
                ndListFile = docFile.getElementsByTagName("DATA");
                Node nodeSignature = docFile.importNode(docSign.getElementsByTagName("Signature").item(0), true);
                ndListFile.item(0).appendChild(nodeSignature);

                for (int s = 1; s < splitChuKy.length; s++) {
                    Node nodeSignatureMore = docFile.importNode(docSign.getElementsByTagName("Signature").item(0), true);
                    ndListFile.item(0).appendChild(nodeSignatureMore);
                }

                TransformerFactory tFactory = TransformerFactory.newInstance();
                Transformer transformer = tFactory.newTransformer();
                transformer.setOutputProperty(OutputKeys.INDENT, "yes");
                DOMSource source = new DOMSource(docFile);
                StreamResult result = new StreamResult(new StringWriter());
                transformer.transform(source, result);
                String dataSigned = result.getWriter().toString();
                String base64Data = Base64.getEncoder().encodeToString(dataSigned.getBytes(StandardCharsets.UTF_8));
                mapResult.put("dataSigned", dataSigned);
                mapResult.put("base64Data", base64Data);
            } catch (TransformerException | ParserConfigurationException e) {
                e.printStackTrace();
                mapResult.put("dataSigned", "");
                mapResult.put("base64Data", "");
            }
        }
        return mapResult;
    }

    public  String XmlSignature(Map cer) {
        String xml = "";
        String certBase64 = "";
        String subjectDN = "";
        String cerSigned = cer.getOrDefault("CERTIFICATE", "").toString();
        Gson gson = new Gson();
        JsonObject jsonObject = gson.fromJson(cerSigned, JsonObject.class);
        subjectDN = jsonObject.get("certSubject").getAsString();
        certBase64 = jsonObject.get("certData").getAsString();
        String signingTime = cer.getOrDefault("CREATEDATE", "").toString();
        xml = "    <Signature>\n" + "        <SignedInfo>\n" + "            <CanonicalizationMethod/>\n" + "            " +
                "<SignatureMethod/>\n" + "            <Reference>\n" + "                <Transforms>\n" + "                   " +
                " <Transform/>\n" + "                </Transforms>\n" + "                <DigestMethod/>\n" + "                " +
                "<DigestValue></DigestValue>\n" + "            </Reference>\n" + "        </SignedInfo>\n" + "        " +
                "<SignatureValue></SignatureValue>\n" + "        <KeyInfo>\n" + "            <X509Data>\n" + "                " +
                "<X509Certificate>" + certBase64 + "</X509Certificate>\n" + "                <X509SubjectName>" + subjectDN +
                "</X509SubjectName>\n" + "            </X509Data>\n" + "        </KeyInfo>\n" + "        <Object>\n" + "            " +
                "<SignatureProperties>\n" + "                <SignatureProperty>\n" + "                    " +
                "<SigningTime>" + signingTime + "</SigningTime>\n" + "                </SignatureProperty>\n" + "            " +
                "</SignatureProperties>\n" + "        </Object>\n" + "    </Signature>";
        return xml;
    }
}