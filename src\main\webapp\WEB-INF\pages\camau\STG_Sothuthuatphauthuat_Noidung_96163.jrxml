<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="report name" language="groovy" pageWidth="1190" pageHeight="842" orientation="Landscape" whenNoDataType="AllSectionsNoDetail" columnWidth="1134" leftMargin="36" rightMargin="20" topMargin="20" bottomMargin="20" uuid="ec4d3d4b-9cc9-4c14-926b-845da70f6c67">
	<property name="ireport.zoom" value="1.0"/>
	<property name="ireport.x" value="441"/>
	<property name="ireport.y" value="0"/>
	<parameter name="dvtt" class="java.lang.String"/>
	<parameter name="ten_dvtt" class="java.lang.String"/>
	<parameter name="tungay" class="java.lang.String"/>
	<parameter name="denngay" class="java.lang.String"/>
	<parameter name="loai_dichvu" class="java.lang.String"/>
	<parameter name="soyte" class="java.lang.String"/>
	<parameter name="maloaidv" class="java.lang.String"/>
	<parameter name="phongchidinh" class="java.lang.String"/>
	<parameter name="khoachidinh" class="java.lang.String"/>
	<parameter name="cachlay" class="java.lang.String"/>
	<parameter name="loaibhyt" class="java.lang.String"/>
	<parameter name="hinhthuc" class="java.lang.String"/>
	<parameter name="dsttpt" class="java.lang.String">
		<defaultValueExpression><![CDATA[]]></defaultValueExpression>
	</parameter>
	<parameter name="khoathuchien" class="java.lang.String"/>
	<parameter name="loaihinhdv" class="java.lang.String"/>
	<queryString language="plsql">
		<![CDATA[{call bcth_sothuthuatphauthuat($P{tungay},$P{denngay},$P{dvtt},$P{khoachidinh},$P{phongchidinh},
$P{maloaidv},$P{khoathuchien},$P{cachlay},$P{loaibhyt},$P{hinhthuc},$P{dsttpt},$P{loaihinhdv},$P{ORACLE_REF_CURSOR})}]]>
	</queryString>
	<field name="mabenhnhan" class="java.lang.Long">
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<field name="ho_ten" class="java.lang.String">
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<field name="namsinh" class="java.lang.String"/>
	<field name="tuoi_nam" class="java.lang.String"/>
	<field name="tuoi_nu" class="java.lang.String"/>
	<field name="dia_chi" class="java.lang.String">
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<field name="co_bhyt" class="java.lang.String">
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<field name="chandoan_truoc_tt" class="java.lang.String"/>
	<field name="chandoan_sau_tt" class="java.lang.String"/>
	<field name="phuongphap_thuthuat" class="java.lang.String"/>
	<field name="phuongphap_vocam" class="java.lang.String">
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<field name="ngaygio_thuthuat" class="java.lang.String"/>
	<field name="loai_thuthuat" class="java.lang.String"/>
	<field name="bacsi_thuthuat" class="java.lang.String"/>
	<field name="bacsi_gayme" class="java.lang.String">
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<field name="ghichu" class="java.lang.String">
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<field name="NGAY_GIO_PTTT" class="java.sql.Timestamp">
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<field name="noiDangKy" class="java.lang.String"/>
	<field name="TEXT_NGAY" class="java.lang.String"/>
	<field name="NGAY_HT" class="java.lang.String"/>
	<field name="TEN_DV" class="java.lang.String"/>
	<field name="BS_CHIDINH" class="java.lang.String"/>
	<group name="GR_NGAY">
		<groupExpression><![CDATA[$F{NGAY_HT}]]></groupExpression>
		<groupHeader>
			<band height="20">
				<textField>
					<reportElement x="0" y="0" width="1134" height="20" uuid="f37186aa-f2bc-4f94-a1bb-2450c01f200f"/>
					<box leftPadding="5">
						<leftPen lineWidth="0.5"/>
						<rightPen lineWidth="0.5"/>
					</box>
					<textElement verticalAlignment="Middle">
						<font fontName="Times New Roman" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{TEXT_NGAY} + ": " + $F{NGAY_HT}]]></textFieldExpression>
				</textField>
			</band>
		</groupHeader>
	</group>
	<title>
		<band height="51" splitType="Stretch">
			<textField>
				<reportElement x="0" y="0" width="1134" height="51" uuid="ab4bcd07-6485-4dd6-9adb-30a92565a92d"/>
				<textElement textAlignment="Center" markup="none">
					<font fontName="Times New Roman" size="36" isBold="true" pdfEncoding="Identity-H"/>
				</textElement>
				<textFieldExpression><![CDATA[($P{dvtt}.equals( "96066" ) && !$P{maloaidv}.equals( "PT" )) ? "SỔ THỦ THUẬT" : "SỔ " + $P{loai_dichvu}.toUpperCase()]]></textFieldExpression>
			</textField>
		</band>
	</title>
	<pageHeader>
		<band splitType="Stretch"/>
	</pageHeader>
	<columnHeader>
		<band height="80" splitType="Stretch">
			<staticText>
				<reportElement x="0" y="0" width="24" height="60" uuid="77917a53-1792-4840-a412-9f2638dbab69"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" isBold="true" pdfEncoding="Identity-H"/>
				</textElement>
				<text><![CDATA[Số thứ tự]]></text>
			</staticText>
			<staticText>
				<reportElement x="24" y="0" width="80" height="60" uuid="e85df9bc-4e2a-4876-92ff-6a756963e38a"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" isBold="true" pdfEncoding="Identity-H"/>
				</textElement>
				<text><![CDATA[Họ tên người bệnh]]></text>
			</staticText>
			<staticText>
				<reportElement x="104" y="0" width="50" height="20" uuid="59103728-d39d-4c53-85dd-13ebbc50ba90"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" isBold="true" pdfEncoding="Identity-H"/>
				</textElement>
				<text><![CDATA[Tuổi]]></text>
			</staticText>
			<staticText>
				<reportElement x="104" y="20" width="25" height="40" uuid="770b3c91-f2ef-457e-bd1a-e296cd3f67df"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" isBold="true" pdfEncoding="Identity-H"/>
				</textElement>
				<text><![CDATA[Nam]]></text>
			</staticText>
			<staticText>
				<reportElement x="129" y="20" width="25" height="40" uuid="f14cb9ba-d62d-4e29-965f-49377185a441"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" isBold="true" pdfEncoding="Identity-H"/>
				</textElement>
				<text><![CDATA[Nữ]]></text>
			</staticText>
			<staticText>
				<reportElement x="154" y="0" width="90" height="60" uuid="c17bfbd7-64b0-4a35-86de-3ab9db30b120"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" isBold="true" pdfEncoding="Identity-H"/>
				</textElement>
				<text><![CDATA[Địa chỉ]]></text>
			</staticText>
			<staticText>
				<reportElement x="244" y="0" width="70" height="60" uuid="01cc1557-6297-4294-b882-93cbd30136b2"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" isBold="true" pdfEncoding="Identity-H"/>
				</textElement>
				<text><![CDATA[Số BHYT]]></text>
			</staticText>
			<staticText>
				<reportElement x="314" y="0" width="90" height="20" uuid="dfb5dea4-8882-4939-8a5b-2d549c3102e0"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" isBold="true" pdfEncoding="Identity-H"/>
				</textElement>
				<text><![CDATA[Chẩn đoán]]></text>
			</staticText>
			<textField>
				<reportElement x="314" y="20" width="90" height="40" uuid="5363b16f-634a-4f7a-8200-60dd51fe669b"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
					<font fontName="Times New Roman" isBold="true" pdfEncoding="Identity-H"/>
				</textElement>
				<textFieldExpression><![CDATA["Trước " + $P{loai_dichvu}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="599" y="0" width="85" height="60" uuid="6dcaa8b4-634c-4d7c-af00-a87375a87460"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
					<font fontName="Times New Roman" isBold="true" pdfEncoding="Identity-H"/>
				</textElement>
				<textFieldExpression><![CDATA["Phương pháp " + $P{loai_dichvu}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="684" y="0" width="69" height="60" uuid="83dd127a-**************-f2e202d9c424"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" isBold="true" pdfEncoding="Identity-H"/>
				</textElement>
				<text><![CDATA[Phương pháp vô cảm ]]></text>
			</staticText>
			<textField>
				<reportElement x="753" y="0" width="58" height="60" uuid="d3c0ab6e-3904-496e-b141-8810e2fc3002"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
					<font fontName="Times New Roman" isBold="true" pdfEncoding="Identity-H"/>
				</textElement>
				<textFieldExpression><![CDATA["Ngày/giờ\n" + $P{loai_dichvu}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="811" y="0" width="53" height="60" uuid="b492637c-3eb4-41bf-a6a8-f3f8d907c10d"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
					<font fontName="Times New Roman" isBold="true" pdfEncoding="Identity-H"/>
				</textElement>
				<textFieldExpression><![CDATA["Loại\n" + $P{loai_dichvu}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="864" y="0" width="62" height="60" uuid="6d51feef-0f83-49c3-9063-606ce26b9408"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
					<font fontName="Times New Roman" isBold="true" pdfEncoding="Identity-H"/>
				</textElement>
				<textFieldExpression><![CDATA["Bác sĩ\n" + $P{loai_dichvu}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="1026" y="0" width="71" height="60" uuid="379d54b0-e0bc-47fb-85f3-4bf9fe3d5da8"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" isBold="true" pdfEncoding="Identity-H"/>
				</textElement>
				<text><![CDATA[Bác sĩ
gây mê/tê]]></text>
			</staticText>
			<staticText>
				<reportElement x="1097" y="0" width="37" height="60" uuid="41ad4594-b512-4aa6-8dc0-ac9be4e07810"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" isBold="true" pdfEncoding="Identity-H"/>
				</textElement>
				<text><![CDATA[Ghi chú]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Transparent" x="0" y="60" width="24" height="20" forecolor="#000000" backcolor="#FFFFFF" uuid="7142c8d8-836f-4893-b1e8-b0ea6a3f33c1"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="10" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Identity-H" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<text><![CDATA[1]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Transparent" x="24" y="60" width="80" height="20" forecolor="#000000" backcolor="#FFFFFF" uuid="06210f67-a92c-4a64-8b52-dc88ede1a67f"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="10" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Identity-H" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<text><![CDATA[2]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Transparent" x="154" y="60" width="90" height="20" forecolor="#000000" backcolor="#FFFFFF" uuid="72765f87-38db-457e-950a-a29b9f167d9d"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="10" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Identity-H" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<text><![CDATA[5]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Transparent" x="129" y="60" width="25" height="20" forecolor="#000000" backcolor="#FFFFFF" uuid="384d5619-baf2-4a3f-bd35-4e9f0fd9ff76"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="10" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Identity-H" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<text><![CDATA[4]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Transparent" x="104" y="60" width="25" height="20" forecolor="#000000" backcolor="#FFFFFF" uuid="c92cf9c4-446e-4c4d-bfe9-de4935e5f914"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="10" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Identity-H" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<text><![CDATA[3]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Transparent" x="244" y="60" width="70" height="20" forecolor="#000000" backcolor="#FFFFFF" uuid="d5ac4fe6-cdad-43fe-903b-c4626305350c"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="10" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Identity-H" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<text><![CDATA[6]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Transparent" x="314" y="60" width="90" height="20" forecolor="#000000" backcolor="#FFFFFF" uuid="79763f52-bfc4-4b1c-b113-088cb51bcd59"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="10" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Identity-H" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<text><![CDATA[7]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Transparent" x="404" y="60" width="115" height="20" forecolor="#000000" backcolor="#FFFFFF" uuid="9d6addea-084a-4611-8ffe-1d8f1712890d"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="10" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Identity-H" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<text><![CDATA[8]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Transparent" x="519" y="60" width="80" height="20" forecolor="#000000" backcolor="#FFFFFF" uuid="56cfccab-dcbe-404e-bc39-b10c7c494a27"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="10" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Identity-H" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<text><![CDATA[9]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Transparent" x="599" y="60" width="85" height="20" forecolor="#000000" backcolor="#FFFFFF" uuid="40fa4098-912c-4427-9e09-10e9fe532a3f"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="10" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Identity-H" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<text><![CDATA[10]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Transparent" x="684" y="60" width="69" height="20" forecolor="#000000" backcolor="#FFFFFF" uuid="a2f27263-ee15-4815-9430-7f8eec831cc3"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="10" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Identity-H" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<text><![CDATA[11]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Transparent" x="753" y="60" width="58" height="20" forecolor="#000000" backcolor="#FFFFFF" uuid="39749616-b966-4bf7-a17e-7a35fc3c53ca"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="10" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Identity-H" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<text><![CDATA[12]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Transparent" x="811" y="60" width="53" height="20" forecolor="#000000" backcolor="#FFFFFF" uuid="16b2f8ac-90f1-48a4-a6b7-78bcda9a9b73"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="10" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Identity-H" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<text><![CDATA[13]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Transparent" x="864" y="60" width="62" height="20" forecolor="#000000" backcolor="#FFFFFF" uuid="2f710f18-492a-4df0-bace-ff7eed91fdc3"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="10" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Identity-H" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<text><![CDATA[14]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Transparent" x="1097" y="60" width="37" height="20" forecolor="#000000" backcolor="#FFFFFF" uuid="8b5d4e96-cb91-43ec-8064-adde7ddbf813"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="10" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Identity-H" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<text><![CDATA[18]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Transparent" x="1026" y="60" width="71" height="20" forecolor="#000000" backcolor="#FFFFFF" uuid="0b8ec0f4-68a7-4682-820c-edd1d77cacd4"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="10" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Identity-H" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<text><![CDATA[17]]></text>
			</staticText>
			<staticText>
				<reportElement x="519" y="0" width="80" height="60" uuid="53f577ae-4a5c-4ee8-b2f6-8cefee34d314"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" isBold="true" pdfEncoding="Identity-H"/>
				</textElement>
				<text><![CDATA[Yêu cầu]]></text>
			</staticText>
			<textField>
				<reportElement x="926" y="0" width="52" height="60" uuid="869ccf69-f2b6-409b-8701-1a0cbf687445"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
					<font fontName="Times New Roman" isBold="true" pdfEncoding="Identity-H"/>
				</textElement>
				<textFieldExpression><![CDATA["Phụ"]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="978" y="0" width="48" height="60" uuid="239852cd-9f73-492e-9128-278e3c5bfc0e"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
					<font fontName="Times New Roman" isBold="true" pdfEncoding="Identity-H"/>
				</textElement>
				<textFieldExpression><![CDATA["Giúp việc"]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement mode="Transparent" x="926" y="60" width="52" height="20" forecolor="#000000" backcolor="#FFFFFF" uuid="d7d6843d-90c2-41ac-852b-81269c37cc70"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="10" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Identity-H" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<text><![CDATA[15]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Transparent" x="978" y="60" width="48" height="20" forecolor="#000000" backcolor="#FFFFFF" uuid="19a0248a-6c8b-487e-89a2-8951b47e36a0"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="10" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Identity-H" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<text><![CDATA[16]]></text>
			</staticText>
			<staticText>
				<reportElement x="404" y="0" width="115" height="60" uuid="95adbb00-fce9-4f53-8068-73d3df560d4d"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" isBold="true" pdfEncoding="Identity-H"/>
				</textElement>
				<text><![CDATA[Bác sĩ chỉ định]]></text>
			</staticText>
		</band>
	</columnHeader>
	<detail>
		<band height="20" splitType="Prevent">
			<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToTallestObject" mode="Transparent" x="0" y="0" width="24" height="20" forecolor="#000000" backcolor="#FFFFFF" uuid="cc94f8f1-ff7f-47f4-af17-3ab143f1b31f">
					<property name="net.sf.jasperreports.export.xls.auto.fit.row" value="true"/>
				</reportElement>
				<box topPadding="1" leftPadding="1" bottomPadding="1" rightPadding="1">
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="10" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{REPORT_COUNT}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToTallestObject" mode="Transparent" x="24" y="0" width="80" height="20" forecolor="#000000" backcolor="#FFFFFF" uuid="caa71967-bf79-4bc0-8d77-063abfe3ea70">
					<property name="net.sf.jasperreports.export.xls.auto.fit.row" value="true"/>
				</reportElement>
				<box topPadding="1" leftPadding="2" bottomPadding="1" rightPadding="1">
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="10" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{ho_ten}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToTallestObject" mode="Transparent" x="104" y="0" width="25" height="20" forecolor="#000000" backcolor="#FFFFFF" uuid="71ca27d4-58aa-431a-99ca-da63c120d630">
					<property name="net.sf.jasperreports.export.xls.auto.fit.row" value="true"/>
				</reportElement>
				<box topPadding="1" leftPadding="1" bottomPadding="1" rightPadding="1">
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="10" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{tuoi_nam}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToTallestObject" mode="Transparent" x="129" y="0" width="25" height="20" forecolor="#000000" backcolor="#FFFFFF" uuid="d3460bf6-e37b-4e25-8f81-670fdd750c13">
					<property name="net.sf.jasperreports.export.xls.auto.fit.row" value="true"/>
				</reportElement>
				<box topPadding="1" leftPadding="1" bottomPadding="1" rightPadding="1">
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="10" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{tuoi_nu}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToTallestObject" mode="Transparent" x="154" y="0" width="90" height="20" forecolor="#000000" backcolor="#FFFFFF" uuid="dd186ec3-b2d6-4a5d-90cf-d520e9114d69">
					<property name="net.sf.jasperreports.export.xls.auto.fit.row" value="true"/>
				</reportElement>
				<box topPadding="1" leftPadding="2" bottomPadding="1" rightPadding="1">
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="10" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{dia_chi}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToTallestObject" mode="Transparent" x="244" y="0" width="70" height="20" forecolor="#000000" backcolor="#FFFFFF" uuid="ab811490-ae3a-43ba-b513-cbbf499e2f56">
					<property name="net.sf.jasperreports.export.xls.auto.fit.row" value="true"/>
				</reportElement>
				<box topPadding="1" leftPadding="1" bottomPadding="1" rightPadding="1">
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="10" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[($F{co_bhyt}==null?"":$F{co_bhyt})+($F{noiDangKy}==null?"":" - "+$F{noiDangKy})]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToTallestObject" mode="Transparent" x="314" y="0" width="90" height="20" forecolor="#000000" backcolor="#FFFFFF" uuid="305ac1bf-9ac1-4dbf-88bf-caf16f75a743">
					<property name="net.sf.jasperreports.export.xls.auto.fit.row" value="true"/>
				</reportElement>
				<box topPadding="1" leftPadding="2" bottomPadding="1" rightPadding="1">
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="10" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{chandoan_truoc_tt}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToTallestObject" mode="Transparent" x="404" y="0" width="115" height="20" forecolor="#000000" backcolor="#FFFFFF" uuid="6f56f9fe-5365-4651-a70e-d08487460210">
					<property name="net.sf.jasperreports.export.xls.auto.fit.row" value="true"/>
				</reportElement>
				<box topPadding="1" leftPadding="2" bottomPadding="1" rightPadding="1">
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="10" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{BS_CHIDINH}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToTallestObject" mode="Transparent" x="599" y="0" width="85" height="20" forecolor="#000000" backcolor="#FFFFFF" uuid="b88cf322-4d57-496c-8903-8cdfce7527c7">
					<property name="net.sf.jasperreports.export.xls.auto.fit.row" value="true"/>
				</reportElement>
				<box topPadding="1" leftPadding="2" bottomPadding="1" rightPadding="1">
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="10" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{phuongphap_thuthuat}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToTallestObject" mode="Transparent" x="684" y="0" width="69" height="20" forecolor="#000000" backcolor="#FFFFFF" uuid="b956ac0e-911d-4452-b644-b0510c45b59b">
					<property name="net.sf.jasperreports.export.xls.auto.fit.row" value="true"/>
				</reportElement>
				<box topPadding="1" leftPadding="2" bottomPadding="1" rightPadding="1">
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="10" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{phuongphap_vocam}.equals("1")?"Khác":
$F{phuongphap_vocam}.equals("2")?"Gây mê tĩnh mạch":
$F{phuongphap_vocam}.equals("3")?"Gây mê nội khí quản":
$F{phuongphap_vocam}.equals("4")?"Gây tê tại chỗ":
$F{phuongphap_vocam}.equals("5")?"Tiền mê + gây mê tại chỗ":
$F{phuongphap_vocam}.equals("6")?"Gây tê tủy sống":
$F{phuongphap_vocam}.equals("7")?"Gây tê bề mặt":
$F{phuongphap_vocam}.equals("8")?"Tiền mê + gây tê tại chỗ":
$F{phuongphap_vocam}.equals("9")?"Gây tê hậu cầu":
$F{phuongphap_vocam}.equals("10")?"Gây tê gai Spix":
$F{phuongphap_vocam}.equals("11")?"Gây tê đám rối thần kinh cánh tay đường nách":
$F{phuongphap_vocam}.equals("12")?"Gây tê đám rối thần kinh cánh tay đường trên đòn":
$F{phuongphap_vocam}.equals("13")?"Gây tê đường giữa các cơ bậc thang":
$F{phuongphap_vocam}.equals("14")?"Gây tê góc cổ tay, cổ chân":
$F{phuongphap_vocam}.equals("15")?"Mê Mask thanh quản":
$F{phuongphap_vocam}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToTallestObject" mode="Transparent" x="753" y="0" width="58" height="20" forecolor="#000000" backcolor="#FFFFFF" uuid="41b44b98-821a-42c4-9d13-c422b533607e">
					<property name="net.sf.jasperreports.export.xls.auto.fit.row" value="true"/>
				</reportElement>
				<box topPadding="1" leftPadding="2" bottomPadding="1" rightPadding="1">
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="10" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{ngaygio_thuthuat}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToTallestObject" mode="Transparent" x="811" y="0" width="53" height="20" forecolor="#000000" backcolor="#FFFFFF" uuid="9cfeb8d4-3f30-4f33-8560-6655889e9a18">
					<property name="net.sf.jasperreports.export.xls.auto.fit.row" value="true"/>
				</reportElement>
				<box topPadding="1" leftPadding="2" bottomPadding="1" rightPadding="1">
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="10" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{loai_thuthuat}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToTallestObject" mode="Transparent" x="864" y="0" width="62" height="20" forecolor="#000000" backcolor="#FFFFFF" uuid="7a046d26-22fd-46ad-a0e6-389755cb6aa9">
					<property name="net.sf.jasperreports.export.xls.auto.fit.row" value="true"/>
				</reportElement>
				<box topPadding="1" leftPadding="2" bottomPadding="1" rightPadding="1">
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="10" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{bacsi_thuthuat} != null ? (
($F{bacsi_thuthuat}.indexOf(":") != -1 && $F{bacsi_thuthuat}.split(":").length > 0) ? 
$F{bacsi_thuthuat}.split(":")[0] : ""
) : ""]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToTallestObject" mode="Transparent" x="1026" y="0" width="71" height="20" forecolor="#000000" backcolor="#FFFFFF" uuid="b8708152-592a-4717-a9d0-b777e8ad16ba">
					<property name="net.sf.jasperreports.export.xls.auto.fit.row" value="true"/>
				</reportElement>
				<box topPadding="1" leftPadding="2" bottomPadding="1" rightPadding="1">
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="10" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{bacsi_gayme}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToTallestObject" mode="Transparent" x="1097" y="0" width="37" height="20" forecolor="#000000" backcolor="#FFFFFF" uuid="a7c45204-325d-417b-b717-e0a73af4fddf">
					<property name="net.sf.jasperreports.export.xls.auto.fit.row" value="true"/>
				</reportElement>
				<box topPadding="1" leftPadding="2" bottomPadding="1" rightPadding="1">
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="10" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{ghichu}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToTallestObject" mode="Transparent" x="519" y="0" width="80" height="20" forecolor="#000000" backcolor="#FFFFFF" uuid="1351d401-9c64-4d54-bf8e-045fb950dff4">
					<property name="net.sf.jasperreports.export.xls.auto.fit.row" value="true"/>
				</reportElement>
				<box topPadding="1" leftPadding="2" bottomPadding="1" rightPadding="1">
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="10" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{TEN_DV}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToTallestObject" mode="Transparent" x="926" y="0" width="52" height="20" forecolor="#000000" backcolor="#FFFFFF" uuid="2dab60ea-9e45-4e48-9603-9ffe5cf43d80">
					<property name="net.sf.jasperreports.export.xls.auto.fit.row" value="true"/>
				</reportElement>
				<box topPadding="1" leftPadding="2" bottomPadding="1" rightPadding="1">
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="10" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{bacsi_thuthuat} != null ? (($F{bacsi_thuthuat}.indexOf(":") != -1 && $F{bacsi_thuthuat}.split(":").length > 1) ? $F{bacsi_thuthuat}.split(":")[1] : "") : ""]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToTallestObject" mode="Transparent" x="978" y="0" width="48" height="20" forecolor="#000000" backcolor="#FFFFFF" uuid="4446206b-f021-4c74-8650-74a8bc1b3b28">
					<property name="net.sf.jasperreports.export.xls.auto.fit.row" value="true"/>
				</reportElement>
				<box topPadding="1" leftPadding="2" bottomPadding="1" rightPadding="1">
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="10" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{bacsi_thuthuat} != null ? (($F{bacsi_thuthuat}.indexOf(":") != -1 && $F{bacsi_thuthuat}.split(":").length > 2) ? $F{bacsi_thuthuat}.split(":")[2] : "") : ""]]></textFieldExpression>
			</textField>
		</band>
	</detail>
	<pageFooter>
		<band height="12">
			<textField pattern="&apos;Ngày giờ in:&apos; dd/MM/YYYY HH:mm:ss">
				<reportElement x="0" y="0" width="147" height="12" uuid="020c4604-f32c-438d-9ce7-6a02b8d3438a">
					<printWhenExpression><![CDATA[!$P{dvtt}.equals("96014")]]></printWhenExpression>
				</reportElement>
				<textElement verticalAlignment="Bottom">
					<font fontName="Times New Roman" size="9" isItalic="true"/>
				</textElement>
				<textFieldExpression><![CDATA[new Date()]]></textFieldExpression>
			</textField>
		</band>
	</pageFooter>
	<summary>
		<band height="126">
			<textField>
				<reportElement x="0" y="21" width="192" height="21" uuid="d061916a-fdc5-40ac-a519-9d04ea265b5a"/>
				<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
					<font fontName="Times New Roman" isBold="true" pdfEncoding="Identity-H"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{dvtt}.equals("96004") ? "NGƯỜI LẬP" : $P{dvtt}.equals("96003") ? "NGƯỜI IN BÁO CÁO" : "GIÁM ĐỐC"]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="598" y="21" width="192" height="21" uuid="b31f89ac-74fb-43aa-9466-673daf18e43d"/>
				<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
					<font fontName="Times New Roman" isBold="true" pdfEncoding="Identity-H"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{dvtt}.equals("96004") ? "TRƯỞNG KHOA CẬN LÂM SÀNG" : $P{dvtt}.equals("96003") ? "" : "TRƯỞNG KHOA"]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="942" y="21" width="192" height="21" uuid="236f98a3-**************-32415d1b0180"/>
				<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
					<font fontName="Times New Roman" isBold="true" pdfEncoding="Identity-H"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{dvtt}.equals("96004") ? "GIÁM ĐỐC" : $P{dvtt}.equals("96003") ? "THỦ TRƯỞNG ĐƠN VỊ" : "NGƯỜI LẬP"]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="942" y="0" width="192" height="21" uuid="6d805440-7c01-4d51-a5f5-b078fcc2ed4b"/>
				<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
					<font fontName="Times New Roman" isBold="false" isItalic="true" pdfEncoding="Identity-H"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{dvtt}.equals("96004") ? "" : $P{dvtt}.equals("96003") ? "Cà Mau, ngày "+new SimpleDateFormat("dd").format(new Date())+" tháng "+new SimpleDateFormat("MM").format(new Date())+" năm "+new SimpleDateFormat("yyyy").format(new Date()) : "Ngày.............Tháng................Năm..........."]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="0" y="0" width="192" height="21" uuid="bb1a5706-63f1-4afe-b2ff-f0a94f43aa30"/>
				<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
					<font fontName="Times New Roman" isBold="false" isItalic="true" pdfEncoding="Identity-H"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{dvtt}.equals("96004") ? "Ngày.............Tháng................Năm..........." : ""]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="307" y="21" width="192" height="21" uuid="3b6fbb51-f824-4e96-9ada-f34202d4fc5d"/>
				<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
					<font fontName="Times New Roman" isBold="true" pdfEncoding="Identity-H"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{dvtt}.equals("96030") ? "PHÒNG KH-NV" : $P{dvtt}.equals("96003") ? "PHÒNG KHÁM QLSKCB" : ""]]></textFieldExpression>
			</textField>
		</band>
	</summary>
</jasperReport>
