<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="rp_thongtu50" language="groovy" pageWidth="842" pageHeight="595" orientation="Landscape" whenNoDataType="AllSectionsNoDetail" columnWidth="814" leftMargin="14" rightMargin="14" topMargin="14" bottomMargin="14" uuid="bda23bd8-0dd3-421d-b93d-4c4886d2225e">
	<property name="ireport.zoom" value="1.3310000000000028"/>
	<property name="ireport.x" value="269"/>
	<property name="ireport.y" value="0"/>
	<parameter name="soyte" class="java.lang.String"/>
	<parameter name="tenbenhvien" class="java.lang.String"/>
	<parameter name="sovaovien" class="java.lang.String"/>
	<parameter name="mabenhnhan" class="java.lang.String"/>
	<parameter name="dvtt" class="java.lang.String"/>
	<parameter name="sovaovien_dt" class="java.lang.String"/>
	<parameter name="ngay" class="java.lang.String"/>
	<parameter name="phongban" class="java.lang.String"/>
	<parameter name="stt_ddt" class="java.lang.String"/>
	<parameter name="nguoilapbieu" class="java.lang.String"/>
	<parameter name="bant" class="java.lang.String"/>
	<queryString language="plsql">
		<![CDATA[{call CMU_PL_TT50($P{dvtt},$P{mabenhnhan},$P{sovaovien},$P{sovaovien_dt},$P{stt_ddt},
$P{ngay},$P{phongban},1,$P{ORACLE_REF_CURSOR})}]]>
	</queryString>
	<field name="TEN_DV" class="java.lang.String"/>
	<field name="DVT" class="java.lang.String"/>
	<field name="GHI_CHU" class="java.lang.String"/>
	<field name="SOBENHAN" class="java.lang.String"/>
	<field name="KHOA" class="java.lang.String"/>
	<field name="TEN_BN" class="java.lang.String"/>
	<field name="NGAYSINH" class="java.lang.String"/>
	<field name="GIOITINH" class="java.lang.Integer"/>
	<field name="SOGIUONG" class="java.lang.String"/>
	<field name="SOBUONG" class="java.lang.String"/>
	<field name="NGAYVAO" class="java.lang.String"/>
	<field name="CHANDOAN" class="java.lang.String"/>
	<field name="LOAI_DV" class="java.lang.String"/>
	<field name="PHONGBAN" class="java.lang.String"/>
	<field name="NGAY_BD" class="java.util.Date"/>
	<field name="SL1" class="java.lang.Integer"/>
	<field name="SL2" class="java.lang.Integer"/>
	<field name="SL3" class="java.lang.Integer"/>
	<field name="SL4" class="java.lang.Integer"/>
	<field name="SL5" class="java.lang.Integer"/>
	<field name="SL6" class="java.lang.Integer"/>
	<field name="SL7" class="java.lang.Integer"/>
	<field name="SL8" class="java.lang.Integer"/>
	<field name="SL9" class="java.lang.Integer"/>
	<field name="SL10" class="java.lang.Integer"/>
	<field name="SL11" class="java.lang.Integer"/>
	<field name="SL12" class="java.lang.Integer"/>
	<field name="SL13" class="java.lang.Integer"/>
	<field name="SL14" class="java.lang.Integer"/>
	<field name="SL15" class="java.lang.Integer"/>
	<field name="NGAYCUOI" class="java.util.Date"/>
	<variable name="V_G_LOAI_DV_COUNT" class="java.lang.Integer" incrementType="Group" incrementGroup="G_LOAI_DV" calculation="Count">
		<variableExpression><![CDATA[$F{LOAI_DV}]]></variableExpression>
		<initialValueExpression><![CDATA[]]></initialValueExpression>
	</variable>
	<group name="G_LOAI_DV">
		<groupExpression><![CDATA[$F{LOAI_DV}]]></groupExpression>
		<groupHeader>
			<band height="20">
				<textField isStretchWithOverflow="true" isBlankWhenNull="true">
					<reportElement x="25" y="0" width="789" height="20" uuid="a720875f-eab0-4892-b6b1-77273ff606fa"/>
					<box leftPadding="3">
						<pen lineWidth="0.5"/>
						<topPen lineWidth="0.5"/>
						<leftPen lineWidth="0.5"/>
						<bottomPen lineWidth="0.5"/>
						<rightPen lineWidth="0.5"/>
					</box>
					<textElement textAlignment="Justified" verticalAlignment="Middle" markup="none">
						<font fontName="Times New Roman" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{LOAI_DV}.equals("1.XN")?"Xét nghiệm":
$F{LOAI_DV}.equals("2.CDHA")?"Chẩn đoán hình ảnh":
$F{LOAI_DV}.equals("3.TDCN")?"Thăm dò chức năng":
$F{LOAI_DV}.equals("4.TTPT")?"Dịch vụ kỹ thuật":
$F{LOAI_DV}.equals("5.MAU")?"Máu và chế phẩm máu":
$F{LOAI_DV}.equals("6.THUOC")?"Thuốc, dịch truyền":
$F{LOAI_DV}.equals("7.VTYT")?"Vật tư y tế":
"Khác"]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true" isBlankWhenNull="true">
					<reportElement x="0" y="0" width="25" height="20" uuid="40d0d9a1-2ccb-41a7-9e7c-3e00f77857a1"/>
					<box>
						<pen lineWidth="0.5"/>
						<topPen lineWidth="0.5"/>
						<leftPen lineWidth="0.5"/>
						<bottomPen lineWidth="0.5"/>
						<rightPen lineWidth="0.5"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="Times New Roman"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{V_G_LOAI_DV_COUNT}==null?1: $V{V_G_LOAI_DV_COUNT} + 1]]></textFieldExpression>
				</textField>
			</band>
		</groupHeader>
	</group>
	<background>
		<band splitType="Stretch"/>
	</background>
	<title>
		<band height="110" splitType="Stretch">
			<textField>
				<reportElement x="0" y="14" width="236" height="14" uuid="375891dd-3c2f-40e0-8500-9b7ebdc328b3"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{tenbenhvien}]]></textFieldExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="false">
				<reportElement mode="Transparent" x="0" y="0" width="236" height="14" forecolor="#000000" backcolor="#FFFFFF" uuid="a1ee4c23-a072-4141-bca1-d4fe53657e83"/>
				<textElement verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{soyte}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="0" y="28" width="236" height="14" uuid="6bcbfc5e-81a7-48b5-8df7-3987215e3500"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman"/>
				</textElement>
				<textFieldExpression><![CDATA["Số vào viện: " + $F{SOBENHAN}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="0" y="42" width="236" height="14" uuid="e316d21c-5fc4-4a0d-b9a1-d6f09334dc36"/>
				<textElement verticalAlignment="Middle" markup="none">
					<font fontName="Times New Roman"/>
				</textElement>
				<textFieldExpression><![CDATA["Khoa: " + $F{PHONGBAN}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement x="560" y="0" width="233" height="14" uuid="6fde9b90-5821-4bf8-9c9e-30a790f35ae0"/>
				<textElement verticalAlignment="Middle" markup="html">
					<font fontName="Times New Roman"/>
				</textElement>
				<textFieldExpression><![CDATA["Người bệnh: <b>" + $F{TEN_BN} + "</b>"]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement x="560" y="14" width="136" height="14" uuid="38a51164-5b36-4a59-bcac-9b774241fcd9"/>
				<textElement verticalAlignment="Middle" markup="none">
					<font fontName="Times New Roman"/>
				</textElement>
				<textFieldExpression><![CDATA["Ngày sinh: " + $F{NGAYSINH}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="696" y="14" width="118" height="14" uuid="e065ab0a-d74d-465b-a0a6-************"/>
				<textElement textAlignment="Right" verticalAlignment="Middle" markup="none">
					<font fontName="Times New Roman"/>
				</textElement>
				<textFieldExpression><![CDATA["Giới tính: " + ($F{GIOITINH} > 0 ? "Nam" : "Nữ")]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement x="560" y="28" width="136" height="14" uuid="00d40c1a-ea71-4e60-ae9d-217dbfd15872"/>
				<textElement verticalAlignment="Middle" markup="none">
					<font fontName="Times New Roman"/>
				</textElement>
				<textFieldExpression><![CDATA["Giường:" + $F{SOGIUONG} + "  Buồng: " + $F{SOBUONG}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement x="696" y="28" width="118" height="14" uuid="4907de03-f249-48df-933f-3cbd169f110f"/>
				<textElement verticalAlignment="Middle" markup="none">
					<font fontName="Times New Roman"/>
				</textElement>
				<textFieldExpression><![CDATA["Ngày vào: " + $F{NGAYVAO}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement x="560" y="42" width="254" height="14" uuid="75025252-a7d2-43e2-9af3-1d667d0744f1"/>
				<textElement verticalAlignment="Middle" markup="none">
					<font fontName="Times New Roman"/>
				</textElement>
				<textFieldExpression><![CDATA["Chẩn đoán: " + $F{CHANDOAN}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement positionType="FixRelativeToBottom" x="0" y="60" width="25" height="50" uuid="58150728-bc9e-4aab-9483-056a29f478c4"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
					<font fontName="Times New Roman" isBold="true"/>
				</textElement>
				<text><![CDATA[TT]]></text>
			</staticText>
			<staticText>
				<reportElement positionType="FixRelativeToBottom" x="25" y="60" width="211" height="50" uuid="5e58e1df-9482-4aeb-b1eb-ba33f06fd83b"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
					<font fontName="Times New Roman" isBold="true"/>
				</textElement>
				<text><![CDATA[Tên Dịch vụ khám bệnh, chữa bệnh]]></text>
			</staticText>
			<staticText>
				<reportElement positionType="FixRelativeToBottom" x="236" y="60" width="39" height="50" uuid="8f941894-1a3f-45f9-9f33-85699f91d0ed"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
					<font fontName="Times New Roman" isBold="true"/>
				</textElement>
				<text><![CDATA[Đơn
vị]]></text>
			</staticText>
			<staticText>
				<reportElement positionType="FixRelativeToBottom" x="275" y="60" width="510" height="31" uuid="179d7da3-83f6-41f3-a90d-b65d1d525087"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
					<font fontName="Times New Roman" isBold="true"/>
				</textElement>
				<text><![CDATA[Số lượng
(theo ngày/ tháng)]]></text>
			</staticText>
			<staticText>
				<reportElement positionType="FixRelativeToBottom" x="785" y="60" width="29" height="50" uuid="fee9f3ab-dfdd-4afc-9127-b90bf29d48f3"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
					<font fontName="Times New Roman" isBold="true"/>
				</textElement>
				<text><![CDATA[Ghi chú]]></text>
			</staticText>
			<textField pattern="dd/MM" isBlankWhenNull="true">
				<reportElement positionType="FixRelativeToBottom" x="275" y="91" width="34" height="19" uuid="d7b1105b-b200-43c8-9604-1940a65c2ea9"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
					<font fontName="Times New Roman" isBold="false" isItalic="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{NGAY_BD}]]></textFieldExpression>
			</textField>
			<textField pattern="dd/MM" isBlankWhenNull="true">
				<reportElement positionType="FixRelativeToBottom" x="309" y="91" width="34" height="19" uuid="fb136df4-908f-429f-83d5-de5ac0718e98"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
					<font fontName="Times New Roman" isBold="false" isItalic="true"/>
				</textElement>
				<textFieldExpression><![CDATA[new java.util.Date($F{NGAY_BD}.getTime() + 24*60*60*1000).compareTo($F{NGAYCUOI}) <= 0 ?
new java.util.Date($F{NGAY_BD}.getTime() + 24*60*60*1000): null]]></textFieldExpression>
			</textField>
			<textField pattern="dd/MM" isBlankWhenNull="true">
				<reportElement positionType="FixRelativeToBottom" x="343" y="91" width="34" height="19" uuid="97fa3d6d-aad6-4d46-a913-98ae610aeb0c"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
					<font fontName="Times New Roman" isBold="false" isItalic="true"/>
				</textElement>
				<textFieldExpression><![CDATA[new java.util.Date($F{NGAY_BD}.getTime() + 2*24*60*60*1000).compareTo($F{NGAYCUOI}) <= 0 ?
new java.util.Date($F{NGAY_BD}.getTime() + 2*24*60*60*1000): null]]></textFieldExpression>
			</textField>
			<textField pattern="dd/MM" isBlankWhenNull="true">
				<reportElement positionType="FixRelativeToBottom" x="377" y="91" width="34" height="19" uuid="a6792024-889a-421c-9f93-ec92f1071833"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
					<font fontName="Times New Roman" isBold="false" isItalic="true"/>
				</textElement>
				<textFieldExpression><![CDATA[new java.util.Date($F{NGAY_BD}.getTime() + 3*24*60*60*1000).compareTo($F{NGAYCUOI}) <= 0 ?
new java.util.Date($F{NGAY_BD}.getTime() + 3*24*60*60*1000): null]]></textFieldExpression>
			</textField>
			<textField pattern="dd/MM" isBlankWhenNull="true">
				<reportElement positionType="FixRelativeToBottom" x="411" y="91" width="34" height="19" uuid="02c46a7f-a2ed-4d7e-a4ac-d729640a51e7"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
					<font fontName="Times New Roman" isBold="false" isItalic="true"/>
				</textElement>
				<textFieldExpression><![CDATA[new java.util.Date($F{NGAY_BD}.getTime() + 4*24*60*60*1000).compareTo($F{NGAYCUOI}) <= 0 ?
new java.util.Date($F{NGAY_BD}.getTime() + 4*24*60*60*1000): null]]></textFieldExpression>
			</textField>
			<textField pattern="dd/MM" isBlankWhenNull="true">
				<reportElement positionType="FixRelativeToBottom" x="445" y="91" width="34" height="19" uuid="eeb1ffe0-12da-496f-a32c-2059cf35cda4"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
					<font fontName="Times New Roman" isBold="false" isItalic="true"/>
				</textElement>
				<textFieldExpression><![CDATA[new java.util.Date($F{NGAY_BD}.getTime() + 5*24*60*60*1000).compareTo($F{NGAYCUOI}) <= 0 ?
new java.util.Date($F{NGAY_BD}.getTime() + 5*24*60*60*1000): null]]></textFieldExpression>
			</textField>
			<textField pattern="dd/MM" isBlankWhenNull="true">
				<reportElement positionType="FixRelativeToBottom" x="479" y="91" width="34" height="19" uuid="89700972-6c31-427b-9296-1fd98da7082b"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
					<font fontName="Times New Roman" isBold="false" isItalic="true"/>
				</textElement>
				<textFieldExpression><![CDATA[new java.util.Date($F{NGAY_BD}.getTime() + 6*24*60*60*1000).compareTo($F{NGAYCUOI}) <= 0 ?
new java.util.Date($F{NGAY_BD}.getTime() + 6*24*60*60*1000): null]]></textFieldExpression>
			</textField>
			<textField pattern="dd/MM" isBlankWhenNull="true">
				<reportElement positionType="FixRelativeToBottom" x="513" y="91" width="34" height="19" uuid="bdf71ce9-5bb3-4023-94c6-259c15534b76"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
					<font fontName="Times New Roman" isBold="false" isItalic="true"/>
				</textElement>
				<textFieldExpression><![CDATA[new java.util.Date($F{NGAY_BD}.getTime() + 7*24*60*60*1000).compareTo($F{NGAYCUOI}) <= 0 ?
new java.util.Date($F{NGAY_BD}.getTime() + 7*24*60*60*1000): null]]></textFieldExpression>
			</textField>
			<textField pattern="dd/MM" isBlankWhenNull="true">
				<reportElement positionType="FixRelativeToBottom" x="547" y="91" width="34" height="19" uuid="4bd532fc-c9fc-4b4f-a646-72a65bd8f9e7"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
					<font fontName="Times New Roman" isBold="false" isItalic="true"/>
				</textElement>
				<textFieldExpression><![CDATA[new java.util.Date($F{NGAY_BD}.getTime() + 8*24*60*60*1000).compareTo($F{NGAYCUOI}) <= 0 ?
new java.util.Date($F{NGAY_BD}.getTime() + 8*24*60*60*1000): null]]></textFieldExpression>
			</textField>
			<textField pattern="dd/MM" isBlankWhenNull="true">
				<reportElement positionType="FixRelativeToBottom" x="581" y="91" width="34" height="19" uuid="6ab0403e-0949-47f9-824e-6c334095e17d"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
					<font fontName="Times New Roman" isBold="false" isItalic="true"/>
				</textElement>
				<textFieldExpression><![CDATA[new java.util.Date($F{NGAY_BD}.getTime() + 9*24*60*60*1000).compareTo($F{NGAYCUOI}) <= 0 ?
new java.util.Date($F{NGAY_BD}.getTime() + 9*24*60*60*1000): null]]></textFieldExpression>
			</textField>
			<textField pattern="dd/MM" isBlankWhenNull="true">
				<reportElement positionType="FixRelativeToBottom" x="615" y="91" width="34" height="19" uuid="24d2e97f-3334-4b26-b7f6-78782a5efd65"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
					<font fontName="Times New Roman" isBold="false" isItalic="true"/>
				</textElement>
				<textFieldExpression><![CDATA[new java.util.Date($F{NGAY_BD}.getTime() + 10*24*60*60*1000).compareTo($F{NGAYCUOI}) <= 0 ?
new java.util.Date($F{NGAY_BD}.getTime() + 10*24*60*60*1000): null]]></textFieldExpression>
			</textField>
			<textField pattern="dd/MM" isBlankWhenNull="true">
				<reportElement positionType="FixRelativeToBottom" x="649" y="91" width="34" height="19" uuid="023e881b-46a6-4cd2-a787-4a06444e3913"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
					<font fontName="Times New Roman" isBold="false" isItalic="true"/>
				</textElement>
				<textFieldExpression><![CDATA[new java.util.Date($F{NGAY_BD}.getTime() + 11*24*60*60*1000).compareTo($F{NGAYCUOI}) <= 0 ?
new java.util.Date($F{NGAY_BD}.getTime() + 11*24*60*60*1000): null]]></textFieldExpression>
			</textField>
			<textField pattern="dd/MM" isBlankWhenNull="true">
				<reportElement positionType="FixRelativeToBottom" x="683" y="91" width="34" height="19" uuid="749188e9-b341-4dc3-8156-fad8c09f8e2d"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
					<font fontName="Times New Roman" isBold="false" isItalic="true"/>
				</textElement>
				<textFieldExpression><![CDATA[new java.util.Date($F{NGAY_BD}.getTime() + 12*24*60*60*1000).compareTo($F{NGAYCUOI}) <= 0 ?
new java.util.Date($F{NGAY_BD}.getTime() + 12*24*60*60*1000): null]]></textFieldExpression>
			</textField>
			<textField pattern="dd/MM" isBlankWhenNull="true">
				<reportElement positionType="FixRelativeToBottom" x="717" y="91" width="34" height="19" uuid="70186192-72c4-4c12-8b01-5c2523cca776"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
					<font fontName="Times New Roman" isBold="false" isItalic="true"/>
				</textElement>
				<textFieldExpression><![CDATA[new java.util.Date($F{NGAY_BD}.getTime() + 13*24*60*60*1000).compareTo($F{NGAYCUOI}) <= 0 ?
new java.util.Date($F{NGAY_BD}.getTime() + 13*24*60*60*1000): null]]></textFieldExpression>
			</textField>
			<textField pattern="dd/MM" isBlankWhenNull="true">
				<reportElement positionType="FixRelativeToBottom" x="751" y="91" width="34" height="19" uuid="078dbf04-37eb-4821-86d9-34d6de70f1a1"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
					<font fontName="Times New Roman" isBold="false" isItalic="true"/>
				</textElement>
				<textFieldExpression><![CDATA[new java.util.Date($F{NGAY_BD}.getTime() + 14*24*60*60*1000).compareTo($F{NGAYCUOI}) <= 0 ?
new java.util.Date($F{NGAY_BD}.getTime() + 14*24*60*60*1000): null]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement x="265" y="0" width="263" height="56" uuid="5550cc79-3752-47dd-a534-fafb09b40455"/>
				<textElement textAlignment="Center" verticalAlignment="Middle" markup="html">
					<font fontName="Times New Roman" size="16" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA["PHIẾU CÔNG KHAI DỊCH VỤ KHÁM, CHỮA BỆNH " + (
$P{bant}.toString().equals( "1" )? 
"NGOẠI TRÚ" :
"NỘI TRÚ"
)]]></textFieldExpression>
			</textField>
		</band>
	</title>
	<detail>
		<band height="20" splitType="Stretch">
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToTallestObject" x="275" y="0" width="34" height="20" uuid="1c46ce98-9b9a-4220-9b26-b89d170675e0"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.0"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.25" lineStyle="Dashed"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
					<font fontName="Times New Roman" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{SL1} > 0?$F{SL1}:null]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToTallestObject" x="25" y="0" width="211" height="20" uuid="1e5dd088-b311-4071-a4aa-4e2b65801357">
					<property name="net.sf.jasperreports.export.xls.auto.fit.row" value="true"/>
				</reportElement>
				<box leftPadding="5" rightPadding="2">
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.0"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.25" lineStyle="Dashed"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Justified" verticalAlignment="Middle" markup="none">
					<font fontName="Times New Roman" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{TEN_DV}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToTallestObject" x="236" y="0" width="39" height="20" uuid="6b5056ba-16c7-407d-bff1-33aa1d8947b8"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.0"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.25" lineStyle="Dashed"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
					<font fontName="Times New Roman" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{DVT}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToTallestObject" x="0" y="0" width="25" height="20" uuid="8008a564-187f-4817-9661-dc1848413766"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.0"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.25" lineStyle="Dashed"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
					<font fontName="Times New Roman" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{G_LOAI_DV_COUNT} < 10 ? ("0"+$V{G_LOAI_DV_COUNT}): $V{G_LOAI_DV_COUNT}+""]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToTallestObject" x="785" y="0" width="29" height="20" uuid="d81cd6d8-d18d-4ac8-9e17-9067a84cd963"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.0"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.25" lineStyle="Dashed"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
					<font fontName="Times New Roman" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{GHI_CHU}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToTallestObject" x="309" y="0" width="34" height="20" uuid="da0eff26-f993-4f1b-b418-331f1cfdb9f8"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.0"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.25" lineStyle="Dashed"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
					<font fontName="Times New Roman" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{SL2} > 0 ? $F{SL2} : null]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToTallestObject" x="343" y="0" width="34" height="20" uuid="9be5553d-d6c6-4f75-a935-5492e03672fb"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.0"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.25" lineStyle="Dashed"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
					<font fontName="Times New Roman" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{SL3} > 0 ? $F{SL3} : null]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToTallestObject" x="377" y="0" width="34" height="20" uuid="bf8d69b0-6fbe-4506-b6a3-1cd8d73d75ec"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.0"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.25" lineStyle="Dashed"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
					<font fontName="Times New Roman" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{SL4} > 0 ? $F{SL4} : null]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToTallestObject" x="411" y="0" width="34" height="20" uuid="7e0257dd-6b70-45ef-a017-c147fd297818"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.0"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.25" lineStyle="Dashed"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
					<font fontName="Times New Roman" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{SL5} > 0 ? $F{SL5} : null]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToTallestObject" x="445" y="0" width="34" height="20" uuid="3c69e7d7-70e0-4af8-83d7-02ff7c9aa379"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.0"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.25" lineStyle="Dashed"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
					<font fontName="Times New Roman" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{SL6} > 0 ? $F{SL6} : null]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToTallestObject" x="479" y="0" width="34" height="20" uuid="4d36b6fa-c3a6-4b4c-95ca-6327a50d778a"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.0"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.25" lineStyle="Dashed"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
					<font fontName="Times New Roman" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{SL7} > 0 ? $F{SL7} : null]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToTallestObject" x="513" y="0" width="34" height="20" uuid="1e103c19-5fa2-4ddf-8db6-6b1438885fe7"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.0"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.25" lineStyle="Dashed"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
					<font fontName="Times New Roman" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{SL8} > 0 ? $F{SL8} : null]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToTallestObject" x="547" y="0" width="34" height="20" uuid="efa18dde-d1a7-4591-950e-1ed331d8e5fc"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.0"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.25" lineStyle="Dashed"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
					<font fontName="Times New Roman" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{SL9} > 0 ? $F{SL9} : null]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToTallestObject" x="581" y="0" width="34" height="20" uuid="56925ab8-3af5-4933-a7f9-5c562fd66277"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.0"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.25" lineStyle="Dashed"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
					<font fontName="Times New Roman" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{SL10} > 0 ? $F{SL10} : null]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToTallestObject" x="615" y="0" width="34" height="20" uuid="200be8c8-7bc2-409b-a6ba-307bbb6278e5"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.0"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.25" lineStyle="Dashed"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
					<font fontName="Times New Roman" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{SL11} > 0 ? $F{SL11} : null]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToTallestObject" x="649" y="0" width="34" height="20" uuid="9d1c587b-3467-412e-b3de-08b0c252cea8"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.0"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.25" lineStyle="Dashed"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
					<font fontName="Times New Roman" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{SL12} > 0 ? $F{SL12} : null]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToTallestObject" x="683" y="0" width="34" height="20" uuid="5a16064b-e449-4adc-961f-97c150b6d300"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.0"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.25" lineStyle="Dashed"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
					<font fontName="Times New Roman" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{SL13} > 0 ? $F{SL13} : null]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToTallestObject" x="717" y="0" width="34" height="20" uuid="9f2a4395-d8bb-4c72-bc40-175956c18200"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.0"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.25" lineStyle="Dashed"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
					<font fontName="Times New Roman" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{SL14} > 0 ? $F{SL14} : null]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToTallestObject" x="751" y="0" width="34" height="20" uuid="e7baec2d-7331-4e7c-9d8f-56d79c6a228f"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.0"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.25" lineStyle="Dashed"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
					<font fontName="Times New Roman" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{SL15} > 0 ? $F{SL15} : null]]></textFieldExpression>
			</textField>
		</band>
	</detail>
	<summary>
		<band height="120" splitType="Stretch">
			<staticText>
				<reportElement x="0" y="0" width="275" height="40" uuid="cecf551a-b4fc-494d-b35f-39ad820a3e8d"/>
				<box rightPadding="3">
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle" markup="none">
					<font fontName="Times New Roman" isBold="false"/>
				</textElement>
				<text><![CDATA[Người lập phiếu: ]]></text>
			</staticText>
			<staticText>
				<reportElement x="0" y="40" width="236" height="40" uuid="f89c6185-02b1-48b4-a1fa-ee6f574e9f3c"/>
				<box rightPadding="5">
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5" lineStyle="Dashed"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle" markup="none">
					<font fontName="Times New Roman" isBold="false"/>
				</textElement>
				<text><![CDATA[Xác nhận của người bệnh]]></text>
			</staticText>
			<staticText>
				<reportElement x="0" y="80" width="236" height="40" uuid="f7654566-e4bd-493b-8500-57f2c0e6ea50"/>
				<box rightPadding="5">
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle" markup="none">
					<font fontName="Times New Roman" isBold="false"/>
				</textElement>
				<text><![CDATA[người nhà]]></text>
			</staticText>
			<staticText>
				<reportElement x="236" y="80" width="39" height="40" uuid="d79ba32b-0999-413e-b134-642fada22d55"/>
				<box rightPadding="30">
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle" markup="none">
					<font fontName="Times New Roman" isBold="false"/>
				</textElement>
				<text><![CDATA[]]></text>
			</staticText>
			<staticText>
				<reportElement x="236" y="40" width="39" height="40" uuid="edcd1e7f-95aa-4df5-abf6-ba3e0dc5b860"/>
				<box rightPadding="30">
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5" lineStyle="Dashed"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle" markup="none">
					<font fontName="Times New Roman" isBold="false"/>
				</textElement>
				<text><![CDATA[]]></text>
			</staticText>
			<staticText>
				<reportElement x="785" y="40" width="29" height="40" uuid="fea8e45a-3d31-492c-908e-a43330760704"/>
				<box rightPadding="30">
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5" lineStyle="Solid"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle" markup="none">
					<font fontName="Times New Roman" isBold="false"/>
				</textElement>
				<text><![CDATA[]]></text>
			</staticText>
			<staticText>
				<reportElement x="785" y="80" width="29" height="40" uuid="85351634-c7dc-4e0e-b6be-0e59632e7c33"/>
				<box rightPadding="30">
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle" markup="none">
					<font fontName="Times New Roman" isBold="false"/>
				</textElement>
				<text><![CDATA[]]></text>
			</staticText>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToTallestObject" x="275" y="40" width="34" height="40" uuid="7ac321a8-4a4f-45d6-a88c-10f104b48f1d"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.0"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.25" lineStyle="Dashed"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
					<font fontName="Times New Roman" isBold="false"/>
				</textElement>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToTallestObject" x="309" y="40" width="34" height="40" uuid="56ccaefe-28ef-4ab2-847a-6739a92adb12"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.0"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.25" lineStyle="Dashed"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
					<font fontName="Times New Roman" isBold="false"/>
				</textElement>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToTallestObject" x="377" y="40" width="34" height="40" uuid="6b165c04-c520-4233-9712-06d0c8650cdd"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.0"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.25" lineStyle="Dashed"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
					<font fontName="Times New Roman" isBold="false"/>
				</textElement>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToTallestObject" x="343" y="40" width="34" height="40" uuid="8648d2da-8797-4226-9cd1-9f11193be1f7"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.0"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.25" lineStyle="Dashed"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
					<font fontName="Times New Roman" isBold="false"/>
				</textElement>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToTallestObject" x="479" y="40" width="34" height="40" uuid="fbe1b011-8e3a-4f1c-86be-70924f5ca6b5"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.0"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.25" lineStyle="Dashed"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
					<font fontName="Times New Roman" isBold="false"/>
				</textElement>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToTallestObject" x="513" y="40" width="34" height="40" uuid="e44bb889-3a65-41da-9a01-181a8237df81"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.0"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.25" lineStyle="Dashed"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
					<font fontName="Times New Roman" isBold="false"/>
				</textElement>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToTallestObject" x="445" y="40" width="34" height="40" uuid="d435267b-7111-4614-b182-5fa8cb74e813"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.0"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.25" lineStyle="Dashed"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
					<font fontName="Times New Roman" isBold="false"/>
				</textElement>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToTallestObject" x="411" y="40" width="34" height="40" uuid="29d787fb-2399-43da-a02a-d171b97a177a"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.0"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.25" lineStyle="Dashed"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
					<font fontName="Times New Roman" isBold="false"/>
				</textElement>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToTallestObject" x="581" y="40" width="34" height="40" uuid="a7f260cc-d294-4a5f-87f6-f160a33658f4"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.0"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.25" lineStyle="Dashed"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
					<font fontName="Times New Roman" isBold="false"/>
				</textElement>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToTallestObject" x="547" y="40" width="34" height="40" uuid="f8fcc4f7-1e51-4be0-9862-42ef103e0d69"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.0"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.25" lineStyle="Dashed"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
					<font fontName="Times New Roman" isBold="false"/>
				</textElement>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToTallestObject" x="649" y="40" width="34" height="40" uuid="49f36112-48a9-45b4-ac07-06d8016b7b7d"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.0"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.25" lineStyle="Dashed"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
					<font fontName="Times New Roman" isBold="false"/>
				</textElement>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToTallestObject" x="615" y="40" width="34" height="40" uuid="0b23320d-ec1d-4373-b189-aacc6b68d9b3"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.0"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.25" lineStyle="Dashed"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
					<font fontName="Times New Roman" isBold="false"/>
				</textElement>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToTallestObject" x="683" y="40" width="34" height="40" uuid="0158bcb3-096f-420e-bb02-1f5680769602"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.0"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.25" lineStyle="Dashed"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
					<font fontName="Times New Roman" isBold="false"/>
				</textElement>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToTallestObject" x="717" y="40" width="34" height="40" uuid="cc609c63-02fe-46a2-aa40-525c4f21c3a0"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.0"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.25" lineStyle="Dashed"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
					<font fontName="Times New Roman" isBold="false"/>
				</textElement>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToTallestObject" x="751" y="40" width="34" height="40" uuid="f3282064-5ab9-458d-a798-85f45fe47e06"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.0"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.25" lineStyle="Dashed"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
					<font fontName="Times New Roman" isBold="false"/>
				</textElement>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToTallestObject" x="343" y="80" width="34" height="40" uuid="3a2c67ec-4fa7-441a-a63d-6ac6e263da5a"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.0"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5" lineStyle="Solid"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
					<font fontName="Times New Roman" isBold="false"/>
				</textElement>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToTallestObject" x="377" y="80" width="34" height="40" uuid="e7c2bc55-82bb-4a22-9015-e51890e86914"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.0"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5" lineStyle="Solid"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
					<font fontName="Times New Roman" isBold="false"/>
				</textElement>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToTallestObject" x="309" y="80" width="34" height="40" uuid="f60e1597-fdd9-41b1-9aef-534d532788f0"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.0"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5" lineStyle="Solid"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
					<font fontName="Times New Roman" isBold="false"/>
				</textElement>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToTallestObject" x="275" y="80" width="34" height="40" uuid="be677a43-58bb-411c-8e07-2ceb0c4d0799"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.0"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5" lineStyle="Solid"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
					<font fontName="Times New Roman" isBold="false"/>
				</textElement>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToTallestObject" x="547" y="80" width="34" height="40" uuid="82fcacc6-6a0a-4019-8f90-34f6ed6d5260"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.0"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5" lineStyle="Solid"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
					<font fontName="Times New Roman" isBold="false"/>
				</textElement>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToTallestObject" x="411" y="80" width="34" height="40" uuid="deaddf67-aa72-4cf2-bb49-47467f2b9f68"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.0"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5" lineStyle="Solid"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
					<font fontName="Times New Roman" isBold="false"/>
				</textElement>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToTallestObject" x="751" y="80" width="34" height="40" uuid="eba27396-181e-4d77-b954-3dbe61eb1bd2"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.0"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5" lineStyle="Solid"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
					<font fontName="Times New Roman" isBold="false"/>
				</textElement>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToTallestObject" x="649" y="80" width="34" height="40" uuid="0cfd9908-4c07-4a43-af53-bcbe1157e056"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.0"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5" lineStyle="Solid"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
					<font fontName="Times New Roman" isBold="false"/>
				</textElement>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToTallestObject" x="683" y="80" width="34" height="40" uuid="77aa1f18-d942-4e2d-8b4f-c832f19cc59e"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.0"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5" lineStyle="Solid"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
					<font fontName="Times New Roman" isBold="false"/>
				</textElement>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToTallestObject" x="717" y="80" width="34" height="40" uuid="8c354812-1a26-45c6-9565-ee818f3b2826"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.0"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5" lineStyle="Solid"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
					<font fontName="Times New Roman" isBold="false"/>
				</textElement>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToTallestObject" x="479" y="80" width="34" height="40" uuid="02924ce7-46d0-4dbd-b541-ace3252f51cf"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.0"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5" lineStyle="Solid"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
					<font fontName="Times New Roman" isBold="false"/>
				</textElement>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToTallestObject" x="445" y="80" width="34" height="40" uuid="e617b52e-4c8e-412b-8479-6d728acdb866"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.0"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5" lineStyle="Solid"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
					<font fontName="Times New Roman" isBold="false"/>
				</textElement>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToTallestObject" x="615" y="80" width="34" height="40" uuid="d88430f9-4a13-4745-b96f-82356a733959"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.0"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5" lineStyle="Solid"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
					<font fontName="Times New Roman" isBold="false"/>
				</textElement>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToTallestObject" x="581" y="80" width="34" height="40" uuid="3e0ffd8a-4479-436f-bd2b-5fdb57385e70"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.0"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5" lineStyle="Solid"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
					<font fontName="Times New Roman" isBold="false"/>
				</textElement>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToTallestObject" x="513" y="80" width="34" height="40" uuid="ad4e5541-9a4a-44ed-bded-b66052a0d304"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.0"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5" lineStyle="Solid"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
					<font fontName="Times New Roman" isBold="false"/>
				</textElement>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToTallestObject" x="717" y="0" width="34" height="40" uuid="db251e01-bfe4-4655-99ec-7d607568c9ca"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.0"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.25" lineStyle="Dashed"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
					<font fontName="Times New Roman" isBold="false"/>
				</textElement>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToTallestObject" x="683" y="0" width="34" height="40" uuid="d31a589b-38e6-4c91-b528-e1d6cc25a614"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.0"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.25" lineStyle="Dashed"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
					<font fontName="Times New Roman" isBold="false"/>
				</textElement>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToTallestObject" x="411" y="0" width="34" height="40" uuid="0f2162fb-5c87-4f33-85a8-500cac507223"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.0"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.25" lineStyle="Dashed"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
					<font fontName="Times New Roman" isBold="false"/>
				</textElement>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToTallestObject" x="445" y="0" width="34" height="40" uuid="dd9235ee-1557-451c-8094-fcbc40c8aa7f"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.0"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.25" lineStyle="Dashed"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
					<font fontName="Times New Roman" isBold="false"/>
				</textElement>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToTallestObject" x="343" y="0" width="34" height="40" uuid="c749e149-e292-48ca-9dab-4cb908ce9ff2"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.0"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.25" lineStyle="Dashed"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
					<font fontName="Times New Roman" isBold="false"/>
				</textElement>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToTallestObject" x="275" y="0" width="34" height="40" uuid="b45dff28-6581-4a9f-8c6d-e9cf38199f62"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.0"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.25" lineStyle="Dashed"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
					<font fontName="Times New Roman" isBold="false"/>
				</textElement>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToTallestObject" x="309" y="0" width="34" height="40" uuid="655b5da5-e0df-4ede-9a83-50b1ea442c3d"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.0"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.25" lineStyle="Dashed"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
					<font fontName="Times New Roman" isBold="false"/>
				</textElement>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToTallestObject" x="751" y="0" width="34" height="40" uuid="e48a2639-4baa-4ab8-a2d9-************"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.0"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.25" lineStyle="Dashed"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
					<font fontName="Times New Roman" isBold="false"/>
				</textElement>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToTallestObject" x="649" y="0" width="34" height="40" uuid="4db7803a-97aa-4f4e-b76e-f7b48e16eb7a"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.0"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.25" lineStyle="Dashed"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
					<font fontName="Times New Roman" isBold="false"/>
				</textElement>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToTallestObject" x="547" y="0" width="34" height="40" uuid="8f802e20-73f2-458b-91b2-ceeeb24eeb90"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.0"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.25" lineStyle="Dashed"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
					<font fontName="Times New Roman" isBold="false"/>
				</textElement>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToTallestObject" x="513" y="0" width="34" height="40" uuid="4f5cd573-e317-4cbc-8e41-fc18aad0150b"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.0"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.25" lineStyle="Dashed"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
					<font fontName="Times New Roman" isBold="false"/>
				</textElement>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToTallestObject" x="479" y="0" width="34" height="40" uuid="e1f207c5-6bb7-4daf-bbdf-543414ad1e36"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.0"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.25" lineStyle="Dashed"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
					<font fontName="Times New Roman" isBold="false"/>
				</textElement>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToTallestObject" x="615" y="0" width="34" height="40" uuid="5bcb3a4b-4923-4b4e-9f63-693f7c7a8f42"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.0"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.25" lineStyle="Dashed"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
					<font fontName="Times New Roman" isBold="false"/>
				</textElement>
			</textField>
			<staticText>
				<reportElement x="785" y="0" width="29" height="40" uuid="843f7fbd-24c5-4483-9584-890278bc746d"/>
				<box rightPadding="30">
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5" lineStyle="Solid"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle" markup="none">
					<font fontName="Times New Roman" isBold="false"/>
				</textElement>
				<text><![CDATA[]]></text>
			</staticText>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToTallestObject" x="377" y="0" width="34" height="40" uuid="31fd0a89-479b-4faa-bbe5-c4cfe19fb868"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.0"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.25" lineStyle="Dashed"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
					<font fontName="Times New Roman" isBold="false"/>
				</textElement>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToTallestObject" x="581" y="0" width="34" height="40" uuid="d588ca99-5d13-498e-b87d-5526e5dce8cb"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.0"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.25" lineStyle="Dashed"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
					<font fontName="Times New Roman" isBold="false"/>
				</textElement>
			</textField>
		</band>
	</summary>
</jasperReport>
