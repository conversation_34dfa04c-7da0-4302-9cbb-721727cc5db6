
$(function() {
    $(".input-date").inputmask({
        mask: "1/2/y h:s",
        placeholder: "dd/mm/yyyy hh:mm",
        alias: "datetime",
        hourFormat: "24"
    });
    $(".input-date").datetimepicker({dateFormat: "dd/mm/yy"});
    $(".input-date").val(singletonObject.ngayhientai + " 00:00");
    $(".input-date-second").inputmask({
        mask: "1/2/y h:s:s",
        placeholder: "dd/mm/yyyy hh:mm:ss",
        alias: "datetime",
        hourFormat: "24"
    });
    $(".input-date-second").datetimepicker({
        dateFormat: "dd/mm/yy",
        timeFormat: 'HH:mm:ss',
        showSecond: true,
    });
    $(".input-date-second").val(singletonObject.ngayhientai + " 00:00:00");
    $(".input-only-date").inputmask({
        mask: "1/2/y",
        placeholder: "dd/mm/yyyy",
        alias: "datetime",
        hourFormat: "24"
    });
    $(".input-only-date").datepicker({dateFormat: "dd/mm/yy"});
    $(".input-only-date").val(singletonObject.ngayhientai);
    $(".input-only-time").inputmask({
        mask: "h:s",
        placeholder: "hh:mm",
        alias: "datetime",
        hourFormat: "24"
    });
    $(".input-only-time-second").inputmask({
        mask: "h:s:s",
        placeholder: "hh:mm:ss",
        alias: "datetime",
        hourFormat: "24"
    });
    $("#hsba_tabs").tabs();
    $('#v-menu-tab1').on('shown.bs.tab', function (e) {
        $("#v-menu-tab1").find('.nav-link').removeClass('active');
        $(e.target).addClass('active');
    });

    $(document).on('click', '#bangke_kysokt_action', function() {
        var rowData = getThongtinRowSelected("list_benhnhan");
        kySoChung({
            dvtt: singletonObject.dvtt,
            userId: singletonObject.userId,
            url: $('#iframePreviewAndSign').attr('src'),
            loaiGiay: "PHIEU_NOITRU_BANGKE_KETOAN",
            maBenhNhan: rowData.MA_BENH_NHAN,
            soBenhAn: rowData.STT_BENHAN,
            soPhieuDichVu: rowData.SOBENHAN,
            soVaoVien: rowData.SOVAOVIEN,
            soVaoVienDT: rowData.SOVAOVIEN_DT,
            keyword: "KẾ TOÁN VIỆN PHÍ",
            fileName: "Bảng kê: " + rowData.TEN_BENH_NHAN + " - Số bệnh án: " + rowData.SOBENHAN,
            y2: -80,
            y1: -10,
        }, function(dataKySo) {
            $("#modalPreviewAndSignPDF").modal("hide");
            getDanhSachBenhNhan();
        });
    });

    $(document).on('click', '#bangke_kysolap_action', function() {
        var rowData = getThongtinRowSelected("list_benhnhan");
        kySoChung({
            dvtt: singletonObject.dvtt,
            userId: singletonObject.userId,
            url: $('#iframePreviewAndSign').attr('src'),
            loaiGiay: "PHIEU_NOITRU_BANGKE_NGUOILAP",
            maBenhNhan: rowData.MA_BENH_NHAN,
            soBenhAn: rowData.STT_BENHAN,
            soPhieuDichVu: rowData.SOBENHAN,
            soVaoVien: rowData.SOVAOVIEN,
            soVaoVienDT: rowData.SOVAOVIEN_DT,
            keyword: "NGƯỜI LẬP BẢNG KÊ",
            fileName: "Bảng kê: " + rowData.TEN_BENH_NHAN + " - Số bệnh án: " + rowData.SOBENHAN,
            y2: -80,
            y1: -10,
        }, function(dataKySo) {
            $("#modalPreviewAndSignPDF").modal("hide");
            getDanhSachBenhNhan();
        });
    });

    $("#list_benhnhan").jqGrid({
        url: "cmu_list_CMU_GET_DS_BNDATAO_BADT?url=" + convertArray([singletonObject.dvtt, null, null, $("#ngay_ra_vien").val(), $("#denngay_ra_vien").val(), $("#khoa_phong").val()]),
        datatype: "json",
        loadonce: true,
        styleUI: 'Bootstrap',
        iconSet: 'Octicons',
        width: null,
        shrinkToFit: false,
        height: 400,
        rowNum: 100,
        rowList: [100, 200, 300],
        colModel: [
            {label: 'Sổ vào viện', name: 'SOVAOVIEN', width: 100, hidden:true},
            {label: 'SOVAOVIEN_DT', name: 'SOVAOVIEN_DT', width: 100, hidden:true},
            {label: 'Số bệnh án', name: 'SOBENHAN', width: 100},
            {label: 'Toàn trình', name: 'TOANTRINH', width: 100,
                cellattr: function (rowId, value, rawObject, cm, rdata) {
                    if(!value) {
                        'style="color: #000000;"';
                    }
                    return 'style="color: rgb(0, 123, 255); font-weight: 600"';
                }},
            {label: "GRV/GCT", name: 'GRV', index: 'GRV',hidden: true},
            {label: "GRV/GCT", name: 'GRV_HT', index: 'GRV_HT',width: 150, cellattr: function (cellvalue, options, rowObject) {
                    var classString = 'class="cellWithoutBackground"';
                    var color = 'red';

                    switch(rowObject.GRV) {
                        case '1':
                            grvctht = 'GRV (Trưởng khoa chưa ký)';
                            color = 'red';
                            break;
                        case '2':
                            grvctht = 'GRV (BGĐ chưa ký)';
                            color = 'red';
                            break;
                        case '3':
                            grvctht = 'GRV (Đã ký)';
                            color = 'green';
                            break;
                        case '4':
                            grvctht = 'GCT (BGĐ chưa ký)';
                            color = 'red';
                            break;
                        case '5':
                            grvctht = 'GCT (Đã ký)';
                            color = 'green';
                            break;
                        default:
                            color = 'red';
                            break;
                    }
                    var styleString = 'style="white-space: nowrap; white-space: normal;font-weight:bold ;color:' + color + '"';
                    return classString + ' ' + styleString;
                },
                formatter: function (cellvalue, options, rowObject) {
                    var grvctht = '';
                    var attrColorGRVCT = {
                        background: 'transparent',
                        color: 'white',
                        'font-weight': 600
                    }
                    switch(rowObject.GRV) {
                        case '1':
                            grvctht = 'GRV (Trưởng khoa chưa ký)';
                            break;
                        case '2':
                            grvctht = 'GRV (BGĐ chưa ký)';
                            break;
                        case '3':
                            grvctht = 'GRV (Đã ký)';
                            break;
                        case '4':
                            grvctht = 'GCT (BGĐ chưa ký)';
                            break;
                        case '5':
                            grvctht = 'GCT (Đã ký)';
                            break;
                        default:
                            grvctht = 'Chưa gửi giấy ra viện để ký';
                            break;
                    }
                    return grvctht;
                }
            },
            {
                label: 'Đã ký giao / nhận', name: 'DAKYGIAONHAN', width: 120,
                cellattr: function (rowId, value, rawObject, cm, rdata) {
                    return 'style="color: green; font-weight: 600"';
                }
            },
            {label: 'STT_BENHAN', name: 'STT_BENHAN', width: 10, hidden:true},
            {label: 'STT_DOTDIEUTRI', name: 'STT_DOTDIEUTRI', width: 10, hidden:true},
            {label: 'TRANG_THAI', name: 'TRANG_THAI', width: 10, hidden:true},
            {label: 'MA_BENH_NHAN', name: 'MA_BENH_NHAN', width: 10, hidden:true},
            {
                label: 'Tên bệnh nhân', name: 'TEN_BENH_NHAN', width: 250,
                cellattr: function (rowId, value, rawObject, cm, rdata) {
                    return 'style="color: #000000; font-weight: 600"';
                }
            },
            {label: 'Tuổi', name: 'TUOI', width: 70, align: "center"},
            {label: 'NGAY_SINH', name: 'NGAY_SINH', width: 200, hidden:true},
            {label: 'Địa chỉ', name: 'DIA_CHI', width: 300},
            {label: 'Chẩn đoán vào viện ', name: 'ICD_NHAPVIEN', width: 150},
            {label: 'Tên khoa', name: 'TENKHOA_NHAPVIENVAOKHOA', width: 200},
            {label: 'Ngày nhập viện', name: 'NGAYGIO_NHAPVIEN', width: 200},
            {label: 'BANT', name: 'BANT', width: 200,hidden: true},
            {label: 'Loại hồ sơ', name: 'BANT_HT', width: 200, formatter: function (cellvalue, options, rowObject) {
                    return rowObject.BANT == 1 ? "BA Ngoại trú" : "BA Nội trú";
                }
            },

            {label: 'Người bệnh/Người nhà',name: 'NGUOIBENH_KYTEN', index: 'NGUOIBENH_KYTEN', width: 200, hidden: true},
            {label: 'Kế toán',name: 'KETOAN_KYTEN', index: 'KETOAN_KYTEN', width: 200, hidden: true},
            {label: 'Người lập',name: 'NGUOILAP_KYTEN', index: 'NGUOILAP_KYTEN', width: 200, hidden: true},
            {label: 'GIAYCHUNGTU',name: 'GIAYCHUNGTU', index: 'GIAYCHUNGTU', width: 200, hidden: true},
        ],
        ignoreCase: true,
        caption: "Danh sách bệnh nhân",
        onSelectRow: function (id) {
            resetDataBN();
        },
        onRightClickRow: function (id1) {
            if (id1) {
                $.contextMenu('destroy', '#list_benhnhan tr');
                var rowData = getThongtinRowSelected("list_benhnhan");
                var items = {
                    "xem": {name: '<p><i class="fa fa-eye text-primary" aria-hidden="true"></i> Xem bảng kê</p>'},
                    "lichsu": {name: '<p><i class="fa fa-list text-primary" aria-hidden="true"></i> Lịch sử bệnh án</p>'},
                }
                if (rowData.NGUOIBENH_KYTEN){
                    if (rowData.KETOAN_KYTEN && rowData.NGUOILAP_KYTEN){
                        var daykt, daylap;
                        getFilesign769("PHIEU_NOITRU_BANGKE_KETOAN", rowData.SOBENHAN, -1, singletonObject.dvtt,
                            rowData.SOVAOVIEN, rowData.SOVAOVIEN_DT, -1, function(dataKySo) {
                                if (dataKySo.length > 0) {
                                    daykt = moment(dataKySo[0].CREATE_DATE_STRING, "DD/MM/YYYY HH:mm:ss");
                                }
                            });
                        getFilesign769("PHIEU_NOITRU_BANGKE_NGUOILAP", rowData.SOBENHAN, -1, singletonObject.dvtt,
                            rowData.SOVAOVIEN, rowData.SOVAOVIEN_DT, -1, function(dataKySo) {
                                if (dataKySo.length > 0) {
                                    daylap = moment(dataKySo[0].CREATE_DATE_STRING, "DD/MM/YYYY HH:mm:ss");
                                }
                            });
                        if (daykt.isAfter(daylap)){
                            items = {
                                "huykysokt": {name: '<p><i class="fa fa-key text-danger" aria-hidden="true"></i> Hủy ký số Kế toán</p>'},
                                ...items
                            }
                        }else{
                            items = {
                                "huykysolap": {name: '<p><i class="fa fa-key text-danger" aria-hidden="true"></i> Hủy ký số Người lặp</p>'},
                                ...items
                            }
                        }
                    }else if (rowData.KETOAN_KYTEN){
                        items = {
                            "huykysokt": {name: '<p><i class="fa fa-key text-danger" aria-hidden="true"></i> Hủy ký số Kế toán</p>'},
                            "kysolap": {name: '<p><i class="fa fa-key text-success" aria-hidden="true"></i> Ký số Người lập</p>'},
                            ...items
                        }
                    }else if (rowData.NGUOILAP_KYTEN){
                        items = {
                            "huykysolap": {name: '<p><i class="fa fa-key text-danger" aria-hidden="true"></i> Hủy ký số Người lập</p>'},
                            "kysokt": {name: '<p><i class="fa fa-key text-success" aria-hidden="true"></i> Ký số Kế toán</p>'},
                            ...items
                        }
                    }else{
                        items = {
                            "kysokt": {name: '<p><i class="fa fa-key text-success" aria-hidden="true"></i> Ký số Kế toán</p>'},
                            "kysolap": {name: '<p><i class="fa fa-key text-success" aria-hidden="true"></i> Ký số Người lập</p>'},
                            "huykyso": {name: '<p><i class="fa fa-key text-danger" aria-hidden="true"></i> Hủy ký số BN</p>'},
                            ...items,
                        }
                    }
                } else {
                    items = {
                        "kysophieu": {name: '<p><i class="fa fa-key text-success" aria-hidden="true"></i> Ký số bệnh nhân</p>'},
                        ...items,
                    }
                }
                if(rowData.TRANG_THAI == 4) {
                    items = {
                        ...items,
                        "giaychuyentuyen": {name: '<p><i class="fa fa-eye text-primary" aria-hidden="true"></i> Xem giấy chuyển tuyến</p>'},
                    }
                } else {
                    items = {
                        ...items,
                        "giayravien": {name: '<p><i class="fa fa-eye text-primary" aria-hidden="true"></i> Xem giấy ra viện</p>'},
                        "huygiayravien": {name: '<p><i class="fa fa-trash-o text-danger" aria-hidden="true"></i> Hủy ký số giấy ra viện</p>'},
                    }
                }
                if(rowData.GIAYCHUNGTU) {
                    items = {
                        ...items,
                        "giaychungtu": {name: '<p><i class="fa fa-eye text-primary" aria-hidden="true"></i> Xem giấy chứng tử</p>'},
                    }
                }
                $.contextMenu({
                    selector: '#list_benhnhan tr',
                    callback: function (key, options) {
                        var url;
                        var rowData = getThongtinRowSelected("list_benhnhan");
                        getFilesign769("PHIEU_NOITRU_BANGKE_BENHNHAN", rowData.SOBENHAN, -1, singletonObject.dvtt,
                            rowData.SOVAOVIEN, rowData.SOVAOVIEN_DT, -1, function(dataKySo) {
                                if (dataKySo.length > 0) {
                                    var keyMinio = getCMUFileSigned769GetLinkV2(dataKySo[0].KEYMINIO, 'pdf');
                                    keyMinio != '-1' ? url = keyMinio : url = url;
                                }
                            });
                        if (key == "xem") {
                            var arrTemp = [];
                            getFilesign769("PHIEU_NOITRU_BANGKE_BENHNHAN", rowData.SOBENHAN, -1, singletonObject.dvtt,
                                rowData.SOVAOVIEN, rowData.SOVAOVIEN_DT, -1, function(dataKySo) {
                                    if (dataKySo.length > 0) {
                                        arrTemp.push(dataKySo[0])
                                    }
                                });
                            getFilesign769("PHIEU_NOITRU_BANGKE_KETOAN", rowData.SOBENHAN, -1, singletonObject.dvtt,
                                rowData.SOVAOVIEN, rowData.SOVAOVIEN_DT, -1, function(dataKySo) {
                                    if (dataKySo.length > 0) {
                                        arrTemp.push(dataKySo[0])
                                    }
                                });
                            getFilesign769("PHIEU_NOITRU_BANGKE_NGUOILAP", rowData.SOBENHAN, -1, singletonObject.dvtt,
                                rowData.SOVAOVIEN, rowData.SOVAOVIEN_DT, -1, function(dataKySo) {
                                    if (dataKySo.length > 0) {
                                        arrTemp.push(dataKySo[0])
                                    }
                                });
                            var maxCreateDate = null;
                            var maxCreateDataObject = null;
                            if(arrTemp.length > 0) {
                                $.each(arrTemp, function(index, dataObject) {
                                    var createDate = moment(dataObject.CREATE_DATE_STRING, "DD/MM/YYYY HH:mm:ss");
                                    if (maxCreateDate === null || createDate.isAfter(maxCreateDate)) {
                                        maxCreateDate = createDate;
                                        maxCreateDataObject = dataObject;
                                    }
                                });
                                getCMUFileSigned769(maxCreateDataObject.KEYMINIO, "pdf")
                            } else {
                                noitruTaoBangke(rowData, function(data) {
                                    if(data && data.ERROR) {
                                        return notifiToClient("Red", "Lỗi tạo bảng kê")
                                    }
                                    xembangkenoitru({
                                        ...rowData
                                    }, function() {

                                    }, function () {

                                    }, 0)
                                })
                            }
                        }
                        if (key == "lichsu"){
                            initGridLichSuBenhAn();
                            loadGridLichSuBenhAn(rowData.SOVAOVIEN);
                            $("#modalDSLichSuBenhAn").modal("show");
                        }
                        if (key == "huykyso") {
                            confirmToClient("Bạn có chắc sẽ hủy ký số phiếu này?", function() {
                                huykysoFilesign769("PHIEU_NOITRU_BANGKE_BENHNHAN", rowData.SOBENHAN, singletonObject.userId, singletonObject.dvtt,
                                    rowData.SOVAOVIEN, rowData.SOVAOVIEN_DT, -1, function(data) {
                                        getDanhSachBenhNhan();
                                    })
                            }, function () {

                            })
                        }
                        if (key == "huykysokt") {
                            confirmToClient("Bạn có chắc sẽ hủy ký số phiếu này?", function() {
                                huykysoFilesign769("PHIEU_NOITRU_BANGKE_KETOAN", rowData.SOBENHAN, singletonObject.userId, singletonObject.dvtt,
                                    rowData.SOVAOVIEN, rowData.SOVAOVIEN_DT, -1, function(data) {
                                        getDanhSachBenhNhan();
                                    })
                            }, function () {

                            })
                        }
                        if (key == "huykysolap") {
                            confirmToClient("Bạn có chắc sẽ hủy ký số phiếu này?", function() {
                                huykysoFilesign769("PHIEU_NOITRU_BANGKE_NGUOILAP", rowData.SOBENHAN, singletonObject.userId, singletonObject.dvtt,
                                    rowData.SOVAOVIEN, rowData.SOVAOVIEN_DT, -1, function(data) {
                                        getDanhSachBenhNhan();
                                    })
                            }, function () {

                            })
                        }
                        if (key == "kysokt") {
                            getFilesign769("PHIEU_NOITRU_BANGKE_NGUOILAP", rowData.SOBENHAN, -1, singletonObject.dvtt,
                                rowData.SOVAOVIEN, rowData.SOVAOVIEN_DT, -1, function(dataKySo) {
                                    if (dataKySo.length > 0) {
                                        url = getCMUFileSigned769GetLinkV2(dataKySo[0].KEYMINIO, 'pdf');
                                    }
                                    previewAndSignPdfDefaultModal({
                                        url: url,
                                        idButton: 'bangke_kysokt_action',
                                    }, function(){

                                    });
                                });
                        }
                        if (key == "kysolap") {
                            getFilesign769("PHIEU_NOITRU_BANGKE_KETOAN", rowData.SOBENHAN, -1, singletonObject.dvtt,
                                rowData.SOVAOVIEN, rowData.SOVAOVIEN_DT, -1, function(dataKySo) {
                                    if (dataKySo.length > 0) {
                                        url = getCMUFileSigned769GetLinkV2(dataKySo[0].KEYMINIO, 'pdf');
                                    }
                                    console.log(url)
                                    previewAndSignPdfDefaultModal({
                                        url: url,
                                        idButton: 'bangke_kysolap_action',
                                    }, function(){

                                    });
                                });
                        }
                        if (key == "kysophieu") {
                            var params = {
                                dvtt: singletonObject.dvtt,
                                SOVAOVIEN: rowData.SOVAOVIEN,
                                SOVAOVIEN_DT: rowData.SOVAOVIEN_DT,
                                STT_BENHAN: rowData.STT_BENHAN,
                                STT_DOTDIEUTRI: rowData.STT_DOTDIEUTRI,
                                SOBENHAN: rowData.SOBENHAN,
                                SOPHIEUTHANHTOAN: rowData.SOPHIEUTHANHTOAN,
                                MABENHNHAN: rowData.MA_BENH_NHAN
                            }

                            getUrlBangKeNoiTru(params).then(objReturn => {
                                if (objReturn.isError == 0) {
                                    var P_ID_NHAN_KHAU = 0;
                                    $.ajax({
                                        url: "check_out_byt_noi_tru?sovaovien_dt=" + rowData.SOVAOVIEN_DT + "&sovaovien=" + rowData.SOVAOVIEN + "&dvtt=" + singletonObject.dvtt,
                                        type: "post"
                                    });
                                    $.ajax({
                                        url: "lay_id_nhankhau?mabenhnhan=" + rowData.MA_BENH_NHAN
                                    }).done(function (dt) {
                                        P_ID_NHAN_KHAU = parseInt(dt);
                                        $.post("ls_hssk_insert",
                                            {
                                                mabenhnhan: rowData.MA_BENH_NHAN,
                                                idnhankhau: P_ID_NHAN_KHAU,
                                                sovaovien: rowData.SOVAOVIEN,
                                                sovaovien_dt: rowData.SOVAOVIEN_DT,
                                                tt_hssk: 3
                                            }).done(function (trang_thai) {
                                        });

                                        $.post("hssk_get_pid",
                                            {
                                                mabenhnhan: rowData.MA_BENH_NHAN,
                                                idnhankhau: P_ID_NHAN_KHAU,
                                                sovaovien: rowData.SOVAOVIEN,
                                                sovaovien_dt: rowData.SOVAOVIEN_DT,
                                                tt_hssk: 3
                                            }).done(function (trang_thai) {

                                        })

                                    });

                                    previewAndSignPdfNguoiBenhDefaultModal({
                                        url: objReturn.url,
                                        idButtonVanTay: 'bangke_kysovantay_action',
                                    }, function(){
                                        timNguoiBenhKyTen({
                                            dvtt: singletonObject.dvtt,
                                            maBenhNhan: rowData.MA_BENH_NHAN,
                                        });
                                        $("#bangke_kysovantay_action").click(function() {
                                            var isNguoiBenh = $("#nguoiBenhKyTenSelect").val();
                                            var tenNguoiBenhKyTen = isNguoiBenh == '0' ? rowData.TEN_BENH_NHAN : $("#nguoiBenhKyTenInput").val();
                                            if (isNguoiBenh == '1' && $("#nguoiBenhKyTenInput").val().trim() == "") {
                                                notifiToClient("Red", "Vui lòng nhập tên người ký!");
                                                return;
                                            }
                                            var urlFinger = [singletonObject.dvtt, rowData.SOVAOVIEN, tenNguoiBenhKyTen, "CMU_BANGKE_XACNHANNB_INSERT"]
                                            $.post("cmu_post", {
                                                url: urlFinger.join('```')
                                            }).done(function (data) {

                                            }).fail(function(error) {
                                                notifiToClient("Red",MESSAGEAJAX.ERROR);
                                            })
                                            getImageBase64FingerCMU()
                                                .then(function(imageDataAdd) {
                                                    doSignSmartca769Finger({
                                                        url: objReturn.url,
                                                        imageSource: imageDataAdd,
                                                        fileName: "Bảng kê " + " - " + rowData.TEN_BENH_NHAN,
                                                        dvtt: singletonObject.dvtt,
                                                        soVaoVien: rowData.SOVAOVIEN,
                                                        soVaoVienDT: rowData.SOVAOVIEN_DT,
                                                        sttDotDieuTri: rowData.STT_DOTDIEUTRI,
                                                        maBenhNhan: rowData.MA_BENH_NHAN,
                                                        soBenhAn: rowData.SOBENHAN,
                                                        kyHieuPhieu: "PHIEU_NOITRU_BANGKE_BENHNHAN",
                                                        soPhieuDichVu: rowData.SOBENHAN,
                                                        nghiepVu: "",
                                                        noiTru: 1,
                                                        userId: singletonObject.userId,
                                                        trangThai: 2,
                                                        keyword: "Tôi đã nhận",
                                                        hoTenBenhNhan: rowData.TEN_BENH_NHAN,
                                                        llx: 140,
                                                        urx: 20,
                                                        lly: -100,
                                                        ury: -20,
                                                    }, function() {
                                                        // Ký lỗi thì để vô đây
                                                    }, function(data) {
                                                        var dataNT = {
                                                            dvtt: singletonObject.dvtt,
                                                            maBenhNhan: rowData.MA_BENH_NHAN,
                                                        }
                                                        luuNguoiBenhKyTen(dataNT);
                                                        $("#modalPreviewAndSignPDFNguoiBenh").modal("hide");
                                                        getDanhSachBenhNhan();
                                                        previewPdfDefaultModal(data, "preview_xembangke");
                                                    });
                                                })
                                                .catch(function(error) {
                                                    console.error(error);
                                                });
                                        });
                                    });
                                } else {
                                    notifiToClient("Red", "Bảng kê - Đợt " + itemDDT.STT_DOTDIEUTRI + ": " + objReturn.message);
                                }
                            }).catch(error => {
                                notifiToClient("Red", error.message || "Lỗi không xác định");
                            });
                        }

                        if (key == "giaychuyentuyen") {
                            if(rowData.GRV != 5) {
                                return notifiToClient("Red","Giấy chuyển tuyến chưa được gửi ký số hoặc chưa được ký")
                            }
                            var params = {
                                dvtt: singletonObject.dvtt,
                                SOVAOVIEN: rowData.SOVAOVIEN,
                                SOVAOVIEN_DT: rowData.SOVAOVIEN_DT,
                                STT_BENHAN: rowData.STT_BENHAN,
                                STT_DOTDIEUTRI: rowData.STT_DOTDIEUTRI,
                                SOBENHAN: rowData.SOBENHAN,
                                SOPHIEUTHANHTOAN: rowData.SOPHIEUTHANHTOAN,
                                MABENHNHAN: rowData.MA_BENH_NHAN
                            }

                            getUrlGiayChuyenTuyen(params).then(objReturn => {
                                if (objReturn.isError == 0) {
                                    previewPdfDefaultModal( objReturn.url, "preview_tthc_ct_xemgiaychuyentuyen")
                                } else {
                                    notifiToClient("Red", "Chưa ký số giấy chuyển tuyến");
                                }
                            }).catch(error => {
                                notifiToClient("Red", error.message || "Lỗi không xác định");
                            });
                        }
                        if (key == "giayravien") {
                            if(rowData.GRV != 3) {
                                return notifiToClient("Red","Giấy ra viện chưa được gửi ký số hoặc chưa được ký")
                            }
                            var params = {
                                dvtt: singletonObject.dvtt,
                                SOVAOVIEN: rowData.SOVAOVIEN,
                                SOVAOVIEN_DT: rowData.SOVAOVIEN_DT,
                                STT_BENHAN: rowData.STT_BENHAN,
                                STT_DOTDIEUTRI: rowData.STT_DOTDIEUTRI,
                                SOBENHAN: rowData.SOBENHAN,
                                SOPHIEUTHANHTOAN: rowData.SOPHIEUTHANHTOAN,
                                MABENHNHAN: rowData.MA_BENH_NHAN
                            }

                            getUrlGiayRaVien(params).then(objReturn => {
                                if (objReturn.isError == 0) {
                                    previewPdfDefaultModal( objReturn.url, "preview_tthc_ct_xemgiayravien")
                                } else {
                                    notifiToClient("Red", "Chưa ký số giấy ra viện");
                                }
                            }).catch(error => {
                                notifiToClient("Red", error.message || "Lỗi không xác định");
                            });
                        }
                        if(key == 'huygiayravien') {
                            confirmToClient(MESSAGEAJAX.CONFIRM, function () {
                                huykysoFilesign769("NOITRU_GIAYRAVIEN",
                                    rowData.SOVAOVIEN,
                                    -1, singletonObject.dvtt,
                                    rowData.SOVAOVIEN,
                                    rowData.SOVAOVIEN_DT, -1, function(data) {
                                        if(_.get(data, 'SUCCESS') == 1) {
                                            luuLogHSBATheoBN({
                                                SOVAOVIEN: rowData.SOVAOVIEN,
                                                LOAI: LOGHSBALOAI.XUATVIEN.KEY,
                                                NOIDUNGBANDAU: "Hủy ký số",
                                                NOIDUNGMOI: "",
                                                USERID: singletonObject.userId,
                                                ACTION: LOGHSBAACTION.EDIT.KEY,
                                            })
                                            $.post("cmu_post_cmu_grvct_del", {
                                                url: [
                                                    singletonObject.dvtt,
                                                    rowData.SOVAOVIEN,
                                                    'GRV'
                                                ].join("```")
                                            })
                                            getFilesign769("NOITRU_GIAYRAVIEN_BGD",
                                                rowData.SOVAOVIEN, -1,
                                                singletonObject.dvtt,
                                                rowData.SOVAOVIEN,
                                                rowData.SOVAOVIEN_DT, -1, function(dataGD) {
                                                    if(dataGD.length > 0) {
                                                        $.post("smartca-capnhat-huykyso?keysign="+dataGD[0].KEYSIGN).done(function() {

                                                        })
                                                        $("#tim_kiem").click()
                                                    }
                                                })
                                        }
                                    })

                            })
                        }
                        if (key == "giaychungtu") {
                            var params = {
                                dvtt: singletonObject.dvtt,
                                SOVAOVIEN: rowData.SOVAOVIEN,
                                SOVAOVIEN_DT: rowData.SOVAOVIEN_DT,
                                STT_BENHAN: rowData.STT_BENHAN,
                                STT_DOTDIEUTRI: rowData.STT_DOTDIEUTRI,
                                SOBENHAN: rowData.SOBENHAN,
                                SOPHIEUTHANHTOAN: rowData.SOPHIEUTHANHTOAN,
                                MABENHNHAN: rowData.MA_BENH_NHAN
                            }
                            getUrlGiayBaoTu(params).then(objReturn => {
                                if (objReturn.isError == 0) {
                                    previewPdfDefaultModal(objReturn.url, "tthc_tv_giaybaotu_preview")
                                } else {
                                    notifiToClient("Red", "Lỗi lấy url giấy báo tử");
                                }
                            }).catch(error => {
                                notifiToClient("Red", error.message || "Lỗi không xác định");
                            });


                        }
                    },
                    items: items
                })
            }
        },
        footerrow: true,
        gridComplete: function () {
            var grid = $("#list_benhnhan");
            var sl = grid.getGridParam("records");
            sl = sl + " bệnh nhân";
            grid.jqGrid("footerData", "set", {TEN_BENH_NHAN: sl});
            var dataGrid = getAllRowDataJqgrid("list_benhnhan")

        },
        ondblClickRow: function (id) {
            var rowData = getThongtinBnSelected("list_benhnhan");
            findBenhNhanBySoBenhAn('-1', rowData.SOBENHAN, rowData.BANT != 1 ? 0: rowData.BANT)
        }
    });
    $("#list_benhnhan").bind("touchstart", function (e) {
        var $this = $(this), now = new Date().getTime(),
            lastTouchTime = $this.data("lastTouchTime") || now + 1,
            timeInterval = now - lastTouchTime;
        if (timeInterval < 500 && timeInterval > 0) {
            var $tr = $(e.target).closest("tr.jqgrow");
            if ($tr.length > 0) {
                var rowData = getThongtinBnSelected("list_benhnhan");
                findBenhNhanBySoBenhAn('-1', rowData.SOBENHAN)
            } else {

            }
        }
        $this.data("lastTouchTime", now);
    });
    $("#list_benhnhan").jqGrid('filterToolbar', {stringResult: true, searchOnEnter: false, defaultSearch: "cn"});

    $('#list_dotdieutri').jqGrid({
        datatype: "local",
        loadonce: true,
        styleUI: 'Bootstrap',
        iconSet: 'Octicons',
        width: null,
        shrinkToFit: false,
        height: 400,
        colModel: [
            {
                name: 'CHON', label: 'Chọn', width: 100, align: "center",
                formatter: function (cellvalue, options, rowobject) {
                    var btn = ' ';
                    if (rowobject.SOBENHAN != null){
                        btn = String.format('<button type="button" sobenhan="{0}" class="btn btn-success chon-benh-nhan"><i class="fa fa-check" > Chọn</i></button>', rowobject.SOBENHAN);
                    }
                    return btn;
                }
            },
            {name: 'STT_DOTDIEUTRI', label: 'Đợt', width: 50, align: 'center'},
            {name: 'SOBENHAN', label: 'Số bệnh án', width: 100, align: 'center'},
            {name: 'MABENHNHAN', label: 'Mã BN', width: 80, align: 'center'},
            {name: 'TEN_BENH_NHAN', label: 'Họ tên', width: 150},
            {name: 'NAMSINH', label: 'Năm sinh', width: 50, align: 'center', hidden: true},
            {name: 'NGAY_SINH', label: 'Ngày sinh', width: 70, align: 'center', hidden: true},
            {name: 'DIA_CHI', label: 'Địa chỉ', width: 100, align: 'left', hidden: true},
            {name: 'CHANDOANBENH', label: 'Chẩn đoán', width: 350, align: 'left'},
            {name: 'GIOI_TINH', label: 'Giới tính', width: 70, align: 'center'},
            {name: 'NGAY_NHAP_VIEN', label: 'Ngày nhập viện', width: 150},
            {name: 'TEN_PHONGBAN', label: 'Khoa điều trị', width: 180},
            {name: 'TRANG_THAI', label: 'Trạng thái', width: 200}
        ],
        loadComplete: function () {
        },
        ondblClickRow: function (id, selected) {
            var ret = $("#list_dotdieutri").jqGrid('getRowData', id);
            if(ret.SOBENHAN != ""){
                findBenhNhanBySoBenhAn(ret.STT_DOTDIEUTRI, ret.SOBENHAN);
                $('#modalDanhSachDotDieuTri').modal('hide');
            }
        },
        caption: 'Danh sách đợt điều trị',
        singleSelectClickMode: 'selectonly',
        ignoreCase: true,
        pager: true,
        iconSet: "fontAwesome",
        autoencode: true,
        viewrecords: true,
        rowNum: 20,
        altRows: true,
        altclass: "myAltRowClass",
        rowList: [20, 40, 100, "10000:All"],
        navOptions: {
            del: true,
            add: false,
            edit: false
        },
        searching: {
            closeAfterSearch: true,
            closeAfterReset: true,
            closeOnEscape: true,
            searchOnEnter: true,
            multipleSearch: true,
            multipleGroup: true,
            showQuery: true
        }
    }).jqGrid("navGrid");
    $('#list_dotdieutri').jqGrid('filterToolbar', {stringResult: true, searchOnEnter: false, defaultSearch: "cn"});

    $("#tim_kiem").click(function () {
        getDanhSachBenhNhan();
    });

    $("#hsba_log_lammoi").click(function() {
        var rowData = getThongtinRowSelected("list_benhnhan");
        loadGridLichSuBenhAn(rowData.SOVAOVIEN)
    })
    hideSelfLoadingByClass("btn-loading");
    // FUNCTION
    $.get("cmu_list_STG_TAM_select_phong_ban?url="+convertArray([singletonObject.dvtt])).done(function(data) {
        data.forEach(function(obj){
            if(obj.MAKHOA != 0) {
                $("#khoa_phong").append("<option value='"+obj.MAKHOA+"'>"+obj.TENKHOA+"</option>")
            }
        })
        $("#khoa_phong").select2();
    })
    function getICDHT(listSovaovien) {
        var grid = $("#list_benhnhan");
        var ids = grid.jqGrid('getDataIDs');
        $.get("cmu_list_CMU_DSLAYICD_MOINHAT?url=" +
            convertArray([singletonObject.dvtt, listSovaovien.join(";")])).done(function (data) {
            for (var i = 0; i < ids.length; i++) {
                var rowId = ids[i];
                var rowData = grid.jqGrid('getRowData', rowId);

                data.forEach(function (obj) {
                    if (rowData.SOVAOVIEN == obj.SOVAOVIEN && rowData.SOVAOVIEN_DT == obj.SOVAOVIEN_DT) {

                        grid.jqGrid("setCell", rowId, "ICD_HT", obj.TENBENH);
                        grid.jqGrid("setCell", rowId, "NGAYGIO_NHAPVIEN", obj.NGAYGIO_NHAPVIEN);
                        grid.jqGrid("setCell", rowId, "SOGIUONG", obj.SOGIUONG);
                        var attrColor = {
                            background: 'transparent',
                            color: 'black'
                        }
                        if (obj.CANDOICHIPHI < 0) {
                            attrColor.background = 'red';
                            attrColor.color = 'white';
                            attrColor['font-weight'] = '600';
                        }
                        grid.jqGrid("setCell", rowId, "CANDOICHIPHI", obj.CANDOICHIPHI, attrColor);
                        grid.jqGrid("setCell", rowId, "TONGCHIPHI", obj.TONGCHIPHI);
                    }
                })

            }

        })
    }

    function getDanhSachBenhNhan() {
        var maBenhNhan = $("#ma_benh_nhan").val()
        var soBenhAn = $("#so_benh_an").val()
        var ngay = $("#ngay_ra_vien").val()
        var denngay = $("#denngay_ra_vien").val()
        var khoa = $("#khoa_phong").val()
        var params = [singletonObject.dvtt, maBenhNhan || null, soBenhAn || null, ngay || null, denngay||null, khoa || -1];
        var url = "cmu_list_CMU_GET_DS_BNDATAO_BADT?url=" + convertArray(params);
        $("#list_benhnhan").jqGrid('setGridParam', {
            url: url,
            datatype: 'json'
        }).trigger('reloadGrid');
        resetDataBN();
    }

    function resetDataBN() {
        thongtinhsba = {
            todieutri: [],
            thuoc: [],
            vattuyte: [],
            phieuchamsoc: [],
            phieuchucnangsong: [],
            xetnghiem: [],
            cdha: [],
            ttpt: [],
            thongtinbenhnhan: {}
        }
    }

    function getThongtinBnSelected() {
        var id = $("#list_benhnhan").jqGrid("getGridParam", "selrow");
        var rowData = $("#list_benhnhan").jqGrid("getRowData", id);
        return rowData;
    }

    function getThongtinVoBA() {
        var resThongtinba = $.ajax({
            url: "cmu_list_CMU_HSBA_GETBENHAN?url=" + convertArray([singletonObject.dvtt, thongtinhsba.thongtinbn.SOVAOVIEN]),
            type: "GET",
            async: false,
        }).responseText;
        resThongtinba = JSON.parse(resThongtinba);
        thongtinhsba.thongtinbn["VOBENHAN"] = resThongtinba;
        thongtinhsba.thongtinbn.VOBENHAN.forEach(function (obj) {
            var resThongTinHanhChinh = getThongTinHanhChinhVoBA(obj.ID)
            obj["HANHCHINH"] = JSON.parse(resThongTinHanhChinh)[0];
        });
        loadThongTinVBATrang1();
        return resThongtinba;
    }

    function getThongTinHanhChinhVoBA(id) {
        var {responseText: resThongTinHanhChinh} = $.ajax({
            url: "cmu_list_CMU_GET_VOBENHAN_HANHCHINH?url=" + convertArray([singletonObject.dvtt, id]),
            type: "GET",
            async: false,
        });
        return resThongTinHanhChinh
    }

    function findBenhNhanBySoBenhAn(stt_dotdieutri, soBenhAn, bant) {
        if (soBenhAn) {
            showLoaderIntoWrapId("wrap_modalBATong");
            $("#modalXemBenhAnTong").modal("show");
            $.ajax({
                url: "findBenhNhanBySoBenhAn?soBenhAn=" + soBenhAn +"&bant="+bant+"&stt_dotdieutri="+stt_dotdieutri,
                type: "GET",
                success: function (data) {
                    if(parseInt(data.KETQUA) == 0){
                        notifiToClient("Red", "Không tìm thấy bệnh nhân");
                        return "";
                    }
                    if(parseInt(data.KETQUA) > 1){
                        var params = {
                            mabenhnhan: '0',
                            sobenhan: soBenhAn,
                            tenbenhnhan: '0',
                            bant: '0',
                            ngaySinh: '',
                            diaChi: '',
                        };
                        $.ajax({
                            url: 'searchHoSoBenhAn?' + $.param(params),
                            type: 'GET',
                            success: function (dataSearch) {
                                var dsDotDieuTri = [];
                                dataSearch.forEach(function (item) {
                                    $.ajax({
                                        url: "findBenhNhanBySoBenhAn?soBenhAn=" + item.SOBENHAN + "&bant="+bant+"&stt_dotdieutri=" + item.STT_DOTDIEUTRI,
                                        type: "GET",
                                        async: false,
                                        success: function (dataBN) {
                                            dsDotDieuTri.push(dataBN);
                                        }
                                    });
                                });
                                thongtinhsba.thongtinbn = dsDotDieuTri[dsDotDieuTri.length - 1];
                                thongtinhsba.thongtinbn.dsDotDieuTri = dsDotDieuTri;
                                var resThongtinba = getThongtinVoBA();
                                getMenuBenhAnTong(dsDotDieuTri,
                                    function(){
                                        $("#hsba-tthc-tong-tab").click();
                                        addTextTitleModal("titleXemBenhAnTong");
                                        hideLoaderIntoWrapId("wrap_modalBATong");
                                    },
                                    function(error){
                                        notifiToClient("Red", "Lỗi load danh sách menu bệnh án tổng hợp!");
                                    });
                            }
                        });
                        return "";
                    }
                    $("#hsba_tabs").tabs("enable", 1);
                    $("#hsba_tabs").tabs("option", "active", 1);
                    thongtinhsba.thongtinbn = data;
                    thongtinhsba.thongtinbn['MA_BENH_NHAN'] = data.MABENHNHAN;
                    singletonObject.bant = bant;
                    $.ajax({
                        url: "lay_id_nhankhau?mabenhnhan=" + data.MABENHNHAN,
                        async: false
                    }).done(function (dt) {
                        thongtinhsba.thongtinbn['IDNHANKHAU'] = dt;
                    })
                    var dsDotDieuTri = [data];
                    thongtinhsba.thongtinbn.dsDotDieuTri = dsDotDieuTri;
                    var resThongtinba = getThongtinVoBA();
                    getMenuBenhAnTong(dsDotDieuTri,
                        function(){
                            $("#hsba-tthc-tong-tab").click();
                            addTextTitleModal("titleXemBenhAnTong");
                            hideLoaderIntoWrapId("wrap_modalBATong");
                        },
                        function(error){
                            notifiToClient("Red", "Lỗi load danh sách menu bệnh án tổng hợp!");
                        });
                },
                error: function (err) {
                    notifiToClient("Red", "Không tìm thấy bệnh nhân");
                }
            })
        }
    }

    function initGridLichSuBenhAn() {
        var list = $("#hsba_log_table");
        if(!list[0].grid) {
            list.jqGrid({
                url: '',
                datatype: "local",
                loadonce: true,
                height: 500,
                rowNum: 1000000,
                ignoreCase: true,
                width: null,
                shrinkToFit: false,
                colModel: [
                    { label: "Ngày giờ", name: "NGAY_TAO", width: 200,
                        cellattr: function(rowId, val, rawObject, cm, rdata) {
                            return 'style="white-space: normal;"';
                        }
                    },
                    { label: "Loại", name: "LOAI", width: 200 ,
                        cellattr: function(rowId, val, rawObject, cm, rdata) {
                            return 'style="white-space: normal;"';
                        },
                        formatter: function(cellvalue, options, rowObject) {
                            return LOGHSBALOAI[cellvalue]? LOGHSBALOAI[cellvalue].VALUE: cellvalue
                        }
                    },
                    { label: "Hành động", name: "ACTION", width: 100,
                        formatter: function(cellvalue, options, rowObject) {
                            return LOGHSBAACTION[cellvalue]? LOGHSBAACTION[cellvalue].VALUE:cellvalue
                        }
                    },
                    { label: "Nội dung cũ", name: "NOIDUNGBANDAU", width: 400 ,
                        cellattr: function(rowId, val, rawObject, cm, rdata) {
                            return 'style="white-space: normal;"';
                        }
                    },
                    { label: "Nội dung mới", name: "NOIDUNGMOI", width: 400,
                        cellattr: function(rowId, val, rawObject, cm, rdata) {
                            return 'style="white-space: normal;"';
                        }
                    },
                    { label: "Người thực hiện", name: "TENNHANVIEN", width: 200 },

                ],
                caption: "Lịch sử bệnh án",

            })
            list.jqGrid('filterToolbar', {stringResult: true, searchOnEnter: false, defaultSearch: "cn"});
        }

    }
    function loadGridLichSuBenhAn(sovaovien) {
        var arr = [
            singletonObject.dvtt,
            sovaovien,
            $("#hsba_log_tungay").val(),
            $("#hsba_log_denngay").val()
        ];
        var url = "cmu_list_CMU_HSBA_LOG_GET?url=" + convertArray(arr);
        var list = $("#hsba_log_table");
        loadDataGridGroupBy(list, url, null, function(data) {
            return data.map(function(item) {
                return {
                    ...item,
                    LOAI: LOGHSBALOAI[item.LOAI]? LOGHSBALOAI[item.LOAI].VALUE: item.LOAI,
                    ACTION: LOGHSBAACTION[item.ACTION]? LOGHSBAACTION[item.ACTION].VALUE: item.ACTION,
                }
            })
        });
    }

    function initData() {
        if($("#showModalPhieuChuanDoanNguyenNhanTuVong").length > 0)  {
            document.getElementById('showModalPhieuChuanDoanNguyenNhanTuVong').style.display = 'none';
        }
        $.get("cmu_list_STG_TAM_select_phong_ban?url="+convertArray([singletonObject.dvtt])).done(function(data) {
            singletonObject.danhsachphongban = data;
            singletonObject.danhsachphongbanFormio = data.map(function(object) {
                return {
                    label: object.TENKHOA,
                    value: object.MAKHOA,
                }
            });
        })
        $.get("cmu_list_CMU_DSNHANVIENTOANBV?url="+convertArray([singletonObject.dvtt])).done(function(data){
            singletonObject.danhsachnhanvien = data;
            singletonObject.danhsachnhanvienFormio = data.map(function(object) {
                return {
                    label: object.TEN_NHANVIEN + "(" + object.TEN_PHONGBAN + ")",
                    value: object.MA_NHANVIEN,
                }
            });
        });
        $.get("cmu_list_CMU_DSNHANVIENTOANBV_V2?url="+convertArray([singletonObject.dvtt])).done(function(data){
            singletonObject.danhsachtatcanhanvien = data.map(function(object) {
                return {
                    label: object.TEN_NHANVIEN + "(" + object.TEN_PHONGBAN + ")",
                    value: object.MA_NHANVIEN.toString(),
                    tennhanvien: object.TEN_NHANVIEN,
                }
            });
        });
        $.get("cmu_list_LAYDSNHANVIEN_THUTRUONGDONVI?url="+convertArray([singletonObject.dvtt])).done(function(data) {
            singletonObject.danhsachthutruong = data;
            $("#tthc_xv_giamdoc").html("<option value='-1'></option>")
            data.forEach(function(obj){
                $("#tthc_xv_giamdoc").append("<option value='"+obj.MA_BAC_SI+"'>"+ obj.CHUCDANH_HOTEN +"</option>")
            })
            $("#tthc_xv_giamdoc").select2({ width: '100%',
                dropdownParent: $("#tthc_xv_giamdoc").parent()
            });
        })
    }
    initData()
});