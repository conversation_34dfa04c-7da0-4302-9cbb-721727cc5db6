<%@page contentType="text/html" pageEncoding="UTF-8" %>
<%@taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<div class="modal fade" id="modalFormBienBanKiemThaoTV" role="dialog"
     aria-labelledby="modalFormBienBanKiemThaoTV" aria-hidden="true"
     data-keyboard="false" data-backdrop="static">
    <div class="modal-dialog modal-dialog-centered" style="max-width: 92%;width:92%;" role="document">
        <div class="modal-content">
            <div class="modal-header modal-header-sticky py-2 d-flex justify-content-center align-items-center">
                <h6 class="modal-title text-primary font-weight-bold" id="titleFormBienBanKiemThaoTV"></h6>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <div id="wrapFormBienBanKiemThaoTV">
                    <div class="row ml-0 mr-0 mt-2">
                        <div class="col-md-12" id="formNhapBienBanKiemThaoTV"></div>
                    </div>
                </div>
            </div>
            <div class="modal-footer modal-footer-sticky py-2">
                <div class="col-md-12 text-right" id="bienbankiemthaotv-btn-wrap">
<%--                    <button class="btn btn-success btn-loading btn-luu ml-2" type="button" id="camkettuchoisddv_mau">--%>
<%--                        <i class="fa fa fa-clone"></i>--%>
<%--                        Mẫu phiếu--%>
<%--                    </button>--%>
                    <div class="col-md-6 text-left">
                        <div class="edit">Người thực hiện ký số (chọn khi thực hiện ký số)</div>
                    </div>
                    <div class="col-md-6 text-left">
                        <select class="form-control form-control-sm edit" id="bienbankiemthaotv_kysosl"
                                name="bienbankiemthaotv_kysosl" style="width: 100%" required>
                            <option value="" disabled selected>Chọn người thực hiện</option>
                            <option value="KYSOTHUKY">Thư ký</option>
                            <option value="KYSOCHUTRI">Chủ trì</option>
                        </select>
                    </div>
                    <button class="btn btn-warning btn-loading form-control-sm line-height-1 edit" type="button" id="bienbankiemthaotv_huykyso">
                        <span class="spinner-border spinner-border-sm spinner" role="status" aria-hidden="true"></span>
                        <i class="fa fa-window-close"></i>
                        Hủy ký số
                    </button>
                    <button class="btn btn-success btn-loading form-control-sm line-height-1 edit" type="button" id="bienbankiemthaotv_kyso">
                        <span class="spinner-border spinner-border-sm spinner" role="status" aria-hidden="true"></span>
                        <i class="fa fa-key"></i>
                        Ký số
                    </button>
                    <button class="btn btn-success form-control-sm line-height-1 btn-loading" type="button" data-action="THEM" id="bienbankiemthaotv_luu">
                        <span class="spinner-border spinner-border-sm spinner" role="status" aria-hidden="true"></span>
                        <i class="fa fa-floppy-o"></i> Lưu
                    </button>
                    <button class="btn btn-info btn-loading form-control-sm line-height-1 edit" type="button" id="bienbankiemthaotv_xoa">
                                            <span class="spinner-border spinner-border-sm spinner" role="status"
                                                  aria-hidden="true"></span>
                        <i class="fa fa-trash-o" aria-hidden="true"></i>
                        Xóa
                    </button>
                    <button class="btn btn-info btn-loading form-control-sm line-height-1 edit" type="button"
                            id="bienbankiemthaotv_in">
                                            <span class="spinner-border spinner-border-sm spinner" role="status"
                                                  aria-hidden="true"></span>
                        In
                    </button>
                    <button class="btn btn-default ml-2 form-control-sm line-height-1" type="button"
                            data-dismiss="modal">
                        Đóng
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>