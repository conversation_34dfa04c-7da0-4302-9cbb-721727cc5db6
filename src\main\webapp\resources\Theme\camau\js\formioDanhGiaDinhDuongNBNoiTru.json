{"display": "form", "components": [{"label": "MA_PHIEU", "key": "MA_PHIEU", "type": "hidden", "input": true}, {"label": "Columns", "columns": [{"components": [{"label": "<PERSON><PERSON><PERSON> (Điều dưỡng)", "customClass": "mr-2 formio-css-datetime formio-css-suffix luulog", "key": "NGAY_KY_DD", "type": "datetime", "format": "dd/MM/yyyy HH:mm", "timePicker": {"showMeridian": false}, "widget": {"enableTime": true, "format": "dd/MM/yyyy HH:mm", "time_24hr": true}}], "width": 2, "size": "md", "currentWidth": 2}, {"components": [{"label": "<PERSON><PERSON><PERSON><PERSON> dưỡng", "widget": "<PERSON><PERSON><PERSON>", "customClass": "mr-2 luulog", "tableView": true, "dataSrc": "json", "template": "<span>{{ item.tennhanvien }}</span>", "validateWhenHidden": false, "key": "DIEU_DUONG", "type": "select", "input": true}], "width": 4, "size": "md", "currentWidth": 4}, {"components": [{"label": "<PERSON><PERSON><PERSON> (<PERSON><PERSON><PERSON>)", "customClass": "mr-2 formio-css-datetime formio-css-suffix luulog", "key": "NGAY_KY_BS", "type": "datetime", "format": "dd/MM/yyyy HH:mm", "timePicker": {"showMeridian": false}, "widget": {"enableTime": true, "format": "dd/MM/yyyy HH:mm", "time_24hr": true}}], "width": 2, "size": "md", "currentWidth": 2}, {"components": [{"label": "<PERSON><PERSON><PERSON>", "widget": "<PERSON><PERSON><PERSON>", "customClass": "lu<PERSON><PERSON>", "tableView": true, "dataSrc": "json", "template": "<span>{{ item.tennhanvien }}</span>", "validateWhenHidden": false, "key": "BAC_SI_DANH_GIA", "type": "select", "input": true}], "width": 4, "size": "md", "currentWidth": 4}], "key": "columns", "type": "columns", "customClass": "ml-0 mr-0"}, {"label": "Columns", "columns": [{"components": [{"label": "ICD", "customClass": "mr-2 luulog", "tableView": true, "validateWhenHidden": false, "key": "ICD", "type": "textfield", "input": true}], "width": 2, "size": "md", "currentWidth": 2}, {"components": [{"label": "<PERSON><PERSON><PERSON> đ<PERSON>", "customClass": "lu<PERSON><PERSON>", "tableView": true, "validateWhenHidden": false, "key": "CHAN_DOAN", "type": "textfield", "input": true}], "width": 10, "size": "md", "currentWidth": 10}], "key": "columns", "type": "columns", "customClass": "ml-0 mr-0"}, {"label": "Columns", "columns": [{"components": [{"label": "Cân nặng", "customClass": "mr-2 formio-css-suffix luulog", "suffix": "kg", "tableView": true, "validateWhenHidden": false, "key": "CANNANG", "type": "textfield", "input": true}], "width": 2, "size": "md", "currentWidth": 2}, {"components": [{"label": "<PERSON><PERSON><PERSON> cao", "customClass": "mr-2 formio-css-suffix luulog", "suffix": "m", "tableView": true, "validateWhenHidden": false, "key": "CHIEUCAO", "type": "textfield", "input": true}], "width": 2, "size": "md", "currentWidth": 2}, {"components": [{"label": "BMI", "customClass": "mr-2 luulog", "tableView": true, "validateWhenHidden": false, "key": "BMI", "type": "textfield", "input": true}], "width": 2, "size": "md", "currentWidth": 2}, {"components": [{"label": "<PERSON><PERSON><PERSON> cân trong 3 tháng gần đây", "widget": "<PERSON><PERSON><PERSON>", "customClass": "mr-2 formio-css-selection luulog", "tableView": true, "data": {"values": [{"label": "K<PERSON>ô<PERSON>", "value": "1"}, {"label": "<PERSON><PERSON>", "value": "2"}, {"label": "<PERSON><PERSON> nh<PERSON>ng không biết", "value": "3"}]}, "validateWhenHidden": false, "key": "SUTCAN_3THGANDAY", "type": "select", "input": true}], "width": 3, "size": "md", "currentWidth": 3}, {"components": [{"label": "Số kg sụt", "customClass": "mr-2 formio-css-suffix luulog", "suffix": "kg", "tableView": true, "validateWhenHidden": false, "key": "SOKGSUT", "type": "number", "input": true}], "width": 1, "size": "md", "currentWidth": 1}, {"components": [{"label": "Tỷ lệ % mất cân", "customClass": "formio-css-suffix luulog", "suffix": "%", "tableView": true, "validateWhenHidden": false, "key": "TYLEMATCAN", "type": "number", "input": true}], "width": 2, "size": "md", "currentWidth": 2}], "key": "columns", "type": "columns", "customClass": "ml-0 mr-0"}, {"label": "Titles", "customClass": "formio-css-p", "type": "htmlelement", "tag": "p", "content": "<b>Phần I: <PERSON><PERSON><PERSON> lọc nguy cơ suy dinh dưỡng (SDD)</b> (Phần dành cho Đ<PERSON>u dưỡng)", "input": false}, {"label": "Columns", "columns": [{"components": [{"label": "Titles", "customClass": "formio-css-p", "type": "htmlelement", "tag": "p", "content": "<b><PERSON><PERSON> g<PERSON><PERSON> tình trạng DD</b>", "input": false}], "width": 4, "size": "md", "currentWidth": 4}, {"components": [{"label": "Titles", "customClass": "formio-css-p", "type": "htmlelement", "tag": "p", "content": "<b><PERSON><PERSON><PERSON></b>", "input": false}], "width": 2, "size": "md", "currentWidth": 2}, {"components": [{"label": "Titles", "customClass": "formio-css-p", "type": "htmlelement", "tag": "p", "content": "<b>Ảnh hưởng của bệnh lý</b>", "input": false}], "width": 4, "size": "md", "currentWidth": 4}, {"components": [{"label": "Titles", "customClass": "formio-css-p", "type": "htmlelement", "tag": "p", "content": "<b><PERSON><PERSON><PERSON></b>", "input": false}], "width": 2, "size": "md", "currentWidth": 2}], "key": "columns", "type": "columns", "customClass": "ml-0 mr-0"}, {"label": "Columns", "columns": [{"components": [{"label": "Titles", "customClass": "mr-2", "type": "htmlelement", "tag": "p", "content": "- <PERSON><PERSON><PERSON> ≥ 5% CN/ 3 tháng hoặc<br/>- Ăn gi<PERSON>m còn 50-75%/1 tuần trước đây", "input": false}], "width": 4, "size": "md", "currentWidth": 4}, {"components": [{"label": "Nhẹ (1 điểm)", "customClass": "mr-2 luulog", "tableView": false, "validateWhenHidden": false, "key": "TINHDIEM1_1", "type": "checkbox", "input": true}], "width": 2, "size": "md", "currentWidth": 2}, {"components": [{"label": "Titles", "customClass": "mr-2", "type": "htmlelement", "tag": "p", "content": "- <PERSON><PERSON><PERSON> x<PERSON><PERSON> đ<PERSON> (<PERSON><PERSON><PERSON><PERSON> lớn), bệnh lý mạn hoặc đợt cấp nhẹ/bệnh lý mạn tính…", "input": false}], "width": 4, "size": "md", "currentWidth": 4}, {"components": [{"label": "Nhẹ (1 điểm).", "customClass": "lu<PERSON><PERSON>", "tableView": false, "validateWhenHidden": false, "key": "TINHDIEM2_1", "type": "checkbox", "input": true}], "width": 2, "size": "md", "currentWidth": 2}], "key": "columns", "type": "columns", "customClass": "ml-0 mr-0"}, {"label": "Columns", "columns": [{"components": [{"label": "Titles", "customClass": "mr-2", "type": "htmlelement", "tag": "p", "content": "- <PERSON><PERSON><PERSON> ≥ 5% CN/ 2 tháng hoặc<br/>- Ăn gi<PERSON>m còn 25-50%/1 tuần hoặc<br/>- BMI từ 18,5-20,5", "input": false}], "width": 4, "size": "md", "currentWidth": 4}, {"components": [{"label": "<PERSON><PERSON> bình (2 điểm)", "customClass": "mr-2 dgddchung-2 luulog", "tableView": false, "validateWhenHidden": false, "key": "TINHDIEM3_2", "type": "checkbox", "input": true}], "width": 2, "size": "md", "currentWidth": 2}, {"components": [{"label": "Titles", "customClass": "mr-2", "type": "htmlelement", "tag": "p", "content": "- <PERSON><PERSON><PERSON> quỵ, nh<PERSON><PERSON> máu c<PERSON> tim, phẫu thuật lớn, vi<PERSON><PERSON> phổi, ung thư máu…", "input": false}], "width": 4, "size": "md", "currentWidth": 4}, {"components": [{"label": "<PERSON><PERSON> bình (2 điểm).", "customClass": "dgddchung-2 luulog", "tableView": false, "validateWhenHidden": false, "key": "TINHDIEM4_2", "type": "checkbox", "input": true}], "width": 2, "size": "md", "currentWidth": 2}], "key": "columns", "type": "columns", "customClass": "ml-0 mr-0"}, {"label": "Columns", "columns": [{"components": [{"label": "Titles", "customClass": "mr-2", "type": "htmlelement", "tag": "p", "content": "- <PERSON><PERSON><PERSON> ≥ 5% CN/ 1 tháng hoặc<br/>- Ăn gi<PERSON>m còn 0-25%/1 tuần hoặc<br/>- BMI <18,5", "input": false}], "width": 4, "size": "md", "currentWidth": 4}, {"components": [{"label": "Nặng (3 điểm)", "customClass": "mr-2 dgddchung-3 luulog", "tableView": false, "validateWhenHidden": false, "key": "TINHDIEM5_3", "type": "checkbox", "input": true}], "width": 2, "size": "md", "currentWidth": 2}, {"components": [{"label": "Titles", "customClass": "mr-2", "type": "htmlelement", "tag": "p", "content": "- <PERSON><PERSON><PERSON> nặng (nh<PERSON> điều trị hồi sức)", "input": false}], "width": 4, "size": "md", "currentWidth": 4}, {"components": [{"label": "Nặng (3 điểm).", "customClass": "dgddchung-3 luulog", "tableView": false, "validateWhenHidden": false, "key": "TINHDIEM6_3", "type": "checkbox", "input": true}], "width": 2, "size": "md", "currentWidth": 2}], "key": "columns", "type": "columns", "customClass": "ml-0 mr-0"}, {"label": "Columns", "columns": [{"components": [{"label": "Tổng điểm (<PERSON><PERSON><PERSON> thêm 1 điểm nếu người bệnh  ≥ 70 tuổi)", "hideLabel": true, "prefix": "<PERSON><PERSON><PERSON> điểm", "suffix": "(<PERSON><PERSON><PERSON> thêm 1 điểm nếu người b<PERSON>nh  ≥ 70 tuổi)", "customClass": "formio-css-input-text-center formio-css-suffix formio-css-prefix luulog", "tableView": true, "validateWhenHidden": false, "key": "TONG_DIEM", "type": "textfield", "input": true, "disabled": true}], "width": 6, "size": "md", "currentWidth": 6}], "key": "columns", "type": "columns", "customClass": "ml-0 mr-0"}, {"label": "Titles", "customClass": "formio-css-p", "type": "htmlelement", "tag": "p", "content": "<b>Phần II: <PERSON><PERSON><PERSON> đo<PERSON> suy dinh dưỡng</b> (<PERSON>ầ<PERSON> dành cho <PERSON>ỹ)", "input": false}, {"label": "Columns", "columns": [{"components": [{"label": "Titles", "customClass": "formio-css-p", "type": "htmlelement", "tag": "p", "content": "<b><PERSON><PERSON><PERSON><PERSON> chí kiểu hình</b>", "input": false}], "width": 6, "size": "md", "currentWidth": 6}, {"components": [{"label": "Titles", "customClass": "formio-css-p", "type": "htmlelement", "tag": "p", "content": "<b><PERSON><PERSON><PERSON><PERSON> chí nguyên nhân</b>", "input": false}], "width": 6, "size": "md", "currentWidth": 6}], "key": "columns", "type": "columns", "customClass": "ml-0 mr-0"}, {"label": "Columns", "columns": [{"components": [{"label": "<PERSON><PERSON><PERSON> cân ≥ 5%/ 3 tháng gần đây?", "widget": "<PERSON><PERSON><PERSON>", "customClass": "mr-2 formio-css-selection luulog", "tableView": true, "data": {"values": [{"label": "K<PERSON>ô<PERSON>", "value": "1"}, {"label": "<PERSON><PERSON>", "value": "2"}]}, "validateWhenHidden": false, "key": "TIEUCHI_KIEU_HINH1", "type": "select", "input": true}], "width": 6, "size": "md", "currentWidth": 6}, {"components": [{"label": "<PERSON><PERSON><PERSON><PERSON> bệnh ăn uống giảm sút kéo dài trên 1 tuần?", "widget": "<PERSON><PERSON><PERSON>", "customClass": "formio-css-selection luulog", "tableView": true, "data": {"values": [{"label": "K<PERSON>ô<PERSON>", "value": "1"}, {"label": "<PERSON><PERSON>", "value": "2"}]}, "validateWhenHidden": false, "key": "TIEUCHI_NGUYENNHAN1", "type": "select", "input": true}], "width": 6, "size": "md", "currentWidth": 6}], "key": "columns", "type": "columns", "customClass": "ml-0 mr-0"}, {"label": "Columns", "columns": [{"components": [{"label": "BMI < 18,5 nếu NB < 70 tuổi, hoặc BMI < 20 nếu NB ≥ 70 tuổi", "widget": "<PERSON><PERSON><PERSON>", "customClass": "mr-2 formio-css-selection luulog", "tableView": true, "data": {"values": [{"label": "K<PERSON>ô<PERSON>", "value": "1"}, {"label": "<PERSON><PERSON>", "value": "2"}]}, "validateWhenHidden": false, "key": "TIEUCHI_KIEU_HINH2", "type": "select", "input": true}], "width": 6, "size": "md", "currentWidth": 6}, {"components": [{"label": "Hoặc có bệnh lý tiêu hóa mạn tính gây kém tiêu hóa/ hấp thu.", "widget": "<PERSON><PERSON><PERSON>", "customClass": "formio-css-selection luulog", "tableView": true, "data": {"values": [{"label": "K<PERSON>ô<PERSON>", "value": "1"}, {"label": "<PERSON><PERSON>", "value": "2"}]}, "validateWhenHidden": false, "key": "TIEUCHI_NGUYENNHAN2", "type": "select", "input": true}], "width": 6, "size": "md", "currentWidth": 6}], "key": "columns", "type": "columns", "customClass": "ml-0 mr-0"}, {"label": "Columns", "columns": [{"components": [{"label": "<PERSON><PERSON>/gi<PERSON>m kh<PERSON>i c<PERSON> (ngoại vi…)", "widget": "<PERSON><PERSON><PERSON>", "customClass": "mr-2 formio-css-selection luulog", "tableView": true, "data": {"values": [{"label": "K<PERSON>ô<PERSON>", "value": "1"}, {"label": "<PERSON><PERSON>", "value": "2"}]}, "validateWhenHidden": false, "key": "TIEUCHI_KIEU_HINH3", "type": "select", "input": true}], "width": 6, "size": "md", "currentWidth": 6}], "key": "columns", "type": "columns", "customClass": "ml-0 mr-0"}, {"label": "Columns", "columns": [{"components": [{"label": "<PERSON><PERSON> ngoại vi", "widget": "<PERSON><PERSON><PERSON>", "customClass": "mr-2 formio-css-selection luulog", "tableView": true, "data": {"values": [{"label": "K<PERSON>ô<PERSON>", "value": "1"}, {"label": "<PERSON><PERSON>", "value": "2"}]}, "validateWhenHidden": false, "key": "TIEUCHI_KIEU_HINH4", "type": "select", "input": true}], "width": 6, "size": "md", "currentWidth": 6}], "key": "columns", "type": "columns", "customClass": "ml-0 mr-0"}, {"label": "Titles", "customClass": "formio-css-p", "type": "htmlelement", "tag": "p", "content": "<b>Phần III: <PERSON><PERSON><PERSON> n<PERSON>n kế hoạch chăm sóc DD</b>", "input": false}, {"label": "Columns", "columns": [{"components": [{"label": "Phần III: <PERSON><PERSON><PERSON>n kế hoạch chăm sóc <PERSON>", "hideLabel": true, "widget": "<PERSON><PERSON><PERSON>", "customClass": "mr-2 formio-css-selection luulog", "tableView": true, "data": {"values": [{"label": "<PERSON><PERSON> sung DD qua miệng", "value": "1"}, {"label": "<PERSON><PERSON> độ DD qua ống thông", "value": "2"}, {"label": "<PERSON><PERSON> độ DD qua tĩnh mạch toàn phần", "value": "3"}, {"label": "<PERSON><PERSON> độ DD qua tĩnh mạch bổ sung", "value": "4"}, {"label": "<PERSON><PERSON><PERSON>", "value": "5"}]}, "validateWhenHidden": false, "key": "KEHOACH_CS_DD", "type": "select", "input": true}], "width": 6, "size": "md", "currentWidth": 6}], "key": "columns", "type": "columns", "customClass": "ml-0 mr-0"}]}