create or replace PROCEDURE     HIS_MANAGER.CMU_CHANDOANNNTUVONG_PROCED(
    p_id              IN     VARCHAR2,
    p_dvtt               IN     NUMBER,
    cur OUT SYS_REFCURSOR
) AS
BEGIN
open cur for
select
    phieu.ID,
    phieu.DVTT,
    phieu.SOVAOVIEN,
    phieu.MA_BENH_NHAN,
    nvl(JSON_VALUE(THONGTINBENHJSON, '$.BENHAN'), '...') AS BENHAN,
    nvl(JSON_VALUE(THONGTINBENHJSON, '$.MAHSBA'), '...') AS MAHSBA,
    nvl(JSON_VALUE(THONGTINBENHJSON, '$.SODINHDANH'), '...') AS SODINHDANH,
    nvl(JSON_VALUE(THONGTINBENHJSON, '$.LOAI'), '...') AS LOAI,
    nvl(JSON_VALUE(THONGTINBENHJSON, '$.SOTHEBHYT'), '...') AS SOTHEBHYT,
    nvl(JSON_VALUE(THONGTINBENHJSON, '$.GIATRITU'), '...') AS GIATRITU,
    nvl(JSON_VALUE(THONGTINBENHJSON, '$.GIATRIDEN'), '...') AS GIATRIDEN,
    nvl(JSON_VALUE(THONGTINBENHJSON, '$.NOIDANGKY'), '...') AS NOIDANGKY,
    nvl(JSON_VALUE(THONGTINBENHJSON, '$.THOIGIAN5NAM'), '...') AS THOIGIAN5NAM,
    nvl(JSON_VALUE(THONGTINBENHJSON, '$.MIENCUNGCHITRA'), '...') AS MIENCUNGCHITRA,
    nvl(JSON_VALUE(THONGTINBENHJSON, '$.DIACHIHTTINH'), '...') AS DIACHIHTTINH,
    nvl(JSON_VALUE(THONGTINBENHJSON, '$.DIACHIHTHUYEN'), '...') AS DIACHIHTHUYEN,
    nvl(JSON_VALUE(THONGTINBENHJSON, '$.DIACHIHTXA'), '...') AS DIACHIHTXA,
    nvl(JSON_VALUE(THONGTINBENHJSON, '$.DIACHIHTSONHA'), '...') AS DIACHIHTSONHA,
    nvl(JSON_VALUE(THONGTINBENHJSON, '$.DIACHITRTINH'), '...') AS DIACHITRTINH,
    nvl(JSON_VALUE(THONGTINBENHJSON, '$.DIACHITRHUYEN'), '...') AS DIACHITRHUYEN,
    nvl(JSON_VALUE(THONGTINBENHJSON, '$.DIACHITRXA'), '...') AS DIACHITRXA,
    nvl(JSON_VALUE(THONGTINBENHJSON, '$.DIACHITRSONHA'), '...') AS DIACHITRSONHA,
    nvl(JSON_VALUE(THONGTINBENHJSON, '$.DIACHIBENHTINH'), '...') AS DIACHIBENHTINH,
    nvl(JSON_VALUE(THONGTINBENHJSON, '$.DIACHIBENHHUYEN'), '...') AS DIACHIBENHHUYEN,
    nvl(JSON_VALUE(THONGTINBENHJSON, '$.DIACHIBENHXA'), '...') AS DIACHIBENHXA,
    nvl(JSON_VALUE(THONGTINBENHJSON, '$.DIACHIBENHSONHA'), '...') AS DIACHIBENHSONHA,
    nvl(JSON_VALUE(THONGTINBENHJSON, '$.TONGIAO'), '...') AS TONGIAO,
    nvl(JSON_VALUE(THONGTINBENHJSON, '$.SONGAYVANGMAT'), '...') AS SONGAYVANGMAT,
    nvl(JSON_VALUE(THONGTINBENHJSON, '$.LOAIVAOVIEN'), '...') AS LOAIVAOVIEN,
    nvl(JSON_VALUE(THONGTINBENHJSON, '$.NGAYRAVIEN'), '...') AS NGAYRAVIEN,
    nvl(JSON_VALUE(THONGTINBENHJSON, '$.CHUYENTU'), '...') AS CHUYENTU,
    nvl(JSON_VALUE(THONGTINBENHJSON, '$.CHUYENDEN'), '...') AS CHUYENDEN,
    nvl(JSON_VALUE(THONGTINBENHJSON, '$.TINHTRANGRV'), '...') AS TINHTRANGRV,
    nvl(JSON_VALUE(THONGTINBENHJSON, '$.KETQUADT'), '...') AS KETQUADT,
    nvl(JSON_VALUE(THONGTINBENHJSON, '$.SONGAYICU'), '...') AS SONGAYICU,
    nvl(JSON_VALUE(THONGTINBENHJSON, '$.CANNANGTRE'), '...') AS CANNANGTRE,
    nvl(JSON_VALUE(THONGTINBENHJSON, '$.CHANDOANRAVIEN'), '...') AS CHANDOANRAVIEN,
    phieu.TUVONGTAI,
    nvl(JSON_VALUE(NNTUVONGJSON, '$[0].BENHLY'), '...') AS BENHLY_1,
    nvl(JSON_VALUE(NNTUVONGJSON, '$[0].ICD'), '...') AS ICD_1,
    nvl(JSON_VALUE(NNTUVONGJSON, '$[0].DONVI'), '...') AS DONVI_1,
    nvl(JSON_VALUE(NNTUVONGJSON, '$[0].THOIGIAN'), '...') AS THOIGIAN_1,
    nvl(JSON_VALUE(NNTUVONGJSON, '$[1].BENHLY'), '...') AS BENHLY_2,
    nvl(JSON_VALUE(NNTUVONGJSON, '$[1].ICD'), '...') AS ICD_2,
    nvl(JSON_VALUE(NNTUVONGJSON, '$[1].DONVI'), '...') AS DONVI_2,
    nvl(JSON_VALUE(NNTUVONGJSON, '$[1].THOIGIAN'), '...') AS THOIGIAN_2,
    nvl(JSON_VALUE(NNTUVONGJSON, '$[2].BENHLY'), '...') AS BENHLY_3,
    nvl(JSON_VALUE(NNTUVONGJSON, '$[2].ICD'), '...') AS ICD_3,
    nvl(JSON_VALUE(NNTUVONGJSON, '$[2].DONVI'), '...') AS DONVI_3,
    nvl(JSON_VALUE(NNTUVONGJSON, '$[2].THOIGIAN'), '...') AS THOIGIAN_3,
    nvl(JSON_VALUE(NNTUVONGJSON, '$[3].BENHLY'), '...') AS BENHLY_4,
    nvl(JSON_VALUE(NNTUVONGJSON, '$[3].ICD'), '...') AS ICD_4,
    nvl(JSON_VALUE(NNTUVONGJSON, '$[3].DONVI'), '...') AS DONVI_4,
    nvl(JSON_VALUE(NNTUVONGJSON, '$[3].THOIGIAN'), '...') AS THOIGIAN_4,
    phieu.NNTUVONGJSON,
    nvl(JSON_VALUE(BENHKHACJSON, '$[0].BENHLY'), '...') AS BENHLYKHAC_1,
    nvl(JSON_VALUE(BENHKHACJSON, '$[0].ICD'), '...') AS ICDKHAC_1,
    nvl(JSON_VALUE(BENHKHACJSON, '$[0].DONVI'), '...') AS DONVIKHAC_1,
    nvl(JSON_VALUE(BENHKHACJSON, '$[0].THOIGIAN'), '...') AS THOIGIANKHAC_1,
    phieu.BENHKHACJSON,
    nvl(JSON_VALUE(PHAUTHUAT4TUANJSON, '$.PHAUTHUAT4TUAN'), '...') AS PHAUTHUAT4TUAN,
    nvl(JSON_VALUE(PHAUTHUAT4TUANJSON, '$.LYDOPHAUTHUAT4TUAN'), '...') AS LYDOPHAUTHUAT4TUAN,
    nvl(JSON_VALUE(PHAUTHUAT4TUANJSON, '$.TRUNGCAU'), '...') AS TRUNGCAU,
    nvl(JSON_VALUE(PHAUTHUAT4TUANJSON, '$.THOIGIAN4TUAN'), '...') AS THOIGIAN4TUAN,
    nvl(JSON_VALUE(PHAUTHUAT4TUANJSON, '$.SUDUNGKETQUA'), '...') AS SUDUNGKETQUA,
    phieu.PHAUTHUAT4TUANJSON,
    phieu.HINHTHUCTV,
    nvl(JSON_VALUE(NNBENNGOAIJSON, '$.TENNNBENNGOAI'), '...') AS TENNNBENNGOAI,
    nvl(JSON_VALUE(NNBENNGOAIJSON, '$.MOTANNBENNGOAI'), '...') AS MOTANNBENNGOAI,
    nvl(JSON_VALUE(NNBENNGOAIJSON, '$.MAICDNNBENNGOAI'), '...') AS MAICDNNBENNGOAI,
    nvl(JSON_VALUE(NNBENNGOAIJSON, '$.TENICDNNBENNGOAI'), '...') AS TENICDNNBENNGOAI,
    nvl(JSON_VALUE(NNBENNGOAIJSON, '$.NGAYXAYRA'), '...') AS NGAYXAYRA,
    nvl(JSON_VALUE(NNBENNGOAIJSON, '$.NOIXAYRATAINAN'), '...') AS NOIXAYRATAINAN,
    nvl(JSON_VALUE(NNBENNGOAIJSON, '$.DIADIEM'), '...') AS DIADIEM,
    phieu.NNBENNGOAIJSON,
    nvl(JSON_VALUE(TUVONGTHAINHIJSON, '$.DATHAI'), '...') AS DATHAI,
    nvl(JSON_VALUE(TUVONGTHAINHIJSON, '$.SINHNON'), '...') AS SINHNON,
    nvl(JSON_VALUE(TUVONGTHAINHIJSON, '$.TUVONGTHAINHI'), '...') AS TUVONGTHAINHI,
    nvl(JSON_VALUE(TUVONGTHAINHIJSON, '$.TUOITHAI'), '...') AS TUOITHAI,
    nvl(JSON_VALUE(TUVONGTHAINHIJSON, '$.TUOIME'), '...') AS TUOIME,
    nvl(JSON_VALUE(TUVONGTHAINHIJSON, '$.SOGIOSONG'), '...') AS SOGIOSONG,
    nvl(JSON_VALUE(TUVONGTHAINHIJSON, '$.CANNANGKHISINH'), '...') AS CANNANGKHISINH,
    nvl(JSON_VALUE(TUVONGTHAINHIJSON, '$.BENHLYCUAME'), '...') AS BENHLYCUAME,
    nvl(JSON_VALUE(TUVONGTHAINHIJSON, '$.MAICDBENHLYME'), '...') AS MAICDBENHLYME,
    nvl(JSON_VALUE(TUVONGTHAINHIJSON, '$.TENICDBENHLYME'), '...') AS TENICDBENHLYME,
    phieu.TUVONGTHAINHIJSON,
    nvl(JSON_VALUE(PHUNUJSON, '$.DANGMANGTHAI'), '...') AS DANGMANGTHAI,
    nvl(JSON_VALUE(PHUNUJSON, '$.THOIDIEMMANGTHAI'), '...') AS THOIDIEMMANGTHAI,
    nvl(JSON_VALUE(PHUNUJSON, '$.GOPPHANNANG'), '...') AS GOPPHANNANG,
    phieu.PHUNUJSON,
    nvl(JSON_VALUE(KETLUANJSON, '$.KETLUANTENBL'), '...') AS KETLUANTENBL,
    nvl(JSON_VALUE(KETLUANJSON, '$.KETLUANMAICD'), '...') AS KETLUANMAICD,
    nvl(JSON_VALUE(KETLUANJSON, '$.KETLUANTENICD'), '...') AS KETLUANTENICD,
    phieu.KETLUANJSON,
    phieu.BSDIEUTRI,
    phieu.TRUONGKHOA,
    phieu.THUTRUONG,
    nv1.TEN_NHANVIEN_CD TENBACSI,
    nv2.TEN_NHANVIEN_CD TENTRUONGKHOA,
    nv3.TEN_NHANVIEN_CD TENTHUTRUONG,
    dt.TEN_DANTOC DANTOC,
    pb.ten_phongban khoa,
    nn.TEN_NGHE_NGHIEP NGHENGHIEP,
    TO_CHAR(phieu.NGAY_TAO_PHIEU, ' "Ngày" DD "tháng" MM "năm" YYYY') NGAY_TAO_PHIEU

FROM CMU_CHANDOANNNTUVONG phieu
         LEFT JOIN HIS_FW.DM_NHANVIEN_CD nv1 ON phieu.BSDIEUTRI = nv1.MA_NHANVIEN
         LEFT JOIN HIS_FW.DM_NHANVIEN_CD nv2 ON phieu.TRUONGKHOA = nv2.MA_NHANVIEN
         LEFT JOIN HIS_FW.DM_NHANVIEN_CD nv3 ON phieu.THUTRUONG = nv3.MA_NHANVIEN
         LEFT JOIN his_fw.dm_phongban          pb ON phieu.makhoa = pb.ma_phongban
         LEFT JOIN his_public_list.dm_benh_nhan bn ON phieu.MA_BENH_NHAN = bn.ma_benh_nhan
         LEFT JOIN his_public_list.dm_dan_toc dt ON bn.ma_dantoc = dt.ma_dantoc
         LEFT JOIN his_public_list.dm_nghenghiep nn ON bn.MA_NGHE_NGHIEP = nn.MA_NGHE_NGHIEP

WHERE phieu.ID = p_id and phieu.dvtt = p_dvtt;
--order by phieu.NGAY_TAO_PHIEU;
END;