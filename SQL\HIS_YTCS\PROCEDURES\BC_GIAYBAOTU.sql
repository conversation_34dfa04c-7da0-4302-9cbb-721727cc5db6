CREATE OR REPLACE PROCEDURE bc_giaybaotu (
    b_id_nhankhau   VARCHAR2,
    b_ma<PERSON><PERSON>han    VARCHAR2,
    b_tuvong        VARCHAR2,--0: in giấy báo tử form <PERSON><PERSON> chết, 1: in giấy báo tử form tử vong
    b_dvtt          VARCHAR2,
    cur             OUT             SYS_REFCURSOR
) IS
    v_count NUMBER(3);
BEGIN
    IF b_tuvong = 1 THEN
SELECT
    COUNT(1)
INTO v_count
FROM
    his_ytcs.tv_thong_tin
WHERE
    ma_benh_nhan = b_ma<PERSON><PERSON>han
  AND tuvong_tress = 0
  AND b_<PERSON><PERSON><PERSON><PERSON> > 0;

IF v_count > 0 THEN
            OPEN cur FOR SELECT DISTINCT
                                tv.hoten_nguoibaotu   AS nguoibaotu,
                                namsinh_nguoibaotu    AS namsinhnguoibaotu,
                                (
                                    SELECT
                                        ten_quan_he
                                    FROM
                                        his_ytcs.dm_quan_he
                                    WHERE
                                        id_quan_he = tv.quanhe_nguoibaotu
                                ) AS quanhe,
                                bn.ten_benh_nhan      AS nguoichet,
                                (
                                    SELECT
                                        ten_gioi_tinh
                                    FROM
                                        dm_gioi_tinh
                                    WHERE
                                        id_gioi_tinh = bn.gioi_tinh
                                ) gioitinh,
                                TO_CHAR(bn.ngay_sinh, 'dd/MM/yyyy') namsinhnguoichet,
                                (
                                    SELECT
                                        ten_dantoc
                                    FROM
                                        his_ytcs.dm_dan_toc dt
                                    WHERE
                                        dt.id_dan_toc = bn.ma_dantoc
                                      AND ROWNUM = 1
                                ) AS dantoc,
                                'Việt Nam' AS quoctich,
                                bn.dia_chi            AS thuongtru,
                                TO_CHAR(tv.gio_tv)
                                    || ' giờ '
                                    || TO_CHAR(tv.phut_tv)
                                    || ' phút, '
                                    || ' ngày '
                                    || TO_CHAR(tv.ngay_tu_vong, 'dd/MM/yyyy') AS thoigianchet,
                                nvl(noitv.ten_noi_tv, 'chưa xác định') AS noichet,
                                nvl(nvl(tv.chi_tiet_nguyen_nhan, nntv.ten_nguyen_nhan_tv), 'Chưa xác dịnh') AS nguyennhan,
                                --tv.so_giayto cmt_benhnhan,
                                CASE
                                    WHEN tv.loai_giayto != 6 THEN
                                        tv.so_giayto
                                    ELSE
                                        ''
                                    END AS cmt_benhnhan,
                                --tv.noi_cap noicapcmt,
                                CASE
                                    WHEN tv.loai_giayto = 6 THEN
                                        ''
                                    ELSE
                                        tv.noi_cap
                                    END AS noicapcmt,
                                --nvl(to_char(tv.ngay_cap,'dd/mm/yyyy'),'') as ngaycapcmt,
                                CASE
                                    WHEN tv.loai_giayto != 6 THEN
                                        TO_CHAR(tv.ngay_cap, 'dd/mm/yyyy')
                                    ELSE
                                        ''
                                    END AS ngaycapcmt,
                                (
                                    SELECT
                                        dv.diachi
                                    FROM
                                        his_fw.dm_donvi dv
                                    WHERE
                                        dv.ma_donvi = b_dvtt
                                ) AS diachi,
                                --bn.ma_benh_nhan,
                                CASE
                                    WHEN REGEXP_LIKE ( tv.so_giayto,
                                                       '^[0-9]+$' ) THEN
                                        tv.so_giayto
                                    ELSE
                                        ''
                                    END AS ma_benh_nhan,
                                nvl(so_baotu, ' ') so_baotu,
                                nvl(quyenbt.tenquyen, ' ') tenquyen,
                                tv.nguoi_thu_thap,
                                his_manager.cmu_keystore_tracuu_get(b_dvtt, tv.sovaovien, 'GCHUNGTU') keysearch,
                                CASE
                                    WHEN ngayvao IS NOT NULL THEN
                                        TO_CHAR(ngayvao, 'HH24')
                                            || ' giờ '
                                            || TO_CHAR(ngayvao, 'MI')
                                            || ' phút, ngày '
                                            || TO_CHAR(ngayvao, 'DD/MM/YYYY')
                                    ELSE
                                        ''
                                    END ngayvao
                         FROM
                                his_ytcs.tv_thong_tin             tv
                                    INNER JOIN his_public_list.dm_benh_nhan      bn ON tv.ma_benh_nhan = bn.ma_benh_nhan
                                    INNER JOIN his_ytcs.dm_noi_tu_vong           noitv ON noitv.id_noi_tv = tv.id_noi_tv
                                    INNER JOIN his_ytcs.dm_nguyen_nhan_tu_vong   nntv ON nntv.id_nguyen_nhan_tv = tv.id_nguyen_nhan_tv
                                    LEFT JOIN his_manager.dm_quyenchungtu       quyenbt ON quyenbt.dvtt = b_dvtt
                                    AND tv.quyen_so = quyenbt.maquyen
                                    LEFT JOIN his_manager.noitru_dotdieutri     dot ON dot.dvtt = tv.id_don_vi
                                    AND dot.sovaovien = tv.sovaovien
                                    AND dot.stt_dotdieutri = 1
                         WHERE
                                tv.ma_benh_nhan = b_mabenhnhan
                           AND tuvong_tress = 0;

ELSE
            OPEN cur FOR SELECT DISTINCT
                             tv.hoten_nguoibaotu   AS nguoibaotu,
                             namsinh_nguoibaotu    AS namsinhnguoibaotu,
                             (
                                 SELECT
                                     ten_quan_he
                                 FROM
                                     his_ytcs.dm_quan_he
                                 WHERE
                                     id_quan_he = tv.quanhe_nguoibaotu
                             ) AS quanhe,
                             bn.ho_ten             AS nguoichet,
                             (
                                 SELECT
                                     ten_gioi_tinh
                                 FROM
                                     dm_gioi_tinh
                                 WHERE
                                     id_gioi_tinh = bn.gioi_tinh
                             ) gioitinh,
                             TO_CHAR(bn.ngay_sinh, 'dd/MM/yyyy') namsinhnguoichet,
                             (
                                 SELECT
                                     ten_dantoc
                                 FROM
                                     his_ytcs.dm_dan_toc dt
                                 WHERE
                                     dt.id_dan_toc = bn.id_dan_toc
                                   AND ROWNUM = 1
                             ) AS dantoc,
                             'Việt Nam' AS quoctich,
                             f_get_dia_phuong_text(bn.id_dia_phuong) AS thuongtru,
                             TO_CHAR(tv.gio_tv)
                                 || ' giờ '
                                 || TO_CHAR(tv.phut_tv)
                                 || ' phút, '
                                 || ' ngày '
                                 || TO_CHAR(tv.ngay_tu_vong, 'dd/MM/yyyy') AS thoigianchet,
                             nvl(noitv.ten_noi_tv, 'chưa xác định') AS noichet,
                             nvl(nvl(tv.chi_tiet_nguyen_nhan, nntv.ten_nguyen_nhan_tv), 'Chưa xác dịnh') AS nguyennhan,
                             tv.so_giayto          cmt_benhnhan,
                             tv.noi_cap            noicapcmt,
                             nvl(TO_CHAR(tv.ngay_cap, 'dd/mm/yyyy'), '') AS ngaycapcmt,
                             (
                                 SELECT
                                     dv.diachi
                                 FROM
                                     his_fw.dm_donvi dv
                                 WHERE
                                     dv.ma_donvi = b_dvtt
                             ) AS diachi,
                             bn.mabenhnhan         AS ma_benh_nhan,
                             nvl(so_baotu, ' ') so_baotu,
                             nvl(quyenbt.tenquyen, ' ') tenquyen,
                             tv.nguoi_thu_thap,
                             his_manager.cmu_keystore_tracuu_get(b_dvtt, tv.sovaovien, 'GCHUNGTU') keysearch,
                             CASE
                                 WHEN ngayvao IS NOT NULL THEN
                                     TO_CHAR(ngayvao, 'HH24')
                                         || ' giờ '
                                         || TO_CHAR(ngayvao, 'MI')
                                         || ' phút, ngày '
                                         || TO_CHAR(ngayvao, 'DD/MM/YYYY')
                                 ELSE
                                     ''
                                 END ngayvao
                         FROM
                             his_ytcs.tv_thong_tin             tv
                                 INNER JOIN ds_nhan_khau                      bn ON tv.id_nhan_khau = bn.id_nhan_khau
                                 INNER JOIN his_ytcs.dm_noi_tu_vong           noitv ON noitv.id_noi_tv = tv.id_noi_tv
                                 INNER JOIN his_ytcs.dm_nguyen_nhan_tu_vong   nntv ON nntv.id_nguyen_nhan_tv = tv.id_nguyen_nhan_tv
                                 LEFT JOIN his_manager.dm_quyenchungtu       quyenbt ON quyenbt.dvtt = b_dvtt
                                 AND tv.quyen_so = quyenbt.maquyen
                                 LEFT JOIN his_manager.noitru_dotdieutri     dot ON dot.dvtt = tv.id_don_vi
                                 AND dot.sovaovien = tv.sovaovien
                                 AND dot.stt_dotdieutri = 1
                         WHERE
                             tv.id_nhan_khau = b_id_nhankhau
                           AND tuvong_tress = 0;

END IF;

ELSE
        OPEN cur FOR SELECT DISTINCT
                         bd.nguoi_bao_tu   nguoibaotu,
                         bd.namsinh_nbt    namsinhnguoibaotu,
                         (
                             SELECT
                                 ten_quan_he
                             FROM
                                 his_ytcs.dm_quan_he
                             WHERE
                                 id_quan_he = bd.quanhe_nbt
                         ) AS quanhe,
                         nk.ho_ten         nguoichet,
                         (
                             SELECT
                                 ten_gioi_tinh
                             FROM
                                 dm_gioi_tinh
                             WHERE
                                 id_gioi_tinh = nk.gioi_tinh
                         ) gioitinh,
                         TO_CHAR(nk.ngay_sinh, 'dd/MM/yyyy') namsinhnguoichet,
                         (
                             SELECT
                                 ten_dantoc
                             FROM
                                 his_ytcs.dm_dan_toc dt
                             WHERE
                                 dt.id_dan_toc = nk.id_dan_toc
                               AND ROWNUM = 1
                         ) AS dantoc,
                         'Việt Nam' AS quoctich,
                         his_ytcs.f_get_dia_phuong_text(nk.id_dia_phuong) thuongtru,
                         TO_CHAR(bd.giotuvong)
                             || ' giờ '
                             || TO_CHAR(bd.phuttuvong)
                             || ' phút, '
                             || ' ngày '
                             || TO_CHAR(bd.ngay_bien_dong, 'dd/MM/yyyy') AS thoigianchet,
                         nvl(noitv.ten_noi_tv, 'chưa xác định') AS noichet,
                         nvl(nvl(tv.chi_tiet_nguyen_nhan, nntv.ten_nguyen_nhan_tv), 'Chưa xác dịnh') AS nguyennhan,
                         nk.cmt            AS cmt_benhnhan,
                         nk.noicap_cmt     AS noicapcmt,
                         nvl(TO_CHAR(nk.ngaycap_cmt, 'dd/mm/yyyy'), '') AS ngaycapcmt,
                         (
                             SELECT
                                 dv.diachi
                             FROM
                                 his_fw.dm_donvi dv
                             WHERE
                                 dv.ma_donvi = b_dvtt
                         ) AS diachi,
                         nk.mabenhnhan     AS ma_benh_nhan,
                         nvl(so_baotu, ' ') so_baotu,
                         nvl(quyenbt.tenquyen, ' ') tenquyen,
                         tv.nguoi_thu_thap,
                         his_manager.cmu_keystore_tracuu_get(b_dvtt, tv.sovaovien, 'GCHUNGTU') keysearch,
                         CASE
                             WHEN ngayvao IS NOT NULL THEN
                                 TO_CHAR(ngayvao, 'HH24')
                                     || ' giờ '
                                     || TO_CHAR(ngayvao, 'MI')
                                     || ' phút, ngày '
                                     || TO_CHAR(ngayvao, 'DD/MM/YYYY')
                             ELSE
                                 ''
                             END ngayvao
                     FROM
                         his_ytcs.ds_nhan_khau             nk
                             INNER JOIN his_ytcs.ds_bien_dong_chet        bd ON nk.id_nhan_khau = bd.id_nhan_khau
                             LEFT JOIN his_ytcs.tv_thong_tin             tv ON tv.id_nhan_khau = nk.id_nhan_khau
                             LEFT JOIN his_ytcs.dm_noi_tu_vong           noitv ON noitv.id_noi_tv = bd.id_noi_tv
                             LEFT JOIN his_ytcs.dm_nguyen_nhan_tu_vong   nntv ON nntv.id_nguyen_nhan_tv = bd.id_nguyen_nhan_tv
                             LEFT JOIN his_manager.dm_quyenchungtu       quyenbt ON quyenbt.dvtt = b_dvtt
                             AND tv.quyen_so = quyenbt.maquyen
                             LEFT JOIN his_manager.noitru_dotdieutri     dot ON dot.dvtt = tv.id_don_vi
                             AND dot.sovaovien = tv.sovaovien
                             AND dot.stt_dotdieutri = 1
                     WHERE
                         bd.id_nhan_khau = b_id_nhankhau;

END IF;
END; -- Procedure