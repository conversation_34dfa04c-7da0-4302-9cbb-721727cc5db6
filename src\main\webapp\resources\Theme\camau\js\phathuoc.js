var arrPhaThuocTemp = [];
var formphathuoc, formphathuoc_chitiet, formUpdatePhaThuocCT;
var arrIdGridDangPha = []
var bienPhaThuoc = true;
var bienCopyPhaThuoc = true;
var ngungCopyPha = false;
var arrBienCopyPha = [];

function reloadGridPhaThuoc_DanhSachThuoc(){
    var url = "cmu_getlist?url="+convertArray([todieutriObject.SOVAOVIEN, todieutriObject.ID_DIEUTRI, todieutriObject.STT_DIEUTRI,
        todieutriObject.STT_DOTDIEUTRI, thongtinhsba.thongtinbn.DVTT,'CMU_DANHSACHTHUOCPHA']);
    $("#list_phathuoc_dapha").jqGrid('setGridParam', {datatype: 'json', url: url}).trigger('reloadGrid');
}

function reloadGridPhaThuoc_ChiTiet(id_phathuoc){
    var url = "cmu_getlist?url="+convertArray([todieutriObject.SOVAOVIEN, todieutriObject.ID_DIEUTRI, todieutriObject.STT_DIEUTRI,
        todieutriObject.STT_DOTDIEUTRI, id_phathuoc, thongtinhsba.thongtinbn.DVTT,'CMU_DANHSACHTHUOCPHA_CHITIET']);
    $("#list_phathuoc_dapha_chitiet").jqGrid('setGridParam', {datatype: 'json', url: url}).trigger('reloadGrid');
}

function createFromPhaThuoc_DanhSachThuoc(){
    Formio.createForm(document.getElementById('formPhathuoc'),
        {
            "display": "form",
            "components": [
                {
                    "label": "Danh sách thuốc",
                    "disableAddingRemovingRows": true,
                    "reorder": false,
                    "layoutFixed": false,
                    "enableRowGroups": false,
                    "initEmpty": true,
                    "customClass": "ml-2 table-datagrid-no-border",
                    "tableView": false,
                    "defaultValue": [
                        {
                            "phathuoc_tendichtruyen": "",
                            "submit": false,
                            "phathuoc_soluongpha": null,
                            "phathuoc_soluongtong": null
                        }
                    ],
                    "persistent": false,
                    "key": "phathuoc_danhsachthuocpha",
                    "type": "datagrid",
                    "input": true,
                    "components": [
                        {
                            "label": "Columns",
                            "columns": [
                                {
                                    "components": [
                                        {
                                            "label": "Tên dịch truyền / pha thuốc",
                                            "applyMaskOn": "change",
                                            "customClass": "mr-2 ml-3",
                                            "disabled": true,
                                            "tableView": true,
                                            "validate": {
                                                "required": true
                                            },
                                            "key": "phathuoc_tendichtruyen",
                                            "attributes": {
                                                "id": "phathuoc_tendichtruyen"
                                            },
                                            "type": "textfield",
                                            "input": true
                                        },
                                        {
                                            "label": "DT/SL",
                                            "applyMaskOn": "change",
                                            "customClass": "mr-2",
                                            "mask": false,
                                            "tableView": true,
                                            "delimiter": false,
                                            "requireDecimal": false,
                                            "inputFormat": "plain",
                                            "truncateMultipleSpaces": false,
                                            "validate": {
                                                "required": true
                                            },
                                            "tooltip": "Dung tích hoặc số lượng thuốc/dịch truyền của 1 thuốc/dịch truyền",
                                            "key": "phathuoc_dungtich",
                                            "attributes": {
                                                "id": "phathuoc_dungtich"
                                            },
                                            "type": "number",
                                            "input": true,
                                            "hidden": true,
                                        }
                                    ],
                                    "width": 8,
                                    "offset": 0,
                                    "push": 0,
                                    "pull": 0,
                                    "size": "md",
                                    "currentWidth": 8
                                },
                                {
                                    "components": [
                                        {
                                            "label": "aaaa",
                                            "key": "htmlBtnThuoc",
                                            "type": "htmlelement",
                                            "content": "{{ row.htmlBtnThuoc }}",
                                            "input": false,
                                            "tableView": false
                                        }
                                    ],
                                    "size": "md",
                                    "width": 2,
                                    "offset": 0,
                                    "push": 0,
                                    "pull": 0,
                                    "currentWidth": 2
                                },
                                {
                                    "components": [
                                        {
                                            "label": "DT/SL pha",
                                            "applyMaskOn": "change",
                                            "customClass": "pl-2",
                                            "mask": false,
                                            "tableView": true,
                                            "delimiter": false,
                                            "requireDecimal": false,
                                            "inputFormat": "plain",
                                            "truncateMultipleSpaces": false,
                                            "validate": {
                                                "required": true,
                                                "min": 0.001
                                            },
                                            "key": "phathuoc_soluongpha",
                                            "attributes": {
                                                "id": "phathuoc_soluongpha"
                                            },
                                            "type": "number",
                                            "input": true
                                        }
                                    ],
                                    "size": "md",
                                    "width": 2,
                                    "offset": 0,
                                    "push": 0,
                                    "pull": 0,
                                    "currentWidth": 2
                                }
                            ],
                            "customClass": "mt-0 mr-0",
                            "hideLabel": true,
                            "key": "phathuoc_formphathuoc1",
                            "type": "columns",
                            "input": false,
                            "tableView": false
                        }
                    ]
                }
            ]
        }
    ).then(function(form) {
        arrPhaThuocTemp = [];
        formphathuoc = form;
        arrIdGridDangPha = [];
        var previousData = JSON.stringify(form.submission.data);
        form.on('change', function(changed) {
            var currentData = JSON.stringify(form.submission.data);
            var  data = form.submission.data;
            var datThuoc = '';
            var soLo = '';
            var tenThuoc = '';
            var soLuongTruyen = 0;
            var obj
            if (bienPhaThuoc) {
                for (var i = 0; i < data.phathuoc_danhsachthuocpha.length; i++) {
                    obj = data.phathuoc_danhsachthuocpha;
                    if (obj[i].phathuoc_nghiepvu != "thuocpha") {
                        initButtonEditDungTich(obj[i].idBtnThuoc, obj[i].phathuoc_mavattu, obj[i].phathuoc_nghiepvu, function (dataReturn) {
                            if (dataReturn.change == 1){
                                formphathuoc.submission.data.phathuoc_danhsachthuocpha.filter(function (item) {
                                    if (item.idBtnThuoc == dataReturn.idBtnThuoc){
                                        item.phathuoc_dungtich = dataReturn.DUNGTICH;
                                        item.phathuoc_soluongtong = dataReturn.DUNGTICH * item.phathuoc_soluongthuoc;
                                        item.phathuoc_soluongpha = dataReturn.DUNGTICH * item.phathuoc_soluongthuoc - item.phathuoc_soluongdasudung;
                                        item.phathuoc_dvtnhonhat = dataReturn.DVTNHONHAT;
                                    }
                                });
                                formphathuoc.submission = {
                                    data: {
                                        phathuoc_danhsachthuocpha: formphathuoc.submission.data.phathuoc_danhsachthuocpha
                                    }
                                };
                            }
                        });
                    }
                    if (i == 0) {
                        datThuoc += obj[i].phathuoc_hoatchat + "(" + obj[i].phathuoc_tendichtruyen + ") " + obj[i].phathuoc_soluongpha + obj[i].phathuoc_dvtnhonhat;
                        soLo += obj[i].phathuoc_solo_ct;
                        tenThuoc += obj[i].phathuoc_tendichtruyen;
                    } else if (i == 1) {
                        datThuoc += " pha với " + obj[i].phathuoc_hoatchat + "(" + obj[i].phathuoc_tendichtruyen + ") " + obj[i].phathuoc_soluongpha + obj[i].phathuoc_dvtnhonhat;
                        soLo += ', ' + obj[i].phathuoc_solo_ct;
                        tenThuoc += ', ' + obj[i].phathuoc_tendichtruyen;
                    } else {
                        datThuoc += " và " + obj[i].phathuoc_hoatchat + "(" + obj[i].phathuoc_tendichtruyen + ") " + obj[i].phathuoc_soluongpha + obj[i].phathuoc_dvtnhonhat;
                        soLo += ', ' + obj[i].phathuoc_solo_ct;
                        tenThuoc += ', ' + obj[i].phathuoc_tendichtruyen;
                    }
                    soLuongTruyen += obj[i].phathuoc_soluongpha;
                }
                formphathuoc_chitiet.getComponent('phathuoc_ghichu').setValue(datThuoc);
                formphathuoc_chitiet.getComponent('phathuoc_solo').setValue(soLo);
                formphathuoc_chitiet.getComponent('phathuoc_tenthuocpha').setValue(datThuoc);
                formphathuoc_chitiet.getComponent('SOLUONGTRUYEN').setValue(soLuongTruyen);
                formphathuoc_chitiet.getComponent('phathuoc_dungtichsaupha').setValue(soLuongTruyen);
                formphathuoc_chitiet.submitForm();
                formphathuoc.submitForm();
            }
        });

        function showFormUpdatePhaThuocCT(data) {
            var jsonForm = getJSONObjectForm([

                {
                    "label": "Tên thuốc pha",
                    "key": "TENTHUOCPHA",
                    "customClass": "pr-2",
                    "type": "textarea",
                },
                {
                    "label": "Ghi chú",
                    "key": "GHICHU",
                    "customClass": "pr-2",
                    "type": "textarea",
                },
                {
                    "label": "",
                    "columns": [
                        {
                            "components": [
                                {
                                    "label": "Y lệnh",
                                    "customClass": "pr-2",
                                    "key": "YLENH",
                                    "type": "select",
                                    others: {
                                        "data": {
                                            "values": [
                                                {
                                                    "label": "Thuốc",
                                                    "value": "thuoc"
                                                },
                                                {
                                                    "label": "Truyền dịch",
                                                    "value": "truyendich"
                                                },
                                            ]
                                        },
                                    },
                                },
                            ],
                            "width": 6,
                            "size": "md",
                        },
                        {
                            "components": [
                                JSONDateTime("Ngày y lệnh", "NGAY_Y_LENH", "pr-2", "dd/MM/yyyy HH:mm",
                                    true, moment(todieutriObject.NGAYGIOLAPTDT, ['DD/MM/YYYY HH:mm']).format("YYYY-MM-DD HH:mm"),
                                    null, {}, {
                                        "validate": {
                                            "required": false
                                        },
                                    }),
                            ],
                            "width": 6,
                            "size": "md",
                        },
                    ],
                    "key": "columns_nhin",
                    "type": "columns",
                    "customClass": "ml-0 mr-0 pb-2",
                },
                {
                    "label": "",
                    "columns": [
                        {
                            "components": [
                                {
                                    "label": "Tốc độ",
                                    "customClass": "pr-2",
                                    "key": "LOAITOCDO",
                                    "type": "select",
                                    others: {
                                        "data": {
                                            "values": [
                                                {
                                                    "label": "Giọt/phút",
                                                    "value": 'giot/phut'
                                                },
                                                {
                                                    "label": "ml/giờ",
                                                    "value": 'ml/gio'
                                                },
                                            ]
                                        },
                                        "defaultValue": "giot/phut",
                                        "customConditional": "show = (data.YLENH == 'truyendich' ? 1 : 0);",
                                    },
                                },
                            ],
                            "width": 3,
                            "size": "md",
                        },
                        {
                            "components": [
                                JSONNumber("&nbsp;", "TOCDO", "pr-2", {}, {
                                    "validate": {
                                        "required": true
                                    },
                                    "min": 1,
                                    others: {
                                        "customConditional": "show = (data.YLENH == 'truyendich' ? 1 : 0);",
                                    }
                                }),
                            ],
                            "width": 3,
                            "size": "md",
                        },
                        {
                            "components": [
                                JSONNumber("Số lượng truyền (ml)", "SOLUONGTRUYEN", "pr-2", {}, {
                                    "validate": {
                                        "required": true
                                    },
                                    "min": 0.001,
                                    others: {
                                        "customConditional": "show = (data.YLENH == 'truyendich' ? 1 : 0);",
                                    }
                                }),
                            ],
                            "width": 4,
                            "size": "md",
                        },
                        {
                            "components": [
                                JSONTextField("Liều dùng", "LIEUDUNG", "", {}, {
                                    others: {
                                        "customConditional": "show = (data.YLENH == 'truyendich' ? 1 : 0);",
                                    }
                                }),
                            ],
                            "width": 2,
                            "size": "md",
                        },
                    ],
                    "key": "columns_nhin",
                    "type": "columns",
                    "customClass": "ml-0 mr-0 pb-2",
                },
                {
                    "label": "Id",
                    "key": "ID_PHATHUOC",
                    "type": "hidden",
                },
            ])
            Formio.createForm(document.getElementById('formUpdatePhaThuocCT'),
                jsonForm,{}
            ).then(function(form) {
                formUpdatePhaThuocCT = form;
                formUpdatePhaThuocCT.submission = {
                    data: {
                        ...data,
                        YLENH: data.LOAI_YLENH,
                        LOAITOCDO: data.LOAI_TOCDO,
                        NGAY_Y_LENH:  (data.YLENH_SHOW ? moment(data.YLENH_SHOW.split(" - ")[1], ['DD/MM/YYYY HH:mm']): moment()).toISOString(),
                    }
                }
            });
        }




        $("#list_phathuoc_dapha").jqGrid({
            url: "cmu_getlist?url="+convertArray([todieutriObject.SOVAOVIEN, todieutriObject.ID_DIEUTRI, todieutriObject.STT_DIEUTRI, todieutriObject.STT_DOTDIEUTRI, thongtinhsba.thongtinbn.DVTT,'CMU_DANHSACHTHUOCPHA']),
            datatype: "json",
            loadonce: true,
            height: 200,
            autowidth: true,
            shrinkToFit: true,
            colModel: [
                {label:'Mã', name: 'ID_PHATHUOC', index: 'ID_PHATHUOC', width: 30},
                {label:'Mã', name: 'ID_DIEUTRI', index: 'ID_DIEUTRI', width: 10, hidden: true},
                {label:'Mã', name: 'STT_BENHAN', index: 'STT_BENHAN', width: 10, hidden: true},
                {label:'Mã', name: 'STT_DOTDIEUTRI', index: 'STT_DOTDIEUTRI', width: 10, hidden: true},
                {label:'Mã', name: 'STT_DIEUTRI', index: 'STT_DIEUTRI', width: 10, hidden: true},
                {label:'Mã', name: 'SOVAOVIEN', index: 'SOVAOVIEN', width: 10, hidden: true},
                {label:'Mã', name: 'ID_YLENH', index: 'ID_YLENH', width: 10, hidden: true},
                {label:'Mã', name: 'LOAI_YLENH', index: 'LOAI_YLENH', width: 10, hidden: true},
                {label:'Mã', name: 'TOCDO', index: 'TOCDO', width: 10, hidden: true},
                {label:'Mã', name: 'LOAI_TOCDO', index: 'LOAI_TOCDO', width: 10, hidden: true},
                {label:'Mã', name: 'SOLUONGTRUYEN', index: 'SOLUONGTRUYEN', width: 10, hidden: true},
                {label:'Mã', name: 'LIEUDUNG', index: 'LIEUDUNG', width: 10, hidden: true},
                {label:'Tên thuốc pha', name: 'TENTHUOCPHA', index: 'TENTHUOCPHA', width: 200,
                    cellattr: function (rowId, tv, rawObject, cm, rdata) {
                        return 'style="white-space: normal;"';
                    }},
                {label:'Số lô', name: 'SOLO_TONG', index: 'SOLO_TONG', width: 80},
                {label:'SL pha', name: 'SOLUONGPHA_TONG', index: 'SOLUONGPHA_TONG', width: 60},
                {label:'Ghi chú', name: 'GHICHU', index: 'GHICHU', width: 200,
                    cellattr: function (rowId, tv, rawObject, cm, rdata) {
                        return 'style="white-space: normal;"';
                    }},
                {label:'Y lệnh', name: 'YLENH_SHOW', index: 'YLENH_SHOW', width: 150,
                    cellattr: function (rowId, tv, rawObject, cm, rdata) {
                        return 'style="white-space: normal;"';
                    }},
            ],
            sortorder: "asc",
            rowNum: 100000,
            ignoreCase: true,
            loadComplete: function() {

            },
            onSelectRow: function (id) {
                if (id) {
                    var ret = $(this).jqGrid('getRowData', id);
                    reloadGridPhaThuoc_ChiTiet(ret.ID_PHATHUOC);
                }
            },
            onRightClickRow: function (id1) {
                if (id1) {
                    $("#list_phathuoc_dapha").jqGrid('setSelection', id1);
                    $.contextMenu({
                        selector: '#list_phathuoc_dapha tr',
                        callback: function (key, options) {
                            var id = $("#list_phathuoc_dapha").jqGrid('getGridParam', 'selrow');
                            var ret = $("#list_phathuoc_dapha").jqGrid('getRowData', id);
                            if (key == "sua"){
                                showFormUpdatePhaThuocCT(ret);
                                $("#modalFormUpdatePhaThuocCT").modal("show");
                                addTextTitleModal('titleFormUpdatePhaThuocCT', "Cập nhật thông tin pha thuốc");
                            }
                            if (key == "copy"){
                                arrBienCopyPha = [];
                                var thuocDapha = getAllRowDataJqgrid("list_phathuoc_dapha_chitiet")
                                thuocDapha.forEach(function(rowData) {
                                    var rowIds = $("#list_phathuoc_dangpha").jqGrid('getDataIDs');
                                    rowIds.forEach(function(rowId) {
                                        var checkbox = document.getElementById("checkboxListDangPha" + rowId);
                                        var rowData2 = $("#list_phathuoc_dangpha").jqGrid('getRowData', rowId);
                                        if (rowData2.MAVATTU == rowData.MAVATTU) {
                                            if (checkbox) {
                                                // checkbox.checked = true;
                                                if (!ngungCopyPha){
                                                    themThuocPha(rowId, rowData.TENVATTU);
                                                    if (bienCopyPhaThuoc){
                                                        // checkbox.checked = false;
                                                        arrBienCopyPha.push(rowId)
                                                    }
                                                }
                                                if (ngungCopyPha){
                                                    arrBienCopyPha = []
                                                }
                                            }
                                        } else {
                                            // checkbox.checked = false;
                                        }
                                    });
                                });
                                bienCopyPhaThuoc = true;
                                ngungCopyPha = false;
                                if (arrBienCopyPha.length > 0) {
                                    formphathuoc.submission = {
                                        data: {
                                            phathuoc_danhsachthuocpha: []
                                        }
                                    }
                                    var arrTemp = [];
                                    arrBienCopyPha.forEach(function (row){
                                        var rowIds = $("#list_phathuoc_dangpha").jqGrid('getDataIDs');
                                        rowIds.forEach(function(rowId) {
                                            var rowDataDangPha = $("#list_phathuoc_dangpha").jqGrid('getRowData', rowId);
                                            var checkbox = document.getElementById("checkboxListDangPha" + rowId);
                                            if (arrBienCopyPha.includes(rowId)){
                                            } else {
                                                checkbox.checked = false;
                                            }
                                            if (rowId == row){
                                                checkbox.checked = true;
                                                bienPhaThuoc = false;

                                                thuocDapha.forEach(function(rowData) {
                                                    if (rowDataDangPha.MAVATTU == rowData.MAVATTU){
                                                        var objectTemp = {
                                                            phathuoc_mavattu: rowDataDangPha.MAVATTU,
                                                            phathuoc_hoatchat: rowDataDangPha.HOAT_CHAT,
                                                            phathuoc_dvt: rowDataDangPha.DVT,
                                                            phathuoc_solo_ct: rowDataDangPha.SOLOSANXUAT,
                                                            phathuoc_tendichtruyen: rowData.TENVATTU ? rowData.TENVATTU : rowDataDangPha.TEN_VAT_TU,
                                                            phathuoc_dungtich: rowDataDangPha.DUNGTICH_DICHTRUYEN,
                                                            phathuoc_soluongthuoc_max: rowDataDangPha.SO_LUONG_THUC_LINH,
                                                            phathuoc_soluongthuoc: rowDataDangPha.SO_LUONG_THUC_LINH,
                                                            phathuoc_soluongtong: rowDataDangPha.DUNGTICH_TONG,
                                                            // phathuoc_soluongpha: rowData.SOLUONGPHA ? rowData.SOLUONGPHA :  Number(rowDataDangPha.DUNGTICH_TONG) - Number(rowDataDangPha.DUNGTICH_SUDUNG),
                                                            phathuoc_soluongpha: rowData.SOLUONGPHA ? rowData.SOLUONGPHA :  Number(rowDataDangPha.DUNGTICH_TONG),
                                                            // phathuoc_soluongdasudung: Number(rowDataDangPha.DUNGTICH_SUDUNG),
                                                            phathuoc_soluongdasudung: 0,
                                                            phathuoc_loai: rowDataDangPha.LOAI,
                                                            phathuoc_id_dieutri: rowDataDangPha.ID_DIEUTRI,
                                                            phathuoc_stt_toathuoc: rowDataDangPha.STT_TOATHUOC,
                                                            phathuoc_nghiepvu: rowDataDangPha.NGHIEP_VU,
                                                            phathuoc_dvtnhonhat: rowDataDangPha.DVTNHONHAT,
                                                        }
                                                        arrTemp.push(objectTemp);
                                                    }
                                                })

                                            }
                                        })
                                    })
                                    formphathuoc.submission = {
                                        data: {
                                            phathuoc_danhsachthuocpha: arrTemp
                                        }
                                    };
                                    formphathuoc_chitiet.submission = {
                                        data: {
                                            phathuoc_tenthuocpha: ret.TENTHUOCPHA,
                                            phathuoc_solo: ret.SOLO_TONG,
                                            phathuoc_ghichu: ret.GHICHU,
                                            YLENH: ret.LOAI_YLENH,
                                            LOAITOCDO: ret.LOAI_YLENH == "truyendich" ? ret.LOAI_TOCDO : "",
                                            TOCDO: ret.LOAI_YLENH == "truyendich" ? ret.TOCDO : "",
                                            SOLUONGTRUYEN: ret.LOAI_YLENH == "truyendich" ? ret.SOLUONGTRUYEN : "",
                                            LIEUDUNG: ret.LOAI_YLENH == "truyendich" ? ret.LIEUDUNG : "",
                                            NGAY_Y_LENH:  (ret.YLENH_SHOW ? moment(ret.YLENH_SHOW.split(" - ")[1], ['DD/MM/YYYY HH:mm']): moment()).toISOString(),
                                        }
                                    }
                                } else {
                                    arrIdGridDangPha = []
                                    formphathuoc.submission = {
                                        data: {
                                            phathuoc_danhsachthuocpha: []
                                        }
                                    };
                                    formphathuoc_chitiet.submission = {
                                        data: {}
                                    }
                                }
                            }
                            if (key == "xoa") {
                                $.confirm({
                                    title: 'Xác nhận!',
                                    type: 'orange',
                                    content: 'Bạn có chắc chắn muốn xóa thuốc pha này?',
                                    buttons: {
                                        warning: {
                                            btnClass: 'btn-warning',
                                            text: "Tiếp tục",
                                            action: function(){
                                                var thuocDapha = getAllRowDataJqgrid("list_phathuoc_dapha_chitiet")
                                                $.post("cmu_post", {
                                                    url: [thongtinhsba.thongtinbn.DVTT,
                                                        todieutriObject.ID_DIEUTRI,
                                                        ret.ID_PHATHUOC,
                                                        ret.ID_YLENH,
                                                        "CMU_PHATHUOC_DEL_V2"
                                                    ].join("```")
                                                }).done(function (dt) {
                                                    if(dt==1){
                                                        thuocDapha.forEach(function(rowData) {
                                                            if (rowData.LOAI != 'phathuoc'){
                                                                $.post("cmu_post", {
                                                                    url: [thongtinhsba.thongtinbn.DVTT,
                                                                        rowData.ID_DIEUTRI,
                                                                        rowData.STT_TOATHUOC,
                                                                        rowData.LOAI == 'muangoai' ? 'noitru_toamuangoai' : 'noitru_toathuoc',
                                                                        "CMU_CAPNHATDUNGTICHTHUOC"
                                                                    ].join("```")
                                                                }).done(function (dt) {

                                                                });
                                                            }
                                                        });
                                                        $("#action_dsphathuoclammoi").click();
                                                        createFromPhaThuoc_ChiTiet();
                                                        reloadGridPhaThuoc_DanhSachThuoc();
                                                        reloadGridPhaThuoc_ChiTiet("");
                                                        notifiToClient("Green", "Xóa pha thuốc thành công");
                                                    } else if (dt==-1){
                                                        notifiToClient("Red", "Thuốc đã được truyền, không thể xóa");
                                                    } else if (dt==-2){
                                                        notifiToClient("Red", "Thuốc đã được pha, không thể xóa");
                                                    }
                                                }).fail(function() {
                                                    notifiToClient("Red", "Xóa thất bại")
                                                });
                                            }
                                        },
                                        cancel: function () {
                                        }
                                    }
                                });
                            }
                        },
                        items: {
                            "copy": {name: '<p><i class="fa fa-bolt text-primary" aria-hidden="true"></i> Copy dữ liệu</p>'},
                            "sua": {name: '<p><i class="fa fa-pencil-square-o text-primary" aria-hidden="true"></i> Chỉnh sửa</p>'},
                            "xoa": {name: '<p style="color:red"><i class="fa fa-trash-o" aria-hidden="true"></i> Xóa</p>'},
                        }
                    });
                }
            },
        });

        $("#list_phathuoc_dapha_chitiet").jqGrid({
            url: "cmu_getlist?url="+convertArray([todieutriObject.SOVAOVIEN, todieutriObject.ID_DIEUTRI, todieutriObject.STT_DIEUTRI, todieutriObject.STT_DOTDIEUTRI, '', thongtinhsba.thongtinbn.DVTT,'CMU_DANHSACHTHUOCPHA_CHITIET']),
            datatype: "json",
            loadonce: true,
            height: 200,
            autowidth: true,
            shrinkToFit: true,
            colModel: [
                {label:'Mã', name: 'ID_PHATHUOC_CHITIET', index: 'ID_PHATHUOC_CHITIET', width: 10, hidden: true},
                {label:'Mã', name: 'ID_PHATHUOC', index: 'ID_PHATHUOC', width: 10, hidden: true},
                {label:'Mã', name: 'ID_DIEUTRI', index: 'ID_DIEUTRI', width: 10, hidden: true},
                {label:'Mã', name: 'STT_BENHAN', index: 'STT_BENHAN', width: 10, hidden: true},
                {label:'Mã', name: 'STT_DOTDIEUTRI', index: 'STT_DOTDIEUTRI', width: 10, hidden: true},
                {label:'Mã', name: 'STT_DIEUTRI', index: 'STT_DIEUTRI', width: 10, hidden: true},
                {label:'Mã', name: 'STT_TOATHUOC', index: 'STT_TOATHUOC', width: 10, hidden: true},
                {label:'Mã', name: 'SOVAOVIEN', index: 'SOVAOVIEN', width: 10, hidden: true},
                {label:'Mã', name: 'LOAI', index: 'LOAI', width: 10, hidden: true},
                {label:'Mã', name: 'MAVATTU', index: 'MAVATTU', width: 10},
                {label:'Tên thuốc', name: 'TENVATTU', index: 'TENVATTU', width: 200},
                {label:'Hoạt chất', name: 'HOATCHAT', index: 'HOATCHAT', width: 200},
                {label:'Số lượng', name: 'SOLUONGTHUOC', index: 'SOLUONGTHUOC', width: 50},
                {label:'SL pha', name: 'SOLUONGPHA', index: 'SOLUONGPHA', width: 50},
            ],
            sortorder: "asc",
            rowNum: 100000,
            ignoreCase: true
        });
    });
}

function createFromPhaThuoc_ChiTiet(){
    Formio.createForm(document.getElementById('formPhathuoc_ChiTiet'),
        {
            "display": "form",
            "components": [
                {
                    "label": "Columns",
                    "columns": [
                        {
                            "components": [
                                {
                                    "label": "Tên thuốc pha",
                                    "applyMaskOn": "change",
                                    "customClass": "mr-2",
                                    "tableView": true,
                                    "validate": {
                                        "required": true
                                    },
                                    "key": "phathuoc_tenthuocpha",
                                    "type": "textfield",
                                    "input": true,
                                    "tooltip": "Tên thuốc pha sẽ hiển thị trong truyền dịch"
                                }
                            ],
                            "width": 8,
                            "offset": 0,
                            "push": 0,
                            "pull": 0,
                            "size": "md",
                            "currentWidth": 8
                        },
                        {
                            "components": [
                                {
                                    "label": "Số lô",
                                    "applyMaskOn": "change",
                                    "tableView": true,
                                    "validate": {
                                        "required": true
                                    },
                                    "key": "phathuoc_solo",
                                    "type": "textfield",
                                    "input": true
                                }
                            ],
                            "width": 4,
                            "offset": 0,
                            "push": 0,
                            "pull": 0,
                            "size": "md",
                            "currentWidth": 4
                        }
                    ],
                    "customClass": "mr-0 ml-0",
                    "key": "columns1",
                    "type": "columns",
                    "input": false,
                    "tableView": false
                },
                {
                    "label": "Columns",
                    "columns": [
                        {
                            "components": [
                                {
                                    "label": "Ghi chú",
                                    "applyMaskOn": "change",
                                    "autoExpand": false,
                                    "tableView": true,
                                    "validate": {
                                        "required": true
                                    },
                                    "customClass": "mr-2",
                                    "key": "phathuoc_ghichu",
                                    "type": "textarea",
                                    "input": true,
                                    "tooltip": "Ghi chú sẽ hiển thị trong tờ điều trị"
                                },
                            ],
                            "width": 9,
                            "size": "md",
                        },
                        {
                            "components": [
                                {
                                    "label": "DT/SL sau pha",
                                    "applyMaskOn": "change",
                                    "tableView": true,
                                    "validate": {
                                        "required": true
                                    },
                                    "customClass": "mr-2",
                                    "key": "phathuoc_dungtichsaupha",
                                    "type": "textfield",
                                    "input": true
                                }
                            ],
                            "width": 2,
                            "size": "md",
                        },
                        {
                            "components": [
                                {
                                    "label": "ĐVT",
                                    "applyMaskOn": "change",
                                    "tableView": true,
                                    "validate": {
                                        "required": true
                                    },
                                    "key": "phathuoc_donvitinh",
                                    "type": "textfield",
                                    "input": true
                                }
                            ],
                            "width": 1,
                            "size": "md",
                        }
                    ],
                    "customClass": "mr-0 ml-0",
                    "key": "columns1",
                    "type": "columns",
                    "input": false,
                    "tableView": false
                },
                {
                    "label": "Columns",
                    "columns": [
                        {
                            "components": [
                                JSONSelect("Y lệnh", "YLENH", "pr-2", [
                                    {
                                        "label": "Thuốc",
                                        "value": 'thuoc'
                                    },
                                    {
                                        "label": "Truyền dịch",
                                        "value": 'truyendich'
                                    },
                                ], {}, {
                                    "defaultValue": "thuoc",
                                    "validate": {
                                        "required": true
                                    },
                                }),
                            ],
                            "width": 2,
                            "size": "md",
                        },
                        {
                            "components": [
                                JSONDateTime("Ngày y lệnh", "NGAY_Y_LENH", "pr-2", "dd/MM/yyyy HH:mm",
                                    true, moment(todieutriObject.NGAYGIOLAPTDT, ['DD/MM/YYYY HH:mm']).format("YYYY-MM-DD HH:mm"),
                                    null, {}, {
                                        "validate": {
                                            "required": true
                                        },
                                    }),
                            ],
                            "width": 2,
                            "size": "md",
                        },
                        {
                            "components": [
                                JSONNumber("Số lần", "phathuoc_solan", "pr-2", {}, {
                                    "validate": {
                                        "required": true,
                                        "min": 1,
                                    },
                                    "defaultValue": 1
                                }),
                            ],
                            "width": 1,
                            "size": "md",
                        },
                        {
                            "components": [
                                JSONNumber("Cách giờ", "phathuoc_cachgio", "pr-2", {}, {
                                    "validate": {
                                        "required": true,
                                        "min": 0,
                                    },
                                    "defaultValue": 0
                                }),
                            ],
                            "width": 1,
                            "size": "md",
                        },
                        {
                            "components": [
                                JSONSelect("Tốc độ", "LOAITOCDO", "pr-2", [
                                    {
                                        "label": "Giọt/phút",
                                        "value": 'giot/phut'
                                    },
                                    {
                                        "label": "ml/giờ",
                                        "value": 'ml/gio'
                                    },
                                ], {}, {
                                    "defaultValue": "giot/phut",
                                    "validate": {
                                        "required": true
                                    },
                                    "customConditional": "show = (data.YLENH == 'truyendich' ? 1 : 0);",
                                }),
                            ],
                            "width": 2,
                            "size": "md",
                        },
                        {
                            "components": [
                                JSONNumber("&nbsp;", "TOCDO", "pr-2", {}, {
                                    "validate": {
                                        "required": true,
                                        "min": 1,
                                    },
                                    "customConditional": "show = (data.YLENH == 'truyendich' ? 1 : 0);",
                                }),
                            ],
                            "width": 1,
                            "size": "md",
                        },
                        {
                            "components": [
                                JSONNumber("Số lượng truyền", "SOLUONGTRUYEN", "pr-2", {}, {
                                    "validate": {
                                        "required": true,
                                        "min": 0.001,
                                    },
                                    "customConditional": "show = (data.YLENH == 'truyendich' ? 1 : 0);",
                                }),
                            ],
                            "width": 2,
                            "size": "md",
                        },
                        {
                            "components": [
                                JSONTextField("Liều dùng", "LIEUDUNG", "", {}, {
                                    "customConditional": "show = (data.YLENH == 'truyendich' ? 1 : 0);",
                                }),
                            ],
                            "width": 1,
                            "size": "md",
                        },
                    ],
                    "customClass": "mr-0 ml-0",
                    "key": "columns1",
                    "type": "columns",
                    "input": false,
                    "tableView": false
                },
            ]
        }
    ).then(function(form) {
        formphathuoc_chitiet = form;
    });
}
function checkboxPhaThuocFormatter(cellvalue, options, rowObject) {
    return "<input type='checkbox' name='checkboxListDangPha' id='checkboxListDangPha" + options.rowId + "' onchange='themThuocPha(" + options.rowId + ");'>";
}
function themThuocPha(id, tenthuoc = ""){
    bienPhaThuoc = true;
    var arrTemp = formphathuoc.submission.data.phathuoc_danhsachthuocpha;
    if(arrIdGridDangPha.indexOf(Number(id)) == "-1"){
        var ret = $("#list_phathuoc_dangpha").jqGrid('getRowData', id);
        // if (ret.DUNGTICH_TONG != 0 && Number(ret.DUNGTICH_TONG ) <= Number(ret.DUNGTICH_SUDUNG)) {
        //     $("#checkboxListDangPha" + id).prop("checked", false);
        //     notifiToClient("Red", "Thuốc" + tenthuoc + " đã được sử dụng hết");
        //     bienCopyPhaThuoc = false;
        //     ngungCopyPha = true;
        //     return;
        // }
        arrIdGridDangPha.push(Number(id));
        var stringIdBtn = "active-edit-dungtich-" + ret.MAVATTU + ret.ID_DIEUTRI + ret.STT_TOATHUOC + ret.LOAI;
        var objectTemp = {
            id_griddangpha: id,
            phathuoc_mavattu: ret.MAVATTU,
            phathuoc_hoatchat: ret.HOAT_CHAT,
            phathuoc_dvt: ret.DVT,
            phathuoc_solo_ct: ret.SOLOSANXUAT,
            phathuoc_tendichtruyen: ret.TEN_VAT_TU,
            phathuoc_dungtich: ret.DUNGTICH_DICHTRUYEN,
            phathuoc_soluongthuoc_max: ret.SO_LUONG_THUC_LINH,
            phathuoc_soluongthuoc: ret.SO_LUONG_THUC_LINH,
            phathuoc_soluongtong: ret.DUNGTICH_TONG,
            // phathuoc_soluongpha: Number(ret.DUNGTICH_TONG) - Number(ret.DUNGTICH_SUDUNG),
            phathuoc_soluongpha: Number(ret.DUNGTICH_TONG),
            // phathuoc_soluongdasudung: Number(ret.DUNGTICH_SUDUNG),
            phathuoc_soluongdasudung: 0,
            phathuoc_loai: ret.LOAI,
            phathuoc_id_dieutri: ret.ID_DIEUTRI,
            phathuoc_stt_toathuoc: ret.STT_TOATHUOC,
            phathuoc_nghiepvu: ret.NGHIEP_VU,
            phathuoc_ngay_yl: ret.NGAY_YL,
            phathuoc_dvtnhonhat: ret.DVTNHONHAT,
            htmlBtnThuoc: '<label for="'+stringIdBtn+'" class="col-form-label  field-required" ref="label">DT/SL</label>' +
                '<button class="btn btn-default w-100 line-height-1 text-left active-edit-dungtich" id="'+stringIdBtn+'" type="button">' + ret.DUNGTICH_DICHTRUYEN + ' ' + ret.DVTNHONHAT + '</button>',
            idBtnThuoc: stringIdBtn
        }
        arrTemp.push(objectTemp);
    } else {
        var removeIndex = arrTemp.map(function (item) {
            return Number(item.id_griddangpha);
        }).indexOf(id);
        arrTemp.splice(removeIndex, 1);
        arrIdGridDangPha = arrIdGridDangPha.filter((id1) => id1 !== id)
        if(arrTemp.length == 0){
            createFromPhaThuoc_DanhSachThuoc();
        }
    }
    formphathuoc.submission = {
        data: {
            phathuoc_danhsachthuocpha: arrTemp
        }
    };
}

$(function() {
    $("#action_thuoc_phathuoc").click(function() {
        createFromPhaThuoc_DanhSachThuoc();
        createFromPhaThuoc_ChiTiet();
        reloadGridPhaThuoc_DanhSachThuoc();
        addTextTitleModal("titlePhathuoc", "Pha thuốc");
        initGridDangphathuoc();
        var url = 'cmu_getlist?url=' + convertArray([
            thongtinhsba.thongtinbn.STT_BENHAN,
            0,
            -1,
            singletonObject.dvtt,
            "HSBA_TODIEUTRI_CMU_SEL"
        ]);
        $.get(url).done(function(data){
            if (data && data.length > 0) {
                $("#phathuoc_todieutri").html("");
                for (var i = 0; i < data.length; i++) {
                    if(todieutriObject.ID_DIEUTRI != data[i].ID_DIEUTRI) {
                        $("#phathuoc_todieutri").append("<option value='" + data[i].ID_DIEUTRI + "'>" +
                            "Tờ điều trị " + data[i].STT_DIEUTRI + " - " + data[i].NGAYGIO + " - " + data[i].TEN_NHANVIEN + "</option>");
                    }

                }

                $("#modalPhathuoc").modal("show");
                $("#phathuoc_todieutri").select2({
                    width: '100%',
                    dropdownParent: $("#modalPhathuoc")
                });
                $("#action_dsphathuoclammoi").click();
                $("#list_phathuoc_dangpha").jqGrid('setGridWidth', $('#griddangpha-container').width(), true);
                $("#list_phathuoc_dapha_chitiet").jqGrid('setGridWidth', $('#griddaphachitiet-container').width(), true);
                $("#list_phathuoc_dapha").jqGrid('setGridWidth', $('#griddapha-container').width(), true);
            } else  {
                notifiToClient("Red", "Không có thông tin tờ điều trị");
            }
        });
    });

    $("#action_phathuoc").click(function(){
        showSelfLoading("action_phathuoc");
        var checkylenh = true;
        var ngayylenh = _.get(formphathuoc_chitiet, 'submission.data.NGAY_Y_LENH');
        var message = [];
        if(ngayylenh) {
            formphathuoc.submission.data.phathuoc_danhsachthuocpha.forEach(function(obj){
                if(obj.phathuoc_loai == 'noitru' && moment(moment(ngayylenh).format("DD/MM/YYYY HH:mm"), ["DD/MM/YYYY HH:mm"]).isBefore(
                    moment(obj.phathuoc_ngay_yl, ["DD/MM/YYYY HH:mm"])
                )) {
                    checkylenh = false;
                    message.push("Thuốc: " + obj.phathuoc_tendichtruyen + " - " + obj.phathuoc_ngay_yl);
                }
            })
        }
        if(!checkylenh) {
            notifiToClient("Red", "Thời gian pha thuốc: "+moment(ngayylenh).format("DD/MM/YYYY HH:mm")+" nhỏ hơn ngày y lệnh của các thuốc sau: " + message.join(", "));
            hideSelfLoading("action_phathuoc");
            return;
        }

        if(formphathuoc_chitiet.checkValidity(formphathuoc_chitiet.submission.data) == true && formphathuoc.checkValidity(formphathuoc.submission.data) == true){
            var danhsachthuoc = formphathuoc.submission.data.phathuoc_danhsachthuocpha;
            var phathuocchitiet = formphathuoc_chitiet.submission.data;
            if(danhsachthuoc.length < 2) {
                notifiToClient("Red", "Vui lòng chọn từ 2 thuốc trở lên để pha");
                hideSelfLoading("action_phathuoc");
                return;
            }
            if (phathuocchitiet.phathuoc_solan > 1 && phathuocchitiet.phathuoc_cachgio == 0) {
                notifiToClient("Red", "Thuốc pha nhiều lần 'Cách giờ' phải lớn hơn 0");
                hideSelfLoading("action_phathuoc");
                return;
            }

            // var thuocSai = [];
            // var thongBaoLoi = "";
            // danhsachthuoc.forEach(function(obj){
            //     if(obj.phathuoc_soluongthuoc && obj.phathuoc_dungtich){
            //         obj.phathuoc_soluongtong = obj.phathuoc_dungtich*obj.phathuoc_soluongthuoc;
            //     }
            //
            //     if(Number(obj.phathuoc_soluongdasudung) + (Number(obj.phathuoc_soluongpha) * Number(phathuocchitiet.phathuoc_solan)) > Number(obj.phathuoc_soluongtong)){
            //         thuocSai.push(obj.phathuoc_tendichtruyen);
            //         var dungTichDuocPha = Number(obj.phathuoc_soluongtong) - Number(obj.phathuoc_soluongdasudung);
            //         thongBaoLoi += "- Thuốc " + obj.phathuoc_tendichtruyen + " dung tích tổng: " + obj.phathuoc_soluongtong + ", đã sử dụng: " + obj.phathuoc_soluongdasudung + ", không thể pha lớn hơn: " + dungTichDuocPha + " (hiện tại: " + obj.phathuoc_soluongpha + " * " + phathuocchitiet.phathuoc_solan + " lần)</br>";
            //     }
            // });
            // if(thuocSai.length > 0){
            //     notifiToClient("Red", thongBaoLoi);
            //     hideSelfLoading("action_phathuoc");
            //     return;
            // }

            for (var i = 0; i < phathuocchitiet.phathuoc_solan; i++) {
                var ngayYLenh = moment(phathuocchitiet.NGAY_Y_LENH).add(i*phathuocchitiet.phathuoc_cachgio, 'hours');
                phathuocInsert(ngayYLenh, phathuocchitiet, danhsachthuoc)
            }
        } else {
            // notifiToClient("Red", "Vui lòng nhập đầy đủ thông tin");
            hideSelfLoading("action_phathuoc");
            formphathuoc_chitiet.submitForm();
            formphathuoc.submitForm();
        }
    });

    $("#action_dsphathuoclammoi").click(function() {
        createFromPhaThuoc_DanhSachThuoc();
        var url = "cmu_getlist?url="+convertArray([todieutriObject.SOVAOVIEN, todieutriObject.SOVAOVIEN_DT,
            todieutriObject.ID_DIEUTRI, singletonObject.dvtt, 1,'CMU_THUOC_CHUAPHA_V3']);
        $("#list_phathuoc_dangpha").jqGrid('setGridParam', {datatype: 'json', url: url}).trigger('reloadGrid');
    })

    $("#action_themvaodsphathuoc").click(function() {
        createFromPhaThuoc_DanhSachThuoc();
        $.loadDSThuocdangpha($("#phathuoc_todieutri").val())
    })

    $("#updatephathuocct_luu").click(function () {
        var idButton = this.id
        showSelfLoading(idButton);
        formUpdatePhaThuocCT.emit("checkValidity");
        if (!formUpdatePhaThuocCT.checkValidity(null, false, null, true)) {
            hideSelfLoading(idButton);
            return;
        }

        var actionUrl;
        var url;
        var dataSubmit = formUpdatePhaThuocCT.submission.data;
        $.get("cmu_list_CMU_CHECKTHOIGIANPHATHUOC?url="+
            convertArray([
                singletonObject.dvtt,
                thongtinhsba.thongtinbn.SOVAOVIEN,
                formUpdatePhaThuocCT.submission.data.ID_PHATHUOC,
                moment(dataSubmit.NGAY_Y_LENH).format("DD/MM/YYYY HH:mm")
            ]))
            .done(function(data){
                if(data.length > 0){
                    var message = [];
                    data.forEach(function(obj){
                        message.push(obj.TEN_VAT_TU + ' - ' + obj.NGAY_YL);
                    })
                    notifiToClient("Red", "Thời gian pha thuốc: "+moment(dataSubmit.NGAY_Y_LENH).format("DD/MM/YYYY HH:mm")
                        +" nhỏ hơn ngày y lệnh của các thuốc sau: " +message.join(", "));
                    hideSelfLoading(idButton);
                } else {
                    actionUrl = "cmu_post";
                    url = [
                        dataSubmit.ID_PHATHUOC,
                        singletonObject.dvtt,
                        dataSubmit.TENTHUOCPHA,
                        dataSubmit.GHICHU,
                        moment(dataSubmit.NGAY_Y_LENH).format("DD/MM/YYYY HH:mm"),
                        dataSubmit.YLENH,
                        dataSubmit.LOAITOCDO,
                        dataSubmit.TOCDO,
                        dataSubmit.SOLUONGTRUYEN,
                        dataSubmit.LIEUDUNG,
                        "CMU_PHATHUOCCT_UPDATE_V2"
                    ];

                    $.post(actionUrl, {
                        url: url.join('```')
                    }).done(function (data) {
                        if(data > 0){
                            notifiToClient("Green",MESSAGEAJAX.SUCCESS);
                            $("#modalFormUpdatePhaThuocCT").modal("hide");
                        } else {
                            notifiToClient("Red",MESSAGEAJAX.ERROR);
                        }
                    }).fail(function(error) {
                        notifiToClient("Red",MESSAGEAJAX.ERROR);
                    }).always(function() {
                        hideSelfLoading("updatephathuocct_luu");
                        reloadGridPhaThuoc_DanhSachThuoc();
                    });
                }
            }).fail(function() {
            notifiToClient("Red", MESSAGEAJAX.ERROR)
            hideSelfLoading(idButton);
        })

    });

    $("#phathuocct_mau").click(function() {
        var element = $("#mau_danhsachmaujson_wrap");
        element.attr("function-add", 'insertMauHSBAPHATHUOCCT');
        element.attr("function-chinhsua", 'editMauHSBAPHATHUOCCT');
        element.attr("function-select", 'selectMauHSBAPHATHUOCCT');
        element.attr("function-getdata", 'getdataMauHSBAPHATHUOCCT');
        element.attr("function-validate", 'formioHSBAPHATHUOCCTValidate');
        element.attr("data-key", 'MAUPHATHUOCCT');
        $("#modalMauChungJSON").modal("show");
        $.loadDanhSachMauChungJSON('MAUPHATHUOCCT')
    })

    $.extend({
        loadDSThuocdangpha: function(ID_DIEUTRI){
            var list = $("#list_phathuoc_dangpha");
            var dataGrid = list.jqGrid('getGridParam','data');
            $.get("cmu_getlist?url="+convertArray([todieutriObject.SOVAOVIEN, todieutriObject.SOVAOVIEN_DT,
                ID_DIEUTRI, singletonObject.dvtt, 0,'CMU_THUOC_CHUAPHA_V3'])).done(function(data){
                $.each(data, function(index, item2) {
                    var exists = dataGrid.some(function(item1) {
                        return item1.ID_DIEUTRI === item2.ID_DIEUTRI && item1.STT_TOATHUOC === item2.STT_TOATHUOC && item1.LOAI == item2.LOAI;
                    });

                    if (!exists) {
                        dataGrid.push(item2);
                    }
                });
                list[0].grid.beginReq();
                list.jqGrid("clearGridData");
                list.jqGrid('setGridParam', { data: dataGrid });
                list[0].grid.endReq();
                list.trigger('reloadGrid');
            })
        },
        insertMauHSBAPHATHUOCCT: function () {
            generateFormMauPHATHUOCCT({})
        },
        editMauHSBAPHATHUOCCT: function (rowSelect) {
            var json = JSON.parse(rowSelect.NOIDUNG);
            var dataMau = {}
            json.forEach(function(item) {
                dataMau[item.key] = item.value
            })

            generateFormMauPHATHUOCCT({
                ID: rowSelect.ID,
                TENMAU: rowSelect.TENMAU,
                ...dataMau
            })
        },
        selectMauHSBAPHATHUOCCT: function (rowSelect) {
            var json = JSON.parse(rowSelect.NOIDUNG);
            var dataMau = {
                ...formphathuoc_chitiet.submission.data,
                ...formphathuoc.submission.data.phathuoc_danhsachthuocpha,
                phathuoc_tenthuocpha: json.phathuoc_ghichu
            }
            var dsThuoc = [];
            json.forEach(function(item) {
                if(item.key == "danhsachthuoc") {
                    dsThuoc = item.value
                } else if(item.key == 'phathuoc_ghichu') {
                    dataMau[item.key] = item.value
                    dataMau['phathuoc_tenthuocpha'] = item.value
                } else {
                    dataMau[item.key] = item.value
                }
            })
            var mavt = []
            var soluongPha = {};
            dsThuoc.forEach(function(item) {
                mavt.push(item.MAVATTU+'')
                soluongPha[item.MAVATTU] = item.SOLUONGPHA
            })
            var rowIds = $("#list_phathuoc_dangpha").jqGrid('getDataIDs');
            var arrTemp = [];
            var soloPha = []
            rowIds.forEach(function(rowId) {
                var checkbox = document.getElementById("checkboxListDangPha" + rowId);
                var rowData2 = $("#list_phathuoc_dangpha").jqGrid('getRowData', rowId);
                if (mavt.indexOf(rowData2.MAVATTU) > -1) {
                    checkbox.checked = true;
                    themThuocPha(rowId);
                    soloPha.push(rowData2.SOLOSANXUAT)
                    var objectTemp;
                    objectTemp = {
                        phathuoc_mavattu: rowData2.MAVATTU,
                        phathuoc_hoatchat: rowData2.HOAT_CHAT,
                        phathuoc_dvt: rowData2.DVT,
                        phathuoc_solo_ct: rowData2.SOLOSANXUAT,
                        phathuoc_tendichtruyen: rowData2.TEN_VAT_TU,
                        phathuoc_dungtich: rowData2.DUNGTICH_DICHTRUYEN,
                        phathuoc_soluongthuoc_max: rowData2.SO_LUONG_THUC_LINH,
                        phathuoc_soluongthuoc: rowData2.SO_LUONG_THUC_LINH,
                        phathuoc_soluongtong: rowData2.DUNGTICH_TONG,
                        // phathuoc_soluongpha: soluongPha[rowData2.MAVATTU] ? soluongPha[rowData2.MAVATTU] :  Number(rowData2.DUNGTICH_TONG) - Number(rowData2.DUNGTICH_SUDUNG),
                        phathuoc_soluongpha: soluongPha[rowData2.MAVATTU] ? soluongPha[rowData2.MAVATTU] :  Number(rowData2.DUNGTICH_TONG),
                        // phathuoc_soluongdasudung: Number(rowData2.DUNGTICH_SUDUNG),
                        phathuoc_soluongdasudung: 0,
                        phathuoc_loai: rowData2.LOAI,
                        phathuoc_id_dieutri: rowData2.ID_DIEUTRI,
                    }

                    arrTemp.push(objectTemp);

                }
            })
            bienPhaThuoc = false;
            formphathuoc.submission.data.phathuoc_danhsachthuocpha = []

            formphathuoc.submission = {
                data: {
                    phathuoc_danhsachthuocpha: arrTemp
                }
            };
            console.log("dataMau", dataMau)
            formphathuoc_chitiet.submission = {
                data: {
                    ...dataMau,
                    phathuoc_solo: soloPha.join(",")
                }
            }
            if (ngungCopyPha){
                arrIdGridDangPha = []
                formphathuoc.submission = {
                    data: {
                        phathuoc_danhsachthuocpha: []
                    }
                };
                formphathuoc_chitiet.submission = {
                    data: {}
                }
            }
            ngungCopyPha = false;


            $("#modalMauChungJSON").modal("hide");
        },
        getdataMauHSBAPHATHUOCCT: function () {
            var objectNoidung = [];
            getObjectMauPHATHUOCCT([]).forEach(function(item) {
                if(item.key != 'ID' && item.key != 'TENMAU') {
                    objectNoidung.push({
                        "label": item.label,
                        "value": _.get(formioMauHSBA.submission.data, item.key, ''),
                        "key": item.key,
                    })
                }
            })
            return {
                ID: formioMauHSBA.submission.data.ID,
                TENMAU: formioMauHSBA.submission.data.TENMAU,
                NOIDUNG: JSON.stringify(objectNoidung),
                KEYMAUCHUNG: 'MAUPHATHUOCCT'
            };
        },
        formioHSBAPHATHUOCCTValidate: function() {
            formioMauHSBA.emit("checkValidity");
            if (!formioMauHSBA.checkValidity(null, false, null, true)) {
                return false;
            }
            return true;
        },
    })

    function generateFormMauPHATHUOCCT(dataForm) {
        $.get("cmu_getlist?url="+convertArray([singletonObject.dvtt, 'CMU_GETDSTHUOCMAU'])).done(function(data) {
            var mapData = data.map(function(item) {
                return {
                    label: item.MAVATTU + ' - '+ item.TENVATTU + ' '+ item.HAMLUONG + ' (' + item.HOATCHAT + ')',
                    value: item.MAVATTU
                }
            })
            Formio.createForm(document.getElementById('formChiTietMauChungJSON'),
                {
                    "display": "form",
                    "components": getObjectMauPHATHUOCCT(mapData)
                },{}
            ).then(function(form) {
                formioMauHSBA = form;
                formioMauHSBA.submission = {
                    data: {
                        ...dataForm
                    }
                }
            });
        })

    }
    function getObjectMauPHATHUOCCT(data) {

        return [
            {
                "label": "ID",
                "key": "ID",
                "type": "textfield",
                hidden: true
            },
            {
                "label": "Tên mẫu",
                "key": "TENMAU",
                "type": "textarea",
                validate: {
                    required: true
                },
                others: {
                    "labelPosition": "left-left",
                    "labelWidth": 10
                }
            },
            {
                "label": "Danh sách thuốc",
                "tableView": false,
                "rowDrafts": false,
                "key": "danhsachthuoc",
                "type": "editgrid",
                "displayAsTable": false,
                "input": true,
                "components": [
                    {
                        "label": "Columns",
                        "columns": [

                            {
                                "components": [
                                    {
                                        "label": "Thuốc",
                                        "key": "MAVATTU",
                                        "type": "select",
                                        "widget": "choicesjs",
                                        customClass: "pr-2",
                                        validate: {
                                            required: true
                                        },
                                        "data": {
                                            "values": [
                                                ...data
                                            ]
                                        }
                                    }
                                ],
                                "width": 9,
                                "size": "md"
                            },
                            {
                                "components": [
                                    {
                                        "label": "Số lượng pha",
                                        "key": "SOLUONGPHA",
                                        "type": "number",
                                        customClass: "pr-2",
                                        validate: {
                                            required: true,
                                            min: 0
                                        }
                                    }
                                ],
                                "size": "md",
                                "width": 3
                            },

                        ],
                        "key": "columns",
                        "type": "columns",
                    }
                ],
                "templates": {
                    "header": "<div class=\"row\">\n      {% util.eachComponent(components, function(component) { %}\n        {% if (component.key == 'MAVATTU') { %}\n          <div class=\"col-sm-9\">{{ t(component.label) }}</div>\n        {% } %}\n        {% if (component.key == 'SOLUONGPHA') { %}\n          <div class=\"col-sm-2\">{{ t(component.label) }}</div>\n        {% } %}\n             {% }) %}\n    </div>",
                    "row": "<div class=\"row\">\n      {% util.eachComponent(components, function(component) { %}\n        {% if (component.key == 'MAVATTU') { %}\n          <div class=\"col-sm-9\">\n            {{ isVisibleInRow(component) ? getView(component, row[component.key]) : ''}}\n          </div>\n        {% } %}\n        {% if ( component.key == 'SOLUONGPHA') { %}\n          <div class=\"col-sm-2\">\n            {{ isVisibleInRow(component) ? getView(component, row[component.key]) : ''}}\n          </div>\n        {% } %}\n      {% }) %}\n      {% if (!instance.options.readOnly && !instance.disabled) { %}\n        <div class=\"col-sm-1\">\n          <div class=\"btn-group pull-right\">\n            <button class=\"btn btn-default btn-primary btn-sm editRow\"><i class=\"fa fa-pencil-square-o\"></i></button>\n            {% if (!instance.hasRemoveButtons || instance.hasRemoveButtons()) { %}\n              <button class=\"btn btn-danger btn-sm removeRow\"><i class=\"fa fa-trash-o\"></i></button>\n            {% } %}\n          </div>\n        </div>\n      {% } %}\n    </div>"
                },
                "addAnother": "Thêm mới",
                "saveRow": "Lưu",
                "removeRow": "Hủy",
            },
            {
                "label": "Ghi chú",
                "key": "phathuoc_ghichu",
                "type": "textarea",
                others: {
                    "labelPosition": "left-left",
                    "labelWidth": 10
                }
            },
            {
                "label": "Y lệnh",
                "key": "YLENH",
                "type": "select",
                "data": {
                    "values": [
                        {
                            "label": "Thuốc",
                            "value": "thuoc"
                        },
                        {
                            "label": "Truyền dịch",
                            "value": "truyendich"
                        },
                    ]
                },
                "labelPosition": "left-left",
                "labelWidth": 10
            },
            {
                "label": "Tốc độ",
                "customClass": "pr-2",
                "key": "LOAITOCDO",
                "type": "select",
                "data": {
                    "values": [
                        {
                            "label": "Giọt/phút",
                            "value": 'giot/phut'
                        },
                        {
                            "label": "ml/giờ",
                            "value": 'ml/gio'
                        },
                    ]
                },
                "defaultValue": "giot/phut",
                "customConditional": "show = (data.YLENH == 'truyendich' ? 1 : 0);",
                "labelPosition": "left-left",
                "labelWidth": 10
            },
            {
                "label": "Tốc độ truyền",
                "customClass": "pr-2",
                "key": "TOCDO",
                "type": "textfield",
                "validate": {
                    "required": false
                },
                "min": 0.1,
                "customConditional": "show = (data.YLENH == 'truyendich' ? 1 : 0);",
                "labelPosition": "left-left",
                "labelWidth": 10
            },
            {
                "label": "Số lượng truyền (ml)",
                "customClass": "pr-2",
                "key": "SOLUONGTRUYEN",
                "type": "textfield",
                "validate": {
                    "required": false
                },
                "min": 0.01,
                "customConditional": "show = (data.YLENH == 'truyendich' ? 1 : 0);",
                "labelPosition": "left-left",
                "labelWidth": 10
            },
            {
                "label": "Liều dùng",
                "customClass": "pr-2",
                "key": "LIEUDUNG",
                "type": "textfield",
                "validate": {
                    "required": false
                },
                // "min": 0.01,
                "customConditional": "show = (data.YLENH == 'truyendich' ? 1 : 0);",
                "labelPosition": "left-left",
                "labelWidth": 10
            },
        ];
    }

    function initGridDangphathuoc() {
        var list = $("#list_phathuoc_dangpha");
        if(!list[0].grid) {
            list.jqGrid({
                url: "",
                datatype: "local",
                loadonce: true,
                height: 200,
                width: '100%',
                shrinkToFit: true,
                colModel: [
                    {label:' ', name: 'CHECKBOX', index: 'CHECKBOX',key: true,width:20,editable:true, edittype:'checkbox',formatter: checkboxPhaThuocFormatter, editoptions: {value:"True:False"}
                    },
                    {label:'TĐT', name: 'STT_DIEUTRI', index: 'STT_DIEUTRI', width: 50},
                    {label:'Mã', name: 'MAVATTU', index: 'MAVATTU', width: 50, hidden: true},
                    {label:'Tên thương mại', name: 'TEN_VAT_TU', index: 'TEN_VAT_TU', width: 150,
                        cellattr: function(rowId, val, rawObject, cm, rdata) {
                            var colorBackground = '';
                            if(rawObject.LOAI == 'noitru') {
                                colorBackground = 'background-color: #0bff00;';
                            } else if (rawObject.LOAI == 'muangoai') {
                                colorBackground = 'background-color: #ffac00;';
                            } else if (rawObject.LOAI == 'phathuoc') {
                                colorBackground = 'background-color: #ff0000;';
                            }
                            return 'style="' + colorBackground + '"';
                        }
                    },
                    {label:'Tên gốc,Hoạt chất', name: 'HOAT_CHAT', index: 'HOAT_CHAT', width: 150},
                    {label:'Hàm lượng', name: 'HAMLUONG', index: 'HAMLUONG', width: 60},
                    {label:'SL', name: 'SO_LUONG_THUC_LINH', index: 'SO_LUONG_THUC_LINH', width: 30},
                    {label:'DT.Tổng', name: 'DUNGTICH_TONG', index: 'DUNGTICH_TONG', width: 50,
                        formatter: function (cellvalue, options, rowObject) {
                            return Number(rowObject.DUNGTICH_DICHTRUYEN) * Number(rowObject.SO_LUONG_THUC_LINH);
                        }
                    },
                    // {label:'DT.Sử dụng', name: 'DUNGTICH_SUDUNG', index: 'DUNGTICH_SUDUNG', width: 50},

                    {label:'ĐVT', name: 'DVT', index: 'DVT', width: 50},
                    {label:'Dung tích', name: 'DUNGTICH_DICHTRUYEN', index: 'DUNGTICH_DICHTRUYEN', width: 10},
                    {label:'Số lô', name: 'SOLOSANXUAT', index: 'SOLOSANXUAT', width: 10, hidden: true},
                    {label:'LOAI', name: 'LOAI', index: 'LOAI', width: 10, hidden: true},
                    {label:'ID_DIEUTRI', name: 'ID_DIEUTRI', index: 'ID_DIEUTRI', width: 10, hidden: true},
                    {label:'Mã', name: 'STT_TOATHUOC', index: 'STT_TOATHUOC', width: 10, hidden: true},
                    {label:'NGAY_YL', name: 'NGAY_YL', index: 'NGAY_YL', width: 10, hidden: true},
                    {label:'NGHIEP_VU', name: 'NGHIEP_VU', index: 'NGHIEP_VU', width: 50, hidden: true},
                    {label:'DVTNHONHAT', name: 'DVTNHONHAT', index: 'DVTNHONHAT', width: 50, hidden: true},
                ],
                sortorder: "asc",
                rowNum: 100000,
                ignoreCase: true,
            });
            list.jqGrid('filterToolbar', {
                defaultSearch: "cn",
                stringResult: true,
                searchOnEnter: false,
                ignoreCase: true
            });
        } else {
            list.jqGrid("clearGridData");
        }

    }

    function phathuocInsert(ngayYLenh, phathuocchitiet, danhsachthuoc) {
        try {
            $.post("cmu_post", {
                url: [thongtinhsba.thongtinbn.DVTT,
                    todieutriObject.ID_DIEUTRI,
                    todieutriObject.STT_BENHAN,
                    todieutriObject.STT_DOTDIEUTRI,
                    todieutriObject.STT_DIEUTRI,
                    todieutriObject.SOVAOVIEN,
                    phathuocchitiet.phathuoc_tenthuocpha,
                    phathuocchitiet.phathuoc_ghichu,
                    phathuocchitiet.phathuoc_solo,
                    phathuocchitiet.phathuoc_dungtichsaupha,
                    todieutriObject.BACSIDIEUTRI,
                    todieutriObject.NGAYGIO,
                    moment(ngayYLenh).format("DD/MM/YYYY HH:mm"),
                    phathuocchitiet.LOAITOCDO,
                    phathuocchitiet.TOCDO,
                    phathuocchitiet.SOLUONGTRUYEN,
                    phathuocchitiet.LIEUDUNG,
                    phathuocchitiet.YLENH,
                    phathuocchitiet.phathuoc_donvitinh,
                    "CMU_PHATHUOC_INS_V4"
                ].join("```")
            }).done(function (dt) {
                if(dt > 0){
                    var count = 0;
                    for(var i = 0; i < danhsachthuoc.length; i++){
                        $.post("cmu_post", {
                            url: [thongtinhsba.thongtinbn.DVTT,
                                danhsachthuoc[i].phathuoc_id_dieutri,
                                dt,
                                todieutriObject.STT_BENHAN,
                                todieutriObject.STT_DOTDIEUTRI,
                                todieutriObject.STT_DIEUTRI,
                                todieutriObject.SOVAOVIEN,
                                danhsachthuoc[i].phathuoc_mavattu.indexOf('pha') == '-1' ? null : danhsachthuoc[i].phathuoc_mavattu.replace('pha', ''),
                                danhsachthuoc[i].phathuoc_mavattu.indexOf('pha') == '-1' ? danhsachthuoc[i].phathuoc_mavattu : null,
                                danhsachthuoc[i].phathuoc_soluongthuoc,
                                danhsachthuoc[i].phathuoc_soluongpha,
                                danhsachthuoc[i].phathuoc_dungtich,
                                danhsachthuoc[i].phathuoc_tendichtruyen,
                                danhsachthuoc[i].phathuoc_loai,
                                danhsachthuoc[i].phathuoc_stt_toathuoc,
                                "CMU_PHATHUOC_CHITIET_INS_V5"
                            ].join("```")
                        }).done(function (dtct) {
                        });
                    }
                    $.post("cmu_post_CMU_YLENHTHUOC_THEOGIO_INS_V5", {
                        url: [
                            singletonObject.dvtt,
                            todieutriObject.ID_DIEUTRI,
                            todieutriObject.SOVAOVIEN,
                            todieutriObject.SOVAOVIEN_DT,
                            "PHA_" + dt,
                            moment(ngayYLenh).format("DD/MM/YYYY HH:mm"),
                            "",
                            phathuocchitiet.YLENH,
                            phathuocchitiet.LOAITOCDO,
                            phathuocchitiet.TOCDO,
                            phathuocchitiet.SOLUONGTRUYEN,
                            phathuocchitiet.LIEUDUNG,
                            todieutriObject.BACSIDIEUTRI
                        ].join("```")
                    }).done(function(data) {
                        if(data == -1) {
                            notifiToClient("Red", "Thời gian y lệnh đã tồn tại");
                            return;
                        }
                        $.post("cmu_post", {
                            url: [singletonObject.dvtt,
                                thongtinhsba.thongtinbn.STT_BENHAN,
                                todieutriObject.ID_DIEUTRI,
                                todieutriObject.SOVAOVIEN,
                                todieutriObject.STT_DIEUTRI,
                                '-1',
                                dt, // id_phathuoc
                                phathuocchitiet.phathuoc_solo,
                                phathuocchitiet.phathuoc_tenthuocpha,
                                phathuocchitiet.SOLUONGTRUYEN,
                                phathuocchitiet.LOAITOCDO,
                                data, // id_ylenh
                                singletonObject.makhoa,
                                "CMU_TRUYENDICH_INSERT_V2"].join("```")
                        }).done(function(dt) {
                            notifiToClient("Green", "Lưu thành công");
                            reloadGridPhaThuoc_DanhSachThuoc();
                            createFromPhaThuoc_DanhSachThuoc();
                            createFromPhaThuoc_ChiTiet();
                            var dataJSON = formphathuoc_chitiet.submission.data
                            dataJSON.NGAY_Y_LENH = moment(ngayYLenh).format("DD/MM/YYYY HH:mm")
                            dataJSON.soluongphatong = phathuocchitiet.phathuoc_dungtichsaupha
                            var stringLog = []
                            Object.keys(keyLuuLogTruyenDich).forEach(function(key) {
                                stringLog.push(getLabelValueTruyenDich(key, dataJSON))
                            })
                            luuLogHSBATheoBN({
                                SOVAOVIEN: thongtinhsba.thongtinbn.SOVAOVIEN,
                                LOAI: LOGHSBALOAI.TRUYENDICH.PHATHUOC.KEY,
                                NOIDUNGBANDAU: "",
                                NOIDUNGMOI: stringLog.join(";"),
                                USERID: singletonObject.userId,
                                ACTION: LOGHSBAACTION.INSERT.KEY,
                            })
                            $("#action_thuoc_phathuoc").click();
                        }).fail(function() {
                        })
                    }).fail(function(err) {
                        notifiToClient("Red", "Lỗi thêm y lệnh")
                    });
                }
            }).fail(function() {
                notifiToClient("Red", "Lưu không thành công");
            }).always(function() {
                hideSelfLoading("action_phathuoc");
            });
        } catch (e) {
            hideSelfLoading("action_phathuoc");
            notifiToClient("Red", "Lỗi phát sinh, vui lòng thử lại");
        }
    }
});

