//KIEMTRATHEBHYT
function fillDulieudoituongthe(data, madt){
    var arr = data.toString().split(":");
    $("#tthc_sobhyt_chuoind").val(madt);
    $("#tthc_sobhyt_doituong").val(arr[0]);
    $("#tthc_bhyt_tilebhyt").val(arr[1]);
}
function fillDulieunoidangkykcb(data){
    $("#tthc_bhyt_noidkkcb").val(data);
}

function loadDSHuyenTT30(idTinh, idHuyen){
    console.log('huyen', idTinh, idHuyen)
    var resData = $.ajax({
        url: "cmu_list_DS_HUYEN_TT30?url="+convertArray([idTinh ? idTinh : ""]),
        type: "GET",
        async: false,
    }).responseText;
    initSelect2IfnotIntance('tthc_mahuyen30', JSON.parse(resData), 'MA_HUYEN', 'TEN_HUYEN', 0, 1, idHuyen, 1);
}
function loadDSXaTT30(idHuyen, idXa){
    console.log('xa', idHuyen, idXa)
    var resData = $.ajax({
        url: "cmu_list_DS_XA_TT30?url="+convertArray([idHuyen ? idHuyen : ""]),
        type: "GET",
        async: false,
    }).responseText;
    initSelect2IfnotIntance('tthc_maxa30', JSON.parse(resData), 'MA_XA', 'TEN_XA', 0, 1, idXa, 1);
}

$(function() {

    var objectBHXHCMU = null;

    $("#tthc-edit").click(function() {
        initFormData()
        showformtntt();
        $("#modalFormthongtinhanhchinh").modal("show");
    })
    $("#tthc_sobhyt").keypress(function(e) {
        var sobhyt = $(this).val().trim();
        var hoten = $("#tthc_hoten").val().trim();
        var namsinh = $("#tthc_namsinh").val();
        if (e.keyCode == 13) {
            if (hoten.length == 0){
                return notifiToClient("Red", "Họ tên không để trống");
            }
            if (namsinh.length == 0){
                return notifiToClient("Red", "Năm sinh không để trống");
            }
            if(sobhyt.length == 10){
                var chiNamSinh = $("#tthc_chilaynamsinh").prop('checked') == true ? "1" : "0"
                kiemTraTheBhytCv1666(sobhyt, hoten, namsinh, chiNamSinh, "tthc_bhyt_wrap", function(data) {
                    $("#tthc_bhyt_ngay5nam").val(data.ngayDu5Nam);
                    $("#tthc_hoten").val(data.hoTen);
                    $("#tthc_namsinh").val(data.ngaySinh);
                    $("#tthc_diachi").val(data.diaChi);
                    $("#tthc_gioitinh").val((data.gioiTinh == "Nam" ? "1" : "0"));
                    $("#tthc_bhyt_manoidkkcb").val(data.maDKBD);
                    $("#tthc_bhyt_tungay").val(data.gtTheTu);
                    $("#tthc_bhyt_denngay").val(data.gtTheDen);
                    $("#tthc_bhyt_makhuvuc").val(data.maKV);
                    $("#tthc_chilaynamsinh").prop("checked", (data.hinhthinamsinh == "1" ? true : false));
                    $("#tthc_sobhyt").val(data.maThe);
                    var sobhyt = data.maThe;
                    var madt = sobhyt.substring(0, 3).toUpperCase();
                    kiemTraDoituongTheBhyt(madt, fillDulieudoituongthe);
                    layTennoidangkykcb(data.maDKBD, fillDulieunoidangkykcb);
                })
                return false;
            }
            if(sobhyt.length == 15){
                kiemTraDoituongTheBhyt(sobhyt.substring(0, 3), fillDulieudoituongthe)
                $("#tthc_bhyt_tungay").focus();
            }

        }
    })
    $("#tthc_bhyt_kiemtrathe").click(function() {
        var buttonId = this.id;
        showSelfLoading(buttonId)
        var thongtin = {
            makcbbd : $("#tthc_bhyt_manoidkkcb").val(),
            magioitinh : $("#tthc_gioitinh").val(),
            ngaysinh_nhap : $("#tthc_namsinh").val(),
            hoten_nhap : $("#tthc_hoten").val().trim(),
            sothebhyt : $("#tthc_sobhyt").val().trim(),
            ngaybatdau : $("#tthc_bhyt_tungay").val(),
            ngayketthuc : $("#tthc_bhyt_denngay").val(),
            manoidoituong : $("#tthc_bhyt_makhuvuc").val(),
            diachi: $("#tthc_diachi").val(),
            thoigian_du5namlientuc: $("#tthc_bhyt_ngay5nam").val()
        };
        objectBHXHCMU = null;
        cmuKiemtrathongtuyen(thongtin, buttonId, function(dataCheck) {
            $("#tthc_footer_kiemtrathe .btn-laythongtin").hide()
            $("#tthc_bhyt_laythongtin").show()
            objectBHXHCMU = dataCheck
        });
    });

    $("#tthc_bhyt_laythongtin").click(function() {
        if(objectBHXHCMU != null) {
            var tungayDiv = $("#tthc_bhyt_tungay")
            var denngayDiv = $("#tthc_bhyt_denngay")
            if(objectBHXHCMU.maKetQua == '003') {
                tungayDiv.val(objectBHXHCMU.gtTheTuMoi)
                denngayDiv.val(objectBHXHCMU.gtTheDenMoi)
            } else if(objectBHXHCMU.maKetQua == '004') {
                tungayDiv.val(objectBHXHCMU.gtTheTu)
                denngayDiv.val(objectBHXHCMU.gtTheDen)
                if (objectBHXHCMU.gtTheDenMoi != null) {
                    var nammoi = objectBHXHCMU.gtTheDenMoi.split('/')[2];
                    var namcu = objectBHXHCMU.gtTheDen.split('/')[2];
                    var namcuTu = objectBHXHCMU.gtTheTu.split('/')[2];
                    var gtTheDen = new Date(convertStr_MysqlDate(objectBHXHCMU.gtTheDen))
                    var gtTheTuMoi = new Date(convertStr_MysqlDate(objectBHXHCMU.gtTheTuMoi))
                    if(nammoi == namcu && namcu == namcuTu && (gtTheTuMoi-gtTheDen)/(24*60*60*1000) == 1) {
                        tungayDiv.val(objectBHXHCMU.gtTheTu)
                        denngayDiv.val(objectBHXHCMU.gtTheDenMoi)
                    }
                }
            } else {
                tungayDiv.val(objectBHXHCMU.gtTheTu)
                denngayDiv.val(objectBHXHCMU.gtTheDen)
            }
            if(objectBHXHCMU.ngaySinh.length == 4) {
                $("#tthc_namsinh").val('01/01/'+objectBHXHCMU.ngaySinh)
                $("#tthc_chilaynamsinh").prop('checked', true)
            } else {
                $("#tthc_namsinh").val(objectBHXHCMU.ngaySinh)
                $("#tthc_chilaynamsinh").prop('checked', false)
            }
            $("#tthc_bhyt_ngay5nam").val(objectBHXHCMU.ngayDu5Nam)
            $("#tthc_gioitinh").val(objectBHXHCMU.gioiTinh == 'Nam'? '1': '0')
            $("#tthc_diachi").val(objectBHXHCMU.diaChi)
            $("#tthc_bhyt_makhuvuc").val(objectBHXHCMU.maKV)
            var noidangkibd = objectBHXHCMU.maDKBDMoi == null? objectBHXHCMU.maDKBD: objectBHXHCMU.maDKBDMoi;
            $("#tthc_bhyt_manoidkkcb").val(noidangkibd);
            $("#tthc_bhyt_noidkkcb").val(objectBHXHCMU.tenDKBDMoi == null? objectBHXHCMU.tenDKBD: objectBHXHCMU.tenDKBDMoi);
            if(objectBHXHCMU.ngayDu5Nam) {
                $("#tthc_bhyt_ngay5nam").val(objectBHXHCMU.ngayDu5Nam);
            }
            var theBHYT = objectBHXHCMU.maTheMoi == null? objectBHXHCMU.maThe : objectBHXHCMU.maTheMoi;
            $("#tthc_sobhyt").val(theBHYT);
            var chuoind = theBHYT.substring(0, 3);
            kiemTraDoituongTheBhyt(chuoind.toUpperCase(), fillDulieudoituongthe);
            $("#modalFormkiemtrabhyt").modal('hide');
        }
    })

    $("#tthc_luuthongtin").click(function() {
        thongtinhsba.thongTinCu = thongtinhsba.thongtinbn;
        var idButton = this.id;
        showSelfLoading(idButton);
        var checkData = kiemtradulieu();
        console.log("checkData", checkData == 1)
        if(checkData == false) {
            hideSelfLoading(idButton);
            return false;
        }
        if(checkData === 1) {
            $.confirm({
                title: 'Xác nhận!',
                type: 'orange',
                content: 'Bảo hiểm đã hết hạn bạn có chắc sẽ lưu thông tin?',
                buttons: {
                    warning: {
                        btnClass: 'btn-warning',
                        text: "Tiếp tục",
                        action: function(){
                            luuThongtinBenhnhan();
                        }
                    },
                    cancel: function () {
                        hideSelfLoading("tthc_luuthongtin");
                    }
                }
            });
            return false;
        }
        luuThongtinBenhnhan();
    })

    $("#tthc_bhyt_xoathongtin").click(function() {
        $("#tthc_sobhyt").val("");
        $("#tthc_sobhyt_chuoind").val("");
        $("#tthc_sobhyt_doituong").val("");
        $("#tthc_bhyt_tungay").val("");
        $("#tthc_bhyt_denngay").val("");
        $("#tthc_bhyt_tilebhyt").val("");
        $("#tthc_bhyt_ngay5nam").val("");
        $("#tthc_bhyt_manoidkkcb").val("");
        $("#tthc_bhyt_noidkkcb").val("");
        $("#tthc_bhyt_ngaymcct_giayto").val("");
        $("#tthc_bhyt_makhuvuc").val("");
        $("#tthc_bhyt_ngaymcct_khonggiayto").val("");
    })

    $("#tthc_bhyt_manoidkkcb").keypress(function(e) {
      if(e.keyCode == 13) {
          layTennoidangkykcb($(this).val(), fillDulieunoidangkykcb)
      }
    })

    $("#tthc_icdbenhchinhnv").keypress(function(e) {
        if(e.keyCode == 13) {
            getMotabenhly($(this).val(), function(data) {
                var splitIcd = data.split("!!!")
                $("#tthc_tenbenhchinhnv").val(splitIcd[1])
            })
        }
    })
    $("#tthc_icdbenhphunv").keypress(function(e) {
        if(e.keyCode == 13) {
            var string = $("#tthc_tenbenhphunv").val()
            var icd = $(this).val();
            if(!string.includes(icd.toUpperCase())) {
                getMotabenhly(icd, function(data) {
                    var splitIcd = data.split("!!!")
                    $("#tthc_tenbenhphunv").val(string+";" + "("+icd.toUpperCase() + ") "+splitIcd[1])
                    $("#tthc_icdbenhphunv").val("")
                })
            }
        }
    })
    $("#tthc_manoigioithieu").keypress(function(e) {
        var manoigioithieu = $(this).val();
        if(e.keyCode == 13 && manoigioithieu.trim().length > 0) {
            layTennoidangkykcb(manoigioithieu, function(data) {
                $("#tthc_tennoigioithieu").val(data);
            })
        }
    })
    $("#hsba-lichsubenhan").click(function() {
        initGridLichsukham()
        load_benhan_theosttbenhan(-1, thongtinhsba.thongtinbn.MA_BENH_NHAN)
        $("#modalFormlichsukham").modal("show");
    })
    $("#hsba-lichsu-thuoc-lammoi").click(function() {
        var ret = getThongtinRowSelected("hsba_list_dskham")
        loadLichsuDsThuocVattuTonghop($("#hsba-lichsu-nghiepvutoathuoc").val(),ret.STT_BENHAN, ret.MABENHNHAN)
    })
    $("#tthc-capnhat-congkham").click(function() {
        showModalFormcapnhatcongkham("v-pills-tabContent")
    })

    $("#tthc_icdchandoannoigioithieu").keypress(function(e) {
        var icd = $(this).val();
        if(e.keyCode == 13) {
            getMotabenhly($(this).val(), function(data) {
                var splitIcd = data.split("!!!")
                $("#tthc_icdchandoannoigioithieu").val(icd ? icd.toUpperCase() : "");
                $("#tthc_chandoannoigioithieu").val(splitIcd[1])
            })
        }
    })

    $("#tthc_matinh30").change(function(){
        loadDSHuyenTT30($(this).val(), thongtinhsba.thongtinbn.MAHUYEN_CU_TRU);
    });
    $("#tthc_mahuyen30").change(function(){
        loadDSXaTT30($(this).val(), thongtinhsba.thongtinbn.MAXA_CU_TRU);
    });
    $("#tthc_tennoigioithieu").combogrid({
        url: 'laydanhsachnoichuyenden',
        debug: true,
        width: "600px",
        colModel: [{'columnName': 'MA_NOITIEPNHAN', 'label': 'Mã', 'width': '20'},
            {'columnName': 'TEN_NOITIEPNHAN', 'width': '80', 'label': 'Tên nơi chuyển', 'align': 'left'}
        ],
        select: function (event, ui) {
            $("#tthc_tennoigioithieu").val(ui.item.TEN_NOITIEPNHAN);
            $("#tthc_manoigioithieu").val(ui.item.MA_NOITIEPNHAN);
            return false;
        }

    });
    $("#tthc_tntt").change(function() {
        showformtntt();
    });

    $("#tthc_mapthongtinme_giaychungsinh").click(function() {
        openModalFormMapSoBenhAnMeVaGiayChungSinh(thongtinhsba.thongtinbn.SOBENHAN_HOSOME, thongtinhsba.thongtinbn.ID_GIAYCHUNGSINH);
    });

    $("#tthc_ttm_gcs_sobenhan").keypress(function(e) {
        if(e.keyCode == 13) {
            var soBenhAn = $(this).val();
            if(soBenhAn.length > 0) {
                loadDsGiayChungSinhBySoBenhAn(soBenhAn);
            }
        }
    });

    $("#tthc_ttm_gcs_luu_action").click(function () {
        var soBenhAn = $('#tthc_ttm_gcs_giaychungsinh option:selected').data('sobenhan');
        var idGCS = $('#tthc_ttm_gcs_giaychungsinh').val();
        if(idGCS != null) {
            $.post("cmu_post_CMU_MAPSOBENHAN_GCS_ME", {
                url: [
                    singletonObject.dvtt,
                    thongtinhsba.thongtinbn.MA_BENH_NHAN,
                    soBenhAn,
                    idGCS
                ].join("```")
            }).done(function (data) {
                if (data == "1") {
                    notifiToClient("Green", "Cập nhật thông tin thành công!");
                    $("#modalFormMapSoBenhAnMeVaGiayChungSinh").modal("hide");
                    load_dsbenhnhan(thongtinhsba.thongtinbn.SOVAOVIEN_DT);
                } else {
                    notifiToClient("Red", "Lỗi không cập nhật được thông tin!");
                }
            }).fail(function () {
                notifiToClient("Red", "Lỗi không cập nhật được thông tin!");
            });
        } else {
            notifiToClient("Red", "Vui lòng chọn giấy chứng sinh!");
        }
    });

    function loadDsGiayChungSinhBySoBenhAn(soBenhAn, idGCS = null) {
        $("#tthc_ttm_gcs_sobenhan").val(soBenhAn);
        $.get('cmu_getlist?url='+convertArray([
            singletonObject.dvtt,
            soBenhAn,
            'CMU_DSGIAYCHUNGSINH_BYSOBENHAN'
        ])).done(function(data) {
            $("#tthc_ttm_gcs_giaychungsinh").empty();
            if(data.length > 0) {
                for (var i = 0; i < data.length; i++) {
                    $("#tthc_ttm_gcs_giaychungsinh").append("<option value='"+data[i].ID_GIAYCHUNGSINH+"' data-sobenhan='" + data[i].SOBENHAN + "'>"+data[i].GIAYCHUNGSINH+"</option>");
                }
                $("#tthc_ttm_gcs_giaychungsinh").select2({width: "100%"});
                if(idGCS != null) {
                    $("#tthc_ttm_gcs_giaychungsinh").val(idGCS).trigger('change');
                }
            }
            $("#modalFormMapSoBenhAnMeVaGiayChungSinh").modal("show");
        });
    }

    function openModalFormMapSoBenhAnMeVaGiayChungSinh(soBenhAn = null, idGCS = null) {
        $("#tthc_ttm_gcs_sobenhan").val(soBenhAn);
        loadDsGiayChungSinhBySoBenhAn(soBenhAn, idGCS);
    }

    function showformtntt(){
        if($("#tthc_tntt").val() > 0) {
            $("#tthc_showformtntt").show();
        } else {
            $("#tthc_showformtntt").hide();
        }
    }
    function luuThongtinBenhnhan() {
        var sothe = $("#tthc_sobhyt").val();
        var hoten = $("#tthc_hoten").val();
        var url = "timkiembntheobhyt?bhyt=" + sothe + "&mabn=0";
        $.getJSON(url, function (result) {
            if ( result.length > 0 &&  result[0].TEN_BENH_NHAN != hoten) {
                $.confirm({
                    title: 'Xác nhận!',
                    type: 'orange',
                    content: "Số thẻ " + sothe + " đã sử dụng cho bệnh nhân: " + result[0].TEN_BENH_NHAN + ", bạn chắc chắn sửa?",
                    buttons: {
                        warning: {
                            btnClass: 'btn-warning',
                            text: "Tiếp tục",
                            action: function(){
                                luuThongtin();
                            }
                        },
                        cancel: function () {
                            hideSelfLoading("tthc_luuthongtin");
                        }
                    }
                });
            } else {
                luuThongtin();
            }
        }).fail(function() {
            hideSelfLoading("tthc_luuthongtin");
            notifiToClient("Red", "Lỗi không lưu đuợc thông tin bệnh nhân!");
        });
    }

    function luuThongtin() {
        var url = "noitru_kiemtrathanhtoan_ddt_luu";
        $.post(url, {
            dvtt: singletonObject.dvtt, stt_benhan: thongtinhsba.thongtinbn.STT_BENHAN, stt_dotdieutri: thongtinhsba.thongtinbn.STT_DOTDIEUTRI
        }).done(function (data) {
            if (data != "0") {
                notifiToClient("Red", "Đợt điều trị này đã được thanh toán, không thể chỉnh sửa!");
            } else {
                var sothe_kiemtra_the = $("#tthc_sobhyt").val();
                var bienkiemtra_trangthaibhbenhnhan_truocchuyen = 0;
                $.post("checktrang_thaibhyt_benhnhannoitru_bant", {
                    sovaovien_dt: thongtinhsba.thongtinbn.SOVAOVIEN_DT,
                    stt_benhan: thongtinhsba.thongtinbn.STT_BENHAN,
                    stt_dotdieutri: thongtinhsba.thongtinbn.STT_DOTDIEUTRI,
                    sovaovien: thongtinhsba.thongtinbn.SOVAOVIEN,
                    dvtt: singletonObject.dvtt
                }).done(function (data) {
                    bienkiemtra_trangthaibhbenhnhan_truocchuyen = data;
                    if ((bienkiemtra_trangthaibhbenhnhan_truocchuyen == 0 && sothe_kiemtra_the.length > 0)
                        || (bienkiemtra_trangthaibhbenhnhan_truocchuyen == 1 && sothe_kiemtra_the.length == 0)) {
                        var url = "kiemtra_dvkt_cls_thanhtoan_chuyentuyen_truocchuyenbhyt?dvtt="+singletonObject.dvtt+"&sovaovien=" + thongtinhsba.thongtinbn.SOVAOVIEN
                            + "&sovaovien_dt=" + thongtinhsba.thongtinbn.SOVAOVIEN_DT + "&stt_benhan=" + thongtinhsba.thongtinbn.STT_BENHAN
                            + "&stt_dotdieutri=" + thongtinhsba.thongtinbn.STT_DOTDIEUTRI;
                        $.ajax({
                            url: url,
                            success: function (data) {
                                if (data == "0") {
                                    capnhatthongtinhc(bienkiemtra_trangthaibhbenhnhan_truocchuyen);
                                    capnhat_tthc_tntt();
                                } else {
                                    hideSelfLoading("tthc_luuthongtin");
                                    notifiToClient("Red", "Lỗi không lưu đuợc thông tin bệnh nhân!");
                                }
                            }
                        }).fail(function () {
                            hideSelfLoading("tthc_luuthongtin");
                            notifiToClient("Red", "Lỗi không lưu đuợc thông tin bệnh nhân!");
                        });
                    } else {
                        capnhatthongtinhc(bienkiemtra_trangthaibhbenhnhan_truocchuyen);
                        capnhat_tthc_tntt();
                    }
                }).fail(function () {
                    hideSelfLoading("tthc_luuthongtin");
                    notifiToClient("Red", "Lỗi không lưu đuợc thông tin bệnh nhân!");
                });

            }
        });
    }


    function capnhatthongtinhc(bienkiemtra_trangthaibhbenhnhan_truocchuyen) {
        var sothe = $("#tthc_sobhyt").val();
        var madt = $("#tthc_sobhyt_chuoind").val();
        var noidangky = $("#tthc_bhyt_manoidkkcb").val();
        var tennoidangky = $("#tthc_bhyt_noidkkcb").val();
        var ngaybatdau = convertStr_MysqlDate($("#tthc_bhyt_tungay").val());
        var ngayhethan = convertStr_MysqlDate($("#tthc_bhyt_denngay").val());
        var capcuu = $("#tthc_bhyt_hinhthucnhapvien").val() == 0 ? "1" : "0";
        var dungtuyen = $("#tthc_bhyt_hinhthucnhapvien").val() != 3? true: false;
        var hoten = $("#tthc_hoten").val();
        var ngaysinh = convertStr_MysqlDate($("#tthc_namsinh").val());
        var cmt = $("#tthc_cccd_cmt").val();
        var bhytnoitinh = "0";
        var hotrotienan = 0;
        var gioitinh = $("#tthc_gioitinh").val();
        var dantoc = $("#tthc_dantoc").val();
        var suatt_diachi = $("#tthc_diachi").val();
        var suatt_sonha = $("#tthc_sonha").val();
        var makhuvuc = $("#tthc_bhyt_makhuvuc").val();
        var chinamsinh = $("#tthc_chilaynamsinh").prop('checked') == true ? 1 : 0;
        var nghenghiep = $("#tthc_nghenghiep").val();
        var icdnhapvien = $("#tthc_icdbenhchinhnv").val();
        var tenbenhchinh = $("#tthc_tenbenhchinhnv").val();
        var tenbenhphu = $("#tthc_tenbenhphunv").val();
        var manoigioithieu = $("#tthc_manoigioithieu").val();
        var tennoigioithieu = $("#tthc_tennoigioithieu").val();
        var chandoannoichuyen = $("#tthc_chandoannoigioithieu").val();
        var nhommau = $("#tthc_nhommau").val();
        var khangthe =  $("#tthc_nhommaurh").val() == "" ? 0: $("#tthc_nhommaurh").val();
        var vongbung = $("#tthc_vongbung").val();
        var cannang_hc = $("#tthc_cannang").val();
        var nguoilienhe = $("#tthc_nguoilienhe").val();
        var sodienthoai = $("#tthc_sdt").val();
        var mayte = thongtinhsba.thongtinbn.MA_BENH_NHAN;
        var tegt = $("#tthc_bhyt_loaigiayte").val();
        var tegiayto = $("#tthc_bhyt_loaigiayte_text").val();
        var str2 = [mayte, tegt == ""? " ": tegt, !tegiayto ? " ": tegiayto];
        var optCanNgheo = 0;
        var gioSinhCV130 = $('#tthc_ngaysinh130').val();
        var ngoaikieu = $("#tthc_ngoaikieu").prop('checked') == true ? 1 : 0;
        var theTam = $("#tthc_thetam").prop('checked');
        var arr = [thongtinhsba.thongtinbn.STT_DOTDIEUTRI,
            thongtinhsba.thongtinbn.STT_BENHAN, singletonObject.dvtt,
            thongtinhsba.thongtinbn.STT_LOGKHOAPHONG, singletonObject.makhoa, thongtinhsba.thongtinbn.MA_BENH_NHAN,
            singletonObject.userId,
            sothe, madt, 0, noidangky, tennoidangky, ngaybatdau, ngayhethan, dungtuyen, capcuu, "0", hoten, ngaysinh, cmt, bhytnoitinh, hotrotienan, gioitinh, dantoc, suatt_diachi,
            suatt_sonha, $("#tthc_bhyt_mcct").val(),
            makhuvuc, chinamsinh, nghenghiep, icdnhapvien, tenbenhchinh, tenbenhphu, manoigioithieu, tennoigioithieu, chandoannoichuyen, vongbung, nhommau, khangthe, optCanNgheo, "0"];
        $.post("noitru_capnhatddt_luu_5nam", {
            url: convertArray(arr),
            ngay_mien_cung_ct: $("#tthc_bhyt_ngaymcct_giayto").val(),
            ngay_mien_cung_ct_khonggiay: $("#tthc_bhyt_ngaymcct_khonggiayto").val(),
            thoigian_du5namlientuc_tnhc: $("#tthc_bhyt_ngay5nam").val(),
            cannang_hc: cannang_hc,
            nguoilienhe: nguoilienhe,
            sodienthoai: sodienthoai,
            thongtuyen_bhxh: ($("#tthc_bhyt_hinhthucnhapvien").val() == 2 ? 1 : 0),
            hieuluc_tungay_bhpvi: null,
            hieuluc_denngay_bhpvi: null,
            donvi_bhpvi: null,
            sothebh_pvi: null,
            chuongtrinh_bhpvi: 'PVI CARE',
            dinhmuc_conlai_bhpvi: 0,
            sovaovien: thongtinhsba.thongtinbn.SOVAOVIEN,
            sovaovien_dt: thongtinhsba.thongtinbn.SOVAOVIEN_DT,
            ngoaikieu: ngoaikieu
            ,duongTinhCovid19: 0
            ,dieuTriPhongKhamDK: 0,
            gioSinhCV130: gioSinhCV130,
            maTinhTT30: $("#tthc_matinh30").val() == ""? "0": $("#tthc_matinh30").val(),
            maHuyenTT30: $("#tthc_mahuyen30").val() == ""? "0": $("#tthc_mahuyen30").val(),
            maXaTT30: $("#tthc_maxa30").val() == ""? "0": $("#tthc_maxa30").val(),
            hoTenMe: $("#tthc_hotenme").val(),
            hoTenCha: $("#tthc_hotencha").val(),
            maBHXHCha: $("#tthc_bhxhcha").val(),
            maBHXHMe: $("#tthc_bhxhme").val(),
            theTam: theTam
        }).done(function () {
            $.post("suagiaytote1", {url: str2.join("```")});
            $.post("checktrang_thaibhyt_benhnhannoitru_bant", {
                sovaovien_dt: thongtinhsba.thongtinbn.SOVAOVIEN_DT,
                stt_benhan: thongtinhsba.thongtinbn.STT_BENHAN,
                stt_dotdieutri: thongtinhsba.thongtinbn.STT_DOTDIEUTRI,
                sovaovien: thongtinhsba.thongtinbn.SOVAOVIEN,
                dvtt: singletonObject.dvtt
            }).done(function (data) {
                var bienkiemtra_benhnhanbhyt_saucapnhat = data;
                if (bienkiemtra_benhnhanbhyt_saucapnhat != bienkiemtra_trangthaibhbenhnhan_truocchuyen) {
                    capnhatthongtinhanhchinh_chuyenbhyt(bienkiemtra_benhnhanbhyt_saucapnhat);
                } else {
                    notifiToClient("Green", "Cập nhật thành công!");
                    hideSelfLoading("tthc_luuthongtin");
                }
            }).fail(function () {
                hideSelfLoading("tthc_luuthongtin");
                notifiToClient("Red", "Cập nhật thông tin bệnh nhân thất bại!");
            });
            var tthcVBA = thongtinhsba.thongtinbn.VOBENHAN[0].HANHCHINH;
            tthcVBA.ICD_CHANDOAN_NOIGIOITHIEU = $("#tthc_icdchandoannoigioithieu").val();
            tthcVBA.CHANDOAN_NOIGIOITHIEU = chandoannoichuyen;
            tthcVBA.CMND_CCCD_BN = cmt;
            tthcVBA.DANTOC = dantoc;
            tthcVBA.DENNGAY_BHYT = $("#tthc_bhyt_denngay").val();
            tthcVBA.DIACHI = suatt_diachi;
            tthcVBA.GIOITINH = gioitinh;
            tthcVBA.HOTEN = hoten;
            tthcVBA.NGAYSINH = $("#tthc_namsinh").val();
            tthcVBA.NGHENGHIEP = nghenghiep;
            tthcVBA.NGUOILIENHE = nguoilienhe;
            tthcVBA.NOI_GIOI_THIEU = $("#tthc_tennoigioithieu").val();
            tthcVBA.SDT_NGUOILIENHE = $("#tthc_sdt").val();
            tthcVBA.SONHA = suatt_sonha;
            tthcVBA.SOTHEBHYT = sothe;
            tthcVBA.SO_DIEN_THOAI_BN = sodienthoai;
            tthcVBA.TUNGAY_BHYT = $("#tthc_bhyt_tungay").val();
            tthcVBA.TINHTHANH = $("#tthc_matinh30").val();
            tthcVBA.TINHTHANH_TEXT = $("#tthc_matinh30 option:selected").text() == "-------" ? "": $("#tthc_matinh30 option:selected").text();
            tthcVBA.QUANHUYEN = $("#tthc_mahuyen30").val();
            tthcVBA.QUANHUYEN_TEXT = $("#tthc_mahuyen30 option:selected").text() == "-------" ? "": $("#tthc_mahuyen30 option:selected").text();
            tthcVBA.XAPHUONG = $("#tthc_maxa30").val();
            tthcVBA.XAPHUONG_TEXT = $("#tthc_maxa30 option:selected").text() == "-------" ? "": $("#tthc_maxa30 option:selected").text();
            tthcVBA.HOTEN_BO = $("#tthc_hotencha").val();
            tthcVBA.HOTEN_ME = $("#tthc_hotenme").val();
            capNhatThongTinhHanhChinhVBA(tthcVBA);
            getThongtinBNChitiet(function() {
                thongtinhsba.thongTinMoi = thongtinhsba.thongtinbn;
                luuLogHSBAChinhSua(thongtinhsba.thongTinCu, thongtinhsba.thongTinMoi, "THONGTINHANHCHINH");
            }, function() {

            });
            $.post("cmu_post_cmu_updatelydovaovien", {
                url: [singletonObject.dvtt, thongtinhsba.thongtinbn.STT_BENHAN, $("#tthc_lydovaovien").val()].join("```")
            })
        }).fail(function () {
            hideSelfLoading("tthc_luuthongtin");
            notifiToClient("Red", "Cập nhật thông tin bệnh nhân thất bại!");
        });
    }

    function capnhatthongtinhanhchinh_chuyenbhyt(data) {
        if (data == 1) {
            $.confirm({
                title: 'Xác nhận!',
                type: 'orange',
                content: "Bạn có muốn chuyển các thông tin chi phí sang có bảo hiểm. Bạn có muốn tiếp tục?",
                buttons: {
                    warning: {
                        btnClass: 'btn-warning',
                        text: "Tiếp tục",
                        action: function(){
                            var url = "kiemtra_dvkt_cls_thanhtoan_all_noitru?dvtt="+singletonObject.dvtt+"&sovaovien="
                                + thongtinhsba.thongtinbn.SOVAOVIEN + "&sovaovien_dt=" + thongtinhsba.thongtinbn.SOVAOVIEN_DT +
                                "&stt_benhan=" + thongtinhsba.thongtinbn.STT_BENHAN
                                + "&stt_dotdieutri=" + thongtinhsba.thongtinbn.STT_DOTDIEUTRI;
                            $.ajax({
                                url: url,
                                success: function (resData) {
                                    if (resData == "0") {
                                        $.post("cn_chuyen_bh_toandichvu", {
                                            sovaovien_dt: thongtinhsba.thongtinbn.SOVAOVIEN_DT,
                                            stt_benhan: thongtinhsba.thongtinbn.STT_BENHAN,
                                            stt_dotdieutri: thongtinhsba.thongtinbn.STT_DOTDIEUTRI,
                                            sovaovien: thongtinhsba.thongtinbn.SOVAOVIEN,
                                            dvtt: singletonObject.dvtt
                                        }).done(function () {
                                            notifiToClient("Green", "Chuyển các thông tin chi phí sang có bảo hiểm thành công!");
                                        }).fail(function () {
                                            notifiToClient("Red", "Chuyển các thông tin chi phí sang có bảo hiểm không thành công!");
                                        }).always(function () {
                                            hideSelfLoading("tthc_luuthongtin");
                                            noitruTaoBangke(thongtinhsba.thongtinbn)
                                        });
                                    } else {
                                        hideSelfLoading("tthc_luuthongtin");
                                        noitruTaoBangke(thongtinhsba.thongtinbn)
                                        notifiToClient("Red", "Không thể chuyển bệnh nhân đã thanh toán hoắc bệnh nhân đã chuyển tuyến. Vui lòng hủy thanh toán hoặc hủy chuyển tuyến nếu có!");
                                    }
                                }
                            }).fail(function () {
                                hideSelfLoading("tthc_luuthongtin");
                                notifiToClient("Red", "Chuyên không thành công vui lòng thử lại sau!");
                            });
                        }
                    },
                    cancel: function () {
                        hideSelfLoading("tthc_luuthongtin");
                    }
                }
            });
        } else {
            $.confirm({
                title: 'Xác nhận!',
                type: 'orange',
                content: "Bạn có muốn chuyển các thông tin chi phí sang không bảo hiểm. Bạn có muốn tiếp tục?",
                buttons: {
                    warning: {
                        btnClass: 'btn-warning',
                        text: "Tiếp tục",
                        action: function(){
                            var url = "kiemtra_dvkt_cls_thanhtoan_all_noitru?dvtt="+singletonObject.dvtt+"&sovaovien="
                                + thongtinhsba.thongtinbn.SOVAOVIEN + "&sovaovien_dt=" + thongtinhsba.thongtinbn.SOVAOVIEN_DT +
                                "&stt_benhan=" + thongtinhsba.thongtinbn.STT_BENHAN
                                + "&stt_dotdieutri=" + thongtinhsba.thongtinbn.STT_DOTDIEUTRI;
                            $.ajax({
                                url: url,
                                success: function (resData) {
                                    if (resData == "0") {
                                        $.post("cn_chuyen_khongbh_toandichvu", {
                                            sovaovien_dt: thongtinhsba.thongtinbn.SOVAOVIEN_DT,
                                            stt_benhan: thongtinhsba.thongtinbn.STT_BENHAN,
                                            stt_dotdieutri: thongtinhsba.thongtinbn.STT_DOTDIEUTRI,
                                            sovaovien: thongtinhsba.thongtinbn.SOVAOVIEN,
                                            dvtt: singletonObject.dvtt
                                        }).done(function () {
                                            notifiToClient("Green", "Chuyển các thông tin chi phí sang không bảo hiểm thành công!");
                                        }).fail(function () {
                                            notifiToClient("Red", "Chuyển các thông tin chi phí sang không bảo hiểm không thành công!");
                                        }).always(function () {
                                            hideSelfLoading("tthc_luuthongtin");
                                            noitruTaoBangke(thongtinhsba.thongtinbn)
                                        });
                                    } else {
                                        hideSelfLoading("tthc_luuthongtin");
                                        noitruTaoBangke(thongtinhsba.thongtinbn)
                                        notifiToClient("Red", "Không thể chuyển bệnh nhân đã thanh toán hoắc bệnh nhân đã chuyển tuyến. Vui lòng hủy thanh toán hoặc hủy chuyển tuyến nếu có!");
                                    }
                                }
                            }).fail(function () {
                                hideSelfLoading("tthc_luuthongtin");
                                notifiToClient("Red", "Chuyên không thành công vui lòng thử lại sau!");
                            });
                        }
                    },
                    cancel: function () {
                        hideSelfLoading("tthc_luuthongtin");
                    }
                }
            });

        }

    }



    function initFormData() {
        var dataBN = thongtinhsba.thongtinbn;
        $("#tthc_sobhyt_chuoind").val(dataBN.MADOITUONG)
        if(dataBN.MADOITUONG != "") {
            kiemTraDoituongTheBhyt(dataBN.MADOITUONG, fillDulieudoituongthe)
        }
        $("#tthc_sobhyt").val(dataBN.SOBAOHIEMYTE)
        $("#tthc_bhyt_tungay").val(dataBN.NGAYBATDAU_THEBHYT)
        $("#tthc_bhyt_denngay").val(dataBN.NGAYHETHAN_THEBHYT)
        $("#tthc_bhyt_manoidkkcb").val(dataBN.NOIDANGKYBANDAU)
        $("#tthc_bhyt_noidkkcb").val(dataBN.TENNOIDANGKYBANDAU)
        $("#tthc_bhyt_ngay5nam").val(dataBN.NT_THOIDIEM_5NAM_LIENTUC)
        $("#tthc_bhyt_ngaymcct_khonggiayto").val(dataBN.NT_THOIDIEM_MCCT_KH_GIAY)
        $("#tthc_bhyt_ngaymcct_giayto").val(dataBN.NGAY_MIEN_CUNG_CT)
        $("#tthc_bhyt_mcct").val(dataBN.BAOHIEMYTE5NAM)
        $("#tthc_bhyt_makhuvuc").val(dataBN.MA_KHUVUC)
        if(dataBN.CAPCUU == "1"){
            $("#tthc_bhyt_hinhthucnhapvien").val(0)
        } else {
            if(dataBN.DUNGTUYEN == 0) {
                $("#tthc_bhyt_hinhthucnhapvien").val(3)
            } else if(dataBN.THONGTUYEN_BHXH_XML4210 == 1) {
                $("#tthc_bhyt_hinhthucnhapvien").val(2)
            } else {
                $("#tthc_bhyt_hinhthucnhapvien").val(1)
            }
        }
        $("#tthc_bhyt_loaigiayte").val("")
        $("#tthc_bhyt_loaigiayte_text").val("")
        if(dataBN.TE_GIAYCHUNGSINH != null && dataBN.TE_GIAYCHUNGSINH != "") {
            $("#tthc_bhyt_loaigiayte").val("GCS")
            $("#tthc_bhyt_loaigiayte_text").val(dataBN.TE_GIAYCHUNGSINH)
        }
        if(dataBN.TE_GIAYKHAISINH != null && dataBN.TE_GIAYKHAISINH != "") {
            $("#tthc_bhyt_loaigiayte").val("GKS")
            $("#tthc_bhyt_loaigiayte_text").val(dataBN.TE_GIAYKHAISINH)
        }
        if(dataBN.TE_GIAYTOKHAC != null && dataBN.TE_GIAYTOKHAC != "") {
            $("#tthc_bhyt_loaigiayte").val("GTK")
            $("#tthc_bhyt_loaigiayte_text").val(dataBN.TE_GIAYTOKHAC)
        }
        $("#tthc_hoten").val(dataBN.TEN_BENH_NHAN)
        $("#tthc_gioitinh").val(dataBN.GIOI_TINH)
        $("#tthc_namsinh").val(dataBN.NGAY_SINH)
        $("#tthc_chilaynamsinh").prop("checked", dataBN.HIEN_NAMSINH == 1)
        $("#tthc_sdt").val(dataBN.SO_DIEN_THOAI)
        $("#tthc_sonha").val(dataBN.SO_NHA)
        $("#tthc_ngoaikieu").prop("checked", dataBN.NGOAIKIEU != 0)
        $("#tthc_diachi").val(dataBN.DIA_CHI)
        $("#tthc_cccd_cmt").val(dataBN.CMT_BENHNHAN)
        $("#tthc_icdbenhchinhnv").val(dataBN.ICD_NHAPVIEN)
        $("#tthc_tenbenhchinhnv").val(dataBN.TENBENHCHINH_NHAPVIEN)
        $("#tthc_tenbenhphunv").val(dataBN.TENBENHPHU_NHAPVIEN)
        $("#tthc_manoigioithieu").val(dataBN.MANOIGIOITHIEU)
        $("#tthc_tennoigioithieu").val(dataBN.TEN_NOIGIOITHIEU)
        $("#tthc_chandoannoigioithieu").val(dataBN.CHANDOAN_NOIGIOITHIEU)
        $('#tthc_ngaysinh130').val(dataBN.GIO_SINH_CV130);
        $('#tthc_vongbung').val(dataBN.VONGBUNG);
        $('#tthc_cannang').val(dataBN.CANNANG);
        $("#tthc_nhommau").val(dataBN.NHOMMAU);
        $("#tthc_nhommaurh").val(dataBN.KHANGTHE);
        $("#tthc_nguoilienhe").val(dataBN.NGUOI_LIEN_HE);
        $("#tthc_dantoc").val(dataBN.MA_DANTOC).trigger('change');
        $("#tthc_nghenghiep").val(dataBN.MA_NGHE_NGHIEP).trigger('change');
        $("#tthc_matinh30").val(dataBN.MATINH_CU_TRU).trigger('change');
        $("#tthc_hotenme").val(dataBN.HO_TEN_ME);
        $("#tthc_hotencha").val(dataBN.HO_TEN_CHA);
        $("#tthc_bhxhme").val(dataBN.MA_ME);
        $("#tthc_bhxhcha").val(dataBN.MA_CHA);
        $("#tthc_lydovaovien").val(dataBN.LYDO_TRANGTHAI_BN_NHAPVIEN);
        $("#tthc_thetam").prop("checked", thongtinhsba.thongtinbn.LATHETAM == 1);
        $("#tthc_tntt").val(dataBN.TAINANTHUONGTICH).trigger('change');
    }

    function kiemtradulieu() {
        var sothe = $("#tthc_sobhyt").val();
        var ngaybatdau = $("#tthc_bhyt_tungay").val();
        var ngayhethan = $("#tthc_bhyt_denngay").val();
        if($("#tthc_lydovaovien").val().trim().length == 0) {
            notifiToClient("Red", "Vui lòng nhập lý do vào viện");
            return false;
        }
        if (sothe.length != 15 && sothe != "") {
            notifiToClient("Red", "Thẻ BHYT không đúng");
            return false;
        }
        if (sothe != "" && sothe.length == 15 &&
            ($("#tthc_bhyt_tilebhyt").val() == "" || $("#tthc_bhyt_tilebhyt").val() == "0"
                || $("#tthc_sobhyt_chuoind").val() == "" || $("#tthc_sobhyt_doituong").val() == ""
                || sothe.indexOf("_") >= 0 || ngaybatdau == ""
                || ngayhethan == "")) {
            notifiToClient("Red", "Vui lòng kiểm tra lại thông tin thẻ BHYT");
            return false;
        }
        if (sothe != "" && sothe.length == 15 && (sothe.indexOf("_") < 0)) {
            var today = getDate(singletonObject.ngayhientai); //new Date();
            var ngaybatdau = getDate(ngaybatdau);
            var ngayhethan = getDate(ngayhethan);
            var chuoind = sothe.substring(0, 3);
            var sothebhyt_ktr = sothe.trim().toUpperCase();
            var url1 = "kiemtrathebhyt?madt=" + chuoind;
            var resData = $.ajax({
                url: url1,
                async: false,
                type: "GET",
            }).responseText;
            if (resData == "UNVALID") {
                notifiToClient("Red", "Thẻ bảo hiểm không đúng");
                return false;
            }
            if (ngaybatdau > today) {
                notifiToClient("Red", "Ngày bắt đầu có hiệu lực của BHYT chưa đến");
                return false;
            }
            if (ngayhethan < today) {
                var namsinharr = $("#tthc_namsinh").val().split('/');
                var tuoi = getDate(singletonObject.ngayhientai).getFullYear() - namsinharr[2];
                if ((sothe.indexOf("TE") == "0") && (tuoi <= 6) && (today.getMonth() < 8)) {
                    return true;
                } else {
                    return 1;
                }
            }
            if (ngaybatdau > ngayhethan) {
                notifiToClient("Red", "Ngày bắt đầu phải nhỏ hơn ngày kết thúc");
                return false;
            }
            if (sothe.trim() != "" && ($("#tthc_bhyt_manoidkkcb").val() == "" || $("#tthc_bhyt_noidkkcb").val() == "")) {
                notifiToClient("Red", "Nơi đăng ký khám chữa bệnh ban đầu chưa đúng. Vui lòng kiểm tra lại");
                return false;
            }
        }
        if ($("#tthc_hoten").val() == "") {
            notifiToClient("Red", "Họ tên không được trống");
            return false;
        }
        if (!isValidDate($("#tthc_namsinh").val())) {
            notifiToClient("Red", "Năm sinh không hợp lệ");
            return false;
        }
        if ($("#tthc_diachi").val() == "") {
            notifiToClient("Red", "Địa chỉ không được trống");
            return false;
        }
        if ($("#tthc_icdbenhchinhnv").val() == "") {
            notifiToClient("Red", "ICD không được trống");
            return false;
        }
        var dataBN = thongtinhsba.thongtinbn;
        if(dataBN.TUOI <=1 && dataBN.THANG < 12 && !$("#tthc_cannang").val()) {
            notifiToClient("Red", "Phải cập nhật CÂN NẶNG cho bệnh nhân dưới 1 tuổi")
            return false;
        }
        if(!$("#tthc_cannang").val()) {
            notifiToClient("Red", "Vui lòng nhập cân nặng")
            return false;
        }
        if($("#tthc_cannang").val() < 0) {
            notifiToClient("Red", "CÂN NẶNG phải lớn hơn 0")
            return false;
        }
        if ($("#tthc_sdt").val().length > 12 ) {
            notifiToClient("Red", "Số điện thoại không hợp lệ");
            return false;
        }
        if ($("#tthc_icdbenhchinhnv").val() == "") {
            notifiToClient("Red", "ICD không được trống");
            return false;
        }
        if(!isValidTimeSecond($("#tthc_ngaysinh130").val())) {
            notifiToClient("Red", "Giờ sinh không hợp lệ");
            return false;
        }
        if(!$("#tthc_matinh30").val() || !$("#tthc_mahuyen30").val() || !$("#tthc_maxa30").val()) {
            notifiToClient("Red", "Vui lòng chọn đủ thông tin địa chỉ thường trú (CV130)");
            return false;
        }
        if($("#tthc_cccd_cmt").val().trim().length > 0 &&
            ($("#tthc_cccd_cmt").val().trim().length != 9 && $("#tthc_cccd_cmt").val().trim().length != 12)
        ) {
            notifiToClient("Red", "CMND/CCCD không hợp lệ");
            return false;
        }
        return true;
    }


    function initGridLichsukham() {
        var list = $("#hsba_list_dskham")
        if(!list[0].grid) {
            list.jqGrid({
                url: "",
                datatype: "local",
                loadonce: false,
                height: 250,
                width: null,
                shrinkToFit: false,
                colModel: [
                    {label: "Số phiếu", name: 'STT_BENHAN', index: 'STT_BENHAN', width: 90, hidden: true},
                    {label: "Số BA", name: 'SOBENHAN', index: 'SOBENHAN', width: 90},
                    {label: "Tên bệnh nhân", name: 'TEN_BENH_NHAN', index: 'TEN_BENH_NHAN', width: 180},
                    {label: "Ngày ra", name: 'NGAY_RAVIEN', index: 'NGAY_RAVIEN', width: 100, formatter: function(cellValue, options, rowObject) {
                            if(cellValue  == null || cellValue == "") {
                                return ''
                            }
                            return moment(cellValue).format("DD/MM/YYYY");
                        }
                    },
                    {label: "MABENHNHAN", name: 'MABENHNHAN', index: 'MABENHNHAN', width: 200, hidden: true},
                    {label: "STT_DOTDIEUTRI", name: 'STT_DOTDIEUTRI', index: 'STT_DOTDIEUTRI', width: 200, hidden: true}
                ],
                rowNum: 1000,
                caption: "",
                onSelectRow: function (id) {
                    if (id) {
                        var ret = list.jqGrid('getRowData', id);
                        $("#hsba-lichsu-thuoc").click()
                        $("#hsba-lichsu-nghiepvutoathuoc").val("noitru_toathuoc")
                        loadLichsuDsThuocVattuTonghop("noitru_toathuoc",ret.STT_BENHAN, ret.MABENHNHAN)
                        loadLichsuDsXetnghiemTonghop(ret.STT_BENHAN, ret.MABENHNHAN)
                        loadLichsuDsCDHATonghop(ret.STT_BENHAN, ret.MABENHNHAN)
                        loadLichsuDsTTPTTonghop(ret.STT_BENHAN, ret.MABENHNHAN)
                    }
                },
                gridComplete: function () {
                    var str = list.getGridParam("records");
                    if (str != "0") {
                        list.jqGrid('setSelection', str);
                    }
                }
            });
        }
        var lisThuoc = $("#hsba_lichsu_list_thuoc")
        if(!lisThuoc[0].grid) {
            lisThuoc.jqGrid({
                datatype: "local",
                loadonce: false,
                height: 450,
                width: null,
                shrinkToFit: false,
                colModel: [
                    {label: "STT_DOTDIEUTRI", name: 'STT_DOTDIEUTRI', index: 'STT_DOTDIEUTRI', width: 10},
                    {label: "TEN_PHONGBAN", name: 'TEN_PHONGBAN', index: 'TEN_PHONGBAN', width: 10},
                    {label: "NGAYLAP", name: 'NGAYLAP', index: 'NGAYLAP', width: 10},
                    {label: "STT_DIEUTRI", name: 'STT_DIEUTRI', index: 'STT_DIEUTRI', width: 10},
                    {label: "Toa", name: 'MOTA_NGHIEPVU', index: 'MOTA_NGHIEPVU', width: 100},
                    {label: "Tên thương mại", name: 'TEN_VAT_TU', index: 'TEN_VAT_TU', width: 100},
                    {label: "Hoạt chất", name: 'HOAT_CHAT', index: 'HOAT_CHAT', width: 130},
                    {label: "DVT", name: 'DVT', index: 'DVT', width: 50},
                    {label: "Số ngày uống", name: 'SO_NGAY_UONG', index: 'SO_NGAY_UONG', width: 60},
                    {label: "Sáng", name: 'SANG_UONG', index: 'SANG_UONG', width: 40},
                    {label: "Trưa", name: 'TRUA_UONG', index: 'TRUA_UONG', width: 40},
                    {label: "Chiều", name: 'CHIEU_UONG', index: 'CHIEU_UONG', width: 40},
                    {label: "Tối", name: 'TOI_UONG', index: 'TOI_UONG', width: 40},
                    {label: "Số lượng", name: "SO_LUONG", index: "SO_LUONG", width: 60},
                    {label: "Cách dùng", name: 'CACH_SU_DUNG', index: 'CACH_SU_DUNG', width: 100},
                    {
                        label: "Đơn giá",
                        name: 'DONGIA_BAN_BH',
                        index: 'DONGIA_BAN_BH',
                        width: 80,
                        align: 'right',
                        formatter: 'integer',
                        formatoptions: {decimalSeparator: ".", thousandsSeparator: ","}
                    },
                    {
                        label: "Thành tiền",
                        name: 'THANHTIEN_THUOC',
                        index: 'THANHTIEN_THUOC',
                        width: 80,
                        align: 'right',
                        formatter: 'integer',
                        formatoptions: {decimalSeparator: ".", thousandsSeparator: ","}
                    },
                    {label: "Ghi chú", name: 'GHI_CHU_CT_TOA_THUOC', index: 'GHI_CHU_CT_TOA_THUOC', width: 130},
                    {label: "Bác sĩ thêm thuốc", name: 'TEN_NHANVIEN', index: 'TEN_NHANVIEN', width: 120},
                    {label: "MAVATTU", name: 'MAVATTU', index: 'MAVATTU', hidden: true},
                    {label: "MAKHOVATTU", name: 'MAKHOVATTU', index: 'MAKHOVATTU', hidden: true},
                    {label: "DONGIA_BAN_BV", name: 'DONGIA_BAN_BV', index: 'DONGIA_BAN_BV', hidden: true},
                    {label: "MA_TOA_THUOC", name: 'MA_TOA_THUOC', index: 'MA_TOA_THUOC', hidden: true}
                ],
                caption: "Danh sách thuốc/vật tư",
                rowNum: 1000,
                grouping: true,
                groupingView: {
                    groupField: ["STT_DOTDIEUTRI", "TEN_PHONGBAN", "NGAYLAP", "STT_DIEUTRI"],
                    groupColumnShow: [false, false, false, false],
                    groupText: ['<b>Đợt: {0}</b>', '<b>Khoa lập phiếu: {0}</b>', '<b>Thời gian: {0}</b>', "<b>PHIẾU: {0}</b>"],
                    groupCollapse: false
                },
                onSelectRow: function (id) {
                },
                onRightClickRow: function (id1) {

                }
            });
        }
        var listXn = $("#hsba_lichsu_list_xetnghiem")
        if(!listXn[0].grid) {
            listXn.jqGrid({
                datatype: "local",
                loadonce: false,
                height: 450,
                width: null,
                shrinkToFit: false,
                colModel: [
                    {label: "STT_DOTDIEUTRI",name: 'STT_DOTDIEUTRI', index: 'STT_DOTDIEUTRI', width: 10, hidden: true},
                    {label: "NGAY_CHI_DINH",name: 'NGAY_CHI_DINH', index: 'NGAY_CHI_DINH', width: 10, hidden: true},
                    {label: "STT_DIEUTRI",name: 'STT_DIEUTRI', index: 'STT_DIEUTRI', width: 10, hidden: true},
                    {label: "SOBENHAN_TT",name: 'SOBENHAN_TT', index: 'SOBENHAN_TT', width: 10, hidden: true},
                    {label: "SOBENHAN",name: 'SOBENHAN', index: 'SOBENHAN', width: 10, hidden: true},
                    {label: "TEN_LOAI_XETNGHIEM",name: 'TEN_LOAI_XETNGHIEM', index: 'TEN_LOAI_XETNGHIEM', width: 10, hidden: true},
                    {label: "ICD_KHOADIEUTRI",name: 'ICD_KHOADIEUTRI', index: 'ICD_KHOADIEUTRI', width: 10, hidden: true},
                    {label: "TENICD_KHOADIEUTRI",name: 'TENICD_KHOADIEUTRI', index: 'TENICD_KHOADIEUTRI', width: 10, hidden: true},
                    {label: "SO_PHIEU_XN",name: 'SO_PHIEU_XN', index: 'SO_PHIEU_XN', width: 100, hidden: true},
                    {label: "Tên xét nghiệm",name: 'TEN_XETNGHIEM', index: 'TEN_XETNGHIEM', width: 270,
                        cellattr: function (rowId, cellValue, rowObject) {
                            if (rowObject.INDAM != 0) {
                                return 'style="font-weight:bold;color: blue"';
                            }
                            return 'style="white-space: normal;"';
                        }
                    },
                    {label: "Số lượng",name: 'SO_LUONG', index: 'SO_LUONG', width: 50},
                    {
                        label: "Đơn giá",
                        name: 'DON_GIA',
                        index: 'DON_GIA',
                        width: 100,
                        align: 'right',
                        formatter: 'integer',
                        formatoptions: {decimalSeparator: ".", thousandsSeparator: ",", decimalPlaces: 2}
                    },
                    {
                        label: "Thành tiền",
                        name: 'THANH_TIEN',
                        index: 'THANH_TIEN',
                        width: 100,
                        align: 'right',
                        formatter: 'integer',
                        formatoptions: {decimalSeparator: ".", thousandsSeparator: ",", decimalPlaces: 2}
                    },
                    {
                        label: "BHYT không chi",
                        name: 'BHYTKCHI',
                        index: 'BHYTKCHI',
                        width: 110,
                        align: 'center',
                        formatter: 'checkbox',
                        formatoptions: {value: 'true:false'}
                    },
                    {label: "Kết quả",name: 'KET_QUA', index: 'KET_QUA', width: 100,
                        // cellattr: function (rowId, cellValue, rowObject) {
                        //     return getJqGridKETQUAStyle(rowId, cellValue, rowObject);
                        // }
                    },
                    {label: "CSBT",name: 'CSBT', index: 'CSBT', width: 100},
                    {label: "Đơn vị tính",name: 'DONVI', index: 'DONVI', width: 100},
                    {label: "Nguời chỉ định",name: 'NGUOI_CHI_DINH', index: 'NGUOI_CHI_DINH', width: 100},
                    {label: "STT_BENHAN",name: 'STT_BENHAN', index: 'STT_BENHAN', width: 10, hidden: true},
                    {label: "SOVAOVIEN",name: 'SOVAOVIEN', index: 'SOVAOVIEN', width: 10, hidden: true},
                    {label: "SOVAOVIEN_DT",name: 'SOVAOVIEN_DT', index: 'SOVAOVIEN_DT', width: 10, hidden: true},
                    {label: "KQBATTHUONG",name: 'KQBATTHUONG', index: 'KQBATTHUONG', width: 10, hidden: true},
                    {label: "CSBT_TREN",name: 'CSBT_TREN', index: 'CSBT_TREN', width: 10, hidden: true},
                    {label: "CSBT_DUOI",name: 'CSBT_DUOI', index: 'CSBT_DUOI', width: 10, hidden: true},
                    {label: "Bác sĩ điều trị",name: 'BAC_SI_DIEU_TRI', index: 'BAC_SI_DIEU_TRI', width: 150}
                ],
                caption: "Danh sách xét nghiệm",
                rowNum: 1000,
                grouping: true,
                groupingView: {
                    groupField: ["STT_DOTDIEUTRI", "NGAY_CHI_DINH", "STT_DIEUTRI", "TEN_LOAI_XETNGHIEM"],
                    groupColumnShow: [false, false, false, false, false],
                    groupText: [
                        '<b>Đợt: {0}</b>', '<b>Ngày lập: {0}</b>',
                        '<b>Phiếu: {0}</b>', '<b>{0}</b>'],
                    groupCollapse: false
                },
                onLeftClickRow: function (id) {

                }
                ,
                onRightClickRow: function (id1) {

                }
            });
        }

        var listCDHA = $("#hsba_lichsu_list_chandoanhinhanh")
        if(!listCDHA[0].grid) {
            listCDHA.jqGrid({
                url: "",
                datatype: "local",
                loadonce: false,
                height: 450,
                width: null,
                shrinkToFit: false,
                colModel: [
                    {label: "STT_DOTDIEUTRI",name: 'STT_DOTDIEUTRI', index: 'STT_DOTDIEUTRI', width: 10, hidden: true},
                    {label: "NGAY_CHI_DINH",name: 'NGAY_CHI_DINH', index: 'NGAY_CHI_DINH', width: 10, hidden: true},
                    {label: "STT_DIEUTRI",name: 'STT_DIEUTRI', index: 'STT_DIEUTRI', width: 10, hidden: true},
                    {label: "SOBENHAN_TT",name: 'SOBENHAN_TT', index: 'SOBENHAN_TT', width: 10, hidden: true},
                    {label: "SOBENHAN",name: 'SOBENHAN', index: 'SOBENHAN', width: 10, hidden: true},
                    {label: "ICD_KHOADIEUTRI",name: 'ICD_KHOADIEUTRI', index: 'ICD_KHOADIEUTRI', width: 10, hidden: true},
                    {label: "TENICD_KHOADIEUTRI",name: 'TENICD_KHOADIEUTRI', index: 'TENICD_KHOADIEUTRI', width: 10, hidden: true},
                    {label: "TEN_CDHA",name: 'TEN_CDHA', index: 'TEN_CDHA', width: 200, hidden: true},
                    {label: "Tên chẩn đoán",name: 'TEN_CDHA_HT', index: 'TEN_CDHA_HT', width: 200
                        , formatter: function (cellvalue, options, rowObject) {
                            var iconSign = ""
                            if(!rowObject.KEYSIGN && rowObject.DA_CHAN_DOAN == 1) {
                                iconSign = "<p style='text-align: center;font-size: 20px;margin-bottom: 4px;margin-top:4px'><i class='fa fa-exclamation-circle text-danger'></i></p>"
                            }
                            if(rowObject.KEYSIGN) {
                                iconSign = "<i class='fa fa-check-square-o text-primary'></i>"
                                if(!rowObject.KEYSIGNCONFIRM) {
                                    iconSign += "<i class='fa fa-exclamation-triangle text-warning'></i>"
                                }
                                iconSign = "<p style='text-align: center;font-size: 20px;margin-bottom: 4px;margin-top:4px'>"+iconSign+"</p>"
                            }

                            return iconSign+'<p style="margin-bottom: 4px">' + rowObject.TEN_CDHA + '</p>';
                        }
                    },
                    {label: "Số lượng",name: 'SO_LUONG', index: 'SO_LUONG', width: 100},
                    {   label: "Đơn giá",
                        name: 'DON_GIA',
                        index: 'DON_GIA',
                        width: 100,
                        align: 'right',
                        formatter: 'integer',
                        formatoptions: {decimalSeparator: ".", thousandsSeparator: ",", decimalPlaces: 2}
                    },
                    {
                        label: "Thành tiền",
                        name: 'THANH_TIEN',
                        index: 'THANH_TIEN',
                        width: 100,
                        align: 'right',
                        formatter: 'integer',
                        formatoptions: {decimalSeparator: ".", thousandsSeparator: ",", decimalPlaces: 2}
                    },
                    {
                        label: "BHYT không chi",
                        name: 'BHYTKCHI',
                        index: 'BHYTKCHI',
                        width: 110,
                        align: 'center',
                        formatter: 'checkbox',
                        formatoptions: {value: 'true:false'}
                    },
                    {label: "Kết quả",name: 'KET_QUA', index: 'KET_QUA', width: 100 },
                    {label: "Người chỉ định",name: 'NGUOI_CHI_DINH', index: 'NGUOI_CHI_DINH', width: 100},
                    {label: "MABENHNHAN",name: 'MABENHNHAN', index: 'MABENHNHAN', hidden: true},
                    {label: "SO_PHIEU_CDHA",name: 'SO_PHIEU_CDHA', index: 'SO_PHIEU_CDHA', hidden: true},
                    {label: "MA_CDHA",name: 'MA_CDHA', index: 'MA_CDHA', hidden: true},
                    {label: "STT_BENHAN",name: 'STT_BENHAN', index: 'STT_BENHAN', hidden: true},
                    {label: "SOVAOVIEN",name: 'SOVAOVIEN', index: 'SOVAOVIEN', hidden: true},
                    {label: "SOVAOVIEN_DT",name: 'SOVAOVIEN_DT', index: 'SOVAOVIEN_DT', hidden: true},
                    {label: "MOTA_LOAI_CDHA",name: 'MOTA_LOAI_CDHA', index: 'MOTA_LOAI_CDHA', with: 50},
                    {label: "Bác sĩ điều trị",name: 'BAC_SI_DIEU_TRI', index: 'BAC_SI_DIEU_TRI', width: 150},
                    {label: "DA_CHAN_DOAN",name: 'DA_CHAN_DOAN', index: 'DA_CHAN_DOAN', hidden: true},
                    {label: "KEYSIGN",name: 'KEYSIGN', index: 'KEYSIGN', hidden: true},
                    {label: "KEYSIGNCONFIRM",name: 'KEYSIGNCONFIRM', index: 'KEYSIGNCONFIRM', hidden: true},
                ],
                caption: "Danh sách chẩn đoán hình ảnh",
                rowNum: 1000,
                grouping: true,
                groupingView: {
                    groupField: ["STT_DOTDIEUTRI", "NGAY_CHI_DINH", "STT_DIEUTRI"],
                    groupColumnShow: [false, false, false, false],
                    groupText: ['<b>Đợt: {0}</b>', '<b>Ngày lập: {0}</b>', '<b>Phiếu: {0}</b>'],
                    groupCollapse: false
                },
                onLeftClickRow: function (id) {
                },
                onRightClickRow: function (id) {

                }
            });
        }
        var listTTPT = $("#hsba_lichsu_list_ttpt")
        if(!listTTPT[0].grid) {
            listTTPT.jqGrid({
                datatype: "local",
                loadonce: false,
                height: 450,
                width: null,
                shrinkToFit: false,
                colNames: ["MABENHNHAN", "STT_DOTDIEUTRI", "MA_LOAI_DICHVU", "NGAY_CHI_DINH", "CHUYEN_KHOA",
                    "CHI_TIET_CHUYEN_KHOA", "STT_DIEUTRI", "SOBENHAN_TT", "SOBENHAN",
                    "ICD_KHOADIEUTRI", "TENICD_KHOADIEUTRI", "Tên dịch vụ", "Số lượng", "Đơn giá", "Thành tiền",
                    "BHYT không chi", "Kết quả", "Người chỉ định", "STT_BENHAN", "MA_DV", "SO_PHIEU_DICHVU", "SOVAOVIEN", "SOVAOVIEN_DT",
                    "Bác sĩ điều trị"
                ],
                colModel: [
                    {name: 'MABENHNHAN', index: 'MABENHNHAN', hidden: true},
                    {name: 'STT_DOTDIEUTRI', index: 'STT_DOTDIEUTRI', width: 10},
                    {name: 'MA_LOAI_DICHVU', index: 'MA_LOAI_DICHVU', hidden: true},
                    {name: 'NGAY_CHI_DINH', index: 'NGAY_CHI_DINH', width: 10},
                    {name: 'CHUYEN_KHOA', index: 'CHUYEN_KHOA', hidden: true},
                    {name: 'CHI_TIET_CHUYEN_KHOA', index: 'CHI_TIET_CHUYEN_KHOA', hidden: true},
                    {name: 'STT_DIEUTRI', index: 'STT_DIEUTRI', width: 10},
                    {name: 'SOBENHAN_TT', index: 'SOBENHAN_TT', width: 10, hidden: true},
                    {name: 'SOBENHAN', index: 'SOBENHAN', width: 10, hidden: true},
                    {name: 'ICD_KHOADIEUTRI', index: 'ICD_KHOADIEUTRI', width: 10, hidden: true},
                    {name: 'TENICD_KHOADIEUTRI', index: 'TENICD_KHOADIEUTRI', width: 10, hidden: true},
                    {name: 'TEN_DV', index: 'TEN_DV', width: 200},
                    {name: 'SO_LUONG', index: 'SO_LUONG', width: 100},
                    {
                        name: 'DON_GIA',
                        index: 'DON_GIA',
                        width: 100,
                        align: 'right',
                        formatter: 'integer',
                        formatoptions: {decimalSeparator: ".", thousandsSeparator: ",", decimalPlaces: 2}
                    },
                    {
                        name: 'THANH_TIEN',
                        index: 'THANH_TIEN',
                        width: 100,
                        align: 'right',
                        formatter: 'integer',
                        formatoptions: {decimalSeparator: ".", thousandsSeparator: ",", decimalPlaces: 2}
                    },
                    {
                        name: 'BHYTKCHI',
                        index: 'BHYTKCHI',
                        width: 110,
                        align: 'center',
                        formatter: 'checkbox',
                        formatoptions: {value: 'true:false'}
                    },
                    {name: 'KET_QUA', index: 'KET_QUA', width: 100},
                    {name: 'NGUOI_CHI_DINH', index: 'NGUOI_CHI_DINH', width: 100},
                    {name: 'STT_BENHAN', index: 'STT_BENHAN', hidden: true},
                    {name: 'MA_DV', index: 'MA_DV', hidden: true},
                    {name: 'SO_PHIEU_DICHVU', index: 'SO_PHIEU_DICHVU', hidden: true},
                    {name: 'SOVAOVIEN', index: 'SOVAOVIEN', hidden: true},
                    {name: 'SOVAOVIEN_DT', index: 'SOVAOVIEN_DT', hidden: true},
                    {name: 'BAC_SI_DIEU_TRI', index: 'BAC_SI_DIEU_TRI', width: 150}
                ],
                caption: "Danh sách thủ thuật phẫu thuật",
                rowNum: 1000,
                grouping: true,
                groupingView: {
                    groupField: ["STT_DOTDIEUTRI", "NGAY_CHI_DINH", "STT_DIEUTRI"],
                    groupColumnShow: [false, false, false, false],
                    groupText: ['<b>Đợt: {0}</b>', '<b>Ngày lập: {0}</b>', '<b>Phiếu: {0}</b>'],
                    groupCollapse: false
                },
                onLeftClickRow: function (id) {
                },
                onRightClickRow: function (id1) {
                }
            });
        }
    }

    function load_benhan_theosttbenhan(STT_BENHAN, MABENHNHAN) {
        var url = "hsba_laybenhan_theosttbenhan?dvtt="+
            singletonObject.dvtt
            +"&sttbenhan=" + STT_BENHAN + "&mabenhnhan=" + MABENHNHAN;
        $("#hsba_list_dskham").jqGrid('setGridParam', {datatype: 'json', url: url}).trigger('reloadGrid');
    }
    function loadLichsuDsThuocVattuTonghop(maloai, STT_BENHAN, MA_BENH_NHAN) {
        var url = 'cmu_list_HSBA_DSTHUOC_TONGHOP?url='
            + convertArray([singletonObject.dvtt, STT_BENHAN, MA_BENH_NHAN, maloai])
        loadDataGridGroupBy($("#hsba_lichsu_list_thuoc"), url);
    }

    function loadLichsuDsXetnghiemTonghop(STT_BENHAN, MA_BENH_NHAN) {
        var url = 'cmu_list_HSBA_DSXN_TONGHOP?url='
            + convertArray([singletonObject.dvtt, STT_BENHAN, MA_BENH_NHAN]);
        loadDataGridGroupBy($("#hsba_lichsu_list_xetnghiem"), url);
    }

    function loadLichsuDsCDHATonghop(STT_BENHAN, MA_BENH_NHAN) {
        var url = 'cmu_list_HSBA_DSCDHA_TONGHOP?url='
            + convertArray([singletonObject.dvtt, STT_BENHAN,MA_BENH_NHAN])
        loadDataGridGroupBy($("#hsba_lichsu_list_chandoanhinhanh"), url);
    }

    function loadLichsuDsTTPTTonghop(STT_BENHAN, MA_BENH_NHAN) {
        var url = 'cmu_list_HSBA_DSTTPT_TONGHOP?url='
            + convertArray([singletonObject.dvtt, STT_BENHAN, MA_BENH_NHAN, '-1'])
        loadDataGridGroupBy($("#hsba_lichsu_list_ttpt"), url);
    }

    function loadformtntt(){
        var tngt = $("#tthc_tntt").val();
        if (tngt != "0") {
            $("#modalFormthongtintntt").modal("show");
            $("#tthc_mayte_tntt").val(thongtinhsba.thongtinbn.MA_BENH_NHAN);
            $("#tthc_hoten_tntt").val($("#tthc_hoten").val());
            $("#tthc_sobhyt_tntt").val($("#tthc_sobhyt").val());
            $("#tthc_doituong_tntt").val($("#tthc_sobhyt_doituong").val());
            $("#tthc_tuoi_tntt").val(thongtinhsba.thongtinbn.TUOI);
            $("#tthc_tlmg_tntt").val($("#tthc_bhyt_tilebhyt").val());
            $("#tthc_gioitinh_tntt").val($("#tthc_gioitinh").val() == '1'? "true": "false");
            $("#tthc_diachi_tntt").val($("#tthc_diachi").val());
            $("#tthc_makhambenh_tntt").val("");
            $("#tthc_icd_tntt").val($("#tthc_icdbenhchinhnv").val());
            $("#tthc_tntt_nghenghiep").val($("#tthc_nghenghiep").val()).trigger("change");
            $("#tthc_mabenhly_tntt").val("");
            $("#tthc_cbicd_tntt").val($("#tthc_tenbenhchinhnv").val());
            $("#tthc_benhphu_tntt").val($("#tthc_icdbenhphunv").val());
            $("#tthc_noicapthe_tntt").val($("#hsba_noidangky").text());
            $("#tthc_dacdiem_tntt").val("");
            $("#tthc_nguyennhan_tntt").val(tngt);
            $("input[name=tthc_hinhthuc_tntt][value=" + 1 + "]").prop('checked', true);
            $("input[name=tthc_xacnhancongan_tntt][value=" + 1 + "]").prop('checked', true);
            //tainangiaothong_select($("#sovaovien").val(), dvtt);
            //tainangiaothong_dialog.dialog("option", "title", "TNTT: " + $("#tthc_tntt option:selected").text());
            $("#tthc_tntt option:selected").text() == 'Tai nạn giao thông' ? modeg = 1 : modeg = 2;
            if (modeg == 2) {
                $("#tthc_ptgayra").hide();
                $("#tthc_ttbosung").hide();
                $("#tthc_ttmubh").hide();
                $("#tthc_tntt_tuvong").hide();
                $("#tthc_loai_chanthuong").hide();
                $("#tthc_tt_chanthuong").hide();
            } else {
                $("#tthc_ptgayra").show();
                $("#tthc_ttbosung").show();
                $("#tthc_ttmubh").show();
                $("#tthc_tntt_tuvong").show();
                $("#tthc_loai_chanthuong").show();
                $("#tthc_tt_chanthuong").show();
            }
            $("#tthc_tntt option:selected").text() == 'Ngộ độc các loại' ? modeg1 = 1 : modeg1 = 2;
            if (modeg1 == 2) {
                $("#tthc_ttngodoc1").hide();
                $("#tthc_ttngodoc2").hide();
            } else {
                $("#tthc_ttngodoc1").show();
                $("#tthc_ttngodoc2").show();
            }

            $.get("cmu_list_LAY_CHITIET_TNTT?url="+convertArray([singletonObject.dvtt,thongtinhsba.thongtinbn.SOVAOVIEN])).done(function(data) {
                //singletonObject.danhsachthutruong = data;
                $("#tthc_ngaygiongoai_tntt").val(data[0].NGAYGIOTNTT);
                $("#tthc_tntt_diadiemxayra").val(data[0].MA_DIA_DIEM_NGUYEN_NHAN).trigger("change");
                $("#tthc_tntt_bophanbithuong").val(data[0].MA_BOPHAN_BI_THUONG).trigger("change");
                $("#tthc_tntt_dienbiensautainan").val(data[0].MA_DIENBIEN_SAUTAINAN).trigger("change");
                $("#tthc_ptvc_cskcb").val(data[0].PTVC_CSKCB);
                $("#tthc_tntt_cb_tudieutri").prop("checked",data[0].DT_BANDAU_TUDIEUTRI==1?true:false);
                $("#tthc_tntt_cb_ttyt").prop("checked",data[0].DT_BANDAU_COSOKCB_TTYT_BVHUYEN==1?true:false);
                $("#tthc_tntt_cb_doisocapcuu").prop("checked",data[0].DT_BANDAU_DOISOCAPCUU==1?true:false);
                $("#tthc_tntt_cb_bvtinh").prop("checked",data[0].DT_BANDAU_COSOKCB_BVTINH==1?true:false);
                $("#tthc_tntt_cb_cosotunhan").prop("checked",data[0].DT_BANDAU_COSOKCB_TUNHAN==1?true:false);
                $("#tthc_tntt_cb_bvtrunguong").prop("checked",data[0].DT_BANDAU_COSOKCB_BVTW==1?true:false);
                $("#tthc_tntt_cb_tramyte").prop("checked",data[0].DT_BANDAU_COSOKCB_TYT==1?true:false);
                $("#tthc_tntt_cb_khac").prop("checked",data[0].DT_BANDAU_COSOKCB_KHAC==1?true:false);
                $('input[name="tthc_hinhthuc_tntt"][value="' + data[0].HINH_THUC_VAO_VIEN + '"]').prop('checked', true);
                $('input[name="tthc_xacnhancongan_tntt"][value="' + data[0].XAC_NHAN + '"]').prop('checked', true);
                $('input[name="tthc_tinhtrang_tntt"][value="' + data[0].MACCHET + '"]').prop('checked', true);
                $('input[name="tthc_tinhtrang_tuvong"][value="' + data[0].TINHTRANGTUVONG + '"]').prop('checked', true);
                $("#tthc_tntt_phuongtiengaytn").val(data[0].PHUONGTIENGAYTAINAN).trigger("change");
                $("#tthc_tntt_cb_tudieutri2").prop("checked",data[0].COSUDUNGRUOUBIA==1?true:false);
                $("#tthc_tntt_cb_tudieutri3").prop("checked",data[0].COCONTRONGMAU==1?true:false);
                $("#tthc_tntt_cb_tudieutri4").prop("checked",data[0].CODOIMUBAOHIEM==1?true:false);
                $("#tthc_tntt_cb_tudieutri7").prop("checked",data[0].DOIMUBIVO==1?true:false);
                $("#tthc_tntt_cb_tudieutri6").prop("checked",data[0].DOIMUKHONGCOQUAI==1?true:false);
                $("#tthc_tntt_cb_tudieutri5").prop("checked",data[0].DOIMUKHONGRONGUONGOC==1?true:false);
                $("#tthc_tntt_ngodoc_cb_1").prop("checked",data[0].NGODOC_HOACHAT==1?true:false);
                $("#tthc_tntt_ngodoc_cb_2").prop("checked",data[0].NGODOC_THUCPHAM==1?true:false);
                $("#tthc_tntt_ngodoc_cb_3").prop("checked",data[0].NGODOC_DONGVAT==1?true:false);
                $("#tntt_ngodoc_cb_4").prop("checked",data[0].NGODOC_THUCVAT==1?true:false);
                $("#tntt_ngodoc_cb_5").prop("checked",data[0].NGODOC_RUOU==1?true:false);
                $("#tntt_ngodoc_cb_6").prop("checked",data[0].NGODOC_KHAC==1?true:false);
                $("#tthc_tntt_phanloaibithuong").val(data[0].PHANLOAICHANTHUONG).trigger("change");
                $("#tthc_tntt_giaothong_01").prop("checked",data[0].CHANTHUONGSONAO==1?true:false);
                $("#tthc_tntt_giaothong_02").prop("checked",data[0].CHANTHUONCOTSONG==1?true:false);
                $("#tthc_tntt_giaothong_03").prop("checked",data[0].MOCAPCUU==1?true:false);
            })
        }
    }

    function capnhat_tthc_tntt() {
        var mayte = thongtinhsba.thongtinbn.MA_BENH_NHAN;
        var dvtt = singletonObject.dvtt;
        var sovaovien = thongtinhsba.thongtinbn.SOVAOVIEN;
        var matntt = $("#tthc_tntt").val();
        $.post("cmu_post_NOT_CAPNHAT_TRANGTHAI_TNTT", {
            url: [mayte,dvtt,sovaovien,matntt].join("```")
        }).done(function () {
            console.log("Cập nhật TNTT thành công!");
        }).fail(function () {
            console.log("Cập nhật TNTT thất bại!");
        });
    }
})