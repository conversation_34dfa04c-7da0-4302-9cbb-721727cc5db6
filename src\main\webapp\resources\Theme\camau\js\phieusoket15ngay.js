thongTinPhieuSoKetMoiNhat = {};
thongTinPhieuSoKetTruocChinhSua = {};
thongTinPhieuSoKetBoSung = {
    bsKySoKet: {}
};

var keyLogPSK = {
    DIEN_BIEN: '<PERSON>ễ<PERSON> biến lâm sàng trong đợt điều trị',
    XN_CANLAMSANG: 'Xét nghiệm cận lâm sàng',
    QUATRINH_DIEUTRI: 'Quá trình điều trị',
    DANHGIA_KETQUA: 'Đánh giá kết quả',
    HUONG_DIEUTRI: 'Hướng điều trị tiếp và tiên lượng'
};
var pskDuLieuBanDau = {};

function loadDsPhieusoketTonghop() {
    $("#list_phieusoket").jqGrid('clearGridData');
    var url = 'cmu_getlist?url=' + convertArray([thongtinhsba.thongtinbn.STT_BENHAN, thongtinhsba.thongtinbn.SOVAOVIEN, -1, singletonObject.dvtt, 'CMU_SELECT_DS_DOTSOKET']);
    $.get(url).done(function(data){
        if (data && data.length > 0) {
            $("#data_phieusoket15ngay").html('Phiếu số: ' + data[0].DOT_SOKET + ' - ' + data[0].NGAY_TAO);
            thongTinPhieuSoKetMoiNhat = data[0];
            $("#list_phieusoket").jqGrid('setGridParam', {
                datatype: 'local',
                data: data
            }).trigger("reloadGrid");
            if (data[0].KEYSIGN_TRUONGKHOA || data[0].KEYSIGN_BACSI){
                $('#edit_single_phieusoket15ngay').css('visibility', 'hidden');
                $('#delete_single_phieusoket15ngay').css('visibility', 'hidden');
            }else{
                $('#handle_icon_phieusoket15ngay').css('visibility', 'unset');
                $('#view_single_phieusoket15ngay').css('visibility', 'unset');
                $('#edit_single_phieusoket15ngay').css('visibility', 'unset');
                $('#delete_single_phieusoket15ngay').css('visibility', 'unset');
            }
        } else  {
            $("#data_phieusoket15ngay").html('Không có dữ liệu');
            $('#handle_icon_phieusoket15ngay').css('visibility', 'hidden');
        }
    });
}

function loadInfoPhieuSoKet(data){
    $("#sk15_dotsoket").val(data.DOT_SOKET);
    $("#sk15_dienbienlamsang").val(data.DIEN_BIEN);
    $("#sk15_xetnghiem").val(data.XN_CANLAMSANG.trim() == ';' ? '' : data.XN_CANLAMSANG);
    $("#sk15_quatrinhdieutri").val(data.QUATRINH_DIEUTRI);
    $("#sk15_danhgiaketqua").val(data.DANHGIA_KETQUA);
    $("#sk15_huongdieutri").val(data.HUONG_DIEUTRI);
    $("#sk15_ngaytao").val(data.NGAY_TAO);
    thongTinPhieuSoKetTruocChinhSua = data;
    pskDuLieuBanDau = data;
}

function loadDsXetnghiemdathuchien() {
    var url = 'cmu_list_CMU_NOI_XETNGHIEMDATHUCHIEN?url='
        + convertArray([thongtinhsba.thongtinbn.STT_BENHAN, thongtinhsba.thongtinbn.SOVAOVIEN, singletonObject.dvtt]);
    loadDataGridGroupBy($("#list_sks15_xn"), url);
}

function loadDsCDHAdathuchien() {
    var url = 'cmu_list_CMU_NOI_CDHADATHUCHIEN?url='
        + convertArray([thongtinhsba.thongtinbn.STT_BENHAN, thongtinhsba.thongtinbn.SOVAOVIEN, singletonObject.dvtt]);
    loadDataGridGroupBy($("#list_sks15_cdha"), url);
}

function loadDsToDieuTri_SK15() {
    var url = 'cmu_list_CMU_NOT_DSTDTD_INFO?url='
        + convertArray([thongtinhsba.thongtinbn.STT_BENHAN, thongtinhsba.thongtinbn.STT_DOTDIEUTRI, singletonObject.dvtt]);
    loadDataGridGroupBy($("#list_sks15_tdt"), url);
}

function inPhieuSoKet(dot_soket){
    var arrTemp = [];
    getFilesign769("PHIEU_NOITRU_SOKET15NGAY_TRUONGKHOA", dot_soket, -1, singletonObject.dvtt,
        thongtinhsba.thongtinbn.SOVAOVIEN, thongtinhsba.thongtinbn.SOVAOVIEN_DT, -1, function(dataKySo) {
            if (dataKySo.length > 0) {
                arrTemp.push(dataKySo[0])
            }
        });
    getFilesign769("PHIEU_NOITRU_SOKET15NGAY_BACSI", dot_soket, -1, singletonObject.dvtt,
        thongtinhsba.thongtinbn.SOVAOVIEN, thongtinhsba.thongtinbn.SOVAOVIEN_DT, -1, function(dataKySo) {
            if (dataKySo.length > 0) {
                arrTemp.push(dataKySo[0])
            }
        });
    var maxCreateDate = null;
    var maxCreateDataObject = null;
    if(arrTemp.length > 0) {
        $.each(arrTemp, function(index, dataObject) {
            var createDate = moment(dataObject.CREATE_DATE_STRING, "DD/MM/YYYY HH:mm:ss");
            if (maxCreateDate === null || createDate.isAfter(maxCreateDate)) {
                maxCreateDate = createDate;
                maxCreateDataObject = dataObject;
            }
        });
        getCMUFileSigned769(maxCreateDataObject.KEYMINIO, "pdf")
    } else {
        if (dot_soket != "") {
            var arr = [thongtinhsba.thongtinbn.STT_BENHAN, thongtinhsba.thongtinbn.SOVAOVIEN,
                dot_soket, singletonObject.dvtt, thongtinhsba.thongtinbn.TEN_BENH_NHAN, thongtinhsba.thongtinbn.TUOI
                , thongtinhsba.thongtinbn.GIOI_TINH == 1 ? 'true' : 'false', thongtinhsba.thongtinbn.DIA_CHI, thongtinhsba.thongtinbn.TEN_PHONGBAN
                , thongtinhsba.thongtinbn.ICD_HT ? thongtinhsba.thongtinbn.ICD_HT: " "];
            var url = "vlg_inphieusoket?url=" + convertArray(arr) + '&viewPDF=1';
            previewPdfIframe(url, 'sk15_frame_inphieusoket')
            $("#modalInphieusoket").modal("show");
        }
    }
}

function btnGetXetNghiem(id) {
    var ret = $("#list_sks15_xn").jqGrid('getRowData', id);
    var xn = "";
    if(ret.ID_XNCHA == "-1") {
        var allData = $('#list_sks15_xn').getGridParam('data');
        var xncon = "";
        for (var i = 0; i < allData.length; i++) {
            if (allData[i].ID_XNCHA == ret.MA_XETNGHIEM) {
                xncon += allData[i].TEN_XETNGHIEM + ": " + allData[i].KET_QUA + '; ';
            }
        }
        if (xncon == "") {
            xn = ret.TEN_XETNGHIEM + ": " + ret.KET_QUA;
        } else {
            xn = ret.TEN_XETNGHIEM + " [";
            xncon = xncon;
            xn += xncon;
            xn += "]";
        }
    } else {
        xn = ret.TEN_XETNGHIEM + ": " + ret.KET_QUA;
    }
    var xnfull = $("#sk15_xetnghiem").val()  == "" ? xn :  $("#sk15_xetnghiem").val()  + '; ' + xn;
    $("#sk15_xetnghiem").val(xnfull);
}

function btnGetCDHA(id) {
    var ret = $("#list_sks15_cdha").jqGrid('getRowData', id);
    var xn = ret.TEN_CDHA + ": " + ret.MO_TA;
    var xnfull = $("#sk15_xetnghiem").val()  == "" ? xn :  $("#sk15_xetnghiem").val()  + '; ' + xn;
    $("#sk15_xetnghiem").val(xnfull);
}

function btnGetToDieuTri(id) {
    var ret = $("#list_sks15_tdt").jqGrid('getRowData', id);
    $("#sk15_dienbienlamsang").val(ret.DIEN_BIEN_BENH);
}

function initModalPhieuoKet(action, data) {
    $('#modalPhieusoket').modal('show');
    addTextTitleModal('titlePhieusoket', "Phiếu sơ kết 15 ngày");
    if(action === 'THEM') {
        $('#action_luu_phieusoket').attr('data-action', 'THEM');
        $('#action_luu_phieusoket').html('<i class="fa fa-floppy-o"></i> Lưu phiếu');
    } else {
        loadInfoPhieuSoKet(data);
        $('#action_luu_phieusoket').attr('data-action', 'CAP_NHAT');
        $('#action_luu_phieusoket').html('<i class="fa fa-floppy-o"></i> Cập nhật');
    }
}

function xoaPhieuSoKet(ret){
    pskDuLieuBanDau = ret
    $.confirm({
        title: 'Xác nhận!',
        type: 'orange',
        content: 'Bạn có chắc chắn muốn xóa phiếu sơ kết này?',
        buttons: {
            warning: {
                btnClass: 'btn-warning',
                text: "Tiếp tục",
                action: function(){
                    showLoaderIntoWrapId("phieusoket15ngay-wrap");
                    var arr = [ret.DVTT, ret.STT_BENHAN, ret.SOVAOVIEN, ret.DOT_SOKET];
                    var url = "vlg_xoa_dotsoket";
                    $.post(url, {url: convertArray(arr)}).done(function (dt) {
                        // var data_log = {
                        //     SOVAOVIEN: thongtinhsba.thongtinbn.SOVAOVIEN,
                        //     LOAI: "PHIEUSOKET15NGAYDIEUTRI",
                        //     NOIDUNGBANDAU: "Xóa sơ kết 15 ngày điều trị đợt : " + ret.DOT_SOKET + " của bệnh nhân: " + thongtinhsba.thongtinbn.TEN_BENH_NHAN,
                        //     NOIDUNGMOI: "Xóa mới sơ kết 15 ngày điều trị đợt : " + ret.DOT_SOKET + " của bệnh nhân: " + thongtinhsba.thongtinbn.TEN_BENH_NHAN,
                        //     USERID: singletonObject.userId,
                        //     ACTION: "DELETE"
                        // }
                        // luuLogHSBATheoBN(data_log);

                        luuLogPSK(3);
                        notifiToClient("Green", "Xóa thành công")
                        loadDsPhieusoketTonghop()
                    }).fail(function() {
                        jAlert("Xóa thất bại")
                        notifiToClient("Red", "Xóa thất bại")
                    }).always(function() {
                        hideLoaderIntoWrapId("phieusoket15ngay-wrap");
                    })
                }
            },
            cancel: function () {

            }
        }
    });
}

function luuLogPSK(type) {
    let formPSKData = getPSKData(0);

    if (type == 1) {
        luuLogHSBATheoBN({
            SOVAOVIEN: thongtinhsba.thongtinbn.SOVAOVIEN,
            LOAI: LOGHSBALOAI.PHIEUSOKET15NGAYDIEUTRI.KEY,
            NOIDUNGBANDAU: '',
            NOIDUNGMOI: getStringPSK(formPSKData),
            USERID: singletonObject.userId,
            ACTION: LOGHSBAACTION.INSERT.KEY,
        })
    } else if (type == 2) {
        if (pskDuLieuBanDau) {
            let noiDungBanDauEdit = getPSKData(1);
            if (noiDungBanDauEdit) {
                let  diff = findDifferencesBetweenObjects(noiDungBanDauEdit, formPSKData)
                if (Object.keys(diff).length > 0) {
                    let noiDungThayDoi = {};
                    Object.keys(diff).forEach(key => noiDungThayDoi[key] = diff[key][1]);

                    luuLogHSBATheoBN({
                        SOVAOVIEN: thongtinhsba.thongtinbn.SOVAOVIEN,
                        LOAI: LOGHSBALOAI.PHIEUSOKET15NGAYDIEUTRI.KEY,
                        NOIDUNGBANDAU: getStringPSK(noiDungBanDauEdit),
                        NOIDUNGMOI: getStringPSK(noiDungThayDoi),
                        USERID: singletonObject.userId,
                        ACTION: LOGHSBAACTION.EDIT.KEY,
                    })
                }
            }
        }
    } else if (type == 3) {
        if (pskDuLieuBanDau) {
            let noiDungBanDauEdit = getPSKData(1);
            if (noiDungBanDauEdit) {
                luuLogHSBATheoBN({
                    SOVAOVIEN: thongtinhsba.thongtinbn.SOVAOVIEN,
                    LOAI: LOGHSBALOAI.PHIEUSOKET15NGAYDIEUTRI.KEY,
                    NOIDUNGBANDAU: getStringPSK(noiDungBanDauEdit),
                    NOIDUNGMOI: '',
                    USERID: singletonObject.userId,
                    ACTION: LOGHSBAACTION.DELETE.KEY,
                })
            }
        }
    }
}

function getPSKData (type) {
    let rsData = {}
    Object.keys(keyLogPSK).forEach(key => rsData[key] = '');
    if (type == 0) {
        rsData = {
            DIEN_BIEN: $("#sk15_dienbienlamsang").val(),
            XN_CANLAMSANG: $("#sk15_xetnghiem").val(),
            QUATRINH_DIEUTRI: $("#sk15_quatrinhdieutri").val(),
            DANHGIA_KETQUA: $("#sk15_danhgiaketqua").val(),
            HUONG_DIEUTRI: $("#sk15_huongdieutri").val()
        }
    } else if (type == 1) {
        Object.keys(rsData).forEach(key => rsData[key] = pskDuLieuBanDau[key]);
    }

    return rsData
}

function getStringPSK(obj) {
    let string = '';
    let objLength = Object.keys(obj).length;
    Object.keys(obj).forEach((key, index) => {
        if (index != objLength - 1)
            string += `${keyLogPSK[key]}: ${obj[key]}; `
        else
            string += `${keyLogPSK[key]}: ${obj[key]}.`
    });
    return string;
}

function chonBSKySoKet15Ngay(data, callback) {
    Formio.createForm(document.getElementById('chonBSKySoKet15Ngay'),
        getJSONObjectForm([
            {
                "label": "BSKYSOKET15NGAY",
                "columns": [
                    {
                        "components": [
                            {
                                "label": "Trưởng khoa",
                                "key": "TRUONGKHOA",
                                "type": "select",
                                "customClass": "mr-2",
                                others: {
                                    "dataSrc": "json",
                                    template: "<span>{{ item.tennhanvien }}</span>",
                                }
                            }
                        ],
                        "width": 6,
                        "size": "md",
                    },
                    {
                        "components": [
                            {
                                "label": "Bác sĩ điều trị",
                                "key": "BSDIEUTRI",
                                "type": "select",
                                others: {
                                    "dataSrc": "json",
                                    template: "<span>{{ item.tennhanvien }}</span>",
                                }
                            }
                        ],
                        "width": 6,
                        "size": "md",
                    },
                ],
                "customClass": "mr-0 ml-0",
                "type": "columns",
            },
        ])
    ).then(function(form) {
        thongTinPhieuSoKetBoSung.bsKySoKet = form;
        let truongKhoa = thongTinPhieuSoKetBoSung.bsKySoKet.getComponent("TRUONGKHOA").component;
        truongKhoa.data.json = singletonObject.danhsachtatcanhanvien;
        let bsDieuTri = thongTinPhieuSoKetBoSung.bsKySoKet.getComponent("BSDIEUTRI").component;
        bsDieuTri.data.json = singletonObject.danhsachtatcanhanvien;
        let initData = { data: {} };
        if (data) {
            initData.data = data;
        }
        else {
            initData.data = {}
        }

        thongTinPhieuSoKetBoSung.bsKySoKet.setSubmission(initData).then(function () {
        }).catch(function(err) {
            notifiToClient('Red', 'Lỗi cài đặt dữ liệu'); console.error(err);
        });

        callback && callback();
    });
}

function capNhatBoSungPhieuSoKet(ngaytao, dotsoket) {
    let dataBoSung = {
        ...thongTinPhieuSoKetBoSung.bsKySoKet.submission.data
    }
    $.post('cmu_post', {
        url: [
            singletonObject.dvtt,
            thongtinhsba.thongtinbn.SOVAOVIEN,
            ngaytao, dotsoket,
            JSON.stringify(dataBoSung),
            'CAPNHAT_DOT_SOKET_NGAY'
        ].join('```')
    }).done(function (dt) { })
}

$(function() {
    $("#sk15_tabs").tabs();

    $("#list_phieusoket").jqGrid({
        datatype: "local",
        data: [],
        loadonce: true,
        height: 400,
        width: null,
        shrinkToFit: false,
        colModel: [
            {
                name: "KYSO",
                label: "Ký số trưởng khoa",
                align: 'left',
                width: 100,
                formatter: function (cellValue, options, rowData) {
                    if (rowData.KEYSIGN_TRUONGKHOA) {
                        return '<span class="cellWithoutBackground" style="font-weight:bold ;color: green">Đã ký</span>';
                    } else {
                        return '<span class="cellWithoutBackground" style="font-weight:bold ;color: red">Chưa ký</span>';
                    }
                }
            },
            {
                name: "KYSO",
                label: "Ký số bác sĩ",
                align: 'left',
                width: 100,
                formatter: function (cellValue, options, rowData) {
                    if (rowData.KEYSIGN_BACSI) {
                        return '<span class="cellWithoutBackground" style="font-weight:bold ;color: green">Đã ký</span>';
                    } else {
                        return '<span class="cellWithoutBackground" style="font-weight:bold ;color: red">Chưa ký</span>';
                    }
                }
            },
            {label: "Phiếu", name: 'DOT_SOKET', index: 'DOT_SOKET', width: 30},
            {label: "Số BA", name: 'STT_BENHAN', index: 'STT_BENHAN', width: 10, hidden: true},
            {label: "DVTT",name: 'DVTT', index: 'DVTT', width: 10, hidden: true},
            {label: "SOVAOVIEN",name: 'SOVAOVIEN', index: 'SOVAOVIEN', width: 10, hidden: true},
            {label: "Diễn biến", name: 'DIEN_BIEN', index: 'DIEN_BIEN', width: 200 ,cellattr: function (rowId, tv, rawObject, cm, rdata) {return 'style="white-space: normal;padding: 2px;"';}},
            {label: "Xét nghiệm lâm sàng", name: 'XN_CANLAMSANG', index: 'XN_CANLAMSANG', width: 200 ,cellattr: function (rowId, tv, rawObject, cm, rdata) {return 'style="white-space: normal;padding: 2px;"';}},
            {label: "Quá trình điều trị", name: 'QUATRINH_DIEUTRI', index: 'QUATRINH_DIEUTRI', width: 200 ,cellattr: function (rowId, tv, rawObject, cm, rdata) {return 'style="white-space: normal;padding: 2px;"';}},
            {label: "Đánh giá kết quả", name: 'DANHGIA_KETQUA', index: 'DANHGIA_KETQUA', width: 200 ,cellattr: function (rowId, tv, rawObject, cm, rdata) {return 'style="white-space: normal;padding: 2px;"';}},
            {label: "Hướng điều trị", name: 'HUONG_DIEUTRI', index: 'HUONG_DIEUTRI', width: 200, cellattr: function (rowId, tv, rawObject, cm, rdata) {return 'style="white-space: normal;padding: 2px;"';}},
            {label: "Người tạo",name: 'NGUOI_TAO', index: 'NGUOI_TAO', width: 120, cellattr: function (rowId, tv, rawObject, cm, rdata) {return 'style="white-space: normal;padding: 2px;"';}},
            {label: "Ngày tạo", name: 'NGAY_TAO', index: 'NGAY_TAO', width: 130},
            {name: "KEYSIGN_TRUONGKHOA", label: "KEYSIGN", align: 'center', width: 150, hidden: true},
            {name: "KEYSIGN_BACSI", label: "KEYSIGN", align: 'center', width: 150, hidden: true},
            {name: "DATA_BOSUNG", label: "DATA_BOSUNG", hidden: true},
        ],
        rowNum: 1000000,
        caption: "Danh sách phiếu sơ kết",
        onRightClickRow: function (id1) {
            if (id1) {
                $('#list_phieusoket').jqGrid('setSelection', id1);
                var ret = getThongtinRowSelected("list_phieusoket");
                var items = {
                }
                $.contextMenu('destroy', '#list_phieusoket tr');
                if (ret.KEYSIGN_BACSI && ret.KEYSIGN_TRUONGKHOA){
                    var daybs, dayld;
                    getFilesign769("PHIEU_NOITRU_SOKET15NGAY_BACSI", ret.DOT_SOKET, -1, singletonObject.dvtt,
                        thongtinhsba.thongtinbn.SOVAOVIEN, thongtinhsba.thongtinbn.SOVAOVIEN_DT, -1, function(dataKySo) {
                            if (dataKySo.length > 0) {
                                daybs = moment(dataKySo[0].CREATE_DATE_STRING, "DD/MM/YYYY HH:mm:ss");
                            }
                        });
                    getFilesign769("PHIEU_NOITRU_SOKET15NGAY_TRUONGKHOA", ret.DOT_SOKET, -1, singletonObject.dvtt,
                        thongtinhsba.thongtinbn.SOVAOVIEN, thongtinhsba.thongtinbn.SOVAOVIEN_DT, -1, function(dataKySo) {
                            if (dataKySo.length > 0) {
                                dayld = moment(dataKySo[0].CREATE_DATE_STRING, "DD/MM/YYYY HH:mm:ss");
                            }
                        });
                    if (daybs.isAfter(dayld)){
                        items = {
                            "xem": {name: '<p><i class="fa fa-eye text-primary" aria-hidden="true"></i> Xem</p>'},
                            "huykysobs": {name: '<p><i class="fa fa-key text-danger" aria-hidden="true"></i> Hủy ký số Bác sĩ</p>'},
                            ...items
                        }
                    }else{
                        items = {
                            "xem": {name: '<p><i class="fa fa-eye text-primary" aria-hidden="true"></i> Xem</p>'},
                            "huykysotk": {name: '<p><i class="fa fa-key text-danger" aria-hidden="true"></i> Hủy ký số Trưởng khoa</p>'},
                            ...items
                        }
                    }
                }else if (ret.KEYSIGN_BACSI){
                    items = {
                        "xem": {name: '<p><i class="fa fa-eye text-primary" aria-hidden="true"></i> Xem</p>'},
                        "huykysobs": {name: '<p><i class="fa fa-key text-danger" aria-hidden="true"></i> Hủy ký số Bác sĩ</p>'},
                        "kysotk": {name: '<p><i class="fa fa-key text-danger" aria-hidden="true"></i> Ký số Trưởng khoa</p>'},
                        ...items
                    }
                }else if (ret.KEYSIGN_TRUONGKHOA){
                    items = {
                        "xem": {name: '<p><i class="fa fa-eye text-primary" aria-hidden="true"></i> Xem</p>'},
                        "huykysotk": {name: '<p><i class="fa fa-key text-danger" aria-hidden="true"></i> Hủy ký số Trưởng khoa</p>'},
                        "kysobs": {name: '<p><i class="fa fa-key text-danger" aria-hidden="true"></i> Ký số Bác sĩ</p>'},
                        ...items
                    }
                }else{
                    items = {
                        "kysobs": {name: '<p><i class="fa fa-key text-success" aria-hidden="true"></i> Ký số Bác sĩ</p>'},
                        "kysotk": {name: '<p><i class="fa fa-key text-danger" aria-hidden="true"></i> Ký số Trưởng khoa</p>'},
                        "xem": {name: '<p><i class="fa fa-eye text-primary" aria-hidden="true"></i> Xem</p>'},
                        "sua": {name: '<i class="fa fa-pencil-square-o text-primary" aria-hidden="true"></i> Sửa</p>'},
                        "xoa": {name: '<p><i class="fa fa-trash-o text-danger" aria-hidden="true"></i> Xóa</p>'},
                        ...items
                    }
                }
                $.contextMenu({
                    selector: '#list_phieusoket tr',
                    callback: function (key, options) {
                        var id = $("#list_phieusoket").jqGrid('getGridParam', 'selrow');
                        var ret = $("#list_phieusoket").jqGrid('getRowData', id);
                        if (key == "kysobs") {
                            var arr = [thongtinhsba.thongtinbn.STT_BENHAN, thongtinhsba.thongtinbn.SOVAOVIEN, ret.DOT_SOKET
                                , singletonObject.dvtt, thongtinhsba.thongtinbn.TEN_BENH_NHAN, thongtinhsba.thongtinbn.TUOI
                                , thongtinhsba.thongtinbn.GIOI_TINH, thongtinhsba.thongtinbn.DIA_CHI, thongtinhsba.thongtinbn.TEN_PHONGBAN
                                , thongtinhsba.thongtinbn.ICD_HT ? thongtinhsba.thongtinbn.ICD_HT: " "];
                            var url = "vlg_inphieusoket?url=" + convertArray(arr) + '&viewPDF=1';
                            getFilesign769("PHIEU_NOITRU_SOKET15NGAY_TRUONGKHOA", ret.DOT_SOKET, -1, singletonObject.dvtt,
                                thongtinhsba.thongtinbn.SOVAOVIEN, thongtinhsba.thongtinbn.SOVAOVIEN_DT, -1, function(dataKySo) {
                                    if (dataKySo.length > 0) {
                                        getCMUFileSigned769GetLink(dataKySo[0].KEYMINIO, 'pdf').then(pdfData => {
                                            url = pdfData;
                                            previewAndSignPdfDefaultModal({
                                                url: url,
                                                idButton: 'phieusoket15ngay_kysobs_action',
                                            }, function(){
                                                $("#phieusoket15ngay_kysobs_action").click(function() {
                                                    // getXMLHSBA({
                                                    //     fileName: 'XML_PHIEU_SO_KET_DIEU_TRI',
                                                    //     method: "sendXMLSoKetDieuTriEmr",
                                                    //     maBenhNhan: thongtinhsba.thongtinbn.MA_BENH_NHAN,
                                                    //     soVaoVien: thongtinhsba.thongtinbn.SOVAOVIEN,
                                                    //     soBenhAn: thongtinhsba.thongtinbn.SOBENHAN,
                                                    //     bant: singletonObject.bant == ""? 0: singletonObject.bant,
                                                    //     sttBenhAn: thongtinhsba.thongtinbn.STT_BENHAN,
                                                    //     sttDotDieuTri: thongtinhsba.thongtinbn.STT_DOTDIEUTRI,
                                                    //     soVaoVienDT: thongtinhsba.thongtinbn.SOVAOVIEN_DT,
                                                    //     dotSoKet: ret.DOT_SOKET
                                                    // }, function(response) {

                                                    kySoChung({
                                                        dvtt: singletonObject.dvtt,
                                                        userId: singletonObject.userId,
                                                        url: url,
                                                        loaiGiay: "PHIEU_NOITRU_SOKET15NGAY_BACSI",
                                                        maBenhNhan: thongtinhsba.thongtinbn.MA_BENH_NHAN,
                                                        soBenhAn: thongtinhsba.thongtinbn.STT_BENHAN,
                                                        soPhieuDichVu: ret.DOT_SOKET,
                                                        soVaoVien: thongtinhsba.thongtinbn.SOVAOVIEN,
                                                        soVaoVienDT: thongtinhsba.thongtinbn.SOVAOVIEN_DT,
                                                        keyword: "Bác sĩ điều trị",
                                                        fileName: "Phiếu sơ kết 15 ngày: " + thongtinhsba.thongtinbn.TEN_BENH_NHAN + " - Mã phiếu: " + ret.DOT_SOKET,
                                                        // signXML: response.contentFile.buffer
                                                    }, function(dataKySo) {
                                                        $("#modalPreviewAndSignPDF").modal("hide");
                                                        loadDsPhieusoketTonghop();
                                                    });
                                                    // });
                                                });
                                            });
                                        })
                                    } else {
                                        previewAndSignPdfDefaultModal({
                                            url: url,
                                            idButton: 'phieusoket15ngay_kysobs_action',
                                        }, function(){
                                            $("#phieusoket15ngay_kysobs_action").click(function() {
                                                // getXMLHSBA({
                                                //     fileName: 'XML_PHIEU_SO_KET_DIEU_TRI',
                                                //     method: "sendXMLSoKetDieuTriEmr",
                                                //     maBenhNhan: thongtinhsba.thongtinbn.MA_BENH_NHAN,
                                                //     soVaoVien: thongtinhsba.thongtinbn.SOVAOVIEN,
                                                //     soBenhAn: thongtinhsba.thongtinbn.SOBENHAN,
                                                //     bant: singletonObject.bant == ""? 0: singletonObject.bant,
                                                //     sttBenhAn: thongtinhsba.thongtinbn.STT_BENHAN,
                                                //     sttDotDieuTri: thongtinhsba.thongtinbn.STT_DOTDIEUTRI,
                                                //     soVaoVienDT: thongtinhsba.thongtinbn.SOVAOVIEN_DT,
                                                //     dotSoKet: ret.DOT_SOKET
                                                // }, function(response) {
                                                kySoChung({
                                                    dvtt: singletonObject.dvtt,
                                                    userId: singletonObject.userId,
                                                    url: url,
                                                    loaiGiay: "PHIEU_NOITRU_SOKET15NGAY_BACSI",
                                                    maBenhNhan: thongtinhsba.thongtinbn.MA_BENH_NHAN,
                                                    soBenhAn: thongtinhsba.thongtinbn.STT_BENHAN,
                                                    soPhieuDichVu: ret.DOT_SOKET,
                                                    soVaoVien: thongtinhsba.thongtinbn.SOVAOVIEN,
                                                    soVaoVienDT: thongtinhsba.thongtinbn.SOVAOVIEN_DT,
                                                    keyword: "Bác sĩ điều trị",
                                                    fileName: "Phiếu sơ kết 15 ngày: " + thongtinhsba.thongtinbn.TEN_BENH_NHAN + " - Mã phiếu: " + ret.DOT_SOKET,
                                                    //signXML: response.contentFile.buffer
                                                }, function(dataKySo) {
                                                    $("#modalPreviewAndSignPDF").modal("hide");
                                                    loadDsPhieusoketTonghop();
                                                });
                                                // });
                                            });
                                        });
                                    }
                                });
                        }
                        if (key == "kysotk") {
                            var arr = [thongtinhsba.thongtinbn.STT_BENHAN, thongtinhsba.thongtinbn.SOVAOVIEN, ret.DOT_SOKET
                                , singletonObject.dvtt, thongtinhsba.thongtinbn.TEN_BENH_NHAN, thongtinhsba.thongtinbn.TUOI
                                , thongtinhsba.thongtinbn.GIOI_TINH, thongtinhsba.thongtinbn.DIA_CHI, thongtinhsba.thongtinbn.TEN_PHONGBAN
                                , thongtinhsba.thongtinbn.ICD_HT ? thongtinhsba.thongtinbn.ICD_HT: " "];
                            var url = "vlg_inphieusoket?url=" + convertArray(arr) + '&viewPDF=1';
                            getFilesign769("PHIEU_NOITRU_SOKET15NGAY_BACSI", ret.DOT_SOKET, -1, singletonObject.dvtt,
                                thongtinhsba.thongtinbn.SOVAOVIEN, thongtinhsba.thongtinbn.SOVAOVIEN_DT, -1, function(dataKySo) {
                                    if (dataKySo.length > 0) {
                                        getCMUFileSigned769GetLink(dataKySo[0].KEYMINIO, 'pdf').then(pdfData => {
                                            url = pdfData;
                                            previewAndSignPdfDefaultModal({
                                                url: url,
                                                idButton: 'phieusoket15ngay_kysotk_action',
                                            }, function(){
                                                $("#phieusoket15ngay_kysotk_action").click(function() {
                                                    kySoChung({
                                                        dvtt: singletonObject.dvtt,
                                                        userId: singletonObject.userId,
                                                        url: url,
                                                        loaiGiay: "PHIEU_NOITRU_SOKET15NGAY_TRUONGKHOA",
                                                        maBenhNhan: thongtinhsba.thongtinbn.MA_BENH_NHAN,
                                                        soBenhAn: thongtinhsba.thongtinbn.STT_BENHAN,
                                                        soPhieuDichVu: ret.DOT_SOKET,
                                                        soVaoVien: thongtinhsba.thongtinbn.SOVAOVIEN,
                                                        soVaoVienDT: thongtinhsba.thongtinbn.SOVAOVIEN_DT,
                                                        keyword: "Trưởng khoa",
                                                        fileName: "Phiếu sơ kết 15 ngày: " + thongtinhsba.thongtinbn.TEN_BENH_NHAN + " - Mã phiếu: " + ret.DOT_SOKET,
                                                    }, function(dataKySo) {
                                                        $("#modalPreviewAndSignPDF").modal("hide");
                                                        loadDsPhieusoketTonghop();
                                                    });
                                                });
                                            });
                                        })
                                    } else {
                                        previewAndSignPdfDefaultModal({
                                            url: url,
                                            idButton: 'phieusoket15ngay_kysotk_action',
                                        }, function(){
                                            $("#phieusoket15ngay_kysotk_action").click(function() {
                                                kySoChung({
                                                    dvtt: singletonObject.dvtt,
                                                    userId: singletonObject.userId,
                                                    url: url,
                                                    loaiGiay: "PHIEU_NOITRU_SOKET15NGAY_TRUONGKHOA",
                                                    maBenhNhan: thongtinhsba.thongtinbn.MA_BENH_NHAN,
                                                    soBenhAn: thongtinhsba.thongtinbn.STT_BENHAN,
                                                    soPhieuDichVu: ret.DOT_SOKET,
                                                    soVaoVien: thongtinhsba.thongtinbn.SOVAOVIEN,
                                                    soVaoVienDT: thongtinhsba.thongtinbn.SOVAOVIEN_DT,
                                                    keyword: "Trưởng khoa",
                                                    fileName: "Phiếu sơ kết 15 ngày: " + thongtinhsba.thongtinbn.TEN_BENH_NHAN + " - Mã phiếu: " + ret.DOT_SOKET,
                                                }, function(dataKySo) {
                                                    $("#modalPreviewAndSignPDF").modal("hide");
                                                    loadDsPhieusoketTonghop();
                                                });
                                            });
                                        });
                                    }
                                });
                        }
                        if (key == "huykysobs") {
                            confirmToClient("Bạn có chắc sẽ hủy ký số phiếu này?", function() {
                                huykysoFilesign769("PHIEU_NOITRU_SOKET15NGAY_BACSI", ret.DOT_SOKET, singletonObject.userId, singletonObject.dvtt,
                                    thongtinhsba.thongtinbn.SOVAOVIEN, thongtinhsba.thongtinbn.SOVAOVIEN_DT, -1, function(data) {
                                        loadDsPhieusoketTonghop();
                                    })
                            }, function () {

                            })
                        }
                        if (key == "huykysotk") {
                            confirmToClient("Bạn có chắc sẽ hủy ký số phiếu này?", function() {
                                huykysoFilesign769("PHIEU_NOITRU_SOKET15NGAY_TRUONGKHOA", ret.DOT_SOKET, singletonObject.userId, singletonObject.dvtt,
                                    thongtinhsba.thongtinbn.SOVAOVIEN, thongtinhsba.thongtinbn.SOVAOVIEN_DT, -1, function(data) {
                                        loadDsPhieusoketTonghop();
                                    })
                            }, function () {

                            })
                        }
                        if (key == "sua") {
                            $(".clear-input").val("");
                            var id = $("#list_phieusoket").jqGrid('getGridParam', 'selrow');
                            var ret = $("#list_phieusoket").jqGrid('getRowData', id);
                            let jsonBoSung = ret.DATA_BOSUNG ? JSON.parse(ret.DATA_BOSUNG) : {};
                            initModalPhieuoKet('CAP_NHAT', ret);
                            loadInfoPhieuSoKet(ret);
                            loadDsXetnghiemdathuchien();
                            loadDsToDieuTri_SK15();
                            loadDsCDHAdathuchien();
                            chonBSKySoKet15Ngay(jsonBoSung);
                        }
                        if (key == "xoa") {
                            var id = $("#list_phieusoket").jqGrid('getGridParam', 'selrow');
                            var ret = $("#list_phieusoket").jqGrid('getRowData', id);
                            xoaPhieuSoKet(ret)
                        }
                        if (key == "xem") {
                            inPhieuSoKet(ret.DOT_SOKET);
                        }
                    },
                    items: items
                });
            }
        },
        gridComplete : function() {
        }
    });

    $("#list_sks15_xn").jqGrid({
        datatype: "local",
        loadonce: false,
        height: 515,
        witdh: null,
        shrinkToFit: false,
        colModel: [
            {label:' ',index:' ',key: true,width:50,sorttype:"text",formatter:function(cellvalue, option, rowObject){
                    return "<center><input type='button' style='background-color:lightgreen;color:black' value='+'   onClick=\"btnGetXetNghiem(" + option.rowId + ")\" \></center>";
                }},
            {label: "NGAYGIO_PHIEU",name: 'NGAYGIO_PHIEU', index: 'NGAYGIO_PHIEU', width: 10, hidden: true},
            {label: "KET_QUA",name: 'KET_QUA', index: 'KET_QUA', width: 10, hidden: true},
            {label: "DVTT",name: 'DVTT', index: 'DVTT', width: 10, hidden: true},
            {label: "CANHBAO",name: 'CANHBAO', index: 'CANHBAO', width: 10, hidden: true},
            {label: "ID_XNCHA",name: 'ID_XNCHA', index: 'ID_XNCHA', width: 10, hidden: true},
            {label: "MA_XETNGHIEM",name: 'MA_XETNGHIEM', index: 'MA_XETNGHIEM', width: 10, hidden: true},
            {label: "CO_DULIEUCON",name: 'CO_DULIEUCON', index: 'CO_DULIEUCON', width: 10, hidden: true},
            {label: "Xét nghiệm",name: 'TEN_XETNGHIEM', index: 'TEN_XETNGHIEM', width: 300 ,cellattr: function (rowId, tv, rawObject, cm, rdata) {return 'style="white-space: normal;padding: 2px;"';}},
            {label: "Kết quả", name: 'KET_QUA_TEXT', index: 'KET_QUA_TEXT', width: 150 ,
                cellattr: function (rowId, tv, rawObject, cm, rdata) {return 'style="white-space: normal;padding: 2px;"';},
                formatter: function (cellvalue, options, rowObject) {
                    var color;
                    if (rowObject.CANHBAO == '1') {
                        return cellvalue;
                    } else {
                        return '<span class="cellWithoutBackground" style="font-weight:bold">' + cellvalue + '</span>';
                    }
                }
            }
        ],
        grouping: true,
        groupingView: {
            groupField: ["NGAYGIO_PHIEU"],
            groupColumnShow: [false],
            groupText: ['<b>{0}</b>'],
            groupCollapse: false
        },
        rowNum: 1000000,
        caption: "Danh sách xét nghiệm",
        loadComplete : function() {
            var newWidth = $("#list_sks15_xn").closest(".ui-jqgrid").parent().width();
            $("#list_sks15_xn").jqGrid("setGridWidth", newWidth, true);
        }
    });

    $("#list_sks15_cdha").jqGrid({
        datatype: "local",
        loadonce: false,
        height: 515,
        witdh: null,
        shrinkToFit: false,
        colModel: [
            {label:' ',index:' ',key: true,width:50,sorttype:"text",formatter:function(cellvalue, option, rowObject){
                    return "<center><input type='button' style='background-color:lightgreen;color:black' value='+'   onClick=\"btnGetCDHA(" + option.rowId + ")\" \></center>";
                }},
            {label: "TEN_LOAI_CDHA",name: 'TEN_LOAI_CDHA', index: 'TEN_LOAI_CDHA', width: 10, hidden: true},
            {label: "Tên",name: 'TEN_CDHA', index: 'TEN_CDHA', width: 300 ,cellattr: function (rowId, tv, rawObject, cm, rdata) {return 'style="white-space: normal;padding: 2px;"';}},
            {label: "Kết quả", name: 'MO_TA', index: 'MO_TA', width: 150 ,cellattr: function (rowId, tv, rawObject, cm, rdata) {return 'style="white-space: normal;padding: 2px;"';}},
        ],
        grouping: true,
        groupingView: {
            groupField: ["TEN_LOAI_CDHA"],
            groupColumnShow: [false],
            groupText: ['<b>{0}</b>'],
            groupCollapse: false
        },
        rowNum: 1000000,
        caption: "Danh sách CĐHA",
        loadComplete : function() {
            var newWidth = $("#list_sks15_cdha").closest(".ui-jqgrid").parent().width();
            $("#list_sks15_cdha").jqGrid("setGridWidth", newWidth, true);
        }
    });

    $("#list_sks15_tdt").jqGrid({
        datatype: "local",
        loadonce: false,
        height: 515,
        witdh: null,
        shrinkToFit: false,
        colModel: [
            {label:' ',index:' ',key: true,width:50,sorttype:"text",formatter:function(cellvalue, option, rowObject){
                    return "<center><input type='button' style='background-color:lightgreen;color:black' value='+'   onClick=\"btnGetToDieuTri(" + option.rowId + ")\" \></center>";
                }},
            {label: "STT_DIEUTRI",name: 'STT_DIEUTRI', index: 'STT_DIEUTRI', width: 10, hidden: true},
            {label: "NGAYLAP",name: 'NGAYLAP', index: 'NGAYLAP', width: 10, hidden: true},
            {label: "Tờ điều trị",name: 'PHIEUDIEUTRI', index: 'PHIEUDIEUTRI', width: 100 ,cellattr: function (rowId, tv, rawObject, cm, rdata) {return 'style="white-space: normal;padding: 2px;"';}},
            {label: "Diễn biến bệnh", name: 'DIEN_BIEN_BENH', index: 'DIEN_BIEN_BENH', width: 150 ,cellattr: function (rowId, tv, rawObject, cm, rdata) {return 'style="white-space: normal;padding: 2px;"';}},
        ],
        grouping: true,
        groupingView: {
            groupField: ["NGAYLAP"],
            groupColumnShow: [false],
            groupText: ['<b>{0}</b>'],
            groupCollapse: false
        },
        rowNum: 1000000,
        caption: "Danh sách tờ điều trị",
        loadComplete : function() {
            var newWidth = $("#list_sks15_tdt").closest(".ui-jqgrid").parent().width();
            $("#list_sks15_tdt").jqGrid("setGridWidth", newWidth, true);
        }
    });

    $(".phieusoket15ngay_them").click(function() {
        showSelfLoading("themphieusoket15ngay");
        initModalPhieuoKet('THEM', null);
        $(".clear-input").val("");
        loadDsPhieusoketTonghop();
        loadDsXetnghiemdathuchien();
        loadDsToDieuTri_SK15();
        loadDsCDHAdathuchien();
        chonBSKySoKet15Ngay();
        hideSelfLoading("themphieusoket15ngay");
    });

    $("#sk15_tab0_click").click(function(){
        loadDsXetnghiemdathuchien();
    });

    $("#sk15_tab1_click").click(function(){
        loadDsToDieuTri_SK15();
    });

    $("#sk15_tab2_click").click(function(){
        loadDsCDHAdathuchien();
    });

    $('#view_single_phieusoket15ngay').click(function () {
        inPhieuSoKet(thongTinPhieuSoKetMoiNhat.DOT_SOKET)
    })

    $('#edit_single_phieusoket15ngay').click(function () {
        initModalPhieuoKet('SUA', thongTinPhieuSoKetMoiNhat);
        let jsonBoSung = thongTinPhieuSoKetMoiNhat.DATA_BOSUNG ? JSON.parse(thongTinPhieuSoKetMoiNhat.DATA_BOSUNG) : {};
        chonBSKySoKet15Ngay(jsonBoSung);
    });

    $('#delete_single_phieusoket15ngay').click(function () {
        xoaPhieuSoKet(thongTinPhieuSoKetMoiNhat);
    })

    $("#action_luu_phieusoket").click(function() {
        showSelfLoading("action_luu_phieusoket");
        var btnAction = $('#action_luu_phieusoket').attr('data-action');
        if (btnAction === 'THEM') {
            var arr = [singletonObject.dvtt, thongtinhsba.thongtinbn.STT_BENHAN, thongtinhsba.thongtinbn.SOVAOVIEN, singletonObject.userId];
            $.post('cmu_post_CMU_THEM_DOT_SOKET', {
                url: arr.join('```')
            }).done(function (dt) {
                if (Number(dt) >= 0) {
                    $("#sk15_dotsoket").val(Number(dt) + 1);
                    var dotsoket = "" + $("#sk15_dotsoket").val();
                    var dienbienlamsan = "" + $("#sk15_dienbienlamsang").val();
                    var tenxetnghiem = "" + $("#sk15_xetnghiem").val();
                    var quatrinhdieutri = "" + $("#sk15_quatrinhdieutri").val();
                    var danhgia = "" + $("#sk15_danhgiaketqua").val();
                    var huongdieutri = "" + $("#sk15_huongdieutri").val();
                    var ngaytao = "" + $("#sk15_ngaytao").val();
                    if (danhgia === "") danhgia = " ";
                    if (huongdieutri === "") huongdieutri = " ";
                    var arr = [singletonObject.dvtt, thongtinhsba.thongtinbn.STT_BENHAN, thongtinhsba.thongtinbn.SOVAOVIEN,
                        dotsoket, dienbienlamsan, tenxetnghiem, quatrinhdieutri, danhgia, huongdieutri, ngaytao];
                    if (dotsoket === "") {
                        notifiToClient("Red", "Chưa chọn đợt sơ kết");
                        hideSelfLoading("action_luu_phieusoket")
                    } else {
                        var url = "vlg_capnhat_dotsoket";
                        $.post(url, {url: convertArray(arr)}).done(function (dt) {
                            // var data_log = {
                            //     SOVAOVIEN: thongtinhsba.thongtinbn.SOVAOVIEN,
                            //     LOAI: "PHIEUSOKET15NGAYDIEUTRI",
                            //     NOIDUNGBANDAU: "Thêm mới sơ kết 15 ngày điều trị đợt : " + dotsoket + " của bệnh nhân: " + thongtinhsba.thongtinbn.TEN_BENH_NHAN,
                            //     NOIDUNGMOI: "Thêm mới sơ kết 15 ngày điều trị đợt : " + dotsoket + " của bệnh nhân: " + thongtinhsba.thongtinbn.TEN_BENH_NHAN,
                            //     USERID: singletonObject.userId,
                            //     ACTION: "INSERT"
                            // }
                            // luuLogHSBATheoBN(data_log);

                            capNhatBoSungPhieuSoKet(ngaytao, dotsoket);
                            luuLogPSK(1);
                            notifiToClient("Green", "Lưu thành công");
                            loadDsPhieusoketTonghop()
                            $("#modalPhieusoket").modal("hide")
                        }).fail(function() {
                            notifiToClient("Red", "Lưu không thành công");
                        }).always(function() {
                            hideSelfLoading("action_luu_phieusoket")
                        });
                    }
                } else {
                    notifiToClient("Red", "Thêm đợt sơ kết thất bại")
                }
            });
        } else {
            var dotsoket = "" + $("#sk15_dotsoket").val();
            var dienbienlamsan = "" + $("#sk15_dienbienlamsang").val();
            var tenxetnghiem = "" + $("#sk15_xetnghiem").val();
            var quatrinhdieutri = "" + $("#sk15_quatrinhdieutri").val();
            var danhgia = "" + $("#sk15_danhgiaketqua").val();
            var huongdieutri = "" + $("#sk15_huongdieutri").val();
            var ngaytao = "" + $("#sk15_ngaytao").val();
            if (danhgia === "") danhgia = " ";
            if (huongdieutri === "") huongdieutri = " ";
            var arr = [singletonObject.dvtt, thongtinhsba.thongtinbn.STT_BENHAN, thongtinhsba.thongtinbn.SOVAOVIEN,
                dotsoket, dienbienlamsan, tenxetnghiem, quatrinhdieutri, danhgia, huongdieutri, ngaytao];
            if (dotsoket === "") {
                notifiToClient("Red", "Chưa chọn đợt sơ kết");
                hideSelfLoading("action_luu_phieusoket")
            } else {
                var url = "vlg_capnhat_dotsoket";
                $.post(url, {url: convertArray(arr)}).done(function (dt) {
                    notifiToClient("Green", "Lưu thành công");
                    var thongTinPhieuSoKetSauChinhSua = {
                        DANHGIA_KETQUA: danhgia,
                        DIEN_BIEN: dienbienlamsan,
                        DOT_SOKET: dotsoket,
                        DVTT: singletonObject.dvtt,
                        HUONG_DIEUTRI: huongdieutri,
                        NGAY_TAO: thongTinPhieuSoKetTruocChinhSua.NGAY_TAO,
                        NGUOI_TAO: thongTinPhieuSoKetTruocChinhSua.NGUOI_TAO,
                        QUATRINH_DIEUTRI: quatrinhdieutri,
                        SOVAOVIEN: thongtinhsba.thongtinbn.SOVAOVIEN,
                        STT_BENHAN: thongtinhsba.thongtinbn.STT_BENHAN,
                        TEN_NHANVIEN: thongTinPhieuSoKetTruocChinhSua.TEN_NHANVIEN,
                        XN_CANLAMSANG: tenxetnghiem
                    }
                    // var data_log = {
                    //     SOVAOVIEN: thongtinhsba.thongtinbn.SOVAOVIEN,
                    //     LOAI: "PHIEUSOKET15NGAYDIEUTRI",
                    //     NOIDUNGBANDAU: "Chỉnh sửa sơ kết 15 ngày điều trị đợt : " + dotsoket + " dữ liệu cũ: " + JSON.stringify(thongTinPhieuSoKetTruocChinhSua),
                    //     NOIDUNGMOI: "Chỉnh sửa sơ kết 15 ngày điều trị đợt : " + dotsoket + " dữ liệu mới: " + JSON.stringify(thongTinPhieuSoKetSauChinhSua),
                    //     USERID: singletonObject.userId,
                    //     ACTION: "UPDATE"
                    // }
                    // luuLogHSBATheoBN(data_log);
                    capNhatBoSungPhieuSoKet(ngaytao, dotsoket);
                    luuLogPSK(2);
                    loadDsPhieusoketTonghop()
                    $("#modalPhieusoket").modal("hide")
                }).fail(function() {
                    notifiToClient("Red", "Lưu không thành công");
                }).always(function() {
                    hideSelfLoading("action_luu_phieusoket")
                });
            }
        }
    });

    $("#soket15ngay_mau").click(function() {
        var element = $("#mau_danhsachmaujson_wrap");
        element.attr("function-add", 'insertMauHSBASOKET15NGAY');
        element.attr("function-chinhsua", 'editMauHSBASOKET15NGAY');
        element.attr("function-select", 'selectMauHSBASOKET15NGAY');
        element.attr("function-getdata", 'getdataMauHSBASOKET15NGAY');
        element.attr("function-validate", 'formioHSBASOKET15NGAYValidate');
        element.attr("data-key", 'MAUSOKET15NGAY');
        $("#modalMauChungJSON").modal("show");
        $.loadDanhSachMauChungJSON('MAUSOKET15NGAY')
    })

    $.extend({
        insertMauHSBASOKET15NGAY: function () {
            generateFormMauSOKET15NGAY({})
        },
        editMauHSBASOKET15NGAY: function (rowSelect) {
            var json = JSON.parse(rowSelect.NOIDUNG);
            var dataMau = {}
            json.forEach(function(item) {
                dataMau[item.key] = item.value
            })

            generateFormMauSOKET15NGAY({
                ID: rowSelect.ID,
                TENMAU: rowSelect.TENMAU,
                ...dataMau
            })
        },
        selectMauHSBASOKET15NGAY: function (rowSelect) {
            var json = JSON.parse(rowSelect.NOIDUNG);
            json.forEach(function(item) {
                $("#formPhieusoket [name="+item.key+"]").val(item.value)
            })
            $("#modalMauChungJSON").modal("hide");
        },
        getdataMauHSBASOKET15NGAY: function () {
            var objectNoidung = [];
            getObjectMauSOKET15NGAY().forEach(function(item) {
                if(item.key != 'ID' && item.key != 'TENMAU') {
                    objectNoidung.push({
                        "label": item.label,
                        "value": formioMauHSBA.submission.data[item.key],
                        "key": item.key,
                    })
                }
            })
            return {
                ID: formioMauHSBA.submission.data.ID,
                TENMAU: formioMauHSBA.submission.data.TENMAU,
                NOIDUNG: JSON.stringify(objectNoidung),
                KEYMAUCHUNG: 'MAUSOKET15NGAY'
            };
        },
        formioHSBASOKET15NGAYValidate: function() {
            formioMauHSBA.emit("checkValidity");
            if (!formioMauHSBA.checkValidity(null, false, null, true)) {
                return false;
            }
            return true;
        },
    })
    function generateFormMauSOKET15NGAY(dataForm) {
        var jsonForm = getJSONObjectForm(getObjectMauSOKET15NGAY());
        Formio.createForm(document.getElementById('formChiTietMauChungJSON'),
            jsonForm,{}
        ).then(function(form) {
            formioMauHSBA = form;
            formioMauHSBA.submission = {
                data: {
                    ...dataForm
                }
            }
        });
    };
    function getObjectMauSOKET15NGAY() {
        return [
            {
                "label": "ID",
                "key": "ID",
                "type": "textfield",
                others: {
                    hidden: true
                }
            },
            {
                "label": "Tên mẫu",
                "key": "TENMAU",
                "type": "textarea",
                validate: {
                    required: true
                },
                others: {
                    "labelPosition": "left-left",
                    "labelWidth": 10
                }
            },
            {
                "label": "Diễn biến lâm sàng trong đợt điều trị",
                "key": "DIEN_BIEN",
                "type": "textarea",
                others: {
                    "labelPosition": "left-left",
                    "labelWidth": 10
                }
            },
            {
                "label": "Xét nghiệm cận lâm sàng",
                "key": "XET_NGHIEM",
                "type": "textarea",
                others: {
                    "labelPosition": "left-left",
                    "labelWidth": 10
                }
            },
            {
                "label": "Quá trình điều trị",
                "key": "QUA_TRINH_DT",
                "type": "textarea",
                others: {
                    "labelPosition": "left-left",
                    "labelWidth": 10
                }
            },
            {
                "label": "Đánh giá kết quả",
                "key": "DANH_GIA_KQ",
                "type": "textarea",
                others: {
                    "labelPosition": "left-left",
                    "labelWidth": 10
                }
            },
            {
                "label": "Hướng điều trị tiếp và tiên lượng",
                "key": "HUONG_DIEU_TRI",
                "type": "textarea",
                others: {
                    "labelPosition": "left-left",
                    "labelWidth": 10
                }
            }];
    };
});