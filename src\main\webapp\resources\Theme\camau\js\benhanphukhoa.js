function getBAPHUKHOAJSON () {
  var form;
  var formTongket;
  var keyMauHSBAPHUKHOA = "MAUHSBAPHUKHOA";
  var keyMauHSBAPHUKHOATongket = "MAUHSBAPHUKHOATONGKET";
  var formioMauHSBA;
  var formioMauHSBATongket;
  return {
    script: {},
    scriptTongket: {},
    initObjectFormPage1: function(edit, hidden) {
      if(edit) {
        $("#hsba_tthc_chitietbenhan").hide();
        $("#hsba_tthc_luuchitietbenhan").show();
        $("#hsba_tthc_huychitietbenhan").show();
      } else {
        $("#hsba_tthc_chitietbenhan").show();
        $("#hsba_tthc_luuchitietbenhan").hide();
        $("#hsba_tthc_huychitietbenhan").hide();
      }
      return getJSONObjectForm([
        {
          "key": "p-chandoan",
          "type": "tabs",
          "customClass": "hsba-tabs-wrap",
          "components": [
            {
              "label": "QUẢN LÝ NGƯỜI BỆNH",
              "key": "tabQuanLyNguoiBenh",
              "components": [
                getObjectQuanLyNguoiBenhVBAT1_1(edit, hidden),
              ]
            },
            {
              "label": "CHẨN ĐOÁN",
              "key": "tabChanDoan",
              "components": [
                getObjectChanDoanVBAT1_1(edit, hidden),
              ]
            },
            {
              "label": "TÌNH TRẠNG RA VIỆN",
              "key": "tabTinhTrangRaVien",
              "components": [
                getObjectTinhTrangRaVienVBAT1_1(edit, hidden),
              ]
            },
          ]
        },
      ])
    },
    initObjectFormPage2: function() {
      return getJSONObjectForm([
        {
          "collapsible": true,
          "key": "p-lydovaovien",
          "type": "panel",
          "label": "Lý do vào viện",
          "title": "BỆNH ÁN VÀ HỎI BỆNH",
          "collapsed": false,
          "input": false,
          "tableView": false,
          "customClass": "hsba-tabs-wrap",
          "components": [
            {
              label: "lydovaovien",
              key: "colLydovaovien",
              columns: [
                {
                  "components": [
                    {
                      "label": "Lý do vào viện",
                      "key": "LYDOVAOVIEN",
                      "type": "textarea",
                      customClass: "pr-2",
                      rows: 2,
                      "validate": {
                        "required": true
                      }
                    },
                  ],
                  "width": 12,
                  "size": "md",
                },
              ],
              "customClass": "ml-0 mr-0",
              "type": "columns",
            },
            {
              "label": "Quá trình bệnh lý",
              others: {
                "tooltip": "Quá trình bệnh lý: (khởi phát, diễn biến, chẩn đoán, điều trị tuyến dưới, v.v...)",
              },
              "key": "QUATRINHBENHLY",
              "type": "textarea",
              "validate": {
                "minLength": 5,
                "maxLength": 3000,
                "required": true
              }
            },
            {
              "label": "Tiền sử bệnh (bản thân)",
              others: {
                "tooltip": " Bản thân: (phát triển thể lực từ nhỏ đến lớn, những bệnh đã mắc, phương pháp ĐTr, tiêm phòng, ăn uống, sinh hoạt vv...)",
              },
              "key": "TIENSUBANTHAN",
              "type": "textarea",
              "validate": {
                "minLength": 5,
                "maxLength": 3000,
                "required": true
              }
            },
            {
              "label": "Gia đình",
              others: {
                "tooltip": "Gia đình: (Những người trong gia đình: bệnh đã mắc, đời sống, tinh thần, vật chất v.v...)",
              },
              "key": "TIENSUGIADINH",
              "type": "textarea",
              "rows": 2,
              "validate": {
                "minLength": 5,
                "maxLength": 3000,
              }
            },
            {
              "title": "3. Tiền sử bệnh phụ khoa",
              "label": "3. Tiền sử bệnh phụ khoa",
              "customClass": "hsba-panel-child-wrap",
              "key": "tiensusanphukhoa_panel",
              "type": "panel",
              "others": {
                "collapsible": true,
              },
              "components": [
                {
                  "label": "",
                  "key": "tiensusanphukhoa",
                  "components": [
                    {
                      "label": "Tiền sử sản phụ khoa 1",
                      "columns": [
                        {
                          "components": [
                            {
                              "label": "Bắt đầu thấy kinh năm",
                              "applyMaskOn": "change",
                              "autoExpand": false,
                              "customClass": "pr-2",
                              "tableView": true,
                              "key": "BATDAUKINHNAM",
                              "type": "number",
                              "input": true
                            }
                          ],
                          "width": 2,
                          "offset": 0,
                          "push": 0,
                          "pull": 0,
                          "size": "md",
                          "currentWidth": 4
                        },
                        {
                          "components": [
                            {
                              "label": "Tuổi bắt đầu thấy kinh",
                              "applyMaskOn": "change",
                              "autoExpand": false,
                              "customClass": "pr-2",
                              "tableView": true,
                              "key": "TUOICOKINH",
                              "type": "number",
                              "input": true
                            }
                          ],
                          "width": 2,
                          "offset": 0,
                          "push": 0,
                          "pull": 0,
                          "size": "md",
                          "currentWidth": 4
                        },
                        {
                          "components": [
                            {
                              "label": "Tính chất kinh nguyệt",
                              "applyMaskOn": "change",
                              "autoExpand": false,
                              "customClass": "pr-2",
                              "tableView": true,
                              "key": "TINHCHATKINHNGUYET",
                              "type": "textfield",
                              "input": true
                            }
                          ],
                          "width": 2,
                          "offset": 0,
                          "push": 0,
                          "pull": 0,
                          "size": "md",
                          "currentWidth": 4
                        },
                        {
                          "components": [
                            {
                              "label": "Chu kỳ (ngày)",
                              "applyMaskOn": "change",
                              "autoExpand": false,
                              "customClass": "pr-2",
                              "tableView": true,
                              "key": "CHUKY",
                              "type": "number",
                              "input": true
                            }
                          ],
                          "width": 2,
                          "offset": 0,
                          "push": 0,
                          "pull": 0,
                          "size": "md",
                          "currentWidth": 4
                        },
                        {
                          "components": [
                            {
                              "label": "Số ngày thấy kinh",
                              "applyMaskOn": "change",
                              "autoExpand": false,
                              "customClass": "pr-2",
                              "tableView": true,
                              "key": "SONGAYTHAYKINH",
                              "type": "number",
                              "input": true
                            }
                          ],
                          "width": 2,
                          "offset": 0,
                          "push": 0,
                          "pull": 0,
                          "size": "md",
                          "currentWidth": 4
                        },
                        {
                          "components": [
                            {
                              "label": "Lượng kinh",
                              "applyMaskOn": "change",
                              "autoExpand": false,
                              "customClass": "pr-2",
                              "tableView": true,
                              "key": "LUONGKINH",
                              "type": "textfield",
                              "input": true
                            }
                          ],
                          "width": 2,
                          "offset": 0,
                          "push": 0,
                          "pull": 0,
                          "size": "md",
                          "currentWidth": 4
                        },
                      ],
                      "customClass": "ml-0 mr-0",
                      "key": "TIENSUSANPHUKHOA1",
                      "type": "columns",
                      "input": false,
                      "tableView": false
                    },
                    {
                      "label": "Tiền sử sản phụ khoa 2",
                      "columns": [
                        {
                          "components": [
                            {
                              "label": "Kinh lần cuối ngày",
                              "key": "KINHLANCUOINGAY",
                              "type": "datetime",
                              format: "dd/MM/yyyy HH:mm",
                              enableTime: true,
                              customClass: "pr-2",
                              // validate: {
                              //   required: true
                              // }
                            }
                          ],
                          "size": "md",
                          "width": 4
                        },
                        {
                          "components": [
                            {
                              "label": "Đau bụng",
                              "customClass": "pr-2",
                              "defaultValue": {
                                "BOXDAUBUNG": false
                              },
                              "values": [
                                {
                                  "label": "",
                                  "value": "BOXDAUBUNG",
                                  "shortcut": ""
                                }
                              ],
                              "key": "SELECTBOX_DAUBUNG",
                              "type": "selectboxes",
                            }
                          ],
                          "width": 3,
                          "offset": 0,
                          "push": 0,
                          "pull": 0,
                          "size": "md",
                          "currentWidth": 4
                        },
                        {
                          "components": [
                            {
                              "label": "Thời gian",
                              "customClass": "pr-2",
                              "inline": true,
                              "defaultValue": {
                                "BOXTRUOC": false,
                                "BOXTRONG": false,
                                "BOXSAU": false
                              },
                              "values": [
                                {
                                  "label": "Trước",
                                  "value": "BOXTRUOC",
                                  "shortcut": ""
                                },
                                {
                                  "label": "Trong",
                                  "value": "BOXTRONG",
                                  "shortcut": ""
                                },
                                {
                                  "label": "Sau",
                                  "value": "BOXSAU",
                                  "shortcut": ""
                                }
                              ],
                              "key": "SELECTBOX_THOIGIAN",
                              "type": "selectboxes",
                            }
                          ],
                          "width": 5,
                          "offset": 0,
                          "push": 0,
                          "pull": 0,
                          "size": "md",
                          "currentWidth": 4
                        },
                      ],
                      "customClass": "ml-0 mr-0",
                      "key": "TIENSUSANPHUKHOA2",
                      "type": "columns",
                      "input": false,
                      "tableView": false
                    },
                    {
                      "label": "Tiền sử sản phụ khoa 3",
                      "columns": [
                        {
                          "components": [
                            {
                              "label": "Lấy chồng năm",
                              "applyMaskOn": "change",
                              "autoExpand": false,
                              "customClass": "pr-2",
                              "tableView": true,
                              "key": "LAYCHONGNAM",
                              "type": "number",
                              "input": true
                            }
                          ],
                          "width": 3,
                          "offset": 0,
                          "push": 0,
                          "pull": 0,
                          "size": "md",
                          "currentWidth": 4
                        },
                        {
                          "components": [
                            {
                              "label": "Tuổi lấy chồng",
                              "applyMaskOn": "change",
                              "autoExpand": false,
                              "customClass": "pr-2",
                              "tableView": true,
                              "key": "TUOILAYCHONG",
                              "type": "number",
                              "input": true
                            }
                          ],
                          "width": 3,
                          "offset": 0,
                          "push": 0,
                          "pull": 0,
                          "size": "md",
                          "currentWidth": 4
                        },
                        {
                          "components": [
                            {
                              "label": "Hết kinh năm",
                              "applyMaskOn": "change",
                              "autoExpand": false,
                              "customClass": "pr-2",
                              "tableView": true,
                              "key": "HETKINHNAM",
                              "type": "number",
                              "input": true
                            }
                          ],
                          "width": 3,
                          "offset": 0,
                          "push": 0,
                          "pull": 0,
                          "size": "md",
                          "currentWidth": 4
                        },
                        {
                          "components": [
                            {
                              "label": "Tuổi hết kinh",
                              "applyMaskOn": "change",
                              "autoExpand": false,
                              "customClass": "pr-2",
                              "tableView": true,
                              "key": "TUOIHETKINH",
                              "type": "number",
                              "input": true
                            }
                          ],
                          "width": 3,
                          "offset": 0,
                          "push": 0,
                          "pull": 0,
                          "size": "md",
                          "currentWidth": 4
                        },
                      ],
                      "customClass": "ml-0 mr-0",
                      "key": "TIENSUSANPHUKHOA3",
                      "type": "columns",
                      "input": false,
                      "tableView": false
                    },
                    {
                      "label": "Tiền sử sản phụ khoa 4",
                      "customClass": "ml-0 mr-0",
                      "key": "TIENSUSANPHUKHOA4",
                      "type": "columns",
                      "input": false,
                      "tableView": false,
                      "columns": [
                        {
                          "components": [
                            {
                              "label": "Những bệnh phụ khoa đã điều trị",
                              "applyMaskOn": "change",
                              "autoExpand": false,
                              "customClass": "pr-2",
                              "tableView": true,
                              "key": "BENHPHUKHOA",
                              "type": "textarea",
                              "input": true
                            }
                          ],
                          "width": 12,
                          "offset": 0,
                          "push": 0,
                          "pull": 0,
                          "size": "md",
                          "currentWidth": 4
                        },
                      ],
                    },

                  ]
                },
              ],
            },
            {
              "title": "4. Tiền sử sản khoa",
              "label": "4. Tiền sử sản khoa",
              "customClass": "hsba-panel-child-wrap",
              "key": "hoibenh_panel",
              "type": "panel",
              "others": {
                "collapsible": false,
              },
              "components": [
                {
                  "components": [
                    {
                      "label": "",
                      "customClass": "ml-0 mr-0",
                      "key": "TIENSUSANPHUKHOA5",
                      "type": "columns",
                      "input": false,
                      "tableView": false,
                      "columns": [
                        {
                          "components": [
                            {
                              "label": "Sinh (đủ tháng)",
                              "applyMaskOn": "change",
                              "autoExpand": false,
                              "customClass": "pr-2",
                              "tableView": true,
                              "key": "BOXTIENTHAISINH",
                              "type": "number",
                              "input": true
                            }
                          ],
                          "width": 3,
                          "offset": 0,
                          "push": 0,
                          "pull": 0,
                          "size": "md",
                          "currentWidth": 4
                        },
                        {
                          "components": [
                            {
                              "label": "Sớm (đẻ non)",
                              "applyMaskOn": "change",
                              "autoExpand": false,
                              "customClass": "pr-2",
                              "tableView": true,
                              "key": "BOXTIENTHAISOM",
                              "type": "number",
                              "input": true
                            }
                          ],
                          "width": 3,
                          "offset": 0,
                          "push": 0,
                          "pull": 0,
                          "size": "md",
                          "currentWidth": 4
                        },
                        {
                          "components": [
                            {
                              "label": "Sẩy (nạo hút)",
                              "applyMaskOn": "change",
                              "autoExpand": false,
                              "customClass": "pr-2",
                              "tableView": true,
                              "key": "BOXTIENTHAISAY",
                              "type": "number",
                              "input": true
                            }
                          ],
                          "width": 3,
                          "offset": 0,
                          "push": 0,
                          "pull": 0,
                          "size": "md",
                          "currentWidth": 4
                        },
                        {
                          "components": [
                            {
                              "label": "Sống",
                              "applyMaskOn": "change",
                              "autoExpand": false,
                              "customClass": "pr-2",
                              "tableView": true,
                              "key": "BOXTIENTHAISONG",
                              "type": "number",
                              "input": true
                            }
                          ],
                          "width": 3,
                          "offset": 0,
                          "push": 0,
                          "pull": 0,
                          "size": "md",
                          "currentWidth": 4
                        },
                      ],
                    },
                  ]
                }
              ]
            },
          ]
        },
        {
          "collapsible": true,
          "key": "p-khambenh",
          "type": "panel",
          "label": "Khám bệnh",
          "title": "KHÁM BỆNH",
          "collapsed": false,
          "input": false,
          "tableView": false,
          "customClass": "hsba-tabs-wrap",
          "components": [
            {
              "label": "left",
              "columns": [
                {
                  "components": [
                    {
                      "label": "Toàn thân",
                      others: {
                        "tooltip": " Toàn thân: (da niêm mạc)",
                      },
                      "key": "DANIENMAC",
                      "type": "textarea",
                      "rows": 2,
                      "input": true,
                      "validate": {
                        "minLength": 5,
                        "maxLength": 3000,
                        "required": true
                      }
                    },
                    {
                      "label": "Hạch",
                      "key": "HACH",
                      "type": "textarea",
                      "rows": 2,
                      "input": true,
                      "validate": {
                        "minLength": 5,
                        "maxLength": 3000,
                        "required": true
                      }
                    },
                    {
                      "label": "Vú",
                      "key": "VU",
                      "type": "textarea",
                      "rows": 2,
                      "input": true,
                      "validate": {
                        "minLength": 5,
                        "maxLength": 3000,
                        "required": true
                      }
                    },
                    {
                      "label": " Các cơ quan",
                      "columns": [
                        {
                          "components": [
                            {
                              "label": "Tuần hoàn",
                              "customClass": "pr-2",
                              "key": "TUANHOAN",
                              "type": "textarea",
                              "rows": 2,
                              "input": true
                            }
                          ],
                          "width": 4,
                          "offset": 0,
                          "push": 0,
                          "pull": 0,
                          "size": "md",
                          "currentWidth": 4
                        },
                        {
                          "components": [
                            {
                              "label": "Hô hấp",
                              "customClass": "pr-2",
                              "key": "HOHAP",
                              "type": "textarea",
                              "rows": 2,
                              "input": true
                            }
                          ],
                          "width": 4,
                          "offset": 0,
                          "push": 0,
                          "pull": 0,
                          "size": "md",
                          "currentWidth": 4
                        },
                        {
                          "components": [
                            {
                              "label": "Tiêu hóa",
                              "key": "TIEUHOA",
                              "type": "textarea",
                              "rows": 2,
                              "input": true
                            }
                          ],
                          "size": "md",
                          "width": 4,
                          "offset": 0,
                          "push": 0,
                          "pull": 0,
                          "currentWidth": 4
                        }
                      ],
                      "customClass": "ml-0 mr-0",
                      "key": "CACCOQUAN",
                      "type": "columns",
                    },
                    {
                      "label": "CACCOQUAN2",
                      "columns": [
                        {
                          "components": [
                            {
                              "label": "Thần kinh",
                              "customClass": "pr-2",
                              "key": "THANKINH",
                              "type": "textarea",
                              "rows": 2,
                            }
                          ],
                          "width": 4,
                          "offset": 0,
                          "push": 0,
                          "pull": 0,
                          "size": "md",
                          "currentWidth": 4
                        },
                        {
                          "components": [
                            {
                              "label": "Cơ - Xương - Khớp",
                              "customClass": "pr-2",
                              "key": "XUONGKHOP",
                              "type": "textarea",
                              "rows": 2,
                            }
                          ],
                          "size": "md",
                          "width": 4,
                          "offset": 0,
                          "push": 0,
                          "pull": 0,
                          "currentWidth": 4
                        },
                        {
                          "components": [
                            {
                              "label": " Thận - Tiết niệu",
                              "key": "THANTIETNIEU",
                              "type": "textarea",
                              "rows": 2,
                            }
                          ],
                          "width": 4,
                          "offset": 0,
                          "push": 0,
                          "pull": 0,
                          "size": "md",
                          "currentWidth": 4
                        },
                      ],
                      "customClass": "ml-0 mr-0",
                      "key": "caccoquan2",
                      "type": "columns",
                    },
                  ],
                  "width": 8,
                  "offset": 0,
                  "push": 0,
                  "pull": 0,
                  "size": "md",
                  "currentWidth": 8
                },
                {
                  "components": [
                    {
                      "label": "Tabs",
                      "components": [
                        {
                          "label": "Chỉ số sinh tồn",
                          "key": "sinhhieu",
                          "components": [
                            {
                              "label": "chisosinhton1",
                              "columns": [
                                {
                                  "components": [
                                    {
                                      "label": "Mạch",
                                      "customClass": "pr-2",
                                      "validate": {
                                        "maxLength": 20,
                                        required: true
                                      },
                                      "key": "MACH",
                                      "type": "textarea",
                                    }
                                  ],
                                  "width": 4,
                                  "offset": 0,
                                  "push": 0,
                                  "pull": 0,
                                  "size": "md",
                                  "currentWidth": 4
                                },
                                {
                                  "components": [
                                    {
                                      "label": "Nhiệt độ",
                                      "customClass": "pr-2",
                                      "validate": {
                                        "min": 35,
                                        "max": 43,
                                        required: true
                                      },
                                      "key": "NHIETDO",
                                      "type": "number",
                                    }
                                  ],
                                  "width": 4,
                                  "offset": 0,
                                  "push": 0,
                                  "pull": 0,
                                  "size": "md",
                                  "currentWidth": 4
                                },
                                {
                                  "components": [
                                    {
                                      "label": "Nhịp thở",
                                      "validate": {
                                        "maxLength": 20,
                                        required: true
                                      },
                                      "key": "NHIPTHO",
                                      "type": "textarea",
                                    }
                                  ],
                                  "size": "md",
                                  "width": 4,
                                  "offset": 0,
                                  "push": 0,
                                  "pull": 0,
                                  "currentWidth": 4
                                }
                              ],
                              "customClass": "ml-0 mr-0",
                              "key": "chisosinhton1",
                              "type": "columns",
                            },
                            {
                              "label": "chisosinhton2",
                              "columns": [
                                {
                                  "components": [
                                    {
                                      "label": "Cân nặng (kg)",
                                      "customClass": "pr-2",
                                      "validate": {
                                        "min": 0,
                                        "max": 400,
                                        required: true
                                      },
                                      "key": "CANNANG",
                                      "type": "number",
                                    }
                                  ],
                                  "width": 4,
                                  "size": "md",
                                },
                                {
                                  "components": [
                                    {
                                      "label": "Chiều cao (cm)",
                                      "customClass": "pr-2",
                                      "validate": {
                                        "min": 1,
                                        "max": 400,
                                        required: true
                                      },
                                      "key": "CHIEUCAO",
                                      "type": "number",
                                    }
                                  ],
                                  "width": 4,
                                  "offset": 0,
                                  "push": 0,
                                  "pull": 0,
                                  "size": "md",
                                  "currentWidth": 4
                                },
                                {
                                  "components": [
                                    {
                                      "label": "BMI",
                                      "key": "BMI",
                                      others: {
                                        "disabled": true,
                                        "attributes": {
                                          "readonly": "true"
                                        },
                                      },
                                      "type": "number",
                                    }
                                  ],
                                  "size": "md",
                                  "width": 4,
                                  "offset": 0,
                                  "push": 0,
                                  "pull": 0,
                                  "currentWidth": 4
                                }
                              ],
                              "customClass": "ml-0 mr-0",
                              "key": "chisosinhton2",
                              "type": "columns",
                            },
                            {
                              "label": "chisosinhton3",
                              "columns": [
                                {
                                  "components": [
                                    {
                                      "tag": "label",
                                      "content": "Huyết áp",
                                      "refreshOnChange": false,
                                      "key": "htmllabel_huyetap",
                                      "type": "htmlelement",
                                    },
                                  ],
                                  "width": 12,
                                  "size": "md",
                                },
                                {
                                  "components": [
                                    {
                                      "label": "",
                                      "customClass": "pr-2",
                                      "validate": {
                                        "maxLength": 20,
                                        required: true
                                      },
                                      "key": "HUYETAPTREN",
                                      "type": "textarea",
                                    }
                                  ],
                                  "width": 6,
                                  "size": "md",
                                },
                                {
                                  "components": [
                                    {
                                      "label": "",
                                      "validate": {
                                        "maxLength": 20,
                                        required: true
                                      },
                                      "key": "HUYETAPDUOI",
                                      "type": "textarea",
                                    }
                                  ],
                                  "width": 6,
                                  "offset": 0,
                                  "push": 0,
                                  "pull": 0,
                                  "size": "md",
                                  "currentWidth": 6
                                }
                              ],
                              "customClass": "ml-0 mr-0",
                              "key": "chisosinhton3",
                              "type": "columns",
                            }
                          ]
                        }
                      ],
                      "customClass": "hsba-tabs-wrap pl-3",
                      "key": "tabs",
                      "type": "tabs",
                    },
                  ],
                  "width": 4,
                  "offset": 0,
                  "push": 0,
                  "pull": 0,
                  "size": "md",
                  customClass: "pl-2",
                  "currentWidth": 4
                },

              ],
              "customClass": "ml-0 mr-0",
              "key": "kb-column",
              "type": "columns",
              "input": false,
              "tableView": false
            },
            {
              "components": [
                {
                  "label": "Khác",
                  "customClass": "pr-2",
                  "key": "KHAC",
                  "type": "textarea",
                  "rows": 2,
                }
              ],
            },
            {
              "title": "3. Khám chuyên khoa",
              "label": "3. Khám chuyên khoa",
              "customClass": "hsba-panel-child-wrap",
              "key": "khamchuyenkhoa_panel",
              "type": "panel",
              "others": {
                "collapsible": true,
              },
              "components": [
                {
                  "components": [
                    {
                      "title": "a. Khám ngoài",
                      "label": "a. Khám ngoài",
                      "customClass": "hsba-panel-child-wrap",
                      "key": "khamngoai_panel",
                      "type": "panel",
                      "others": {
                        "collapsible": true,
                      },
                      "components": [
                        {
                          "components": [
                            {
                              "label": "Các dấu hiệu sinh dục thứ phát",
                              "key": "CACDAUHIEU",
                              "type": "textarea",
                              customClass: "pr-2",
                              rows: 1,
                            },
                          ],
                          "width": 12,
                          "size": "md",
                        },
                        {
                          "components": [
                            {
                              "label": "Môi lớn",
                              "key": "MOILON",
                              "type": "textarea",
                              customClass: "pr-2",
                              rows: 1,
                            },
                          ],
                          "width": 12,
                          "size": "md",
                        },
                        {
                          "components": [
                            {
                              "label": "Môi bé",
                              "key": "MOIBE",
                              "type": "textarea",
                              customClass: "pr-2",
                              rows: 1,
                            },
                          ],
                          "width": 12,
                          "size": "md",
                        },
                        {
                          "components": [
                            {
                              "label": "Âm vật",
                              "key": "AMVAT",
                              "type": "textarea",
                              customClass: "pr-2",
                              rows: 1,
                            },
                          ],
                          "width": 12,
                          "size": "md",
                        },
                        {
                          "components": [
                            {
                              "label": "Âm hộ",
                              "key": "AMHOT",
                              "type": "textarea",
                              customClass: "pr-2",
                              rows: 1,
                            },
                          ],
                          "width": 12,
                          "size": "md",
                        },
                        {
                          "components": [
                            {
                              "label": "Màng trinh",
                              "key": "MANGTRINH",
                              "type": "textarea",
                              customClass: "pr-2",
                              rows: 1,
                            },
                          ],
                          "width": 12,
                          "size": "md",
                        },
                        {
                          "components": [
                            {
                              "label": "Tầng sinh môn",
                              "key": "TANGSINHMOM",
                              "type": "textarea",
                              customClass: "pr-2",
                              rows: 1,
                            },
                          ],
                          "width": 12,
                          "size": "md",
                        },
                      ],
                    },
                    {
                      "title": "b. Khám trong",
                      "label": "b. Khám trong",
                      "customClass": "hsba-panel-child-wrap",
                      "key": "khamtrong_panel",
                      "type": "panel",
                      "others": {
                        "collapsible": true,
                      },
                      "components": [
                        {
                          "components": [
                            {
                              "label": "Âm đạo",
                              "key": "AMDAO",
                              "type": "textarea",
                              customClass: "pr-2",
                              rows: 1,
                            },
                          ],
                          "width": 12,
                          "size": "md",
                        },
                        {
                          "components": [
                            {
                              "label": "Cổ tử cung",
                              "key": "COTUCUNG",
                              "type": "textarea",
                              customClass: "pr-2",
                              rows: 1,
                            },
                          ],
                          "width": 12,
                          "size": "md",
                        },
                        {
                          "components": [
                            {
                              "label": "Thân tử cung",
                              "key": "THANTUCUNG",
                              "type": "textarea",
                              customClass: "pr-2",
                              rows: 1,
                            },
                          ],
                          "width": 12,
                          "size": "md",
                        },
                        {
                          "components": [
                            {
                              "label": "Phần phụ",
                              "key": "PHANPHU",
                              "type": "textarea",
                              customClass: "pr-2",
                              rows: 1,
                            },
                          ],
                          "width": 12,
                          "size": "md",
                        },
                        {
                          "components": [
                            {
                              "label": "Các túi cùng",
                              "key": "CACTUICUNG",
                              "type": "textarea",
                              customClass: "pr-2",
                              rows: 2,
                            },
                          ],
                          "width": 12,
                          "size": "md",
                        },
                      ],
                    },
                  ],
                  "width": 12,
                  "size": "md",
                },
              ],
            },
            {
              "label": "Copy cận lâm sàng",
              "customClass": "text-right form-control-sm line-height-1",
              "key": "copyclstdt",
              "type": "button",
              others: {
                "leftIcon": "fa fa-ellipsis-v",
                "action": "event",
                "showValidations": false,
                "event": "openmodalcopycls",
                "type": "button",
              }

            },
            {
              "label": "Các xét nghiệm cận lâm sàng cần làm",
              "key": "XETNGHIEM",
              "type": "textarea",
              "rows": 2,
              validate: {
                required: true
              }
            },
            {
              "label": "Tóm tắt bệnh án",
              "key": "TOMTATBENHAN",
              "type": "textarea",
              "rows": 2,
              validate: {
                required: true
              }
            }
          ]
        },
        {
          "collapsible": true,
          "key": "p-chandoanvadieutri",
          "type": "panel",
          "label": "CHẨN ĐOÁN VÀ ĐIỀU TRỊ",
          "title": "CHẨN ĐOÁN VÀ ĐIỀU TRỊ",
          "collapsed": false,
          "input": false,
          "tableView": false,
          "customClass": "hsba-tabs-wrap",
          "components": [
            {
              label: "",
              key: "wrap_benhchinh",
              columns: [
                {
                  "components": [
                    {
                      "tag": "label",
                      "content": "Bệnh chính",
                      "refreshOnChange": false,
                      "key": "htmllabel_benhchinh",
                      "type": "htmlelement",
                    },
                  ],
                  "width": 12,
                  "size": "md",
                },
                {
                  "components": [
                    {
                      "label": "",
                      "key": "ICD_BENHCHINH",
                      "type": "textfield",
                      customClass: "pr-2",
                      others: {
                        "placeholder": "ICD",
                      }
                    },
                  ],
                  "width": 2,
                  "size": "md",
                },
                {
                  "components": [
                    {
                      "label": "",
                      "key": "TENICD_BENHCHINH",
                      "type": "textfield",
                      others: {
                        "placeholder": "Tên bệnh chính",
                      },
                      validate: {
                        required: true
                      }
                    },
                  ],
                  "width": 10,
                  "size": "md",
                },

              ],
              "customClass": "ml-0 mr-0",
              "type": "columns",
            },
            {
              label: "",
              key: "wrap_benhphu",
              columns: [
                {
                  "components": [
                    {
                      "tag": "label",
                      "attrs": [
                        {
                          "attr": "",
                          "value": ""
                        }
                      ],
                      "content": "Bệnh kèm theo (nếu có)",
                      "key": "htmllabel_benhphu",
                      "type": "htmlelement",
                    },
                  ],
                  "width": 12,
                  "size": "md",
                },
                {
                  "components": [
                    {
                      "label": "",
                      "key": "ICD_BENHPHU",
                      "type": "textfield",
                      customClass: "pr-2",
                      others: {
                        "placeholder": "ICD",
                      }
                    },
                  ],
                  "width": 2,
                  "size": "md",
                },
                {
                  "components": [
                    {
                      "label": "",
                      "key": "TENICD_BENHPHU",
                      "type": "textfield",
                      others: {
                        "placeholder": "Tên bệnh",
                      }
                    },
                  ],
                  "width": 10,
                  "size": "md",
                },
                {
                  "components": [
                    {
                      "label": "",
                      "key": "BENHPHU",
                      "type": "textarea",
                      "rows": 2,
                      "input": true
                    },
                  ],
                  "width": 12,
                  "size": "md",
                },
              ],
              "customClass": "ml-0 mr-0",
              "type": "columns",
            },
            {
              "label": "Phân biệt",
              "key": "PHANBIET",
              "type": "textarea",
              "rows": 2,
            },
            {
              "label": "Tiên lượng",
              "key": "TIENLUONG",
              "type": "textarea",
              "rows": 2,
              validate: {
                required: true
              }
            },
            {
              "label": "Hướng điều trị",
              "key": "HUONGDIEUTRI",
              "type": "textarea",
              "rows": 2,
              validate: {
                required: true
              }
            }
          ]
        },
        getObjectThoigianBacsilambenhanFormio()
      ])
    },
    initObjectFormPage3: function() {
      return getJSONObjectForm([
        {
          "collapsible": true,
          "key": "p-tongketdieutri",
          "type": "panel",
          "label": "TỔNG KẾT BỆNH ÁN",
          "title": "TỔNG KẾT BỆNH ÁN",
          "collapsed": false,
          "input": false,
          "tableView": false,
          "customClass": "hsba-tabs-wrap",
          components: [
            {
              "label": "Quá trình bệnh lý và diễn biến lâm sàng",
              "key": "quaTrinhBenhLy",
              "type": "textarea",
              rows: 2,
              validate: {
                "minLength": 5,
                "maxLength": 3000,
                required: true,
              }
            },
            {
              "label": "Copy cận lâm sàng",
              "customClass": "text-right form-control-sm line-height-1",
              "key": "copytomtatcls",
              "type": "button",
              others: {
                "leftIcon": "fa fa-ellipsis-v",
                "action": "event",
                "showValidations": false,
                "event": "openmodalcopytomtatcls",
                "type": "button",
              }

            },
            {
              "label": "Tóm tắt kết quả xét nghiệm cận lâm sàng có giá trị chẩn đoán",
              "key": "tomTatKetQuaXNCLS",
              "type": "textarea",
              rows: 2,
              validate: {
                "minLength": 5,
                "maxLength": 3000,
                required: true,
              }
            },
            {
              "label": "Phương pháp điều trị",
              "key": "phuongPhapDieuTri",
              "type": "textarea",
              rows: 2,
              validate: {
                "minLength": 5,
                "maxLength": 3000,
                required: true,
              }
            },
            {
              "label": "Thuật/phẫu thuật",
              others: {
                "data": {
                  "values": [
                    {
                      "label": "Không",
                      "value": "0"
                    },
                    {
                      "label": "Phẫu thuật",
                      "value": "1"
                    },
                    {
                      "label": "Thủ thuật",
                      "value": "2"
                    },

                  ]
                },
              },
              key: "thuThuatPhauThuat",
              "customClass": "pr-2",
              "type": "select",
            },
            {
              "label": "Danh sách lần thủ thuật/phẫu thuật",
              "tableView": false,
              "rowDrafts": false,
              "key": "chiTietThuThuatPhauThuat",
              "type": "editgrid",
              "displayAsTable": false,
              "input": true,
              "components": [
                {
                  "label": "Columns",
                  "columns": [
                    {
                      "components": [
                        {
                          "label": "Ngày giờ",
                          "key": "GIONGAY",
                          "type": "datetime",
                          "format": "dd/MM/yyyy hh:mm",
                          customClass: "pr-2",
                          enableTime: true,
                          minDate: moment(thongtinhsba.thongtinbn.NGAY_VAO_VIEN, ['DD/MM/YYYY']).format("YYYY-MM-DD"),
                        }
                      ],
                      "size": "md",
                      "width": 2
                    },
                    {
                      "components": [
                        {
                          "label": "Phương pháp phẫu thuật/vô cảm",
                          "key": "PHUONGPHAP",
                          "type": "textfield",
                          customClass: "pr-2",
                        }
                      ],
                      "width": 4,
                      "size": "md"
                    },
                    {
                      "components": [
                        {
                          "label": "Bác sĩ phẫu thuật",
                          "key": "BACSIPHAUTHUAT",
                          "type": "textfield",
                          customClass: "pr-2",
                        }
                      ],
                      "size": "md",
                      "width": 3
                    },
                    {
                      "components": [
                        {
                          "label": "Bác sĩ gây mê",
                          "key": "BACSIGAYME",
                          "type": "textfield",
                          customClass: "pr-2",
                        }
                      ],
                      "size": "md",
                      "width": 3
                    },
                  ],
                  "key": "columns",
                  "type": "columns",
                }
              ],
              others: {
                "templates": {
                  "header": "<div class=\"row\">\n      {% util.eachComponent(components, function(component) { %}\n        {% if (component.key == 'GIONGAY') { %}\n          <div class=\"col-sm-2\">{{ t(component.label) }}</div>\n        {% } %}\n        {% if (displayValue(component) && component.key == 'PHUONGPHAP') { %}\n          <div class=\"col-sm-4\">{{ t(component.label) }}</div>\n        {% } %}\n        {% if (displayValue(component) && component.key == 'BACSIPHAUTHUAT') { %}\n          <div class=\"col-sm-3\">{{ t(component.label) }}</div>\n        {% } %}\n        {% if (displayValue(component) && component.key == 'BACSIGAYME') { %}\n          <div class=\"col-sm-2\">{{ t(component.label) }}</div>\n        {% } %}\n      {% }) %}\n    </div>",
                  "row": "<div class=\"row\">\n      {% util.eachComponent(components, function(component) { %}\n        {% if (component.key == 'GIONGAY') { %}\n          <div class=\"col-sm-2\">\n            {{ isVisibleInRow(component) ? getView(component, row[component.key]) : ''}}\n          </div>\n        {% } %}\n        {% if (displayValue(component) && component.key == 'PHUONGPHAP') { %}\n          <div class=\"col-sm-4\">\n            {{ isVisibleInRow(component) ? getView(component, row[component.key]) : ''}}\n          </div>\n        {% } %}\n        {% if (displayValue(component) && component.key == 'BACSIPHAUTHUAT') { %}\n          <div class=\"col-sm-3\">\n            {{ isVisibleInRow(component) ? getView(component, row[component.key]) : ''}}\n          </div>\n        {% } %}\n        {% if (displayValue(component) && component.key == 'BACSIGAYME') { %}\n          <div class=\"col-sm-2\">\n            {{ isVisibleInRow(component) ? getView(component, row[component.key]) : ''}}\n          </div>\n        {% } %}\n      {% }) %}\n      {% if (!instance.options.readOnly && !instance.disabled) { %}\n        <div class=\"col-sm-1\">\n          <div class=\"btn-group pull-right\">\n            <button class=\"btn btn-default btn-primary btn-sm editRow\"><i class=\"fa fa-pencil-square-o\"></i></button>\n            {% if (!instance.hasRemoveButtons || instance.hasRemoveButtons()) { %}\n              <button class=\"btn btn-danger btn-sm removeRow\"><i class=\"fa fa-trash-o\"></i></button>\n            {% } %}\n          </div>\n        </div>\n      {% } %}\n    </div>"
                },
                "addAnother": "Thêm mới",
                "saveRow": "Lưu",
                "removeRow": "Hủy",
                "customConditional": "show = data.thuThuatPhauThuat == 1 || data.thuThuatPhauThuat == 2;",
              }
            },
            {
              "label": "Tình trạng người bệnh ra viện",
              "key": "tinhTrangNguoiBenhRaVien",
              "type": "textarea",
              rows: 2,
              validate: {
                "minLength": 5,
                "maxLength": 3000,
                required: true,
              }
            },
            {
              "label": "Hướng điều trị và các chế độ tiếp theo",
              "key": "huongDieuTriVaCacCheDo",
              "type": "textarea",
              rows: 2,
              validate: {
                "minLength": 5,
                "maxLength": 3000,
                required: true,
              }
            },
          ]
        },
        getObjectThoigianTongketFormio()
      ]);
    },
    callbackAfterLoad: function (instance, callBack) {
      form = instance;
      var tenBenhchinhElement = form.getComponent('TENICD_BENHCHINH');
      var icdBenhchinhElement = form.getComponent('ICD_BENHCHINH');
      var tenBenhphuElement = form.getComponent('TENICD_BENHPHU');
      var icdBenhphuElement = form.getComponent('ICD_BENHPHU');
      var textBenhphuElement = form.getComponent('BENHPHU');
      var bacsilambenhanElement = form.getComponent('MABACSILAMBENHAN');
      var bmiElement = form.getComponent('BMI');
      var cannangElement = form.getComponent('CANNANG');
      var chieucaoElement = form.getComponent('CHIEUCAO');

      $("#"+getIdElmentFormio(form,'ICD_BENHCHINH')).on('keypress', function(event) {
        var mabenhICD = $(this).val();
        if(event.keyCode == 13 && mabenhICD != "") {
          mabenhICD = mabenhICD.toUpperCase();
          getMotabenhly(mabenhICD, function(data) {
            var splitIcd = data.split("!!!")
            tenBenhchinhElement.setValue(splitIcd[1]);
            icdBenhchinhElement.setValue(mabenhICD)
          })
        }
      })
      $("#"+getIdElmentFormio(form,'ICD_BENHPHU')).on('keypress', function(event) {
        var mabenhICD = $(this).val();
        if(event.keyCode == 13 && mabenhICD != "") {
          mabenhICD = mabenhICD.toUpperCase();
          getMotabenhly(mabenhICD, function(data) {
            var splitIcd = data.split("!!!")
            tenBenhphuElement.setValue(splitIcd[1]);
            tenBenhphuElement.focus()
            icdBenhphuElement.setValue(mabenhICD)
          })
        }
      })
      $("#"+getIdElmentFormio(form,'TENICD_BENHPHU')).on('keypress', function(event) {
        if(event.keyCode == 13) {
          var stringIcd = textBenhphuElement.getValue();
          var mabenhICD = icdBenhphuElement.getValue()
          if(!stringIcd.includes(mabenhICD)) {
            textBenhphuElement.setValue( stringIcd + "; (" +mabenhICD.toUpperCase() + ") " + tenBenhphuElement.getValue());
          }
          icdBenhphuElement.setValue("")
          tenBenhphuElement.setValue("")
          icdBenhphuElement.focus()
        }
      })

      $("#"+getIdElmentFormio(form,'MAKHOA')).change(function() {
        if(!$(this).val()) {
          return;
        }
        getBacsiByKhoaFormio($(this).val(), bacsilambenhanElement)
      })

      $("#"+getIdElmentFormio(form,'CANNANG')).change(function() {
        if(!$(this).val() || !chieucaoElement.getValue()) {
          return;
        }
        bmiElement.setValue((chieucaoElement.getValue()/Math.pow($(this).val()/100, 2)).toFixed(2))
      })

      $("#"+getIdElmentFormio(form,'CHIEUCAO')).change(function() {
        if(!$(this).val() || !cannangElement.getValue()) {
          return;
        }
        bmiElement.setValue((cannangElement.getValue()/Math.pow($(this).val()/100, 2)).toFixed(2))
      })

      instance.on('openmodalcopycls', function(click) {
        addTextTitleModal("titleModalFormhsbacopyylenhcls")
        $("#modalFormhsbacopyylenhcls").modal("show");
        $(document).trigger("reloadDSCLSChiDinh");
      });

      var idWrap = "hsba_vba_trang2-tab";
      showLoaderIntoWrapId(idWrap)
      getThongtinBenhan(thongtinhsba.thongtinbn.VOBENHAN[0].ID, 'PHUKHOA', function(dataTrang2) {
        hideLoaderIntoWrapId(idWrap)
        let phieuNhapVien = thongtinhsba.phieunhapvien;
        let pdtdautien = thongtinhsba.phieudieutridautien;
        delete dataTrang2.ID;
        dataTrang2.SELECTBOX_DAUBUNG = {
          BOXDAUBUNG: dataTrang2.BOXDAUBUNG == "1" ? true : false,
        }
        dataTrang2.SELECTBOX_THOIGIAN = {
          BOXTRUOC: dataTrang2.BOXTRUOC == "1" ? true : false,
          BOXTRONG: dataTrang2.BOXTRONG == "1" ? true : false,
          BOXSAU: dataTrang2.BOXSAU == "1" ? true : false,
        }
        dataTrang2.KINHLANCUOINGAY = moment(dataTrang2.KINHLANCUOINGAY, 'DD/MM/YYYY HH:mm').format('YYYY-MM-DDTHH:mm:ssZ');
        dataTrang2.MAKHOA = dataTrang2.MAKHOA? dataTrang2.MAKHOA: singletonObject.makhoa;
        dataTrang2.XETNGHIEM = dataTrang2.XETNGHIEM ?? thongtinhsba.clsDaThucHien.XET_NGHIEM ?? "";
        if (!$.isEmptyObject(phieuNhapVien)){
          dataTrang2.LYDOVAOVIEN = dataTrang2.LYDOVAOVIEN ?? phieuNhapVien.LY_DO_VAO_VIEN;
          dataTrang2.QUATRINHBENHLY = dataTrang2.QUATRINHBENHLY ??  phieuNhapVien.QUA_TRINH_BENH_LY;
          dataTrang2.TIENSUBANTHAN = dataTrang2.TIENSUBANTHAN ?? phieuNhapVien.TIEN_SU_BAN_THAN;
          dataTrang2.TIENSUGIADINH = dataTrang2.TIENSUGIADINH ?? phieuNhapVien.TIEN_SU_GIA_DINH;
          dataTrang2.DANIENMAC = dataTrang2.DANIENMAC ?? phieuNhapVien.KHAM_XET_TOAN_THAN;
          dataTrang2.MACH = dataTrang2.MACH ?? phieuNhapVien.MACH;
          dataTrang2.NHIETDO = dataTrang2.NHIETDO ?? phieuNhapVien.NHIET_DO;
          dataTrang2.NHIPTHO = dataTrang2.NHIPTHO ?? phieuNhapVien.NHIP_THO;
          dataTrang2.CANNANG = dataTrang2.CANNANG ?? phieuNhapVien.CAN_NANG;
          dataTrang2.HUYETAPTREN = dataTrang2.HUYETAPTREN ?? phieuNhapVien.HUYET_AP_CAO;
          dataTrang2.HUYETAPDUOI = dataTrang2.HUYETAPDUOI ?? phieuNhapVien.HUYET_AP_THAP;
        }
        if (!$.isEmptyObject(pdtdautien)){
          if (dataTrang2.BENHCHINH === null) {
            dataTrang2.BENHCHINH = pdtdautien.ICD_DIEUTRI != null ? pdtdautien.ICD_DIEUTRI + ' - ' + pdtdautien.TENICD_DIEUTRI : '';
          }
          dataTrang2.BENHPHU = dataTrang2.BENHPHU ?? pdtdautien.TEN_BENHPHU;
        }
        if(dataTrang2.BENHCHINH && dataTrang2.BENHCHINH.includes(" - ")) {
          var splitIcd = dataTrang2.BENHCHINH.split(" - ");
          dataTrang2.ICD_BENHCHINH = splitIcd[0];
          dataTrang2.TENICD_BENHCHINH = splitIcd[1];
        }
        dataTrang2.NGAYBSLAMBENHAN =  (dataTrang2.NGAYBSLAMBENHAN? moment(dataTrang2.NGAYBSLAMBENHAN): moment()).toISOString()
        getBacsiByKhoaFormio(dataTrang2.MAKHOA, bacsilambenhanElement);
        if (dataTrang2.MACH == null || dataTrang2.MACH == undefined || dataTrang2.MACH == "") {
          var res = $.ajax({
            url:"cmu_list_CMU_HSBA_GETDEF?url="+
                convertArray([singletonObject.dvtt, thongtinhsba.thongtinbn.SOVAOVIEN, thongtinhsba.thongtinbn.SOVAOVIEN_DT]),
            type:"GET",
            async: false
          }).responseText;

          var dataDef = JSON.parse(res);
          dataTrang2.MACH = dataDef[0].MACH;
          dataTrang2.NHIETDO = dataDef[0].NHIETDO;
          dataTrang2.NHIPTHO = dataDef[0].NHIPTHO;
          dataTrang2.HUYETAPTREN = dataDef[0].HUYETAPTREN;
          dataTrang2.HUYETAPDUOI = dataDef[0].HUYETAPDUOI;
          dataTrang2.CANNANG = dataDef[0].CANNANG;
          dataTrang2.CHIEUCAO = dataDef[0].CHIEUCAO;
          if(!isNaN(dataTrang2.CANNANG) && !isNaN(dataTrang2.CHIEUCAO)) {
            dataTrang2.BMI = (dataTrang2.CANNANG/Math.pow(dataTrang2.CHIEUCAO/100, 2)).toFixed(2);
          }
        }
        var dataCallBack = form.submission =  {
          data: {
            ...dataTrang2
          }
        };
        callBack && callBack(dataCallBack);
      }, function() {
        hideLoaderIntoWrapId(idWrap)
        notifiToClient("Red", "Lỗi load thông tin bệnh án")
      });
    },
    save: function(element, callBackSave, callBackLog) {
      var idButton = element.id;
      if (!('KINHLANCUOINGAY' in form.submission.data) || form.submission.data.KINHLANCUOINGAY === "Invalid date") {
        form.submission.data.KINHLANCUOINGAY = null;
      }
      form.emit("checkValidity");
      if (!form.checkValidity(null, false, null, true)) {
        hideSelfLoading(idButton);
        return;
      }
      var dataSubmit = form.submission.data;
      delete dataSubmit.copyclstdt
      var ngayba = moment(dataSubmit.NGAYBSLAMBENHAN)
      dataSubmit.BACSILAMBENHAN = getTextSelectedFormio(form.getComponent('MABACSILAMBENHAN')).split(" - ")[1];
      dataSubmit.ngayLamBenhAn = "Ngày " + ngayba.format("DD") + " tháng " + ngayba.format("MM") + " năm " + ngayba.format("YYYY");
      dataSubmit.ID = thongtinhsba.thongtinbn.VOBENHAN[0].ID;
      var data = {
        "iD": thongtinhsba.thongtinbn.VOBENHAN[0].ID,
        "lyDoVaoVien": dataSubmit.LYDOVAOVIEN,
        "quaTrinhBenhLy": dataSubmit.QUATRINHBENHLY,
        "tienSuBanThan": dataSubmit.TIENSUBANTHAN,
        "tienSuGiaDinh": dataSubmit.TIENSUGIADINH,
        "namBatDauKinh": dataSubmit.BATDAUKINHNAM,
        "tuoiCoKinh": dataSubmit.TUOICOKINH,
        "tinhChatKinhNguyet": dataSubmit.TINHCHATKINHNGUYET,
        "chuKyKinhNguyet": dataSubmit.CHUKY,
        "soNgayThayKinh": dataSubmit.SONGAYTHAYKINH,
        "luongKinh": dataSubmit.LUONGKINH,
        "ngayKinhLanCuoi": dataSubmit.KINHLANCUOINGAY ? moment(dataSubmit.KINHLANCUOINGAY).format("DD/MM/YYYY HH:mm") : "",
        "dauBung": dataSubmit.SELECTBOX_DAUBUNG.BOXDAUBUNG ? "1" : "0",
        "thgianTruoc": dataSubmit.SELECTBOX_THOIGIAN.BOXTRUOC ? "1" : "0",
        "thgianTrong": dataSubmit.SELECTBOX_THOIGIAN.BOXTRONG ? "1" : "0",
        "thgianSau": dataSubmit.SELECTBOX_THOIGIAN.BOXSAU ? "1" : "0",
        "namLayChong": dataSubmit.LAYCHONGNAM,
        "tuoiLayChong": dataSubmit.TUOILAYCHONG,
        "namHetKinh": dataSubmit.HETKINHNAM,
        "tuoiHetKinh": dataSubmit.TUOIHETKINH,
        "benhPhuKhoaDaTri": dataSubmit.BENHPHUKHOA,
        "tienThaiParaSinh": dataSubmit.BOXTIENTHAISINH,
        "tienThaiParaSom": dataSubmit.BOXTIENTHAISOM,
        "tienThaiParaSay": dataSubmit.BOXTIENTHAISAY,
        "tienThaiParaSong": dataSubmit.BOXTIENTHAISONG,
        "daNiemMac": dataSubmit.DANIENMAC,
        "toanThanHach": dataSubmit.HACH,
        "toanThanVu": dataSubmit.VU,
        "mach": dataSubmit.MACH,
        "nhietDo": dataSubmit.NHIETDO,
        "huyetApTren": dataSubmit.HUYETAPTREN,
        "huyetApDuoi": dataSubmit.HUYETAPDUOI,
        "nhipTho": dataSubmit.NHIPTHO,
        "canNang": dataSubmit.CANNANG,
        "chieuCao": dataSubmit.CHIEUCAO,
        "tuanHoan": dataSubmit.TUANHOAN,
        "hoHap": dataSubmit.HOHAP,
        "tieuHoa": dataSubmit.TIEUHOA,
        "thanKinh": dataSubmit.THANKINH,
        "coXuongKhop": dataSubmit.XUONGKHOP,
        "thanTietNieu": dataSubmit.THANTIETNIEU,
        "khac": dataSubmit.KHAC,
        "dhSinhDucThuPhat": dataSubmit.CACDAUHIEU,
        "moiLon": dataSubmit.MOILON,
        "moiBe": dataSubmit.MOIBE,
        "amVat": dataSubmit.AMVAT,
        "amHot": dataSubmit.AMHOT,
        "mangTrinh": dataSubmit.MANGTRINH,
        "tangSinhMon": dataSubmit.TANGSINHMOM,
        "amDao": dataSubmit.AMDAO,
        "coTuCung": dataSubmit.COTUCUNG,
        "thanTuCung": dataSubmit.THANTUCUNG,
        "phanPhu": dataSubmit.PHANPHU,
        "cacTuiCung": dataSubmit.CACTUICUNG,
        "xnCanLamSang": dataSubmit.XETNGHIEM,
        "tomTatBenhAn": dataSubmit.TOMTATBENHAN,
        "cdBenhChinh": dataSubmit.ICD_BENHCHINH + " - " + dataSubmit.TENICD_BENHCHINH,
        "cdBenhKemTheo": dataSubmit.BENHPHU,
        "cdPhanBiet": dataSubmit.PHANBIET,
        "tienLuong": dataSubmit.TIENLUONG,
        "huongDieuTri": dataSubmit.HUONGDIEUTRI,
        "ngayLamBenhAn": dataSubmit.ngayLamBenhAn,
        "bacSiLamBenhAn": dataSubmit.BACSILAMBENHAN,
      }
      $.ajax({
        type: "POST",
        url: "update-benhan-phukhoa",
        dataType: "json",
        contentType: 'application/json',
        data: JSON.stringify(data),
        success: function (data) {
          if (data.SUCCESS == 1) {
            let dsBacSi = singletonObject.danhsachnhanvien;
            !callBackSave && notifiToClient("Green", "Lưu thành công");
            updateNgaylamVaBSHSBA({
              ...dataSubmit,
              NGAYBA: ngayba.format("DD/MM/YYYY"),
            }, function () {
              callBackSave && callBackSave({keyword: "(Ký, ghi rõ họ tên)"});
              dataOld.BACSILAMBENHAN = getTenBacSiLog(dsBacSi, dataOld.MABACSILAMBENHAN)
              dataOld.ngayLamBenhAn = dataOld.NGAY_LAMBENHAN
              dataSubmit.ICD_BENHPHU = dataOld.ICD_BENHPHU = "";
              dataSubmit.BACSILAMBENHAN = getTenBacSiLog(dsBacSi, dataSubmit.MABACSILAMBENHAN)
              callBackLog && callBackLog(dataSubmit);
            });

            !callBackSave && hideSelfLoading(idButton);
          } else {
            notifiToClient("Red", "Lỗi lưu thông tin");
          }
          hideSelfLoading(idButton);
        },
        error: function (error) {
          notifiToClient("Red", "Lỗi lưu thông tin");
          hideSelfLoading(idButton);
        }
      });
    },
    callbackAfterLoadTongket: function (instance, callBack) {
      formTongket = instance;
      var bacsiketthucBAElement = formTongket.getComponent('MABACSIDIEUTRI');
      var idWrap = "hsba_vba_trang3-tab";
      $("#"+getIdElmentFormio(formTongket,'MAKHOA_KETHUC')).change(function() {
        if(!$(this).val()) {
          return;
        }
        getBacsiByKhoaFormio($(this).val(), bacsiketthucBAElement)
      })
      instance.on('openmodalcopytomtatcls', function(click) {
        addTextTitleModal("titleModalTomtatketCLSDieutri")
        $("#modalTomtatketCLSDieutri").modal("show");
        $(document).trigger("reloadDSTomtatCLS");
        $("#tomtatketCLSDieutriTabs").attr("data-function-copy", "copyTomtatKetquaCLSPage3")
      });
      showLoaderIntoWrapId(idWrap)
      getThongtinTongket(thongtinhsba.thongtinbn.VOBENHAN[0].ID, 'PHUKHOA', function(dataTrang3) {
        hideLoaderIntoWrapId(idWrap)
        dataTrang3.MAKHOA_KETHUC = dataTrang3.MAKHOA_KETHUC? dataTrang3.MAKHOA_KETHUC: singletonObject.makhoa;
        var ctTTPT = dataTrang3.CHITIET_THUTHUAT_PHAUTHUAT ? JSON.parse(dataTrang3.CHITIET_THUTHUAT_PHAUTHUAT) : []
        for(var i = 0; i < ctTTPT.length; i++){
          ctTTPT[i].GIONGAY = moment(ctTTPT[i].GIONGAY, "DD/MM/YYYY HH:mm").toISOString();
        }
        var danhsachnhanvien;
        formTongket.getComponent('MANHANVIEN_GIAOHOSO', function(component) {
          danhsachnhanvien = component.component.data.values;
        });
        var NGUOIGIAO_HOSO = danhsachnhanvien.find(opt => opt.label == dataTrang3.NGUOIGIAO_HOSO);
        var NGUOINHAN_HOSO = danhsachnhanvien.find(opt => opt.label == dataTrang3.NGUOINHAN_HOSO);
        dataTrang3.MANHANVIEN_GIAOHOSO = NGUOIGIAO_HOSO ? NGUOIGIAO_HOSO.value: null;
        dataTrang3.MANHANVIEN_NHANHOSO = NGUOINHAN_HOSO ? NGUOINHAN_HOSO.value: null;
        var danhsachkhoa;
        formTongket.getComponent('MAKHOA_KETHUC', function(component) {
          danhsachkhoa = component.component.data.values;
        });
        var tenKhoaKetThuc = danhsachkhoa.find(opt => opt.value == dataTrang3.MAKHOA_KETHUC);
        var dataCallBack = formTongket.submission =  {
          data: {
            thuThuatPhauThuat: dataTrang3.THUTHUAT_PHAUTHUAT,
            quaTrinhBenhLy: dataTrang3.QUATRINH_BENHLY,
            tomTatKetQuaXNCLS: dataTrang3.TOMTAT_KETQUA,
            phuongPhapDieuTri: dataTrang3.PHUONGPHAP_DIEUTRI,
            tinhTrangNguoiBenhRaVien: dataTrang3.TINHTRANG_RAVIEN,
            chiTietThuThuatPhauThuat: ctTTPT,
            huongDieuTriVaCacCheDo: dataTrang3.HUONG_DIEUTRI,
            soToXQuang: dataTrang3.SOTO_XQUANG,
            soToCTScanner: dataTrang3.SOTO_CTSCANNER,
            soToSieuAm: dataTrang3.SOTO_SIEUAM,
            soToXetNghiem: dataTrang3.SOTO_XETNGHIEM,
            soToKhac: dataTrang3.SOTO_KHAC,
            toanBoHoSo: dataTrang3.SOTO_TOANBOHS,
            loaiGiayToKhac: dataTrang3.LOAI_GIAYTO_KHAC,
            MAKHOA_KETHUC: dataTrang3.MAKHOA_KETHUC,
            KHOA_KETHUC: tenKhoaKetThuc.label,
            MABACSIDIEUTRI: dataTrang3.MABACSIDIEUTRI,
            NGUOIGIAO_HOSO: NGUOIGIAO_HOSO ? NGUOIGIAO_HOSO.label : "",
            NGUOINHAN_HOSO: NGUOINHAN_HOSO ? NGUOINHAN_HOSO.label : "",
            NGAY_TONGKET: (dataTrang3.NGAY_TONGKET_DATETIME? moment(dataTrang3.NGAY_TONGKET_DATETIME, ['DD/MM/YYYY HH:mm']): moment()).toISOString(),
            MANHANVIEN_GIAOHOSO: dataTrang3.MANHANVIEN_GIAOHOSO,
            MANHANVIEN_NHANHOSO: dataTrang3.MANHANVIEN_NHANHOSO,
          }
        };
        callBack && callBack(dataCallBack);
        getBacsiByKhoaFormio(dataTrang3.MAKHOA_KETHUC, bacsiketthucBAElement);

      }, function() {
        hideLoaderIntoWrapId(idWrap)
        notifiToClient("Red", "Lỗi load thông tin bệnh án")
      });
    },
    saveTongket: function(element, callBackSave, callBackLog) {
      var idButton = element.id;
      formTongket.emit("checkValidity");
      if (!formTongket.checkValidity(null, false, null, true)) {
        hideSelfLoading(idButton);
        return;
      }
      var dataSubmit = formTongket.submission.data;
      var chiTietThuThuatPhauThuat = dataSubmit.chiTietThuThuatPhauThuat ? dataSubmit.chiTietThuThuatPhauThuat : [];
      chiTietThuThuatPhauThuat.forEach(function(item) {
        item.GIONGAY = moment(item.GIONGAY).format('DD/MM/YYYY HH:mm');
      });
      dataSubmit.id = thongtinhsba.thongtinbn.VOBENHAN[0].ID;
      dataSubmit.chiTietThuThuatPhauThuat = JSON.stringify(chiTietThuThuatPhauThuat);
      dataSubmit.giaiPhauBenh = "";
      dataSubmit.benh = "";
      var ngayba = moment(dataSubmit.NGAY_TONGKET)
      dataSubmit.ngayTongKet =  ngayba.format("DD/MM/YYYY HH:mm");
      dataSubmit.bacSiDieuTri =  getTextSelectedFormio(formTongket.getComponent('MABACSIDIEUTRI')).split(" - ")[1];
      dataSubmit.nguoiGiaoHoSo = getTextSelectedFormio(formTongket.getComponent('MANHANVIEN_GIAOHOSO')).split(" - ")[1];
      dataSubmit.nguoiNhanHoSo = getTextSelectedFormio(formTongket.getComponent('MANHANVIEN_NHANHOSO')).split(" - ")[1];
      $.ajax({
        url: "TongKetBenhAn_Update",
        type: 'POST',
        data: JSON.stringify(dataSubmit),
        contentType: 'application/json',
        success: function (data) {
          if (data.SUCCESS == 1) {
            !callBackSave && notifiToClient("Green", "Lưu thành công")
            updateThongtinPage3(dataSubmit, function () {
              callBackSave && callBackSave({keyword: "Bác sĩ điều trị"});

              dataSubmit.NGUOIGIAO_HOSO = dataSubmit.nguoiGiaoHoSo;
              dataSubmit.NGUOINHAN_HOSO = dataSubmit.nguoiNhanHoSo;
              dataSubmit.KHOA_KETHUC = getTextSelectedFormio(formTongket.getComponent('MAKHOA_KETHUC'));
              callBackLog && callBackLog(dataSubmit);
            });

            !callBackSave && hideSelfLoading(idButton);
          } else {
            notifiToClient("Red", "Lưu thông tin bệnh án không thành công")
          }
          hideSelfLoading(idButton);
        },
        error: function (error) {
          notifiToClient("Red", "Lỗi lưu thông tin")
          hideSelfLoading(idButton);
        }
      })

    },
    callbackAfterLoadPage1: function (instance, callBack) {
      formPage1 = instance;
      var idWrap = "hsba_vba_trang1-tab";
      showLoaderIntoWrapId(idWrap);

      var dataTrang1 = thongtinhsba.thongtinbn.VOBENHAN[0].TRANG1.INFO;
      var caseNN = dataTrang1.THUTHUAT_PHAUTHUAT;
      var textNN = "";
      switch (caseNN) {
        case 4:
          textNN = "Khác";
          break;
        case 3:
          textNN = "Do nhiễm khuẩn";
          break;
        case 2:
          textNN = "Do gây mê";
          break;
        case 1:
          textNN = "Do phẫu thuật";
          break;
        default:
          textNN = "Chưa cập nhật";
      }
      thongtinhsba.thongtinbn.VOBENHAN[0].TRANG1.INFO["NN_TAIBIEN_BIENCHUNG_TEXT"] = textNN;

      const promises = [
        actionLoadObjectQuanLyNguoiBenhVBAT1_1(formPage1, dataTrang1),
        actionLoadObjectChanDoanVBAT1_1(formPage1, dataTrang1),
        actionLoadObjectTinhTrangRaVienVBAT1_1(formPage1, dataTrang1)
      ];
      Promise.all(promises)
          .then(results => {
            var dataCallBack = formPage1.submission =  {
              data: {
                ...dataTrang1,
                NN_TAIBIEN_BIENCHUNG: dataTrang1.THUTHUAT_PHAUTHUAT,
              }
            };
            callBack && callBack(dataCallBack);
            hideLoaderIntoWrapId(idWrap)
          })
          .catch(error => {
            console.error("An error occurred:", error);
            hideLoaderIntoWrapId(idWrap);
          });

    },
    savePage1: function(element, callBackLog) {
      var idButton = element.id;
      formPage1.emit("checkValidity");
      if (!formPage1.checkValidity(null, false, null, true)) {
        hideSelfLoading(idButton);
        return;
      }
      var dataSubmit = formPage1.submission.data;
      var dataTrang1 = thongtinhsba.thongtinbn.VOBENHAN[0].TRANG1;
      dataTrang1.INFO.CHUYENKHOASONGAY0 = dataSubmit.CHUYENKHOASONGAY0;
      dataTrang1.INFO.CHUYENKHOATHOIGIAN0 = moment(dataSubmit.CHUYENKHOATHOIGIAN0).format("DD/MM/YYYY HH:mm:ss");
      dataTrang1.INFO.ICD_KHOADT = dataSubmit.ICD_KHOADT;
      dataTrang1.INFO.ICD_KHOADT_TEN = dataSubmit.ICD_KHOADT_TEN;

      // Thêm
      dataTrang1.INFO.NOIGIOITHIEU = dataSubmit.NOIGIOITHIEU;
      dataTrang1.INFO.VAOVIENLANTHU = dataSubmit.VAOVIENLANTHU;
      dataTrang1.INFO.TAIBIEN_BIENCHUNG = dataSubmit.TAIBIEN_BIENCHUNG;
      dataTrang1.INFO.THUTHUAT_PHAUTHUAT  = dataSubmit.NN_TAIBIEN_BIENCHUNG;
      dataTrang1.INFO.NN_TAIBIEN_BIENCHUNG  = dataSubmit.NN_TAIBIEN_BIENCHUNG;
      dataTrang1.INFO.SONGAY_SAUPHAUTHUAT = dataSubmit.SONGAY_SAUPHAUTHUAT;
      dataTrang1.INFO.SOLAN_PHAUTHUAT = dataSubmit.SOLAN_PHAUTHUAT;
      dataTrang1.INFO.ICD_NGUYENNHAN = dataSubmit.ICD_NGUYENNHAN;
      dataTrang1.INFO.TENICD_NGUYENNHAN = dataSubmit.TENICD_NGUYENNHAN;
      dataTrang1.INFO.ICD_TRUOC_PHAUTHUAT = dataSubmit.ICD_TRUOC_PHAUTHUAT;
      dataTrang1.INFO.TENICD_TRUOC_PHAUTHUAT = dataSubmit.TENICD_TRUOC_PHAUTHUAT;
      dataTrang1.INFO.ICD_SAU_PHAUTHUAT = dataSubmit.ICD_SAU_PHAUTHUAT;
      dataTrang1.INFO.TENICD_SAU_PHAUTHUAT = dataSubmit.TENICD_SAU_PHAUTHUAT;
      dataTrang1.INFO.GIAIPHAUBENH = dataSubmit.GIAIPHAUBENH;
      dataTrang1.INFO.NN_TUVONG = dataSubmit.NN_TUVONG;
      dataTrang1.INFO.KHOANGTG_TUVONG = dataSubmit.KHOANGTG_TUVONG;
      dataTrang1.INFO.KHAMNGHIEM = dataSubmit.KHAMNGHIEM == true ? 1 : 0;
      dataTrang1.INFO.ICD_GIAIPHAU = dataSubmit.ICD_GIAIPHAU;
      dataTrang1.INFO.TEN_ICD_GIAIPHAU = "";
      luuThongTinVBATrang1();

      dataOld.KHAMNGHIEM ? dataOld.KHAMNGHIEM = "Có" : dataOld.KHAMNGHIEM = "Không"
      dataOld.GIAIPHAUBENH == 1 ? dataOld.GIAIPHAUBENH = "Lành tính" : (dataOld.GIAIPHAUBENH == 2 ? dataOld.GIAIPHAUBENH = "Nghi ngờ" : (dataOld.GIAIPHAUBENH == 3 ? dataOld.GIAIPHAUBENH = "Ác tính" : ""))
      dataOld.NN_TUVONG == 1 ? dataOld.NN_TUVONG = "Do bệnh" : (dataOld.NN_TUVONG == 2 ? dataOld.NN_TUVONG = "Do tai biến điều trị" : (dataOld.NN_TUVONG == 3 ? dataOld.NN_TUVONG = "Khác" : ""))
      dataOld.KHOANGTG_TUVONG == 1 ? dataOld.KHOANGTG_TUVONG = "Trong 24 giờ vào viện" : (dataOld.KHOANGTG_TUVONG == 2 ? dataOld.KHOANGTG_TUVONG = "Trong 48 giờ vào viện" : (dataOld.KHOANGTG_TUVONG == 3 ? dataOld.KHOANGTG_TUVONG = "Trong 72 giờ vào viện" : ""))
      dataOld.TAIBIEN_BIENCHUNG == 1 ? dataOld.TAIBIEN_BIENCHUNG = "Tai biến" : (dataOld.TAIBIEN_BIENCHUNG == 2 ? dataOld.TAIBIEN_BIENCHUNG = "Biến chứng" : "")
      dataOld.THUTHUAT_PHAUTHUAT == 1 ? dataOld.THUTHUAT_PHAUTHUAT = "Thủ thuật" : (dataOld.THUTHUAT_PHAUTHUAT == 2 ? dataOld.THUTHUAT_PHAUTHUAT = "Phẫu thuật" : "")
      dataSubmit.KHAMNGHIEM ? dataSubmit.KHAMNGHIEM = "Có" : dataSubmit.KHAMNGHIEM = "Không"
      dataSubmit.GIAIPHAUBENH == 1 ? dataSubmit.GIAIPHAUBENH = "Lành tính" : (dataSubmit.GIAIPHAUBENH == 2 ? dataSubmit.GIAIPHAUBENH = "Nghi ngờ" : (dataSubmit.GIAIPHAUBENH == 3 ? dataSubmit.GIAIPHAUBENH = "Ác tính" : ""))
      dataSubmit.NN_TUVONG == 1 ? dataSubmit.NN_TUVONG = "Khác" : (dataSubmit.NN_TUVONG == 2 ? dataSubmit.NN_TUVONG = "Do tai biến điều trị" : (dataSubmit.NN_TUVONG == 3 ? dataSubmit.NN_TUVONG = "Khác" : ""))
      dataSubmit.KHOANGTG_TUVONG == 1 ? dataSubmit.KHOANGTG_TUVONG = "Trong 24 giờ vào viện" : (dataSubmit.KHOANGTG_TUVONG == 2 ? dataSubmit.KHOANGTG_TUVONG = "Trong 48 giờ vào viện" : (dataSubmit.KHOANGTG_TUVONG == 3 ? dataSubmit.KHOANGTG_TUVONG = "Trong 72 giờ vào viện" : ""))
      dataSubmit.TAIBIEN_BIENCHUNG == 1 ? dataSubmit.TAIBIEN_BIENCHUNG = "Tai biến" : (dataSubmit.TAIBIEN_BIENCHUNG == 2 ? dataSubmit.TAIBIEN_BIENCHUNG = "Biến chứng" : "")
      dataSubmit.THUTHUAT_PHAUTHUAT == 1 ? dataSubmit.THUTHUAT_PHAUTHUAT = "Thủ thuật" : (dataSubmit.THUTHUAT_PHAUTHUAT == 2 ? dataSubmit.THUTHUAT_PHAUTHUAT = "Phẫu thuật" : "")
      dataSubmit.CHUYENKHOATHOIGIAN0 = dataSubmit.CHUYENKHOATHOIGIAN0? moment(dataSubmit.CHUYENKHOATHOIGIAN0).format("DD/MM/YYYY HH:mm"): "";
      callBackLog && callBackLog(dataSubmit);

      reloadFormVBAPage1(0, 0, idButton);
    },
    saveThongtinHC: function(element) {
      var idButton = element.id;
      showSelfLoading(idButton);
      var dataSubmit = convertDataFormToJson("formHsbatthcqlnb");
      updateQuanlynbvaChandoan(dataSubmit, function() {
        hideSelfLoading(idButton);
        notifiToClient("Green", "Lưu thành công")
      }, function() {
        hideSelfLoading(idButton);
        notifiToClient("Red", "Lỗi lưu thông tin")
      });
    },
    loadThongtinPage1: function() {
      var idWrap = "hsba_vba_trang1-tab";
      showLoaderIntoWrapId(idWrap)
      getThongtinPage1Benhan(thongtinhsba.thongtinbn.VOBENHAN[0].ID, function(response) {

        hideLoaderIntoWrapId(idWrap)
      }, function() {
        hideLoaderIntoWrapId(idWrap)
        notifiToClient("Red", "Lỗi load thông tin")
      });
    },
    copyChidinhCLS: function(cls) {
      form.submission = {
        data: {
          ...form.submission.data,
          CLS: cls
        }
      }
    },
    getInfoMauHSBA: function() {
      this.extendFunctionMau();
      return {
        keyMauHSBA: keyMauHSBAPHUKHOA,
        insertMau: "insertMauHSBAPHUKHOA",
        editMau: "editMauHSBAPHUKHOA",
        selectMau: "selectMauHSBAPHUKHOA",
        getdataMau: "getdataMauHSBAPHUKHOA",
        formioValidate: "formioHSBAPHUKHOAValidate",
      };
    },
    extendFunctionMau: function() {
      var self = this
      $.extend({
        insertMauHSBAPHUKHOA: function () {
          self.generateFormMauHSBA({})
        },
        editMauHSBAPHUKHOA: function (rowSelect) {
          var json = JSON.parse(rowSelect.NOIDUNG);
          var dataMau = {}
          json.forEach(function(item) {
            dataMau[item.key] = item.value
          })

          self.generateFormMauHSBA({
            ID: rowSelect.ID,
            TENMAU: rowSelect.TENMAU,
            ...dataMau
          })
        },
        selectMauHSBAPHUKHOA: function (rowSelect) {
          var json = JSON.parse(rowSelect.NOIDUNG);
          var dataMau = {
            ...form.submission.data,
          }
          json.forEach(function(item) {
            dataMau[item.key] = item.value
          })
          form.submission = {
            data: {
              ...dataMau
            }
          }
          $("#modalMauChungJSON").modal("hide");
        },
        getdataMauHSBAPHUKHOA: function () {
          var objectNoidung = [];
          self.getObjectMauHSBA().forEach(function(item) {
            if(item.key != 'ID' && item.key != 'TENMAU') {
              objectNoidung.push({
                "label": item.label,
                "value": formioMauHSBA.submission.data[item.key],
                "key": item.key,
              })
            }
          })
          return {
            ID: formioMauHSBA.submission.data.ID,
            TENMAU: formioMauHSBA.submission.data.TENMAU,
            NOIDUNG: JSON.stringify(objectNoidung),
            KEYMAUCHUNG: keyMauHSBAPHUKHOA
          };
        },
        formioHSBAPHUKHOAValidate: function() {
          formioMauHSBA.emit("checkValidity");
          if (!formioMauHSBA.checkValidity(null, false, null, true)) {
            return false;
          }
          return true;
        }
      })
    },
    generateFormMauHSBA: function(dataForm) {
      var self = this;
      var jsonForm = getJSONObjectForm(self.getObjectMauHSBA());
      Formio.createForm(document.getElementById('formChiTietMauChungJSON'),
          jsonForm,{}
      ).then(function(form) {
        formioMauHSBA = form;
        formioMauHSBA.submission = {
          data: {
            ...dataForm
          }
        }
      });
    },
    getObjectMauHSBA: function() {
      return getObjectMauHSBAPHUKHOAPAGE2();
    },

    getInfoMauHSBATongket: function() {
      this.extendFunctionMauTongket();
      return {
        keyMauHSBA: keyMauHSBAPHUKHOATongket,
        insertMau: "insertMauHSBAPHUKHOATongket",
        editMau: "editMauHSBAPHUKHOATongket",
        selectMau: "selectMauHSBAPHUKHOATongket",
        getdataMau: "getdataMauHSBAPHUKHOATongket",
        formioValidate: "formioHSBAPHUKHOATongketValidate",
      };
    },
    extendFunctionMauTongket: function() {
      var self = this
      $.extend({
        insertMauHSBAPHUKHOATongket: function () {
          self.generateFormMauHSBATongket({})
        },
        editMauHSBAPHUKHOATongket: function (rowSelect) {
          var json = JSON.parse(rowSelect.NOIDUNG);
          var dataMau = {}
          json.forEach(function(item) {
            dataMau[item.key] = item.value
          })

          self.generateFormMauHSBATongket({
            ID: rowSelect.ID,
            TENMAU: rowSelect.TENMAU,
            ...dataMau
          })
        },
        selectMauHSBAPHUKHOATongket: function (rowSelect) {
          var json = JSON.parse(rowSelect.NOIDUNG);
          var dataMau = {
            ...formTongket.submission.data,
          }
          json.forEach(function(item) {
            dataMau[item.key] = item.value
          })
          formTongket.submission = {
            data: {
              ...dataMau
            }
          }
          $("#modalMauChungJSON").modal("hide");
        },
        getdataMauHSBAPHUKHOATongket: function () {
          var objectNoidung = [];
          self.getObjectMauHSBATongket().forEach(function(item) {
            if(item.key != 'ID' && item.key != 'TENMAU') {
              objectNoidung.push({
                "label": item.label,
                "value": formioMauHSBATongket.submission.data[item.key],
                "key": item.key,
              })
            }
          })
          return {
            ID: formioMauHSBATongket.submission.data.ID,
            TENMAU: formioMauHSBATongket.submission.data.TENMAU,
            NOIDUNG: JSON.stringify(objectNoidung),
            KEYMAUCHUNG: keyMauHSBAPHUKHOATongket
          };
        },
        formioHSBAPHUKHOATongketValidate: function() {
          formioMauHSBATongket.emit("checkValidity");
          if (!formioMauHSBATongket.checkValidity(null, false, null, true)) {
            return false;
          }
          return true;
        }
      })
    },
    generateFormMauHSBATongket: function(dataForm) {
      var self = this;
      var jsonForm = getJSONObjectForm(self.getObjectMauHSBATongket());
      Formio.createForm(document.getElementById('formChiTietMauChungJSON'),
          jsonForm,{}
      ).then(function(form) {
        formioMauHSBATongket = form;
        formioMauHSBATongket.submission = {
          data: {
            ...dataForm
          }
        }
      });
    },
    getObjectMauHSBATongket: function() {
      return getObjectMauPHUKHOATongket();
    }
  }
}