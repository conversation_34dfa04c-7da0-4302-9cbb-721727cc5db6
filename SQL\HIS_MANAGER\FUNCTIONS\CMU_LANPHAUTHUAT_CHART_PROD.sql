CREATE OR REPLACE PROCEDURE "CMU_LANPHAUTHUAT_CHART_PROD" (
    p_dvtt              IN                  VARCHAR2,
    p_id_lanphauthuat   IN                  VARCHAR2,
    p_page              IN                  VARCHAR2,
    cur                 OUT                 SYS_REFCURSOR
) IS

    v_quansat          VARCHAR2(2000);
    v_treem            VARCHAR2(2000);
    v_tennhommau       VARCHAR2(2000);
    v_thongtintruyen   VARCHAR2(2000);
    v_tongcong         VARCHAR2(2000) := NULL;
    v_tongsd           VARCHAR2(2000) := NULL;
    v_thamso           NUMBER := cmu_tsdv(p_dvtt, 960603, 0);
    v_khoachidinh      VARCHAR2(2000);
    v_thamso960616     NUMBER(10) := cmu_tsdv(p_dvtt, 960616, 0);
    v_phongmo          VARCHAR2(255) := '';
BEGIN
SELECT
    quansat,
    phongmo
INTO
    v_quansat,
    v_phongmo
FROM
    cmu_lpt_gaymehoisuc
WHERE
    dvtt = p_dvtt
  AND id_lanphauthuat = p_id_lanphauthuat;

SELECT
    pban.ten_phongban
INTO v_khoachidinh
FROM
    cmu_lanphauthuat     lpt
        JOIN his_fw.dm_phongban   pban ON lpt.khoa = pban.ma_phongban
WHERE
    lpt.dvtt = p_dvtt
  AND lpt."ID" = p_id_lanphauthuat;

SELECT
    LISTAGG(TO_CHAR(gmhs.thoi_gian_block, 'HH24:MI')
                || ' - 01 bé '
                ||
            CASE
                WHEN treem.gioi_tinh = 1 THEN
                    'Trai'
                ELSE
                    'Gái'
                END
                || '; Cân nặng: '
                || treem.cannang
                || ' gram'
                || '; VĐ: '
                || treem.vongdau
                || ' cm'
                || '; Apgar 1: '
                || treem.apgar1
                || '; Apgar 5: '
                || treem.apgar5
                ||
            CASE
                WHEN treem.apgar10 IS NOT NULL THEN
                    '; Apgar 10: ' || treem.apgar10
                ELSE
                    ''
                END, '<br/>') WITHIN GROUP(
            ORDER BY
                gmhs.thoi_gian_block
        )
INTO v_treem
FROM
    cmu_lpt_gaymehoisuc_ct   gmhs
    JOIN cmu_lanphauthuat_treem   treem ON gmhs.dvtt = treem.dvtt
    AND gmhs.id_gmhs_ct = treem.id_ghms_ct
    AND gmhs.id_lanphauthuat = gmhs.id_lanphauthuat
WHERE
    gmhs.dvtt = p_dvtt
  AND gmhs.id_lanphauthuat = p_id_lanphauthuat;

OPEN cur FOR SELECT
                    chart,
                    v_quansat        quansat,
                    v_treem          treem,
                    DECODE(v_thamso, 1, 'Khoa phẫu thuật - gây mê hồi sức', v_khoachidinh) khoachidinh,
                    v_thamso960616   anchuky,
                    v_phongmo        phongmo
                FROM
                    cmu_lanphauthuat_chart
                WHERE
                    dvtt = p_dvtt
                    AND id_lanphauthuat = p_id_lanphauthuat
                    AND page = p_page;

DELETE FROM cmu_lanphauthuat_chart
WHERE
    dvtt = p_dvtt
  AND id_lanphauthuat = p_id_lanphauthuat
  AND page = p_page;

END;