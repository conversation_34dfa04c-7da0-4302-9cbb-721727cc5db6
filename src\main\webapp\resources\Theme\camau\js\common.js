var MESSAGEAJAX = {
    "ERROR": "Lỗi dữ liệu",
    "PERMISSION": "Bạn không có quyền thực hiện chức năng này",
    "CONFIRM": "<PERSON>ạ<PERSON> có chắc thực hiện thao tác này?",
    "ADD_SUCCESS": "Thêm thành công",
    "DEL_SUCCESS": "Xóa thành công",
    "EDIT_SUCCESS": "Cập nhật thành công",
    "FAIL": "<PERSON>hao tác thất bại",
    "SUCCESS": "Thao tác thành công",
}
function uuidv4() {
    return ([1e7]+-1e3+-4e3+-8e3+-1e11).replace(/[018]/g, c =>
        (c ^ crypto.getRandomValues(new Uint8Array(1))[0] & 15 >> c / 4).toString(16)
    );
}
function notifiToClient(type, message) {
    if(type.toUpperCase() == 'GREEN' && $.toast) {
        return $.toast({
            heading: 'Thông báo',
            text: message,
            icon: 'success',
            textColor: "#fff",
            position: 'top-right',
            bgColor: "#28a745",
            stack: 10
        })
    }
    var alertInstance = $.alert({
        title: 'Thông báo!',
        type: type,
        content: message,
        onOpenBefore: function () {
            $(document).on('keydown', function (e) {
                if (e.which === 13) {
                    alertInstance.close();
                }
            });
        },
        onClose: function () {
            $(document).off('keydown');
        }
    });
}

function confirmToClient(message, callbackAgrr, callbackCancel, textAgrr, textCancel) {
    var modalConfirm = $.confirm({
        title: 'Xác nhận!',
        type: 'orange',
        content: message,
        buttons: {
            warning: {
                btnClass: 'btn-warning',
                text: textAgrr? textAgrr :"Tiếp tục" ,
                action: function(){
                    modalConfirm.close();
                    setTimeout(function () {
                        if(typeof callbackAgrr === 'function') {
                            callbackAgrr();
                        }
                    })
                }
            },
            cancel: function () {
                if(typeof callbackCancel === 'function') {
                    callbackCancel();
                }
            }
        },
        onOpenBefore: function () {
            $(document).on('keydown', function (e) {
                if (e.which === 13) {
                    modalConfirm.$$warning[0].click();
                }
            });
        },
        onClose: function () {
            $(document).off('keydown');
        }
    });
}

function confirmToClientPromise(message, textAgrr = "Tiếp tục", textCancel = "Hủy") {
    return new Promise((resolve) => {
        var modalConfirm = $.confirm({
            title: 'Xác nhận!',
            type: 'orange',
            content: message,
            buttons: {
                warning: {
                    btnClass: 'btn-warning',
                    text: textAgrr,
                    action: function () {
                        modalConfirm.close();
                        resolve(true); // Xác nhận
                    }
                },
                cancel: {
                    text: textCancel,
                    action: function () {
                        resolve(false); // Hủy bỏ
                    }
                }
            },
            onOpenBefore: function () {
                $(document).on('keydown', function (e) {
                    if (e.which === 13) { // Nhấn Enter để xác nhận
                        modalConfirm.$$warning[0].click();
                    }
                });
            },
            onClose: function () {
                $(document).off('keydown');
            }
        });
    });
}

function showSelfLoading(id) {
    $("#"+ id).prop("disabled", true)
    $("#"+id + ' .spinner').show()
    $("#"+id + ' .fa').hide()
}
function hideSelfLoading(id) {
    $("#"+ id).prop("disabled", false)
    $("#"+id + ' .spinner').hide()
    $("#"+id + ' .fa').show()
}

function hideSelfLoadingByClass(className) {
    $("."+ className).prop("disabled", false)
    $("."+className + ' .spinner').hide()
}
function clearPdfIframe(idIframe) {
    document.getElementById(idIframe).src = "";

}
function previewPdfIframe(url, idIframe) {
    var xhr = new XMLHttpRequest();

    xhr.open("GET", url, true);

    xhr.responseType = "blob";
    xhr.onload = function (e) {
        if (this.status === 200) {
            // `blob` response
            console.log(this.response);
            var file = window.URL.createObjectURL(this.response);
            document.getElementById(idIframe).src = file;


        }
    };
    xhr.send();
}

function previewPdfIframeLoading(url, idWrap, idIframe) {
    showLoaderIntoWrapId(idWrap)
    var xhr = new XMLHttpRequest();

    xhr.open("GET", url, true);

    xhr.responseType = "blob";
    xhr.onload = function (e) {
        if (xhr.readyState === xhr.DONE) {
            hideLoaderIntoWrapId(idWrap)
        }
        if (this.status === 200) {
            // `blob` response
            $("#"+idWrap).html('<iframe id="'+idIframe+'" src="" width="100%" height="100%" style="overflow: auto;"></iframe>')
            var file = window.URL.createObjectURL(this.response);
            $("#"+idWrap + " #"+idIframe).prop('src', file)
        }
    };
    xhr.send();
}

function previewPdfDefaultModal(url, idIframe) {
    $("#modalPreviewPDF").modal("show");
    previewPdfIframeLoading(url, "wrapLoadIframePreview", idIframe)
}

function previewAndSignPdfIframeLoading(object) {
    showLoaderIntoWrapId('wrapLoadIframePreviewAndSign')
    var xhr = new XMLHttpRequest();

    xhr.open("GET", object.url, true);

    xhr.responseType = "blob";
    xhr.onload = function (e) {
        if (xhr.readyState === xhr.DONE) {
            hideLoaderIntoWrapId('wrapLoadIframePreviewAndSign')
        }
        if (this.status === 200) {
            var file = window.URL.createObjectURL(this.response);
            $("#wrapLoadIframePreviewAndSign #iframePreviewAndSign").prop('src', file);
            hideSelfLoadingByClass("btn-loading")
            $("#modalPreviewAndSignPDF").modal("show");
        }
    };
    xhr.send();
}

function previewAndSignPdfDefaultModal(object, callback) {
    $("#customButtonSign").html('<button class="btn btn-primary form-control-sm line-height-1 btn-loading" type="button" id="' + object.idButton + '">\n' +
        ' <span class="spinner-border spinner-border-sm spinner" role="status" aria-hidden="true"></span>'+
        '                                <i class="fa fa-key"></i> Ký số' +
        '                            </button>');
    callback();
    previewAndSignPdfIframeLoading(object)
}

function previewScanAndSignPdfIframeLoading(object) {
    showLoaderIntoWrapId('wrapLoadIframePreviewScanAndSign')
    var xhr = new XMLHttpRequest();

    xhr.open("GET", object.url, true);

    xhr.responseType = "blob";
    xhr.onload = function (e) {
        if (xhr.readyState === xhr.DONE) {
            hideLoaderIntoWrapId('wrapLoadIframePreviewScanAndSign')
        }
        if (this.status === 200) {
            var file = window.URL.createObjectURL(this.response);
            $("#wrapLoadIframePreviewScanAndSign #iframePreviewScanAndSign").prop('src', file);
            hideSelfLoadingByClass("btn-loading")
            $("#modalPreviewScanAndSignPDF").modal("show");
        }
    };
    xhr.send();
}
function previewScanAndSignPdfDefaultModal(object, callback) {
    $("#customButtonScanAndSign").html('<button class="btn btn-primary form-control-sm line-height-1 btn-loading" type="button" id="' + object.idButton + '">\n' +
        ' <span class="spinner-border spinner-border-sm spinner" role="status" aria-hidden="true"></span>'+
        '                                <i class="fa fa-key"></i> Ký số\n' +
        '                            </button>');
    $("#fileScanInput").val("");
    $("#urlBase64FileScan").val("");
    callback();
    previewScanAndSignPdfIframeLoading(object)
}

function showHideModeButton(hideIds, showIds) {
    hideIds.forEach(function(value) {
        $("#"+value).hide();
    })
    showIds.forEach(function(value) {
        $("#"+value).show();
    })
}


function myelem(value, options) {
    var el = document.createElement("input");
    el.type = "text";
    el.value = value;
    el.onkeypress = function (e) {
        var theEvent = e || window.event;
        var key = theEvent.keyCode || theEvent.which;
        key = String.fromCharCode(key);
        var regex = /[0-9]|\./;
        if (!regex.test(key)) {
            theEvent.returnValue = false;
            if (theEvent.preventDefault)
                theEvent.preventDefault();
        }
    };
    return el;
}

function myvalue(elem, operation, value) {
    if (operation === 'get') {
        return $(elem).val();
    } else if (operation === 'set') {
        $('input', elem).val(value);
    }
}

function loadDataGridGroupBy(list, url, functionCallback = null) {
    list[0].grid.beginReq();
    list.jqGrid("clearGridData");
    $.ajax({
        url: url,
        method: "GET",
        success: function(data) {
            list.jqGrid('setGridParam', { data: data});
            list[0].grid.endReq();
            list.trigger('reloadGrid');
            if (functionCallback){
                functionCallback();
            }
        }
    })
}

function getThongtinRowSelected(idList) {
    var id = $("#"+idList).jqGrid("getGridParam", "selrow");
    var rowData = $("#"+idList).jqGrid("getRowData", id);
    return rowData;
}

function isValidTime(timeString) {
    var timeRegex = /^([01]\d|2[0-3]):[0-5]\d$/
    return timeRegex.test(timeString);
}

function isValidTimeSecond(timeString) {
    var timeRegex = /^([01]\d|2[0-3]):[0-5]\d:[0-5]\d$/
    return timeRegex.test(timeString);
}
function isValidDateTime(dateTimeString) {
    var dateTimeRegex = /^(\d{2}\/\d{2}\/\d{4}) (\d{2}:\d{2})$/;
    return dateTimeRegex.test(dateTimeString);
}

function isValidDateTimeSecond(dateTimeString) {
    var dateTimeRegex = /^(\d{2}\/\d{2}\/\d{4}) (\d{2}:\d{2}:\d{2})$/;
    return dateTimeRegex.test(dateTimeString);
}

function isValidDate(dateString) {
    var dateTimeRegex = /^\d{2}\/\d{2}\/\d{4}$/;
    return dateTimeRegex.test(dateString);
}
function addTextTitleModal(idModal, subText, objectBN) {
    var itemBN = objectBN || thongtinhsba.thongtinbn;
    $("#"+idModal).html(itemBN.TEN_BENH_NHAN + " - Số bệnh án: " + itemBN.SOBENHAN + (subText ? " - " + subText : ""));
}

function noitruTaoBangke(data, callback) {
    var url = "noitru_taobangke";
    var stt_dotdieutri = data.STT_DOTDIEUTRI;
    var arr = [singletonObject.dvtt, stt_dotdieutri, data.STT_BENHAN,
        data.SOPHIEUTHANHTOAN, data.MA_BENH_NHAN, 0];
    if (stt_dotdieutri !== "" && data.STT_BENHAN !== "") {
        $.post(url, {
            url: convertArray(arr)
        }).done(function (data) {
            if(typeof callback === "function") {
                callback(data);
            }
        }).fail(function() {
            if(typeof callback === "function") {
                callback({ERROR: 1});
            }
        });
    }
}

function getMotabenhly(icd, callback) {
    $.get("laymotabenhly?icd="+icd).done(function (data) {
        if(data == "") {
            return notifiToClient("Red", "Không tìm thấy mã bệnh ICD");
        }
        if(typeof callback === "function") {
            callback(data);
        }
    }).fail(function(xhr, status, error) {
        notifiToClient("Red", "Lỗi lấy thông tin ICD");
    })
}
function getMotabenhlyYHCT(icd, callback) {
    $.get("laymotabenhly_yhct?icd_yhct="+icd).done(function (data) {
        if(data == "") {
            return notifiToClient("Red", "Không tìm thấy mã bệnh ICD");
        }
        if(typeof callback === "function") {
            callback(data);
        }
    }).fail(function(xhr, status, error) {
        notifiToClient("Red", "Lỗi lấy thông tin ICD");
    })
}

function initSelect2IfnotIntance(idElement, list, fieldValue, fieldText, showValue, addEmpty, defaultValue, refresh) {
    var $select = $("#"+idElement);
    if(!$select.hasClass("select2-hidden-accessible") || refresh) {
        if(addEmpty) {
            $select.html("<option value=''>-------</option>")
        } else {
            $select.html("");
        }
        list.forEach(function(object) {
            var stringText = showValue? object[fieldValue] + " - " + object[fieldText] : object[fieldText];
            $select.append("<option value='"+object[fieldValue]+"\'>"+stringText+"</option>")
        });
        $select.css({"width": "100%"});
        if(refresh && $select.hasClass("select2-hidden-accessible")) {
            $select.select2("destroy");
        }
        $select.select2({ width: '100%',
            dropdownParent: $select.parents('.modal').length == 0? $select.parent():  $select.parents('.modal')
        });

        if(defaultValue) {
            $select.val(defaultValue).trigger("change");
        }
    }
    $select.on('select2:open', function (e) {
        if($('.modal:visible').length > 0) {
            var zIndex = 1040 + 10 * $('.modal:visible').length + 2;
            $('.select2-container--open').css('z-index', zIndex);
        }
    });
}
function convertDateFromString(stringDate, stringSplit){
    var split_date= stringDate.split(stringSplit);
    var date_result= split_date[1]+stringSplit+split_date[0]+stringSplit+split_date[2];
    return new Date(date_result);
}

function getDSHuyenIDTinh(idTinh, idElment, defaultVal) {
    $.get("cmu_getlist?url="+convertArray([idTinh, 'CMU_GET_MAHUYEN']))
        .done(function(data){
            $("#"+idElment).html("")
            data.forEach(function(obj) {
                $("#"+idElment).append("<option value='"+obj.MA_QUAN_HUYEN+"'>"+obj.TEN_QUAN_HUYEN+"</option>")
            })
            if(defaultVal != undefined) {
                $("#"+idElment).val(defaultVal)
            }
        })

}
function getDSXaIDHuyen(idTinh, idElment, defaultVal) {
    $.get("cmu_getlist?url="+convertArray([idTinh, 'CMU_GET_MAXA']))
        .done(function(data){
            $("#"+idElment).html("")
            data.forEach(function(obj) {
                $("#"+idElment).append("<option value='"+obj.MA_PHUONG_XA+"'>"+obj.TEN_PHUONG_XA+"</option>")
            })
            if(defaultVal != undefined) {
                $("#"+idElment).val(defaultVal)
            }
        })

}

function clearDataBNReloadList() {
    thongtinhsba.thongtinbn['XUATVIEN'] = 1;
    load_dsbenhnhan();

    $("#hsba_tabs").tabs("option", "active", 0);
    $("#hsba_tabs").tabs("option", "disabled", [1]);
}

function kiemTraTheBhytCv1666(soThe, hoTen, namSinh, chiNamSinh, idWrap, callback){
    showLoaderIntoWrapId(idWrap);
    $.ajax({
        url: "kiem_tra_thongtin_the_ws_hienthi_2018?" + "hoten_nhap=" + hoTen +
            "&sothebhyt=" + soThe + "&ngaysinh_nhap=" + namSinh + "&hienthinamsinh=" + chiNamSinh
    }).done(function (data) {
        if (data.MA_KQ_CTR == "1") {
            if(data.THONGBAO_KQ && data.THONGBAO_KQ != undefined && data.THONGBAO_KQ != ''){
                return notifiToClient("Red",data.THONGBAO_KQ);
            }
        } else {
            if (data.MA_KQ == "000") {
                callback(data);
            } else {
                notifiToClient("Red",data.THONGBAO_KQ);
            }
        }
    }).fail(function() {
        notifiToClient("Red", "Lỗi không thể tra cứu thông tin thẻ bảo hiểm");
    }).always(function () {
        hideLoaderIntoWrapId(idWrap)
    });
}

function kiemTraDoituongTheBhyt(madt, callback) {
    $.ajax({
        url: "kiemtrathebhyt?madt=" + madt
    }).done(function (data) {
        callback(data, madt)
    }).fail(function() {
        notifiToClient("Red", "Lỗi không thể tra cứu thông tin thẻ bảo hiểm");
    });
}

function layTennoidangkykcb(maDKBD, callback){
    $.ajax({
        url: "laynoidangkykcb?noidangky="+maDKBD
    }).done(function (data) {
        callback(data);
    }).fail(function() {
        notifiToClient("Red", "Lỗi không thể tra cứu nơi đăng ký ban đầu");
    });
}

function cmuKiemtrathongtuyen(tt, idButton, callback){
    var objThongBaoLoi = {
        "000": "Thẻ con giá trị sử dụng",
        "001": "Thẻ do BHXH Bộ Quốc Phòng quản lý, đề nghị kiểm tra thẻ và thông tin giấy tờ tùy thân",
        "002": "Thẻ do BHXH Bộ Công An quản lý, đề nghị kiểm tra thẻ và thông tin giấy tờ tùy thân",
        "003": "Thẻ cũ hết giá trị sử dụng nhưng đã được cấp thẻ mới",
        "004": "Thẻ cũ còn giá trị sử dụng nhưng đã được cấp thẻ mới",
        "010": "Thẻ hết giá trị sử dụng",
        "051": "Mã thẻ không đúng",
        "052": "Mã tỉnh cấp thẻ(kí tự thứ 4,5 của mã thẻ) không đúng",
        "053": "Mã quyền lợi thẻ(kí tự thứ 3 của mã thẻ) không đúng",
        "050": "Không thấy thông tin thẻ bhyt",
        "060": "Thẻ sai họ tên",
        "061": "Thẻ sai họ tên(đúng kí tự đầu)",
        "070": "Thẻ sai ngày sinh",
        "100": "Lỗi khi lấy dữ liệu sổ thẻ",
        "101": "Lỗi server",
        "110": "Thẻ đã thu hồi",
        "120": "Thẻ đã báo giảm",
        "121": "Thẻ đã báo giảm. Giảm chuyển ngoại tỉnh",
        "122": "Thẻ đã báo giảm. Giảm chuyển nội tỉnh",
        "123": "Thẻ đã báo giảm. Thu hồi do tăng lại cùng đơn vị",
        "124": "Thẻ đã báo giảm. Ngừng tham gia",
        "130": "Trẻ em không xuất trình thẻ",
        "205": "Lỗi sai định dạng tham số truyền vào",
        "401": "Lỗi xác thực tài khoản"
    }
    $("#TTLoi").html(' ');
    $("#TTLoi_cmu").html(' ');
    $("#TTThongBaoTT").html(' ');
    $.get(
        singletonObject.urlCheckBHXH+
        $.param({
            makcbbd : tt.makcbbd,
            dvtt : singletonObject.dvtt,
            magioitinh : tt.magioitinh,
            ngaysinh_nhap : tt.ngaysinh_nhap,
            hoten_nhap : tt.hoten_nhap,
            sothebhyt : tt.sothebhyt,
            ngaybatdau : tt.ngaybatdau,
            ngayketthuc : tt.ngayketthuc,
            manoidoituong : tt.manoidoituong //Mã KV
        })
    ).done(function (datacheck) {
        //Lỗi tự tính
        var TTLoi = "";
        //Lỗi từ cổng BHXH
        $("#TTThongBaoTT_cmu").html(datacheck.maThe + "; " + datacheck.ghiChu);
        if(["000"].indexOf(datacheck.maKetQua)  > -1 ){//, "002", "003"
            if ((!!tt.ngaybatdau && !!tt.ngayketthuc) && (tt.ngaybatdau != datacheck.gtTheTu || tt.ngayketthuc != datacheck.gtTheDen)){
                TTLoi += "Hạn thẻ không đúng. ";
            }
            if(datacheck.hoTen.toLowerCase() != tt.hoten_nhap.toLowerCase()){
                TTLoi += "Họ tên không đúng. ";
            }
            if (!!tt.makcbbd && tt.makcbbd != datacheck.maDKBD) {
                TTLoi += "Nơi ĐKBD không đúng. ";
            }
            if (!!tt.manoidoituong && datacheck.maKV != tt.manoidoituong) {
                TTLoi +=  "Mã khu vực không đúng. ";
            }
            if (datacheck.gioiTinh != (tt.magioitinh == 1 ? "Nam" : "Nữ")) {
                TTLoi +=  "Giới tính không đúng. ";
            }
            if (typeof tt.diachi != 'undefined' && tt.diachi != datacheck.diaChi) {
                TTLoi +=  "Địa chỉ không đúng. ";
            }
            if (typeof tt.ngayDu5Nam != 'undefined' && tt.ngayDu5Nam != datacheck.ngayDu5Nam) {
                TTLoi +=  "Ngày BH 5 năm không đúng. ";
            }

            $("#TTThongBaoTT_cmu").css('color', 'green');
        }else{
            console.log("datacheck.maKetQua", datacheck.maKetQua, objThongBaoLoi[datacheck.maKetQua])
            if(objThongBaoLoi[datacheck.maKetQua] != undefined){
                TTLoi += objThongBaoLoi[datacheck.maKetQua] ;
            }else{
                TTLoi += "Lỗi khác!.";
            }

            $("#TTThongBaoTT_cmu").css('color', 'red');
        }
        if(!!TTLoi){
            $("#TTLoi_cmu").html("Kết quả: " + TTLoi);
        }
        $("#modalFormkiemtrabhyt").modal("show");
        $("#TTLichSuTT_cmu").find("tr:gt(0)").remove();
        callback(datacheck)
        var mydata = datacheck == null ? null :datacheck.dsLichSuKCB2018;
        if(mydata != null){
            for(var i=0 ; i < Math.min(mydata.length, 5) ; i++)
            {

                var data = mydata[i];
                var ngayVao = data.ngayVao.toString().substr(6,2) + '/' + data.ngayVao.toString().substr(4,2) + '/' + data.ngayVao.toString().substr(0,4)
                var ngayRa = data.ngayRa.toString().substr(6,2) + '/' + data.ngayRa.toString().substr(4,2) + '/' + data.ngayRa.toString().substr(0,4)
                var kqDieuTri_Text =  "";
                switch (parseInt(data.kqDieuTri)) {
                    case 1: kqDieuTri_Text = "Khỏi"; break;
                    case 2: kqDieuTri_Text = "Đỡ"; break;
                    case 3: kqDieuTri_Text = "Không đổi"; break;
                    case 4: kqDieuTri_Text = "Nặng hơn"; break;
                    case 5: kqDieuTri_Text = "Tử vong"; break;
                    default: kqDieuTri_Text = "Khác";
                };

                var tinhtrangRV_Text =  "";
                switch (parseInt(data.tinhTrang)) {
                    case 1: tinhtrangRV_Text = "Ra viện"; break;
                    case 2: tinhtrangRV_Text = "Chuyển viện"; break;
                    case 3: tinhtrangRV_Text = "Trốn viện"; break;
                    case 4: tinhtrangRV_Text = "Xin ra viện"; break;
                    default: tinhtrangRV_Text = "Khác";
                };
                $("#TTLichSuTT_cmu").append("<tr>"+
                    "<td align='center'>" + (i + 1) + "</td>" +
                    "<td align='center'>" + data.maCSKCB  + "</td>" +
                    "<td align='center'>" + ngayVao + "</td>" +
                    "<td align='center'>" + ngayRa + "</td>"  +
                    "<td>" + data.tenBenh + "</td>"  +
                    "<td align='center'>" + kqDieuTri_Text + "</td>"  +
                    "<td align='center'>" + tinhtrangRV_Text + "</td>"  +
                    "</tr>");
            }
        }
    }).fail(function () {
        notifiToClient("Red", "Đã có lỗi trong quá trình kiểm tra cổng giám định BHXH! Xin vui lòng thử lại sau.")
    }).always(function () {
        hideSelfLoading(idButton)
    });
}

function xembangkenoitru(dataBN, failCallback, successCallback, bkvienphi) {
    var P_MA_BENH_NHAN = dataBN.MA_BENH_NHAN;
    var P_SOVAOVIEN = dataBN.SOVAOVIEN;
    var P_SOVAOVIEN_DT = dataBN.SOVAOVIEN_DT;
    $.post("noitru_trangthaiketthuc_svv", {
        sovaovien: dataBN.SOVAOVIEN,
        sovaovien_dt: dataBN.SOVAOVIEN_DT,
        dvtt: singletonObject.dvtt,
        stt_benhan: dataBN.STT_BENHAN,
        stt_dotdieutri: dataBN.STT_DOTDIEUTRI
    }).done(function (dt) {
        if (dt == "0" || dt == "1")
            dt = "1";
        if (dt == "6")
            dt = "3";
        var getTongtien = $.ajax({
            url: 'cmu_getlist?url=' + convertArray([singletonObject.dvtt, P_SOVAOVIEN, P_SOVAOVIEN_DT, 'CMU_TONGTIEN_BANGKE_NOITRU']),
            method: 'get',
            dataType: 'json',
            async: false
        }).responseText;
        var tongtienJSON = JSON.parse(getTongtien);
        if (tongtienJSON[0].SOTIENTHIEU > 0) {
            var dataqr = getQrCode(singletonObject.dvtt, P_MA_BENH_NHAN, P_SOVAOVIEN, P_SOVAOVIEN_DT, 'BANGKE_NOITRU', tongtienJSON[0].SOTIENTHIEU);
            getQrCodeBank(singletonObject.dvtt, P_MA_BENH_NHAN, P_SOVAOVIEN, P_SOVAOVIEN_DT, 'BANGKE_NOITRU', tongtienJSON[0].SOTIENTHIEU, 'BIDV');
            getQrCodeBank(singletonObject.dvtt, P_MA_BENH_NHAN, P_SOVAOVIEN, P_SOVAOVIEN_DT, 'BANGKE_NOITRU', tongtienJSON[0].SOTIENTHIEU, 'VIETINBANK');
        }
        var arr_bk = [singletonObject.dvtt,
            dataBN.STT_DOTDIEUTRI, dataBN.STT_BENHAN,
            dataBN.SOPHIEUTHANHTOAN, P_MA_BENH_NHAN, dt, P_SOVAOVIEN, P_SOVAOVIEN_DT];
        var url_bangke = (bkvienphi == 1? "noitru_inbangke_vienphi": 'noitru_inbangke') + "?url=" + convertArray(arr_bk);
        successCallback()
        setTimeout(function () {
            previewPdfDefaultModal(url_bangke, "tthc_ravien_bangke_preview")
        })

        var P_ID_NHAN_KHAU = 0;
        $.ajax({
            url: "check_out_byt_noi_tru?sovaovien_dt=" + P_SOVAOVIEN_DT + "&sovaovien=" + P_SOVAOVIEN + "&dvtt=" + singletonObject.dvtt,
            type: "post"
        });
        $.ajax({
            url: "lay_id_nhankhau?mabenhnhan=" + dataBN.MA_BENH_NHAN
        }).done(function (dt) {
            P_ID_NHAN_KHAU = parseInt(dt);
            $.post("ls_hssk_insert",
                {
                    mabenhnhan: P_MA_BENH_NHAN,
                    idnhankhau: P_ID_NHAN_KHAU,
                    sovaovien: P_SOVAOVIEN,
                    sovaovien_dt: P_SOVAOVIEN_DT,
                    tt_hssk: 3
                }).done(function (trang_thai) {
            });

            $.post("hssk_get_pid",
                {
                    mabenhnhan: P_MA_BENH_NHAN,
                    idnhankhau: P_ID_NHAN_KHAU,
                    sovaovien: P_SOVAOVIEN,
                    sovaovien_dt: P_SOVAOVIEN_DT,
                    tt_hssk: 3
                }).done(function (trang_thai) {

            })

        });
    }).fail(function (dt) {
        failCallback();
        notifiToClient("Red", "Lỗi xem bảng kê, vui lòng thử lại sau");
    })
}


function xembangkenoitrupvi(dataBN, failCallback, successCallback, bkvienphi) {
    var P_MA_BENH_NHAN = dataBN.MA_BENH_NHAN;
    $.post("noitru_trangthaiketthuc_svv", {
        sovaovien: dataBN.SOVAOVIEN,
        sovaovien_dt: dataBN.SOVAOVIEN_DT,
        dvtt: singletonObject.dvtt,
        stt_benhan: dataBN.STT_BENHAN,
        stt_dotdieutri: dataBN.STT_DOTDIEUTRI
    }).done(function (dt) {
        if (dt == "0" || dt == "1")
            dt = "1";
        if (dt == "6")
            dt = "3";
        var arr_bk = [singletonObject.dvtt,
            dataBN.STT_DOTDIEUTRI, dataBN.STT_BENHAN,
            dataBN.SOPHIEUTHANHTOAN, P_MA_BENH_NHAN, dt, 1];
        var url_bangke =  "agg_noitru_inbangke_angiang_bh100?url=" + convertArray(arr_bk);
        successCallback()
        setTimeout(function () {
            previewPdfDefaultModal(url_bangke, "tthc_ravien_bangke_preview")
        })


    }).fail(function (dt) {
        failCallback();
        notifiToClient("Red", "Lỗi xem bảng kê, vui lòng thử lại sau");
    })
}

function xembangkenoitrungoaidm(dataBN, failCallback, successCallback, bkvienphi) {
    var P_MA_BENH_NHAN = dataBN.MA_BENH_NHAN;
    $.post("noitru_trangthaiketthuc_svv", {
        sovaovien: dataBN.SOVAOVIEN,
        sovaovien_dt: dataBN.SOVAOVIEN_DT,
        dvtt: singletonObject.dvtt,
        stt_benhan: dataBN.STT_BENHAN,
        stt_dotdieutri: dataBN.STT_DOTDIEUTRI
    }).done(function (dt) {
        if (dt == "0" || dt == "1")
            dt = "1";
        if (dt == "6")
            dt = "3";
        var arr_bk = [singletonObject.dvtt,
            dataBN.STT_DOTDIEUTRI, dataBN.STT_BENHAN,
            dataBN.SOPHIEUTHANHTOAN, P_MA_BENH_NHAN, dt, 0];
        var url_bangke =  "noitru_inbangke_angiang?url=" + convertArray(arr_bk);
        successCallback()
        setTimeout(function () {
            previewPdfDefaultModal(url_bangke, "tthc_ravien_bangkengoaidm_preview")
        })


    }).fail(function (dt) {
        failCallback();
        notifiToClient("Red", "Lỗi xem bảng kê, vui lòng thử lại sau");
    })
}

function convertDataFormToJson(idForm) {
    var paramObj = {};
    $.each($('#'+idForm).serializeArray(), function(i, kv) {
        if(paramObj[kv.name]) {
            if(!_.isArray(paramObj[kv.name])) {
                paramObj[kv.name] = [paramObj[kv.name], kv.value];
            } else {
                paramObj[kv.name].push(kv.value);
            }
        } else {
            paramObj[kv.name] = kv.value;
        }
    });
    $.each($('#'+idForm).find('input[type=hidden]'), function(_, kv) {
        if (kv.name) {
            paramObj[kv.name] = kv.value;
        }
    });
    $('#'+idForm + ' input[type=checkbox]').each(function(i, kv) {
        if (kv.name) {
            paramObj[kv.name] = this.checked? 1:0;
        }
    });
    $('#'+idForm + ' select').each(function(i, kv) {
        if (kv.name) {
            paramObj[kv.name] = this.value;
        }
    });

    $('#'+idForm + ' textarea').each(function(i, kv) {
        if (kv.name) {
            paramObj[kv.name] = this.value;
        }
    });
    $.each($('#'+idForm).find('input[type=text]'), function(_, kv) {
        if (kv.name) {
            paramObj[kv.name] = kv.value;
        }
    });
    $.each($('#'+idForm).find('input[type=number]'), function(_, kv) {
        if (kv.name) {
            paramObj[kv.name] = kv.value;
        }
    });
    return paramObj;
}

function decodeHTMLEntities (str) {
    if(str && typeof str === 'string') {
        str = str.replace(/<script[^>]*>([\S\s]*?)<\/script>/gmi, '');
        str = str.replace(/<\/?\w(?:[^"'>]|"[^"]*"|'[^']*')*>/gmi, '');
    }
    return str;
}

function combgridTenICD(id, callback) {
    $("#" + id).combogrid({
        url: 'laydanhmucicd',
        debug: true,
        width: "615px",
        colModel: [
            {'columnName': 'ICD', 'label': 'ICD', 'width': '20', 'align': 'left'},
            {'columnName': 'MO_TA_BENH_LY', 'label': 'Mô tả bệnh lý', 'width': '80', 'align': 'left'},
            {'columnName': 'MA_BENH_LY', 'label': 'mabenhly', hidden: true}
        ],
        select: function (event, ui) {
            callback(ui.item);
            return false;
        }
    });
}

function combgridTenICDYHCT(id, callback) {
    $("#" + id).combogrid({
        url: 'laydanhmucicd_yhct',
        debug: true,
        width: "615px",
        colModel: [
            {'columnName': 'MA_YHCT', 'label': 'ICD', 'width': '20', 'align': 'left'},
            {'columnName': 'TENBENH_YHHD', 'label': 'Mô tả bệnh lý', 'width': '80', 'align': 'left'},
            {'columnName': 'MA_BENH_LY', 'label': 'mabenhly', hidden: true}
        ],
        select: function (event, ui) {
            callback(ui.item);
            return false;
        }
    });
}

function getAllRowDataJqgrid(id) {
    return $('#'+id).jqGrid('getGridParam','data');
}

function clearToolbarJqgrid(id) {
    $('#'+id)[0].clearToolbar();
}

function checkKyso769(object, callback, failCallback) {
    $.get("cmu_getlist?url="+convertArray([
        object.dvtt,
        object.soVaoVien,
        object.soVaoVienDT,
        object.kyHieuPhieu,
        object.soPhieuDichVu,
        object.userId,
        object.madv? object.madv : '-1',
        'CMU_SMART769_CHECK']))
        .done(function(data){
            callback(data);
        }).fail(function(data){
        failCallback(data);
    })
}

function layThongTinBuongGiuong(sttBenhAn, sttDotDieuTri, callback){
    var result = {};
    var params = {
        sttBenhAn: sttBenhAn,
        sttDotDieuTri: sttDotDieuTri
    }
    $.get('lay-thong-tin-giuong-benh?' + $.param(params))
        .done(function(data) {
            if (data) {
                result.so_buong = data.SO_BUONG;
                result.so_giuong = data.SO_GIUONG;
            }
            callback(result);
        });
}

function getIdElmentFormio(form, key) {
    var element = form.getComponent(key);
    return element.id+"-"+key;
}
function getBacsiByKhoaFormio(makhoa, elementBacsi) {
    var url = "laybacsi_theokhoa?khoa=" + makhoa;
    $.ajax({
        url: url
    }).done(function (data) {
        var items = data.map(function (item) {
            return {
                value: item.MA_NHANVIEN,
                label: item.MA_NHANVIEN + " - "+ item.TEN_NHANVIEN
            }
        })
        elementBacsi.component.data.values = items
        elementBacsi.setItems(items)
        elementBacsi.redraw()
    }).fail(function() {
        notifiToClient("Red", "Lỗi lấy danh sách bác sĩ theo khoa")
    })
}
function getAllNhanvienByKhoaFormio(makhoa, elementBacsi) {
    var url = "laybacsi_theokhoa_all?khoa=" + makhoa;
    $.ajax({
        url: url
    }).done(function (data) {
        var items = data.map(function (item) {
            return {
                value: item.MA_NHANVIEN,
                label: item.MA_NHANVIEN + " - "+ item.TEN_NHANVIEN
            }
        })
        elementBacsi.component.data.values = items
        elementBacsi.setItems(items)
        elementBacsi.redraw()
    }).fail(function() {
        notifiToClient("Red", "Lỗi lấy danh sách bác sĩ theo khoa")
    })
}
function getNhanVienByKhoaFormio(makhoa, elementBacsi) {
    $.get("cmu_list_CMU_DSNHANVIEN?url="+convertArray([makhoa])).done(function(data){
        var items = data.map(function (item) {
            return {
                value: item.MANHANVIEN,
                label: item.MANHANVIEN + " - "+ item.TENNHANVIEN
            }
        })
        elementBacsi.component.data.values = items
        elementBacsi.setItems(items)
        elementBacsi.redraw()
    }).fail(function() {
        notifiToClient("Red", "Lỗi lấy danh sách nhân viên theo khoa")
    })
}
function getObjectThoigianBacsilambenhanFormio() {
    var optionsKhoa = singletonObject.danhsachphongban.filter(function(object) {
        return object.MAKHOA != 0
    }).map(function(object) {
        return {
            label: object.MAKHOA + " - "+ object.TENKHOA,
            value: object.MAKHOA
        }
    })
    return {
        "collapsible": true,
        "key": "p-thoigianbslamba",
        "type": "panel",
        "label": "THỜI GIAN BÁC SĨ LÀM BỆNH ÁN",
        "title": "THỜI GIAN BÁC SĨ LÀM BỆNH ÁN",
        "collapsed": false,
        "customClass": "hsba-tabs-wrap",
        "components": [
            {
                "label": "Columns",
                "columns": [
                    {
                        "components": [
                            {
                                "label": "Ngày làm bệnh án",
                                "key": "NGAYBSLAMBENHAN",
                                "type": "datetime",
                                format: "dd/MM/yyyy",
                                enableTime: false,
                                customClass: "pr-2",
                                minDate: moment(thongtinhsba.thongtinbn.NGAY_VAO_VIEN, ['DD/MM/YYYY']).format("YYYY-MM-DD"),
                                "validate": {
                                    "required": true
                                },
                            }
                        ],
                        "size": "md",
                        "width": 3
                    },
                    {
                        "components": [
                            {
                                "label": "Khoa",
                                "key": "MAKHOA",
                                "type": "select",
                                options: optionsKhoa,
                                customClass: "pr-2",
                                others: {
                                    defaultValue: singletonObject.makhoa,
                                },
                                "validate": {
                                    "required": true
                                }
                            }
                        ],
                        "width": 4,
                        "size": "md"
                    },
                    {
                        "components": [
                            {
                                "label": "Bác sĩ",
                                "key": "MABACSILAMBENHAN",
                                "type": "select",
                                options: [],
                                "validate": {
                                    "required": true
                                }

                            }
                        ],
                        "size": "md",
                        "width": 5
                    },
                ],
                "customClass": "ml-0 mr-0",
                "key": "columns-thoigianbslamba",
                "type": "columns",
            }
        ]
    }
}

function getObjectThoigianTongketFormio(dataValiate) {
    var optionsKhoa = singletonObject.danhsachphongban.filter(function(object) {
        return object.MAKHOA != 0
    }).map(function(object) {
        return {
            label: object.MAKHOA + " - "+ object.TENKHOA,
            value: object.MAKHOA,
            selected: object.MAKHOA == singletonObject.makhoa
        }
    })
    var optionsNhanvien = singletonObject.danhsachnhanvien.map(function(object) {
        return {
            label: object.MA_NHANVIEN + " - "+ object.TEN_NHANVIEN + "(" + object.TEN_PHONGBAN + ")",
            value: object.MA_NHANVIEN,
        }
    })
    // var ObjectValidate = {
    //     soToXQuang: true,
    //     soToCTScanner: true,
    //     soToSieuAm: true,
    //     soToXetNghiem: true,
    //     soToKhac: true,
    //     MABACSIDIEUTRI: true,
    //     ...(dataValiate? dataValiate: {})
    // }
    return {
        "collapsible": true,
        "key": "p-tongsohosophimanh",
        "type": "panel",
        "label": "TỒNG HỒ SƠ, PHIM, ẢNH",
        "title": "TỒNG HỒ SƠ, PHIM, ẢNH",
        "collapsed": false,
        "customClass": "hsba-tabs-wrap",
        components: [
            {
                "label": "HSPHIMANH",
                "columns": [
                    // {
                    //     "components": [
                    //         {
                    //             "label": "X-Quang",
                    //             "customClass": "pr-2",
                    //             "validate": {
                    //                 "min": 0,
                    //                 max: 1000,
                    //                 required: ObjectValidate.soToXQuang
                    //             },
                    //             "key": "soToXQuang",
                    //             "type": "number",
                    //             others: {
                    //                 "labelPosition": "left-left",
                    //                 "labelWidth": 31
                    //             }
                    //         },
                    //         {
                    //             "label": "CT Scanner",
                    //             "customClass": "pr-2",
                    //             "validate": {
                    //                 "min": 0,
                    //                 max: 1000,
                    //                 required: ObjectValidate.soToCTScanner
                    //             },
                    //             "key": "soToCTScanner",
                    //             "type": "number",
                    //             others: {
                    //                 "labelPosition": "left-left",
                    //                 "labelWidth": 31
                    //             }
                    //         },
                    //         {
                    //             "label": "Siêu âm",
                    //             "customClass": "pr-2",
                    //             "validate": {
                    //                 "min": 0,
                    //                 max: 1000,
                    //                 required: ObjectValidate.soToSieuAm
                    //             },
                    //             "key": "soToSieuAm",
                    //             "type": "number",
                    //             others: {
                    //                 "labelPosition": "left-left",
                    //                 "labelWidth": 31
                    //             }
                    //         },
                    //         {
                    //             "label": "Xét nghiệm",
                    //             "customClass": "pr-2",
                    //             "validate": {
                    //                 "min": 0,
                    //                 max: 10000,
                    //                 required: ObjectValidate.soToXetNghiem
                    //             },
                    //             "key": "soToXetNghiem",
                    //             "type": "number",
                    //             others: {
                    //                 "labelPosition": "left-left",
                    //                 "labelWidth": 31
                    //             }
                    //         },
                    //         {
                    //             "label": "GTKHAC-HSPHIMANH",
                    //             "columns": [
                    //                 {
                    //                     "components": [
                    //                         {
                    //                             "tag": "label",
                    //                             "content": "Khác",
                    //                             "refreshOnChange": false,
                    //                             "key": "htmllabel_tongketkhac",
                    //                             "type": "htmlelement",
                    //                         },
                    //                     ],
                    //                     "width": 4,
                    //                     "size": "md",
                    //                 },
                    //                 {
                    //                     components: [
                    //                         {
                    //                             "label": "",
                    //                             "type": "textfield",
                    //                             "key": "loaiGiayToKhac",
                    //                             customClass: "pr-2",
                    //                         }
                    //                     ],
                    //                     "width": 4,
                    //                     "size": "md",
                    //                 },
                    //                 {
                    //                     components: [
                    //                         {
                    //                             "label": "",
                    //                             "validate": {
                    //                                 "min": 0,
                    //                                 max: 1000,
                    //                                 required: ObjectValidate.soToKhac
                    //                             },
                    //                             "key": "soToKhac",
                    //                             "type": "number",
                    //                             customClass: "pr-2",
                    //                         },
                    //                     ],
                    //                     "width": 4,
                    //                     "size": "md",
                    //                 }
                    //             ],
                    //             "customClass": "mr-0 ml-0",
                    //             "key": "GTKHAC-HSPHIMANH",
                    //             "type": "columns",
                    //         },
                    //         {
                    //             "label": "Toàn bộ hồ sơ",
                    //             "validate": {
                    //                 "min": 0,
                    //                 max: 1000,
                    //             },
                    //             "key": "toanBoHoSo",
                    //             "type": "number",
                    //             customClass: "pr-2",
                    //             others: {
                    //                 "labelPosition": "left-left",
                    //                 "labelWidth": 31
                    //             }
                    //         }
                    //     ],
                    //     "width": 5,
                    //     "size": "md",
                    // },
                    {
                        "components": [
                            {
                                "label": "Người giao hồ sơ",
                                "key": "MANHANVIEN_GIAOHOSO",
                                "type": "select",
                                options: singletonObject.danhsachtatcanhanvien,
                                others: {
                                    "labelPosition": "left-left",
                                    "labelWidth": 35,
                                    "hidden": true,
                                }
                            },
                            {
                                "label": "Người nhận hồ sơ",
                                "key": "MANHANVIEN_NHANHOSO",
                                "type": "select",
                                options: singletonObject.danhsachtatcanhanvien,
                                others: {
                                    "labelPosition": "left-left",
                                    "labelWidth": 35,
                                    "hidden": true,
                                }
                            },
                            {
                                "label": "Ngày giờ kết thúc điều trị",
                                "key": "NGAY_TONGKET",
                                "type": "datetime",
                                format: "dd/MM/yyyy HH:mm",
                                enableTime: true,
                                minDate: moment(thongtinhsba.thongtinbn.NGAY_VAO_VIEN, ['DD/MM/YYYY']).format("YYYY-MM-DD"),
                                others: {
                                    "labelPosition": "left-left",
                                    "labelWidth": 35
                                },
                                validate: {
                                    required: true,
                                }
                            },
                            {
                                "label": "Khoa",
                                "key": "MAKHOA_KETHUC",
                                "type": "select",
                                options: optionsKhoa,
                                others: {
                                    "labelPosition": "left-left",
                                    "labelWidth": 35,
                                    defaultValue: singletonObject.makhoa
                                },
                                validate: {
                                    required: true,
                                }
                            },
                            {
                                "label": "Bác sĩ",
                                "key": "MABACSIDIEUTRI",
                                "type": "select",
                                options: [],
                                others: {
                                    "labelPosition": "left-left",
                                    "labelWidth": 35,
                                    defaultValue: singletonObject.userId,
                                },
                                validate: {
                                    required: true,
                                }
                            }
                        ],
                        "width": 7,
                        "size": "md",
                    },
                ],
                "customClass": "mr-0 ml-0",
                "key": "HSPHIMANH",
                "type": "columns",
            },
        ]
    }
}

function getTextSelectedFormio(component) {
    var selectedValue = component.getValue();
    var selectedOption;
    if(selectedValue.indexOf(' - ') !== '-1')  {
        selectedOption = component.component.data.values.find(option => option.value == selectedValue.split(" - ")[0]);
    } else {
        selectedOption = component.component.data.values.find(option => option.value == selectedValue);
    }

    return selectedOption ? selectedOption.label : '';
}

function showOrHideByClass(idWrap, classHide, classShow){
    $("#"+idWrap+" ."+classHide).hide()
    $("#"+idWrap+" ."+classShow).show()
}

function convertTextDateToDateString(strDate = null){
    if (strDate) {
        let date = strDate.toLowerCase().match(/(\d{2}) tháng (\d{2}) năm (\d{4})/);
        let month = date[2] + 1;
        return `${date[1]}/${month}/${date[3]}`;
    }
    return '';
}

function loadKhoThuocVatTucommon(data, id_element) {
    $("#" + id_element).html("")
    data.forEach(function(data) {
        $("#" + id_element).append("<option value="+data.MAKHO+">"+data.TENKHO+"</option>")
    })
}

function loadKhothuocmacdinhcommon(id_nghiepvu, id_khothuoc) {
    if(localStorage.getItem($("#" + id_nghiepvu).val())) {
        $("#" + id_khothuoc).val(localStorage.getItem($("#" + id_nghiepvu).val()));
        $("#" + id_khothuoc).trigger("change");
    }
}

function changeSourceComboGridcommon(makho, maloai, id_tenthuongmai, id_tengoc) {
    var url = "layvattu_theoloai?makhovt=" + makho + "&loaivattu="+maloai+"&nhomvattu=";
    if(id_tenthuongmai){
        $("#" + id_tenthuongmai).combogrid("option", "url", url);
    }
    var url_tg = "layvattu_theotengoc_theoloai?makhovt=" + makho + "&loaivattu="+maloai;
    if(id_tengoc){
        $("#" + id_tengoc).combogrid("option", "url", url_tg);
    }
}

function loadTatcaNVTheokhoaDieutri(id, makhoa, optionAll, mabs) {
    var url = "cmu_getlist?url=" + convertArray([makhoa, 'CMU_DSNHANVIEN']);
    $.ajax({
        url: url,
        type: "GET"
    }).done(function (data) {
        if (data) {
            $("#"+ id).empty();
            if(optionAll) {
                $("<option value='-1'>Tất cả</option>").appendTo("#"+id);
            }
            $.each(data, function (i) {
                if (data[i].MANHANVIEN != 0) {
                    if (i == 0)
                        $("#id"+ id).val(data[i].MANHANVIEN);
                    $("<option value='" + data[i].MANHANVIEN + "'>" + data[i].TENNHANVIEN + "</option>").appendTo("#"+id);
                }
            });
        }
        $("#" + id).select2({ width: '100%' });
        if(mabs != ""){
            $("#" + id).val(mabs).trigger("change");
        }
        $("#" + id).on('select2:open', function (e) {
            if($('.modal:visible').length > 0) {
                var zIndex = 1040 + 10 * $('.modal:visible').length + 2;
                $('.select2-container--open').css('z-index', zIndex);
            }

        });
    });
}
function splitMultiSelectAddToObjectFormio(object, key, loop) {
    if (object[key].length > 0) {
        var arr = object[key].sort(function(a, b) {
            return a - b;
        });
        if (!loop) loop = arr.length;
        for(var i = 0; i < loop; i++) {
            object[key + (i + 1)] = object[key][i] ? object[key][i] : null;
        }
    }
}

function mergeMultiSelectAddToObjectFormio(object, key, loop) {
    object[key] = [];
    for(var i = 0; i < loop; i++) {
        if (object[key + (i + 1)]) {
            object[key].push(object[key + (i + 1)]);
        }
    }
}

function downloadPDF(url) {
    return new Promise(function (resolve, reject) {
        var xhr = new XMLHttpRequest();
        xhr.open('GET', url, true);
        xhr.responseType = 'blob';
        xhr.onload = function () {
            if (xhr.status === 200 && xhr.response.type === 'application/pdf') {
                resolve(xhr.response);
            } else {
                reject(new Error('Failed to download PDF'));
            }
        };
        xhr.onerror = function () {
            reject(new Error('Failed to download PDF'));
        };
        xhr.send();
    });
}

async function loadAndCombinePDFs(arrUrl) {
    const pdfBlobs = [];
    const dsPhieu = [];
    const Alert = "Lỗi load phiếu ";
    for (var i = 0; i < arrUrl.length; i++) {
        var check = 0;
        for (var j = 0; j < arrUrl[i].url.length; j++) {
            try {
                var pdfBytes = await downloadPDF(arrUrl[i].url[j]);
                pdfBlobs.push(pdfBytes);
                check = 1;
                break;
            } catch (error) {
            }
        }
        if (check == 0) {
            dsPhieu.push(arrUrl[i].name)
        }
    }
    const { PDFDocument } = PDFLib;
    const mergedPdf = await PDFDocument.create();
    for (const pdfBlob of pdfBlobs) {
        console.log(pdfBlob)
        const arrayBuffer = await pdfBlob.arrayBuffer();
        const pdf = await PDFDocument.load(arrayBuffer);
        const copiedPages = await mergedPdf.copyPages(pdf, pdf.getPageIndices());
        copiedPages.forEach(page => mergedPdf.addPage(page));
    }
    const mergedPdfArray = await mergedPdf.save();
    const mergedPdfBlob = new Blob([mergedPdfArray], { type: "application/pdf" });
    const file = window.URL.createObjectURL(mergedPdfBlob);
    if (dsPhieu.length > 0) {
        notifiToClient("Red", Alert + dsPhieu.join(", "));
    }
    return file;
}

function getColorByNghiepvu(nghiepvu) {
    var color_text;
    switch (nghiepvu) {
        case "noitru_toathuoc":
        case "ba_ngoaitru_toathuoc":
        case "ngoaitru_toathuoc":
            color_text = '#2c387e';
            break;
        case "noitru_toavattu":
        case "ba_ngoaitru_toavattu":
        case "ngoaitru_toavattu":
            color_text = '#00695f';
            break;
        case "noitru_toadichvu":
        case "ba_ngoaitru_toadichvu":
        case "ngoaitru_toadichvu":
            color_text = '#b28704';
            break;
        case "noitru_toaquaybanthuocbv":
        case "ba_ngoaitru_toaquaybanthuocbv":
        case "ngoaitru_toaquaybanthuocbv":
            color_text = '#482880';
            break;
        case "noitru_toadongy":
        case "ba_ngoaitru_toadongy":
        case "ngoaitru_toadongy":
            color_text = '#618833';
            break;
        case "noitru_toamienphi":
        case "ba_ngoaitru_toamienphi":
        case "ngoaitru_toamienphi":
            color_text = '#b23c17';
            break;
        case "noitru_toamuangoai":
        case "ba_ngoaitru_toamuangoai":
        case "ngoaitru_toamuangoai":
            color_text = '#9500ae';
            break;
        default:
            color_text = 'black';
            break;
    }
    console.log("color_text",nghiepvu, color_text)
    return color_text;
}

function veGridCanvas(idCanvas, dataConfig) {
    var canvas = document.getElementById(idCanvas);
    var ctx = canvas.getContext('2d');
    var canvasWidth = canvas.width;
    var canvasHeight = canvas.height;
    ctx.clearRect(0, 0, canvasWidth, canvasHeight)
    var margin = dataConfig.margin;
    var Hmargin = dataConfig.Hmargin;
    var chartWidth = canvasWidth - 2 * margin;
    var chartHeight = canvasHeight - 2 * Hmargin;
    var barWidth = chartWidth / dataConfig.column;
    ctx.lineWidth = 1; // Line chart width

    // Set the dot color
    ctx.strokeStyle = '#000000';
    ctx.fillStyle = '#000000'; // Dot color (red in this example)

    // Begin drawing the line chart
    ctx.beginPath();
    // Draw the grid lines
    for (let i = 0; i <= dataConfig.row; i++) {
        var yPos = Hmargin + i * (chartHeight / dataConfig.row);
        ctx.beginPath();
        ctx.moveTo(margin, yPos);
        ctx.lineTo(canvasWidth - margin, yPos);
        ctx.stroke();

    }
    // Draw the y-axis
    for (let i = 0; i <= dataConfig.column; i++) {
        var xPos = margin + i * barWidth;
        ctx.beginPath();
        ctx.moveTo(xPos, Hmargin);
        ctx.lineTo(xPos, canvasHeight - Hmargin);
        if(dataConfig.dashY) {
            ctx.setLineDash(dataConfig.dashY);
        }

        ctx.stroke();

    }
}

function getBacsiAllKhoaFormio(makhoakey, mabacsikey) {
    var optionsKhoa = singletonObject.danhsachphongban.filter(function(object) {
        return object.MAKHOA != 0
    }).map(function(object) {
        return {
            label: object.MAKHOA + " - "+ object.TENKHOA,
            value: object.MAKHOA
        }
    })
    return {
        "label": "Columns",
        "columns": [
            {
                "components": [
                    {
                        "label": "Khoa",
                        "key": makhoakey,
                        "type": "select",
                        options: optionsKhoa,
                        customClass: "pr-2",
                        others: {
                            defaultValue: singletonObject.makhoa,
                        },
                    }
                ],
                "width": 6,
                "size": "md"
            },
            {
                "components": [
                    {
                        "label": "Bác sĩ",
                        "key": mabacsikey,
                        "type": "select",
                        options: [],
                    }
                ],
                "size": "md",
                "width": 6
            },
        ],
        "customClass": "ml-0 mr-0",
        "key": "columns-thoigianbslamba1",
        "type": "columns",
    }
}

function showLoadingFinger(options) {
    var remainingSeconds = options.timeout ? Math.floor(options.timeout / 1000) : 20;
    $('body').append('' +
        '<div id="smartca-loading" class="smartca-loading-wrapper">' +
        '<div class="smartca-loading-loader"></div>' +
        '<div class="' + (options.type === "finger" ? "finger-loading-icon" : options.type === "vnptca" ? "vnptca-loading-icon" : "smartca-loading-icon") + '"></div>' +
        '<div class="smartca-loading-text">' + options.text + ' - Còn lại ' + remainingSeconds + ' giây</div>' +
        '</div>');
    if (options.closeOnClick) {
        $("#smartca-loading").click(function () {
            hideLoadingFinger({idWrap: options.idWrap});
        });
    }
    $('body').css('overflow', 'hidden');
    var interval = setInterval(function () {
        remainingSeconds--;
        $('.smartca-loading-text').text(options.text + " - Còn lại " + remainingSeconds + " giây");
        if (remainingSeconds <= 0) {
            clearInterval(interval);
            hideLoadingFinger();
        }
    }, 1000);
}

function hideLoadingFinger() {
    $('body').css('overflow','auto');
    $(".smartca-loading-wrapper").css("cursor", "auto");
    $("#smartca-loading").fadeOut(500, function() {
        $("#smartca-loading").remove();
    });
}

function previewAndSignPdfNguoiBenhIframeLoading(object) {
    showLoaderIntoWrapId('wrapLoadIframePreviewAndSignNguoiBenh')
    var xhr = new XMLHttpRequest();

    xhr.open("GET", object.url, true);

    xhr.responseType = "blob";
    xhr.onload = function (e) {
        if (xhr.readyState === xhr.DONE) {
            hideLoaderIntoWrapId('wrapLoadIframePreviewAndSignNguoiBenh')
        }
        if (this.status === 200) {
            var file = window.URL.createObjectURL(this.response);
            $("#wrapLoadIframePreviewAndSignNguoiBenh #iframePreviewAndSignNguoiBenh").prop('src', file);
            $("#modalPreviewAndSignPDFNguoiBenh").modal("show");
        }
    };
    xhr.send();
}

function previewAndSignPdfNguoiBenhDefaultModal(object, callback) {
    var stringHTML = '';
    $("#nguoiBenhKyTenSelect").change(function() {
        if($("#nguoiBenhKyTenSelect").val() == "0") {
            $("#nguoiBenhKyTenInput").hide();
        } else {
            $("#nguoiBenhKyTenInput").show();
        }
    });
    $("#nguoiBenhKyTenSelect").val("0").change();
    $("#nguoiBenhKyTenInput").val("");
    if (object.idButtonVanTay) {
        stringHTML += '<button class="btn btn-primary form-control-sm line-height-1" type="button" id="' + object.idButtonVanTay + '">\n' +
            '                                <i class="fa fa-fingerprint"></i> Ký vân tay\n' +
            '                            </button>';
    }
    $("#customButtonSignNguoiBenh").html(stringHTML);
    callback();
    previewAndSignPdfNguoiBenhIframeLoading(object)
}

function luuNguoiBenhKyTen(data) {
    if($("#nguoiBenhKyTenSelect").val() == "1") {
        $.post('cmu_post_cmu_luu_nguoithan', {
            url: [
                data.dvtt,
                data.maBenhNhan,
                $("#nguoiBenhKyTenInput").val(),
            ].join('```')
        });
    }
}

function timNguoiBenhKyTen(data) {
    $("#nguoiBenhKyTenInput").hasOwnProperty("autocomplete") && $("#nguoiBenhKyTenInput").autocomplete("destroy");
    var res = $.ajax({
        url:"cmu_list_CMU_GET_DS_NGUOITHAN?url=" + convertArray([data.dvtt, data.maBenhNhan]),
        type:"GET",
        async: false
    }).responseText;
    var dataNT = JSON.parse(res);
    $("#nguoiBenhKyTenInput").autocomplete({
        source: dataNT.map(function(item) {
            return item.VALUE;
        })
    });
}

function getTenKhoaLog(dsKhoa, ID){
    for (let i = 0; i < dsKhoa.length; i++) {
        if (dsKhoa[i].MAKHOA == parseInt(ID)) {
            return dsKhoa[i].TENKHOA;
        }
    }
}

function getTenBacSiLog(dsBacSi, ID){
    for (let i = 0; i < dsBacSi.length; i++) {
        if (dsBacSi[i].MA_NHANVIEN == parseInt(ID)) {
            return dsBacSi[i].TEN_NHANVIEN;
        }
    }
}

function getOptionValue(listP, valueP){
    for (let i = 0; i < listP.length; i++) {
        if (listP[i].value == valueP) {
            return listP[i].text;
        }
    }
}

function getXMLHSBA(data, callbackSuccess, callbackFail) {
    $.ajax({
        type: "POST",
        contentType: "application/json",
        dataType: "json",
        url: "get-xml-hsba",
        data: JSON.stringify(data),
        success: function (response) {
            callbackSuccess(response);
        },
        error: function (jqXHR, exception) {
            callbackFail();
        }
    });
}

function findComponentDataByKey(formIO) {
    var arrComponentSubmit = findAllFinalComponents(formIO.components);
    if (arrComponentSubmit.length > 0) {
        var returnArr = [];
        for (let item of arrComponentSubmit) {
            if (item.hasOwnProperty('key')) {
                var label = formIO.getComponent(item.key).label.trim(":")
                var value = getValueOfFormIO(formIO.getComponent(item.key));
                returnArr.push(label + ": " + value);
            }
        }
        return returnArr;
    }
}

function findAllFinalComponents(dataArray) {
    let finalComponents = [];
    function findComponentData(obj) {
        if (obj.hasOwnProperty('components')) {
            for (let item of obj.components) {
                findComponentData(item);
            }
        } else {
            finalComponents.push(obj.component);
        }
    }
    for (let obj of dataArray) {
        findComponentData(obj);
    }
    return finalComponents;
}

function addSuggestListToInput(id, list) {
    $("#"+id).attr("list", id+"_sg");
    var option = "";
    for (var i = 0; i < list.length; i++) {
        option += "<option>"+list[i]+"</option>";
    }
    $("#"+id).parent().append(
        "<datalist id='"+id+"_sg'>" +option + "</datalist>"
    )
}

function sendMessageTopicFirebase(topic, title, message) {
    $.post("cmu-send-firebase-message?topic="+topic+"&title="+title+"&message="+message)
}

function getThongTinNhanVienByMaNhanVien(maNV) {
    return singletonObject.danhsachnhanvien.find(function(item) {
        return item.MA_NHANVIEN == maNV;
    });
}

function autoCompleteTags( id, source, callback) {
    $("#"+id).on( "keydown", function( event ) {
        if ( event.keyCode === $.ui.keyCode.TAB &&
            $( this ).autocomplete( "instance" ).menu.active ) {
            event.preventDefault();
        }
    }).autocomplete({
        minLength: 0,
        source: function( request, response ) {
            response( $.ui.autocomplete.filter(
                source, extractLastAutoComplete( request.term ) ) );
        },
        focus: function() {
            return false;
        },
        select: function( event, ui ) {
            var terms = splitValuAutoComplete( this.value );
            if (terms.length > 0) {
                if (terms.includes(ui.item.value)) {
                    callback(ui.item, this.value)
                    notifiToClient("Red", "Đã có dữ liệu cần chọn trong danh sách");
                    return false;
                }
            }
            terms.pop();
            terms.push( ui.item.value );
            this.value = terms.join( "," );
            if(callback) {
                callback(ui.item, this.value)
            }
            return false;
        },
    }).focus(function () {
        $(this).autocomplete('search', "")
    });
}

function splitValuAutoComplete( val ) {
    return val.split( /,\s*/ );
}
function extractLastAutoComplete( term ) {
    return splitValuAutoComplete( term ).pop();
}

function getElementSpace () {
    return {
        "tag": "p",
        "customClass": "w-100 tag-p-space",
        "type": "htmlelement",
    }
}

function kiemtraBNControngkhoa(callbackSuccess, callbackFail) {
    var url = "noitru_kt_controngkhoa";
    $.post(url, {
        stt_benhan: thongtinhsba.thongtinbn.STT_BENHAN,
        dvtt: singletonObject.dvtt,
        maphongban: singletonObject.makhoa,
        stt_logkhoaphong: thongtinhsba.thongtinbn.STT_LOGKHOAPHONG
    }).done(function (dt) {
        callbackSuccess(dt);

    }).fail(function (dt) {
        callbackFail()
    });
}

function getSttToDieuTriWithEmptyYLenhHoacDienBien(item) {
    const resThongTinDieuTri = $.ajax({
        url: "cmu_getlist?url=" + convertArray([
            item.STT_BENHAN,
            0,
            -1,
            singletonObject.dvtt,
            "HSBA_TODIEUTRI_CMU_SEL"
        ]),
        async: false
    }).responseText;

    const resThongTinDieuTriJson = JSON.parse(resThongTinDieuTri);
    if (resThongTinDieuTriJson && resThongTinDieuTriJson.length > 0) {
        const sttDieuTriStr = resThongTinDieuTriJson.map(tt =>  {
            if (tt.PHAUTHUAT != 0) {
                return null;
            }
            const parser = new DOMParser();
            const doc = parser.parseFromString(tt.Y_LENH, 'text/html');
            const yLenh = doc.body.textContent;

            const dienBienBenh = tt.DIEN_BIEN_BENH;

            if (yLenh.trim() === 'Y lệnh:' || dienBienBenh.trim() === '') {
                return tt.STT_DIEUTRI;
            }
            return null;
        })
            .filter(tt => tt != null)
            .join(",");

        return sttDieuTriStr;
    }
    return '';
}

function convertDataObjectToDataFormio(obj) {
    for (var key in obj) {
        if (typeof obj[key] === 'number') {
            obj[key] = obj[key].toString();
        }

        if (typeof obj[key] === 'string') {
            var dateOnlyDate = moment(obj[key], ['DD/MM/YYYY'], true);
            var dateDateTimeHM = moment(obj[key], ['DD/MM/YYYY HH:mm'], true);
            var dateDateTimeHMS = moment(obj[key], ['DD/MM/YYYY HH:mm:ss'], true);
            if (dateOnlyDate.isValid()) {
                obj[key + "_FORMIO_ONLYDATE"] = dateOnlyDate.format();
            }
            if (dateDateTimeHM.isValid()) {
                obj[key + "_FORMIO_DATETIMEHM"] = dateDateTimeHM.format();
            }
            if (dateDateTimeHMS.isValid()) {
                obj[key + "_FORMIO_DATETIMEHMS"] = dateDateTimeHMS.format();
            }
        }
    }
    return obj;
}

function convertDataFormioToDataObject(obj) {
    for (var key in obj) {
        if (typeof obj[key] === 'string') {
            if (key.endsWith("_FORMIO_ONLYDATE")) {
                var date = moment(obj[key]);
                if (date.isValid()) {
                    obj[key.replace("_FORMIO_ONLYDATE", "")] = date.format("DD/MM/YYYY");
                }
            }
            if (key.endsWith("_FORMIO_DATETIMEHM")) {
                var date = moment(obj[key]);
                if (date.isValid()) {
                    obj[key.replace("_FORMIO_DATETIMEHM", "")] = date.format("DD/MM/YYYY HH:mm");
                }
            }
            if (key.endsWith("_FORMIO_DATETIMEHMS")) {
                var date = moment(obj[key]);
                if (date.isValid()) {
                    obj[key.replace("_FORMIO_DATETIMEHMS", "")] = date.format("DD/MM/YYYY HH:mm:ss");
                }
            }
        }
    }
    return obj;
}

function getHuyenByTinhFormio(idTinh, idHuyen, element, callback) {
    $.get("cmu_list_DS_HUYEN_TT30?url="+convertArray([idTinh ? idTinh : ""])).done(function(data){
        var items = data.map(function (item) {
            return {
                value: item.MA_HUYEN,
                label: item.TEN_HUYEN
            }
        })
        element.component.data.values = items
        element.setItems(items)
        element.redraw()
        if (idHuyen) {
            var selectedValue = element.component.data.values.find(function (item) {
                return item.value == idHuyen;
            });
            if (selectedValue) {
                element.setValue(selectedValue.value);
            } else {
                element.setValue("");
            }
        } else {
            element.setValue("");
        }
        callback();
    }).fail(function() {
        notifiToClient("Red", "Lỗi lấy danh sách huyện")
    })
}

function getXaByHuyenFormio(idHuyen, idXa, element) {
    $.get("cmu_list_DS_XA_TT30?url="+convertArray([idHuyen ? idHuyen : ""])).done(function(data){
        var items = data.map(function (item) {
            return {
                value: item.MA_XA,
                label: item.TEN_XA
            }
        })
        element.component.data.values = items
        element.setItems(items)
        element.redraw()
        if (idXa) {
            var selectedValue = element.component.data.values.find(function (item) {
                return item.value == idXa;
            });
            if (selectedValue) {
                element.setValue(selectedValue.value);
            } else {
                element.setValue("");
            }
        } else {
            element.setValue("");
        }
    }).fail(function() {
        notifiToClient("Red", "Lỗi lấy danh sách huyện")
    })
}

function actionICDBenhChinh(icd, tenICD, form, callback) {
    var tenBenhchinhElement = form.getComponent(tenICD);
    var icdBenhchinhElement = form.getComponent(icd);
    $("#"+getIdElmentFormio(form, icd)).on('keypress', function(event) {
        var mabenhICD = $(this).val();
        if(event.keyCode == 13 && mabenhICD != "") {
            mabenhICD = mabenhICD.trim().toUpperCase();
            getMotabenhly(mabenhICD, function(data) {
                var splitIcd = data.split("!!!")
                tenBenhchinhElement.setValue(splitIcd[1]);
                icdBenhchinhElement.setValue(mabenhICD);
                if (typeof callback == "function" && callback) callback(splitIcd);
            })
        }
    })
}

function actionICDBenhChinhYHCT(icd, tenICD, form, callback) {
    var tenBenhchinhElement = form.getComponent(tenICD);
    var icdBenhchinhElement = form.getComponent(icd);
    $("#"+getIdElmentFormio(form, icd)).on('keypress', function(event) {
        var mabenhICD = $(this).val();
        if(event.keyCode == 13 && mabenhICD != "") {
            mabenhICD = mabenhICD.trim().toUpperCase();
            getMotabenhlyYHCT(mabenhICD, function(data) {
                var splitIcd = data.split("!!!")
                tenBenhchinhElement.setValue(splitIcd[0]);
                icdBenhchinhElement.setValue(mabenhICD);
                if (typeof callback == "function" && callback) callback(splitIcd);
            })
        }
    })
}

function actionICDBenhKemTheo(icd, tenICD, fullICD, form) {
    $("#" + getIdElmentFormio(form, icd)).on('keypress', function (event) {
        var tenBenhphuElement = form.getComponent(tenICD);
        var icdBenhphuElement = form.getComponent(icd);
        var textBenhphuElement = form.getComponent(fullICD);
        var mabenhICD = $(this).val();
        if (event.keyCode == 13 && mabenhICD != "") {
            mabenhICD = mabenhICD.toUpperCase();
            getMotabenhly(mabenhICD, function (data) {
                var splitIcd = data.split("!!!")
                tenBenhphuElement.setValue(splitIcd[1]);
                tenBenhphuElement.focus()
                icdBenhphuElement.setValue(mabenhICD)
            })
        }
    })
    $("#" + getIdElmentFormio(form, tenICD)).on('keypress', function (event) {
        if (event.keyCode == 13) {
            var stringIcd = textBenhphuElement.getValue();
            var mabenhICD = icdBenhphuElement.getValue()
            if (!stringIcd.includes(mabenhICD)) {
                textBenhphuElement.setValue(stringIcd + "; (" + mabenhICD.toUpperCase() + ") " + tenBenhphuElement.getValue());
            }
            icdBenhphuElement.setValue("")
            tenBenhphuElement.setValue("")
            icdBenhphuElement.focus()
        }
    })
}

function actionGetTenDonViTheoMa(maDonViKey, tenDonViKey, form) {
    var tenDonViComp = form.getComponent(tenDonViKey);
    var maDonViComp = form.getComponent(maDonViKey);
    $("#"+getIdElmentFormio(form, maDonViKey)).on('keypress', function(event) {
        var maDonViVal = $(this).val();
        if(event.keyCode == 13 && maDonViVal != "") {
            maDonViVal = maDonViVal.trim().toUpperCase();
            $.ajax({
                url: "laytennoichuyenden?ma=" + maDonViVal
            }).done(function(data) {
                if (data.trim() != "") {
                    tenDonViComp.setValue(data);
                    maDonViComp.setValue(mabenhICDmaDonViVal)
                } else {
                    notifiToClient("Red", "Không tìm thấy đơn vị với mã: " + maDonViVal);
                    tenDonViComp.setValue("");
                    maDonViComp.setValue("");
                }
            });
        }
    })
}

async function confirmAndRun(content = "Bạn có chắc chắn muốn thực hiện thao tác này?") {
    return new Promise(function(resolve, reject) {
        $.confirm({
            title: 'Xác nhận!',
            type: 'orange',
            content: content,
            buttons: {
                warning: {
                    btnClass: 'btn-warning',
                    text: "Tiếp tục",
                    action: function() {
                        resolve(1);
                    }
                },
                cancel: function () {
                    resolve(0);
                }
            }
        });
    });
}

function genrateCodeTracuu(data, callback) {
    $.post("get-encode-magiayto?magiay="+data.magiay).done(function(data) {
        callback({data: data, error: 0});
    }).fail(function() {
        callback({error: 1})
    })
}

$(function() {
    $.validator.addMethod("validDateTime", function(value, element) {
        if(!value) return true;
        return isValidDateTime(value);
    }, "Thời gian không hợp lệ");
    $.validator.addMethod("validDateTimeSecond", function(value, element) {
        if(!value) return true;
        return isValidDateTimeSecond(value);
    }, "Thời gian không hợp lệ");
    $.validator.addMethod("validDate", function(value, element) {
        if(!value) return true;
        return isValidDate(value);
    }, "Thời gian không hợp lệ");
    $.validator.addMethod("validTime", function(value, element) {
        if(!value) return true;
        return isValidTime(value);
    }, "Thời gian không hợp lệ");
    $.validator.addMethod("validTimeSecond", function(value, element) {
        if(!value) return true;
        return isValidTimeSecond(value);
    }, "Thời gian không hợp lệ");

    $.validator.addMethod("validDateNgayhientai", function(value, element) {
        if(!value) return true;
        var tem = value.split(" ")
        var ngayhientai = cmuGetCurrentTimeServer(singletonObject.dvtt)
        return convertDateFromString(tem[0], "/") <= convertDateFromString(ngayhientai.split(" ")[0], "/");
    }, "Không được lớn hơn ngày hiện tại");

    $.validator.addMethod("validDateNgaynhapvien", function(value, element) {
        if(isValidDateTime(value)){
            return !moment(value+":00",["DD/MM/YYYY HH:mm:ss"]).isBefore(moment(thongtinhsba.thongtinbn.NGAYGIO_NHAPVIEN,["DD/MM/YYYY HH:mm:ss"]));
        }
        if(isValidDate(value)){
            return !moment(value,["DD/MM/YYYY"]).isBefore(moment(thongtinhsba.thongtinbn.NGAYGIO_NHAPVIEN.split(" ")[0],["DD/MM/YYYY"]));
        }
        if(isValidTimeSecond(value)){
            return !moment(value,["DD/MM/YYYY HH:mm:ss"]).isBefore(moment(thongtinhsba.thongtinbn.NGAYGIO_NHAPVIEN,["DD/MM/YYYY HH:mm:ss"]));
        }
        return true
    }, "Không được nhỏ hơn ngày nhập viện");

    $.validator.addMethod("phaichonkhoa", function(value, element) {
        return value > 0;
    }, "Vui lòng chọn khoa");

    $.extend($.validator.messages, {
        required: "Vui lòng nhập thông tin này.",
        maxlength: $.validator.format("Không được nhập quá {0} ký tự."),
        minlength: $.validator.format("Không được nhập ít hơn {0} ký tự."),
        range: $.validator.format("Vui lòng nhập giá trị từ {0} đến {1}."),
        max: $.validator.format("Vui lòng nhập giá trị nhỏ hơn hoặc bằng {0}."),
        min: $.validator.format("Vui lòng nhập giá trị lớn hơn hoặc bằng {0}.")
    });
    $.validator.setDefaults({
        errorClass : "is-invalid",
    })

})
