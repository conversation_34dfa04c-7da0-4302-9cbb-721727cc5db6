create or replace PROCEDURE "HIS_MANAGER"."CMU_PHIEU_DGDD_CHUNG_P" (
    p_dvtt              IN VARCHAR2,
    p_MA_PHIEU          IN NUMBER,
    p_STT_DOTDIEUTRI    IN VARCHAR2,
    p_<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>         IN VARCHAR2,
    CUR                 OUT SYS_REFCURSOR
)
IS
    v_thamso960616 number(10) := cmu_tsdv(p_dvtt, 960616, 0);
BEGIN
    OPEN CUR FOR
    SELECT
        BN.TEN_BENH_NHAN,
        CASE
            WHEN MONTHS_BETWEEN(SYSDATE, BN.NGAY_SINH) < 1 THEN
                TRUNC(SYSDATE - BN.NGAY_SINH) || ' ngày'
            WHEN EXTRACT(YEAR FROM SYSDATE) - EXTRACT(YEAR FROM BN.NGAY_SINH) = 0 THEN
                TRUNC(MONTHS_BETWEEN(SYSDATE, BN.NGAY_SINH)) || ' tháng'
            ELSE
                TO_CHAR(EXTRACT(YEAR FROM SYSDATE) - EXTRACT(YEAR FROM BN.NGAY_SINH)) || ' tuổi'
            END AS TUOI,
        BN.GIOI_TINH,
        BA.TENKHOA_NHAPVIENVAOKHOA,
        BG.STT_BUONG, BG.STT_GIUONG,
        NVL(BG.STT_BUONG, ' ') || ' / ' || NVL(BG.STT_GIUONG, ' ') AS BUONG_GIUONG,
        CK.DATA_PHIEU,
        v_thamso960616 ANCHUKY
    FROM "HIS_MANAGER"."CMU_PHIEU_DANH_GIA_DINH_DUONG" CK
    INNER JOIN HIS_PUBLIC_LIST.DM_BENH_NHAN BN
        ON BN.MA_BENH_NHAN = CK.MA_BENH_NHAN
    INNER JOIN HIS_MANAGER.NOITRU_BENHAN BA
        ON BA.DVTT = CK.DVTT
        AND BA.SOVAOVIEN = p_SOVAOVIEN
    LEFT JOIN HIS_MANAGER.CMU_SOBUONGGIUONG BG
        ON BG.DVTT = BA.DVTT
        AND BG.STT_BENHAN = BA.STT_BENHAN
        AND BG.STT_DOTDIEUTRI = p_STT_DOTDIEUTRI
    WHERE CK.MA_PHIEU = p_MA_PHIEU;
END;