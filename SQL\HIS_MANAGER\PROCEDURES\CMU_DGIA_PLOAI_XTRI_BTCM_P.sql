create or replace PROCEDURE "HIS_MANAGER"."CMU_DGIA_PLOAI_XTRI_BTCM_P" (
    p_dvtt              IN VARCHAR2,
    p_ID                IN NUMBER,
    p_STT_DOTDIEUTRI    IN VARCHAR2,
    CUR                 OUT SYS_REFCURSOR
)
IS
    v_thamso960616 number(10) := cmu_tsdv(p_dvtt, 960616, 0);
BEGIN
    OPEN CUR FOR
    SELECT
        BN.TEN_BENH_NHAN,
        CASE
            WHEN MONTHS_BETWEEN(SYSDATE, BN.NGAY_SINH) < 1 THEN
                TRUNC(SYSDATE - BN.NGAY_SINH) || ' ngày'
            WHEN EXTRACT(YEAR FROM SYSDATE) - EXTRACT(YEAR FROM BN.NGAY_SINH) = 0 THEN
                TRUNC(MONTHS_BETWEEN(SYSDATE, BN.NGAY_SINH)) || ' tháng'
            ELSE
                TO_CHAR(EXTRACT(YEAR FROM SYSDATE) - EXTRACT(YEAR FROM BN.NGAY_SINH)) || ' tuổi'
        END AS TUOI,
        BN.GIOI_TINH,
        BA.TENKHOA_NHAPVIENVAOKHOA,
        NVL(BG.STT_BUONG, ' ') || ' / ' || NVL(BG.STT_GIUONG, ' ') AS BUONG_GIUONG,
        CK.DATA_PHIEU,
        SS.KEYSIGN, NV.TEN_NHANVIEN_CD TENBS, TO_CHAR(SS.NGAY_KY, 'DD/MM/YYYY HH24:MI:SS') NGAY_KY,
        v_thamso960616 anchuky, cmu_get_chukynhanvien(SS.MA_NHANVIEN_KY) anhchuky
    FROM "HIS_MANAGER"."CMU_DGIA_PLOAI_XTRI_BTCM" CK
        INNER JOIN HIS_PUBLIC_LIST.DM_BENH_NHAN BN
            ON BN.MA_BENH_NHAN = CK.MA_BENHNHAN
        INNER JOIN HIS_MANAGER.NOITRU_BENHAN BA
            ON BA.DVTT = CK.DVTT
            AND BA.SOVAOVIEN = CK.SOVAOVIEN
        LEFT JOIN HIS_MANAGER.CMU_SOBUONGGIUONG BG
            ON BG.DVTT = BA.DVTT
            AND BG.STT_BENHAN = BA.STT_BENHAN
            AND BG.STT_DOTDIEUTRI = p_STT_DOTDIEUTRI
        LEFT JOIN HIS_MANAGER.SMARTCA_SIGNED_KCB SS
            ON CK.DVTT = SS.DVTT
            AND CK.SOVAOVIEN = SS.SOVAOVIEN
            AND SS.SO_PHIEU_DV = CK.ID
            AND SS.STATUS = 0
            AND SS.KY_HIEU_PHIEU IN ('PHIEUDGIAPLOAIXTRITCM_BACSY_KHAM')
        LEFT JOIN HIS_FW.DM_NHANVIEN_CD NV
            ON NV.MA_NHANVIEN = SS.MA_NHANVIEN_KY
    WHERE CK.ID = p_ID;
END;