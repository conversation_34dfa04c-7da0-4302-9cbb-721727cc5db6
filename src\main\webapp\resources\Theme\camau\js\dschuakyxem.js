
$(function (){

    $("#danhsachchuakyxem").click(function () {
        instanceGridDSChuaKyXem();
        reloadDSChuaKyXem();
        $("#modalDSChuaKyXem").modal("show");

    });

    $("#dschuakyxem_lammoi").click(function () {
        reloadDSChuaKyXem();
    });

    function reloadDSChuaKyXem(){
        var url = "cmu_getlist?url=" + convertArray([singletonObject.dvtt, singletonObject.makhoa,
            singletonObject.bant == ''? 0: singletonObject.bant, "NOT_TN_DSBENHNHAN_CHUAKYXEMDV"]);
        $("#list_dschuakyxem").jqGrid('setGridParam', {
            datatype: 'json',
            url: url
        }).trigger('reloadGrid')
    }


    function instanceGridDSChuaKyXem(){
        var list = $("#list_dschuakyxem");
        if(!list[0].grid) {
            list.jqGrid({
                url: "cmu_getlist?url=" + convertArray([singletonObject.dvtt, singletonObject.makhoa,
                    singletonObject.bant == ''? 0: singletonObject.bant, "NOT_TN_DSBENHNHAN_CHUAKYXEMDV"]),
                datatype: "json",
                loadonce: true,
                height: 400,
                width: null,
                shrinkToFit: false,
                colModel: [
                    {label: "Số BA",name: 'SOBENHAN', index: 'SOBENHAN',width: 100},
                    {label: "Mã BN", name: 'MA_BENH_NHAN', index: 'MA_BENH_NHAN',align: "center", fixed: true, hidden: true},
                    {label: "Họ tên", name: 'TEN_BENH_NHAN', index: 'TEN_BENH_NHAN', width: 200},
                    {label: "Tuổi", name: 'TUOI', index: 'TUOI', width: 200},
                    {label: "Phòng", name: 'TEN_PHONG', index: 'TEN_PHONG',align: "center", fixed: true, width: 80},
                    {label: "Giường", name: 'SOGIUONG', index: 'SOGIUONG',align: "center", fixed: true, width: 80},

                    {label: "Số phiếu", name: 'SO_PHIEU_DV', index: 'SO_PHIEU_DV', width: 100},
                    {label: "Ngày ký kết quả", name: 'NGAY_KETQUA', index: 'NGAY_KETQUA', width: 100},
                    {label: "Người ký kết quả", name: 'NGUOI_KY_KQ', index: 'NGUOI_KY_KQ', width: 100},
                    {label: 'SOVAOVIEN', name: 'SOVAOVIEN', index: 'SOVAOVIEN', hidden: true},
                    {label: 'STT_BENHAN', name: 'STT_BENHAN', index: 'STT_BENHAN', hidden: true},
                    {label: 'SOVAOVIEN_DT', name: 'SOVAOVIEN_DT', index: 'SOVAOVIEN_DT', hidden: true},
                    {label: 'KY_HIEU_PHIEU', name: 'KY_HIEU_PHIEU', index: 'KY_HIEU_PHIEU', hidden: true},
                    {label: 'SO_PHIEU_DV', name: 'SO_PHIEU_DV', index: 'SO_PHIEU_DV', hidden: true},
                    {label: 'MA_DICH_VU', name: 'MA_DICH_VU', index: 'MA_DICH_VU', hidden: true},
                    {label: 'KEYMINIO', name: 'KEYMINIO', index: 'KEYMINIO', hidden: true},
                    {label: 'SOPHIEU_NGOAITRU', name: 'SOPHIEU_NGOAITRU', index: 'SOPHIEU_NGOAITRU', hidden: true},
                ],
                rowNum: 100000,
                ignoreCase: true,
                caption: "Danh sách bệnh nhân",
                afterInsertRow: function(rowid, aData) {
                },
                footerrow: true,
                gridComplete: function () {
                    var grid = $("#list_dschuakyxem");
                    var sl = grid.getGridParam("records");
                    sl = sl + " phiếu kết quả";
                    grid.jqGrid("footerData", "set", {TEN_BENH_NHAN: sl});
                },
                ondblClickRow: function (id) {
                    var rowData = getThongtinRowSelected("list_dschuakyxem");
                    openModalXemvakyxem(rowData);
                },
                onRightClickRow: function (id) {
                    if(id) {
                        var rowData = getThongtinRowSelected("list_dschuakyxem");
                        $.contextMenu('destroy', '#list_dschuakyxem tr');
                        var items = {
                            "xemkq": {name: '<p><i class="fa fa-eye text-primary" aria-hidden="true"></i> Xem kết quả</p>'},
                        };

                        $.contextMenu({
                            selector: '#list_dschuakyxem tr',
                            reposition : false,
                            callback: function (key, options) {
                                var rowData = getThongtinRowSelected("list_dschuakyxem");
                                if(key == 'xemkq') {
                                    openModalXemvakyxem(rowData);
                                }
                            },
                            items: items
                        });
                    }
                },
            });

            list.jqGrid('filterToolbar', {stringResult: true, searchOnEnter: false, defaultSearch: "cn"});
        }

    }

    function openModalXemvakyxem(rowData) {
        var urlKeyMinio = '';
        var objPos = {
            keyword: "KYXEMKQ",
            y1:  0 ,
            y2: -65,
            x1: 20,
            x: 160,
        }
        if(rowData.KY_HIEU_PHIEU == 'PHIEUKQ_CDHA_RISPACS') {
            var responseText = $.ajax({
                url: "cmu_getlist?url="+convertArray([
                    singletonObject.dvtt,
                    rowData.SOVAOVIEN,
                    rowData.SOVAOVIEN_DT,
                    rowData.KY_HIEU_PHIEU,
                    rowData.SO_PHIEU_DV,
                    -1,
                    rowData.MA_DICH_VU,
                    'CMU_SMART769_GET']),
                method: "GET",
                async: false
            }).responseText;
            responseText = JSON.parse(responseText);
            if(responseText.length > 0) {
                urlKeyMinio = getCMUFileSigned769GetLinkV2(responseText[0].KEYMINIO, 'pdf');
            } else {
                urlKeyMinio = "download-result-ris-pacs?maPhieuChiDinh="+
                    (rowData.SOPHIEU_NGOAITRU? rowData.SOPHIEU_NGOAITRU: rowData.SO_PHIEU_DV)
                    +"&maDichVu="+rowData.MA_DICH_VU
            }

            objPos = {
                keyword: 'KYXEMKQ',
                x1: 20,
                x: 140
            }
        } else {
            urlKeyMinio = getCMUFileSigned769GetLinkV2(rowData.KEYMINIO, 'pdf');
            objPos = {
                keyword: "KYXEMKQ",
                y1:  0,
                y2: -65,
                x1: 20,
                x: 160,
            }
        }
        previewAndSignPdfDefaultModal({
            url: urlKeyMinio,
            idButton: 'cls_kyxem_action',
        }, function(){
            hideSelfLoading("cls_kyxem_action")
            $("#cls_kyxem_action").click(function() {
                var idButton = this.id;
                showSelfLoading(idButton)
                kySoChung({
                    dvtt: singletonObject.dvtt,
                    userId: singletonObject.userId,
                    url: urlKeyMinio,
                    loaiGiay: (rowData.KY_HIEU_PHIEU == 'PHIEUKQ_XETNGHIEM' || rowData.KY_HIEU_PHIEU == 'PHIEU_KETQUA_XETNGHIEMTAIKHOA')
                        ? "PHIEUKQ_XETNGHIEM_KYXEM": "PHIEUKQ_CDHA_KYXEM",
                    maBenhNhan: rowData.MA_BENH_NHAN,
                    soBenhAn: rowData.STT_BENHAN,
                    soPhieuDichVu: rowData.SO_PHIEU_DV,
                    maDichVu: rowData.MA_DICH_VU,
                    madv: rowData.MA_DICH_VU,
                    soVaoVien: rowData.SOVAOVIEN,
                    soVaoVienDT: rowData.SOVAOVIEN_DT,
                    ...objPos,
                    fileName: "Ký xem kết quả cls: " + rowData.TEN_BENH_NHAN + " - Số phiếu: " + rowData.SO_PHIEU_DV,
                }, function(dataKySo) {
                    hideSelfLoading(idButton)
                    $("#modalPreviewAndSignPDF").modal("hide");
                    reloadDSChuaKyXem();
                });
            });
        });
    }
    
})