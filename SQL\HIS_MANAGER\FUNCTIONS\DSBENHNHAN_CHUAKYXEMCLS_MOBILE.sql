CREATE OR REPLACE FUNCTION dsben<PERSON>han_chuakyxemcls_mobile (
    p_dvtt         IN             VARCHAR2,
    p_maphongban   IN             NUMBER,
    p_bant         IN             NUMBER
) RETURN SYS_REFCURSOR IS
    cur SYS_REFCURSOR;
    v_thamso960590   VARCHAR2(50) := cmu_tsdv(p_dvtt, 960590, '1');
BEGIN
OPEN cur FOR SELECT DISTINCT
                     a.ma_benh_nhan,
                     a.stt_benhan,
                     a.stt_dotdieutri,
                     a.sobenhan,
                     a.ten_benh_nhan,
                     a.gioi_tinh,
                     a.sovaovien,
                     a.sovaovien_dt,
                     a.tuoi,
                     a.dia_chi,
                     a.ten_phong,
                     a.sogiuong,
                     a.ky_hieu_phieu,
                     a.so_phieu_dv,
                     a.ma_dich_vu,
                     a.nguoi_ky_kq,
                     a.ngay_ketqua,
                     a.keyminio,
                     a.ngay_ky,
                     a.so<PERSON><PERSON>_ng<PERSON>,
                     a.urlgetpdf,
                     a.keyword,
                     a.x1,
                     a.x,
                     a.y1,
                     a.y2,
                     a.k<PERSON><PERSON><PERSON><PERSON><PERSON>,
                     v_thamso960590 visibletype,
                     CASE
                         WHEN a.id_dieutri IS NULL THEN
                             - 1
                         ELSE
                             a.id_dieutri
END id_dieutri
FROM
                     (
                         SELECT
                             bn.ma_benh_nhan,
                             ba.stt_benhan,
                             ba.sobenhan,
                             ddt.stt_dotdieutri,
                             bn.ten_benh_nhan,
                             bn.gioi_tinh,
                             ba.sovaovien,
                             ddt.sovaovien_dt,
                             hgi_hienthi_tuoi_benhnhan(bn.ngay_sinh, ba.ngaynhapvien, p_dvtt) AS tuoi,
                             bn.dia_chi,
                             giuong.stt_buong     ten_phong,
                             giuong.stt_giuong    sogiuong,
                             signkcb.ky_hieu_phieu,
                             signkcb.so_phieu_dv,
                             signkcb.ma_dich_vu,
                             nv.ten_nhanvien_cd   nguoi_ky_kq,
                             TO_CHAR(signkcb.ngay_ky, 'DD/MM/YYYY HH24:MI:SS') ngay_ketqua,
                             cmu_checkphieukqdakyxem(p_dvtt, ba.sovaovien, ddt.sovaovien_dt, signkcb.ky_hieu_phieu, signkcb.so_phieu_dv
                             , signkcb.ma_dich_vu) dakyxem,
                             minio.keyminio,
                             signkcb.ngay_ky,
                             cdha.so_phieu_cdha   sophieu_ngoaitru,
                             CASE
                                 WHEN signkcb.ky_hieu_phieu = 'PHIEUKQ_CDHA_RISPACS'
                                      AND minio.keyminio IS NULL THEN
                                     'kyso/get-rispacsfile-minio'
                                 ELSE
                                     'kyso/get-file-minio'
                             END urlgetpdf,
                             CASE
                                 WHEN signkcb.ky_hieu_phieu IN (
                                     'PHIEUKQ_CDHA_RISPACS'
                                 ) THEN
                                     'Lời dặn của BS chuyên khoa'
                                 ELSE
                                     'KYXEMKQ'
                             END keyword,
                             '20' x1,
                             '160' x,
                             CASE WHEN v_thamso960590 = '1' THEN '0' ELSE '-15' END y1,
                             CASE WHEN v_thamso960590 = '1' THEN '0' ELSE '-65' END y2,
                             CASE
                                 WHEN signkcb.ky_hieu_phieu IN (
                                     'PHIEUKQ_DIENTIM',
                                     'PHIEUKQ_CDHA_RISPACS'
                                 ) THEN
                                     'PHIEUKQ_CDHA_KYXEM'
                                 ELSE
                                     'PHIEUKQ_XETNGHIEM_KYXEM'
                             END kyhieukyso,
                             signkcb.id_dieutri
                         FROM
                             noitru_benhan                  ba
                             JOIN smartca_signed_kcb             signkcb ON ba.dvtt = signkcb.dvtt
                                                                AND ba.sovaovien = signkcb.sovaovien
                                                                AND signkcb.status = 0
                                                                AND signkcb.ky_hieu_phieu IN (
                                 'PHIEUKQ_XETNGHIEM',
                                 'PHIEUKQ_DIENTIM',
                                 'PHIEU_KETQUA_XETNGHIEMTAIKHOA',
                                 'PHIEUKQ_CDHA_RISPACS'
                             )
                             JOIN smartca_signed_file_minio      minio ON signkcb.dvtt = minio.dvtt
                                                                     AND signkcb.keysign = minio.keysign
                             JOIN his_fw.dm_nhanvien_cd          nv ON signkcb.ma_nhanvien_ky = nv.ma_nhanvien
                             LEFT JOIN noitru_cd_cdha                 cdha ON signkcb.dvtt = cdha.dvtt
                                                              AND cdha.sovaovien = signkcb.sovaovien
                                                              AND cdha.sovaovien_dt = signkcb.sovaovien_dt
                                                              AND cdha.so_phieu_cdha = signkcb.so_phieu_dv, noitru_dotdieutri              ddt
                             LEFT JOIN cmu_sobuonggiuong              giuong ON ddt.dvtt = giuong.dvtt
                                                                   AND ddt.stt_benhan = giuong.stt_benhan
                                                                   AND giuong.stt_dotdieutri = ddt.stt_dotdieutri,
                             noitru_logkhoaphong            log,
                             his_public_list.dm_benh_nhan   bn
                         WHERE
                             ba.bant = p_bant
                             AND ddt.dvtt = p_dvtt
                             AND ba.dvtt = p_dvtt
                             AND log.dvtt = p_dvtt
                             AND ba.trang_thai < 3
                             AND ddt.tt_dotdieutri < 3
                             AND log.trang_thai IN (
                                 0,
                                 1,
                                 2
                             )
                             AND log.maphongban = p_maphongban
                             AND log.dvtt = p_dvtt
                             AND ddt.stt_benhan = ba.stt_benhan
                             AND log.stt_benhan = ba.stt_benhan
                             AND ba.mabenhnhan = bn.ma_benh_nhan
                     ) a
                 WHERE
                     dakyxem = 0
                 ORDER BY
                     ten_benh_nhan,
                     ngay_ky DESC;

RETURN cur;
END;