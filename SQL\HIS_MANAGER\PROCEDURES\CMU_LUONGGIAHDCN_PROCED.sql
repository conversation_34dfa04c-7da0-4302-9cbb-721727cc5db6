create or replace PROCEDURE              CMU_LUONGGIAHDCN_PROCED(
    p_dvtt              IN     VARCHAR2,
    p_id               IN     NUMBER,
    p_sovaovien      IN     NUMBER,
    p_stt_benhan      IN     VARCHAR2,
    p_stt_dotdieutri      IN     VARCHAR2,
    cur OUT SYS_REFCURSOR
) IS
v_stt_buong varchar2(255):='';
v_stt_giuong varchar2(255):='';
v_thamso960616 number(10) := cmu_tsdv(p_dvtt, 960616, 0);
BEGIN
begin
select stt_buong, stt_giuong into v_stt_buong, v_stt_giuong from cmu_sobuonggiuong
where dvtt = p_dvtt and stt_benhan = p_stt_benhan and stt_dotdieutri = p_stt_dotdieutri and rownum <= 1;
exception
	when no_data_found then v_stt_buong:=null;v_stt_giuong:=null;
end;
open cur for
select
    phieu.ID,
    phieu.DVTT,
    phieu.SOVAOVIEN,
    phieu.MA_BENH_NHAN,
    phieu.VAN_DONG,
    phieu.SINH_HOAT,
    phieu.NHAN_THUC,
    phieu.CHUC_NANG_KHAC,
    phieu.THAM_GIA_HOAT_DONG,
    phieu.MOI_TRUONG,
    phieu.CA_NHAN,
--    TO_CHAR(phieu.NGAY_TAO_PHIEU, 'dd/MM/yyyy') NGAY_TAO_PHIEU,
    'Ngày ' || TO_CHAR(phieu.NGAY_TAO_PHIEU, 'DD') ||
    ' tháng ' || TO_CHAR(phieu.NGAY_TAO_PHIEU, 'MM') ||
    ' năm ' || TO_CHAR(phieu.NGAY_TAO_PHIEU, 'YYYY') NGAY_TAO_PHIEU,
    phieu.NGUOI_TAO,
    nv.TEN_NHANVIEN NGUOITHUCHIEN,
    v_stt_buong PHONG,
    v_stt_giuong GIUONG,
    v_thamso960616 ANCHUKY

FROM CMU_LUONGGIAHDCN phieu
         LEFT JOIN HIS_FW.DM_NHANVIEN nv ON phieu.NGUOI_TAO = nv.MA_NHANVIEN

WHERE phieu.ID = p_id and phieu.SOVAOVIEN = p_sovaovien
order by phieu.NGAY_TAO_PHIEU;
END;