<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="cls_xn_all" language="groovy" pageWidth="595" pageHeight="842" whenNoDataType="AllSectionsNoDetail" columnWidth="483" leftMargin="56" rightMargin="56" topMargin="20" bottomMargin="20" whenResourceMissingType="Error" uuid="0f5e3019-005e-44fe-bb8f-29ae14f10109">
	<property name="ireport.zoom" value="1.1000000000000028"/>
	<property name="ireport.x" value="0"/>
	<property name="ireport.y" value="0"/>
	<parameter name="soyte" class="java.lang.String"/>
	<parameter name="benhvien" class="java.lang.String"/>
	<parameter name="dvtt" class="java.lang.String"/>
	<parameter name="mabenhnhan" class="java.lang.String"/>
	<parameter name="magiay" class="java.lang.String">
		<defaultValueExpression><![CDATA[]]></defaultValueExpression>
	</parameter>
	<parameter name="text_date" class="java.lang.String">
		<defaultValueExpression><![CDATA[]]></defaultValueExpression>
	</parameter>
	<parameter name="text_month" class="java.lang.String">
		<defaultValueExpression><![CDATA[]]></defaultValueExpression>
	</parameter>
	<parameter name="text_year" class="java.lang.String">
		<defaultValueExpression><![CDATA[]]></defaultValueExpression>
	</parameter>
	<parameter name="sobenhan" class="java.lang.String">
		<defaultValueExpression><![CDATA[]]></defaultValueExpression>
	</parameter>
	<parameter name="gioitinh" class="java.lang.String">
		<defaultValueExpression><![CDATA[]]></defaultValueExpression>
	</parameter>
	<parameter name="tennguoibenh" class="java.lang.String">
		<defaultValueExpression><![CDATA[]]></defaultValueExpression>
	</parameter>
	<parameter name="tuoi" class="java.lang.String">
		<defaultValueExpression><![CDATA[]]></defaultValueExpression>
	</parameter>
	<parameter name="khoa" class="java.lang.String">
		<defaultValueExpression><![CDATA[]]></defaultValueExpression>
	</parameter>
	<parameter name="stt_dotdieutri" class="java.lang.String">
		<defaultValueExpression><![CDATA[]]></defaultValueExpression>
	</parameter>
	<parameter name="stt_benhan" class="java.lang.String">
		<defaultValueExpression><![CDATA[]]></defaultValueExpression>
	</parameter>
	<parameter name="sovaovien" class="java.lang.String">
		<defaultValueExpression><![CDATA[]]></defaultValueExpression>
	</parameter>
	<queryString language="plsql">
		<![CDATA[{call CMU_LUONGGIAHDCN_PROCED($P{dvtt},$P{magiay},$P{sovaovien},$P{stt_benhan},$P{stt_dotdieutri},$P{ORACLE_REF_CURSOR})}]]>
	</queryString>
	<field name="NGAY_TAO_PHIEU" class="java.lang.String"/>
	<field name="PHONG" class="java.lang.String"/>
	<field name="GIUONG" class="java.lang.String"/>
	<field name="VAN_DONG" class="java.lang.String"/>
	<field name="SINH_HOAT" class="java.lang.String"/>
	<field name="NHAN_THUC" class="java.lang.String"/>
	<field name="CHUC_NANG_KHAC" class="java.lang.String"/>
	<field name="THAM_GIA_HOAT_DONG" class="java.lang.String"/>
	<field name="MOI_TRUONG" class="java.lang.String"/>
	<field name="CA_NHAN" class="java.lang.String"/>
	<field name="NGUOITHUCHIEN" class="java.lang.String"/>
	<field name="ANCHUKY" class="java.lang.String"/>
	<title>
		<band height="213" splitType="Stretch">
			<textField>
				<reportElement x="0" y="0" width="215" height="20" uuid="bf002bb6-f816-4a93-92a0-feb2a2ee4d12"/>
				<textElement verticalAlignment="Middle" markup="none">
					<font fontName="Times New Roman" size="14"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{soyte}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="0" y="20" width="358" height="20" uuid="867d6065-44c4-4b22-a0a5-4f4a81bc84eb"/>
				<textElement verticalAlignment="Middle" markup="none">
					<font fontName="Times New Roman" size="14" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{benhvien}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="0" y="90" width="483" height="20" uuid="45e2c42f-894e-4cef-abae-aa9e9bc6c881"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="14" isBold="true"/>
				</textElement>
				<text><![CDATA[PHIẾU LƯỢNG GIÁ HOẠT ĐỘNG CHỨC NĂNG VÀ SỰ THAM GIA]]></text>
			</staticText>
			<staticText>
				<reportElement x="358" y="0" width="156" height="20" uuid="a74db1e8-89fa-478d-a7a9-ca1528a9cae9"/>
				<textElement>
					<font fontName="Times New Roman" size="14"/>
				</textElement>
				<text><![CDATA[MS:]]></text>
			</staticText>
			<textField>
				<reportElement x="358" y="20" width="156" height="20" uuid="5c4c762f-4b08-4e76-83ab-f18a60474908"/>
				<textElement verticalAlignment="Middle" markup="none">
					<font fontName="Times New Roman" size="14"/>
				</textElement>
				<textFieldExpression><![CDATA["Số vào viện: " + $P{sobenhan}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="0" y="123" width="304" height="20" uuid="9385dcbc-b425-4d99-b09e-55ad6ba93b75"/>
				<textElement verticalAlignment="Middle" markup="none">
					<font fontName="Times New Roman" size="14"/>
				</textElement>
				<textFieldExpression><![CDATA["Họ tên người bệnh: " + $P{tennguoibenh}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="304" y="123" width="65" height="20" uuid="3ff583c2-ef51-44c8-922c-bcdcda38e405"/>
				<box leftPadding="3"/>
				<textElement verticalAlignment="Middle" markup="none">
					<font fontName="Times New Roman" size="14"/>
				</textElement>
				<textFieldExpression><![CDATA["Tuổi: " + $P{tuoi}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="397" y="123" width="33" height="20" uuid="e65e88bf-d914-4c7a-ac02-561f509cd843"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="14"/>
					<paragraph leftIndent="2"/>
				</textElement>
				<text><![CDATA[Nam]]></text>
			</staticText>
			<textField>
				<reportElement x="369" y="123" width="28" height="20" uuid="6c2c9dab-1edf-4b16-adb5-17e987218644"/>
				<box>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="14"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{gioitinh}.equals( "1" ) ? "x" : ""]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="458" y="123" width="25" height="20" uuid="95ff8a89-87e9-469b-8fc2-21801331121e"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="14"/>
					<paragraph leftIndent="2"/>
				</textElement>
				<text><![CDATA[Nữ]]></text>
			</staticText>
			<textField>
				<reportElement x="430" y="123" width="28" height="20" uuid="79294430-b8f4-4df1-93fb-a96a45c94303"/>
				<box>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="14"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{gioitinh}.equals( "0" ) ? "x" : ""]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="0" y="143" width="279" height="20" uuid="66ad3b1e-c5e2-49ed-8778-5540457d8a2e"/>
				<textElement verticalAlignment="Middle" markup="none">
					<font fontName="Times New Roman" size="14"/>
				</textElement>
				<textFieldExpression><![CDATA["Khoa: " + $P{khoa}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="279" y="143" width="102" height="20" uuid="74a96b4d-099d-463f-a6ac-8fd4cabc748c"/>
				<textElement verticalAlignment="Middle" markup="none">
					<font fontName="Times New Roman" size="14"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{PHONG} == null ? "Buồng: " : "Buồng: " + $F{PHONG}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="381" y="143" width="102" height="20" uuid="55915627-fbe2-4584-9eec-eaf34bd895b1"/>
				<textElement verticalAlignment="Middle" markup="none">
					<font fontName="Times New Roman" size="14"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{GIUONG} == null ? "Giường: " : "Giường: " + $F{GIUONG}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="215" y="0" width="143" height="20" uuid="61ab74fa-332f-41d3-8027-1c186a32bedd"/>
				<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
					<font fontName="Times New Roman" size="14" isBold="false" isItalic="true"/>
				</textElement>
				<text><![CDATA[(Mẫu phiếu số 1)]]></text>
			</staticText>
			<staticText>
				<reportElement x="0" y="163" width="483" height="20" uuid="e9afb6e9-86e1-4ee6-ab47-d05f03ed65ac"/>
				<textElement verticalAlignment="Middle" markup="none">
					<font fontName="Times New Roman" size="14" isBold="true"/>
				</textElement>
				<text><![CDATA[1. Hoạt động chức năng]]></text>
			</staticText>
			<textField isStretchWithOverflow="true">
				<reportElement positionType="Float" x="0" y="183" width="483" height="20" uuid="98373946-5a1c-4105-8468-aa30dfb9145b"/>
				<box topPadding="0"/>
				<textElement verticalAlignment="Middle" markup="styled">
					<font fontName="Times New Roman" size="14" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA["<b>1.1. Vận động và di chuyển</b> (thay đổi vị trí, tư thế; di chuyển độc lập hay cần dụng cụ trợ giúp, người trợ giúp; đi lại....)"
+
($F{VAN_DONG} == null ?
"<br/>" :
"<br/>- " + $F{VAN_DONG})
+
"<br/>"
+
"<b>1.2. Chức năng sinh hoạt hàng ngày </b>(mức độ độc lập trong hoạt động: ăn uống; tắm rửa; vệ sinh cá nhân; mặc quần áo; sử dụng nhà vệ sinh; kiểm soát đại tiện - tiểu tiện...)"
+
($F{SINH_HOAT} == null ?
"<br/>" :
"<br/>- " + $F{SINH_HOAT})
+
"<br/>"
+
"<b>1.3. Nhận thức, giao tiếp</b> (định hướng; tập trung chú ý; trí nhớ; thờ ơ lãng quên; chức năng điều hành; giao tiếp, ngôn ngữ...)"
+
($F{NHAN_THUC} == null ?
"<br/>" :
"<br/>- " + $F{NHAN_THUC})
+
"<br/>"
+
"<b>1.4. Các chức năng khác</b> (rối loạn nuốt, tiết niệu, sinh dục, da, các giác quan…….)"
+
($F{CHUC_NANG_KHAC} == null ?
"<br/>" :
"<br/>- " + $F{CHUC_NANG_KHAC})
+
"<br/>"
+
"<b>2. Sự tham gia các hoạt động trong gia đình và xã hội</b> (chuẩn bị bữa ăn, công việc nội trợ, dọn dẹp nơi sinh hoạt/nhà cửa, đi chợ, mua sắm, tham gia hoạt động xã hội ….)"
+
($F{THAM_GIA_HOAT_DONG} == null ?
"<br/>" :
"<br/>- " + $F{THAM_GIA_HOAT_DONG})
+
"<br/>"
+
"<b>3. Yếu tố môi trường</b> (Đánh giá tiếp cận môi trường của trẻ khuyết tật/NB: tình trạng nơi sinh hoạt/ điều trị, nhà vệ sinh, dụng cụ PHCN đang sử dụng; sự hỗ trợ và quan tâm của những người xung quanh; thái độ và cách ứng xử của gia đình, xã hội...)"
+
($F{MOI_TRUONG} == null ?
"<br/>" :
"<br/>- " + $F{MOI_TRUONG})
+
"<br/>"
+
"<b>4. Yếu tố cá nhân</b> (tình trạng hôn nhân, trình độ học vấn, tình trạng việc làm, thể lực, tâm lý, sở thích, lối sống, thói quen, kỹ năng xử lý tình huống, tính cách,…)"
+
($F{CA_NHAN} == null ?
"<br/>" :
"<br/>- " + $F{CA_NHAN})]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="0" y="40" width="358" height="20" uuid="c5f59caf-7a99-43ad-b9fa-566cafc235f7"/>
				<textElement verticalAlignment="Middle" markup="none">
					<font fontName="Times New Roman" size="14" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{khoa}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="0" y="60" width="105" height="20" uuid="9c87d388-2c57-4d40-8963-016bfbf97d6c"/>
				<textElement verticalAlignment="Middle" markup="none">
					<font fontName="Times New Roman" size="14"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{PHONG} == null ? "Buồng: " : "Buồng: " + $F{PHONG}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="105" y="60" width="110" height="20" uuid="db5abcef-0de7-456b-b99b-79ffe9dbe8e3"/>
				<textElement verticalAlignment="Middle" markup="none">
					<font fontName="Times New Roman" size="14"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{GIUONG} == null ? "Giường: " : "Giường: " + $F{GIUONG}]]></textFieldExpression>
			</textField>
		</band>
	</title>
	<summary>
		<band height="115" splitType="Stretch">
			<textField>
				<reportElement positionType="Float" x="290" y="0" width="245" height="20" uuid="4fdb2a2f-f5f5-4b9b-ad76-3a161735696b"/>
				<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
					<font fontName="Times New Roman" size="14"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{NGAY_TAO_PHIEU}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" x="290" y="95" width="245" height="20" uuid="b1967fa6-f6a3-4fde-b2ca-1b4fe4319f91">
					<printWhenExpression><![CDATA[$F{ANCHUKY}.equals("0")]]></printWhenExpression>
				</reportElement>
				<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
					<font fontName="Times New Roman" size="14"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{NGUOITHUCHIEN}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement positionType="Float" x="290" y="20" width="245" height="35" uuid="7aacbfe5-5761-4f7c-9d93-cca4e61f7a65"/>
				<textElement textAlignment="Center" verticalAlignment="Middle" markup="styled">
					<font fontName="Times New Roman" size="14"/>
				</textElement>
				<text><![CDATA[Người thực hiện
<i>(Ký và ghi rõ họ tên)</i>]]></text>
			</staticText>
		</band>
	</summary>
</jasperReport>
