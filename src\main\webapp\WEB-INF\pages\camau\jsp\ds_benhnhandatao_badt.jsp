<%@page import="l2.ThamSoManager" %>
<%@taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>
<%@ taglib uri="http://www.springframework.org/tags/form" prefix="form"%>
<%@page contentType="text/html" pageEncoding="UTF-8"  %>
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8"/>
    <meta name="google-site-verification" content="u6uNEfD4cb3gidezi4r_6aI8Wb1E07-ufBeCQpvmlqQ" />
    <meta http-equiv="Cache-Control" content="no-store" />
    <title>Hệ thống chăm sóc sức khỏe</title>
    <link rel="icon" href="<c:url value="/resources/images/favicon.ico" />" type="image/x-icon"/>
    <link rel="shortcut icon" href="<c:url value="/resources/images/favicon.ico" />" type="image/x-icon"/>
    <link href="<c:url value="/resources/css/divheader.css" />" rel="stylesheet"/>
    <link href="<c:url value="/resources/css/style_new.css" />" rel="stylesheet"/>

    <link rel="stylesheet" href="<c:url value="/resources/css/jquery-ui-redmond.1.9.1.css" />" />
    <script src="<c:url value="/resources/js/jquery.min.1.8.3.js" />"></script>
    <script src="<c:url value="/resources/js/jquery-ui.1.9.1.js" />"></script>
    <link href="<c:url value="/resources/bootstrap-4.1.3/dist/css/bootstrap.min.css" />" rel="stylesheet"/>
    <link href="<c:url value="/resources/jqgrid/css/ui.jqgrid.css" />" rel="stylesheet"/>
    <script src="<c:url value="/resources/jqgrid/js/i18n/grid.locale-en.js" />"></script>
    <script src="<c:url value="/resources/jqgrid/js/jquery.jqGrid.src.js" />"></script>
    <script src="<c:url value="/resources/js/common_function.js" />"></script>
    <script src="<c:url value="/resources/js/jquery.inputmask.bundle.min.js" />"></script>
    <script src="<c:url value="/resources/blockUI/jquery.blockUI.js" />"></script>
    <script src="<c:url value="/resources/dialog/jquery.alerts.js" />"></script>
    <link href="<c:url value="/resources/dialog/jquery.alerts.1.css" />" rel="stylesheet"/>
    <script src="<c:url value="/resources/camau/popper.1.11.0.js" />"></script>
    <script src="<c:url value="/resources/camau/js/lodash.min.js" />"></script>
    <script src="<c:url value="/resources/camau/js/common.js?timestamp=${System.currentTimeMillis()}" />"></script>
    <link rel="stylesheet" href="<c:url value="/resources/font-awesome-4.7.0/css/font-awesome.min.css"/>">
    <script src="<c:url value="/resources/bootstrap-4.4.1-dist/js/bootstrap.min.js"/>" ></script>
    <link href="<c:url value="/resources/camau/css/khambenhnoitru.css" />" rel="stylesheet"/>
    <link href="<c:url value="/resources/camau/css/formio.full.min.css" />" rel="stylesheet"/>
    <link href="<c:url value="/resources/camau/css/loader.css" />" rel="stylesheet"/>
    <link href="<c:url value="/resources/camau/css/custom.css" />" rel="stylesheet"/>
    <link rel="stylesheet" href="<c:url value="/resources/camau/css/select2.min.css" />" />
    <script src="<c:url value="/resources/camau/js/select2.min.js" />"></script>
    <script src="<c:url value="/resources/js/datetimepicker.js" />"></script>
    <link rel="stylesheet" href="<c:url value="/resources/css/datetimepicker.css" />" />
    <script src="<c:url value="/resources/js/jquery-confirm.min.js" />"></script>
    <link href="<c:url value="/resources/css/jquery-confirm.min.css" />" rel="stylesheet"/>
    <link href="<c:url value="/resources/combogrid/css/smoothness/jquery.ui.combogrid.css" />" rel="stylesheet"/>
    <script src="<c:url value="/resources/combogrid/plugin/jquery.ui.combogrid-1.6.3.js" />"></script>
    <script src="<c:url value="/resources/contextmenu/jquery.contextMenu.js" />"></script>
    <link href="<c:url value="/resources/contextmenu/jquery.contextMenu.css" />" rel="stylesheet"/>
    <script src="<c:url value="/resources/camau/smartca769.js?timestamp=${System.currentTimeMillis()}" />"></script>
    <script src="<c:url value="/resources/camau/js/jquery.validate.min.js" />"></script>
    <script src="<c:url value="/resources/camau/js/formio.full.min.js" />"></script>
    <script src="<c:url value="/resources/camau/material/moment.js" />"></script>
    <script src="<c:url value="/resources/print-js/v1.0.43/print.min.js" />"></script>
    <script src="<c:url value="/resources/smartca/js/pdf.js" />"></script>
    <link href="<c:url value="/resources/smartca/css/smartca-loading.css" />" rel="stylesheet"/>
    <script src="<c:url value="/resources/smartca/js/smartca-loading.js?timestamp=${System.currentTimeMillis()}"/>"></script>
    <script src="<c:url value="/resources/js/jquery.md5.js?timestamp=${System.currentTimeMillis()}" />"></script>
    <link href="<c:url value="/resources/smartca/css/scanner-loading.css" />" rel="stylesheet"/>
    <script src="<c:url value="/resources/smartca/js/scanner-loading.js?timestamp=${System.currentTimeMillis()}"/>"></script>
    <script src="https://unpkg.com/pdf-lib@^1.16.0/dist/pdf-lib.min.js"></script>
    <script src="<c:url value="/resources/camau/js/jquery.toast.min.js" />"></script>
    <link href="<c:url value="/resources/camau/css/jquery.toast.min.css" />" rel="stylesheet"/>
    <link rel="stylesheet" href="<c:url value="/resources/camau/css/swiper-bundle.min.css" />" />
    <script src="<c:url value="/resources/camau/js/swiper-bundle.min.js" />"></script>

    <script>
        var urlCheckBHXH = "<%= ThamSoManager.instance(session).getThamSoString("960564","0")%>";
        var url = "cmu_ktra_thongtuyen_2024?";
        if(urlCheckBHXH == 1) {
            url = "https://apikysohis.vnptcamau.vn/kiem-tra-thong-tuyen?"
        }
        var singletonObject = {
            makhoa: "${Sess_PhongBan}",
            maphongbenh: "${Sess_Phong}",
            dvtt: "${Sess_DVTT}",
            userId: "${Sess_UserID}",
            user: "${Sess_User}",
            admin: "${Sess_Admin}",
            danhsachphongban: [],
            danhsachphongbenh: [],
            danhsachnhanvienkhoa: [],
            ngayhientai: "${ngayhientai}",
            danhsachloaittpt: [],
            danhsachkhothuocBHYT: [],
            danhsachkhovattu: [],
            danhsachkhomienphi: [],
            danhsachkhomuataiquay: [],
            danhsachkhodongy: [],
            danhsachkhodichvu: [],
            danhsachgiuongbenh: [],
            danhsachnghenghiep: [],
            danhsachdantoc: [],
            danhsachthutruong: [],
            danhsachtruongkhoa: [],
            danhsachbvchuyentuyen: [],
            danhsachxangdau: [],
            danhsachnhanvien: [],
            danhsachquanhe: [],
            danhsachnguyennhantv: [],
            danhsachnnoituvong: [],
            danhsachtinh: [],
            danhsachquyenchungtu: [],
            danhsachloaibenhan: [],
            urlCheckBHXH: url,
            thamso82816: 0,
            thamso42001: 0,
            thamso960518: 0,
            thamso960484: 0,
            hgi_tt50_pdf: "${hgi_tt50_pdf}",
            thamSo960011: "<%= ThamSoManager.instance(session).getThamSoString("960011","0")%>", // Bật chức năng tiền sử dị ứng
            thamSo960012: "<%= ThamSoManager.instance(session).getThamSoString("960012","0")%>", // Bật chức năng glasgow
            thamSo960013: "<%= ThamSoManager.instance(session).getThamSoString("960013","0")%>", // Bật chức năng Đánh giá rối loạn nuốt
            thamSo960014: "<%= ThamSoManager.instance(session).getThamSoString("960014","0")%>", // Bật chức năng Phiếu Chuyển Dạ
            thamSo960601: "<%= ThamSoManager.instance(session).getThamSoString("960601","0")%>", // Bật xem giấy ra viện trên app mobile
            thamSo960602: "<%= ThamSoManager.instance(session).getThamSoString("960602","0")%>", // Bật chức năng phiếu chuyển tuyến 1 hoặc 2 chữ ký
            thamSo960610: "<%= ThamSoManager.instance(session).getThamSoString("960610","0")%>", // Bật bảng kiểm trước phẫu thuât 96161
        }
    </script>
    <style>
        .v-menu-tab .nav-link {
             margin-top: 0px !important;
        }
    </style>
</head>
<body>
<div id="panel_all" style="background: white">
    <%@include file="../../../../resources/Theme/include_pages/menu.jsp"%>
    <div class="p-4">
        <div>
            <div class="form-row d-flex p-2">
                <div  class="col-1">
                    <label>Mã bệnh nhân</label>
                    <input type="text" class="form-control form-control-sm input-custom" id="ma_benh_nhan" placeholder="Nhập mã bệnh nhân">
                </div>
                <div  class="col-1">
                    <label>Số bệnh án</label>
                    <input type="text" class="form-control form-control-sm input-custom" id="so_benh_an" placeholder="Nhập số bệnh án">
                </div>
                <div class="col-2">
                    <label>Ngày ra (từ ngày)</label>
                    <input type="text" class="form-control form-control-sm input-only-date input-custom" id="ngay_ra_vien">
                </div>
                <div class="col-2">
                    <label>Đến ngày</label>
                    <input type="text" class="form-control form-control-sm input-only-date input-custom" id="denngay_ra_vien">
                </div>
                <div class="col-3 select2-custom">
                    <label>Khoa</label>
                    <select class=" form-control form-control-sm khoa-phong" id="khoa_phong" data-show-subtext="true" data-live-search="true">
                        <option selected value="-1">Tất cả khoa</option>
                    </select>
                </div>
                <div class="col-auto d-flex align-items-end">
                    <button class="btn btn-primary ml-1 pl-2 pr-2 form-control form-control-sm line-height-1" id="tim_kiem" type="button">
                        <i class="fa fa-search" aria-hidden="true"></i> Tìm kiếm
                    </button>
                </div>
            </div>
        </div>
        <div id="list_benhnhan_wrap" class="wrap-jqgrid">
            <table id="list_benhnhan"></table>
        </div>
    </div>
</div>
<div class="modal fade" id="modalDanhSachDotDieuTri" role="dialog"
     aria-labelledby="modalDanhSachDotDieuTri" aria-hidden="true"
     data-keyboard="false" data-backdrop="static">
    <div class="modal-dialog" style="max-width: 92%;width:92%" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h6 class="modal-title text-primary font-weight-bold" id="titleDanhSachDotDieuTri">Danh sách đợt điều trị</h6>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <div id="list_dotdieutri_wrap" class="wrap-jqgrid">
                    <table id="list_dotdieutri" data-toggle="jqgrid-fit-container"></table>
                </div>
            </div>
            <div class="modal-footer">
                <div class="col-md-5 text-right">
                    <button class="btn btn-default ml-2 form-control-sm line-height-1 close_giaycdgm" type="button"
                            id="action_closegiaycdgm" data-dismiss="modal">
                        Đóng
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>
<audio id="fingerAudio" hidden>
    <source src="resources/smartca/audio/fingerAudio.mp3" type="audio/mpeg">
</audio>
<div class="modal fade" id="modalHienThiPdf" role="dialog"
     aria-labelledby="modalHienThiPdf" aria-hidden="true"
     data-keyboard="false" data-backdrop="static">
    <div class="modal-dialog modal-dialog-centered" style="max-width: 92%; width:40%" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h6 class="modal-title text-primary font-weight-bold" id="titleHienThiPdf">Tài liệu</h6>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <div id="myModalHienThiPdf" class="modalImgClass">
                    <div class="modalImg-content">
                        <iframe id="modalIframePdf" src="" width="100%" height="400px" style="border: none;"></iframe>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <div class="col-md-12 text-right">
                    <button class="btn btn-default ml-2 form-control-sm line-height-1" type="button" data-dismiss="modal">
                        Đóng
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>
<jsp:include page="../../../../resources/Theme/include_pages/footerBT.jsp"/>
<jsp:include page="loader.jsp"/>
<jsp:include page="emr_lichsubenhan_badt.jsp"/>
<jsp:include page="emr_benhantonghop_body.jsp"/>
<jsp:include page="emr_modalkygiaohsba.jsp"/>
<jsp:include page="emr_benhansankhoa.jsp"/>
<jsp:include page="emr_themttchuyenkhoa.jsp"/>
<jsp:include page="emr_bienbankiemthaotv.jsp"/>
<jsp:include page="emr_phieuchuandoannguyennhantuvong.jsp"/>

<%--START VO BENH AN--%>
<script src="<c:url value="/resources/camau/js/benhannoi.js?timestamp=${System.currentTimeMillis()}"/>" ></script>
<script src="<c:url value="/resources/camau/js/benhanbong.js?timestamp=${System.currentTimeMillis()}"/>" ></script>
<script src="<c:url value="/resources/camau/js/benhannoitruyhct.js?timestamp=${System.currentTimeMillis()}"/>" ></script>
<script src="<c:url value="/resources/camau/js/benhannhi.js?timestamp=${System.currentTimeMillis()}"/>" ></script>
<script src="<c:url value="/resources/camau/js/benhantaimuihong.js?timestamp=${System.currentTimeMillis()}"/>" ></script>
<script src="<c:url value="/resources/camau/js/benhantaychanmieng.js?timestamp=${System.currentTimeMillis()}"/>" ></script>
<script src="<c:url value="/resources/camau/js/benhanngoaikhoa.js?timestamp=${System.currentTimeMillis()}"/>" ></script>
<script src="<c:url value="/resources/camau/js/benhandalieu.js?timestamp=${System.currentTimeMillis()}"/>" ></script>
<script src="<c:url value="/resources/camau/js/benhanranghammat.js?timestamp=${System.currentTimeMillis()}"/>" ></script>
<script src="<c:url value="/resources/camau/js/benhanmatglocom.js?timestamp=${System.currentTimeMillis()}"/>" ></script>
<script src="<c:url value="/resources/camau/js/benhannhiem.js?timestamp=${System.currentTimeMillis()}"/>" ></script>
<script src="<c:url value="/resources/camau/js/benhanphukhoa.js?timestamp=${System.currentTimeMillis()}"/>" ></script>
<script src="<c:url value="/resources/camau/js/benhantamthan.js?timestamp=${System.currentTimeMillis()}"/>" ></script>
<script src="<c:url value="/resources/camau/js/benhandaymat.js?timestamp=${System.currentTimeMillis()}"/>" ></script>
<script src="<c:url value="/resources/camau/js/benhanmatlac.js?timestamp=${System.currentTimeMillis()}"/>" ></script>
<script src="<c:url value="/resources/camau/js/benhanmattreem.js?timestamp=${System.currentTimeMillis()}"/>" ></script>
<script src="<c:url value="/resources/camau/js/benhanphuchoichucnangnhi.js?timestamp=${System.currentTimeMillis()}"/>" ></script>
<script src="<c:url value="/resources/camau/js/benhanphuchoichucnang.js?timestamp=${System.currentTimeMillis()}"/>" ></script>
<script src="<c:url value="/resources/camau/js/benhanmatchanthuong.js?timestamp=${System.currentTimeMillis()}"/>" ></script>
<script src="<c:url value="/resources/camau/js/benhanngoaitruPHCN.js?timestamp=${System.currentTimeMillis()}"/>" ></script>
<script src="<c:url value="/resources/camau/js/benhanngoaitruchung.js?timestamp=${System.currentTimeMillis()}"/>" ></script>
<script src="<c:url value="/resources/camau/js/benhansankhoa.js?timestamp=${System.currentTimeMillis()}"/>" ></script>
<script src="<c:url value="/resources/camau/js/benhansosinh.js?timestamp=${System.currentTimeMillis()}"/>" ></script>
<script src="<c:url value="/resources/camau/js/benhanmatbanphantruoc.js?timestamp=${System.currentTimeMillis()}"/>" ></script>
<script src="<c:url value="/resources/camau/js/benhanngoaitruyhct.js?timestamp=${System.currentTimeMillis()}"/>" ></script>
<script src="<c:url value="/resources/camau/js/benhantonghop.js?timestamp=${System.currentTimeMillis()}"/>" ></script>
<script src="<c:url value="/resources/camau/js/benhanngoaitruyhct.js?timestamp=${System.currentTimeMillis()}"/>" ></script>
<script src="<c:url value="/resources/camau/js/benhanngoaitrurhm.js?timestamp=${System.currentTimeMillis()}"/>" ></script>
<script src="<c:url value="/resources/camau/js/benhanphathai.js?timestamp=${System.currentTimeMillis()}"/>" ></script>
<%--END VO BENH AN--%>

<script src="<c:url value="/resources/camau/js/quanlyhosobenhan.js?timestamp=${System.currentTimeMillis()}"/>" ></script>
<script src="<c:url value="/resources/camau/js/common.js?timestamp=${System.currentTimeMillis()}" />"></script>
<script src="<c:url value="/resources/camau/js/luulog.js?timestamp=${System.currentTimeMillis()}"/>" ></script>
<script src="<c:url value="/resources/js/libControl.js?timestamp=${System.currentTimeMillis()}"/>" ></script>
<script src="<c:url value="/resources/camau/js/jsonform.js?timestamp=${System.currentTimeMillis()}"/>" ></script>
<script src="<c:url value="/resources/camau/js/vobenhanCommon.js?timestamp=${System.currentTimeMillis()}"/>" ></script>
<script src="<c:url value="/resources/camau/js/vobenhanFunc.js?timestamp=${System.currentTimeMillis()}"/>" ></script>
<script src="<c:url value="/resources/camau/js/kysonoitru.js?timestamp=${System.currentTimeMillis()}"/>" ></script>
<script src="<c:url value="/resources/camau/js/thongtinbenhan.js?timestamp=${System.currentTimeMillis()}"/>" ></script>
<script src="<c:url value="/resources/camau/js/geturlxemcacphieu.js?timestamp=${System.currentTimeMillis()}"/>" ></script>
<script src="<c:url value="/resources/camau/js/lichsubenhan.js?timestamp=${System.currentTimeMillis()}"/>" ></script>
<script src="<c:url value="/resources/camau/vnptmoney.js?timestamp=${System.currentTimeMillis()}"/>" ></script>
<script src="<c:url value="/resources/camau/js/keyluulog.js?timestamp=${System.currentTimeMillis()}"/>" ></script>
<script src="<c:url value="/resources/camau/js/bienbankiemthaotv.js?timestamp=${System.currentTimeMillis()}"/>" ></script>
<script src="<c:url value="/resources/camau/js/phieuchuandoannguyennhantuvong.js?timestamp=${System.currentTimeMillis()}"/>" ></script>
</body>
</html>