<%@page import="l2.ThamSoManager" %>
<%@taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>
<%@ taglib uri="http://www.springframework.org/tags/form" prefix="form"%>
<%@page contentType="text/html" pageEncoding="UTF-8"  %>
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8"/>
    <meta name="google-site-verification" content="u6uNEfD4cb3gidezi4r_6aI8Wb1E07-ufBeCQpvmlqQ" />
    <meta http-equiv="Cache-Control" content="no-store" />
    <title>Hệ thống chăm sóc sức khỏe</title>
    <link rel="icon" href="<c:url value="/resources/images/favicon.ico" />" type="image/x-icon"/>
    <link rel="shortcut icon" href="<c:url value="/resources/images/favicon.ico" />" type="image/x-icon"/>
    <link href="<c:url value="/resources/css/divheader.css" />" rel="stylesheet"/>
    <link href="<c:url value="/resources/css/style_new.css" />" rel="stylesheet"/>

    <link rel="stylesheet" href="<c:url value="/resources/css/jquery-ui-redmond.1.9.1.css" />" />
    <script src="<c:url value="/resources/js/jquery.min.1.8.3.js" />"></script>
    <script src="<c:url value="/resources/js/jquery-ui.1.9.1.js" />"></script>
    <link href="<c:url value="/resources/bootstrap-4.1.3/dist/css/bootstrap.min.css" />" rel="stylesheet"/>
    <link href="<c:url value="/resources/jqgrid/css/ui.jqgrid.css" />" rel="stylesheet"/>
    <script src="<c:url value="/resources/jqgrid/js/i18n/grid.locale-en.js" />"></script>
    <script src="<c:url value="/resources/jqgrid/js/jquery.jqGrid.src.js" />"></script>
    <script src="<c:url value="/resources/js/common_function.js" />"></script>
    <script src="<c:url value="/resources/js/jquery.inputmask.bundle.min.js" />"></script>
    <script src="<c:url value="/resources/blockUI/jquery.blockUI.js" />"></script>
    <script src="<c:url value="/resources/dialog/jquery.alerts.js" />"></script>
    <link href="<c:url value="/resources/dialog/jquery.alerts.1.css" />" rel="stylesheet"/>
    <script src="<c:url value="/resources/camau/popper.1.11.0.js" />"></script>
    <script src="<c:url value="/resources/camau/js/lodash.min.js" />"></script>
    <link rel="stylesheet" href="<c:url value="/resources/font-awesome-4.7.0/css/font-awesome.min.css"/>">
    <script src="<c:url value="/resources/bootstrap-4.4.1-dist/js/bootstrap.min.js"/>" ></script>
    <link href="<c:url value="/resources/camau/css/khambenhnoitru.css" />" rel="stylesheet"/>
    <link href="<c:url value="/resources/camau/css/formio.full.min.css" />" rel="stylesheet"/>
    <link href="<c:url value="/resources/camau/css/loader.css" />" rel="stylesheet"/>
    <link href="<c:url value="/resources/camau/css/custom.css?timestamp=${System.currentTimeMillis()}" />" rel="stylesheet"/>
    <link href="<c:url value="/resources/camau/css/painter.css?timestamp=${System.currentTimeMillis()}" />" rel="stylesheet"/>
    <link rel="stylesheet" href="<c:url value="/resources/camau/css/select2.min.css" />" />
    <script src="<c:url value="/resources/camau/js/select2.min.js" />"></script>
    <script src="<c:url value="/resources/js/datetimepicker.js" />"></script>
    <link rel="stylesheet" href="<c:url value="/resources/css/datetimepicker.css" />" />
    <script src="<c:url value="/resources/js/jquery-confirm.min.js" />"></script>
    <link href="<c:url value="/resources/css/jquery-confirm.min.css" />" rel="stylesheet"/>
    <link href="<c:url value="/resources/combogrid/css/smoothness/jquery.ui.combogrid.css" />" rel="stylesheet"/>
    <script src="<c:url value="/resources/combogrid/plugin/jquery.ui.combogrid-1.6.3.js" />"></script>
    <script src="<c:url value="/resources/contextmenu/jquery.contextMenu.js" />"></script>
    <link href="<c:url value="/resources/contextmenu/jquery.contextMenu.css" />" rel="stylesheet"/>

    <script src="<c:url value="/resources/camau/js/jquery.validate.min.js" />"></script>
    <script src="<c:url value="/resources/camau/js/formio.full.min.js" />"></script>
    <script src="<c:url value="/resources/camau/material/moment.js" />"></script>
    <script src="<c:url value="/resources/smartca/js/pdf.js" />"></script>
    <script src="<c:url value="/resources/ckeditor/ckeditor.js" />"></script>
    <script src="<c:url value="/resources/ckeditor/adapters/jquery.js" />"></script>
    <script src="<c:url value="/resources/camau/js/pdf-lib.min.js" />"></script>
    <script src="<c:url value="/resources/camau/sha256.min.js?timestamp=${System.currentTimeMillis()}"/>" ></script>
    <script src="<c:url value="/resources/camau/js/jsonform.js?timestamp=${System.currentTimeMillis()}"/>" ></script>
    <script src="<c:url value="/resources/js/read_file.js" />"></script>
    <script src="<c:url value="/resources/CMU/pdfobject.js" />"></script>
    <script src="<c:url value="/resources/CMU/print.min.js" />"></script>

    <style>
        .ui-widget-content .bncapcuuClass {
            color: red;
            weight-font: bold;
            background-image: none;
        }
        .ui-widget-content .bncapcuucoclsClass {
            color: #1500ff;
            weight-font: bold;
            background-image: none;
        }
        .ui-widget-content .treemClass {
            color: #137c13;
            weight-font: bold;
            background-image: none;
        }
        .ui-widget-content .treemcoclsClass {
            color: #1500ff;
            weight-font: bold;
            background-image: none;
        }
        .ui-widget-content .kobhytClass {
            color: #bf00ff;
            weight-font: bold;
            background-image: none;
        }
        .ui-widget-content .kobhytcoclsClass {
            color: #1500ff;
            weight-font: bold;
            background-image: none;
        }
        .ui-widget-content .vienphiClass {
            color: #EE7600;
            weight-font: bold;
            background-image: none;
        }
        .ui-widget-content .vienphicoclsClass {
            color: #1500ff;
            weight-font: bold;
            background-image: none;
        }
        .ui-widget-content .kevattuclsClass {
            color: #1500ff;
            weight-font: bold;
            background-image: none;
        }
        .ui-widget-content .moneyClass {
            color: #21409A;
            weight-font: bold;
            background-image: none;
        }
    </style>
    <script>
        var singletonObject = {
            khoaKham: "${KHOAKHAM}" ? "${KHOAKHAM}" : 0,
            phongDuocSet: "${Sess_PhongDuocSet}",
            makhoa: "${Sess_PhongBan}",
            maphongbenh: "${Sess_Phong}",
            dvtt: "${Sess_DVTT}",
            userId: "${Sess_UserID}",
            user: "${Sess_User}",
            admin: "${Sess_Admin}",
            danhsachphongban: [],
            danhsachphongbenh: [],
            ngayhientai: "${ngayhientai}",
            danhsachloaittpt: [],
            danhsachkhothuocBHYT: [],
            danhsachkhovattu: [],
            danhsachkhomienphi: [],
            danhsachkhomuataiquay: [],
            danhsachkhodongy: [],
            danhsachkhodichvu: [],
            danhsachgiuongbenh: [],
            danhsachnghenghiep: [],
            danhsachdantoc: [],
            danhsachthutruong: [],
            danhsachtruongkhoa: [],
            danhsachbvchuyentuyen: [],
            danhsachxangdau: [],
            danhsachnhanvien: [],
            danhsachtatcanhanvien: [],
            danhsachquanhe: [],
            danhsachnguyennhantv: [],
            danhsachnnoituvong: [],
            danhsachtinh: [],
            danhsachquyenchungtu: [],
            danhsachloaibenhan: [],
            objkhoanhanvien: {},
            thamso82816: 0,
            thamso42001: 0,
            thamso960518: 0,
            thamso960484: 0,
            bant: "${BANT}",
            tenbenhvien: "<%= ThamSoManager.instance(session).getThamSoString("16","0")%>",
            locDsBnTheoPhong: "<%= ThamSoManager.instance(session).getThamSoString("82031","0")%>",
            timKiemCLS: "<%= ThamSoManager.instance(session).getThamSoString("228","0")%>",
            hienThiMauSacBNCoVTXN: "<%= ThamSoManager.instance(session).getThamSoString("40061","0")%>",
            hienThiMauSacBN: "<%= ThamSoManager.instance(session).getThamSoString("31008","0")%>",
            layMaBenhNhanGrid: "<%= ThamSoManager.instance(session).getThamSoString("82040","0")%>",
            phaiDongVienPhi: "<%= ThamSoManager.instance(session).getThamSoString("89","0")%>",
            bnMienPhiKhongCanDongTien: "<%= ThamSoManager.instance(session).getThamSoString("820343","0")%>",
            layKetQuaTuDong: "<%= ThamSoManager.instance(session).getThamSoString("91045","0")%>",
            canhBaoChuaDongVienPhi: "<%= ThamSoManager.instance(session).getThamSoString("96052","0")%>",
            hienThiThongTinMoRong: "<%= ThamSoManager.instance(session).getThamSoString("31009","0")%>",
            batBuocChonNguoiDoc: "<%= ThamSoManager.instance(session).getThamSoString("96082","0")%>",
            clsVuotNguong: "<%= ThamSoManager.instance(session).getThamSoString("74006","0")%>",
            sapXepXetNghiem: "<%= ThamSoManager.instance(session).getThamSoString("82001","0")%>",
            kyThuatVienHuyKetQua: "<%= ThamSoManager.instance(session).getThamSoString("94336","0")%>",
            goiSo: "<%= ThamSoManager.instance(session).getThamSoString("91100","0")%>",
            chanSuaKQKhiThanhToan: "<%= ThamSoManager.instance(session).getThamSoString("229","0")%>",
            apDungCheckBox: "<%= ThamSoManager.instance(session).getThamSoString("96063","0")%>",
            soPhutGiuaChiDinhVaTraKetQua: "<%= ThamSoManager.instance(session).getThamSoString("960545","0")%>",
            inPhieuKQMay: "<%= ThamSoManager.instance(session).getThamSoString("960588","0")%>",
        }
    </script>
</head>
<body>
<div id="panel_all" style="background: white">
    <%@include file="../../../../resources/Theme/include_pages/menu.jsp"%>
    <div class="p-4">
        <div id="hsba_tabs">
            <ul>
                <li><a href="#hsba_tab0" id="hsba_tab0_header">Tiếp nhận</a></li>
                <li><a href="#hsba_tab1" id="hsba_tab1_header">Kết quả</a></li>
                <input type="text" class="input-only-date input-custom" style="float:right" id="xn_ngaylamviec">
            </ul>
            <div id="hsba_tab0">
                <div>
                    <div class="row ml-0 mr-0 mt-3 mb-3 pl-2 pr-2">
                        <div class="col-sm-12">
                            <div class="row ml-0 mr-0">
                                <div class="col-sm-1 pr-2 mb-2">
                                    <label>Ngày</label>
                                    <input type="text" class="form-control form-control-sm input-only-date input-custom" id="xn_ngaychidinh">
                                </div>
                                <div class="col-sm-2 pr-2">
                                    <label>Khoa</label>
                                    <select class=" form-control form-control-sm" id="xn_khoa" data-show-subtext="true" data-live-search="true">
                                    </select>
                                </div>
                                <div class="col-sm-2 pr-2">
                                    <label>Phòng</label>
                                    <select class=" form-control form-control-sm" id="xn_phong" data-show-subtext="true" data-live-search="true">
                                    </select>
                                </div>
                                <div class="col-sm-1 pr-2">
                                    <label>Đối tượng</label>
                                    <select class=" form-control form-control-sm" id="xn_doituong" data-show-subtext="true" data-live-search="true">
                                        <option value="-1">--Tất cả--</option>
                                        <option value="1">Có BHYT</option>
                                        <option value="0">Không BHYT</option>
                                    </select>
                                </div>
                                <div class="col-sm-1 pr-2">
                                    <label>Trạng thái</label>
                                    <select class=" form-control form-control-sm" id="xn_trangthai" data-show-subtext="true" data-live-search="true">
                                        <option value="0">Chưa thực hiện</option>
                                        <option value="1">Đã thực hiện</option>
                                    </select>
                                </div>
                                <div class="col-sm-1">
                                    <label>&nbsp;</label>
                                    <button id="xn_lammoi" class="btn btn-primary form-control form-control-sm line-height-1">
                                        <i class="fa fa-refresh"></i> Làm mới
                                    </button>
                                </div>
                                <div class="col-sm-12">
                                    <div id="list_benhnhan_wrap" class="wrap-jqgrid">
                                        <table id="list_benhnhan"></table>
                                    </div>
                                </div>
                                <div class="col-sm-12 mt-2">
                                    <label style="color:red;font-weight: normal;">BN cấp cứu</label>
                                    <label style="color:#137c13;margin-left:20px;font-weight: normal;">BN &lt; 6
                                        tuổi</label><br>
                                    <label style="color:#bf00ff;font-weight: normal;">Bệnh nhân VP, chưa đóng
                                        tiền</label><br>
                                    <label style="color:#EE7600;font-weight: normal;">Bệnh nhân VP, đã đóng
                                        tiền</label>
                                    <label style="color:#1500ff;;font-weight: normal;">Bệnh nhân đã kê vật tư CLS</label>     <!--vuthanhvinh.nan HISHTROTGG-37490-->
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div id="hsba_tab1" class="pl-4 pr-4 pt-2 pb-2">
                <div class="row ml-0 mr-0">
                    <div class="col-sm-12">
                        <div id="formKetQua"></div>
                    </div>
                    <div class="text-center col-sm-12 pb-2">
                        <button type="button" class="btn btn-sm btn-primary chuakyshow" id="xn_luuthongtin"><i class="fa fa-save"></i> Lưu thông tin</button>
                        <button type="button" class="btn btn-sm btn-success" id="xn_xemketqua"><i class="fa fa-eye"></i> Xem kết quả</button>
                        <button type="button" class="btn btn-sm btn-success kyso" id="xn_kyso"><i class="fa fa-key"></i> Ký số</button>
                        <button type="button" class="btn btn-sm btn-danger kyso" id="xn_huykyso"><i class="fa fa-key"></i> Huỷ ký số</button>
                        <button type="button" class="btn btn-sm btn-warning chuakyshow" id="xn_layketquatudong"><i class="fa fa-save"></i> Lấy kết quả tự động</button>
                        <button type="button" class="btn btn-sm btn-warning chuakyshow" id="xn_danhsachketquamay"><i class="fa fa-list"></i> Danh sách kết quả máy</button>
                    </div>
                    <div class="col-sm-12">
                        <div id="list_xetnghiem_wrap" class="wrap-jqgrid">
                            <table id="list_xn_bhyt"></table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="modal fade" id="modalDanhSachKetQuaMay" role="dialog"
         aria-labelledby="modalDanhSachKetQuaMay" aria-hidden="true"
         data-keyboard="false" data-backdrop="static">
        <div class="modal-dialog modal-dialog-scrollable modal-dialog-centered" style="max-width: 92%; width: 60%;" role="document">
            <div class="modal-content wrap-full" style="height: auto">
                <div class="modal-header modal-header-sticky py-2 d-flex justify-content-center align-items-center">
                    <h6 class="modal-title text-primary font-weight-bold">Danh sách kết quả máy</h6>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <div class="row ml-0 mr-0 mb-2">
                        <div class="col-sm-auto pr-2">
                            <label>Ngày</label>
                        </div>
                        <div class="col-sm-2 pr-2">
                            <input type="text" class="form-control form-control-sm input-only-date input-custom" id="xn_ngayketquamay">
                        </div>
                        <div class="col-sm-auto pr-2">
                            <label>Máy</label>
                        </div>
                        <div class="col-sm-2 pr-2">
                            <select class=" form-control form-control-sm" id="xn_mayketquamay" data-show-subtext="true" data-live-search="true">
                            </select>
                        </div>
                        <div class="col-sm-4">
                            <button type="button" class="btn btn-sm btn-primary" id="xn_lammoiketquamay"><i class="fa fa-refresh"></i> Làm mới</button>
                        </div>
                    </div>
                    <div class="row ml-0 mr-0">
                        <div class="col-sm-12" id="wrap_danhSachKetQuaMay">
                            <table id="list_ketquamay"></table>
                        </div>
                    </div>

                </div>
            </div>
        </div>
    </div>

    <jsp:include page="../../../../resources/Theme/include_pages/footerBT.jsp"/>
    <jsp:include page="loader.jsp"/>

    <script src="<c:url value="/resources/camau/js/xetnghiem.js?timestamp=${System.currentTimeMillis()}"/>" ></script>
    <script src="<c:url value="/resources/camau/js/kysonoitru.js?timestamp=${System.currentTimeMillis()}"/>" ></script>
    <script src="<c:url value="/resources/camau/js/luulog.js?timestamp=${System.currentTimeMillis()}"/>" ></script>
    <script src="<c:url value="/resources/camau/vnptmoney.js?timestamp=${System.currentTimeMillis()}"/>" ></script>
    <script src="<c:url value="/resources/camau/js/keyluulog.js?timestamp=${System.currentTimeMillis()}"/>" ></script>
    <script src="<c:url value="/resources/camau/js/common.js?timestamp=${System.currentTimeMillis()}" />"></script>
    <script src="<c:url value="/resources/camau/smartca769.js?timestamp=${System.currentTimeMillis()}" />"></script>
    <script src="<c:url value="/resources/camau/js/lichsubenhan.js?timestamp=${System.currentTimeMillis()}"/>" ></script>
</body>
</html>