function getUrlChamSocCap2(dataCSC2, kySo = 0) {
    return new Promise(async (resolve, reject) => {
        getFilesign769(
            "PHIEU_NOITRU_CHAMSOCCAP2",
            dataCSC2.ID_CHAM_SOC_CAP_2,
            -1,//singletonObject.userId,
            singletonObject.dvtt,
            thongtinhsba.thongtinbn.SOVAOVIEN,
            thongtinhsba.thongtinbn.SOVAOVIEN_DT,
            -1,
            function(data) {
                if(data.length > 0 && kySo == 0) {
                    getCMUFileSigned769GetLink(data[0].KEYMINIO, 'pdf').then(pdfData => {
                        resolve({
                            url: pdfData,
                            kySo: 1,
                            isError: 0,
                            message: 'Thành công',
                        });
                    });
                } else {
                    $.get("cmu_getlist?url=" + convertArray([
                        3,
                        dataCSC2.ID_CHAM_SOC_CAP_2,
                        "CMU_LCSC2_CHITIET_SELXROW"
                    ])).done(function(data) {
                        if (data.length > 0) {
                            var dsPdf = [];
                            for(var p = 0; p < Math.ceil(data.length/3); p++){
                                var objectNgay = {};
                                var objectGio = {};
                                var objectPhanCap = {};
                                var objectNhanDinh = {};
                                var objectMach = {};
                                var objectNhietDo = {};
                                var objectHuyetAp = {};
                                var objectNhipTho = {};
                                var objectSPO2 = {};
                                var objectCanNang = {};
                                var objectBMI = {};
                                var objectDieuDuong = {};
                                var objectIdChiTiet = {};
                                var objectKeySign = {};
                                for(var i = 0; i < 3; i++) {
                                    var item = data[i + p * 3];
                                    if (item) {
                                        objectNgay['ngay_' + (i+1)] = item.ONLY_NGAY;
                                        objectGio['gio_' + (i+1)] = item.ONLY_GIO;
                                        objectPhanCap['phancapcs_' + (i+1)] = item.PHAN_CAP_CS;
                                        objectNhanDinh['nhandinhtd_' + (i+1)] = item.NHAN_DINH_TD;
                                        objectDieuDuong['dieuduong_' + (i+1)] = item.TEN_NGUOI_TAO;
                                        objectMach['mach_' + (i+1)] = item.MACH;
                                        objectNhietDo['nhietdo_' + (i+1)] = item.NHIET_DO;
                                        objectHuyetAp['huyetap_' + (i + 1)] = item.HUYET_AP;
                                        objectNhipTho['nhiptho_' + (i+1)] = item.NHIP_THO;
                                        objectSPO2['spo2_' + (i+1)] = item.SPO2;
                                        objectCanNang['cannang_' + (i+1)] = item.CAN_NANG;
                                        objectBMI['bmi_' + (i+1)] = item.BMI;
                                        objectIdChiTiet['id_chi_tiet_' + (i+1)] = item.ID_CHI_TIET;
                                        objectKeySign['keysign_' + (i+1)] = dataCSC2.ID_CHI_TIET && dataCSC2.ID_CHI_TIET == item.ID_CHI_TIET ? "1" : item.KEYSIGN;
                                    }
                                }
                                var params = {
                                    dvtt: singletonObject.dvtt,
                                    stt_benhan: thongtinhsba.thongtinbn.STT_BENHAN,
                                    sobenhan: thongtinhsba.thongtinbn.SOBENHAN,
                                    mabenhnhan: thongtinhsba.thongtinbn.MABENHNHAN,
                                    ngayvaovien: thongtinhsba.thongtinbn.NGAYGIO_NHAPVIEN,
                                    ngaydautien: data[0].ONLY_NGAY,
                                    id_phieu: dataCSC2.ID_CHAM_SOC_CAP_2,
                                    maxrow: singletonObject.maxRowCSC2,
                                    page: p,
                                    ...objectNgay,
                                    ...objectGio,
                                    ...objectPhanCap,
                                    ...objectNhanDinh,
                                    ...objectDieuDuong,
                                    ...objectMach,
                                    ...objectNhietDo,
                                    ...objectHuyetAp,
                                    ...objectNhipTho,
                                    ...objectSPO2,
                                    ...objectCanNang,
                                    ...objectBMI,
                                    ...objectIdChiTiet,
                                    ...objectKeySign
                                };
                                resolve({
                                    url: 'cmu_in_CMU_CHAMSOCCAP2?type=pdf&' + $.param(params),
                                    kySo: 1,
                                    isError: 0,
                                    message: 'Thành công',
                                });
                            }
                        }
                    });
                }
            }
        );
    });
}
$(function() {
    var formCSC2ThongTinChung;
    $("#lanchamsoccap2_formtaophieu").validate({
        rules: {
            lanchamsoc2_ngaybatdau: {
                required: true,
                validDateTime: true,
                validDateNgayhientai: true,
                validDateNgaynhapvien: true
            },
        }
    });

    $("#modalLanChamSocCap2").on("hidden.bs.modal", function() {
        thongtinhsba.thongtinbn.lanChamSocCap2 = {};
        reloadDSLanChamSocCap2();
    });

    $("#csc2_lammoi").click(function(){
        $("#ttchamsoc-chamsoccap2").click();
    });

    $("#modalTaoLanChamSocCap2").on("show.bs.modal", function() {
        $("#lanchamsoc2_ngaybatdau").val(moment().format('DD/MM/YYYY HH:mm'));
    });

    $("#ttchamsoc-chamsoccap2").click(function(){
        instanceGridChamSocCap2();
        loadConfigChamSocCap2();
    });

    $("#csc2_them").click(function(){
        if (singletonObject.configGroupChamSocCap2.length == 0){
            return notifiToClient("Red", "Vui lòng liên hệ IT để cấu hình nhóm chỉ số chăm sóc cấp 2, 3");
        }
        thongtinhsba.thongtinbn.lanChamSocCap2 = {};
        $("#modalTaoLanChamSocCap2").modal("show");
    });

    $("#lanchamsoc2_action_taolan").click(function() {
        if($("#lanchamsoccap2_formtaophieu").valid()) {
            var idButton = "lanchamsoc2_action_taolan";
            var ngay = $("#lanchamsoc2_ngaybatdau").val();
            var data = thongtinhsba.thongtinbn;
            showSelfLoading(idButton);
            $.post("cmu_post", {url:[singletonObject.dvtt,
                    singletonObject.userId,
                    singletonObject.makhoa,
                    data.SOVAOVIEN,
                    data.SOVAOVIEN_DT,
                    data.MA_BENH_NHAN,
                    data.STT_BENHAN,
                    thongtinhsba.thongtinbn.ICD_HT.split('-')[0],
                    thongtinhsba.thongtinbn.ICD_HT.split('-')[1],
                    ngay,
                    "CMU_LANCHAMSOCCAP2_INS_V2"].join("```")}).done(function (data) {
                if(data > 0){
                    // var url = "cmu_getlist?url=" + convertArray([data.ID_CHAM_SOC_CAP_2, "CMU_LCSC2_CHANDOAN_SEL"]);
                    $.get("cmu_getlist?url="+convertArray([data.ID_CHAM_SOC_CAP_2, "CMU_LCSC2_CHANDOAN_SEL"])).done(function(dataLog){
                        $.post("cmu_post", {url:[thongtinhsba.thongtinbn.lanChamSocCap2.ID_CHAM_SOC_CAP_2,
                                dataSubmit.ICD_DIEUDUONG,
                                dataSubmit.TENICD_DIEUDUONG,
                                singletonObject.userId,
                                singletonObject.makhoa,
                                "CMU_LCSC2_CHANDOAN_INS"].join("```")}).done(function (id) {
                            if(id > 0){
                                luuLogHSBATheoBN({
                                    SOVAOVIEN: thongtinhsba.thongtinbn.SOVAOVIEN,
                                    LOAI: LOGHSBALOAI.CHAMSOCCAP2CHANDOAN.KEY,
                                    NOIDUNGBANDAU: "Thêm chẩn đoán điều dưỡng/lượng giá mục tiêu '" + dataSubmit.ICD_DIEUDUONG + " - " + dataSubmit.TENICD_DIEUDUONG + "' tờ chăm sóc cấp 2 số: " + thongtinhsba.thongtinbn.lanChamSocCap2.TO_SO,
                                    NOIDUNGMOI: "",
                                    USERID: singletonObject.userId,
                                    ACTION: LOGHSBAACTION.INSERT.KEY,
                                });
                                releadGridChamSocCap2ChanDoanDieuDuong();
                                icdChanDoanDieuDuongElement.setValue("");
                                tenICDChanDoanDieuDuongElement.setValue("");
                            } else if (id == -1) {
                                notifiToClient("Red", "ICD đã tồn tại");
                            } else if (id == -2) {
                                notifiToClient("Red", "Chỉ được nhập tối đa 4 chẩn đoán");
                            } else {
                                notifiToClient("Red",MESSAGEAJAX.ERROR);
                            }
                        }).fail(function(error) {
                            notifiToClient("Red",MESSAGEAJAX.ERROR);
                        });
                    });
                    $.get("cmu_getlist?url="+convertArray([singletonObject.dvtt, data, 'CMU_LANCHAMSOCCAP2_SEL_ID'])).done(function(dataLog){
                        luuLogHSBATheoBN({
                            SOVAOVIEN: thongtinhsba.thongtinbn.SOVAOVIEN,
                            LOAI: LOGHSBALOAI.CHAMSOCCAP2.KEY,
                            NOIDUNGBANDAU: "Tạo đợt chăm sóc cấp 2 tờ số: " + dataLog[0].TO_SO,
                            NOIDUNGMOI: "",
                            USERID: singletonObject.userId,
                            ACTION: LOGHSBAACTION.INSERT.KEY,
                        });
                    });
                    $("#modalTaoLanChamSocCap2").modal("hide");
                    createFormChamSocCap2({ID_CHAM_SOC_CAP_2: data});
                } else {
                    notifiToClient("Red",MESSAGEAJAX.ERROR);
                }
            }).fail(function(error) {
                notifiToClient("Red",MESSAGEAJAX.ERROR);
            }).always(function() {
                hideSelfLoading(idButton);
            });
        }
    });

    $("#lanchamsoccap2_action_luu").click(function() {
        if(formCSC2ThongTinChung.checkValidity()) {
            var dataSubmit = formCSC2ThongTinChung.submission.data;
            var idButton = "lanchamsoccap2_action_luu";
            showSelfLoading(idButton);
            $.post("cmu_post", {url:[dataSubmit.ID_CHAM_SOC_CAP_2,
                    dataSubmit.TO_SO,
                    dataSubmit.SO_GIUONG,
                    dataSubmit.CAN_NANG,
                    dataSubmit.IS_DI_UNG,
                    dataSubmit.STRING_DI_UNG,
                    dataSubmit.ICD_CHANDOAN,
                    dataSubmit.TENICD_CHANDOAN,
                    "CMU_LANCHAMSOCCAP2_UPD"].join("```")}).done(function (data) {
                if(data > 0){
                    luuLogHSBAChinhSua(singletonObject.thongTinCSC2TruocKhiChinhSua, dataSubmit, LOGHSBALOAI.CHAMSOCCAP2.KEY, keyLuuLog);
                    notifiToClient("Green",MESSAGEAJAX.ADD_SUCCESS);
                    createFormChamSocCap2(dataSubmit);
                } else {
                    notifiToClient("Red",MESSAGEAJAX.ERROR);
                }
            }).fail(function(error) {
                notifiToClient("Red",MESSAGEAJAX.ERROR);
            }).always(function() {
                hideSelfLoading(idButton);
            });
        }
    });

    $("#lanchamsoc2_action_ketthuclan").click(function() {
        var ngay = moment($("#lanchamsoc2_ngayketthuc").val(), "DD/MM/YYYY HH:mm");
        var ret = getThongtinRowSelected("ttcs_list_chamsoccap2");
        var ngayTemp = moment(ret.NGAY_KET_THUC_TEMP, "DD/MM/YYYY HH:mm");
        if (ngay < ngayTemp) {
            return notifiToClient("Red", "Ngày kết thúc không được nhỏ hơn " + ret.NGAY_KET_THUC_TEMP);
        }
        $.ajax({
            url: "cmu_post",
            method: "POST",
            async: false,
            data: {
                url:[
                    ret.ID_CHAM_SOC_CAP_2,
                    $("#lanchamsoc2_ngayketthuc").val(),
                    "CMU_LANCHAMSOCCAP2_KETTHUC"
                ].join("```")
            },
            success: function (countIns) {
                if(countIns > 0){
                    luuLogHSBATheoBN({
                        SOVAOVIEN: thongtinhsba.thongtinbn.SOVAOVIEN,
                        LOAI: LOGHSBALOAI.CHAMSOCCAP2.KEY,
                        NOIDUNGBANDAU: "",
                        NOIDUNGMOI: "Kết thúc đợt chăm sóc cấp 2 tờ số: " + ret.TO_SO + " - " + $("#lanchamsoc2_ngayketthuc").val(),
                        USERID: singletonObject.userId,
                        ACTION: LOGHSBAACTION.EDIT.KEY,
                    });
                    notifiToClient("Green",MESSAGEAJAX.EDIT_SUCCESS);
                    $("#modalKetThucLanChamSocCap2").modal("hide");
                    $("#ttchamsoc-chamsoccap2").click();
                } else {
                    notifiToClient("Red",MESSAGEAJAX.ERROR);
                }
            }
        });
    });

    $("#lanchamsoccap2_action_xem").click(function() {
        xemChamSocCap2(thongtinhsba.thongtinbn.lanChamSocCap2);
    });

    function instanceGridChamSocCap2() {
        var list = $("#ttcs_list_chamsoccap2");
        if(!list[0].grid) {
            list.jqGrid({
                url: '',
                datatype: "local",
                loadonce: true,
                height: 450,
                width: null,
                shrinkToFit: false,
                colModel: [
                    {
                        name: "TT",
                        label: "TT",
                        align: 'left',
                        width: 120,
                        formatter: function (cellValue, options, rowData) {
                            if (rowData.NGAY_KET_THUC) {
                                return '<span class="cellWithoutBackground" style="font-weight:bold ;color: green">Kết thúc</span>';
                            } else {
                                return '<span class="cellWithoutBackground" style="font-weight:bold ;color: red">Đang điều trị</span>';
                            }
                        }
                    },
                    {label: "KEYSIGN",name: 'KEYSIGN', index: 'KEYSIGN', width: 50, hidden: true},
                    {label: "ID_CHAM_SOC_CAP_2",name: 'ID_CHAM_SOC_CAP_2', index: 'ID_CHAM_SOC_CAP_2', width: 50, hidden: true},
                    {label: "MA_KHOA_TAO_PHIEU",name: 'MA_KHOA_TAO_PHIEU', index: 'MA_KHOA_TAO_PHIEU', width: 10, hidden: true},
                    {label: "Tờ",name: 'TO_SO', index: 'TO_SO', width: 50, align: 'center'},
                    {label: "Chẩn đoán",name: 'CHAN_DOAN', index: 'CHAN_DOAN', width: 400,
                        cellattr: function (rowId, tv, rawObject, cm, rdata) {
                            return 'style="white-space: normal;"';
                        }
                    },
                    {label: "Giường",name: 'SO_GIUONG', index: 'SO_GIUONG', width: 70},
                    {label: "C.Nặng",name: 'CAN_NANG', index: 'CAN_NANG', width: 70},
                    {label: "Dị ứng",name: 'DI_UNG_SHOW', index: 'DI_UNG_SHOW', width: 300, hidden: true},
                    {label: "IS_DI_UNG",name: 'IS_DI_UNG', index: 'IS_DI_UNG', width: 150, hidden: true},
                    {label: "STRING_DI_UNG",name: 'STRING_DI_UNG', index: 'STRING_DI_UNG', width: 150, hidden: true},
                    {label: "Người tạo phiếu",name: 'TEN_NGUOI_TAO_PHIEU', index: 'TEN_NGUOI_TAO_PHIEU', width: 150},
                    {label: "Ngày bắt đầu",name: 'NGAY_TAO_PHIEU', index: 'NGAY_TAO_PHIEU', width: 150},
                    {label: "Ngày kết thúc",name: 'NGAY_KET_THUC', index: 'NGAY_KET_THUC', width: 150},
                    {label: "NGAY_KET_THUC_TEMP",name: 'NGAY_KET_THUC_TEMP', index: 'NGAY_KET_THUC_TEMP', width: 150, hidden: true},
                    {label: "MA_NGUOI_TAO_PHIEU",name: 'MA_NGUOI_TAO_PHIEU', index: 'MA_NGUOI_TAO_PHIEU', hidden: true},
                ],
                rowNum: 1000000,
                caption: "Danh sách đợt chăm sóc cấp 2",
                onSelectRow: function (id) {
                },
                onRightClickRow: function(id) {
                    if (id) {
                        var ret = getThongtinRowSelected("ttcs_list_chamsoccap2");
                        var items = {
                            "xem": {name: '<p class="text-primary"><i class="fa fa-eye text-primary" aria-hidden="true"></i> Xem</p>'},
                        }
                        if (ret.NGAY_KET_THUC) {
                            items = {
                                ...items,
                                "huyketthuc": {name: '<p class="text-danger"><i class="fa fa-ban text-danger" aria-hidden="true"></i> Huỷ kết thúc</p>'},
                            }
                        } else {
                            items = {
                                ...items,
                                "ketthuc": {name: '<p class="text-success"><i class="fa fa-check text-success" aria-hidden="true"></i> Kết thúc</p>'},
                                "capnhat": {name: '<p class="text-success"><i class="fa fa-pencil-square-o text-success" aria-hidden="true"></i> Cập nhật</p>'},
                                "xoa": {name: '<p class="text-danger"><i class="fa fa-remove text-danger" aria-hidden="true"></i> Xoá</p>'},
                            }
                        }
                        $.contextMenu('destroy', '#ttcs_list_chamsoccap2 tr');
                        $.contextMenu({
                            selector: '#ttcs_list_chamsoccap2 tr',
                            reposition : false,
                            callback: function (key, options) {
                                if(key == 'capnhat') {
                                    createFormChamSocCap2(ret);
                                }
                                if(key == 'xoa'){
                                    if (singletonObject.userId !== ret.MA_NGUOI_TAO_PHIEU) {
                                        return notifiToClient("Red", "Bạn không có quyền xóa phiếu này");
                                    }
                                    confirmToClient("Xác nhận xóa đợt chăm sóc cấp 2", function (confirm) {
                                        $.ajax({
                                            url: "cmu_post",
                                            method: "POST",
                                            data: {url:[ret.ID_CHAM_SOC_CAP_2, "CMU_LANCHAMSOCCAP2_DEL"].join("```")},
                                            success: function (data) {
                                                if(data > 0){
                                                    luuLogHSBATheoBN({
                                                        SOVAOVIEN: thongtinhsba.thongtinbn.SOVAOVIEN,
                                                        LOAI: LOGHSBALOAI.CHAMSOCCAP2.KEY,
                                                        NOIDUNGBANDAU: "",
                                                        NOIDUNGMOI: "Xoá đợt chăm sóc cấp 2 tờ số: " + ret.TO_SO,
                                                        USERID: singletonObject.userId,
                                                        ACTION: LOGHSBAACTION.DELETE.KEY,
                                                    });
                                                    notifiToClient("Green",MESSAGEAJAX.DEL_SUCCESS);
                                                    $("#ttchamsoc-chamsoccap2").click();
                                                } else if (data == '-1'){
                                                    notifiToClient("Red", "Vui lòng xoá tất cả chi tiết đợt chăm sóc.");
                                                } else {
                                                    notifiToClient("Red",MESSAGEAJAX.ERROR);
                                                }
                                            },
                                            error: function (error) {
                                                notifiToClient("Red",MESSAGEAJAX.ERROR);
                                            }
                                        });
                                    });
                                }
                                if (key == 'xem'){
                                    xemChamSocCap2(ret);
                                }
                                if (key == 'ketthuc') {
                                    if (!ret.NGAY_KET_THUC_TEMP) {
                                        return notifiToClient("Red", "Đợt chăm sóc cấp 2 chưa cập nhật chi tiết.");
                                    }
                                    $("#lanchamsoc2_ngayketthuc").val(ret.NGAY_KET_THUC_TEMP);
                                    $("#modalKetThucLanChamSocCap2").modal("show");
                                }
                                if (key == 'kyso') {
                                    xemChamSocCap2(ret, 1);
                                }
                                if (key == 'huykyso') {
                                    confirmToClient("Bạn có chắc sẽ hủy ký số phiếu này?", function() {
                                        huykysoFilesign769("PHIEU_NOITRU_CHAMSOCCAP2", ret.ID_CHAM_SOC_CAP_2, singletonObject.userId, singletonObject.dvtt,
                                            thongtinhsba.thongtinbn.SOVAOVIEN, thongtinhsba.thongtinbn.SOVAOVIEN_DT, -1, function(data) {
                                                $("#ttchamsoc-chamsoccap2").click();
                                            })
                                    }, function () {

                                    })
                                }
                                if (key == 'huyketthuc') {
                                    confirmToClient("Xác nhận huỷ kết thúc đợt chăm sóc cấp 2", function (confirm) {
                                        $.ajax({
                                            url: "cmu_post",
                                            method: "POST",
                                            async: false,
                                            data: {
                                                url:[
                                                    ret.ID_CHAM_SOC_CAP_2,
                                                    "CMU_CSC2_NGAYKETTHUC_DEL"
                                                ].join("```")
                                            },
                                            success: function (countIns) {
                                                if(countIns > 0){
                                                    luuLogHSBATheoBN({
                                                        SOVAOVIEN: thongtinhsba.thongtinbn.SOVAOVIEN,
                                                        LOAI: LOGHSBALOAI.CHAMSOCCAP2.KEY,
                                                        NOIDUNGBANDAU: "",
                                                        NOIDUNGMOI: "Huỷ kết thúc đợt chăm sóc cấp 2 tờ số: " + ret.TO_SO,
                                                        USERID: singletonObject.userId,
                                                        ACTION: LOGHSBAACTION.EDIT.KEY,
                                                    });
                                                    notifiToClient("Green",MESSAGEAJAX.EDIT_SUCCESS);
                                                    $("#ttchamsoc-chamsoccap2").click();
                                                } else {
                                                    notifiToClient("Red",MESSAGEAJAX.ERROR);
                                                }
                                            }
                                        });
                                    });
                                }
                            },
                            items: items
                        });
                    }
                }
            });
        }
        reloadDSLanChamSocCap2();
    }
    var maxIdChamSocCap2 = null;

    function reloadDSLanChamSocCap2() {
        var data = thongtinhsba.thongtinbn;
        var url = "cmu_getlist?url=" + convertArray([singletonObject.dvtt, data.MABENHNHAN, data.SOVAOVIEN, data.SOVAOVIEN_DT, "CMU_LANCHAMSOCCAP2_SEL"]);
        $("#ttcs_list_chamsoccap2").jqGrid('setGridParam', {
            datatype: 'json',
            url: url,
            loadComplete: function(data) {
                // Lấy ID_CHAM_SOC_CAP_2 lớn nhất từ dữ liệu trả về
                if (Array.isArray(data) && data.length > 0) {
                    // Dữ liệu đã được sắp xếp DESC trong SQL, nên record đầu tiên có ID lớn nhất
                    maxIdChamSocCap2 = data[0].ID_CHAM_SOC_CAP_2;
                    console.log("ID_CHAM_SOC_CAP_2 lớn nhất:", maxIdChamSocCap2);
                } else {
                    maxIdChamSocCap2 = null;
                }
            }
        }).trigger('reloadGrid')
    }

    function createFormChamSocCap2(dataID) {
        $.get("cmu_getlist?url="+convertArray([singletonObject.dvtt, dataID.ID_CHAM_SOC_CAP_2, 'CMU_LANCHAMSOCCAP2_SEL_ID'])).done(function(data){
            if(data.length > 0) {
                thongtinhsba.thongtinbn.lanChamSocCap2 = data[0];
                singletonObject.thongTinCSC2TruocKhiChinhSua = JSON.parse(JSON.stringify(data[0]));
                Formio.createForm(document.getElementById('formCSC2ThongTinChung'),
                    {
                        "display": "form",
                        "components": [
                            {
                                label: "",
                                key: "wrap_benhchinh",
                                columns: [
                                    {
                                        "components": [
                                            {
                                                "label": "Tờ số",
                                                "key": "TO_SO",
                                                "type": "textfield",
                                                customClass: "pr-2",
                                                validate: {
                                                    required: true
                                                },
                                            },
                                        ],
                                        "width": 4,
                                        "size": "md",
                                    },
                                    {
                                        "components": [
                                            {
                                                "label": "Số giường",
                                                "key": "SO_GIUONG",
                                                "type": "textfield",
                                                customClass: "pr-2",
                                                "placeholder": "Số giường",
                                                hidden: true,
                                            },
                                        ],
                                        "width": 4,
                                        "size": "md",
                                    },
                                    {
                                        "components": [
                                            {
                                                "label": "Cân nặng",
                                                "key": "CAN_NANG",
                                                "type": "textfield",
                                                validate: {
                                                    // required: true
                                                },
                                                "placeholder": "Cân nặng",
                                                hidden: true
                                            },
                                        ],
                                        "width": 4,
                                        "size": "md",
                                    },

                                ],
                                "customClass": "ml-0 mr-0",
                                "type": "columns",
                            },
                            {
                                label: "",
                                key: "wrap_benhchinh",
                                columns: [
                                    {
                                        "components": [
                                            {
                                                "tag": "label",
                                                "content": "Chẩn đoán",
                                                "key": "htmllabel",
                                                "type": "htmlelement",
                                            },
                                        ],
                                        "width": 12,
                                        "size": "md",
                                    },
                                    {
                                        "components": [
                                            {
                                                "label": "",
                                                "key": "ICD_CHANDOAN",
                                                "type": "textfield",
                                                customClass: "pr-2",
                                                validate: {
                                                    required: true
                                                },
                                                "placeholder": "ICD",
                                            },
                                        ],
                                        "width": 2,
                                        "size": "md",
                                    },
                                    {
                                        "components": [
                                            {
                                                "label": "",
                                                "key": "TENICD_CHANDOAN",
                                                "type": "textfield",
                                                customClass: "",
                                                validate: {
                                                    required: true
                                                },
                                                "placeholder": "Tên bệnh chính",
                                            },
                                        ],
                                        "width": 10,
                                        "size": "md",
                                    },

                                ],
                                "customClass": "ml-0 mr-0",
                                "type": "columns",
                            },
                            {
                                label: "",
                                key: "wrap_benhchinh",
                                columns: [
                                    {
                                        "components": [
                                            {
                                                "tag": "label",
                                                "content": "Dị ứng",
                                                "key": "htmllabel",
                                                "type": "htmlelement",
                                            },
                                        ],
                                        "width": 12,
                                        "size": "md",
                                    },
                                    {
                                        "components": [
                                            {
                                                "label": "",
                                                "key": "IS_DI_UNG",
                                                "type": "select",
                                                "data": {
                                                    "values": [
                                                        {
                                                            "label": "Không",
                                                            "value": '0'
                                                        },
                                                        {
                                                            "label": "Có",
                                                            "value": '1'
                                                        },
                                                    ]
                                                },
                                                "defaultValue": "0",
                                                "customClass": "pr-2",
                                                validate: {
                                                    required: true
                                                }
                                            },
                                        ],
                                        "width": 2,
                                        "size": "md",
                                    },
                                    {
                                        "components": [
                                            {
                                                "label": "",
                                                "key": "STRING_DI_UNG",
                                                "type": "textfield",
                                                "placeholder": "Dị ứng",
                                                "customConditional": "show = (data.IS_DI_UNG == '1' ? 1 : 0);",
                                            },
                                        ],
                                        "width": 10,
                                        "size": "md",
                                    },

                                ],
                                "customClass": "ml-0 mr-0",
                                "type": "columns",
                            },
                        ]
                    }
                ).then(function (form) {
                    formCSC2ThongTinChung = form;
                    var tenBenhchinhElement = form.getComponent('TENICD_CHANDOAN');
                    var icdBenhchinhElement = form.getComponent('ICD_CHANDOAN');
                    actionICDBenhChinh("ICD_CHANDOAN", "TENICD_CHANDOAN", form);
                    combgridTenICD(getIdElmentFormio(form,'TENICD_CHANDOAN'), function(item) {
                        icdBenhchinhElement.setValue(item.ICD);
                        tenBenhchinhElement.setValue(item.MO_TA_BENH_LY);
                    });

                    var giuong;
                    layThongTinBuongGiuong(thongtinhsba.thongtinbn.STT_BENHAN, thongtinhsba.thongtinbn.STT_DOTDIEUTRI, function(result) {
                        giuong = result.so_giuong;
                        form.submission =  {
                            data: {
                                ...form.submission.data,
                                giuong: giuong
                            }
                        };
                    });
                    thongtinhsba.thongtinbn.lanChamSocCap2.CAN_NANG = thongtinhsba.thongtinbn.lanChamSocCap2.CAN_NANG ? thongtinhsba.thongtinbn.lanChamSocCap2.CAN_NANG : thongtinhsba.thongtinbn.CANNANG;
                    thongtinhsba.thongtinbn.lanChamSocCap2.SO_GIUONG = thongtinhsba.thongtinbn.lanChamSocCap2.SO_GIUONG ? thongtinhsba.thongtinbn.lanChamSocCap2.SO_GIUONG : giuong;
                    thongtinhsba.thongtinbn.lanChamSocCap2.TENICD_CHANDOAN = thongtinhsba.thongtinbn.lanChamSocCap2.TENICD_CHANDOAN ? thongtinhsba.thongtinbn.lanChamSocCap2.TENICD_CHANDOAN : thongtinhsba.thongtinbn.ICD_HT.split('-')[1];
                    thongtinhsba.thongtinbn.lanChamSocCap2.ICD_CHANDOAN = thongtinhsba.thongtinbn.lanChamSocCap2.ICD_CHANDOAN ? thongtinhsba.thongtinbn.lanChamSocCap2.ICD_CHANDOAN : thongtinhsba.thongtinbn.ICD_HT.split('-')[0];
                    form.submission =  {
                        data: {
                            ...thongtinhsba.thongtinbn.lanChamSocCap2
                        }
                    };

                    $("#modalLanChamSocCap2").modal("show");
                });
            }
        });
    }

    function xemChamSocCap2(dataCSC2, kySo = 0) {
        getUrlChamSocCap2(dataCSC2, kySo).then(objReturn => {
            if (objReturn.isError == 0) {
                previewPdfDefaultModal(objReturn.url, 'td_frame_inphieutruyendich');
            } else {
                notifiToClient("Red", objReturn.message);
            }
        }).catch(error => {
            notifiToClient("Red", error.message || "Lỗi không xác định");
        });
    }

    function loadConfigChamSocCap2() {
        $("#dli_action_them").hide();
        $("#dli_action_them_csc2").show();
        $.get("cmu_getlist?url=" + convertArray([singletonObject.dvtt, "-1", "CMU_CHAMSOCCAP2_CONFIG_GET"])).done(function(data) {
            if (data.length > 0) {
                singletonObject.configChamSocCap2 = data;
                $.ajax({
                    url: "cmu_getlist?url=" + convertArray([singletonObject.dvtt, "-1", "CMU_CSC2_GROUP_CONFIG_GET"]),
                    type: 'GET',
                    async: false
                }).done(function (data) {
                    if (data.length > 0) {
                        singletonObject.configGroupChamSocCap2 = data;
                        var arrJsonGroup = [];
                        for(var i=0; i < data.length; i++) {
                            arrJsonGroup.push(genJsonGroupCSC2({
                                "labelGroup": data[i].TENHIENTHI,
                                "key": data[i].LOAI,
                            }));
                        }
                        singletonObject.jsonCSC2 = {
                            "display": "form",
                            "components": [
                                {
                                    "customClass": "mr-0 ml-0",
                                    "key": "columns1",
                                    "type": "columns",
                                    "label": "Columns",
                                    "columns": arrJsonGroup
                                }
                            ]
                        }
                    } else {
                        notifiToClient("Red", "Vui lòng liên hệ IT để cấu hình nhóm chỉ số chăm sóc cấp 2");
                    }
                });
            }
        });

        singletonObject.maxRowCSC2 = 30;

        $.get("cmu_getlist?url=" + convertArray([singletonObject.dvtt, singletonObject.makhoa, "-1", "CMU_CSC2_DATALIST_ITEM_SEL"])).done(function(data) {
            if (data && data.length > 0) {
                singletonObject.danhSachItemChamSocCap2 = data.map(function(item) {
                    return {
                        label: item.TEN_ITEM,
                        value: item.TEN_ITEM,
                        ghiChu: item.MOTA_ITEM,
                        loai: item.LOAI_ITEM,
                    }
                })
            }
        });
    }
});