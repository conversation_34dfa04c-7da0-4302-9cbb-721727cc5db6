create or replace FUNCTION             CMU_CHECKPHIEUKQDAKYXEM(
p_dvtt          IN varchar2,
p_sovaovien    IN varchar2,
p_sovaovien_dt 	 IN varchar2,
p_ky_hieu_phieu 	 IN varchar2,
p_SO_PHIEU_DV 	 IN varchar2,
p_MA_DICH_VU 	 IN varchar2
)
return number IS
v_result number:=0;
BEGIN


SELECT count(1) into v_result
from smartca_signed_kcb
where dvtt = p_dvtt and
    sovaovien =p_sovaovien
  and sovaovien_dt = p_sovaovien_dt
  and SO_PHIEU_DV =  p_SO_PHIEU_DV
  and MA_DICH_VU = p_MA_DICH_VU
  and status = 0
  and ky_hieu_phieu in ('PHIEUKQ_XETNGHIEM_KYXEM', 'PHIEUKQ_CDHA_KYXEM');
return v_result;

END;
