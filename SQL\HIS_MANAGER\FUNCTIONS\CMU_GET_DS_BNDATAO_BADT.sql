create or replace FUNCTION "CMU_GET_DS_BNDATAO_BADT" (
    p_dvtt            IN                VARCHAR2,
    p_ma<PERSON><PERSON>_<PERSON>han     IN                VARCHAR2,
    p_ma<PERSON>han        IN                VARCHAR2,
    p_ngayravien      IN                VARCHAR2,
    p_den<PERSON><PERSON>vien   IN                VARCHAR2,
    p_khoa            IN                NUMBER
) RETURN SYS_REFCURSOR IS

    v_ngayravien      DATE := TO_DATE(p_ngayravien || ' 00:00:00', 'dd/mm/yyyy hh24:mi:ss');
    v_denngayravien   DATE := TO_DATE(p_denngayravien || ' 23:59:59', 'dd/mm/yyyy hh24:mi:ss');
    cur               SYS_REFCURSOR;
BEGIN
    OPEN cur FOR SELECT DISTINCT
                     vba.sovaovien,
                     dt.sovaovien_dt,
                     vba.mabenhan          sobenhan,
                     bn.ten_benh_nhan      ten_benh_nhan,
                     bn.tuoi               tuoi,
                     TO_CHAR(bn.ngay_sinh, 'dd/mm/yyyy') ngay_sinh,
                     bn.dia_chi            dia_chi,
                     TO_CHAR(nt.ngaynhapvien, 'dd/MM/yyyy HH24:MI') ngaynhapvien,
                     TO_CHAR(nt.ngaynhapvien, 'dd/MM/yyyy HH24:MI') ngaygio_nhapvien,
                     nt.icd_nhapvien       icd_nhapvien,
                     pban.ten_phongban     tenkhoa_nhapvienvaokhoa,
                     nt.stt_benhan,
                     vba.mabenhnhan        ma_benh_nhan,
                     dt.stt_dotdieutri,
                     signkcb_bn.keysign    nguoibenh_kyten,
                     signkcb_kt.keysign    ketoan_kyten,
                     signkcb_lap.keysign   nguoilap_kyten,
                     nt.bant,
                     CASE
                         WHEN nt.emr = 1 THEN
                             'Toàn trình'
                         ELSE
                             ''
                     END toantrinh,
                     CASE
                         WHEN (vba.da_ky_so_nhan = '1') THEN
                            'Đã lưu trữ'
                         WHEN (vba.da_ky_so_giao = '1') THEN
                            'Đã giao hồ sơ'
                         ELSE
                            ''
                     END dakygiaonhan,
                     nt.trang_thai,
                     CASE
                         WHEN grvct.sovaovien IS NOT NULL THEN
                             CASE
                                 WHEN grvct.loaigiay = 'GRV' THEN
                                     CASE
                                         WHEN grvct.matruongkhoa IS NULL THEN
                                             '1'
                                         WHEN grvct.matruongkhoa IS NOT NULL
                                              AND grvct.mabgd IS NULL THEN
                                             '2'
                                         ELSE
                                             '3'
                                     END
                                 ELSE
                                     CASE
                                         WHEN grvct.mabgd IS NULL THEN
                                             '4'
                                         ELSE
                                             '5'
                                     END
                             END
                         ELSE
                             '0'
                     END grv,
                     gct.sovaovien         giaychungtu
                 FROM
                     noitru_vobenhan                 vba
                     JOIN his_public_list.dm_benh_nhan    bn ON vba.mabenhnhan = bn.ma_benh_nhan
                     JOIN his_manager.noitru_dotdieutri   dt ON vba.dvtt = dt.dvtt
                                                              AND vba.sovaovien = dt.sovaovien
                                                              AND vba.mabenhnhan = dt.mabenhnhan
                     JOIN his_manager.noitru_benhan       nt ON vba.mabenhnhan = nt.mabenhnhan
                                                          AND vba.dvtt = p_dvtt
                                                          AND vba.sovaovien = nt.sovaovien
                     LEFT JOIN cmu_grvct                       grvct ON grvct.dvtt = dt.dvtt
                                                  AND grvct.sovaovien = dt.sovaovien
                     LEFT JOIN cmu_giay_chung_tu               gct ON gct.dvtt = dt.dvtt
                                                        AND gct.sovaovien = dt.sovaovien
                     LEFT JOIN smartca_signed_kcb              signkcb_bn ON vba.dvtt = signkcb_bn.dvtt
                                                                AND vba.sovaovien = signkcb_bn.sovaovien
                                                                AND signkcb_bn.so_phieu_dv = vba.mabenhan
                                                                AND signkcb_bn.status IN (
                         '0',
                         '2'
                     )
                                                                AND signkcb_bn.ky_hieu_phieu IN (
                         'PHIEU_NOITRU_BANGKE_BENHNHAN'
                     )
                     LEFT JOIN smartca_signed_kcb              signkcb_kt ON vba.dvtt = signkcb_kt.dvtt
                                                                AND vba.sovaovien = signkcb_kt.sovaovien
                                                                AND signkcb_kt.so_phieu_dv = vba.mabenhan
                                                                AND signkcb_kt.status = 0
                                                                AND signkcb_kt.ky_hieu_phieu IN (
                         'PHIEU_NOITRU_BANGKE_KETOAN'
                     )
                     LEFT JOIN smartca_signed_kcb              signkcb_lap ON vba.dvtt = signkcb_lap.dvtt
                                                                 AND vba.sovaovien = signkcb_lap.sovaovien
                                                                 AND signkcb_lap.so_phieu_dv = vba.mabenhan
                                                                 AND signkcb_lap.status = 0
                                                                 AND signkcb_lap.ky_hieu_phieu IN (
                         'PHIEU_NOITRU_BANGKE_NGUOILAP'
                     )
                     LEFT JOIN his_fw.dm_phongban              pban ON dt.khoa_ketthucdotdieutri = pban.ma_phongban
                                                          AND pban.ma_donvi = p_dvtt
                 WHERE
                     vba.dvtt = p_dvtt
                     AND vba.id_vba IS NOT NULL
                     AND ( dt.KHOA_KETTHUCDOTDIEUTRI = p_khoa
                           OR p_khoa = - 1 )
                     AND nt.dvtt = p_dvtt
                     AND nt.ngayravien BETWEEN v_ngayravien AND v_denngayravien
                     AND ( ( p_mabenh_nhan IS NULL
                             OR ( p_mabenh_nhan != ' '
                                  AND vba.mabenhnhan = p_mabenh_nhan ) )
                           AND ( p_mabenhan IS NULL
                                 OR ( p_mabenhan != ' '
                                      AND vba.mabenhan = p_mabenhan ) ) )
                 GROUP BY
                     vba.sovaovien,
                     dt.sovaovien_dt,
                     vba.mabenhan,
                     bn.ten_benh_nhan,
                     bn.tuoi,
                     bn.ngay_sinh,
                     bn.dia_chi,
                     nt.ngaynhapvien,
                     nt.icd_nhapvien,
                     pban.ten_phongban,
                     nt.stt_benhan,
                     vba.mabenhnhan,
                     dt.stt_dotdieutri,
                     signkcb_bn.keysign,
                     signkcb_kt.keysign,
                     signkcb_lap.keysign,
                     nt.bant,
                     nt.trang_thai,
                     nt.emr,
                     CASE
                             WHEN grvct.sovaovien IS NOT NULL THEN
                                 CASE
                                     WHEN grvct.loaigiay = 'GRV' THEN
                                         CASE
                                             WHEN grvct.matruongkhoa IS NULL THEN
                                                 '1'
                                             WHEN grvct.matruongkhoa IS NOT NULL
                                                  AND grvct.mabgd IS NULL THEN
                                                 '2'
                                             ELSE
                                                 '3'
                                         END
                                     ELSE
                                         CASE
                                             WHEN grvct.mabgd IS NULL THEN
                                                 '4'
                                             ELSE
                                                 '5'
                                         END
                                 END
                             ELSE
                                 '0'
                         END,
                     gct.sovaovien,
                     vba.da_ky_so_giao,
                     vba.da_ky_so_nhan;

    RETURN cur;
END;