create or replace FUNCTION       HIS_MANAGER."CMU_TTTTBENHNANGXINVE_INSERT" (
    p_dvtt                IN VARCHAR2,
    p_so_vao_vien         IN NUMBER,
    p_ma_<PERSON><PERSON>_<PERSON>han        IN NUMBER,
    p_thongtin           IN VARCHAR2,
    p_nntuvong           IN VARCHAR2,
    p_<PERSON><PERSON><PERSON><PERSON>           IN VARCHAR2,
    p_pha<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>           IN VARCHAR2,
    p_hinhthuctv           IN VARCHAR2,
    p_nnbenngo<PERSON>           IN VARCHAR2,
    p_tuvongthainhi           IN VARCHAR2,
    p_phunu           IN VARCHAR2,
    p_ketluan           IN VARCHAR2,
    p_bacsi           IN VARCHAR2,
    p_truongkhoa           IN VARCHAR2,
    p_thutruong           IN VARCHAR2,
    p_ngaytao           IN VARCHAR2,
    p_nguoi_tao           IN VARCHAR2,
    p_makhoa           IN VARCHAR2
)
return number IS
    v_id CMU_TTTTBENHNANGXINVE.id%TYPE;
    v_ngaytao   DATE := TO_DATE(p_ngaytao, 'dd/mm/yyyy');
B<PERSON>IN
INSERT INTO CMU_TTTTBENHNANGXINVE
(
    <PERSON>V<PERSON>,
    SOVAOVIEN,
    MA_BENH_NHAN,
    THONGTINBENHJSON,
    NNTUVONGJSON,
    BENHKHACJSON,
    PHAUTHUAT4TUANJSON,
    HINHTHUCTV,
    NNBENNGOAIJSON,
    TUVONGTHAINHIJSON,
    PHUNUJSON,
    KETLUANJSON,
    BSDIEUTRI,
    TRUONGKHOA,
    THUTRUONG,
    NGAY_TAO_PHIEU,
    NGUOI_TAO,
    MAKHOA
)
VALUES
    (
        p_dvtt,
        p_so_vao_vien,
        p_ma_benh_nhan,
        p_thongtin,
        p_nntuvong,
        p_benhkhac,
        p_phauthuat4tuan,
        p_hinhthuctv,
        p_nnbenngoai,
        p_tuvongthainhi,
        p_phunu,
        p_ketluan,
        p_bacsi,
        p_truongkhoa,
        p_thutruong,
        v_ngaytao,
        p_nguoi_tao,
        p_makhoa
    )
    RETURNING id INTO v_id;

RETURN v_id;
END;