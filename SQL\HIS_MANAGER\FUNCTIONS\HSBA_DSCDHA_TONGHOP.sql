CREATE OR REPLACE FUNCTION "HSBA_DSCDHA_TONGHOP" (
    p_dvtt         IN             VARCHAR2,
    p_stt_benhan   IN             VARCHAR2,
    p_ma<PERSON><PERSON>han   IN             VARCHAR2
) RETURN SYS_REFCURSOR IS
    cur              SYS_REFCURSOR;
    v_thamso820894   NUMBER(1) DEFAULT '0';
    v_sovaovien      NUMBER;
BEGIN
DELETE FROM noitru_dieutri_hsba_temp
WHERE
    dvtt = p_dvtt
  AND stt_benhan = p_stt_benhan;

INSERT INTO noitru_dieutri_hsba_temp (
    dvtt,
    stt_dieutri,
    stt_dotdieutri,
    ma<PERSON><PERSON>han,
    sovaovien,
    sovaovien_dt,
    id_dieutri,
    stt_benhan,
    ngaygiolap_tdt,
    tdt_nguoilap,
    tdt_dienbienbenh,
    tdt_ylenh,
    icd_dieutri,
    tenicd_dieutri,
    ten_ben<PERSON>hu,
    bmi,
    mach,
    nhiet<PERSON>,
    huyeta<PERSON><PERSON>,
    huyeta<PERSON><PERSON><PERSON>,
    chieu<PERSON><PERSON>,
    nhiptho,
    cannang,
    nguoitao
)
SELECT
    dvtt,
    stt_dieutri,
    stt_dotdieutri,
    ma<PERSON><PERSON><PERSON>,
    sovaovien,
    sovaovien_dt,
    id_dieutri,
    stt_benhan,
    ngaygiolap_tdt,
    tdt_nguoilap,
    tdt_dienbienbenh,
    tdt_ylenh,
    icd_dieutri,
    tenicd_dieutri,
    ten_benhphu,
    bmi,
    mach,
    nhietdo,
    huyetaptren,
    huyetapduoi,
    chieucao,
    nhiptho,
    cannang,
    nguoitao
FROM
    noitru_dieutri
WHERE
    dvtt = p_dvtt
  AND stt_benhan = p_stt_benhan;

OPEN cur FOR SELECT
                    a.ngay_chi_dinh,
                    a.stt_dieutri,
                    a.stt_benhan,
                    a.stt_dotdieutri,
                    a.bhytkchi,
                    a.ten_cdha,
                    a.so_luong,
                    a.don_gia,
                    a.thanh_tien,
                    a.ket_qua,
                    chd.mota_chucdanh
                    || '.'
                    || nv.ten_nhanvien AS nguoi_chi_dinh,
                    a.mabenhnhan,
                    a.ma_cdha,
                    a.sovaovien,
                    a.sovaovien_dt,
                    a.mota_loai_cdha,
                    a.bac_si_dieu_tri,
                    a.so_phieu_cdha,
                    a.da_chan_doan,
                    a.keysign,
                    a.keysignconfirm,
                    a.keysign_kyxem,
                    a.sophieu_ngoaitru,
                    a.mota_text,
                    a.ngaygiochidinh,
                    a.ngay_tra_ketqua,
                    a.gio_tra_ketqua,
                    a.khoa_chi_dinh,
                    a.chieucao,
                    a.cannang,
                    a.tenkhoa,
                    a.ngaychidinh
                FROM
                    (
                        SELECT
                            TO_CHAR(cd.ngay_chi_dinh, 'DD/MM/YYYY') AS ngay_chi_dinh,
                            TO_CHAR(cd.ngay_chi_dinh, 'DD/MM/YYYY HH24:MI') AS ngaygiochidinh,
                            TO_CHAR(cd.ngay_tra_ketqua, 'DD/MM/YYYY') AS ngay_tra_ketqua,
                            TO_CHAR(cd.ngay_tra_ketqua, 'HH24:MI:SS') AS gio_tra_ketqua,
                            cd.phong_chi_dinh     AS khoa_chi_dinh,
                            dt.chieucao,
                            dt.cannang,
                            khoa.ten_phongban     tenkhoa,
                            cd.stt_dieutri        AS stt_dieutri,
                            cd.stt_benhan         AS stt_benhan,
                            cd.stt_dotdieutri     AS stt_dotdieutri,
                            CASE ct.bhytkchi
                                WHEN 0 THEN
                                    '0'
                                ELSE
                                    '1'
                            END AS bhytkchi,
                            dm.ten_cdha           AS ten_cdha,
                            so_luong              AS so_luong,
                            don_gia               AS don_gia,
                            thanh_tien            AS thanh_tien,
                            nvl(ket_qua, '')
                            ||
                                CASE
                                    WHEN ct.mo_ta IS NULL THEN
                                        ''
                                    ELSE
                                        chr('10')
                                        || 'KẾT LUẬN: '
                                        || ct.mo_ta
                                END
                            AS ket_qua,
                            CASE
                                WHEN v_thamso820894 = 1 THEN
                                    cd.nguoi_chi_dinh
                                ELSE
                                    cd.bac_si_dieu_tri
                            END AS nguoi_chi_dinh,
                            cd.mabenhnhan         AS mabenhnhan,
                            ct.ma_cdha            AS ma_cdha,
                            cd.sovaovien          AS sovaovien,
                            cd.sovaovien_dt       AS sovaovien_dt,
                            loai.mota_loai_cdha   AS mota_loai_cdha,
                            l.mota_chucdanh
                            || '.'
                            || k.ten_nhanvien AS bac_si_dieu_tri,
                            cd.so_phieu_cdha,
                            ct.da_chan_doan,
                            signkcb.keysign,
                            signkcbcf.keysign     keysignconfirm,
                            signkcbkx.keysign     keysign_kyxem,
                            ct.sophieu_ngoaitru,
                            loai.mota_text,
                            cd.ngay_chi_dinh      ngaychidinh
                        FROM
                            noitru_dieutri_hsba_temp      dt
                            INNER JOIN noitru_cd_cdha                cd ON dt.dvtt = cd.dvtt
                                                            AND dt.sovaovien = cd.sovaovien
                                                            AND dt.id_dieutri = cd.id_dieutri
                            INNER JOIN dm_phong_benh                 phong ON cd.phong_chi_dinh = phong.ma_phong_benh
                            INNER JOIN his_fw.dm_phongban            khoa ON phong.ma_phong_ban = khoa.ma_phongban
                            INNER JOIN noitru_cd_cdha_chi_tiet       ct ON cd.dvtt = ct.dvtt
                                                                     AND cd.sovaovien = ct.sovaovien
                                                                     AND cd.so_phieu_cdha = ct.so_phieu_cdha
                                                                     AND cd.sovaovien_dt = ct.sovaovien_dt
                            LEFT JOIN his_fw.dm_nhanvien            k ON dt.tdt_nguoilap = k.ma_nhanvien
                            LEFT JOIN his_fw.dm_chucdanh_nhanvien   l ON l.ma_chucdanh = k.chucdanh_nhanvien
                            LEFT JOIN smartca_signed_kcb            signkcb ON ct.dvtt = signkcb.dvtt
                                                                    AND ct.sovaovien = signkcb.sovaovien
                                                                    AND signkcb.so_phieu_dv = ct.so_phieu_cdha
                                                                    AND signkcb.ma_dich_vu = ct.ma_cdha
                                                                    AND signkcb.status = 0
                                                                    AND signkcb.ky_hieu_phieu IN (
                                'PHIEUKQ_CT_XQUANG_MRI',
                                'PHIEUKQ_DIENTIM'
                            )
                            LEFT JOIN smartca_signed_kcb            signkcbkx ON ct.dvtt = signkcbkx.dvtt
                                                                      AND ct.sovaovien = signkcbkx.sovaovien
                                                                      AND signkcbkx.so_phieu_dv = ct.so_phieu_cdha
                                                                      AND signkcbkx.ma_dich_vu = ct.ma_cdha
                                                                      AND signkcbkx.status = 0
                                                                      AND signkcbkx.ky_hieu_phieu IN (
                                'PHIEUKQ_CDHA_KYXEM'
                            )
                            LEFT JOIN smartca_signed_kcb            signkcbcf ON ct.dvtt = signkcb.dvtt
                                                                      AND ct.sovaovien = signkcb.sovaovien
                                                                      AND signkcb.so_phieu_dv = ct.so_phieu_cdha
                                                                      AND signkcb.ma_dich_vu = ct.ma_cdha
                                                                      AND signkcb.status = 0
                                                                      AND signkcb.ky_hieu_phieu IN (
                                'PHIEUKQ_CT_XQUANG_MRI_CONFIRM',
                                'PHIEUKQ_DIEMTIM_CONFIRM'
                            ),
                            cls_cdha                      dm,
                            cls_loaicdha                  loai,
                            his_fw.dm_nhanvien            dmnv
                        WHERE
                            cd.dvtt = ct.dvtt
                            AND cd.bac_si_dieu_tri = dmnv.ma_nhanvien
                            AND cd.stt_benhan = p_stt_benhan
                            AND ct.dvtt = p_dvtt
                            AND dt.dvtt = p_dvtt
                            AND dt.stt_benhan = p_stt_benhan
                            AND dm.dvtt = ct.dvtt
                            AND ct.ma_cdha = dm.ma_cdha
                            AND dm.dvtt = loai.dvtt
                            AND dm.ma_loai_cdha = loai.ma_loai_cdha
                            AND ct.mabenhnhan = p_mabenhnhan
                    ) a
                    LEFT JOIN his_fw.dm_nhanvien            nv ON a.nguoi_chi_dinh = nv.ma_nhanvien
                    LEFT JOIN his_fw.dm_chucdanh_nhanvien   chd ON nv.chucdanh_nhanvien = chd.ma_chucdanh;

RETURN cur;
END;