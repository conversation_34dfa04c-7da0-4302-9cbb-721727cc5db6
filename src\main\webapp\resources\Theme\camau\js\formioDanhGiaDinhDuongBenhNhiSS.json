{"display": "form", "components": [{"label": "MA_PHIEU", "key": "MA_PHIEU", "type": "hidden", "input": true}, {"label": "Columns", "columns": [{"components": [{"label": "<PERSON><PERSON><PERSON> (Điều dưỡng)", "customClass": "mr-2 formio-css-datetime formio-css-suffix luulog", "key": "NGAY_KY_DD", "type": "datetime", "format": "dd/MM/yyyy HH:mm", "timePicker": {"showMeridian": false}, "widget": {"enableTime": true, "format": "dd/MM/yyyy HH:mm", "time_24hr": true}}], "width": 2, "size": "md", "currentWidth": 2}, {"components": [{"label": "<PERSON><PERSON><PERSON><PERSON> dưỡng", "widget": "<PERSON><PERSON><PERSON>", "customClass": "mr-2 luulog", "tableView": true, "dataSrc": "json", "template": "<span>{{ item.tennhanvien }}</span>", "validateWhenHidden": false, "key": "DIEU_DUONG", "type": "select", "input": true}], "width": 4, "size": "md", "currentWidth": 4}, {"components": [{"label": "<PERSON><PERSON><PERSON> (<PERSON><PERSON><PERSON>)", "customClass": "mr-2 formio-css-datetime formio-css-suffix luulog", "key": "NGAY_KY_BS", "type": "datetime", "format": "dd/MM/yyyy HH:mm", "timePicker": {"showMeridian": false}, "widget": {"enableTime": true, "format": "dd/MM/yyyy HH:mm", "time_24hr": true}}], "width": 2, "size": "md", "currentWidth": 2}, {"components": [{"label": "<PERSON><PERSON><PERSON>", "widget": "<PERSON><PERSON><PERSON>", "customClass": "lu<PERSON><PERSON>", "tableView": true, "dataSrc": "json", "template": "<span>{{ item.tennhanvien }}</span>", "validateWhenHidden": false, "key": "BAC_SI_DANH_GIA", "type": "select", "input": true}], "width": 4, "size": "md", "currentWidth": 4}], "key": "columns", "type": "columns", "customClass": "ml-0 mr-0"}, {"label": "Columns", "columns": [{"components": [{"label": "ICD", "customClass": "mr-2 luulog", "tableView": true, "validateWhenHidden": false, "key": "ICD", "type": "textfield", "input": true}], "width": 2, "size": "md", "currentWidth": 2}, {"components": [{"label": "<PERSON><PERSON><PERSON> đ<PERSON>", "customClass": "lu<PERSON><PERSON>", "tableView": true, "validateWhenHidden": false, "key": "CHAN_DOAN", "type": "textfield", "input": true}], "width": 10, "size": "md", "currentWidth": 10}], "key": "columns", "type": "columns", "customClass": "ml-0 mr-0"}, {"label": "Columns", "columns": [{"components": [{"label": "<PERSON><PERSON><PERSON> thai", "customClass": "mr-2 formio-css-suffix luulog", "suffix": "tuần", "tableView": true, "validateWhenHidden": false, "key": "TUOITHAI", "type": "number", "input": true}], "width": 2, "size": "md", "currentWidth": 2}, {"components": [{"label": "<PERSON>ân nặng lúc sinh", "customClass": "mr-2 formio-css-suffix luulog", "suffix": "kg", "tableView": true, "validateWhenHidden": false, "key": "CANNANG_LUCSINH", "type": "number", "input": true}], "width": 3, "size": "md", "currentWidth": 3}, {"components": [{"label": "<PERSON><PERSON> nặng hiện tại", "customClass": "mr-2 formio-css-suffix luulog", "suffix": "kg", "tableView": true, "validateWhenHidden": false, "key": "CANNANG_HIENTAI", "type": "number", "input": true}], "width": 3, "size": "md", "currentWidth": 3}, {"components": [{"label": "<PERSON><PERSON><PERSON> dài", "customClass": "mr-2 formio-css-suffix luulog", "suffix": "cm", "tableView": true, "validateWhenHidden": false, "key": "CHIEUDAI", "type": "number", "input": true}], "width": 2, "size": "md", "currentWidth": 2}, {"components": [{"label": "<PERSON><PERSON><PERSON> đầu", "customClass": "formio-css-suffix luulog", "suffix": "cm", "tableView": true, "validateWhenHidden": false, "key": "VONGDAU", "type": "number", "input": true}], "width": 2, "size": "md", "currentWidth": 2}], "key": "columns", "type": "columns", "customClass": "ml-0 mr-0"}, {"label": "Columns", "columns": [{"components": [{"label": "<PERSON><PERSON> nặng chuẩn so với tuổi", "customClass": "mr-2 formio-css-suffix luulog", "suffix": "SD", "tooltip": "(<PERSON> dẫn của Tổ chức Y thế giới)", "tableView": true, "validateWhenHidden": false, "key": "CN_CHUAN_SOVS_TUOI", "type": "number", "input": true}], "width": 4, "size": "md", "currentWidth": 4}, {"components": [{"label": "<PERSON><PERSON><PERSON> cao chuẩn so với tuổi", "customClass": "mr-2 formio-css-suffix luulog", "suffix": "SD", "tooltip": "(<PERSON> dẫn của Tổ chức Y thế giới)", "tableView": true, "validateWhenHidden": false, "key": "CC_CHUAN_SOVS_TUOI", "type": "number", "input": true}], "width": 4, "size": "md", "currentWidth": 4}, {"components": [{"label": "V<PERSON><PERSON> đầu ch<PERSON> so với tuổi", "customClass": "formio-css-suffix luulog", "suffix": "cm", "tooltip": "(<PERSON> dẫn của Tổ chức Y thế giới)", "tableView": true, "validateWhenHidden": false, "key": "VD_CHUAN_SOVS_TUOI", "type": "number", "input": true}], "width": 4, "size": "md", "currentWidth": 4}], "key": "columns", "type": "columns", "customClass": "ml-0 mr-0"}, {"label": "Titles", "customClass": "formio-css-p", "type": "htmlelement", "tag": "p", "content": "<b>Phần I: <PERSON><PERSON><PERSON> lọc nguy cơ suy dinh dưỡng (SDD) (<PERSON><PERSON>ều dưỡng thực hiện)</b>", "input": false}, {"label": "Columns", "columns": [{"components": [{"label": "Titles", "customClass": "formio-css-p", "type": "htmlelement", "tag": "p", "content": "<b><PERSON><PERSON><PERSON> tố nguy c<PERSON></b>", "input": false}], "width": 4, "size": "md", "currentWidth": 4}, {"components": [{"label": "Titles", "customClass": "formio-css-p", "type": "htmlelement", "tag": "p", "content": "<b><PERSON><PERSON><PERSON><PERSON></b>", "input": false}], "width": 2, "size": "md", "currentWidth": 2}, {"components": [{"label": "Titles", "customClass": "formio-css-p", "type": "htmlelement", "tag": "p", "content": "<b><PERSON><PERSON><PERSON> tố nguy c<PERSON></b>", "input": false}], "width": 4, "size": "md", "currentWidth": 4}, {"components": [{"label": "Titles", "customClass": "formio-css-p", "type": "htmlelement", "tag": "p", "content": "<b><PERSON><PERSON><PERSON><PERSON></b>", "input": false}], "width": 2, "size": "md", "currentWidth": 2}], "key": "columns", "type": "columns", "customClass": "ml-0 mr-0"}, {"label": "Columns", "columns": [{"components": [{"label": "Titles", "customClass": "mr-2", "type": "htmlelement", "tag": "p", "content": "Ng<PERSON><PERSON><PERSON> bệnh (NB) có bệnh lý nền gây nguy cơ SDD  hoặc dự kiến phẫu thuật? (2 điểm)", "input": false}], "width": 4, "size": "md", "currentWidth": 4}, {"components": [{"label": "<PERSON><PERSON><PERSON> tố nguy cơ 1", "hideLabel": true, "widget": "<PERSON><PERSON><PERSON>", "customClass": "mr-2 formio-css-selection luulog", "tableView": true, "data": {"values": [{"label": "K<PERSON>ô<PERSON>", "value": "1"}, {"label": "<PERSON><PERSON>", "value": "2"}]}, "validateWhenHidden": false, "key": "TINHDIEM1_2", "type": "select", "input": true}], "width": 2, "size": "md", "currentWidth": 2}, {"components": [{"label": "Titles", "customClass": "mr-2", "type": "htmlelement", "tag": "p", "content": "<PERSON><PERSON><PERSON><PERSON> lâm sàng có mất lớp mỡ dưới da và/hoặc teo cơ… (1 điểm)", "input": false}], "width": 4, "size": "md", "currentWidth": 4}, {"components": [{"label": "<PERSON><PERSON><PERSON> tố nguy c<PERSON> 2", "hideLabel": true, "widget": "<PERSON><PERSON><PERSON>", "customClass": "formio-css-selection luulog", "tableView": true, "data": {"values": [{"label": "K<PERSON>ô<PERSON>", "value": "1"}, {"label": "<PERSON><PERSON>", "value": "2"}]}, "validateWhenHidden": false, "key": "TINHDIEM2_1", "type": "select", "input": true}], "width": 2, "size": "md", "currentWidth": 2}], "key": "columns", "type": "columns", "customClass": "ml-0 mr-0"}, {"label": "Columns", "columns": [{"components": [{"label": "Titles", "customClass": "mr-2", "type": "htmlelement", "tag": "p", "content": "<PERSON>hi có ít nhất 01 trong các yếu tố sau:<br/>- Ti<PERSON><PERSON> chả<PERSON> (> 5 ngày) và/hoặc nôn (> 3 lần/ngày) kéo dài 1-3 ngày.<br/>- <PERSON><PERSON> giảm kéo dài 1 - 3 ngày.<br/>- <PERSON><PERSON> can thiệp DD trước đó (như qua ống thông/ qua tĩnh mạch).<br/>- Không thể thu nạp đủ dinh dưỡng do kém tiêu hóa/hấp thu hoặc nguyên nhân khác. (1 điểm)", "input": false}], "width": 4, "size": "md", "currentWidth": 4}, {"components": [{"label": "<PERSON><PERSON><PERSON> tố nguy c<PERSON> 3", "hideLabel": true, "widget": "<PERSON><PERSON><PERSON>", "customClass": "mr-2 formio-css-selection luulog", "tableView": true, "data": {"values": [{"label": "K<PERSON>ô<PERSON>", "value": "1"}, {"label": "<PERSON><PERSON>", "value": "2"}]}, "validateWhenHidden": false, "key": "TINHDIEM3_1", "type": "select", "input": true}], "width": 2, "size": "md", "currentWidth": 2}, {"components": [{"label": "Titles", "customClass": "mr-2", "type": "htmlelement", "tag": "p", "content": "<PERSON><PERSON><PERSON><PERSON> tăng cân trong vòng 1 tuần/1 tháng gần đây. (1 điểm)", "input": false}], "width": 4, "size": "md", "currentWidth": 4}, {"components": [{"label": "<PERSON><PERSON><PERSON> tố nguy c<PERSON> 4", "hideLabel": true, "widget": "<PERSON><PERSON><PERSON>", "customClass": "formio-css-selection luulog", "tableView": true, "data": {"values": [{"label": "K<PERSON>ô<PERSON>", "value": "1"}, {"label": "<PERSON><PERSON>", "value": "2"}]}, "validateWhenHidden": false, "key": "TINHDIEM4_1", "type": "select", "input": true}], "width": 2, "size": "md", "currentWidth": 2}], "key": "columns", "type": "columns", "customClass": "ml-0 mr-0"}, {"label": "Columns", "columns": [{"components": [{"label": "<PERSON><PERSON><PERSON> điểm", "labelPosition": "left-left", "customClass": "lu<PERSON><PERSON>", "tableView": true, "validateWhenHidden": false, "key": "TONG_DIEM", "type": "textfield", "input": true, "disabled": true}], "width": 3, "size": "md", "currentWidth": 3}], "key": "columns", "type": "columns", "customClass": "ml-0 mr-0"}, {"label": "Titles", "customClass": "formio-css-p", "type": "htmlelement", "tag": "p", "content": "<b>Phần II:  <PERSON><PERSON><PERSON> gi<PERSON> tăng trưởng</b> (<PERSON>ầ<PERSON> dành cho <PERSON>ỹ)<br/><b>Phần III: <PERSON><PERSON><PERSON> nhận kế hoạch chăm sóc DD</b> (Phần dành cho <PERSON><PERSON> sỹ)", "input": false}, {"label": "Columns", "columns": [{"components": [{"label": "Phần III: <PERSON><PERSON><PERSON>n kế hoạch chăm sóc <PERSON>", "hideLabel": true, "widget": "<PERSON><PERSON><PERSON>", "customClass": "mr-2 formio-css-selection luulog", "tableView": true, "data": {"values": [{"label": "<PERSON><PERSON> sung DD qua miệng", "value": "1"}, {"label": "<PERSON><PERSON> độ DD qua ống thông", "value": "2"}, {"label": "<PERSON><PERSON> độ DD qua tĩnh mạch toàn phần", "value": "3"}, {"label": "<PERSON><PERSON> độ DD qua tĩnh mạch bổ sung", "value": "4"}, {"label": "<PERSON><PERSON><PERSON>", "value": "5"}]}, "validateWhenHidden": false, "key": "KEHOACH_CS_DD", "type": "select", "input": true}], "width": 6, "size": "md", "currentWidth": 6}], "key": "columns", "type": "columns", "customClass": "ml-0 mr-0"}]}