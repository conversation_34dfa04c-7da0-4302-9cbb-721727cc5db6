async function processPage(i) {
    var url = String.format('print-vobenhan-by-id?iD={0}&loaiBenhAn={1}&soVaoVien={2}&soVaoVienDt={3}&smartcafiletype={4}&pageToPrint={5}',
        thongtinhsba.thongtinbn.VOBENHAN[0].ID, thongtinhsba.thongtinbn.VOBENHAN[0].ID_VBA,
        thongtinhsba.thongtinbn.SOVAOVIEN,
        thongtinhsba.thongtinbn.SOVAOVIEN_DT, 'pdf', i);
    if (i == 3) {
        $(".btn-huykyso-trang3").hide();
        $(".btn-kyso-trang3").hide();
        var arrTemp = [];
        let dataKySo;
        dataKySo = await getFilesign769Async("PHIEU_NOITRU_VBATRANG3", thongtinhsba.thongtinbn.VOBENHAN[0].ID, -1, singletonObject.dvtt,
            thongtinhsba.thongtinbn.SOVAOVIEN, thongtinhsba.thongtinbn.SOVAOVIEN_DT, -1);
        if (dataKySo.length > 0) {
            arrTemp.push({
                "loai": "1",
                ...dataKySo[0]
            })
        }
        dataKySo = await getFilesign769Async("PHIEU_NOITRU_VBATRANG3_NGUOIGIAO", thongtinhsba.thongtinbn.VOBENHAN[0].ID, -1, singletonObject.dvtt,
            thongtinhsba.thongtinbn.SOVAOVIEN, thongtinhsba.thongtinbn.SOVAOVIEN_DT, -1);
        if (dataKySo.length > 0) {
            arrTemp.push({
                "loai": "2",
                ...dataKySo[0]
            })
        }
        dataKySo = await getFilesign769Async("PHIEU_NOITRU_VBATRANG3_NGUOINHAN", thongtinhsba.thongtinbn.VOBENHAN[0].ID, -1, singletonObject.dvtt,
            thongtinhsba.thongtinbn.SOVAOVIEN, thongtinhsba.thongtinbn.SOVAOVIEN_DT, -1);
        if (dataKySo.length > 0) {
            arrTemp.push({
                "loai": "3",
                ...dataKySo[0]
            })
        }
        var maxCreateDate = null;
        var maxCreateDataObject = null;
        var arrLoai = [];
        if(arrTemp.length > 0) {
            $.each(arrTemp, function(index, dataObject) {
                var createDate = moment(dataObject.CREATE_DATE_STRING, "DD/MM/YYYY HH:mm:ss");
                if (maxCreateDate === null || createDate.isAfter(maxCreateDate)) {
                    maxCreateDate = createDate;
                    maxCreateDataObject = dataObject;
                }
                arrLoai.push(dataObject.loai);
            });
            try {
                const pdfData = await getCMUFileSigned769GetLink(maxCreateDataObject.KEYMINIO, 'pdf');
                url = pdfData;
                if (maxCreateDataObject.loai == "1") {
                    $(".btn-kyso-trang3").show();
                } else {
                    if (maxCreateDataObject.loai == "2") {
                        $("#hsba-huykyso-nguoigiao").show();
                        if (arrLoai.indexOf("3") == -1){
                            $("#hsba-kyso-nguoinhan").show();
                        }
                    } else if (maxCreateDataObject.loai == "3") {
                        $("#hsba-huykyso-nguoinhan").show();
                        if (arrLoai.indexOf("2") == -1){
                            $("#hsba-kyso-nguoigiao").show();
                        }
                    }
                }
                thongtinhsba.thongtinbn.linkVBA.push({
                    "url": [url],
                    "name": "Vỏ bệnh án trang 3",
                    "key": "VBA",
                    "keyEMR": thongtinhsba.thongtinbn.VOBENHAN[0].ID_VBA,
                    "idPhieu": maxCreateDataObject.ID,
                });
            } catch (error) {
                thongtinhsba.thongtinbn.linkVBA.push({
                    "url": [url],
                    "name": "Vỏ bệnh án trang 3",
                    "key": "VBA"
                });
            }
        } else {
            $(".btn-kyso-trang3").show();
            thongtinhsba.thongtinbn.linkVBA.push({
                "url": [url],
                "name": "Vỏ bệnh án trang 3",
                "key": "VBA"
            });
        }
    } else {
        let dataKySo = await getFilesign769Async(
            "PHIEU_NOITRU_VBATRANG" + i,
            thongtinhsba.thongtinbn.VOBENHAN[0].ID,
            -1,
            singletonObject.dvtt,
            thongtinhsba.thongtinbn.SOVAOVIEN,
            thongtinhsba.thongtinbn.SOVAOVIEN_DT,
            '-1'
        );

        if (dataKySo.length > 0) {
            var trang = i;
            if (trang == 1) {
                $("#hsba-kyso-truongkhoa").hide();
                $("#hsba-huykyso-truongkhoa").show();
            }
            try {
                const pdfData = await getCMUFileSigned769GetLink(dataKySo[0].KEYMINIO, 'pdf');
                url = pdfData;
                thongtinhsba.thongtinbn.linkVBA.push({
                    "url": [url],
                    "name": "Vỏ bệnh án trang " + trang,
                    "key": "VBA",
                    "keyEMR": thongtinhsba.thongtinbn.VOBENHAN[0].ID_VBA,
                    "idPhieu": dataKySo[0].ID,
                });
            } catch (error) {
                thongtinhsba.thongtinbn.linkVBA.push({
                    "url": [url],
                    "name": "Vỏ bệnh án trang " + trang,
                    "key": "VBA"
                });
            }
        } else {
            if (i == 1) {
                $("#hsba-kyso-truongkhoa").show();
                $("#hsba-huykyso-truongkhoa").hide();
            }
            thongtinhsba.thongtinbn.linkVBA.push({
                "url": [url],
                "name": "Vỏ bệnh án trang " + i,
                "key": "VBA"
            });
        }
    }
}

function xemVoBenhAn() {
    return new Promise(async (resolve, reject) => {
        thongtinhsba.thongtinbn.linkVBA = [];
        for (var i = 1; i <= 3; i++) {
            await processPage(i);
        }
        thongtinhsba.thongtinbn.linkVBA.length > 0 ? $("#hsba-ttba-tab").show() : $("#hsba-ttba-tab").hide();
        resolve();
    });
}

function xemBangKe(dsDotDieuTri) {
    thongtinhsba.thongtinbn.linkBangKe = [];
    dsDotDieuTri.forEach(function (itemDDT) {
        var params = {
            sovaovien: itemDDT.SOVAOVIEN,
            sovaovien_dt: itemDDT.SOVAOVIEN_DT,
            dvtt: singletonObject.dvtt,
            stt_benhan: itemDDT.STT_BENHAN,
            stt_dotdieutri: itemDDT.STT_DOTDIEUTRI
        };
        $.ajax({
            url: 'noitru_trangthaiketthuc_svv?' + $.param(params),
            type: 'POST',
            success: function (dt) {
                if (dt == "0" || dt == "1")
                    dt = "1";
                if (dt == "6")
                    dt = "3";
                var arr_bk = [singletonObject.dvtt,
                    itemDDT.STT_DOTDIEUTRI, itemDDT.STT_BENHAN, itemDDT.SOPHIEUTHANHTOAN,
                    itemDDT.MABENHNHAN, dt, itemDDT.SOVAOVIEN, itemDDT.SOVAOVIEN_DT];
                var url = 'noitru_inbangke?url=' + convertArray(arr_bk);
                var arrTemp = [];
                getFilesign769("PHIEU_NOITRU_BANGKE_BENHNHAN", itemDDT.SOBENHAN, -1, singletonObject.dvtt,
                    itemDDT.SOVAOVIEN, itemDDT.SOVAOVIEN_DT, -1, function(dataKySo) {
                        if (dataKySo.length > 0) {
                            arrTemp.push(dataKySo[0])
                        }
                    });
                getFilesign769("PHIEU_NOITRU_BANGKE_KETOAN", itemDDT.SOBENHAN, -1, singletonObject.dvtt,
                    itemDDT.SOVAOVIEN, itemDDT.SOVAOVIEN_DT, -1, function(dataKySo) {
                        if (dataKySo.length > 0) {
                            arrTemp.push(dataKySo[0])
                        }
                    });
                getFilesign769("PHIEU_NOITRU_BANGKE_NGUOILAP", itemDDT.SOBENHAN, -1, singletonObject.dvtt,
                    itemDDT.SOVAOVIEN, itemDDT.SOVAOVIEN_DT, -1, function(dataKySo) {
                        if (dataKySo.length > 0) {
                            arrTemp.push(dataKySo[0])
                        }
                    });
                var maxCreateDate = null;
                var maxCreateDataObject = null;
                if(arrTemp.length > 0) {
                    $.each(arrTemp, function(index, dataObject) {
                        var createDate = moment(dataObject.CREATE_DATE_STRING, "DD/MM/YYYY HH:mm:ss");
                        if (maxCreateDate === null || createDate.isAfter(maxCreateDate)) {
                            maxCreateDate = createDate;
                            maxCreateDataObject = dataObject;
                        }
                    });
                    getCMUFileSigned769GetLink(maxCreateDataObject.KEYMINIO, 'pdf').then(pdfData => {
                        url = pdfData;
                        thongtinhsba.thongtinbn.linkBangKe.push({
                            "url": [url],
                            "name": "Bảng kê - Đợt " + itemDDT.STT_DOTDIEUTRI,
                            "keyEMR": "BM_PHIEU_BANGKE_10",
                            "key": "BANGKE",
                            "idPhieu": maxCreateDataObject.ID,
                        });
                    }).catch(error => {
                        thongtinhsba.thongtinbn.linkBangKe.push({
                            "url": [url],
                            "name": "Bảng kê - Đợt " + itemDDT.STT_DOTDIEUTRI,
                            "key": "BANGKE"
                        });
                    });
                } else {
                    thongtinhsba.thongtinbn.linkBangKe.push({
                        "url": [url],
                        "name": "Bảng kê - Đợt " + itemDDT.STT_DOTDIEUTRI,
                        "key": "BANGKE"
                    });
                }
                thongtinhsba.thongtinbn.linkBangKe.length > 0 ? $("#hsba-bangke-tab").show() : $("#hsba-bangke-tab").hide();
            }
        });
    });
}

function xemToDieuTri(dsDotDieuTri) {
    thongtinhsba.thongtinbn.linkTDT = [];
    thongtinhsba.thongtinbn.linkTDTV2 = [];
    dsDotDieuTri.forEach(function (itemDDT) {
        var tenkhoa = "";
        singletonObject.danhsachphongban.forEach(function(obj) {
            if(obj.MAKHOA == singletonObject.makhoa) {
                tenkhoa = obj.TENKHOA;
            }
        })
        var arr = [singletonObject.dvtt,
            itemDDT.TEN_BENH_NHAN,
            0,
            itemDDT.SOVAOVIEN,
            itemDDT.SOVAOVIEN_DT,
            tenkhoa,
            itemDDT.TUOI,
            // itemDDT.TUOI > 0 ? itemDDT.TUOI : (itemDDT.THANG > 0?  (itemDDT.THANG+ " Tháng"): (itemDDT.NGAY + " Ngày")),
            thongtinhsba.thongtinbn.GIOI_TINH_NUM ? thongtinhsba.thongtinbn.GIOI_TINH : thongtinhsba.thongtinbn.GIOI_TINH_HT,
            itemDDT.SOBENHAN,
            '-1',
            '0',
            itemDDT.STT_BENHAN,
            "/WEB-INF/pages/camau/reports/rp_todieutri_tong_v2_sub.jasper"
        ];
        var param = ['dvtt', 'hovaten', 'id_dieutri', 'sovaovien', 'sovaovien_dt',
            "ten_phongban", "tuoi", "gioitinh", "sobenhan", "userid", "makhoa", "stt_benhan", "FILE_1"];
        var url = "cmu_injasper?url=" + convertArray(arr) + "&param="
            + convertArray(param) + "&loaifile=pdf&jasper=rp_todieutri_tong_v2";
        getFilesign769(
            "TODIEUTRI_NOITRU",
            itemDDT.ID_DIEUTRI,
            0,//singletonObject.userId,
            singletonObject.dvtt,
            itemDDT.SOVAOVIEN,
            itemDDT.SOVAOVIEN_DT ,
            -1,
            function(dataKyso) {
                if(dataKyso.length > 0) {
                    $.ajax({
                        method: "POST", url: "smartca-get-signed-file-minio?keyminio=" + dataKyso[0].KEYMINIO + "&type=pdf", contentType: "charset=utf-8"
                    }).done(function (data) {
                        url = 'data:application/pdf;base64,' +data.FILE;
                        thongtinhsba.thongtinbn.linkTDT.push({
                            "url": [url],
                            "name": "Tờ điều trị - Đợt " + itemDDT.STT_DOTDIEUTRI,
                            "keyEMR": "BM_PHIEU_PHIEUDIEUTRI_12",
                            "key": "TDT",
                            "idPhieu": dataKyso[0].ID,
                        });
                    }).fail(function() {
                        thongtinhsba.thongtinbn.linkTDT.push({
                            "url": [url],
                            "name": "Tờ điều trị - Đợt " + itemDDT.STT_DOTDIEUTRI,
                            "key": "TDT"
                        });
                    });
                } else {

                    thongtinhsba.thongtinbn.linkTDT.push({
                        "url": [url],
                        "name": "Tờ điều trị - Đợt " + itemDDT.STT_DOTDIEUTRI,
                        "key": "TDT"
                    });
                }
                thongtinhsba.thongtinbn.linkTDT.length > 0 ? $("#hsba-todieutri-tab").show() : $("#hsba-todieutri-tab").hide();
            })
    });
    $.get("cmu_getlist?url=" + convertArray([
        thongtinhsba.thongtinbn.STT_BENHAN,
        0, -1, singletonObject.dvtt, "HSBA_TODIEUTRI_CMU_SEL"]), function (data) {
        data.forEach(function (itemDDT) {
            getFilesign769(
                "TODIEUTRI_NOITRU",
                itemDDT.ID_DIEUTRI,
                itemDDT.TDT_NGUOILAP,
                singletonObject.dvtt,
                itemDDT.SOVAOVIEN,
                itemDDT.SOVAOVIEN_DT ,
                -1,
                function(dataKyso) {
                    if(dataKyso.length > 0) {
                        $.ajax({
                            method: "POST", url: "smartca-get-signed-file-minio?keyminio=" + dataKyso[0].KEYMINIO + "&type=pdf", contentType: "charset=utf-8"
                        }).done(function (data) {
                            thongtinhsba.thongtinbn.linkTDTV2.push({
                                "url": ['data:application/pdf;base64,' +data.FILE],
                                "name": "Tờ điều trị - Đợt " + itemDDT.STT_DOTDIEUTRI,
                                "keyEMR": "BM_PHIEU_PHIEUDIEUTRI_12",
                                "key": "TDT",
                                "idPhieu": dataKyso[0].ID,
                            });
                        });
                    }
                })
        })

    })

}

function xemPhieuChamSoc(dsDotDieuTri) {
    thongtinhsba.thongtinbn.linkPCS = [];
    var count = 0;
    dsDotDieuTri.forEach(function (itemDDT) {
        $.ajax({
            url: "cmu_list_cmu_list_phieuchamsoc?url=" + convertArray([singletonObject.dvtt, itemDDT.SOVAOVIEN]),
            type: 'GET',
            success: function (dataPCS) {
                if (dataPCS.length > 0) {
                    $("#hsba-phieuchamsoc-tab").show();
                    $("#wrap_Menu_PhieuKhac").show();
                    var tenkhoa = "";
                    singletonObject.danhsachphongban.forEach(function(obj) {
                        if(obj.MAKHOA == singletonObject.makhoa) {
                            tenkhoa = obj.TENKHOA;
                        }
                    })
                    var params = {
                        ID: '-1',
                        TUOI: thongtinhsba.thongtinbn.TUOI,
                        THANG: thongtinhsba.thongtinbn.THANG_SINH,
                        NGAY: thongtinhsba.thongtinbn.NGAY_NS,
                        TEN_PHONGBAN: tenkhoa,
                        CHAN_DOAN: thongtinhsba.thongtinbn.ICD_NHAPVIEN + "- " + (thongtinhsba.thongtinbn.TEN_BENH_CHINH || thongtinhsba.thongtinbn.TENBENHCHINH_NHAPVIEN),
                        GIOI_TINH_HT: thongtinhsba.thongtinbn.GIOI_TINH,
                        SOBENHAN: thongtinhsba.thongtinbn.SOBENHAN,
                    }
                    getUrlChamSoc(params).then(objReturn => {
                        if (objReturn.isError == 0) {
                            var objTemp = {
                                "url": [objReturn.url],
                                "name": "Phiếu chăm sóc - " + thongtinhsba.thongtinbn.TEN_BENH_NHAN,
                                "key": "PCS",
                            }
                            if (objReturn.kySo == 1) {
                                objTemp = {
                                    ...objTemp,
                                    "keyEMR": "BM_PHIEU_PHIEUCHAMSOC_13",
                                    "idPhieu": objReturn.idKySo,
                                }
                            }
                            thongtinhsba.thongtinbn.linkPCS.push(objTemp);
                        } else {
                            notifiToClient("Red", "Phiếu chăm sóc - " + thongtinhsba.thongtinbn.TEN_BENH_NHAN + ": " + objReturn.message);
                        }
                    }).catch(error => {
                        // notifiToClient("Red", error.message || "Lỗi không xác định");
                    });

                    // dataPCS.forEach(function (item) {
                    //     var arr = [
                    //         itemDDT.TEN_BENH_NHAN,
                    //         itemDDT.TUOI == 0 ? itemDDT.THANG == 0 ? itemDDT.NGAY + " ngày" : itemDDT.THANG + " tháng" : itemDDT.TUOI,
                    //         singletonObject.dvtt,
                    //         itemDDT.TEN_PHONGBAN,
                    //         itemDDT.CHAN_DOAN,
                    //         itemDDT.GIOI_TINH_HT,
                    //         itemDDT.SOVAOVIEN,
                    //         itemDDT.SOBENHAN,
                    //         '-1'
                    //     ];
                    //     var param = ['hovaten', 'tuoi', 'dvtt', 'ten_phongban', 'chandoan', 'gioitinh', 'sovaovien', 'sobenhan', 'idpcs'];
                    //     var url = "cmu_injasper?url=" + convertArray(arr) + "&param=" + convertArray(param) + "&loaifile=pdf&jasper=rp_phieuchamsoc_npcs_v2";
                    //     getFilesign769(
                    //         "PHIEU_NOITRU_PHIEUCHAMSOC",
                    //         item.ID,
                    //         -1,
                    //         singletonObject.dvtt,
                    //         itemDDT.SOVAOVIEN,
                    //         itemDDT.SOVAOVIEN_DT,
                    //         -1,
                    //         function(dataKySo) {
                    //             if(dataKySo.length > 0) {
                    //                 getCMUFileSigned769GetLink(dataKySo[0].KEYMINIO, 'pdf').then(pdfData => {
                    //                     url = pdfData;
                    //                     thongtinhsba.thongtinbn.linkPCS.push({
                    //                         "url": [url],
                    //                         "name": "Phiếu chăm sóc - Đợt " + itemDDT.STT_DOTDIEUTRI,
                    //                         "keyEMR": "BM_PHIEU_PHIEUCHAMSOC_13",
                    //                         "key": "PCS",
                    //                         "idPhieu": dataKySo[0].ID,
                    //                     });
                    //                 }).catch(error => {
                    //                     thongtinhsba.thongtinbn.linkPCS.push({
                    //                         "url": [url],
                    //                         "name": "Phiếu chăm sóc - " + thongtinhsba.thongtinbn.TEN_BENH_NHAN,
                    //                         "key": "PCS",
                    //                     });
                    //                 });
                    //             } else {
                    //                 thongtinhsba.thongtinbn.linkPCS.push({
                    //                     "url": [url],
                    //                     "name": "Phiếu chăm sóc - " + thongtinhsba.thongtinbn.TEN_BENH_NHAN,
                    //                     "key": "PCS",
                    //                 });
                    //             }
                    //         }
                    //     )
                    // });


                } else {
                    $("#hsba-phieuchamsoc-tab").hide();
                }
            }
        });
    });
}

function xemPhieuCNS(dsDotDieuTri) {
    thongtinhsba.thongtinbn.linkCNS = [];
    var count = 0;
    dsDotDieuTri.forEach(function (itemDDT) {
        $.ajax({
            url: "cmu_list_cmu_list_phieucns?url=" + convertArray([singletonObject.dvtt, itemDDT.SOVAOVIEN]),
            type: 'GET',
            success: function (dataCNS) {
                if (dataCNS.length > 0) {
                    $("#hsba-phieuchucnangsong-tab").show();
                    $("#wrap_Menu_PhieuKhac").show();
                    var totalPage = Math.ceil(dataCNS.length / 18);
                    var page = 1;
                    var dsPage = [];
                    for(var i = 1; i <= totalPage; i++) {
                        dsPage.push(i);
                        page++;
                    }
                    dsPage.forEach(function (item) {
                        getFilesign769(
                            "PHIEU_NOITRU_CHUCNANGSONG",
                            item,
                            -1,
                            singletonObject.dvtt,
                            thongtinhsba.thongtinbn.SOVAOVIEN,
                            thongtinhsba.thongtinbn.SOVAOVIEN_DT,
                            '-1',
                            function(dataKySo) {
                                var indexPage = dataCNS.length < 18 ? dataCNS.length - 1: dataCNS.length -  (Number(item) - 1 )*18 - 1;
                                var ret = dataCNS[indexPage];
                                var arr = [singletonObject.dvtt, itemDDT.MABENHNHAN, itemDDT.STT_BENHAN + ";" + ret.THOIGIANTHEODOI,
                                    itemDDT.STT_DOTDIEUTRI, itemDDT.TEN_BENH_NHAN, itemDDT.TUOI, itemDDT.GIOI_TINH, "", "", itemDDT.CHAN_DOAN ? itemDDT.CHAN_DOAN : itemDDT.ICD_HT];
                                var url = "nan_phieuTheodoichucnangsong_report?sovaovien=" + itemDDT.SOVAOVIEN +
                                    "&sovaovien_dt=" + itemDDT.SOVAOVIEN_DT + "&ngaybdin=" +
                                    ret.THOIGIANTHEODOI + "&url=" + convertArray(arr);
                                if (dataKySo.length > 0) {
                                    getCMUFileSigned769GetLink(dataKySo[0].KEYMINIO, 'pdf').then(pdfData => {
                                        url = pdfData;
                                        thongtinhsba.thongtinbn.linkCNS.push({
                                            "url": [url],
                                            "name": "Phiếu chức năng sống - Đợt " + itemDDT.STT_DOTDIEUTRI + " - Thời gian bắt đầu " + ret.THOIGIANTHEODOI,
                                            "key": "CNS",
                                            "keyEMR": "BM_PHIEU_THEODOI_CNSONG_113",
                                            "idPhieu": dataKySo[0].ID,
                                        });
                                    }).catch(error => {
                                        thongtinhsba.thongtinbn.linkCNS.push({
                                            "url": [url],
                                            "name": "Phiếu chức năng sống - Đợt " + itemDDT.STT_DOTDIEUTRI + " - Thời gian bắt đầu " + ret.THOIGIANTHEODOI,
                                            "key": "CNS"
                                        })
                                    });
                                } else {
                                    thongtinhsba.thongtinbn.linkCNS.push({
                                        "url": [url],
                                        "name": "Phiếu chức năng sống - Đợt " + itemDDT.STT_DOTDIEUTRI + " - Thời gian bắt đầu " + ret.THOIGIANTHEODOI,
                                        "key": "CNS"
                                    });
                                }
                            });
                    });
                }
            }
        });
    });
}

function xemPhieuCNSV2() {
    thongtinhsba.thongtinbn.linkCNS = [];
    let urlCNS = "cmu_list_cmu_list_phieucns?url="+convertArray([singletonObject.dvtt, thongtinhsba.thongtinbn.SOVAOVIEN]);
    $.ajax({
        url: urlCNS,
        type: "GET",
        async: false
    }).done(function(data) {
        if (data && data.length > 0) {
            $("#hsba-phieuchucnangsong-tab").show();
            $("#wrap_Menu_PhieuKhac").show();
            thongtinhsba.thongtinbn.phieuchucnangsong = data;
            let totalPage = Math.ceil(data.length / 18);
            let page = 1;
            let pageList = [];
            for (let i = 1; i <= totalPage; i++) {
                pageList.push(i.toString());
                page++;
            }
            $.each(pageList, function(_, v) {
                getFilesign769(
                    "PHIEU_NOITRU_CHUCNANGSONG",
                    v,
                    -1,
                    singletonObject.dvtt,
                    thongtinhsba.thongtinbn.SOVAOVIEN,
                    thongtinhsba.thongtinbn.SOVAOVIEN_DT,
                    '-1',
                    function (dataKySo) {
                        if (dataKySo.length > 0) {
                            getCMUFileSigned769GetLink(dataKySo[0].KEYMINIO, 'pdf').then(urlPhieu => {
                                thongtinhsba.thongtinbn.linkCNS.push({
                                    "url": [urlPhieu],
                                    "name": "Phiếu chức năng sống - Trang " + v,
                                    "keyEMR": "BM_PHIEU_THEODOI_CNSONG_113",
                                    "key": "CNS",
                                    "idPhieu": dataKySo[0].ID,
                                });
                            }).catch(error => {
                                notifiToClient("Red", "Lỗi lấy phiếu ký số chức năng sống - Trang " + v + ": " + error);
                            });
                        }
                        else {
                            getUrlXemPhieuChucNangSongXemTong(v).then(urlPhieu => {
                                thongtinhsba.thongtinbn.linkCNS.push({
                                    "url": [urlPhieu],
                                    "name": "Phiếu chức năng sống - Trang " + v,
                                    "key": "CNS"
                                });
                            }).catch(error => {
                                notifiToClient("Red", "Lỗi lấy phiếu chức năng sống - Trang " + v + ": " + error);
                            });
                        }
                    },
                );
            });
        }
    });
}

function getUrlXemPhieuChucNangSongXemTong(trang, id_ky = 0) {
    return new Promise(async (resolve, reject) => {
        try {
            let list = thongtinhsba.thongtinbn.phieuchucnangsong;
            let indexPage = list.length < 18 ?
                list.length - 1 : list.length -  (Number(trang) - 1 )*18 - 1;
            let ret = list[indexPage];
            let resData = $.ajax({
                url: "cmu_getlist?url=" + convertArray([
                    singletonObject.dvtt,
                    thongtinhsba.thongtinbn.MABENHNHAN,
                    thongtinhsba.thongtinbn.STT_BENHAN,
                    thongtinhsba.thongtinbn.STT_DOTDIEUTRI,
                    ret.THOIGIANTHEODOI,
                    "NAN_PHIEUCHUCNANGSONG_SP_F"]),
                method: "GET",
                async: false,
            }).responseText;
            let dataArr = JSON.parse(resData);
            let params = {};
            if (dataArr.length > 0) {
                let dataMach = [];
                let dataNhietDo = [];
                for (var i = 0; i < dataArr.length; i++) {
                    dataMach.push(dataArr[i].MACH);
                    dataNhietDo.push(dataArr[i].NHIETDO);
                    if (dataArr[i].ID == id_ky) {
                        break;
                    }
                }
                veBieuDoChucNangSongXemTong({
                    MACH: dataMach,
                    NHIET_DO: dataNhietDo
                });
                let image = $("#myChartCSC1").get(0).toDataURL("image/png").replace("data:image/png;base64,", "");
                $.ajax({
                    url: "cmu_post_CMU_CHART_INS",
                    method: "POST",
                    data: {
                        url: [singletonObject.dvtt, ret.THOIGIANTHEODOI, 'CHUCNANGSONG', i+1, image].join("```"),
                    },
                    async: false
                }).done(function(count) {});

                params = {
                    dvtt: singletonObject.dvtt,
                    mabenhnhan: thongtinhsba.thongtinbn.MABENHNHAN,
                    stt_benhan: thongtinhsba.thongtinbn.STT_BENHAN,
                    stt_dotdieutri: thongtinhsba.thongtinbn.STT_DOTDIEUTRI,
                    id_ky: id_ky,
                    in_tu_ngay: ret.THOIGIANTHEODOI
                };
                for (let i = 0; i < 18; i++) {
                    let object = {};
                    let item = dataArr[i];
                    object["day_" + i] = item ? item.NGAY_DO : null;
                    object["gio_" + i] = item ? item.GIO_DO : null;
                    object["hap_tren_" + i] = item ? item.HUYETAPTREN : null;
                    object["hap_duoi_" + i] = item ? item.HUYETAPDUOI : null;
                    object["cannang_" + i] = item ? item.CANNANG : null;
                    object["nhiptho_" + i] = item ? item.NHIPTHO : null;
                    object["spo2_" + i] = item ? item.SPO2 : null;
                    object["fio2_" + i] = item ? item.FIO2 : null;
                    object["khac_" + i] = item ? item.KHAC : null;
                    object["nguoilap_" + i] = item ? item.NGUOILAP : null;
                    object["id_" + i] = item ? item.ID : null;
                    object["keysign_" + i] = item ? item.KEYSIGN : null;
                    params = {...params, ...object};
                    if (item && item.ID == id_ky) {
                        break;
                    }
                }
            }
            resolve('cmu_in_rp_theodoichucnangsong_emr?type=pdf&' + $.param(params));
        }
        catch (e) {
            reject(e);
        }
    });
}

function veBieuDoChucNangSongXemTong(objectChart) {
    $("#wrap_canvas").html("<canvas id='myChartCSC1' width='1028' height='584'></canvas>")
    let canvas = document.getElementById('myChartCSC1');
    let ctx = canvas.getContext('2d');
    let canvasWidth = canvas.width;
    let canvasHeight = canvas.height;
    ctx.clearRect(0, 0, canvasWidth, canvasHeight)
    let margin = 10;
    let Hmargin = 20;
    let chartWidth = canvasWidth - 2 * margin;
    let chartHeight = canvasHeight - 2 * Hmargin;
    let barWidth = chartWidth / 18;

    function isNumeric(value) {
        return !isNaN(parseFloat(value)) && isFinite(value);
    }

    function drawChart() {
        ctx.lineWidth = 1;
        ctx.strokeStyle = '#c5c5c5';
        ctx.fillStyle = '#c5c5c5';
        ctx.beginPath();
        for (let i = 0; i <= 7; i++) {
            let yPos = Hmargin + i * (chartHeight / 7);
            ctx.beginPath();
            ctx.moveTo(margin, yPos);
            ctx.lineTo(canvasWidth - margin, yPos);
            ctx.setLineDash([5, 5]);
            ctx.stroke();
        }
        for (let i = 0; i <= 18; i++) {
            let xPos = margin + i * barWidth;
            ctx.beginPath();
            ctx.moveTo(xPos, Hmargin);
            ctx.lineTo(xPos, canvasHeight - Hmargin);
            ctx.setLineDash([5, 5]);
            ctx.stroke();
        }

    }

    function drawMachChart(data) {
        ctx.lineWidth = 2;
        ctx.setLineDash([]);
        ctx.fillStyle = '#ff4136';
        ctx.strokeStyle = '#ff4136';
        let x = 0;
        let y = 0;
        let stepH = (chartHeight / 7);
        for (let i = 0; i < data.length; i++) {
            if(data[i]){
                x = (margin + i * barWidth) + barWidth / 2;
                if (isNumeric(data[i])) {
                    y = canvasHeight - Hmargin - ((data[i]-40)/20) * stepH;
                    if (y <= canvasHeight - Hmargin && y >= Hmargin) {
                        ctx.beginPath();
                        ctx.arc(x, y, 8, 0, 2 * Math.PI);
                        ctx.fill();
                    }
                    if(data[i] <= 40) {
                        ctx.font = "bold 15px Arial";
                        ctx.fillText("M:"+data[i], x - margin, canvasHeight -stepH );
                    }
                }
                else {
                    ctx.font = "bold 15px Arial";
                    ctx.textAlign = "center";
                    ctx.fillText("M:"+data[i], x, canvasHeight -stepH );
                }
            }
        }
        for (let i = 0; i < data.length; i++) {
            if (data[i]){
                x = (margin + i * barWidth) + barWidth / 2;
                y = canvasHeight - Hmargin - ((data[i]-40)/20) * stepH;
                if (i === 0  ) {
                    ctx.moveTo(x, y);
                }
                else {
                    if (y > canvasHeight - Hmargin) {
                        y = canvasHeight - Hmargin;
                    }
                    ctx.lineTo(x, y);
                }
            }
        }
        ctx.stroke();
    }

    function drawNhietDoChart(data) {
        ctx.lineWidth = 2;
        ctx.setLineDash([]);
        ctx.fillStyle = '#0000ff';
        ctx.strokeStyle = '#0000ff';
        let x = 0;
        let y = 0;
        let stepH = (chartHeight / 7);
        for (let i = 0; i < data.length; i++) {
            if (data[i]){
                x = (margin + i * barWidth) + barWidth / 2;
                if (isNumeric(data[i])) {
                    y = canvasHeight - Hmargin - (data[i]-35) * stepH;
                    if (y <= canvasHeight - Hmargin && y >= Hmargin) {
                        ctx.beginPath();
                        ctx.fillRect(x-3, y-5, 12, 12);
                    }
                    if (data[i] < 35) {
                        ctx.font = "bold 15px Arial";
                        ctx.fillText("t:"+data[i], x - margin, canvasHeight - stepH - 25);
                    }
                }
                else {
                    ctx.font = "bold 15px Arial";
                    ctx.textAlign = "center";
                    ctx.fillText("t:"+data[i], x, canvasHeight - stepH - 25);
                }
            }
        }

        for (let i = 0; i < data.length; i++) {
            if (data[i]) {
                x = (margin + i * barWidth) + barWidth / 2;
                y = canvasHeight - Hmargin - (data[i]-35) * stepH;
                if (y > canvasHeight - Hmargin) {
                    y = canvasHeight - Hmargin;
                }
                if (i === 0  ) {
                    ctx.moveTo(x, y);
                }
                else {
                    ctx.lineTo(x, y);
                }
            }
        }
        ctx.stroke();
    }
    drawChart();
    drawMachChart(objectChart.MACH);
    drawNhietDoChart(objectChart.NHIET_DO);
}

// function xemToaThuoc(dsDotDieuTri) {
//     return new Promise(function(resolve, reject) {
//         thongtinhsba.thongtinbn.linkTOATHUOC = [];
//         dsDotDieuTri.forEach(function (itemDDT) {
//             $.ajax({
//                 url: 'cmu_list_HSBA_DSTHUOC_DISTINCT?url='
//                     + convertArray([singletonObject.dvtt, itemDDT.STT_BENHAN, itemDDT.MABENHNHAN,
//                         itemDDT.STT_DOTDIEUTRI, '-1']),
//                 type: "GET",
//             }).done(function (data) {
//                 if (data.length > 0) {
//                     data.forEach(function (item) {
//                         var arr = [singletonObject.dvtt, itemDDT.STT_BENHAN, itemDDT.STT_DOTDIEUTRI, item.MA_TOA_THUOC, itemDDT.MABENHNHAN,
//                             item.MA_TOA_THUOC, itemDDT.SOPHIEUTHANHTOAN, item.NGHIEP_VU, ' ', ' ', "0"];
//                         var url = 'noitru_intoathuoc?url=' + convertArray(arr) + "&toa=toa_t&thangTuoi=" + itemDDT.THANG_NS + "&namTuoi=" + itemDDT.TUOI_NS + "&viewPDF=1"
//                         thongtinhsba.thongtinbn.linkTOATHUOC.push({
//                             "url": [url],
//                             "name": "Toa thuốc " + item.MA_TOA_THUOC + " - " + item.NGAYLAP,
//                             "ngayLap": item.NGAYLAP,
//                             "key": "TOATHUOC",
//                         });
//                     });
//                     // thongtinhsba.thongtinbn.dsThuoc = [...thongtinhsba.thongtinbn.dsThuoc, ...data];
//                 }
//             });
//         });
//         resolve();
//     });
// }

function xemXetNghiem(dsDotDieuTri) {
    thongtinhsba.thongtinbn.linkXetNghiem = [];
    dsDotDieuTri.forEach(function (itemDDT) {
        var params = {
            soVaoVien: itemDDT.SOVAOVIEN,
            soVaoVienDT: itemDDT.SOVAOVIEN_DT,
            type: 'xn',
            description: -1
        };
        $.ajax({
            url: 'getListCLS?' + $.param(params),
            type: "GET",
        }).done(function (data) {
            if(data.length > 0){
                $("#hsba-xetnghiem-tab").show();
                $("#wrap_Menu_CLS").show();
                var stt = 0;
                var distinctItems = distinctData(data, ['SO_PHIEU_XN', 'TENKHOA_CHIDINH', 'BHYTKHONGCHI', 'STT_DIEUTRI', 'NGAY_TAO', 'SID']);
                distinctItems.forEach(function (itemDistinct) {
                    stt++;
                    var arrCD = [itemDDT.MABENHNHAN,
                        itemDistinct.BHYTKHONGCHI,
                        itemDistinct.SO_PHIEU_XN,
                        singletonObject.dvtt,
                        itemDDT.SOBENHAN,
                        itemDDT.STT_BENHAN,
                        itemDDT.STT_DOTDIEUTRI,
                        itemDistinct.STT_DIEUTRI,
                        itemDDT.CHAN_DOAN,
                        itemDistinct.TENKHOA_CHIDINH,
                        "",
                        "0",
                        itemDDT.SOVAOVIEN,
                        itemDDT.SOVAOVIEN_DT,
                        0];
                    getFilesign769(
                        "PHIEUCD_NOITRU_XN",
                        itemDistinct.SO_PHIEU_XN,
                        -1,
                        singletonObject.dvtt,
                        itemDDT.SOVAOVIEN,
                        itemDDT.SOVAOVIEN_DT,
                        '-1'
                        , function (dataKySo) {
                            var url = "noitru_inphieuxetnghiem_svv?url=" + convertArray(arrCD);
                            var keyEMR = itemDistinct.BHYTKHONGCHI == "0" ? "BM_PHIEU_CHIDINH_XETNGHIEM_BHYT_16" : "BM_PHIEU_CHIDINH_XETNGHIEM_152";
                            if (dataKySo.length > 0) {
                                getCMUFileSigned769GetLink(dataKySo[0].KEYMINIO, 'pdf').then(pdfData => {
                                    url = pdfData;
                                    thongtinhsba.thongtinbn.linkXetNghiem.push({
                                        "url": [url],
                                        "name": "Phiếu chỉ định xét nghiệm " + itemDistinct.SO_PHIEU_XN + " - " + itemDistinct.NGAY_TAO + " - " + itemDistinct.TENKHOA_CHIDINH,
                                        "key": "CDXN",
                                        "ngayLap": itemDistinct.NGAY_TAO,
                                        "keyEMR": keyEMR,
                                        "idPhieu": dataKySo[0].ID,
                                    });
                                }).catch(error => {
                                    thongtinhsba.thongtinbn.linkXetNghiem.push({
                                        "url": [url],
                                        "name": "Phiếu chỉ định xét nghiệm " + itemDistinct.SO_PHIEU_XN + " - " + itemDistinct.NGAY_TAO + " - " + itemDistinct.TENKHOA_CHIDINH,
                                        "key": "CDXN",
                                        "ngayLap": itemDistinct.NGAY_TAO
                                    });
                                });
                            } else {
                                thongtinhsba.thongtinbn.linkXetNghiem.push({
                                    "url": [url],
                                    "name": "Phiếu chỉ định xét nghiệm " + itemDistinct.SO_PHIEU_XN + " - " + itemDistinct.NGAY_TAO + " - " + itemDistinct.TENKHOA_CHIDINH,
                                    "key": "CDXN",
                                    "ngayLap": itemDistinct.NGAY_TAO
                                });
                            }
                        });
                    console.log("itemDDT", itemDDT)
                    getFilesign769(
                        "PHIEUKQ_XETNGHIEM",
                        itemDistinct.SO_PHIEU_XN,
                        -1,
                        singletonObject.dvtt,
                        itemDDT.SOVAOVIEN,
                        itemDDT.SOVAOVIEN_DT,
                        '-1'
                        , function (dataKySo) {
                            var url = "";
                            if (dataKySo.length > 0) {
                                getCMUFileSigned769GetLink(dataKySo[0].KEYMINIO, 'pdf').then(pdfData => {
                                    url = pdfData;
                                    thongtinhsba.thongtinbn.linkXetNghiem.push({
                                        "url": [url],
                                        "name": "Phiếu kết quả xét nghiệm " + itemDistinct.SO_PHIEU_XN + " - " + itemDistinct.NGAY_TAO + " - " + itemDistinct.TENKHOA_CHIDINH,
                                        "key": "KQXN",
                                        "ngayLap": itemDistinct.NGAY_TAO,
                                        "keyEMR": "BM_PHIEU_KETQUA_XETNGHIEM_17",
                                        "idPhieu": dataKySo[0].ID,
                                    });
                                }).catch(error => {
                                    // $.ajax({
                                    //     url: "cmu_list_cmu_laythongtinbnxetnghiem?url=" +
                                    //         convertArray([singletonObject.dvtt, '1', itemDistinct.SO_PHIEU_XN, itemDDT.SOVAOVIEN, itemDDT.SOVAOVIEN_DT]),
                                    //     type: "GET",
                                    //     async: false,
                                    // }).done(function (data3) {
                                    //     var makhambenh = data3[0].MAKHAMBENH;
                                    //     if (makhambenh == null) makhambenh = ' ';
                                    //     var maxetnghiem = ',';
                                    //     var dsXN = data.filter(function (item) {
                                    //         return itemDistinct.SO_PHIEU_XN == item.SO_PHIEU_XN
                                    //     });
                                    //     dsXN.forEach(function (item) {
                                    //         maxetnghiem += item.MA_XET_NGHIEM + ',';
                                    //     })
                                    //     var arr = [makhambenh, itemDDT.TEN_BENH_NHAN, itemDDT.NAM_SINH, itemDDT.GIOI_TINH,
                                    //         itemDDT.DIA_CHI, itemDDT.CHAN_DOAN, data3[0].NGUOICHIDINH, "1", 1, data3[0].STT_BENHAN, data3[0].STT_DOTDIEUTRI,
                                    //         data3[0].STT_DIEUTRI + "--" + maxetnghiem + "--" + itemDistinct.SID, "0", data3[0].SOTHEBHYT, itemDistinct.SO_PHIEU_XN,
                                    //         data3[0].CAPCUU, data3[0].NGAYTAO, itemDDT.TUOI, data3[0].NGAYCHIDINH, "0", itemDistinct.TENKHOA_CHIDINH, maxetnghiem,
                                    //         itemDDT.SOVAOVIEN, itemDDT.SOVAOVIEN, itemDDT.SOVAOVIEN_DT, data3[0].CHANDOANBENH, 0, itemDDT.MABENHNHAN, 0];
                                    //     url = "xetnghiem_inketqua_svv?url=" + convertArray(arr) + "&maxetnghiem=0";
                                    //     thongtinhsba.thongtinbn.linkXetNghiem.push({
                                    //         "url": [url],
                                    //         "name": "Phiếu kết quả xét nghiệm " + itemDistinct.SO_PHIEU_XN + " - " + itemDistinct.NGAY_TAO + " - " + itemDistinct.TENKHOA_CHIDINH,
                                    //         "key": "KQXN",
                                    //         "ngayLap": itemDistinct.NGAY_TAO
                                    //     });
                                    // });
                                });
                            } else {
                                $.ajax({
                                    url: "cmu_list_cmu_laythongtinbnxetnghiem?url=" +
                                        convertArray([singletonObject.dvtt, '1', itemDistinct.SO_PHIEU_XN, itemDDT.SOVAOVIEN, itemDDT.SOVAOVIEN_DT]),
                                    type: "GET",
                                    async: false,
                                }).done(function (data3) {
                                    var makhambenh = data3[0].MAKHAMBENH;
                                    if (makhambenh == null) makhambenh = ' ';
                                    var maxetnghiem = ',';
                                    var dsXN = data.filter(function (item) {
                                        return itemDistinct.SO_PHIEU_XN == item.SO_PHIEU_XN
                                    });
                                    dsXN.forEach(function (item) {
                                        maxetnghiem += item.MA_XET_NGHIEM + ',';
                                    })
                                    var arr = [makhambenh, itemDDT.TEN_BENH_NHAN, itemDDT.NAM_SINH, itemDDT.GIOI_TINH,
                                        itemDDT.DIA_CHI, itemDDT.CHAN_DOAN, data3[0].NGUOICHIDINH, "1", 1, data3[0].STT_BENHAN, data3[0].STT_DOTDIEUTRI,
                                        data3[0].STT_DIEUTRI, "0", data3[0].SOTHEBHYT, itemDistinct.SO_PHIEU_XN,
                                        data3[0].CAPCUU, data3[0].NGAYTAO, itemDDT.TUOI, data3[0].NGAYCHIDINH, "0", itemDistinct.TENKHOA_CHIDINH, maxetnghiem,
                                        itemDDT.SOVAOVIEN, itemDDT.SOVAOVIEN, itemDDT.SOVAOVIEN_DT, data3[0].CHANDOANBENH, 0, itemDDT.MABENHNHAN, 0];
                                    url = "xetnghiem_inketqua_svv?url=" + convertArray(arr) + "&maxetnghiem=" + maxetnghiem;
                                    if(singletonObject.inPhieuKQMay == "1") {
                                        arr = [singletonObject.dvtt,"", itemDDT.TEN_BENH_NHAN,
                                            itemDDT.NAM_SINH, itemDDT.GIOI_TINH_HT, itemDDT.DIA_CHI,
                                            "", data3[0].NGUOI_CHI_DINH, 1, itemDDT.STT_BENHAN, itemDDT.STT_DOTDIEUTRI, data3[0].STT_DIEUTRI +'--,'+maxetnghiem,
                                            itemDDT.SOBAOHIEMYTE, itemDistinct.SO_PHIEU_XN, data3[0].CAPCUU, data3[0].NGAYCHIDINH, itemDDT.TUOI_HT, data3[0].NGAYCHIDINH,
                                            itemDistinct.TENKHOA_CHIDINH, "-1",
                                            0, itemDDT.SOVAOVIEN, itemDDT.SOVAOVIEN_DT, data3[0].CHANDOANBENH, itemDDT.MABENHNHAN,
                                            '/WEB-INF/pages/camau/reports/rp_phieuhoasinhmau.jasper',
                                            '/WEB-INF/pages/camau/reports/rp_phieuxetnghiemhoasinhnuoctieu.jasper',
                                            '/WEB-INF/pages/camau/reports/rp_phieuxetnghiemhuyethoc_tk.jasper',
                                            '/WEB-INF/pages/camau/reports/rp_phieuxetnghiembenhpham.jasper',
                                            '/WEB-INF/pages/camau/reports/rp_phieuxetnghiemhuyethoc_dt.jasper',
                                            '/WEB-INF/pages/camau/reports/rp_phieuxetnghiemhuyethoc_vs.jasper',
                                            '/WEB-INF/pages/camau/reports/rp_phieuxetnghiemtong.jasper',
                                        ];
                                        var phieuxn = "rp_phieuketquaxetnghiem_theomau";
                                        if(singletonObject.dvtt.includes(["96161", "96163", "96025"])) {
                                            phieuxn = "rp_phieuketquaxetnghiem_theomau_96161";
                                        }
                                        var param = ['madonvi','makhambenh',  'hoten',"namsinh",
                                            'gioitinh','diachi_bn','chandoan','bsdieutri','noitru',
                                            'stt_benhan','stt_dotdieutri','stt_dieutri','sothebaohiem',
                                            'sophieuxn','capcuu','ngaytao', 'tuoi','ngaydieutri',
                                            'khoa','loaixetnghiem','sovaovien','sovaovien_noi', 'sovaovien_dt_noi', 'chandoan',
                                            'mabenhnhan',
                                            'FILE_1',
                                            'FILE_2',
                                            'FILE_3',
                                            'FILE_4',
                                            'FILE_5',
                                            'FILE_6',
                                            'FILE_7',
                                        ];
                                        url = "cmu_injasper?url=" + convertArray(arr)+"&param="+ convertArray(param)+"&loaifile=pdf&jasper="+phieuxn;
                                    }

                                    thongtinhsba.thongtinbn.linkXetNghiem.push({
                                        "url": [url],
                                        "name": "Phiếu kết quả xét nghiệm " + itemDistinct.SO_PHIEU_XN + " - " + itemDistinct.NGAY_TAO + " - " + itemDistinct.TENKHOA_CHIDINH,
                                        "key": "KQXN",
                                        "ngayLap": itemDistinct.NGAY_TAO
                                    });
                                });
                            }
                        }
                    )
                    getFilesign769(
                        "PHIEU_KETQUA_XETNGHIEMVISINH_TONG",
                        itemDistinct.SO_PHIEU_XN,
                        -1,
                        singletonObject.dvtt,
                        itemDDT.SOVAOVIEN,
                        itemDDT.SOVAOVIEN_DT ? itemDDT.SOVAOVIEN_DT : "0",
                        '-1'
                        , function (dataKySo) {
                            var url = "";
                            if (dataKySo.length > 0) {
                                getCMUFileSigned769GetLink(dataKySo[0].KEYMINIO, 'pdf').then(pdfData => {
                                    url = pdfData;
                                    thongtinhsba.thongtinbn.linkXetNghiem.push({
                                        "url": [url],
                                        "name": "Phiếu kết quả xét nghiệm vi sinh " + itemDistinct.SO_PHIEU_XN + " - " + itemDistinct.NGAY_TAO + " - " + itemDistinct.TENKHOA_CHIDINH,
                                        "key": "KQXN",
                                        "ngayLap": itemDistinct.NGAY_TAO,
                                        "keyEMR": "BM_PHIEU_KETQUA_XETNGHIEM_17",
                                        "idPhieu": dataKySo[0].ID,
                                    });
                                }).catch(error => {

                                });
                            }
                        }
                    )
                });
            } else {
                $("#hsba-xetnghiem-tab").hide();
            }
        });
    });
}

function xemCDHA(dsDotDieuTri) {
    thongtinhsba.thongtinbn.linkCDHA = [];
    dsDotDieuTri.forEach(function (itemDDT) {
        $.ajax({
            url: 'cmu_list_HSBA_DSCDHA_TONGHOP?url='
                + convertArray([singletonObject.dvtt, itemDDT.STT_BENHAN,
                    itemDDT.MABENHNHAN]),
            type: "GET",
        }).done(function (dataCDHA) {
            if (dataCDHA.length > 0) {
                $("#hsba-cdha-tab").show();
                $("#wrap_Menu_CLS").show();
                // Phiếu chẩn đoán
                var distinctItems = distinctData(dataCDHA, ['SO_PHIEU_CDHA', 'KHOA_CHI_DINH', 'BHYTKCHI', 'STT_DIEUTRI', 'NGAY_CHI_DINH', 'NGAY_TRA_KETQUA', 'GIO_TRA_KETQUA', 'TEN_CDHA', 'CHIEUCAO', 'CANNANG', 'TENKHOA']);
                distinctItems.forEach(function (itemDistinct) {
                    var arr = [itemDDT.MABENHNHAN,
                        itemDistinct.BHYTKCHI,
                        itemDistinct.SO_PHIEU_CDHA,
                        singletonObject.dvtt,
                        itemDDT.SOBENHAN,
                        itemDDT.STT_BENHAN,
                        itemDDT.STT_DOTDIEUTRI,
                        itemDistinct.STT_DIEUTRI,
                        itemDDT.CHAN_DOAN,
                        itemDistinct.KHOA_CHI_DINH,
                        "",
                        "0",
                        itemDDT.SOVAOVIEN,
                        itemDDT.SOVAOVIEN_DT,
                        0,
                        1];
                    var url = "noitru_inphieucdha_svv?url=" + convertArray(arr);

                    getFilesign769(
                        "PHIEUCD_NOITRU_CDHA",
                        itemDistinct.SO_PHIEU_CDHA,
                        -1,
                        singletonObject.dvtt,
                        itemDDT.SOVAOVIEN,
                        itemDDT.SOVAOVIEN_DT,
                        '-1'
                        , function (dataKySo) {
                            // var url = "noitru_inphieuxetnghiem_svv?url=" + convertArray(arrCD);
                            var keyEMR = itemDistinct.BHYTKHONGCHI == "0" ? "BM_PHIEU_CHIDINH_CDHA_BHYT_5" : "BM_PHIEU_CHIDINH_CDHA_151";
                            if (dataKySo.length > 0) {
                                getCMUFileSigned769GetLink(dataKySo[0].KEYMINIO, 'pdf').then(pdfData => {
                                    url = pdfData;
                                    thongtinhsba.thongtinbn.linkCDHA.push({
                                        "url": [url],
                                        "name": "Phiếu chỉ định CDHA " + itemDistinct.SO_PHIEU_CDHA + " - " + itemDistinct.NGAY_CHI_DINH,
                                        "key": "CDCDHA",
                                        "ngayLap": itemDistinct.NGAY_CHI_DINH,
                                        "keyEMR": keyEMR,
                                        "idPhieu": dataKySo[0].ID,
                                    });
                                }).catch(error => {
                                    thongtinhsba.thongtinbn.linkCDHA.push({
                                        "url": [url],
                                        "name": "Phiếu chỉ định CDHA " + itemDistinct.SO_PHIEU_CDHA + " - " + itemDistinct.NGAY_CHI_DINH,
                                        "key": "CDCDHA",
                                        "ngayLap": itemDistinct.NGAY_CHI_DINH
                                    });
                                });
                            } else {
                                thongtinhsba.thongtinbn.linkCDHA.push({
                                    "url": [url],
                                    "name": "Phiếu chỉ định CDHA " + itemDistinct.SO_PHIEU_CDHA + " - " + itemDistinct.NGAY_CHI_DINH,
                                    "key": "CDCDHA",
                                    "ngayLap": itemDistinct.NGAY_CHI_DINH
                                });
                            }
                        });

                    // Phiếu kết quả
                    var dsCDHA = dataCDHA.filter(function (item) {
                        return itemDistinct.SO_PHIEU_CDHA == item.SO_PHIEU_CDHA
                    });
                    dsCDHA.forEach(function (item) {
                        if (item.MOTA_LOAI_CDHA == "XQ") {
                            var tenkhoa = '';
                            singletonObject.danhsachphongban.map(function(item) {
                                if(item.MAKHOA == item.KHOA_CHI_DINH){
                                    tenkhoa = item.TENKHOA;
                                }
                            })
                            var urlRISPACS = "download-result-ris-pacs?maPhieuChiDinh=" + item.SO_PHIEU_CDHA + "&maDichVu=" + item.MA_CDHA;
                            var arr = [itemDDT.MABENHNHAN, itemDDT.TEN_BENH_NHAN, itemDDT.DIA_CHI, itemDDT.TUOI, itemDDT.GIOI_TINH, "",
                                item.SO_PHIEU_CDHA, item.MA_CDHA,
                                singletonObject.dvtt, 1, itemDDT.STT_BENHAN, itemDDT.STT_DOTDIEUTRI, item.STT_DIEUTRI, tenkhoa,
                                itemDDT.SOGIUONG ? itemDDT.SOGIUONG : itemDDT.SO_GIUONG_BENH, itemDDT.TEN_PHONG ? itemDDT.TEN_PHONG : itemDDT.MAGIUONGBENH, itemDDT.SOBAOHIEMYTE, "", item.NGUOI_CHI_DINH, item.BAC_SI_DIEU_TRI,
                                "", item.NGUOI_CHI_DINH, item.NGAY_CHI_DINH, "0", thongtinhsba.thongtinbn.SOVAOVIEN,
                                itemDDT.SOVAOVIEN, itemDDT.SOVAOVIEN_DT, 0, "-1", 0, "2"];
                            var urlEnd = "inketquaxquang_svv?url=" + convertArray(arr) + "&ngaytrakq=" + (itemDistinct.NGAY_TRA_KETQUA ? itemDistinct.NGAY_TRA_KETQUA : item.NGAY_CHI_DINH);
                            getFilesign769(
                                "PHIEUKQ_CT_XQUANG_MRI",
                                item.SO_PHIEU_CDHA,
                                -1,
                                singletonObject.dvtt,
                                itemDDT.SOVAOVIEN,
                                itemDDT.SOVAOVIEN_DT,
                                '-1'
                                , function (dataKySo) {
                                    if (dataKySo.length > 0) {
                                        getCMUFileSigned769GetLink(dataKySo[0].KEYMINIO, 'pdf').then(pdfData => {
                                            urlEnd = pdfData;
                                            thongtinhsba.thongtinbn.linkCDHA.push({
                                                "url": [urlEnd],
                                                "name": "Phiếu kết quả CDHA X-Quang " + item.SO_PHIEU_CDHA + " - " + item.NGAY_CHI_DINH,
                                                "key": "KQCDHA",
                                                "ngayLap": item.NGAY_CHI_DINH,
                                                "keyEMR": "BM_PHIEU_KETQUA_CDHA_7",
                                                "idPhieu": dataKySo[0].ID,
                                            });
                                        }).catch(error => {
                                            thongtinhsba.thongtinbn.linkCDHA.push({
                                                "url": [urlRISPACS, urlEnd],
                                                "name": "Phiếu kết quả CDHA X-Quang " + item.SO_PHIEU_CDHA + " - " + item.NGAY_CHI_DINH,
                                                "key": "KQCDHA",
                                                "ngayLap": item.NGAY_CHI_DINH
                                            });
                                        });
                                    } else {
                                        thongtinhsba.thongtinbn.linkCDHA.push({
                                            "url": [urlRISPACS, urlEnd],
                                            "name": "Phiếu kết quả CDHA X-Quang " + item.SO_PHIEU_CDHA + " - " + item.NGAY_CHI_DINH,
                                            "key": "KQCDHA",
                                            "ngayLap": item.NGAY_CHI_DINH
                                        });
                                    }
                                });
                        }
                        if (item.MOTA_LOAI_CDHA == "SA") {
                            var urlRISPACS = "download-result-ris-pacs?maPhieuChiDinh=" + item.SO_PHIEU_CDHA + "&maDichVu=" + item.MA_CDHA;
                            var arr = [itemDDT.MABENHNHAN, itemDDT.TEN_BENH_NHAN, itemDDT.DIA_CHI, itemDDT.TUOI, itemDDT.GIOI_TINH, "",
                                item.SO_PHIEU_CDHA, item.MA_CDHA, singletonObject.dvtt, 1, itemDDT.STT_BENHAN, itemDDT.STT_DOTDIEUTRI,
                                item.STT_DIEUTRI, itemDDT.SOBAOHIEMYTE, "0", 0, itemDDT.SOVAOVIEN, itemDDT.SOVAOVIEN_DT, 0, "",
                                item.BAC_SI_DIEU_TRI, 0, itemDDT.NAM_SINH, item.KHOA_CHI_DINH, 0, 2];
                            var urlEnd = "inketquasieuam_svv?thongtin=" + convertArray(arr) + "&anh=&viewPDF=1";
                            thongtinhsba.thongtinbn.linkCDHA.push({
                                "url": [urlRISPACS, urlEnd],
                                "name": "Phiếu kết quả CDHA Siêu Âm " + item.SO_PHIEU_CDHA + " - " + item.NGAY_CHI_DINH,
                                "key": "KQCDHA",
                                "ngayLap": item.NGAY_CHI_DINH
                            });
                        }
                        if (item.MOTA_LOAI_CDHA == "TDCN") {
                            var urlRISPACS = "download-result-ris-pacs?maPhieuChiDinh=" + item.SO_PHIEU_CDHA + "&maDichVu=" + item.MA_CDHA;
                            var arr = [itemDDT.MABENHNHAN, itemDDT.TEN_BENH_NHAN, itemDDT.DIA_CHI, itemDDT.TUOI, itemDDT.GIOI_TINH_HT ? itemDDT.GIOI_TINH_HT : itemDDT.GIOI_TINH, "",
                                item.SO_PHIEU_CDHA, item.MA_CDHA, singletonObject.dvtt, 1, itemDDT.STT_BENHAN, itemDDT.STT_DOTDIEUTRI,
                                item.STT_DIEUTRI, itemDistinct.CANNANG, itemDistinct.CHIEUCAO, itemDistinct.TENKHOA, itemDDT.SOGIUONG ? itemDDT.SOGIUONG : itemDDT.SO_GIUONG_BENH, itemDDT.TEN_PHONG ? itemDDT.TEN_PHONG : itemDDT.MAGIUONGBENH, itemDDT.SOBAOHIEMYTE,
                                itemDDT.CHAN_DOAN, itemDistinct.TEN_CDHA, item.NGUOI_CHI_DINH, item.BAC_SI_DIEU_TRI,
                                "STT_HANGNGAY", item.TEN_CDHA, "0", "MOTA_CDHA", 0, itemDDT.SOVAOVIEN, itemDDT.SOVAOVIEN_DT, 0,
                                2, itemDistinct.NGAY_TRA_KETQUA || 0, itemDistinct.GIO_TRA_KETQUA || 0];
                            var urlEnd = "inketquadientim_svv?url=" + convertArray(arr) + "&viewPDF=1";
                            getFilesign769(
                                "PHIEUKQ_DIENTIM",
                                item.SO_PHIEU_CDHA,
                                -1,
                                singletonObject.dvtt,
                                itemDDT.SOVAOVIEN,
                                itemDDT.SOVAOVIEN_DT,
                                '-1'
                                , function (dataKySo) {
                                    if (dataKySo.length > 0) {
                                        getCMUFileSigned769GetLink(dataKySo[0].KEYMINIO, 'pdf').then(pdfData => {
                                            urlEnd = pdfData;
                                            thongtinhsba.thongtinbn.linkCDHA.push({
                                                "url": [urlEnd],
                                                "name": "Phiếu kết quả CDHA TDCN " + item.SO_PHIEU_CDHA + " - " + item.NGAY_CHI_DINH,
                                                "key": "KQCDHA",
                                                "ngayLap": item.NGAY_CHI_DINH,
                                                "keyEMR": "BM_PHIEU_KETQUA_CDHA_7",
                                                "idPhieu": dataKySo[0].ID,
                                            });
                                        }).catch(error => {
                                            thongtinhsba.thongtinbn.linkCDHA.push({
                                                "url": [urlRISPACS, urlEnd],
                                                "name": "Phiếu kết quả CDHA TDCN " + item.SO_PHIEU_CDHA + " - " + item.NGAY_CHI_DINH,
                                                "key": "KQCDHA",
                                                "ngayLap": item.NGAY_CHI_DINH
                                            });
                                        });
                                    } else {
                                        thongtinhsba.thongtinbn.linkCDHA.push({
                                            "url": [urlRISPACS, urlEnd],
                                            "name": "Phiếu kết quả CDHA TDCN " + item.SO_PHIEU_CDHA + " - " + item.NGAY_CHI_DINH,
                                            "key": "KQCDHA",
                                            "ngayLap": item.NGAY_CHI_DINH
                                        });
                                    }
                                });
                        }
                        if (item.MOTA_LOAI_CDHA == "NS") {
                            var urlRISPACS = "download-result-ris-pacs?maPhieuChiDinh=" + item.SO_PHIEU_CDHA + "&maDichVu=" + item.MA_CDHA;
                            var arr = [itemDDT.MABENHNHAN, itemDDT.TEN_BENH_NHAN, itemDDT.DIA_CHI, itemDDT.TUOI, itemDDT.GIOI_TINH, "",
                                item.SO_PHIEU_CDHA, item.MA_CDHA, itemDDT.SOBAOHIEMYTE, singletonObject.dvtt, 1, itemDDT.STT_BENHAN,
                                itemDDT.STT_DOTDIEUTRI, item.STT_DIEUTRI, itemDDT.NGAYBATDAU_THEBHYT, itemDDT.NGAYHETHAN_THEBHYT,
                                itemDDT.NOIDANGKYBANDAU, itemDDT.NAM_SINH, "0", 0, itemDDT.SOVAOVIEN, itemDDT.SOVAOVIEN_DT, 0, itemDDT.CHAN_DOAN,
                                item.BAC_SI_DIEU_TRI, 0, "0"];
                            var urlEnd = "inketquanoisoi_svv?thongtin=" + convertArray(arr) + "&anh=&printAs=2&viewPDF=1";
                            thongtinhsba.thongtinbn.linkCDHA.push({
                                "url": [urlRISPACS, urlEnd],
                                "name": "Phiếu kết quả CDHA Nội Soi " + item.SO_PHIEU_CDHA + " - " + item.NGAY_CHI_DINH,
                                "key": "KQCDHA",
                                "ngayLap": item.NGAY_CHI_DINH
                            });
                        }
                    });
                });
            } else {
                $("#hsba-cdha-tab").hide();
            }
        });
    });
}

function xemTTPT(dsDotDieuTri) {
    thongtinhsba.thongtinbn.linkTTPT = [];
    dsDotDieuTri.forEach(function (itemDDT) {
        $.ajax({
            url: 'cmu_list_HSBA_DSTTPT_TONGHOP?url='
                + convertArray([singletonObject.dvtt, itemDDT.STT_BENHAN, itemDDT.MABENHNHAN, '-1', itemDDT.STT_DOTDIEUTRI]),
            type: "GET",
        }).done(function (dataTTPT) {
            if (dataTTPT.length > 0) {
                $("#hsba-ttpt-tab").show();
                $("#wrap_Menu_CLS").show();
                var distinctItems = distinctData(dataTTPT, ['SO_PHIEU_DICHVU', 'KHOA_CHI_DINH', 'BHYTKCHI', 'STT_DIEUTRI', 'NGAY_CHI_DINH', 'MA_LOAI_DICHVU']);
                distinctItems.forEach(function (itemDistinct) {
                    var arr = [itemDDT.MABENHNHAN,
                        itemDistinct.BHYTKCHI,
                        itemDistinct.SO_PHIEU_DICHVU,
                        itemDistinct.MA_LOAI_DICHVU,
                        0, //ret.CHUYEN_KHOA
                        0, //ret.CHI_TIET_CHUYEN_KHOA
                        singletonObject.dvtt,
                        itemDDT.SOBENHAN,
                        itemDDT.STT_BENHAN,
                        itemDDT.STT_DOTDIEUTRI,
                        itemDistinct.STT_DIEUTRI,
                        itemDDT.CHAN_DOAN,
                        itemDistinct.KHOA_CHI_DINH,
                        "",
                        "0",
                        itemDDT.SOVAOVIEN,
                        itemDDT.SOVAOVIEN_DT,
                        0];
                    var url = "noitru_inphieuttpt_svv?url=" + convertArray(arr);

                    getFilesign769(
                        "PHIEUCD_NOITRU_TTPT",
                        itemDistinct.SO_PHIEU_DICHVU,
                        -1,
                        singletonObject.dvtt,
                        itemDDT.SOVAOVIEN,
                        itemDDT.SOVAOVIEN_DT,
                        '-1'
                        , function (dataKySo) {
                            // var url = "noitru_inphieuxetnghiem_svv?url=" + convertArray(arrCD);
                            var keyEMR = itemDistinct.BHYTKHONGCHI == "0" ? "BM_PHIEU_CHIDINH_PTTT_BHYT_18" : "BM_PHIEU_CHIDINH_PTTT_150";
                            if (dataKySo.length > 0) {
                                getCMUFileSigned769GetLink(dataKySo[0].KEYMINIO, 'pdf').then(pdfData => {
                                    url = pdfData;
                                    thongtinhsba.thongtinbn.linkTTPT.push({
                                        "url": [url],
                                        "name": "Phiếu chỉ định TTPT " + itemDistinct.SO_PHIEU_DICHVU + " - " + itemDistinct.NGAY_CHI_DINH,
                                        "key": "CDTTPT",
                                        "ngayLap": itemDistinct.NGAY_CHI_DINH,
                                        "keyEMR": keyEMR,
                                        "idPhieu": dataKySo[0].ID,
                                    });
                                }).catch(error => {
                                    thongtinhsba.thongtinbn.linkTTPT.push({
                                        "url": [url],
                                        "name": "Phiếu chỉ định TTPT " + itemDistinct.SO_PHIEU_DICHVU + " - " + itemDistinct.NGAY_CHI_DINH,
                                        "key": "CDTTPT",
                                        "ngayLap": itemDistinct.NGAY_CHI_DINH
                                    });
                                });
                            } else {
                                thongtinhsba.thongtinbn.linkTTPT.push({
                                    "url": [url],
                                    "name": "Phiếu chỉ định TTPT " + itemDistinct.SO_PHIEU_DICHVU + " - " + itemDistinct.NGAY_CHI_DINH,
                                    "key": "CDTTPT",
                                    "ngayLap": itemDistinct.NGAY_CHI_DINH
                                });
                            }
                        });

                    // Phiếu kết quả
                    var dsTTPT = dataTTPT.filter(function (item) {
                        return itemDistinct.SO_PHIEU_DICHVU == item.SO_PHIEU_DICHVU
                    });
                    dsTTPT.forEach(function (item) {
                        var url3 = "pttt_select_ketqua_svv?url=" + convertArray(
                            [
                                item.SO_PHIEU_DICHVU,
                                1,
                                itemDDT.STT_BENHAN,
                                itemDDT.STT_DOTDIEUTRI,
                                item.STT_DIEUTRI,
                                '',
                                item.MA_DV,
                                singletonObject.dvtt,
                                "0",
                                0,
                                itemDDT.SOVAOVIEN,
                                itemDDT.SOVAOVIEN_DT
                            ]
                        );
                        $.getJSON(url3, function (result) {
                            var loaiTuongTrinh = result[0].LOAI_TUONGTRINH;
                            var arr = [
                                item.SO_PHIEU_DICHVU,
                                singletonObject.dvtt,
                                item.MA_DV,
                                1,
                                '',
                                itemDDT.STT_BENHAN,
                                itemDDT.STT_DOTDIEUTRI,
                                item.STT_DIEUTRI
                            ];
                            var param  = [
                                "sophieudichvu",
                                "dvtt",
                                "madv",
                                "noitru",
                                "makhambenh",
                                "sttbenhan",
                                "sttdotdieutri",
                                "sttdieutri"
                            ];
                            var url = "";
                            var tenPT = "";
                            if (loaiTuongTrinh == 'phieuphauthuat') {
                                arr = [
                                    item.MA_DV,
                                    item.SO_PHIEU_DICHVU,
                                    '',
                                    singletonObject.dvtt,
                                    1,
                                    itemDDT.STT_BENHAN,
                                    itemDDT.STT_DOTDIEUTRI,
                                    item.STT_DIEUTRI,
                                    itemDDT.SOVAOVIEN,
                                    itemDDT.TEN_BENH_NHAN,
                                    itemDDT.TUOI,
                                    itemDDT.GIOI_TINH_HT,
                                    "0"];
                                url = "inphieuthuthuatphauthuat_hinhmau?url=" + convertArray(arr) + "&&typefile=pdf";
                                // thongtinhsba.thongtinbn.linkTTPT.push({
                                //     "url": [url],
                                //     "name": "Phiếu kết quả TTPT " + itemDistinct.SO_PHIEU_DICHVU + " - " + itemDistinct.NGAY_CHI_DINH,
                                //     "key": "KQTTPT",
                                //     "ngayLap": itemDistinct.NGAY_CHI_DINH
                                // });
                            } else if (loaiTuongTrinh == 'phauthuatmong'){
                                tenPT = "_MONG"
                                url = "cmu_report_emr_rp_phieuphauthuat_mong_cmu?url=" + convertArray(arr)+"&param="+ convertArray(param)+"&loaifile=pdf";
                            } else if (loaiTuongTrinh == 'phauthuatsupmi'){
                                tenPT = "_SUPMI"
                                url = "cmu_report_emr_rp_phieuphauthuat_supmi_cmu?url=" + convertArray(arr)+"&param="+ convertArray(param)+"&loaifile=pdf";
                            } else if (loaiTuongTrinh == 'phauthuattuile'){
                                tenPT = "_TUILE"
                                url = "cmu_report_emr_rp_phieuphauthuat_tuile_cmu?url=" + convertArray(arr)+"&param="+ convertArray(param)+"&loaifile=pdf";
                            } else if (loaiTuongTrinh == 'phauthuatsapejko'){
                                tenPT = "_SAPEJKO"
                                url = "cmu_report_emr_rp_phieuphauthuat_sapejko_cmu?url=" + convertArray(arr)+"&param="+ convertArray(param)+"&loaifile=pdf";
                            } else if (loaiTuongTrinh == 'phauthuatlac'){
                                tenPT = "_LAC"
                                url = "cmu_report_emr_rp_phieuphauthuat_lac_cmu?url=" + convertArray(arr)+"&param="+ convertArray(param)+"&loaifile=pdf";
                            } else if (loaiTuongTrinh == 'phauthuatglocom'){
                                tenPT = "_GLOCOM"
                                url = "cmu_report_emr_rp_phieuphauthuat_glocom_cmu?url=" + convertArray(arr)+"&param="+ convertArray(param)+"&loaifile=pdf";
                            } else if (loaiTuongTrinh == 'phauthuatbematnhancau'){
                                tenPT = "_BEMATNHANCAU"
                                url = "cmu_report_emr_rp_phieuphauthuat_nhancau_cmu?url=" + convertArray(arr)+"&param="+ convertArray(param)+"&loaifile=pdf";
                            } else if (loaiTuongTrinh == 'phauthuatghepgiacmac'){
                                tenPT = "_GHEPGIACMAC"
                                url = "cmu_report_emr_rp_phieuphauthuat_ghepgiacmac_cmu?url=" + convertArray(arr)+"&param="+ convertArray(param)+"&loaifile=pdf";
                            } else if (loaiTuongTrinh == 'thethuytinh'){
                                tenPT = "_THETHUYTINH"
                                url = "cmu_report_emr_rp_phieuphauthuat_thuthuat_cmu?url=" + convertArray(arr)+"&param="+ convertArray(param)+"&loaifile=pdf";
                            }
                            getFilesign769(
                                "PHIEU_NOITRU_TRUONGTRINHPT" + tenPT,
                                item.MA_DV,
                                -1,//singletonObject.userId,
                                singletonObject.dvtt,
                                itemDDT.SOVAOVIEN,
                                itemDDT.SOVAOVIEN_DT,
                                -1,
                                function(dataKySo) {
                                    if(dataKySo.length > 0) {
                                        getCMUFileSigned769GetLink(dataKySo[0].KEYMINIO, 'pdf').then(pdfData => {
                                            url = pdfData;
                                            thongtinhsba.thongtinbn.linkTTPT.push({
                                                "url": [url],
                                                "name": "Phiếu kết quả TTPT " + itemDistinct.SO_PHIEU_DICHVU + " - " + itemDistinct.NGAY_CHI_DINH,
                                                "key": "KQTTPT",
                                                "ngayLap": itemDistinct.NGAY_CHI_DINH,
                                                // "keyEMR": keyEMR,
                                                // "idPhieu": dataKySo[0].ID,
                                            });

                                        }).catch(error => {
                                            thongtinhsba.thongtinbn.linkTTPT.push({
                                                "url": [url],
                                                "name": "Phiếu kết quả TTPT " + itemDistinct.SO_PHIEU_DICHVU + " - " + itemDistinct.NGAY_CHI_DINH,
                                                "key": "KQTTPT",
                                                "ngayLap": itemDistinct.NGAY_CHI_DINH
                                            });
                                        });
                                    } else {
                                        thongtinhsba.thongtinbn.linkTTPT.push({
                                            "url": [url],
                                            "name": "Phiếu kết quả TTPT " + itemDistinct.SO_PHIEU_DICHVU + " - " + itemDistinct.NGAY_CHI_DINH,
                                            "key": "KQTTPT",
                                            "ngayLap": itemDistinct.NGAY_CHI_DINH
                                        });
                                    }
                                }
                            )
                        })
                    })
                });
            } else {
                $("#hsba-ttpt-tab").hide();
            }
        });
    });
}

function xemDanhGiaDinhDuong(dsDotDieuTri) {
    thongtinhsba.thongtinbn.linkDGDD = [];
    dsDotDieuTri.forEach(function (itemDDT) {
        $.ajax({
            url: "cmu_getlist?url=" + convertArray([singletonObject.dvtt, itemDDT.STT_BENHAN, "CMU_GET_DSPHIEU_DANHGIA_DD"]),
            type: "GET",
        }).done(function (dataDGDD) {
            if (dataDGDD.length > 0) {
                $("#hsba-danhgiadinhduong-tab").show();
                $("#wrap_Menu_PhieuKhac").show();
                var today = new Date();
                var ngay = String(today.getDate()).padStart(2, '0');
                var thang = String(today.getMonth() + 1).padStart(2, '0');
                var nam = today.getFullYear();
                dataDGDD.forEach(function (item) {
                    var params = {
                        ID: item.MA_PHIEU,
                        dvtt: singletonObject.dvtt,
                        LOAI_PHIEU: item.LOAI_PHIEU,
                        ngay: ngay,
                        thang: thang,
                        nam: nam,
                        SOVAOVIEN: itemDDT.SOVAOVIEN,
                        SOVAOVIEN_DT: itemDDT.SOVAOVIEN_DT,
                        USER_ID: "-1"
                    }
                    getUrlDanhGiaDinhDuong(params).then(objReturn => {
                        if (objReturn.isError == 0) {
                            var objTemp = {
                                "url": [objReturn.url],
                                "name": "Phiếu đánh giá dinh dưỡng " + thongtinhsba.thongtinbn.TEN_BENH_NHAN,
                                "key": "DGDD",
                            }
                            if (objReturn.kySo == 1) {
                                objTemp = {
                                    ...objTemp,
                                    "keyEMR": "BM_PHIEU_SANGLOC_DINHDUONG_DTRI_155",
                                    "idPhieu": objReturn.idKySo,
                                }
                            }
                            thongtinhsba.thongtinbn.linkDGDD.push(objTemp);
                        } else {
                            notifiToClient("Red", "Phiếu đánh giá dinh dưỡng - " + thongtinhsba.thongtinbn.TEN_BENH_NHAN + ": " + objReturn.message);
                        }
                    }).catch(error => {
                        // notifiToClient("Red", error.message || "Lỗi không xác định");
                    });
                });
            } else {
                $("#hsba-danhgiadinhduong-tab").hide();
            }
        });
    });
}

function xemPhieuHoiChan(dsDotDieuTri) {
    thongtinhsba.thongtinbn.linkPHC = [];
    dsDotDieuTri.forEach(function (itemDDT) {
        $.ajax({
            url: 'cmu_getlist?url=' + convertArray([itemDDT.STT_BENHAN, '-1', singletonObject.dvtt, 'CMU_NOT_HOICHAN_DS_F']),
            type: "GET",
        }).done(function (dataPHC) {
            if (dataPHC.length > 0) {
                $("#hsba-phieuhoichan-tab").show();
                $("#wrap_Menu_PhieuKhac").show();
                dataPHC.forEach(function (item) {
                    var params = {
                        ID: item.ID_HOICHAN,
                        STT_HOICHAN: item.STT_HOICHAN,
                        dvtt: singletonObject.dvtt,
                        SOVAOVIEN: itemDDT.SOVAOVIEN,
                        SOVAOVIEN_DT: itemDDT.SOVAOVIEN_DT,
                    }
                    getUrlHoiChan(params).then(objReturn => {
                        if (objReturn.isError == 0) {
                            var objTemp = {
                                "url": [objReturn.url],
                                "name": "Phiếu hội chẩn - " + item.TENBIENBANHOICHAN,
                                "key": "PHIEUHOICHAN",
                            }
                            if (objReturn.kySo == 1) {
                                objTemp = {
                                    ...objTemp,
                                    "keyEMR": "BM_PHIEU_BIENBAN_HOICHAN_15",
                                    "idPhieu": objReturn.idKySo,
                                }
                            }
                            thongtinhsba.thongtinbn.linkPHC.push(objTemp);
                        } else {
                            notifiToClient("Red", "Phiếu hội chẩn - " + item.TENBIENBANHOICHAN + ": " + objReturn.message);
                        }
                    }).catch(error => {
                        // notifiToClient("Red", error.message || "Lỗi không xác định");
                    });
                });
            } else {
                $("#hsba-phieuhoichan-tab").hide();
            }
        });
    });
}

function xemPhieuSoKet15NgayDieuTri(dsDotDieuTri) {
    thongtinhsba.thongtinbn.linkSK15NDT = [];
    dsDotDieuTri.forEach(function (itemDDT) {
        $.ajax({
            url: 'cmu_getlist?url=' + convertArray([itemDDT.STT_BENHAN, itemDDT.SOVAOVIEN, -1, singletonObject.dvtt, 'CMU_SELECT_DS_DOTSOKET']),
            type: "GET",
        }).done(function (dataSK15NDT) {
            if(dataSK15NDT.length > 0) {
                $("#hsba-phieusoket15ngay-tab").show();
                $("#wrap_Menu_PhieuKhac").show();
                dataSK15NDT.forEach(function (item) {
                    var arr = [itemDDT.STT_BENHAN, itemDDT.SOVAOVIEN, item.DOT_SOKET, singletonObject.dvtt, itemDDT.TEN_BENH_NHAN, itemDDT.TUOI,
                        thongtinhsba.thongtinbn.GIOI_TINH_NUM == 1 ? 'true' : 'false', itemDDT.DIA_CHI, itemDDT.TEN_PHONGBAN, itemDDT.ICD_HT ? itemDDT.ICD_HT : " "];
                    var url = "vlg_inphieusoket?url=" + convertArray(arr) + '&viewPDF=1';

                    var arrTemp = [];
                    getFilesign769("PHIEU_NOITRU_SOKET15NGAY_TRUONGKHOA", item.DOT_SOKET, -1, singletonObject.dvtt,
                        itemDDT.SOVAOVIEN, itemDDT.SOVAOVIEN_DT, -1, function(dataKySo) {
                            if (dataKySo.length > 0) {
                                arrTemp.push(dataKySo[0])
                            }
                        });
                    getFilesign769("PHIEU_NOITRU_SOKET15NGAY_BACSI", item.DOT_SOKET, -1, singletonObject.dvtt,
                        itemDDT.SOVAOVIEN, itemDDT.SOVAOVIEN_DT, -1, function(dataKySo) {
                            if (dataKySo.length > 0) {
                                arrTemp.push(dataKySo[0])
                            }
                        });
                    var maxCreateDate = null;
                    var maxCreateDataObject = null;
                    if(arrTemp.length > 0) {
                        $.each(arrTemp, function(index, dataObject) {
                            var createDate = moment(dataObject.CREATE_DATE_STRING, "DD/MM/YYYY HH:mm:ss");
                            if (maxCreateDate === null || createDate.isAfter(maxCreateDate)) {
                                maxCreateDate = createDate;
                                maxCreateDataObject = dataObject;
                            }
                        });
                        getCMUFileSigned769GetLink(maxCreateDataObject.KEYMINIO, 'pdf').then(pdfData => {
                            url = pdfData;
                            thongtinhsba.thongtinbn.linkSK15NDT.push({
                                "url": [url],
                                "name": "Phiếu sơ kết 15 ngày điều trị " + item.DOT_SOKET,
                                "keyEMR": "BM_PHIEU_SOKET_DIEUTRI_14",
                                "key": "PHIEUSOKET15NGAYDIEUTRI",
                                "idPhieu": maxCreateDataObject.ID,
                            });
                        }).catch(error => {
                            thongtinhsba.thongtinbn.linkSK15NDT.push({
                                "url": [url],
                                "name": "Phiếu sơ kết 15 ngày điều trị " + item.DOT_SOKET,
                                "key": "PHIEUSOKET15NGAYDIEUTRI",
                            });
                        });
                    } else {
                        thongtinhsba.thongtinbn.linkSK15NDT.push({
                            "url": [url],
                            "name": "Phiếu sơ kết 15 ngày điều trị " + item.DOT_SOKET,
                            "key": "PHIEUSOKET15NGAYDIEUTRI",
                        });
                    }
                });
            } else {
                $("#hsba-phieusoket15ngay-tab").hide();
            }
        });
    });
}

function xemPhieuKhamChuyenKhoa(dsDotDieuTri) {
    thongtinhsba.thongtinbn.linkKhamChuyenKhoa = [];
    dsDotDieuTri.forEach(function (itemDDT) {
        $.ajax({
            url: 'cmu_getlist?url=' + convertArray([singletonObject.dvtt, itemDDT.MABENHNHAN, itemDDT.SOVAOVIEN, 'CMU_GET_DSPHIEU_KHAM_CK']),
            type: "GET",
        }).done(function (dataKCK) {
            if(dataKCK.length > 0) {
                $("#hsba-phieukhamchuyenkhoa-tab").show();
                $("#wrap_Menu_PhieuKhac").show();
                dataKCK.forEach(function (item) {
                    var params = {
                        ID: item.MA_PHIEU,
                        dvtt: singletonObject.dvtt,
                        SOVAOVIEN: itemDDT.SOVAOVIEN,
                        SOVAOVIEN_DT: itemDDT.SOVAOVIEN_DT,
                    }
                    getUrlKhamChuyenKhoa(params).then(objReturn => {
                        if (objReturn.isError == 0) {
                            var objTemp = {
                                "url": [objReturn.url],
                                "name": "Phiếu khám chuyên khoa - " + thongtinhsba.thongtinbn.TEN_BENH_NHAN,
                                "key": "PHIEUKHAMCHUYENKHOA",
                            }
                            if (objReturn.kySo == 1) {
                                objTemp = {
                                    ...objTemp,
                                    "keyEMR": "BM_PHIEU_KHAMCHUYENKHOA_77",
                                    "idPhieu": objReturn.idKySo,
                                }
                            }
                            thongtinhsba.thongtinbn.linkKhamChuyenKhoa.push(objTemp);
                        } else {
                            notifiToClient("Red", "Phiếu khám chuyên khoa - " + thongtinhsba.thongtinbn.TEN_BENH_NHAN + ": " + objReturn.message);
                        }
                    }).catch(error => {
                        // notifiToClient("Red", error.message || "Lỗi không xác định");
                    });
                });
            } else {
                $("#hsba-phieukhamchuyenkhoa-tab").hide();
            }
        });
    });
}

function xemPhieuYeuCauSuDungKhangSinh(dsDotDieuTri) {
    thongtinhsba.thongtinbn.linkPhieuYeuCauSuDungKhangSinh = [];
    dsDotDieuTri.forEach(function (itemDDT) {
        $.ajax({
            url: "cmu_getlist?url=" + convertArray([singletonObject.dvtt, itemDDT.SOVAOVIEN, "CMU_GET_PHIEU_YCSDKSINH"]),
            type: "GET",
        }).done(function (dataYCSDKS) {
            if(dataYCSDKS.length > 0) {
                $("#hsba-phieuyeucausudungkhangsinh-tab").show();
                $("#wrap_Menu_PhieuKhac").show();
                dataYCSDKS.forEach(function (item) {
                    var params = {
                        ID: item.ID,
                        dvtt: singletonObject.dvtt,
                        SOVAOVIEN: itemDDT.SOVAOVIEN,
                        SOVAOVIEN_DT: itemDDT.SOVAOVIEN_DT,
                    }
                    getUrlYCSDKhangSinh(params).then(objReturn => {
                        if (objReturn.isError == 0) {
                            var objTemp = {
                                "url": [objReturn.url],
                                "name": "Phiếu yêu cầu sử dụng kháng sinh - " + thongtinhsba.thongtinbn.TEN_BENH_NHAN,
                                "key": "PHIEUYCSDKS",
                            }
                            if (objReturn.kySo == 1) {
                                objTemp = {
                                    ...objTemp,
                                    "keyEMR": "BM_PHIEU_YC_SUDUNG_KHANGSINH_174",
                                    "idPhieu": objReturn.idKySo,
                                }
                            }
                            thongtinhsba.thongtinbn.linkPhieuYeuCauSuDungKhangSinh.push(objTemp);
                        } else {
                            notifiToClient("Red", "Phiếu yêu cầu sử dụng kháng sinh - " + thongtinhsba.thongtinbn.TEN_BENH_NHAN + ": " + objReturn.message);
                        }
                    }).catch(error => {
                        // notifiToClient("Red", error.message || "Lỗi không xác định");
                    });
                });
            } else {
                $("#hsba-phieuyeucausudungkhangsinh-tab").hide();
            }
        });
    });
}

function xemPhieuYeuCauSuDungKhangSinhUuTien(dsDotDieuTri) {
    thongtinhsba.thongtinbn.linkPhieuYeuCauSuDungKhangSinhUuTien = [];
    dsDotDieuTri.forEach(function (itemDDT) {
        $.ajax({
            url: "cmu_getlist?url=" + convertArray([singletonObject.dvtt, itemDDT.SOVAOVIEN, "CMU_GET_PHIEU_YCSDKSINHUT"]),
            type: "GET",
        }).done(function (dataYCSDKSUT) {
            if(dataYCSDKSUT.length > 0) {
                $("#hsba-phieuyeucausudungkhangsinhuutien-tab").show();
                $("#wrap_Menu_PhieuKhac").show();
                dataYCSDKSUT.forEach(function (item) {
                    var params = {
                        ID: item.ID,
                    }
                    getUrlYCSDKhangSinhUT(params).then(objReturn => {
                        if (objReturn.isError == 0) {
                            var objTemp = {
                                "url": [objReturn.url],
                                "name": "Phiếu yêu cầu sử dụng kháng sinh ưu tiên - " + thongtinhsba.thongtinbn.TEN_BENH_NHAN,
                                "key": "PHIEUYCSDKSUT",
                            }
                            if (objReturn.kySo == 1) {
                                objTemp = {
                                    ...objTemp,
                                    "keyEMR": "BM_PHIEU_YC_SUDUNG_KHANGSINH_174",
                                    "idPhieu": objReturn.idKySo,
                                }
                            }
                            thongtinhsba.thongtinbn.linkPhieuYeuCauSuDungKhangSinhUuTien.push(objTemp);
                        } else {
                            notifiToClient("Red", "Phiếu yêu cầu sử dụng kháng sinh ưu tiên - " + thongtinhsba.thongtinbn.TEN_BENH_NHAN + ": " + objReturn.message);
                        }
                    }).catch(error => {
                        // notifiToClient("Red", error.message || "Lỗi không xác định");
                    });
                });
            } else {
                $("#hsba-phieuyeucausudungkhangsinhuutien-tab").hide();
            }
        });
    });
}

function xemPhieuChuanBiTienPhau(dsDotDieuTri) {
    thongtinhsba.thongtinbn.linkPhieuChuanBiTienPhau = [];
    dsDotDieuTri.forEach(function (itemDDT) {
        $.ajax({
            url: "cmu_getlist?url=" + convertArray([singletonObject.dvtt, itemDDT.SOVAOVIEN, "CMU_GET_PHIEU_CBI_TPHAU"]),
            type: "GET",
        }).done(function (dataPhieu) {
            if(dataPhieu.length > 0) {
                $("#hsba-phieuchuanbitienphau-tab").show();
                $("#wrap_Menu_PhieuKhac").show();
                dataPhieu.forEach(function (item) {
                    var params = {
                        ID: item.ID,
                        dvtt: singletonObject.dvtt,
                        SOVAOVIEN: itemDDT.SOVAOVIEN,
                        SOVAOVIEN_DT: itemDDT.SOVAOVIEN_DT,
                        NGAY_TAO_PHIEU: item.NGAY_TAO_PHIEU,
                    }
                    getUrlChuanBiTienPhau(params).then(objReturn => {
                        if (objReturn.isError == 0) {
                            var objTemp = {
                                "url": [objReturn.url],
                                "name": "Phiếu chuẩn bị tiền phẫu - " + thongtinhsba.thongtinbn.TEN_BENH_NHAN,
                                "key": "PHIEUCHUANBITIENPHAU",
                            }
                            if (objReturn.kySo == 1) {
                                objTemp = {
                                    ...objTemp,
                                    "keyEMR": "BM_PHIEU_BANGKIEM_BENHNHAN_TRUOCMO_172",
                                    "idPhieu": objReturn.idKySo,
                                }
                            }
                            thongtinhsba.thongtinbn.linkPhieuChuanBiTienPhau.push(objTemp);
                        } else {
                            notifiToClient("Red", "Phiếu chuẩn bị tiền phẫu - " + thongtinhsba.thongtinbn.TEN_BENH_NHAN + ": " + objReturn.message);
                        }
                    }).catch(error => {
                        // notifiToClient("Red", error.message || "Lỗi không xác định");
                    });
                });
            } else {
                $("#hsba-phieuchuanbitienphau-tab").hide();
            }
        });
    });
}

function xemPhieuTheoDoiChayThan(dsDotDieuTri) {
    thongtinhsba.thongtinbn.linkPhieuTheoDoiChayThan = [];
    dsDotDieuTri.forEach(function (itemDDT) {
        $.ajax({
            url: 'cmu_getlist?url=' + convertArray([singletonObject.dvtt, itemDDT.MABENHNHAN, itemDDT.SOVAOVIEN, 'CMU_GET_DSPHIEU_TD_CHAY_THAN']),
            type: "GET",
        }).done(function (dataPhieu) {
            if(dataPhieu.length > 0) {
                $("#hsba-theodoichaythan-tab").show();
                $("#wrap_Menu_PhieuKhac").show();
                dataPhieu.forEach(function (item) {
                    var params = {
                        ID: item.MA_PHIEU,
                        dvtt: singletonObject.dvtt,
                        SOVAOVIEN: itemDDT.SOVAOVIEN,
                        SOVAOVIEN_DT: itemDDT.SOVAOVIEN_DT,
                    }
                    getUrlTheoDoiChayThan(params).then(objReturn => {
                        if (objReturn.isError == 0) {
                            var objTemp = {
                                "url": [objReturn.url],
                                "name": "Phiếu theo dõi chạy thận - " + thongtinhsba.thongtinbn.TEN_BENH_NHAN,
                                "key": "PHIEUTHEODOICHAYTHAN",
                            }
                            if (objReturn.kySo == 1) {
                                objTemp = {
                                    ...objTemp,
                                    "keyEMR": "BM_PHIEU_THEODOI_CHAYTHAN_157",
                                    "idPhieu": objReturn.idKySo,
                                }
                            }
                            thongtinhsba.thongtinbn.linkPhieuTheoDoiChayThan.push(objTemp);
                        } else {
                            notifiToClient("Red", "Phiếu theo dõi chạy thận - " + thongtinhsba.thongtinbn.TEN_BENH_NHAN + ": " + objReturn.message);
                        }
                    }).catch(error => {
                        // notifiToClient("Red", error.message || "Lỗi không xác định");
                    });
                });
            } else {
                $("#hsba-theodoichaythan-tab").hide();
            }
        });
    });
}

function biaBenhAnTong(){
    thongtinhsba.thongtinbn.linkBiaBATong = [];
    var params = {
        khoa: thongtinhsba.thongtinbn.TINHTRANG_RAVIEN ? thongtinhsba.thongtinbn.PHONGBAN_RAVIEN : thongtinhsba.thongtinbn.TEN_PHONGBAN,
        tenbenhnhan: thongtinhsba.thongtinbn.TEN_BENH_NHAN,
        sogiuong: thongtinhsba.thongtinbn.TINHTRANG_RAVIEN ? thongtinhsba.thongtinbn.SO_GIUONG_BENH : thongtinhsba.thongtinbn.SOGIUONG,
        gioitinh: thongtinhsba.thongtinbn.TINHTRANG_RAVIEN ? thongtinhsba.thongtinbn.GIOI_TINH_NUM : thongtinhsba.thongtinbn.GIOI_TINH,
        sobenhan: thongtinhsba.thongtinbn.SOBENHAN,
        mabenhnhan: thongtinhsba.thongtinbn.MABENHNHAN,
        ngaysinh: thongtinhsba.thongtinbn.NGAY_SINH,
        ngayvaovien: thongtinhsba.thongtinbn.TINHTRANG_RAVIEN ? thongtinhsba.thongtinbn.NGAYNHAPVIEN : thongtinhsba.thongtinbn.NGAY_VAO_VIEN,
        ngayravien: thongtinhsba.thongtinbn.TINHTRANG_RAVIEN ? thongtinhsba.thongtinbn.THOIGIAN_RAVIEN : thongtinhsba.thongtinbn.NGAY_RA_VIEN,
    }
    var url = 'cmu_in_cmu_biahsba?type=pdf&' + $.param(params);
    thongtinhsba.thongtinbn.linkBiaBATong.push({
        "url": [url],
        "name": "Bìa bệnh án tổng ",
        "key": "BIABENHAN",
    });
}

function xemNghiemPhapRoiLoanNuot(dsDotDieuTri) {
    thongtinhsba.thongtinbn.linkNghiemPhapRoiLoanNuot = [];
    dsDotDieuTri.forEach(function (itemDDT) {
        $.ajax({
            url: "cmu_getlist?url=" + convertArray([singletonObject.dvtt, itemDDT.SOVAOVIEN, "CMU_GET_DANHGIAROILOANNUOT"]),
            type: "GET",
        }).done(function (dataPhieu) {
            if(dataPhieu.length > 0) {
                $("#hsba-danhgiaroiloannuot-tab").show();
                $("#wrap_Menu_PhieuKhac").show();
                dataPhieu.forEach(function (item) {
                    var text_date_part = item.NGAY_TAO_PHIEU.split("/");
                    var text_date = text_date_part[0];
                    var text_month = text_date_part[1];
                    var text_year = text_date_part[2];
                    var params = {
                        magiay: item.ID,
                        mabenhnhan: item.MABENHNHAN,
                        text_date,
                        text_month,
                        text_year
                    }
                    var url = 'cmu_in_cmu_danhgiaroiloannuot?type=pdf&' + $.param(params);
                    var arrTemp = [];
                    getFilesign769("PHIEU_NOITRU_DANHGIAROILOANNUOT_BACSI", item.ID, -1, singletonObject.dvtt,
                        itemDDT.SOVAOVIEN, itemDDT.SOVAOVIEN_DT, -1, function(dataKySo) {
                            if (dataKySo.length > 0) {
                                arrTemp.push(dataKySo[0])
                            }
                        });
                    getFilesign769("PHIEU_NOITRU_DANHGIAROILOANNUOT_DIEUDUONG", item.ID, -1, singletonObject.dvtt,
                        itemDDT.SOVAOVIEN, itemDDT.SOVAOVIEN_DT, -1, function(dataKySo) {
                            if (dataKySo.length > 0) {
                                arrTemp.push(dataKySo[0])
                            }
                        });
                    var maxCreateDate = null;
                    var maxCreateDataObject = null;
                    if(arrTemp.length > 0) {
                        $.each(arrTemp, function(index, dataObject) {
                            var createDate = moment(dataObject.CREATE_DATE_STRING, "DD/MM/YYYY HH:mm:ss");
                            if (maxCreateDate === null || createDate.isAfter(maxCreateDate)) {
                                maxCreateDate = createDate;
                                maxCreateDataObject = dataObject;
                            }
                        });
                        getCMUFileSigned769GetLink(maxCreateDataObject.KEYMINIO, 'pdf').then(pdfData => {
                            url = pdfData;
                            thongtinhsba.thongtinbn.linkNghiemPhapRoiLoanNuot.push({
                                "url": [url],
                                "name": "Nghiệm pháp đánh giá rối loạn nuốt " + item.ID,
                                "keyEMR": "RPT_SCAN",
                                "key": "NGHIEMPHAPROILOANNUOT",
                                "idPhieu": maxCreateDataObject.ID,
                            });
                        }).catch(error => {
                            thongtinhsba.thongtinbn.linkNghiemPhapRoiLoanNuot.push({
                                "url": [url],
                                "name": "Nghiệm pháp đánh giá rối loạn nuốt " + item.ID,
                                "key": "NGHIEMPHAPROILOANNUOT",
                            });
                        });
                    } else {
                        thongtinhsba.thongtinbn.linkNghiemPhapRoiLoanNuot.push({
                            "url": [url],
                            "name": "Nghiệm pháp đánh giá rối loạn nuốt " + item.ID,
                            "key": "NGHIEMPHAPROILOANNUOT",
                        });
                    }
                });
            } else {
                $("#hsba-danhgiaroiloannuot-tab").hide();
            }
        });
    });
}

function xemGiayCamDoanPTTT(dsDotDieuTri) {
    thongtinhsba.thongtinbn.linkGiayCamDoanPTTT = [];
    dsDotDieuTri.forEach(function (itemDDT) {
        $.ajax({
            url: 'cmu_getlist?url=' + convertArray([singletonObject.dvtt, itemDDT.SOVAOVIEN, 'CMU_GET_GIAYCDPHAUTHUAT']),
            type: "GET",
        }).done(function (dataPhieu) {
            if(dataPhieu.length > 0) {
                $("#hsba-giaycamdoanpttt-tab").show();
                $("#wrap_Menu_PhieuKhac").show();
                dataPhieu.forEach(function (item) {
                    var params = {
                        ID: item.ID,
                        dvtt: singletonObject.dvtt,
                        SOVAOVIEN: itemDDT.SOVAOVIEN,
                        SOVAOVIEN_DT: itemDDT.SOVAOVIEN_DT,
                    }
                    getUrlCamDoanChapNhanPTTT(params).then(objReturn => {
                        if (objReturn.isError == 0) {
                            var objTemp = {
                                "url": [objReturn.url],
                                "name": "Giấy cam đoan chấp nhận phẫu thuật, thủ thuật - " + thongtinhsba.thongtinbn.TEN_BENH_NHAN,
                                "key": "CAMDOANPHAUTHUAT",
                            }
                            if (objReturn.kySo == 1) {
                                objTemp = {
                                    ...objTemp,
                                    "keyEMR": "RPT_SCAN",
                                    "idPhieu": objReturn.idKySo,
                                }
                            }
                            thongtinhsba.thongtinbn.linkGiayCamDoanPTTT.push(objTemp);
                        } else {
                            notifiToClient("Red", "Giấy cam đoan chấp nhận phẫu thuật, thủ thuật - " + thongtinhsba.thongtinbn.TEN_BENH_NHAN + ": " + objReturn.message);
                        }
                    }).catch(error => {
                        // notifiToClient("Red", error.message || "Lỗi không xác định");
                    });
                });
            } else {
                $("#hsba-giaycamdoanpttt-tab").hide();
            }
        });
    });
}

function xemGiayCamDoanChayThan(dsDotDieuTri) {
    thongtinhsba.thongtinbn.linkGiayCamDoanChayThan = [];
    dsDotDieuTri.forEach(function (itemDDT) {
        $.ajax({
            url: "cmu_getlist?url=" + convertArray([singletonObject.dvtt, itemDDT.SOVAOVIEN, "CMU_GET_GIAYCAMDOAN"]),
            type: "GET",
        }).done(function (dataPhieu) {
            if(dataPhieu.length > 0) {
                $("#hsba-giaycamdoanchaythan-tab").show();
                $("#wrap_Menu_PhieuKhac").show();
                dataPhieu.forEach(function (item) {
                    var params = {
                        ID: item.ID,
                        dvtt: singletonObject.dvtt,
                        SOVAOVIEN: itemDDT.SOVAOVIEN,
                        SOVAOVIEN_DT: itemDDT.SOVAOVIEN_DT,
                        NGAY_TAO_PHIEU: item.NGAY_TAO_PHIEU,
                    }
                    getUrlCamDoanChayThanNT(params).then(objReturn => {
                        if (objReturn.isError == 0) {
                            var objTemp = {
                                "url": [objReturn.url],
                                "name": "Giấy cam đoan chấp nhận chạy thận - " + thongtinhsba.thongtinbn.TEN_BENH_NHAN,
                                "key": "CAMDOANCHAYTHAN",
                            }
                            if (objReturn.kySo == 1) {
                                objTemp = {
                                    ...objTemp,
                                    "keyEMR": "BM_PHIEU_CAMKETCHUNG_146",
                                    "idPhieu": objReturn.idKySo,
                                }
                            }
                            thongtinhsba.thongtinbn.linkGiayCamDoanChayThan.push(objTemp);
                        } else {
                            notifiToClient("Red", "Giấy cam đoan chấp nhận chạy thận - " + thongtinhsba.thongtinbn.TEN_BENH_NHAN + ": " + objReturn.message);
                        }
                    }).catch(error => {
                        // notifiToClient("Red", error.message || "Lỗi không xác định");
                    });
                });
            }
        });
    });
}

function xemGiayCamDoanGayMe(dsDotDieuTri) {
    thongtinhsba.thongtinbn.linkGiayCamDoanGayMe = [];
    dsDotDieuTri.forEach(function (itemDDT) {
        $.ajax({
            url: "cmu_getlist?url=" + convertArray([singletonObject.dvtt, itemDDT.SOVAOVIEN, "CMU_GET_GIAYCDGAYME"]),
            type: "GET",
        }).done(function (dataPhieu) {
            if(dataPhieu.length > 0) {
                $("#hsba-giaycamdoangayme-tab").show();
                $("#wrap_Menu_PhieuKhac").show();
                dataPhieu.forEach(function (item) {
                    var params = {
                        ID: item.ID,
                        dvtt: singletonObject.dvtt,
                        SOVAOVIEN: itemDDT.SOVAOVIEN,
                        SOVAOVIEN_DT: itemDDT.SOVAOVIEN_DT,
                        NGAY_TAO_PHIEU: item.NGAY_TAO_PHIEU,
                    }
                    getUrlCamDoanGayMe(params).then(objReturn => {
                        if (objReturn.isError == 0) {
                            var objTemp = {
                                "url": [objReturn.url],
                                "name": "Giấy cam đoan chấp nhận gây mê - " + thongtinhsba.thongtinbn.TEN_BENH_NHAN,
                                "key": "CAMDOANGAYME",
                            }
                            if (objReturn.kySo == 1) {
                                objTemp = {
                                    ...objTemp,
                                    "keyEMR": "BM_PHIEU_CAMKETCHUNG_146",
                                    "idPhieu": objReturn.idKySo,
                                }
                            }
                            thongtinhsba.thongtinbn.linkGiayCamDoanGayMe.push(objTemp);
                        } else {
                            notifiToClient("Red", "Giấy cam đoan chấp nhận gây mê - " + thongtinhsba.thongtinbn.TEN_BENH_NHAN + ": " + objReturn.message);
                        }
                    }).catch(error => {
                        // notifiToClient("Red", error.message || "Lỗi không xác định");
                    });
                });
            }
        });
    });
}

function xemGiaiPhauBenhSinhThiet(dsDotDieuTri) {
    thongtinhsba.thongtinbn.linkGiaiPhauBenhSinhThiet = [];
    thongtinhsba.thongtinbn.linkKetQuaGiaiPhauBenhSinhThiet = [];
    dsDotDieuTri.forEach(function (itemDDT) {
        $.ajax({
            url: 'cmu_getlist?url=' + convertArray([itemDDT.STT_BENHAN, itemDDT.SOVAOVIEN,
                itemDDT.SOVAOVIEN_DT, '-1', itemDDT.DVTT, 'CMU_GIAIPHAUBENHST_SELECT_L2']),
            type: "GET",
        }).done(function (dataPhieu) {
            if(dataPhieu.length > 0) {
                $("#hsba-giaiphaubenhsinhthiet-tab").show();
                $("#wrap_Menu_PhieuKhac").show();
                dataPhieu.forEach(function (item) {
                    var arr = [item.ID, itemDDT.DVTT, itemDDT.STT_BENHAN];
                    var param  = ["idgpb", "dvtt", "stt_benhan"];
                    var url = "cmu_report_cmu_phieugiaiphaubenhsinhthiet_1?url=" + convertArray(arr)+"&param="+ convertArray(param)+"&loaifile=pdf";
                    getFilesign769v2({
                        dvtt: singletonObject.dvtt,
                        soVaoVien: itemDDT.SOVAOVIEN,
                        soVaoVien_DT: itemDDT.SOVAOVIEN_DT,
                        kyHieuPhieu: "PHIEU_NOITRU_GIAIPHAUBENH",
                        soPhieu: item.ID,
                        userId: -1,
                        soBenhAn: itemDDT.STT_BENHAN,
                        nghiepVu: 'TRANG1',
                    },function(dataKySo) {
                        if(dataKySo.length > 0) {
                            getCMUFileSigned769GetLink(dataKySo[0].KEYMINIO, 'pdf').then(pdfData => {
                                url = pdfData;
                                thongtinhsba.thongtinbn.linkGiaiPhauBenhSinhThiet.push({
                                    "url": [url],
                                    "name": "Phiếu giải phẩu bệnh sinh thiết trang 1 " + thongtinhsba.thongtinbn.TEN_BENH_NHAN,
                                    "keyEMR": "BM_PHIEU_KETQUA_XETNGHIEM_SINHTHIET_17",
                                    "key": "GIAIPHAUBENHSINHTHIET",
                                    "idPhieu": dataKySo[0].ID,
                                });
                            }).catch(error => {
                                thongtinhsba.thongtinbn.linkGiaiPhauBenhSinhThiet.push({
                                    "url": [url],
                                    "name": "Phiếu giải phẩu bệnh sinh thiết trang 1 " + thongtinhsba.thongtinbn.TEN_BENH_NHAN,
                                    "key": "GIAIPHAUBENHSINHTHIET",
                                });
                            });
                        } else {
                            thongtinhsba.thongtinbn.linkGiaiPhauBenhSinhThiet.push({
                                "url": [url],
                                "name": "Phiếu giải phẩu bệnh sinh thiết trang 1 " + thongtinhsba.thongtinbn.TEN_BENH_NHAN,
                                "key": "GIAIPHAUBENHSINHTHIET",
                            });
                        }
                    });
                    if (item.TRANG_THAI == 2) {
                        var arr2 = [itemDDT.DVTT, item.ID, itemDDT.STT_BENHAN];
                        var param2  = ["dvtt", "idgpb", "stt_benhan"];
                        var url2 = "cmu_report_cmu_phieugiaiphaubenhsinhthiet_2?url=" + convertArray(arr)+"&param="+ convertArray(param)+"&loaifile=pdf";
                        getFilesign769v2({
                            dvtt: singletonObject.dvtt,
                            soVaoVien: itemDDT.SOVAOVIEN,
                            soVaoVien_DT: itemDDT.SOVAOVIEN_DT,
                            kyHieuPhieu: "PHIEU_NOITRU_GIAIPHAUBENH_TRANG2",
                            soPhieu: item.ID,
                            userId: -1,
                            soBenhAn: itemDDT.STT_BENHAN,
                            nghiepVu: 'TRANG2',
                        },function(dataKS) {
                            if(dataKS.length > 0) {
                                getCMUFileSigned769GetLink(dataKS[0].KEYMINIO, 'pdf').then(pdfData => {
                                    url2 = pdfData;
                                    thongtinhsba.thongtinbn.linkKetQuaGiaiPhauBenhSinhThiet.push({
                                        "url": [url2],
                                        "name": "Phiếu giải phẩu bệnh sinh thiết trang 2 " + thongtinhsba.thongtinbn.TEN_BENH_NHAN,
                                        "keyEMR": "BM_PHIEU_KETQUA_XETNGHIEM_SINHTHIET_17",
                                        "key": "KQGIAIPHAUBENHSINHTHIET",
                                        "idPhieu": dataKS[0].ID,
                                    });
                                }).catch(error => {
                                    thongtinhsba.thongtinbn.linkKetQuaGiaiPhauBenhSinhThiet.push({
                                        "url": [url2],
                                        "name": "Phiếu giải phẩu bệnh sinh thiết trang 2 " + thongtinhsba.thongtinbn.TEN_BENH_NHAN,
                                        "key": "KQGIAIPHAUBENHSINHTHIET",
                                    });
                                });
                            } else {
                                thongtinhsba.thongtinbn.linkKetQuaGiaiPhauBenhSinhThiet.push({
                                    "url": [url2],
                                    "name": "Phiếu giải phẩu bệnh sinh thiết trang 2 " + thongtinhsba.thongtinbn.TEN_BENH_NHAN,
                                    "key": "KQGIAIPHAUBENHSINHTHIET",
                                });
                            }
                        });
                    }
                });
            }
        });
    });
}

function loadDSLanPhauThuatHSBATong(dsDotDieuTri) {
    return new Promise((resolve, reject) => {
        var arrTemp = [];
        dsDotDieuTri.forEach(function (itemDDT) {
            $.ajax({
                url: "cmu_getlist?url=" + convertArray([singletonObject.dvtt, itemDDT.MABENHNHAN, itemDDT.SOVAOVIEN, "CMU_LANPHAUTHUAT_SEL"]),
                type: "GET",
                async: false,
            }).done(function (data) {
                if(data.length > 0) {
                    data.forEach(function (item) {
                        arrTemp.push(item);
                    });
                }
            }).fail(function(error) {
                reject(error);
            });
        });
        resolve(arrTemp);
    });
}

function xemBangKiemTruocPhauThuat(dsLanPhauThuat) {
    thongtinhsba.thongtinbn.linkKiemTruocPhauThuat = [];
    dsLanPhauThuat.forEach(function (lanPhauThuatObject) {
        $.get("cmu_getlist?url=" + convertArray([singletonObject.dvtt, lanPhauThuatObject.ID, "CMU_GET_BANG_KTRUOC_PT"])).done(function(data) {
            if(data.length > 0) {
                $("#hsba-bangkiemtruoc-tab").show();
                $("#wrap_Menu_PhieuPhauThuat").show();
                var params = {
                    ID: data[0].ID,
                    ID_LPT: lanPhauThuatObject.ID,
                }
                getUrlKiemTruocPhauThuat(params).then(objReturn => {
                    if (objReturn.isError == 0) {
                        var objTemp = {
                            "url": [objReturn.url],
                            "name": "Bảng kiểm trước - " + thongtinhsba.thongtinbn.TEN_BENH_NHAN,
                            "key": "BANGKIEMTRUOC",
                        }
                        if (objReturn.kySo == 1) {
                            objTemp = {
                                ...objTemp,
                                "keyEMR": "BM_PHIEU_KIEMSOAT_TRUOCMO_123",
                                "idPhieu": objReturn.idKySo,
                            }
                        }
                        thongtinhsba.thongtinbn.linkKiemTruocPhauThuat.push(objTemp);
                    } else {
                        notifiToClient("Red", "Bảng kiểm trước - " + thongtinhsba.thongtinbn.TEN_BENH_NHAN + ": " + objReturn.message);
                    }
                }).catch(error => {
                    // notifiToClient("Red", error.message || "Lỗi không xác định");
                });
            }
        }).fail(function() {
            notifiToClient("Red", "Lỗi lấy dữ liệu");
        })
    });
}

function xemBangKiemAnToan(dsLanPhauThuat) {
    thongtinhsba.thongtinbn.linkBangKiemAnToan = [];
    dsLanPhauThuat.forEach(function (lanPhauThuatObject) {
        $.get("cmu_getlist?url=" + convertArray([singletonObject.dvtt, lanPhauThuatObject.ID, "PHIEU_KTAT_PHAU_THUAT_SEL"])).done(function(data) {
            if(data.length > 0) {
                $("#hsba-bangkiemantoan-tab").show();
                $("#wrap_Menu_PhieuPhauThuat").show();
                var params = {
                    ID: data[0].ID_BANG_KIEM_AN_TOAN_PT,
                    ID_LPT: lanPhauThuatObject.ID,
                }
                getUrlBangKiemAnToan(params).then(objReturn => {
                    if (objReturn.isError == 0) {
                        var objTemp = {
                            "url": [objReturn.url],
                            "name": "Bảng kiểm an toàn - " + thongtinhsba.thongtinbn.TEN_BENH_NHAN,
                            "key": "BANGKIEMANTOAN",
                        }
                        if (objReturn.kySo == 1) {
                            objTemp = {
                                ...objTemp,
                                "keyEMR": "BM_PHIEU_BANGKIEM_ATPTTT_86",
                                "idPhieu": objReturn.idKySo,
                            }
                        }
                        thongtinhsba.thongtinbn.linkBangKiemAnToan.push(objTemp);
                    } else {
                        notifiToClient("Red", "Bảng kiểm an toàn - " + thongtinhsba.thongtinbn.TEN_BENH_NHAN + ": " + objReturn.message);
                    }
                }).catch(error => {
                    // notifiToClient("Red", error.message || "Lỗi không xác định");
                });
            }
        }).fail(function() {
            notifiToClient("Red", "Lỗi lấy dữ liệu");
        })
    });
}

function xemBangKiemGac(dsLanPhauThuat) {
    thongtinhsba.thongtinbn.linkBangKiemGac = [];
    dsLanPhauThuat.forEach(function (lanPhauThuatObject) {
        $.get("cmu_getlist?url=" + convertArray([singletonObject.dvtt, lanPhauThuatObject.ID, "CMU_BANG_KIEM_GAC_LANKT_GET"])).done(function(data) {
            if(data.length > 0) {
                $("#hsba-bangkiemgac-tab").show();
                $("#wrap_Menu_PhieuPhauThuat").show();
                var params = {
                    ID: data[0].ID,
                    ID_LPT: lanPhauThuatObject.ID,
                }
                getUrlBangKiemGac(params).then(objReturn => {
                    if (objReturn.isError == 0) {
                        var objTemp = {
                            "url": [objReturn.url],
                            "name": "Bảng kiểm gạc - " + thongtinhsba.thongtinbn.TEN_BENH_NHAN,
                            "key": "BANGKIEMGAC",
                        }
                        if (objReturn.kySo == 1) {
                            objTemp = {
                                ...objTemp,
                                "keyEMR": "BM_PHIEU_BANGKIEM_BONGGIAC_87",
                                "idPhieu": objReturn.idKySo,
                            }
                        }
                        thongtinhsba.thongtinbn.linkBangKiemGac.push(objTemp);
                    } else {
                        notifiToClient("Red", "Bảng kiểm gạc - " + thongtinhsba.thongtinbn.TEN_BENH_NHAN + ": " + objReturn.message);
                    }
                }).catch(error => {
                    // notifiToClient("Red", error.message || "Lỗi không xác định");
                });
            }
        }).fail(function() {
            notifiToClient("Red", "Lỗi lấy dữ liệu");
        })
    });
}

function xemNoiQuyBenhVien(dsDotDieuTri) {
    thongtinhsba.thongtinbn.linkNoiQuyBenhVien = [];
    dsDotDieuTri.forEach(function (itemDDT) {
        $.ajax({
            url: "cmu_getlist?url=" + convertArray([singletonObject.dvtt, itemDDT.SOVAOVIEN, 'CMU_HDTH_NOIQUY_GET']),
            type: "GET",
        }).done(function (dataPhieu) {
            if(dataPhieu.length > 0) {
                $("#hsba-noiquybenhvien-tab").show();
                $("#wrap_Menu_PhieuKhac").show();
                dataPhieu.forEach(function (item) {
                    var params = {
                        ID: item.ID,
                        dvtt: singletonObject.dvtt,
                        SOVAOVIEN: itemDDT.SOVAOVIEN,
                        SOVAOVIEN_DT: itemDDT.SOVAOVIEN_DT,
                        MA_BENH_NHAN: itemDDT.MABENHNHAN,
                    }
                    getUrlHuongDanNoiQuy(params).then(objReturn => {
                        if (objReturn.isError == 0) {
                            var objTemp = {
                                "url": [objReturn.url],
                                "name": "Nội quy bệnh viện - " + thongtinhsba.thongtinbn.TEN_BENH_NHAN,
                                "key": "NOIQUYBENHVIEN",
                            }
                            if (objReturn.kySo == 1) {
                                objTemp = {
                                    ...objTemp,
                                    "keyEMR": "BM_PHIEU_CAMKETCHUNG_146",
                                    "idPhieu": objReturn.idKySo,
                                }
                            }
                            thongtinhsba.thongtinbn.linkNoiQuyBenhVien.push(objTemp);
                        } else {
                            notifiToClient("Red", "Nội quy bệnh viện - " + thongtinhsba.thongtinbn.TEN_BENH_NHAN + ": " + objReturn.message);
                        }
                    }).catch(error => {
                        // notifiToClient("Red", error.message || "Lỗi không xác định");
                    });
                });
            }
        });
    });
}

function xemLoetTiDe(dsDotDieuTri) {
    thongtinhsba.thongtinbn.linkLoetTiDe = [];
    dsDotDieuTri.forEach(function (itemDDT) {
        $.ajax({
            url: "cmu_getlist?url=" + convertArray([singletonObject.dvtt, itemDDT.SOVAOVIEN, itemDDT.SOVAOVIEN_DT,
                itemDDT.STT_BENHAN, itemDDT.MABENHNHAN, 'CMU_PHIEULOETTIDE_SEL']),
            type: "GET",
        }).done(function (dataPhieu) {
            if(dataPhieu.length > 0) {
                $("#hsba-loettide-tab").show();
                $("#wrap_Menu_PhieuKhac").show();
                dataPhieu.forEach(function (item) {
                    var params = {
                        ID: item.ID_PHIEULOETTIDE,
                    }
                    getUrlLoetTiDe(params).then(objReturn => {
                        if (objReturn.isError == 0) {
                            var objTemp = {
                                "url": [objReturn.url],
                                "name": "Phiếu loét tì đè - " + thongtinhsba.thongtinbn.TEN_BENH_NHAN,
                                "key": "PHIEULOETTIDE",
                            }
                            if (objReturn.kySo == 1) {
                                objTemp = {
                                    ...objTemp,
                                    "keyEMR": "BM_PHIEU_SANGLOC_LOET_TYDE_190",
                                    "idPhieu": objReturn.idKySo,
                                }
                            }
                            thongtinhsba.thongtinbn.linkLoetTiDe.push(objTemp);
                        } else {
                            notifiToClient("Red", "Phiếu loét tì đè - " + thongtinhsba.thongtinbn.TEN_BENH_NHAN + ": " + objReturn.message);
                        }
                    }).catch(error => {
                        // notifiToClient("Red", error.message || "Lỗi không xác định");
                    });
                });
            }
        });
    });
}

function xemChanDoanNguyenNhanTV(dsDotDieuTri) {
    thongtinhsba.thongtinbn.linkChanDoanNguyenNhanTV = [];
    dsDotDieuTri.forEach(function (itemDDT) {
        $.ajax({
            url: "cmu_getlist?url=" + convertArray([singletonObject.dvtt, thongtinhsba.thongtinbn.SOVAOVIEN, "CMU_CD_NGUYEN_NHAN_TU_VONG_LST"]),
            type: "GET",
        }).done(function (dataPhieu) {
            if(dataPhieu.length > 0) {
                $("#hsba-chandoannguyennhantv-tab").show();
                $("#wrap_Menu_PhieuKhac").show();
                dataPhieu.forEach(function (item) {
                    var params = {
                        ID: item.ID,
                    }
                    getUrlChuanDoanNguyenNhanTV(params).then(objReturn => {
                        if (objReturn.isError == 0) {
                            var objTemp = {
                                "url": [objReturn.url],
                                "name": "Phiếu chẩn đoán nguyên nhân tử vong - " + thongtinhsba.thongtinbn.TEN_BENH_NHAN,
                                "key": "CHANDOANNGUYENNHANTUVONG",
                            }
                            if (objReturn.kySo == 1) {
                                objTemp = {
                                    ...objTemp,
                                    "keyEMR": "BM_PHIEU_KIEMTHAO_TUVONG_119",
                                    "idPhieu": objReturn.idKySo,
                                }
                            }
                            thongtinhsba.thongtinbn.linkChanDoanNguyenNhanTV.push(objTemp);
                        } else {
                            // notifiToClient("Red", "Phiếu khám tiền mê - " + thongtinhsba.thongtinbn.TEN_BENH_NHAN + ": " + objReturn.message);
                        }
                    }).catch(error => {
                        // notifiToClient("Red", error.message || "Lỗi không xác định");
                    });
                });
            } else {
                $("#hsba-chandoannguyennhantv-tab").hide();
            }
        });
    });
}

function xemKhamTienMe(dsLanPhauThuat) {
    thongtinhsba.thongtinbn.linkKhamTienMe = [];
    dsLanPhauThuat.forEach(function (lanPhauThuatObject) {
        $.get("cmu_getlist?url=" + convertArray([singletonObject.dvtt, lanPhauThuatObject.ID, "CMU_GET_DSPHIEU_KHAM_TIEN_ME"])).done(function(data) {
            if(data.length > 0) {
                $("#hsba-khamtienme-tab").show();
                $("#wrap_Menu_PhieuPhauThuat").show();
                var params = {
                    ID: data[0].MA_PHIEU,
                }
                getUrlKhamTienMe(params).then(objReturn => {
                    if (objReturn.isError == 0) {
                        var objTemp = {
                            "url": [objReturn.url],
                            "name": "Phiếu khám tiền mê - " + thongtinhsba.thongtinbn.TEN_BENH_NHAN,
                            "key": "KHAMTIENME",
                        }
                        if (objReturn.kySo == 1) {
                            objTemp = {
                                ...objTemp,
                                "keyEMR": "BM_PHIEU_KHAM_TIEN_ME_154",
                                "idPhieu": objReturn.idKySo,
                            }
                        }
                        thongtinhsba.thongtinbn.linkKhamTienMe.push(objTemp);
                    } else {
                        notifiToClient("Red", "Phiếu khám tiền mê - " + thongtinhsba.thongtinbn.TEN_BENH_NHAN + ": " + objReturn.message);
                    }
                }).catch(error => {
                    // notifiToClient("Red", error.message || "Lỗi không xác định");
                });
            }
        }).fail(function() {
            notifiToClient("Red", "Lỗi lấy dữ liệu");
        })
    });
}

function vebieudomachhuyetapGMHS(dataMach, dataTamtruong , dataTamthu, indexMe, indexMo, indexKethuc) {
    var canvas = document.getElementById('myChart');
    var ctx = canvas.getContext('2d');
    var canvasWidth = canvas.width;
    var canvasHeight = canvas.height;
    ctx.clearRect(0, 0, canvasWidth, canvasHeight)
    var margin = 10;
    var Hmargin = 0;
    var chartWidth = canvasWidth - 2 * margin;
    var chartHeight = canvasHeight - 2 * Hmargin;
    var barWidth = chartWidth / 12;

    // Function to draw a line chart with dots and grid
    function drawMachChart(data) {

        // Set the line chart color and width
        ctx.lineWidth = 1; // Line chart width

        // Set the dot color
        ctx.strokeStyle = '#000000';
        ctx.fillStyle = '#000000'; // Dot color (red in this example)

        // Begin drawing the line chart
        ctx.beginPath();
        // Draw the grid lines
        for (let i = 0; i <= 16; i++) {
            var yPos = Hmargin + i * (chartHeight / 16);
            ctx.beginPath();
            ctx.moveTo(margin, yPos);
            ctx.lineTo(canvasWidth - margin, yPos);
            ctx.stroke();

        }
        // Draw the y-axis
        for (let i = 0; i <= 12; i++) {
            var xPos = margin + i * barWidth;
            ctx.beginPath();
            ctx.moveTo(xPos, Hmargin);
            ctx.lineTo(xPos, canvasHeight - Hmargin);
            ctx.setLineDash([5, 5]);
            ctx.stroke();

        }
        ctx.setLineDash([]);
        ctx.fillStyle = '#ff4136';
        ctx.strokeStyle = '#ff4136';
        var x = 0;
        var y = 0;
        var stepH = (chartHeight / 16);
        for (let i = 0; i < data.length; i++) {
            x = margin + i * barWidth;
            y = canvasHeight - Hmargin - ((data[i]-30)/10) * stepH;

            // Draw a dot (circle) at each data point
            ctx.beginPath();
            ctx.arc(x, y, 4, 0, 2 * Math.PI);
            if(data[i] < 30) {
                ctx.font = "bold 12px Arial";
                ctx.fillText("M:"+data[i], x - 5, canvasHeight - 2*stepH );
            }
            ctx.fill();
        }

        for (let i = 0; i < data.length; i++) {
            x = margin + i * barWidth;
            y = canvasHeight - Hmargin - ((data[i]-30)/10) * stepH;
            if (i === 0  ) {
                ctx.moveTo(x, y);
            } else {
                ctx.lineTo(x, y);

            }
        }
        ctx.stroke();
    }

    function drawDownArrow(x, y, size, text) {
        ctx.beginPath();
        ctx.moveTo(x, y-10);
        ctx.lineTo(x + size, y + size-10);
        ctx.lineTo(x + size * 2, y-10);
        ctx.font = "bold 12px Arial";
        ctx.fillStyle = "#09329a";
        ctx.fillText(text, x+2, y - 10);
        ctx.stroke();
    }

    function drawUpArrow(x, y, size, text) {
        ctx.beginPath();
        ctx.moveTo(x, y + size);
        ctx.lineTo(x + size, y);
        ctx.lineTo(x + size * 2, y + size);
        ctx.font = "bold 12px Arial";
        ctx.fillStyle = "#09329a";
        ctx.fillText(text, x+2, y + 20);
        ctx.stroke();
    }
    function drawDownTextArrow(x, y, text) {
        ctx.beginPath();
        ctx.font = "bold 12px Arial";
        ctx.fillStyle = "#09329a";
        ctx.fillText(text, x+2, y );
        ctx.stroke();
    }

    function drawHuyetap(dataTamtruong, dataTamthu) {
        var stepH = (canvasHeight / 16);
        ctx.strokeStyle = '#0074d9';
        ctx.lineWidth = 2;
        for (let i = 0; i < dataTamtruong.length; i++) {
            var xPos = margin + i * barWidth;
            if(dataTamtruong[i] < 30 || dataTamthu[i] < 30) {
                ctx.beginPath();
                ctx.fillText(dataTamtruong[i]+"/"+dataTamthu[i], xPos - 10+2, canvasHeight - stepH);
                ctx.stroke();
            } else {
                if(dataTamthu[i] > 190) {
                    drawDownTextArrow(xPos - 10, stepH-5, dataTamthu[i])
                } else {
                    drawDownArrow(xPos - 10, canvasHeight - ((dataTamthu[i]-30)/10) * stepH,10, dataTamthu[i]);
                }
                drawUpArrow(xPos - 10, canvasHeight - ((dataTamtruong[i]-30)/10) * stepH, 10, dataTamtruong[i]);
                ctx.beginPath();
                ctx.moveTo(xPos, canvasHeight - ((dataTamthu[i]-30)/10) * stepH);
                ctx.lineTo(xPos, canvasHeight - ((dataTamtruong[i]-30)/10) * stepH);
                ctx.stroke();
            }

        }

    }

    function drawGayme(indexMe) {
        ctx.strokeStyle = '#ba143b';
        ctx.lineWidth = 2;
        drawDownArrow(indexMe*barWidth, 44, 10, "");
        ctx.beginPath();
        ctx.moveTo(margin +  indexMe*barWidth, 16);
        ctx.lineTo(margin + indexMe*barWidth, 44);
        ctx.stroke();
    }
    function drawGaybatdaumo(indexMo) {
        ctx.strokeStyle = '#ba143b';
        ctx.lineWidth = 2;
        var xPos = margin + barWidth*indexMo
        drawDownArrow(barWidth*indexMo, 44, 10, "");
        ctx.beginPath();
        ctx.moveTo(xPos, 16);
        ctx.lineTo(xPos, 44);
        ctx.stroke();

        ctx.beginPath();
        ctx.moveTo(xPos - 5, 20);
        ctx.lineTo(xPos + 5, 20);
        ctx.stroke();

        ctx.beginPath();
        ctx.moveTo(xPos - 5, 24);
        ctx.lineTo(xPos + 5, 24);
        ctx.stroke();
    }
    function drawGayketthuc(indexKethuc) {
        ctx.strokeStyle = '#ba143b';
        ctx.lineWidth = 2;
        var xPos = margin + indexKethuc*barWidth
        drawUpArrow(xPos - margin, 10, 10, "");
        ctx.beginPath();
        ctx.moveTo(xPos, 10);
        ctx.lineTo(xPos, 44);
        ctx.stroke();
    }
    drawMachChart(dataMach);
    drawHuyetap(dataTamtruong, dataTamthu);
    if(indexMe > -1) {
        drawGayme(indexMe)
    }
    if(indexMo > -1) {
        drawGaybatdaumo(indexMo)
    }
    if(indexKethuc > -1) {
        drawGayketthuc(indexKethuc)
    }
}

function xemGayMeHoiSuc(dsLanPhauThuat, bien = null) {
    if (!bien){
        thongtinhsba.thongtinbn.linkGayMeHoiSuc = [];
    }
    $("#wrap_canvas_doc_gmhs").html("<canvas id='myChart' width='420' height='300'></canvas>")
    dsLanPhauThuat.forEach(function (lanPhauThuatObject) {
        $.get("cmu_getlist?url="+convertArray([singletonObject.dvtt, lanPhauThuatObject.ID, 'CMU_LPT_GMHS_SEL'])).done(function(data){
            if(data.length > 0){
                $("#hsba-gaymehoisuc-tab").show();
                $("#wrap_Menu_PhieuPhauThuat").show();
                var charts = [];
                var ngaykethuc = "";
                var thoigianbatdaume = "";
                for(var p = 0; p < Math.ceil(data.length/12); p++){
                    var dataMach = [];
                    var dataTamtruong = [];
                    var dataTamthu = [];
                    var indexDatnoikq = -1;
                    var indexMo = -1;
                    var indexKetThuc = -1;
                    for(var i = 0; i < 12; i++){
                        var item = data[i+p*12];
                        if(item){
                            dataMach.push(item.MACH);
                            dataTamtruong.push(item.HUYETAP_DUOI) ;
                            dataTamthu.push(item.HUYENAP_TREN);
                            if (item.BAT_DAU_MO == '1') {
                                indexMo = i;
                            } else if(item.NGAY_BAT_DAU_ME == item.THOI_GIAN_BLOCK) {
                                thoigianbatdaume = item.THOI_GIAN_BLOCK;
                            }else if(item.DAT_NOI_KHIQUAN == '1'){
                                indexDatnoikq = i;
                            } else if(item.NGAY_KET_THUC_MO == item.THOI_GIAN_BLOCK) {
                                indexKetThuc = i;
                                ngaykethuc = item.NGAY_KET_THUC_MO;
                            }
                        }
                    }
                    vebieudomachhuyetapGMHS(dataMach, dataTamtruong, dataTamthu, indexMo, indexDatnoikq, indexKetThuc);
                    var image = $("#myChart").get(0).toDataURL("image/png").replace("data:image/png;base64,", "")
                    charts.push("");
                    $.ajax({
                        url: "cmu_post_CMU_LANPHAUTHUAT_CHART_INS",
                        type: "POST",
                        data: {
                            url: [singletonObject.dvtt, lanPhauThuatObject.ID, p, image].join("```"),
                        },
                        async: false
                    })
                }
                if (!bien) {
                    var tenkhoa = '';
                    singletonObject.danhsachphongban.map(function(item) {
                        if(item.MAKHOA == lanPhauThuatObject.KHOA){
                            tenkhoa = item.TENKHOA;
                        }
                    })
                    var tenkhoathuchien = '';
                    singletonObject.danhsachphongban.map(function(item) {
                        if(item.MAKHOA == lanPhauThuatObject.KHOA_THUC_HIEN){
                            tenkhoathuchien = item.TENKHOA;
                        }
                    })
                    var loaiPhauthuat = ['Không', 'Dạ dày đầy', 'Cấp cứu']
                    if (thoigianbatdaume && ngaykethuc) {
                        var stringNgaykettuc = ngaykethuc.split(" ")[0].split("/")
                        var dateMe = moment(thoigianbatdaume, ['DD/MM/YYYY HH:mm']);
                        var dateKetthuc = moment(ngaykethuc, ['DD/MM/YYYY HH:mm']);

                        var diffInMinutes = dateKetthuc.diff(dateMe, 'minutes');
                        var textTongthoigianme = "";
                        if(diffInMinutes < 60) {
                            textTongthoigianme = diffInMinutes + " phút";
                        } else {
                            var hours = Math.floor(diffInMinutes / 60);
                            var minutes = diffInMinutes % 60;
                            textTongthoigianme = hours + " giờ " + minutes + " phút";
                        }
                    }
                    else {
                        var stringNgaykettuc = ["", "", ""];
                    }
                    var url = "inphieugaymehoisuc_v2?url="
                        + convertArray([
                            singletonObject.dvtt,
                            lanPhauThuatObject.MABENHNHAN,
                            lanPhauThuatObject.STT_BENHAN,
                            lanPhauThuatObject.STT_DOTDIEUTRI,
                            thongtinhsba.thongtinbn.TEN_BENH_NHAN,
                            thongtinhsba.thongtinbn.TUOI == 0? thongtinhsba.thongtinbn.TUOI_HT: thongtinhsba.thongtinbn.TUOI,
                            thongtinhsba.thongtinbn.GIOI_TINH
                        ])+
                        "&idphieu="+ lanPhauThuatObject.ID
                        +"&sovaovien="+lanPhauThuatObject.SOVAOVIEN
                        +"&sobenhan="+thongtinhsba.thongtinbn.SOBENHAN
                        +"&chandoan="//lanPhauThuatObject.CHAN_DOAN
                        +"&ngayphauthuat="+ (lanPhauThuatObject.NGAY_THUC_HIEN.split(" ")[0])
                        +"&tongsotrang="+ (Math.ceil(data.length/12))
                        +"&thongtinphauthuat="+ encodeURIComponent(JSON.stringify({
                            "mallampati": lanPhauThuatObject.MALLAMPATI+"",
                            "diung": lanPhauThuatObject.DI_UNG_THUOC == 1 ? "Có" : lanPhauThuatObject.DI_UNG_THUOC == 2 ? "Không" : "",
                            "tiensu": lanPhauThuatObject.TIEN_SU_THUOC,
                            "batthuong": lanPhauThuatObject.BAT_THUONG_CLS,
                            "asa": lanPhauThuatObject.ASA+"",
                            "tenkhoa": tenkhoa+"",
                            "khoathuchien": (tenkhoathuchien+""),
                            "loaiphauthuat": loaiPhauthuat[lanPhauThuatObject.LOAI_PHAU_THUAT],
                            "ketluan": data[0].NHANXET,
                            "tongthoigianme": textTongthoigianme,
                            "ngaythangnam": "Ngày " + stringNgaykettuc[0] + " tháng " + stringNgaykettuc[1] + " năm " + stringNgaykettuc[2],
                            "bacsigaymehs": lanPhauThuatObject.TEN_BAC_SI_GAY_ME || lanPhauThuatObject.BSGAYME,
                            charts: charts,
                            phuongphappt: lanPhauThuatObject.PHUONG_PHAP_PHAU_THUAT?lanPhauThuatObject.PHUONG_PHAP_PHAU_THUAT:"",
                            phuongphapvocam: lanPhauThuatObject.PHUONG_PHAP_VO_CAM,
                            tuthe: lanPhauThuatObject.TU_THE,
                            cannang: thongtinhsba.thongtinbn.CANNANG ? thongtinhsba.thongtinbn.CANNANG : "",
                            chieucao: thongtinhsba.thongtinbn.CHIEUCAO? thongtinhsba.thongtinbn.CHIEUCAO : "",
                            bsphauthuat:  lanPhauThuatObject.BSPHAUTHUAT,
                            bsgayme: lanPhauThuatObject.BSGAYME,
                            tienme: lanPhauThuatObject.TIEN_ME,
                            tacdung: lanPhauThuatObject.TAC_DUNG == 0? "Không": ("+"+lanPhauThuatObject.TAC_DUNG),
                            mayphauthuat: data[0].MAYGAYME,
                            chandoan: lanPhauThuatObject.CHAN_DOAN,
                            nhommau: lanPhauThuatObject.NHOMMAU,
                            tongmatmau: data[0].TONGMATMAU,
                            tongnuoctieu: data[0].TONGNUOCTIEU,
                            tongnhiptho: data[0].TONGNHIPTHO,
                            tongme: data[0].TONGME,
                            phuongphapme: data[0].PHUONGPHAPME,
                            tenthuocme: data[0].TENTHUOCME,
                            khangthe: lanPhauThuatObject.KHANGTHE
                        }))
                    ;
                    getFilesign769(
                        "PHIEU_NOITRU_GAYMEHOISUC",
                        lanPhauThuatObject.ID,
                        -1,//singletonObject.userId,
                        singletonObject.dvtt,
                        lanPhauThuatObject.SOVAOVIEN,
                        lanPhauThuatObject.SOVAOVIEN_DT,
                        -1,
                        function(dataKySo) {
                            if(dataKySo.length > 0) {
                                getCMUFileSigned769GetLink(dataKySo[0].KEYMINIO, 'pdf').then(pdfData => {
                                    url = pdfData;
                                    thongtinhsba.thongtinbn.linkGayMeHoiSuc.push({
                                        "url": [url],
                                        "name": "Phiếu gây mê hồi sức " + thongtinhsba.thongtinbn.TEN_BENH_NHAN,
                                        "keyEMR": "BM_PHIEU_GAYME_HOISUC_124",
                                        "key": "GAYMEHOISUC",
                                        "idPhieu": dataKySo[0].ID,
                                        "chuoi": dsLanPhauThuat,
                                    });
                                }).catch(error => {
                                    thongtinhsba.thongtinbn.linkGayMeHoiSuc.push({
                                        "url": [url],
                                        "name": "Phiếu gây mê hồi sức " + thongtinhsba.thongtinbn.TEN_BENH_NHAN,
                                        "key": "GAYMEHOISUC",
                                        "chuoi": dsLanPhauThuat,
                                    });
                                });
                            } else {
                                thongtinhsba.thongtinbn.linkGayMeHoiSuc.push({
                                    "url": [url],
                                    "name": "Phiếu gây mê hồi sức " + thongtinhsba.thongtinbn.TEN_BENH_NHAN,
                                    "key": "GAYMEHOISUC",
                                    "chuoi": dsLanPhauThuat,
                                });
                            }
                        }
                    );
                }
            }
        }).fail(function() {
            notifiToClient("Red", "Lỗi lấy dữ liệu");
        })
    });
}

function xemThongTu50(dsDotDieuTri) {
    thongtinhsba.thongtinbn.linkThongTu50 = [];
    dsDotDieuTri.forEach(function (itemDDT) {
        $.ajax({
            url: "cmu_getlist?url="+convertArray([
                singletonObject.dvtt,
                itemDDT.MABENHNHAN,
                itemDDT.SOVAOVIEN,
                itemDDT.SOVAOVIEN_DT,
                itemDDT.STT_DOTDIEUTRI,
                'CMU_DS_TT50']),
            type: "GET",
        }).done(function (dataPhieu) {
            if(dataPhieu.length > 0) {
                $("#hsba-tt50-tab").show();
                dataPhieu.forEach(function (item) {
                    var params = {
                        NGAYLAP_TEXT: item.NGAYLAP_TEXT,
                        SOVAOVIEN: itemDDT.SOVAOVIEN,
                        SOVAOVIEN_DT: itemDDT.SOVAOVIEN_DT,
                        MABENHNHAN: itemDDT.MABENHNHAN,
                        STT_DOTDIEUTRI: itemDDT.STT_DOTDIEUTRI,
                    }
                    getUrlThongTu50(params).then(objReturn => {
                        if (objReturn.isError == 0) {
                            var objTemp = {
                                "url": [objReturn.url],
                                "name": "Thông tư 50 - " + thongtinhsba.thongtinbn.TEN_BENH_NHAN + " - " + item.NGAYLAP_TEXT,
                                "key": "TT50",
                            }
                            if (objReturn.kySo == 1) {
                                objTemp = {
                                    ...objTemp,
                                    "keyEMR": "BM_PHIEU_CONGKHAI_THUOC_23",
                                    "idPhieu": objReturn.idKySo,
                                }
                            }
                            thongtinhsba.thongtinbn.linkThongTu50.push(objTemp);
                        } else {
                            notifiToClient("Red", "Thông tư 50 - " + thongtinhsba.thongtinbn.TEN_BENH_NHAN + " - " + item.NGAYLAP_TEXT + ": " + objReturn.message);
                        }
                    }).catch(error => {
                        // notifiToClient("Red", error.message || "Lỗi không xác định");
                    });
                });
            }
        });
    });
}

function xemTruyenDich(dsDotDieuTri) {
    thongtinhsba.thongtinbn.linkTruyenDich = [];
    dsDotDieuTri.forEach(function (itemDDT) {
        $.ajax({
            url: 'cmu_list_CMU_NOI_PHIEUTRUYENDICH_V3?url='
                + convertArray([singletonObject.dvtt, itemDDT.SOVAOVIEN, itemDDT.STT_BENHAN, '-1', '-1', '-1']),
            type: "GET",
        }).done(function (dataPhieu) {
            if(dataPhieu.length > 0) {
                $("#hsba-phieutruyendich-tab").show();
                $("#wrap_Menu_PhieuKhac").show();
                var params = {
                    loaingay: '-1',
                }
                getUrlTruyenDich(params).then(objReturn => {
                    if (objReturn.isError == 0) {
                        var objTemp = {
                            "url": [objReturn.url],
                            "name": "Phiếu truyền dịch - " + thongtinhsba.thongtinbn.TEN_BENH_NHAN,
                            "key": "PHIEUTRUYENDICH",
                        }
                        if (objReturn.kySo == 1) {
                            objTemp = {
                                ...objTemp,
                                "keyEMR": "BM_PHIEU_TRUYENDICH_22",
                                "idPhieu": objReturn.idKySo,
                            }
                        }
                        thongtinhsba.thongtinbn.linkTruyenDich.push(objTemp);
                    } else {
                        notifiToClient("Red", "Phiếu truyền dịch - " + thongtinhsba.thongtinbn.TEN_BENH_NHAN + ": " + objReturn.message);
                    }
                }).catch(error => {
                    // notifiToClient("Red", error.message || "Lỗi không xác định");
                });
            }
        });
    });
}

function xemPhieuGlasgow(dsDotDieuTri) {
    thongtinhsba.thongtinbn.linkPhieuGlasgow = [];
    dsDotDieuTri.forEach(function (itemDDT) {
        $.ajax({
            url: "cmu_getlist?url=" + convertArray([singletonObject.dvtt, itemDDT.SOVAOVIEN, 'CMU_GET_TDNGUOILONGLASGOW']),
            type: "GET",
        }).done(function (dataPhieu) {
            if(dataPhieu.length > 0) {
                $("#hsba-phieuglasgow-tab").show();
                $("#wrap_Menu_PhieuKhac").show();
                var arr = [singletonObject.dvtt, itemDDT.SOVAOVIEN, ];
                var url = "cmu_bangtheodoinguoilonglasgow_report?url=" + convertArray(arr)+'&viewPDF=1';
                getFilesign769(
                    "PHIEU_NOITRU_TDNGUOILONGLASGOW",
                    '1',
                    -1,
                    singletonObject.dvtt,
                    itemDDT.SOVAOVIEN,
                    itemDDT.SOVAOVIEN_DT,
                    '-1',
                    function(dataKySo) {
                        if(dataKySo.length > 0) {
                            getCMUFileSigned769GetLink(dataKySo[0].KEYMINIO, 'pdf').then(pdfData => {
                                url = pdfData;
                                thongtinhsba.thongtinbn.linkPhieuGlasgow.push({
                                    "url": [url],
                                    "name": "Phiếu Glasgow " + thongtinhsba.thongtinbn.TEN_BENH_NHAN,
                                    "keyEMR": "BM_PHIEU_DANHGIAHM_343",
                                    "key": "PHIEUGLASGOW",
                                    "idPhieu": dataKySo[0].ID,
                                });
                            }).catch(error => {
                                thongtinhsba.thongtinbn.linkPhieuGlasgow.push({
                                    "url": [url],
                                    "name": "Phiếu Glasgow " + thongtinhsba.thongtinbn.TEN_BENH_NHAN,
                                    "key": "PHIEUGLASGOW",
                                });
                            });
                        } else {
                            thongtinhsba.thongtinbn.linkPhieuGlasgow.push({
                                "url": [url],
                                "name": "Phiếu Glasgow " + thongtinhsba.thongtinbn.TEN_BENH_NHAN,
                                "key": "PHIEUGLASGOW",
                            });
                        }
                    });
            }
        });
    });
}

function xemTomTatDieuTri(dsDotDieuTri) {
    thongtinhsba.thongtinbn.linkTomTatDieuTri = [];
    dsDotDieuTri.forEach(function (itemDDT) {
        $.ajax({
            url: "cmu_getlist?url="+convertArray(["", "", itemDDT.SOVAOVIEN, itemDDT.SOVAOVIEN_DT, itemDDT.STT_BENHAN, singletonObject.dvtt,'CMU_TOMTATDIEUTRI_SEL']),
            type: "GET",
        }).done(function (dataPhieu) {
            if(dataPhieu.length > 0) {
                $("#hsba-tomtatdieutri-tab").show();
                $("#wrap_Menu_PhieuKhac").show();
                dataPhieu.forEach(function (item) {
                    var params = {
                        ID: item.ID_TOMTAT,
                    }
                    getUrlTomTatDieuTri(params).then(objReturn => {
                        if (objReturn.isError == 0) {
                            var objTemp = {
                                "url": [objReturn.url],
                                "name": "Tóm tắt điều trị - " + thongtinhsba.thongtinbn.TEN_BENH_NHAN,
                                "key": "TOMTATDIEUTRI",
                            }
                            if (objReturn.kySo == 1) {
                                objTemp = {
                                    ...objTemp,
                                    "keyEMR": "RPT_SCAN",
                                    "idPhieu": objReturn.idKySo,
                                }
                            }
                            thongtinhsba.thongtinbn.linkTomTatDieuTri.push(objTemp);
                        } else {
                            notifiToClient("Red", "Tóm tắt điều trị - " + thongtinhsba.thongtinbn.TEN_BENH_NHAN + ": " + objReturn.message);
                        }
                    }).catch(error => {
                        // notifiToClient("Red", error.message || "Lỗi không xác định");
                    });
                });
            }
        });
    });
}

function xemBienBanKiemDiemTuVong(dsDotDieuTri) {
    thongtinhsba.thongtinbn.linkBienBanKiemDiemTuVong = [];
    dsDotDieuTri.forEach(function (itemDDT) {
        $.ajax({
            url: "cmu_getlist?url=" + convertArray([singletonObject.dvtt, itemDDT.MABENHNHAN, "CMU_GET_BIENBANTUVONG"]),
            type: "GET",
        }).done(function (dataPhieu) {
            if(dataPhieu.length > 0) {
                $("#hsba-bienbankiemdiemtuvong-tab").show();
                $("#wrap_Menu_PhieuKhac").show();
                dataPhieu.forEach(function (item) {
                    var params = {
                        ID: item.ID,
                    }
                    getUrlBienBanTuVong(params).then(objReturn => {
                        if (objReturn.isError == 0) {
                            var objTemp = {
                                "url": [objReturn.url],
                                "name": "Biên bản kiểm điểm tử vong - " + thongtinhsba.thongtinbn.TEN_BENH_NHAN,
                                "key": "BIENBANKIEMDIEMTUVONG",
                            }
                            if (objReturn.kySo == 1) {
                                objTemp = {
                                    ...objTemp,
                                    "keyEMR": "BM_PHIEU_KIEMTHAO_TUVONG_119",
                                    "idPhieu": objReturn.idKySo,
                                }
                            }
                            thongtinhsba.thongtinbn.linkBienBanKiemDiemTuVong.push(objTemp);
                        } else {
                            notifiToClient("Red", "Trích biên bản kiểm điểm tử vong - " + thongtinhsba.thongtinbn.TEN_BENH_NHAN + ": " + objReturn.message);
                        }
                    }).catch(error => {
                        // notifiToClient("Red", error.message || "Lỗi không xác định");
                    });
                });
            }
        });
    });
}

function xemBienBanKiemThaoTuVong(dsDotDieuTri) {
    thongtinhsba.thongtinbn.linkBienBanKiemThaoTuVong = [];
    dsDotDieuTri.forEach(function (itemDDT) {
        $.ajax({
            url: "cmu_getlist?url=" + convertArray([singletonObject.dvtt, itemDDT.SOVAOVIEN, "CMU_GET_BIENBANKIEMTHAOTV"]),
            type: "GET",
        }).done(function (dataPhieu) {
            if(dataPhieu.length > 0) {
                $("#hsba-bienbankiemthaotuvong-tab").show();
                $("#wrap_Menu_PhieuKhac").show();
                dataPhieu.forEach(function (item) {
                    var params = {
                        THOIGIANVAOVIEN: thongtinhsba.thongtinbn.NGAYNHAPVIEN,
                        GIOI_TINH: thongtinhsba.thongtinbn.GIOI_TINH_NUM,
                    }
                    getUrlBienBanKiemThaoTV(params).then(objReturn => {
                        if (objReturn.isError == 0) {
                            var objTemp = {
                                "url": [objReturn.url],
                                "name": "Biên bản kiểm thảo tử vong - " + thongtinhsba.thongtinbn.TEN_BENH_NHAN,
                                "key": "BIENBANKIEMTHAOTUVONG",
                            }
                            if (objReturn.kySo == 1) {
                                objTemp = {
                                    ...objTemp,
                                    "keyEMR": "BM_PHIEU_KIEMTHAO_TUVONG_119",
                                    "idPhieu": objReturn.idKySo,
                                }
                            }
                            thongtinhsba.thongtinbn.linkBienBanKiemThaoTuVong.push(objTemp);
                        } else {
                            notifiToClient("Red", "Biên bản kiểm thảo tử vong - " + thongtinhsba.thongtinbn.TEN_BENH_NHAN + ": " + objReturn.message);
                        }
                    }).catch(error => {
                        // notifiToClient("Red", error.message || "Lỗi không xác định");
                    });
                });
            }
        });
    });
}

function xemGiayHenKhamLai(dsDotDieuTri) {
    thongtinhsba.thongtinbn.linkHenKhamLai = [];
    getUrlGiayHen().then(objReturn => {
        if (objReturn.isError == 0) {
            $("#hsba-phieuhenkhamlai-tab").show();
            var objTemp = {
                "url": [objReturn.url],
                "name": "Phiếu hẹn khám lại - " + thongtinhsba.thongtinbn.TEN_BENH_NHAN,
                "key": "PHIEUHENKHAMLAI",
            }
            if (objReturn.kySo == 1) {
                objTemp = {
                    ...objTemp,
                    "keyEMR": "BM_PHIEU_PHIEU_HENTAIKHAM_25",
                    "idPhieu": objReturn.idKySo,
                }
            }
            thongtinhsba.thongtinbn.linkHenKhamLai.push(objTemp);
        } else {
            notifiToClient("Red", "Phiếu hẹn khám lại - " + thongtinhsba.thongtinbn.TEN_BENH_NHAN + ": " + objReturn.message);
        }
    }).catch(error => {
        // notifiToClient("Red", error.message || "Lỗi không xác định");
    });
}

function xemGiayRaVien(dsDotDieuTri) {
    thongtinhsba.thongtinbn.linkGiayRaVien = [];
    var uuid = uuidv4();
    var params = {
        dvtt: singletonObject.dvtt,
        SOVAOVIEN: thongtinhsba.thongtinbn.SOVAOVIEN,
        SOVAOVIEN_DT: thongtinhsba.thongtinbn.SOVAOVIEN_DT
    }
    getUrlGiayRaVien(params).then(objReturn => {
        console.log("objReturn", objReturn)
        if (objReturn.isError == 0) {
            $("#hsba-phieuxuatvien-tab").show();

            if(singletonObject.thamSo960601 == 1 ) {
                return thongtinhsba.thongtinbn.linkGiayRaVien.push({
                    "url": [objReturn.url],
                    "name": "Giấy ra viện " + thongtinhsba.thongtinbn.TEN_BENH_NHAN,
                    "key": "GIAYRAVIEN",
                    "keyEMR": "BM_PHIEU_GIAYRAVIEN_11",
                    "idPhieu": objReturn.idKySo,
                });
            }

            $.post("cmu_apikiso_chi-tiet-giay-ra-vien-sovaovien", {
                dvtt: singletonObject.dvtt,
                sovaovien: thongtinhsba.thongtinbn.SOVAOVIEN,
                sovaovienDt: thongtinhsba.thongtinbn.SOVAOVIEN_DT
            }).done(function (chitietGRV) {
                $.post("cmu_post", {
                    url: [
                        singletonObject.dvtt,
                        uuid,
                        thongtinhsba.thongtinbn.BANT,
                        thongtinhsba.thongtinbn.MABENHNHAN,
                        thongtinhsba.thongtinbn.SOVAOVIEN,
                        thongtinhsba.thongtinbn.SOVAOVIEN_DT,
                        thongtinhsba.thongtinbn.SOBENHAN,
                        thongtinhsba.thongtinbn.STT_DOTDIEUTRI,
                        "NOITRU_GIAYRAVIEN",
                        "",
                        thongtinhsba.thongtinbn.SOVAOVIEN,
                        "",
                        "",
                        "",
                        thongtinhsba.thongtinbn.MAKHOA_XUATVIEN,
                        thongtinhsba.thongtinbn.PHONGBAN_RAVIEN,
                        "",
                        "",
                        0,
                        _.get(chitietGRV, "giamDoc.username",""),
                        _.get(chitietGRV, "giamDoc.maNhanVienHis","0"),
                        1,
                        0,
                        -1,
                        -1,
                        "",
                        0,
                        'SMARTCA_SAVE_SIGNED_GRV'
                    ].join("```")
                }).done(function (idPhieu) {
                    thongtinhsba.thongtinbn.linkGiayRaVien.push({
                        "url": ['data:application/pdf;base64,' + objReturn.url],
                        "name": "Giấy ra viện " + thongtinhsba.thongtinbn.TEN_BENH_NHAN,
                        "key": "GIAYRAVIEN",
                        "keyEMR": "BM_PHIEU_GIAYRAVIEN_11",
                        "idPhieu": idPhieu
                    });
                })
            })
        } else {
            notifiToClient("Red", "Lỗi lấy url giấy báo tử");
        }
    }).catch(error => {
        if (error.isError == 2) {
            try {
                $.get("cmu_list_CMU_NOT_LOAD_TTXUATVIEN_F?url="+convertArray([
                    singletonObject.dvtt,
                    thongtinhsba.thongtinbn.STT_BENHAN,
                    thongtinhsba.thongtinbn.STT_DOTDIEUTRI
                ])).done(function (dataRes) {
                    if(dataRes.length > 0) {
                        var data = dataRes[0];
                        var url = "noitru_xuatvien_loadttBA";
                        $.post(url, {
                            dvtt: singletonObject.dvtt,
                            stt_benhan: thongtinhsba.thongtinbn.STT_BENHAN
                        }).done(function (data2) {
                            if (data2) {
                                var tuoi = thongtinhsba.thongtinbn.TUOI;
                                var thang = thongtinhsba.thongtinbn.THANG_SINH;
                                var gioxuatvien = data.GIO_XUATVIEN.split(":")
                                var thoigianravien = data.NGAY_XUATVIEN + " "+ gioxuatvien[0] + ":" + gioxuatvien[1]
                                var ngayravien = thoigianravien.split(" ")[0];
                                var gioravien = thoigianravien.split(" ")[1]+":00";
                                var icd_xv = data.ICD_BENHCHINH;
                                var tenbenhchinh = data.TEN_BENHCHINH;
                                var benhphu = data.BENHKEMTHEO;
                                var ketquadieutri = data.KETQUADIEUTRI;
                                var ttravien = data.TINHTRANG_RV;
                                var ppdieutri = data.PP_DIEUTRI;
                                var loidanbacsi = data.LOIDAN_BS;
                                var ngayhen = data.NGAY_HENTAIKHAM ? data.NGAY_HENTAIKHAM : "";
                                var trieuchung = data.TRIEUCHUNG;
                                var soluutru = data2.SOXUATVIEN_LUUTRU;
                                var soluutrutam = data2.SOXUATVIEN_TT_LUUTRU ? data2.SOXUATVIEN_TT_LUUTRU : "";
                                var dvtt = singletonObject.dvtt;
                                var tenphongban = thongtinhsba.thongtinbn.PHONGBAN_RAVIEN ? thongtinhsba.thongtinbn.PHONGBAN_RAVIEN : thongtinhsba.thongtinbn.TENKHOA_NHAPVIENVAOKHOA;
                                var url ='ingiayraviennoitru_tmp?dvtt=' + dvtt +
                                    "&stt_benhan=" + thongtinhsba.thongtinbn.STT_BENHAN +
                                    "&stt_dotdieutri=" + thongtinhsba.thongtinbn.STT_DOTDIEUTRI +
                                    "&mabenhnhan=" + thongtinhsba.thongtinbn.MABENHNHAN +
                                    "&tenphongban=" + tenphongban +
                                    "&tuoi=" + tuoi + "&thang=" + thang +
                                    "&ngayravien=" + ngayravien + "&gioravien=" + gioravien + "&icd_xv=" + icd_xv +
                                    "&tenbenhchinh=" + encodeURIComponent(tenbenhchinh) +
                                    "&benhphu=" + encodeURIComponent(benhphu) +
                                    "&ketquadieutri=" + ketquadieutri +
                                    "&ttravien=" + ttravien +
                                    "&ppdieutri=" + ppdieutri +
                                    "&loidanbacsi=" + loidanbacsi +
                                    "&ngayhen=" + ngayhen +
                                    "&trieuchung=" + trieuchung +
                                    "&soluutru=" + soluutru +
                                    "&soluutrutam=" + soluutrutam +
                                    "&noilamviec=''";

                                $("#hsba-phieuxuatvien-tab").show();
                                if (singletonObject.thamSo960601 == 1 ) {
                                    return thongtinhsba.thongtinbn.linkGiayRaVien.push({
                                        "url": [url],
                                        "name": "Giấy ra viện " + thongtinhsba.thongtinbn.TEN_BENH_NHAN,
                                        "key": "GIAYRAVIEN",
                                        // "keyEMR": "BM_PHIEU_GIAYRAVIEN_11",
                                        "idPhieu": error.idKySo,
                                    });
                                }
                            }
                            else {
                                notifiToClient("Red", "Lỗi hệ thống: Không có thông tin xuất viện nt");
                            }
                        });
                    }
                    else {
                        notifiToClient("Red", "Lỗi hệ thống: Không có thông tin xuất viện");
                    }
                });
            }
            catch (e) {
                notifiToClient("Red", "Lỗi hệ thống: " + e);
            }
        }
        else {
            notifiToClient("Red", error.message || "Lỗi không xác định");
        }
    });
}

function xemPhieuChuyenTuyen(dsDotDieuTri) {
    thongtinhsba.thongtinbn.linkPhieuChuyenTuyen = [];
    thongtinhsba.thongtinbn.linkPhieuChuyenTuyen = [];
    $.post("noitru_giaychuyentuyenselect", {
        dvtt: singletonObject.dvtt,
        sovaovien: thongtinhsba.thongtinbn.SOVAOVIEN,
        sovaovien_dt: thongtinhsba.thongtinbn.SOVAOVIEN_DT
    }).done(function (data) {
        if(data) {
            getUrlGiayChuyenTuyen({
                dvtt: singletonObject.dvtt,
                SOVAOVIEN: thongtinhsba.thongtinbn.SOVAOVIEN,
                SOVAOVIEN_DT: thongtinhsba.thongtinbn.SOVAOVIEN_DT
            }).then(objReturn => {
                if (objReturn.isError == 0) {
                    $("#hsba-phieuchuyentuyen-tab").show();
                    thongtinhsba.thongtinbn.linkPhieuChuyenTuyen.push({
                        "url": [objReturn.url],
                        "name": "Phiếu chuyển tuyến " + thongtinhsba.thongtinbn.TEN_BENH_NHAN,
                        "key": "PHIEUCHUYENTUYEN",
                    });
                } else {
                    notifiToClient("Red", "Lỗi lấy url giấy chuyển tuyến");
                }
            }).catch(error => {
                notifiToClient("Red", error.message || "Lỗi không xác định");
            });
        }
    });
}

function xemGiayBaoTu(dsDotDieuTri) {
    thongtinhsba.thongtinbn.linkGiayBaoTu = [];
    dsDotDieuTri.forEach(function (itemDDT) {
        $.ajax({
            url: "cmu_list_CMU_GETTHONGTIN_TUVONG?url="+convertArray(
                [
                    singletonObject.dvtt,
                    itemDDT.MABENHNHAN,
                    itemDDT.SOVAOVIEN
                ]),
            type: "GET",
        }).done(function (dataPhieu) {
            // if(dataPhieu.length > 0) {
            //     $("#hsba-giaybaotu-tab").show();
            //     var url = "ingiaybaotu_nk?url=" + convertArray([itemDDT.IDNHANKHAU, itemDDT.MA_BENH_NHAN, 1, "0"]);
            //     thongtinhsba.thongtinbn.linkGiayBaoTu.push({
            //         "url": [url],
            //         "name": "Giấy báo tử " + thongtinhsba.thongtinbn.TEN_BENH_NHAN,
            //         "key": "GIAYBAOTU",
            //     });
            // }
            if(dataPhieu.length > 0) {
                var params = {
                    dvtt: singletonObject.dvtt,
                    SOVAOVIEN: thongtinhsba.thongtinbn.SOVAOVIEN,
                    SOVAOVIEN_DT: thongtinhsba.thongtinbn.SOVAOVIEN_DT,
                    STT_BENHAN: thongtinhsba.thongtinbn.STT_BENHAN,
                    STT_DOTDIEUTRI: thongtinhsba.thongtinbn.STT_DOTDIEUTRI,
                    SOBENHAN: thongtinhsba.thongtinbn.SOBENHAN,
                    SOPHIEUTHANHTOAN: thongtinhsba.thongtinbn.SOPHIEUTHANHTOAN,
                    MABENHNHAN: thongtinhsba.thongtinbn.MABENHNHAN
                }
                getUrlGiayBaoTu(params).then(objReturn => {
                    if (objReturn.isError == 0) {
                        $("#hsba-giaybaotu-tab").show();
                        thongtinhsba.thongtinbn.linkGiayBaoTu.push({
                            "url": [objReturn.url],
                            "name": "Giấy báo tử " + thongtinhsba.thongtinbn.TEN_BENH_NHAN,
                            "key": "GIAYBAOTU",
                        });
                    } else {
                        // notifiToClient("Red", "Lỗi lấy url giấy báo tử");
                    }
                }).catch(error => {
                    // notifiToClient("Red", error.message || "Lỗi không xác định");
                });
            }
        });
    });
}

function xemKhamBenhVaoVien(dsDotDieuTri) {
    thongtinhsba.thongtinbn.linkKhamBenhVaoVien = [];
    dsDotDieuTri.forEach(function (itemDDT) {
        $("#hsba-phieukhambenhvaovien-tab").show();
        getFilesign769(
            "PHIEUKHAMBENH_VAOVIEN",
            (thongtinhsba.thongtinbn.SOVAOVIEN_NGT !=0 && thongtinhsba.thongtinbn.SOVAOVIEN_NGT )? thongtinhsba.thongtinbn.SOVAOVIEN_NGT :  itemDDT.SOVAOVIEN,
            -1,
            singletonObject.dvtt,
            (thongtinhsba.thongtinbn.SOVAOVIEN_NGT !=0 && thongtinhsba.thongtinbn.SOVAOVIEN_NGT )? thongtinhsba.thongtinbn.SOVAOVIEN_NGT : itemDDT.SOVAOVIEN,
            itemDDT.SOVAOVIEN_DT ? itemDDT.SOVAOVIEN_DT : 0,
            -1,
            function(dataKySo) {
                if(dataKySo.length > 0) {
                    getCMUFileSigned769GetLink(dataKySo[0].KEYMINIO, 'pdf').then(pdfData => {
                        url = pdfData;
                        thongtinhsba.thongtinbn.linkKhamBenhVaoVien.push({
                            "url": [url],
                            "name": "Phiếu khám bệnh vào viện " + thongtinhsba.thongtinbn.TEN_BENH_NHAN,
                            "keyEMR": "BM_PHIEU_KHAMBENH_VAOVIEN_6",
                            "key": "PHIEUKHAMBENHVAOVIEN",
                            "idPhieu": dataKySo[0].ID,
                        });
                    }).catch(error => {
                        thongtinhsba.thongtinbn.linkKhamBenhVaoVien.push({
                            "url": [url],
                            "name": "Phiếu khám bệnh vào viện " + thongtinhsba.thongtinbn.TEN_BENH_NHAN,
                            "key": "PHIEUKHAMBENHVAOVIEN",
                        });
                    });
                } else {
                    if (!itemDDT.SOVAOVIEN_NGT) {
                        // var url = 'noitru_inphieukhambenh?stt_benhan=' + itemDDT.STT_BENHAN + "&dvtt=" + singletonObject.dvtt
                        //     + "&stt_dotdieutri=" + itemDDT.STT_DOTDIEUTRI + "&loai_ck=1&viewPDF=1"
                        var arr = [
                            itemDDT.STT_BENHAN,
                            singletonObject.dvtt,
                            itemDDT.STT_DOTDIEUTRI,
                            '1',
                            '2',
                        ];
                        var param2 = ['stt_benhan', 'dvtt', 'stt_dotdieutri', 'hienthi_sobenhan', 'ver'];
                        var url = "cmu_injasper?url=" + convertArray(arr)+"&param="+ convertArray(param2)+"&loaifile=pdf&jasper=khambenhvaovien";
                        thongtinhsba.thongtinbn.linkKhamBenhVaoVien.push({
                            "url": [url],
                            "name": "Phiếu khám bệnh vào viện " + thongtinhsba.thongtinbn.TEN_BENH_NHAN,
                            "key": "PHIEUKHAMBENHVAOVIEN",
                        });
                    } else {
                        var url = 'inphieunhapvien?makhambenh=' + itemDDT.MAKHAMBENHNGOAITRU_NHAPVIEN + "&dvtt=" + singletonObject.dvtt + "&viewPDF=1"
                        thongtinhsba.thongtinbn.linkKhamBenhVaoVien.push({
                            "url": [url],
                            "name": "Phiếu khám bệnh vào viện " + thongtinhsba.thongtinbn.TEN_BENH_NHAN,
                            "key": "PHIEUKHAMBENHVAOVIEN",
                        });
                    }
                }
            }
        )
    });
}

function xemTienSuDiUng(dsDotDieuTri) {
    thongtinhsba.thongtinbn.linkTienSuDiUng = [];
    dsDotDieuTri.forEach(function (itemDDT) {
        $.ajax({
            url: "cmu_getlist?url=" + convertArray([singletonObject.dvtt, itemDDT.SOVAOVIEN, "CMU_GET_TIENSUDIUNG"]),
            type: "GET",
        }).done(function (dataPhieu) {
            if (dataPhieu.length > 0) {
                $("#hsba-tiensudiung-tab").show();
                $("#wrap_Menu_PhieuKhac").show();
                dataPhieu.forEach(function (item) {
                    var params = {
                        ID: item.ID,
                        NGAY_TAO_PHIEU: item.NGAY_TAO_PHIEU,
                        CHANDOAN: itemDDT.ICD_NHAPVIEN + "- " + itemDDT.TENBENHCHINH_NHAPVIEN,
                        NGAYVAOVIEN: itemDDT.NGAY_VAO_VIEN + " " + itemDDT.GIO_VAO_VIEN,
                    }
                    getUrlTienSuDiUng(params).then(objReturn => {
                        if (objReturn.isError == 0) {
                            var objTemp = {
                                "url": [objReturn.url],
                                "name": "Phiếu khai thác tiền sử dị ứng - " + thongtinhsba.thongtinbn.TEN_BENH_NHAN,
                                "key": "TIENSUDIUNG",
                            }
                            if (objReturn.kySo == 1) {
                                objTemp = {
                                    ...objTemp,
                                    "keyEMR": "BM_PHIEU_THEODOI_DIUNG_180",
                                    "idPhieu": objReturn.idKySo,
                                }
                            }
                            thongtinhsba.thongtinbn.linkTienSuDiUng.push(objTemp);
                        } else {
                            notifiToClient("Red", "Phiếu khai thác tiền sử dị ứng - " + thongtinhsba.thongtinbn.TEN_BENH_NHAN + ": " + objReturn.message);
                        }
                    }).catch(error => {
                        // notifiToClient("Red", error.message || "Lỗi không xác định");
                    });
                    // var text_date_part = item.NGAY_TAO_PHIEU.split("/");
                    // var text_date = text_date_part[0];
                    // var text_month = text_date_part[1];
                    // var text_year = text_date_part[2];
                    // var tenkhoa = "";
                    // singletonObject.danhsachphongban.forEach(function(obj) {
                    //     if(obj.MAKHOA == singletonObject.makhoa) {
                    //         tenkhoa = obj.TENKHOA;
                    //     }
                    // })
                    // var arr = [
                    //     item.ID,
                    //     singletonObject.dvtt,
                    //     tenkhoa,
                    //     itemDDT.ICD_NHAPVIEN + "- " + itemDDT.TENBENHCHINH_NHAPVIEN,
                    //     itemDDT.NGAY_VAO_VIEN + " " + itemDDT.GIO_VAO_VIEN,
                    //     itemDDT.SOVAOVIEN,
                    //     itemDDT.SOBENHAN,
                    //     text_date,
                    //     text_month,
                    //     text_year
                    // ];
                    // var param = ['magiay', 'dvtt', 'khoa', 'chan_doan', 'ngaygio_vaovien', 'sovaovien', 'sobenhan', 'text_date', 'text_month', 'text_year'];
                    // var url = "cmu_injasper?url=" + convertArray(arr)+"&param="+ convertArray(param)+"&loaifile=pdf&jasper=cmu_tiensudiung";
                    // getFilesign769(
                    //     "PHIEU_NOITRU_TIENSUDIUNG",
                    //     item.ID,
                    //     -1,//singletonObject.userId,
                    //     singletonObject.dvtt,
                    //     itemDDT.SOVAOVIEN,
                    //     itemDDT.SOVAOVIEN_DT,
                    //     -1,
                    //     function(dataKySo) {
                    //         if(dataKySo.length > 0) {
                    //             getCMUFileSigned769GetLink(dataKySo[0].KEYMINIO, 'pdf').then(pdfData => {
                    //                 url = pdfData;
                    //                 thongtinhsba.thongtinbn.linkTienSuDiUng.push({
                    //                     "url": [url],
                    //                     "name": "Tiền sử dị ứng " + item.ID,
                    //                     "key": "TIENSUDIUNG",
                    //                     "keyEMR": "BM_PHIEU_THEODOI_DIUNG_180",
                    //                     "idPhieu": dataKySo[0].ID,
                    //                 });
                    //             }).catch(error => {
                    //                 thongtinhsba.thongtinbn.linkTienSuDiUng.push({
                    //                     "url": [url],
                    //                     "name": "Tiền sử dị ứng " + item.ID,
                    //                     "key": "TIENSUDIUNG",
                    //                 });
                    //             });
                    //         } else {
                    //             thongtinhsba.thongtinbn.linkTienSuDiUng.push({
                    //                 "url": [url],
                    //                 "name": "Tiền sử dị ứng " + item.ID,
                    //                 "key": "TIENSUDIUNG",
                    //             });
                    //         }
                    //     }
                    // )
                });
            }
        });
    });
}

function xemPhieuChuyenDa(dsDotDieuTri) {
    function vebieudoTimthai(dataTimthai) {
        var canvas = document.getElementById('bieudochuyenda_timthai');
        var ctx = canvas.getContext('2d');
        var canvasWidth = canvas.width;
        var canvasHeight = canvas.height;
        ctx.clearRect(0, 0, canvasWidth, canvasHeight)
        var margin = 5;
        var Hmargin = 5;
        var chartWidth = canvasWidth - 2 * margin;
        var chartHeight = canvasHeight - 2 * Hmargin;
        var barWidth = chartWidth / 24;

        function drawTimthaiChart(data) {
            ctx.lineWidth = 1;
            ctx.strokeStyle = '#000000';
            ctx.fillStyle = '#000000';
            ctx.beginPath();
            for (let i = 0; i <= 12; i++) {
                var yPos = Hmargin + i * (chartHeight / 12);
                ctx.beginPath();
                ctx.moveTo(margin, yPos);
                ctx.lineTo(canvasWidth - margin, yPos);
                ctx.stroke();

            }
            // Draw the y-axis
            for (let i = 0; i <= 24; i++) {
                var xPos = margin + i * barWidth;
                ctx.beginPath();
                ctx.moveTo(xPos, Hmargin);
                ctx.lineTo(xPos, canvasHeight - Hmargin);
                ctx.stroke();

            }
            ctx.fillStyle = '#ff4136';
            ctx.strokeStyle = '#ff4136';
            var x = 0;
            var y = 0;
            var stepH = (chartHeight / 12);
            for (let i = 0; i < data.length; i++) {
                if(data[i].trim()) {
                    x = margin + i * barWidth;
                    y = canvasHeight - Hmargin - ((data[i]-80)/10) * stepH;

                    // Draw a dot (circle) at each data point
                    ctx.beginPath();
                    ctx.arc(x, y, 4, 0, 2 * Math.PI);
                    if(data[i] < 80) {
                        ctx.font = "bold 12px Arial";
                        ctx.fillText("M:"+data[i], x - 5, canvasHeight - 2*stepH );
                    }
                    ctx.fill();
                }

            }

            for (let i = 0; i < data.length; i++) {
                if(data[i].trim()) {
                    x = margin + i * barWidth;
                    y = canvasHeight - Hmargin - ((data[i]-80)/10) * stepH;
                    if (i === 0  ) {
                        ctx.moveTo(x, y);
                    } else {
                        ctx.lineTo(x, y);

                    }
                }

            }
            ctx.stroke();
        }

        drawTimthaiChart(dataTimthai);

    }

    function vebieudoCTC(dataCTC, dataTientriendau) {
        var canvas = document.getElementById('bieudochuyenda_ctc');
        var ctx = canvas.getContext('2d');
        var canvasWidth = canvas.width;
        var canvasHeight = canvas.height;
        ctx.clearRect(0, 0, canvasWidth, canvasHeight)
        var margin = 5;
        var Hmargin = 5;
        var column = 24;
        var row = 10;
        var chartWidth = canvasWidth - 2 * margin;
        var chartHeight = canvasHeight - 2 * Hmargin;
        var barWidth = chartWidth / column;

        veGridCanvas("bieudochuyenda_ctc", {
            margin: margin,
            Hmargin: Hmargin,
            column: column,
            row: row
        })

        function drawCTCChart(data) {

            // Set the line chart color and width
            ctx.lineWidth = 1; // Line chart width
            ctx.beginPath();
            ctx.fillStyle = '#ba2a1c';
            ctx.strokeStyle = '#ba2a1c';
            var x = 0;
            var y = 0;
            var stepH = (chartHeight / row);
            for (let i = 0; i < data.length; i++) {
                if(data[i].trim()) {
                    x = margin + i * barWidth;
                    y = canvasHeight - Hmargin - data[i] * stepH;

                    // Draw a dot (circle) at each data point
                    ctx.beginPath();
                    ctx.fillStyle = "#ba2a1c";
                    ctx.font = "bold 14px Arial";
                    ctx.fillText('x', x - 5, y + 5);

                    ctx.font = "bold 12px Arial";
                    ctx.fillText(data[i], x + 2, y - 14);
                    ctx.fill();
                }
            }

            for (let i = 0; i < data.length; i++) {
                if(data[i].trim()) {
                    x = margin + i * barWidth;
                    y = canvasHeight - Hmargin - data[i] * stepH;
                    if (i === 0) {
                        ctx.moveTo(x, y);
                    } else {
                        ctx.lineTo(x, y);

                    }
                }
            }
            ctx.stroke();
        }

        function drawTientriendauChart(data) {

            // Set the line chart color and width
            ctx.lineWidth = 1; // Line chart width
            ctx.beginPath();
            ctx.fillStyle = '#147f51';
            ctx.strokeStyle = '#147f51';
            var x = 0;
            var y = 0;
            var stepH = (chartHeight / row);
            for (let i = 0; i < data.length; i++) {
                if(data[i].trim()) {
                    x = margin + i * barWidth;
                    y = canvasHeight - Hmargin - data[i] * stepH;

                    // Draw a dot (circle) at each data point
                    console.log("x, y", x, y)
                    ctx.beginPath();
                    ctx.arc(x, y, 4, 0, 2 * Math.PI);
                    ctx.font = "bold 12px Arial";
                    ctx.fillStyle = "#147f51";
                    ctx.fillText(data[i] + " cm", x + 2, y + 14);
                    ctx.fill();
                }
            }
            ctx.setLineDash([5,5])
            for (let i = 0; i < data.length; i++) {
                if(data[i].trim()) {
                    x = margin + i * barWidth;
                    y = canvasHeight - Hmargin - data[i] * stepH;
                    if (i === 0  ) {
                        ctx.moveTo(x, y);
                    } else {
                        ctx.lineTo(x, y);

                    }
                }

            }
            ctx.stroke();
        }

        function drawDuongbaodong() {
            ctx.beginPath();
            ctx.lineWidth = 2;
            ctx.strokeStyle = '#01579b';
            ctx.moveTo(margin, canvasHeight - Hmargin - 4 * (chartHeight / row));
            ctx.lineTo(margin + 12 * barWidth, Hmargin);
            ctx.stroke();
            ctx.save();
            ctx.translate(margin + 4 * barWidth, canvasHeight - Hmargin - 5 * (chartHeight / row));
            ctx.rotate(-22 * Math.PI / 180);
            ctx.fillStyle = "#01579b";
            ctx.font = '18px Arial';
            ctx.fillText('ĐƯỜNG BÁO ĐỘNG', 0,0);
            ctx.restore();
        }

        function drawDuonghanhdong() {
            ctx.beginPath();
            ctx.lineWidth = 2;
            ctx.strokeStyle = '#a76676';
            ctx.moveTo(margin + 8 * barWidth , canvasHeight - Hmargin - 4 * (chartHeight / row));
            ctx.lineTo(margin + 20 * barWidth, Hmargin);
            ctx.stroke();
            ctx.save();
            ctx.translate(margin + 12 * barWidth, canvasHeight - Hmargin - 5 * (chartHeight / row));
            ctx.rotate(-22 * Math.PI / 180);
            ctx.fillStyle = "#a76676";
            ctx.font = '18px Arial';
            ctx.fillText('ĐƯỜNG HÀNH ĐỘNG', 0,0);
            ctx.restore();
        }


        drawCTCChart(dataCTC);
        drawTientriendauChart(dataTientriendau);
        ctx.setLineDash([])
        drawDuongbaodong()
        drawDuonghanhdong()

    }

    function vebieudoMachHuyetap(dataMach, dataTamtruong, dataTamthu) {
        var canvas = document.getElementById('bieudochuyenda_huyetap');
        var ctx = canvas.getContext('2d');
        var canvasWidth = canvas.width;
        var canvasHeight = canvas.height;
        ctx.clearRect(0, 0, canvasWidth, canvasHeight)
        var margin = 5;
        var Hmargin = 0;
        var column = 24;
        var row = 12;
        var chartWidth = canvasWidth - 2 * margin;
        var chartHeight = canvasHeight - 2 * Hmargin;
        var barWidth = chartWidth / column;
        var minValue = 60;

        veGridCanvas("bieudochuyenda_huyetap", {
            margin: margin,
            Hmargin: Hmargin,
            column: column,
            row: row,
            dashY: [5, 5]
        })
        function drawMachChart(data) {

            ctx.lineWidth = 1; // Line chart width
            ctx.setLineDash([])
            ctx.beginPath();

            ctx.fillStyle = '#ff4136';
            ctx.strokeStyle = '#ff4136';
            var x = 0;
            var y = 0;
            var stepH = (chartHeight / row);
            for (let i = 0; i < data.length; i++) {
                if(data[i].trim()) {
                    x = margin + i * barWidth;
                    y = canvasHeight - Hmargin - ((data[i]-minValue)/10) * stepH;

                    // Draw a dot (circle) at each data point
                    ctx.beginPath();
                    ctx.arc(x, y, 4, 0, 2 * Math.PI);
                    if(data[i] < minValue) {
                        ctx.font = "bold 12px Arial";
                        ctx.fillText("M:"+data[i], x - 5, canvasHeight - 2*stepH );
                    } else {
                        ctx.fillStyle = "#ff4136";
                        ctx.font = "bold 12px Arial";
                        ctx.fillText(data[i], x+10, y+20);
                    }
                }


                ctx.fill();
            }

            for (let i = 0; i < data.length; i++) {
                if(data[i].trim()) {
                    x = margin + i * barWidth;
                    y = canvasHeight - Hmargin - ((data[i]-minValue)/10) * stepH;
                    if (i === 0  ) {
                        ctx.moveTo(x, y);
                    } else {
                        ctx.lineTo(x, y);

                    }
                }

            }
            ctx.stroke();
        }

        function drawDownArrow(x, y, size, text) {
            ctx.beginPath();
            ctx.moveTo(x, y-10);
            ctx.lineTo(x + size, y + size-10);
            ctx.lineTo(x + size * 2, y-10);
            ctx.font = "bold 12px Arial";
            ctx.fillStyle = "#09329a";
            ctx.fillText(text, x+12, y - 20);
            ctx.stroke();
        }

        function drawUpArrow(x, y, size, text) {
            ctx.beginPath();
            ctx.moveTo(x, y + size);
            ctx.lineTo(x + size, y);
            ctx.lineTo(x + size * 2, y + size);
            ctx.font = "bold 12px Arial";
            ctx.fillStyle = "#09329a";
            ctx.fillText(text, x+12, y + 20);
            ctx.stroke();
        }

        function drawHuyetap(dataTamtruong, dataTamthu) {
            var stepH = (canvasHeight / row);
            ctx.strokeStyle = '#0074d9';
            ctx.lineWidth = 2;
            for (let i = 0; i < dataTamtruong.length; i++) {
                if(dataTamtruong[i].trim()) {
                    var xPos = margin + i * barWidth;
                    if(dataTamtruong[i] < minValue || dataTamthu[i] < minValue) {
                        ctx.beginPath();
                        ctx.fillText(dataTamtruong[i]+"/"+dataTamthu[i], xPos - 10+2, canvasHeight - stepH);
                        ctx.stroke();
                    } else {
                        drawDownArrow(xPos - 10, canvasHeight - ((dataTamthu[i]-minValue)/10) * stepH,10, dataTamthu[i]);
                        drawUpArrow(xPos - 10, canvasHeight - ((dataTamtruong[i]-minValue)/10) * stepH, 10, dataTamtruong[i]);
                        ctx.beginPath();
                        ctx.moveTo(xPos, canvasHeight - ((dataTamthu[i]-minValue)/10) * stepH);
                        ctx.lineTo(xPos, canvasHeight - ((dataTamtruong[i]-minValue)/10) * stepH);
                        ctx.stroke();
                    }
                }

            }
        }
        drawMachChart(dataMach);
        drawHuyetap(dataTamtruong, dataTamthu);
    }

    function vebieudoConCCTC(dataCCTC, dataCCTCGAY) {
        var canvas = document.getElementById('bieudochuyenda_concctc');
        var ctx = canvas.getContext('2d');
        var canvasWidth = canvas.width;
        var canvasHeight = canvas.height;
        ctx.clearRect(0, 0, canvasWidth, canvasHeight)
        var margin = 5;
        var Hmargin = 0;
        var column = 24;
        var row = 5;
        var chartWidth = canvasWidth - 2 * margin;
        var chartHeight = canvasHeight - 2 * Hmargin;
        var barWidth = chartWidth / column;

        veGridCanvas("bieudochuyenda_concctc", {
            margin: margin,
            Hmargin: Hmargin,
            column: column,
            row: row
        })
        function drawConCCTCChart(data, dataCCTCGAY) {

            ctx.lineWidth = 1;
            ctx.beginPath();

            ctx.fillStyle = '#ff4136';
            ctx.strokeStyle = '#ff4136';
            var x = 0;
            var y = 0;
            var stepH = (chartHeight / row);
            for (let i = 0; i < data.length; i++) {
                if(data[i].trim()) {
                    x = margin + i * barWidth;
                    y = canvasHeight - Hmargin - data[i] * stepH;
                    if(dataCCTCGAY[i] == 2) {
                        draw20To40Giay(x, y, 40, data[i]);
                    }
                    if(dataCCTCGAY[i] == 3) {
                        drawLarger40Giay(x, y, 40, data[i], stepH);
                    }
                }


            }
        }
        function draw20To40Giay(x, y, width, numRow) {
            ctx.strokeStyle = '#2691d9';
            ctx.lineWidth = 1;
            for(var i = 1; i < 8*numRow; i++) {
                ctx.beginPath();
                ctx.moveTo(x, y+i*4);
                ctx.lineTo(x+width, y+i*4);
                ctx.stroke();
            }
            for(var i = 1; i < 10; i++) {
                ctx.beginPath();
                ctx.moveTo(x+i*4, y);
                ctx.lineTo(x+i*4, chartHeight-Hmargin);
                ctx.stroke();
            }


        }
        function drawLarger40Giay(x, y, width, numRow, stepH) {
            ctx.strokeStyle = '#af4d20';
            ctx.lineWidth = 1;
            console.log("numRow", numRow)
            console.log("x, y", x, y)
            for(var i = 1; i <= numRow; i++) {
                for(var j = 1; j <= 5; j++) {
                    ctx.beginPath();
                    ctx.moveTo(x+j*8, y+(i-1)*stepH);
                    ctx.lineTo(x, y+(i-1)*stepH + j*6);
                    ctx.stroke();
                }
                for(var j = 1; j <= 5; j++) {
                    ctx.beginPath();
                    ctx.moveTo(x + width, y + (i-1)*stepH + j*6);
                    ctx.lineTo(x +j*8, y + i*stepH );
                    ctx.stroke();
                }
            }
        }

        drawConCCTCChart(dataCCTC, dataCCTCGAY);
    }

    $("#bieudochuyendawrap_hsba").html("");
    $("#bieudochuyendawrap").html("");
    thongtinhsba.thongtinbn.linkPhieuChuyenDa = [];
    dsDotDieuTri.forEach(function (itemDDT) {
        $.ajax({
            url: "cmu_getlist?url=" + convertArray([singletonObject.dvtt, itemDDT.SOVAOVIEN, "CMU_GET_BIEUDOCHUYENDA"]),
            type: "GET",
        }).done(function (dataPhieu) {
            if (dataPhieu.length > 0) {
                $("#hsba-phieuchuyenda-tab").show();
                $("#wrap_Menu_PhieuKhac").show();
                dataPhieu.forEach(function (item) {
                    $.get("cmu_getlist?url=" + convertArray([singletonObject.dvtt, item.ID, "CMU_GET_BIEUDOCHUYENDA_DT"]))
                        .done(function(data){
                            $("#bieudochuyendawrap_hsba").html(
                                "<canvas id='bieudochuyenda_timthai' width='960' height='336'></canvas>"+
                                "<canvas id='bieudochuyenda_ctc' width='960' height='318'></canvas>"+
                                "<canvas id='bieudochuyenda_huyetap' width='960' height='364'></canvas>"+
                                "<canvas id='bieudochuyenda_concctc' width='960' height='150'></canvas>"
                            )
                            var dataTimthai = [];
                            var dataCTC = [];
                            var dataTientriendau = [];
                            var dataHuyetaptamtruong = [];
                            var dataHuyetaptamthu = [];
                            var dataMach = [];
                            var dataCCTC = [];
                            var dataCCTCGAY = [];
                            data.forEach(function(obj){
                                dataTimthai.push(obj.NHIP_TIM_THAI);
                                dataCTC.push(obj.DO_MO_CTC);
                                dataTientriendau.push(obj.TIEN_TRIEN_DAU);
                                dataHuyetaptamtruong.push(obj.HUYET_AP_TREN);
                                dataHuyetaptamthu.push(obj.HUYET_AP_DUOI);
                                dataMach.push(obj.MACH);
                                dataCCTC.push(obj.CON_CO_TC);
                                dataCCTCGAY.push(obj.CON_CO_TC_GIAY);
                            })
                            vebieudoTimthai(dataTimthai)
                            vebieudoCTC(dataCTC,dataTientriendau)
                            vebieudoMachHuyetap(dataMach, dataHuyetaptamtruong, dataHuyetaptamthu)
                            vebieudoConCCTC(dataCCTC, dataCCTCGAY)
                            $.ajax({
                                url: "cmu_post_CMU_BIEUDOCHUYENDA_CHART_INS",
                                type: "POST",
                                data: {
                                    url: [singletonObject.dvtt, item.ID, 'TIMTHAI',
                                        $("#bieudochuyenda_timthai").get(0).toDataURL("image/png").replace("data:image/png;base64,", "")].join("```"),
                                },
                                async: false
                            })
                            $.ajax({
                                url: "cmu_post_CMU_BIEUDOCHUYENDA_CHART_INS",
                                type: "POST",
                                data: {
                                    url: [singletonObject.dvtt, item.ID, 'CTC',
                                        $("#bieudochuyenda_ctc").get(0).toDataURL("image/png").replace("data:image/png;base64,", "")].join("```"),
                                },
                                async: false
                            })
                            $.ajax({
                                url: "cmu_post_CMU_BIEUDOCHUYENDA_CHART_INS",
                                type: "POST",
                                data: {
                                    url: [singletonObject.dvtt, item.ID, 'MACHHUYETAP',
                                        $("#bieudochuyenda_huyetap").get(0).toDataURL("image/png").replace("data:image/png;base64,", "")].join("```"),
                                },
                                async: false
                            })
                            $.ajax({
                                url: "cmu_post_CMU_BIEUDOCHUYENDA_CHART_INS",
                                type: "POST",
                                data: {
                                    url: [singletonObject.dvtt, item.ID, 'CONCCTC',
                                        $("#bieudochuyenda_concctc").get(0).toDataURL("image/png").replace("data:image/png;base64,", "")].join("```"),
                                },
                                async: false
                            })
                            var arr = [item.ID, singletonObject.dvtt, itemDDT.SOVAOVIEN];
                            var url = "cmu_bieudochuyenda_report?url=" + convertArray(arr)+'&viewPDF=1';

                            thongtinhsba.thongtinbn.linkPhieuChuyenDa.push({
                                "url": [url],
                                "name": "Phiếu chuyển dạ " + item.ID,
                                "key": "PHIEUCHUYENDA",
                            });
                        });
                });
            }
        });
    });
}

function xemTheoDoiBilan(dsDotDieuTri) {
    thongtinhsba.thongtinbn.linkTheoDoiBilan = [];
    dsDotDieuTri.forEach(function (itemDDT) {
        $.ajax({
            url: 'cmu_getlist?url=' + convertArray([singletonObject.dvtt, itemDDT.SOBENHAN, itemDDT.SOVAOVIEN, itemDDT.SOVAOVIEN_DT, 'CMU_GET_LIST_PTD_BILAN']),
            type: "GET",
        }).done(function (dsPhieu) {
            if (dsPhieu.length > 0) {
                dsPhieu.forEach(function (itemPhieu) {
                    $.ajax({
                        url: 'cmu_getlist?url=' + convertArray([singletonObject.dvtt, itemPhieu.ID_PTD_BILAN, -1, 'CMU_GET_LIST_LANTHEODOI_BILAN']),
                        type: "GET",
                    }).done(function (dataPhieu) {
                        if (dataPhieu.length > 0) {
                            $("#hsba-phieubilan-tab").show();
                            $("#wrap_Menu_PhieuKhac").show();
                            var url = "";
                            var pageArr = [];
                            var totalPage = Math.ceil(dataPhieu.length / 11);
                            var page = 1;
                            for(var i = totalPage; i >= 1; i--) {
                                pageArr.push(i);
                                page++;
                            }
                            var dsTrang = [];
                            pageArr.forEach(function (item) {
                                let params = {
                                    dvtt: '',
                                    idPhieuTheoDoiBilan: itemPhieu.ID_PTD_BILAN,
                                    tenBenhNhan: thongtinhsba.thongtinbn.TEN_BENH_NHAN,
                                    tuoiBenhNhan: thongtinhsba.thongtinbn.TUOI_HT,
                                    gioiTinh: thongtinhsba.thongtinbn.GIOI_TINH,
                                    tenKhoa: thongtinhsba.thongtinbn.TEN_PHONGBAN,
                                    soGiuong: thongtinhsba.thongtinbn.SOGIUONG,
                                    chanDoan: thongtinhsba.thongtinbn.ICD_HT,
                                    page: item
                                }
                                var url = 'cmu_in_phieu_theo_doi_bilan?type=pdf&' + $.param(params);
                                dsTrang.push({
                                    name: "Trang " + (item+1),
                                    url: [url]
                                })
                            });
                            loadAndCombinePDFs(dsTrang).then(data => {
                                url = data;
                                getFilesign769(
                                    "PHIEU_NOITRU_BILAN",
                                    itemPhieu.ID_PTD_BILAN,
                                    -1,//singletonObject.userId,
                                    singletonObject.dvtt,
                                    thongtinhsba.thongtinbn.SOVAOVIEN,
                                    thongtinhsba.thongtinbn.SOVAOVIEN_DT,
                                    -1,
                                    function(dataKySo) {
                                        if(dataKySo.length > 0) {
                                            getCMUFileSigned769GetLink(dataKySo[0].KEYMINIO, 'pdf').then(pdfData => {
                                                url = pdfData;
                                                thongtinhsba.thongtinbn.linkTheoDoiBilan.push({
                                                    "url": [url],
                                                    "name": "Theo dõi Bilan " + itemPhieu.ID_PTD_BILAN,
                                                    "keyEMR": "BM_PHIEU_BILAN",
                                                    "key": "THEODOIBILAN",
                                                    "idPhieu": dataKySo[0].ID,
                                                });
                                            }).catch(error => {
                                                thongtinhsba.thongtinbn.linkTheoDoiBilan.push({
                                                    "url": [url],
                                                    "name": "Theo dõi Bilan " + itemPhieu.ID_PTD_BILAN,
                                                    "key": "THEODOIBILAN",
                                                });
                                            });
                                        } else {
                                            thongtinhsba.thongtinbn.linkTheoDoiBilan.push({
                                                "url": [url],
                                                "name": "Theo dõi Bilan " + itemPhieu.ID_PTD_BILAN,
                                                "key": "THEODOIBILAN",
                                            });
                                        }
                                    }
                                )
                            }).catch(error => {
                                notifiToClient("Red", "Có lỗi xảy ra:", error);
                            });
                        }
                    });
                });
            }
        });
    });
}

function getMenuBenhAnTong(dsDotDieuTri, callBackDone) {
    $(".menu-custom").hide();
    $(".hsba-tong-menu").hide();
    $(".sub-menu-cmu").hide();
    $(".collapse").removeClass('show');
    xemVoBenhAn().then(r => {
        callBackDone();
    });
    biaBenhAnTong();
    xemBangKe(dsDotDieuTri);
    xemKhamBenhVaoVien(dsDotDieuTri);
    xemToDieuTri(dsDotDieuTri);
    xemPhieuChamSoc(dsDotDieuTri);
    xemPhieuCNSV2(dsDotDieuTri);
    xemTTPT(dsDotDieuTri);
    xemCDHA(dsDotDieuTri);
    xemXetNghiem(dsDotDieuTri);
    xemDanhGiaDinhDuong(dsDotDieuTri);
    xemPhieuHoiChan(dsDotDieuTri);
    xemPhieuSoKet15NgayDieuTri(dsDotDieuTri);
    xemPhieuKhamChuyenKhoa(dsDotDieuTri);
    xemPhieuYeuCauSuDungKhangSinh(dsDotDieuTri);
    xemPhieuYeuCauSuDungKhangSinhUuTien(dsDotDieuTri);
    xemPhieuChuanBiTienPhau(dsDotDieuTri);
    xemPhieuTheoDoiChayThan(dsDotDieuTri);
    xemGiayCamDoanPTTT(dsDotDieuTri);
    xemGiayCamDoanChayThan(dsDotDieuTri);
    xemGiayCamDoanGayMe(dsDotDieuTri);
    xemGiaiPhauBenhSinhThiet(dsDotDieuTri);
    xemNoiQuyBenhVien(dsDotDieuTri);
    xemLoetTiDe(dsDotDieuTri);
    xemThongTu50(dsDotDieuTri);
    xemTruyenDich(dsDotDieuTri);
    xemTomTatDieuTri(dsDotDieuTri);
    xemBienBanKiemDiemTuVong(dsDotDieuTri);
    xemBienBanKiemThaoTuVong(dsDotDieuTri);
    xemChanDoanNguyenNhanTV(dsDotDieuTri);
    xemGiayRaVien(dsDotDieuTri);
    xemGiayHenKhamLai(dsDotDieuTri);
    xemPhieuChuyenTuyen(dsDotDieuTri);
    xemGiayBaoTu(dsDotDieuTri);
    xemTheoDoiBilan(dsDotDieuTri);
    xemPhieuTheoDoiGiucSanh(dsDotDieuTri);
    xemPhieuTheoDoi6hSauDe(dsDotDieuTri);
    xemPhieuTheoDoiTienSanGiat(dsDotDieuTri);
    xemPhieuThuPhanUngThuoc(dsDotDieuTri);
    xemPhieuPCA(dsDotDieuTri);
    xemPhieuTruyenMau(dsDotDieuTri);
    xemBienBanHop(dsDotDieuTri);
    xemPhieuLuongGiaHDCN(dsDotDieuTri);
    xemPhieuKhamChiDinhPHCN(dsDotDieuTri);
    xemPhieuKyThuatPHCN(dsDotDieuTri);
    xemCamKetPhauThuatGMHS(dsDotDieuTri);
    xemPhieuKhamBenhTheoYC(dsDotDieuTri);
    xemPhieuCungCapTTNB(dsDotDieuTri);
    xemCamKetTuChoiSDDV(dsDotDieuTri);
    xemCamKetChuyenCSKham(dsDotDieuTri);
    xemCamKetRaVienKBS(dsDotDieuTri);
    xemPhieuTomTatHSBA(dsDotDieuTri);
    xemCamKetDTHoaXaTri(dsDotDieuTri);
    xemCamKetDTXaTri(dsDotDieuTri);
    xemPhieuPhanLoaiNB(dsDotDieuTri);
    xemPhieuBanGiaoBS(dsDotDieuTri);
    xemPhieuDeXuatPTTT(dsDotDieuTri);
    xemGiayCamDoanSuDung(dsDotDieuTri);
    xemPhieuDYXNHIVGhiTen(dsDotDieuTri);
    xemPhieuDangKyKBTheoYC(dsDotDieuTri);
    xemTDSuDungKhangSinhDP(dsDotDieuTri);
    xemPhieuBanGiaoDD(dsDotDieuTri);
    xemPhieuChamSocCap1(dsDotDieuTri)
    xemPhieuChamSocCap2(dsDotDieuTri)
    xemPhieuKiemTiemChungTreSS(dsDotDieuTri)
    xemPhieuKeHoachChamSoc(dsDotDieuTri)
    xemPhieuCSC1Khac(dsDotDieuTri)
    xemPhieuChanDoanNNTuVong(dsDotDieuTri)
    xemPhieuTTTTBenhNangXinVe(dsDotDieuTri)
    loadDSLanPhauThuatHSBATong(dsDotDieuTri).then(data => {
        xemBangKiemTruocPhauThuat(data);
        xemBangKiemAnToan(data);
        xemBangKiemGac(data);
        xemKhamTienMe(data);
        xemGayMeHoiSuc(data);
    });
    // if (singletonObject.thamSo960011 == 1) {
    //     xemTienSuDiUng(dsDotDieuTri)
    // }
    // if (singletonObject.thamSo960012 == 1) {
    //     xemPhieuGlasgow(dsDotDieuTri);
    // }
    // if (singletonObject.thamSo960013 == 1) {
    //     xemNghiemPhapRoiLoanNuot(dsDotDieuTri);
    // }
    // if (singletonObject.thamSo960014 == 1) {
    //     xemPhieuChuyenDa(dsDotDieuTri);
    // }
    xemTienSuDiUng(dsDotDieuTri)
    xemPhieuGlasgow(dsDotDieuTri);
    xemNghiemPhapRoiLoanNuot(dsDotDieuTri);
    xemPhieuChuyenDa(dsDotDieuTri);
}

function distinctData(dataArray, fields) {
    var uniqueSet = new Set();
    var distinctArray = [];
    dataArray.forEach(item => {
        var newItem = {};
        fields.forEach(field => {
            newItem[field] = item[field];
        });
        var key = fields.map(field => newItem[field]).join('|');
        if (!uniqueSet.has(key)) {
            uniqueSet.add(key);
            distinctArray.push(newItem);
        }
    });
    return distinctArray;
}

function xemTatCaBenhAn(idLoader, idWrap) {
    $("#" + idWrap).html("");
    var dsLinkDownload = [
        ...thongtinhsba.thongtinbn.linkBiaBATong,
        ...thongtinhsba.thongtinbn.linkVBA,
        ...thongtinhsba.thongtinbn.linkBangKe,
        ...thongtinhsba.thongtinbn.linkKhamBenhVaoVien,
        ...thongtinhsba.thongtinbn.linkTDT,
        ...thongtinhsba.thongtinbn.linkThongTu50,
        ...thongtinhsba.thongtinbn.linkGiayRaVien,
        ...thongtinhsba.thongtinbn.linkPhieuChuyenTuyen,
        ...thongtinhsba.thongtinbn.linkGiayBaoTu,
        ...thongtinhsba.thongtinbn.linkHenKhamLai,

        //Phieu CLS
        ...thongtinhsba.thongtinbn.linkXetNghiem,
        ...thongtinhsba.thongtinbn.linkCDHA,
        ...thongtinhsba.thongtinbn.linkTTPT,

        //Phieu khac
        ...thongtinhsba.thongtinbn.linkPCS,
        ...thongtinhsba.thongtinbn.linkCNS,
        ...thongtinhsba.thongtinbn.linkDGDD,
        ...thongtinhsba.thongtinbn.linkPHC,
        ...thongtinhsba.thongtinbn.linkSK15NDT,
        ...thongtinhsba.thongtinbn.linkKhamChuyenKhoa,
        ...thongtinhsba.thongtinbn.linkPhieuYeuCauSuDungKhangSinh,
        ...thongtinhsba.thongtinbn.linkPhieuYeuCauSuDungKhangSinhUuTien,
        ...thongtinhsba.thongtinbn.linkPhieuChuanBiTienPhau,
        ...thongtinhsba.thongtinbn.linkPhieuTheoDoiChayThan,
        ...thongtinhsba.thongtinbn.linkNghiemPhapRoiLoanNuot,
        ...thongtinhsba.thongtinbn.linkGiayCamDoanPTTT,
        ...thongtinhsba.thongtinbn.linkTruyenMau,
        ...thongtinhsba.thongtinbn.linkGiayCamDoanChayThan,
        ...thongtinhsba.thongtinbn.linkGiayCamDoanGayMe,
        ...thongtinhsba.thongtinbn.linkGiaiPhauBenhSinhThiet,
        ...thongtinhsba.thongtinbn.linkKetQuaGiaiPhauBenhSinhThiet,
        ...thongtinhsba.thongtinbn.linkKiemTruocPhauThuat,
        ...thongtinhsba.thongtinbn.linkNoiQuyBenhVien,
        ...thongtinhsba.thongtinbn.linkLoetTiDe,
        ...thongtinhsba.thongtinbn.linkTruyenDich,
        ...thongtinhsba.thongtinbn.linkPhieuGlasgow,
        ...thongtinhsba.thongtinbn.linkTomTatDieuTri,
        ...thongtinhsba.thongtinbn.linkBienBanKiemDiemTuVong,
        ...thongtinhsba.thongtinbn.linkBienBanKiemThaoTuVong,
        ...thongtinhsba.thongtinbn.linkChanDoanNguyenNhanTV,
        ...thongtinhsba.thongtinbn.linkTienSuDiUng,
        ...thongtinhsba.thongtinbn.linkPhieuChuyenDa,
        ...thongtinhsba.thongtinbn.linkTheoDoiBilan,
        ...thongtinhsba.thongtinbn.linkTheoDoiGiucSanh,
        ...thongtinhsba.thongtinbn.linkTheoDoi6hSauDe,
        ...thongtinhsba.thongtinbn.linkTheoDoiTienSanGiat,
        ...thongtinhsba.thongtinbn.linkThuPhanUngThuoc,
        ...thongtinhsba.thongtinbn.linkPCA,
        ...thongtinhsba.thongtinbn.linkBienBanHop,
        ...thongtinhsba.thongtinbn.linkLuongGiaHDCN,
        ...thongtinhsba.thongtinbn.linkKhamChiDinhPHCN,
        ...thongtinhsba.thongtinbn.linkKyThuatPHCN,
        ...thongtinhsba.thongtinbn.linkCamKetPhauThuatGMHS,
        ...thongtinhsba.thongtinbn.linkKhamBenhTheoYC,
        ...thongtinhsba.thongtinbn.linkCungCapThongTinNguoiBenh,
        ...thongtinhsba.thongtinbn.linkCamKetTuChoiSDDV,
        ...thongtinhsba.thongtinbn.linkChuyenCSKhamBenh,
        ...thongtinhsba.thongtinbn.linkCamKetRaVienKhongTheoBS,
        ...thongtinhsba.thongtinbn.linkTomTatHSBA,
        ...thongtinhsba.thongtinbn.linkCamKetDTHoaXaTri,
        ...thongtinhsba.thongtinbn.linkCamKetDTXaTri,
        ...thongtinhsba.thongtinbn.linkPhanLoaiNguoiBenh,
        ...thongtinhsba.thongtinbn.linkBanGiaoBS,
        ...thongtinhsba.thongtinbn.linkDeXuatPTTT,
        ...thongtinhsba.thongtinbn.linkGiayCamDoanSuDung,
        ...thongtinhsba.thongtinbn.linkDYXetNghiemHIVGhiTen,
        ...thongtinhsba.thongtinbn.linkDangKyKBTheoYC,
        ...thongtinhsba.thongtinbn.linkTheoDoiSDKhangSinhDP,
        ...thongtinhsba.thongtinbn.linkBanGiaoDD,
        ...thongtinhsba.thongtinbn.linkKiemTiemChungTreSS,
        ...thongtinhsba.thongtinbn.linkKeHoachChamSoc,
        ...thongtinhsba.thongtinbn.linkCSC1Khac,
        ...thongtinhsba.thongtinbn.linkChanDoanNNTuVong,
        ...thongtinhsba.thongtinbn.linkTTTTBenhNangXinVe,

        //Phẫu thuật
        ...thongtinhsba.thongtinbn.linkKiemTruocPhauThuat,
        ...thongtinhsba.thongtinbn.linkBangKiemAnToan,
        ...thongtinhsba.thongtinbn.linkBangKiemGac,
        ...thongtinhsba.thongtinbn.linkKhamTienMe,
        ...thongtinhsba.thongtinbn.linkGayMeHoiSuc,
    ];
    // if (singletonObject.thamSo960014 == 1) { dsLinkDownload = [...dsLinkDownload, ...thongtinhsba.thongtinbn.linkPhieuChuyenDa]; }
    // if (singletonObject.thamSo960011 == 1) { dsLinkDownload = [...dsLinkDownload, ...thongtinhsba.thongtinbn.linkTienSuDiUng]; }
    // if (singletonObject.thamSo960012 == 1) { dsLinkDownload = [...dsLinkDownload, ...thongtinhsba.thongtinbn.linkPhieuGlasgow]; }
    // if (singletonObject.thamSo960013 == 1) { dsLinkDownload = [...dsLinkDownload, ...thongtinhsba.thongtinbn.linkNghiemPhapRoiLoanNuot]; }
    thongtinhsba.thongtinbn.dsLinkDownload = dsLinkDownload;
    console.log(thongtinhsba.thongtinbn.dsLinkDownload)
    showLoaderIntoWrapId(idLoader)
    loadAndCombinePDFs(dsLinkDownload).then(data => {
        $("#" + idWrap).html('<iframe id="loadIframePreview" src="" width="100%" height="100%" style="overflow: auto;"></iframe>')
        $("#" +idWrap + " #loadIframePreview").prop('src', data);
        hideLoaderIntoWrapId(idLoader)
    }).catch(error => {
        notifiToClient("Red", "Có lỗi xảy ra:", error);
        hideLoaderIntoWrapId(idLoader)
    });
}

$(function() {
    var groupTitlesEmr = {
        HO_SO_BENH_AN: "Hồ sơ bệnh án",
        BM_PHIEU_CAMKETCHUNG_146: "Bản cam kết chung",
        BM_PHIEU_PHIEUDIEUTRI_12: "Phiếu điều trị",
        BM_PHIEU_PLNB_341: "Phiếu phân loại người bệnh",
        BM_PHIEU_BANGIAONGUOIBENH_340: "Phiếu bàn giao người bệnh",
        BM_PHIEU_TRUYENMAU_101: "Phiếu truyền máu",
        BM_PHIEU_BAOCAO_DUYETMO_122: "Báo cáo duyệt mổ",
        BM_PHIEU_THU_PHANUNG_THUOC_98: "Phiếu thử phản ứng thuốc",
        PHIEUTHUCHIEN_KYTHUAT_PHCN_A4: "PHIẾU THỰC HIỆN KỸ THUẬT PHỤC HỒI CHỨC NĂNG (TT32)",
        PHIEULUONGGIA_HDCN_A4: "PHIẾU THỰC HIỆN KỸ THUẬT PHỤC HỒI CHỨC NĂNG (TT32)",
        PHIEUKHAMVACHIDINH_PHCN_A4: "PHIẾU KHÁM VÀ CHỈ ĐỊNH PHỤC HỒI CHỨC NĂNG (TT32)",
        BM_PHIEU_PHIEUCHAMSOC_13: "Phiếu chăm sóc",
        BM_PHIEU_TUVAN_338: "Phiếu tư vấn nhập",
        BM_PHIEU_TUVAN_XATRI_339: "339.Phiếu tư vấn và theo dõi xạ trị",
        BM_PHIEU_PHAUTHUAT_163: "Phiếu phẫu thuật",
        //Bệnh án
        TAYCHANMIENG: "Bệnh án tay chân miệng",
        NOI: "Bệnh án nội khoa",
        NHI: "Bệnh án nhi",
        NHIEM: "Bệnh án truyền nhiễm",
        PHUKHOA: "Bệnh án phụ khoa",
        SANKHOA: "Bệnh án sản khoa",
        SOSINH: "Bệnh án sơ sinh",
        TAMTHAN: "Bệnh án tâm thần",
        DALIEU: "Bệnh án da liễu",
        UNGBUOU: "Bệnh án ung bướu",
        RANGHAMMAT: "Bệnh án răng hàm mặt",
        TAIMUIHONG: "Bệnh án tai miệng họng",
        NGOAITRU_CHUNG: "Bệnh án ngoại trú chung",
        NGOAIKHOA: "Bệnh án ngoại khoa",
        NOITRUYHCT: "Bệnh án nội trú y học cổ truyền",
        DAYMAT: "Bệnh án đáy mắt",
        MATLAC: "Bệnh án mắt lác",
        MATTREEM: "Bệnh án mắt trẻ em",
        MATGLOCOM: "Bệnh án mắt glocom",
        BONG: "Bệnh án bỏng",
        //
        BM_PHIEU_BANGKE_10: "Bảng kê chi phí KCB",
        BM_PHIEU_THEODOI_CNSONG_113: "Phiếu theo dõi chức năng sống",
        BM_PHIEU_CHIDINH_XETNGHIEM_BHYT_16: "Phiếu chỉ định XN",
        BM_PHIEU_CHIDINH_XETNGHIEM_152: "Phiếu chỉ định Xet Nghiệm",
        BM_PHIEU_KETQUA_XETNGHIEM_17: "Phiếu thực hiện xét nghiệm",
        BM_PHIEU_CHIDINH_CDHA_BHYT_5: "Phiếu chỉ định CDHA",
        BM_PHIEU_CHIDINH_CDHA_151: "Phiếu chỉ định CDHA",
        BM_PHIEU_KETQUA_CDHA_7: "Phiếu kết quả CDHA",
        BM_PHIEU_CHIDINH_PTTT_BHYT_18: "Phiếu chỉ định PTTT (BHYT)",
        BM_PHIEU_CHIDINH_PTTT_150: "Phiếu chỉ định PTTT",
        BM_PHIEU_SANGLOC_DINHDUONG_DTRI_155: "Phiếu sàng lọc dinh dưỡng nội trú",
        BM_PHIEU_BIENBAN_HOICHAN_15: "Biên bản hội chẩn",
        BM_PHIEU_SOKET_DIEUTRI_14: "Phiếu sơ kết điều trị",
        BM_PHIEU_KHAMCHUYENKHOA_77: "Phiếu khám chuyên khoa",
        BM_PHIEU_YC_SUDUNG_KHANGSINH_174: "Phiếu yêu cầu sử dụng kháng sinh ưu tiên quản lý",
        BM_PHIEU_BANGKIEM_BENHNHAN_TRUOCMO_172: "Phiếu chuẩn bị bệnh nhân trước mổ",
        BM_PHIEU_THEODOI_CHAYTHAN_157: "Phiếu theo dõi chạy thận nhân tạo",
        RPT_SCAN: "Phiếu Scan",
        BM_PHIEU_KETQUA_XETNGHIEM_SINHTHIET_17: "Phiếu thực hiện xét nghiệm sinh thiết",
        BM_PHIEU_KIEMSOAT_TRUOCMO_123: "Bảng kiểm soát bệnh nhân trước mổ",
        BM_PHIEU_BANGKIEM_ATPTTT_86: "Bảng kiểm ATPTTT",
        BM_PHIEU_BANGKIEM_BONGGIAC_87: "Bảng kiểm bông gạc",
        BM_PHIEU_SANGLOC_LOET_TYDE_190: "Phiếu đánh giá loét tỳ đè",
        BM_PHIEU_KHAM_TIEN_ME_154: "Phiếu khám tiền mê (vs2)",
        BM_PHIEU_GAYME_HOISUC_124: "Phiếu gây mê hồi sức",
        BM_PHIEU_CONGKHAI_THUOC_23: "Phiếu công khai đơn thuốc",
        BM_PHIEU_TRUYENDICH_22: "Phiếu truyền dịch",
        BM_PHIEU_DANHGIAHM_343: "343. Phiếu đánh giá hôn mê",
        BM_PHIEU_KIEMTHAO_TUVONG_119: "Biên bản kiểm thảo tử vong",
        BM_PHIEU_GIAYRAVIEN_11: "Giấy ra viện",
        BM_PHIEU_KHAMBENH_VAOVIEN_6: "Phiếu khám bệnh vào viện",
        BM_PHIEU_THEODOI_DIUNG_180: "Thẻ theo dõi dị ứng",
        BM_PHIEU_BILAN: "342. Phiếu theo dõi BILAN",
        BM_PHIEU_PHIEU_HENTAIKHAM_25: "Phiếu hẹn tái khám",
    };
    var processedDataEmr, dataEmr

    $('#v-menu-tab1').on('shown.bs.tab', function (e) {
        $("#v-menu-tab1").find('.nav-link').removeClass('active');
        $(e.target).addClass('active');
    });

    $(document).on('show.bs.modal', '.modal', function() {
        var zIndex = 1040 + 10 * $('.modal:visible').length;
        $(this).css('z-index', zIndex);
        setTimeout(() => $('.modal-backdrop').not('.modal-stack').css('z-index', zIndex - 1).addClass('modal-stack'));
    });

    $("#hsba-xembatong-tab").click(function () {
        xemTatCaBenhAn("wrap_modalBATong", "pdfWrapTong");
    });

    $("#hsba-ttba-tab").click(function() {
        showLoaderIntoWrapId("wrap_modalBATong")
        showPDFBenhAnTongHop({
            key: "VBA",
            dsPhieu: thongtinhsba.thongtinbn.linkVBA,
            idWrapPdf: "pdfVoBenhAnWrap",
            idWrapLoader: "wrap_modalBATong"
        });
    });

    $("#hsba-bangke-tab").click(function() {
        showPDFBenhAnTongHop({
            key: "BANGKE",
            dsPhieu: thongtinhsba.thongtinbn.linkBangKe,
            idWrapPdf: "pdfWrap",
            idWrapLoader: "wrap_modalBATong"
        });
    });

    $("#hsba-todieutri-tab").click(function() {
        showPDFBenhAnTongHop({
            key: "TDT",
            dsPhieu: thongtinhsba.thongtinbn.linkTDT,
            idWrapPdf: "pdfWrap",
            idWrapLoader: "wrap_modalBATong"
        });
    });

    $("#hsba-phieuchamsoc-tab").click(function() {
        showPDFBenhAnTongHop({
            key: "PCS",
            dsPhieu: thongtinhsba.thongtinbn.linkPCS,
            idWrapPdf: "pdfWrap",
            idWrapLoader: "wrap_modalBATong"
        });
    });

    $("#hsba-phieuchucnangsong-tab").click(function() {
        showPDFBenhAnTongHop({
            key: "CNS",
            dsPhieu: thongtinhsba.thongtinbn.linkCNS,
            idWrapPdf: "pdfWrap",
            idWrapLoader: "wrap_modalBATong"
        });
    });

    $("#hsba-xetnghiem-tab").click(function () {
        $(".btn-cls").hide();
        $(".btn-xn").show();
        $("#hsba-kqxn-lammoi").click();
    });
    $("#hsba-kqxn-lammoi").click(function () {
        showPDFBenhAnTongHop({
            key: "KQXN",
            dsPhieu: thongtinhsba.thongtinbn.linkXetNghiem,
            idWrapPdf: "pdfCLSWrap",
            idWrapLoader: "wrap_modalBATong"
        });
    });
    $("#hsba-cdxn-lammoi").click(function () {
        showPDFBenhAnTongHop({
            key: "CDXN",
            dsPhieu: thongtinhsba.thongtinbn.linkXetNghiem,
            idWrapPdf: "pdfCLSWrap",
            idWrapLoader: "wrap_modalBATong"
        });
    });

    $("#hsba-cdha-tab").click(function () {
        $(".btn-cls").hide();
        $(".btn-cdha").show();
        $("#hsba-kqcdha-lammoi").click();
    });
    $("#hsba-kqcdha-lammoi").click(function () {
        showPDFBenhAnTongHop({
            key: "KQCDHA",
            dsPhieu: thongtinhsba.thongtinbn.linkCDHA,
            idWrapPdf: "pdfCLSWrap",
            idWrapLoader: "wrap_modalBATong"
        });
    });
    $("#hsba-cdcdha-lammoi").click(function () {
        showPDFBenhAnTongHop({
            key: "CDCDHA",
            dsPhieu: thongtinhsba.thongtinbn.linkCDHA,
            idWrapPdf: "pdfCLSWrap",
            idWrapLoader: "wrap_modalBATong"
        });
    });

    $("#hsba-ttpt-tab").click(function () {
        $(".btn-cls").hide();
        $(".btn-ttpt").show();
        $("#hsba-kqttpt-lammoi").click();
    });
    $("#hsba-kqttpt-lammoi").click(function () {
        showPDFBenhAnTongHop({
            key: "KQTTPT",
            dsPhieu: thongtinhsba.thongtinbn.linkTTPT,
            idWrapPdf: "pdfCLSWrap",
            idWrapLoader: "wrap_modalBATong"
        });
    });
    $("#hsba-cdttpt-lammoi").click(function () {
        showPDFBenhAnTongHop({
            key: "CDTTPT",
            dsPhieu: thongtinhsba.thongtinbn.linkTTPT,
            idWrapPdf: "pdfCLSWrap",
            idWrapLoader: "wrap_modalBATong"
        });
    });

    $("#hsba-danhgiadinhduong-tab").click(function () {
        showPDFBenhAnTongHop({
            key: "DGDD",
            dsPhieu: thongtinhsba.thongtinbn.linkDGDD,
            idWrapPdf: "pdfWrap",
            idWrapLoader: "wrap_modalBATong"
        });
    });

    $("#hsba-phieuhoichan-tab").click(function () {
        showPDFBenhAnTongHop({
            key: "PHIEUHOICHAN",
            dsPhieu: thongtinhsba.thongtinbn.linkPHC,
            idWrapPdf: "pdfWrap",
            idWrapLoader: "wrap_modalBATong"
        });
    });

    $("#hsba-phieusoket15ngay-tab").click(function () {
        showPDFBenhAnTongHop({
            key: "PHIEUSOKET15NGAYDIEUTRI",
            dsPhieu: thongtinhsba.thongtinbn.linkSK15NDT,
            idWrapPdf: "pdfWrap",
            idWrapLoader: "wrap_modalBATong"
        });
    });

    $("#hsba-phieuchandoannntuvong-tab").click(function () {
        showPDFBenhAnTongHop({
            key: "CHANDOANNNTUVONG",
            dsPhieu: thongtinhsba.thongtinbn.linkChanDoanNNTuVong,
            idWrapPdf: "pdfWrap",
            idWrapLoader: "wrap_modalBATong"
        });
    });

    $("#hsba-phieuttttbenhnangxinve-tab").click(function () {
        showPDFBenhAnTongHop({
            key: "TTTTBENHNANGXINVE",
            dsPhieu: thongtinhsba.thongtinbn.linkTTTTBenhNangXinVe,
            idWrapPdf: "pdfWrap",
            idWrapLoader: "wrap_modalBATong"
        });
    });

    $("#hsba-phieukhamchuyenkhoa-tab").click(function () {
        showPDFBenhAnTongHop({
            key: "PHIEUKHAMCHUYENKHOA",
            dsPhieu: thongtinhsba.thongtinbn.linkKhamChuyenKhoa,
            idWrapPdf: "pdfWrap",
            idWrapLoader: "wrap_modalBATong"
        });
    });

    $("#hsba-phieuyeucausudungkhangsinh-tab").click(function () {
        showPDFBenhAnTongHop({
            key: "PHIEUYCSDKS",
            dsPhieu: thongtinhsba.thongtinbn.linkPhieuYeuCauSuDungKhangSinh,
            idWrapPdf: "pdfWrap",
            idWrapLoader: "wrap_modalBATong"
        });
    });

    $("#hsba-phieuyeucausudungkhangsinhuutien-tab").click(function () {
        showPDFBenhAnTongHop({
            key: "PHIEUYCSDKSUT",
            dsPhieu: thongtinhsba.thongtinbn.linkPhieuYeuCauSuDungKhangSinhUuTien,
            idWrapPdf: "pdfWrap",
            idWrapLoader: "wrap_modalBATong"
        });
    });

    $("#hsba-phieuchuanbitienphau-tab").click(function () {
        showPDFBenhAnTongHop({
            key: "PHIEUCHUANBITIENPHAU",
            dsPhieu: thongtinhsba.thongtinbn.linkPhieuChuanBiTienPhau,
            idWrapPdf: "pdfWrap",
            idWrapLoader: "wrap_modalBATong"
        });
    });

    $("#hsba-theodoichaythan-tab").click(function () {
        showPDFBenhAnTongHop({
            key: "PHIEUTHEODOICHAYTHAN",
            dsPhieu: thongtinhsba.thongtinbn.linkPhieuTheoDoiChayThan,
            idWrapPdf: "pdfWrap",
            idWrapLoader: "wrap_modalBATong"
        });
    });

    $("#hsba-danhgiaroiloannuot-tab").click(function(){
        showPDFBenhAnTongHop({
            key: "NGHIEMPHAPROILOANNUOT",
            dsPhieu: thongtinhsba.thongtinbn.linkNghiemPhapRoiLoanNuot,
            idWrapPdf: "pdfWrap",
            idWrapLoader: "wrap_modalBATong"
        });
    });

    $("#hsba-phieutruyenmau-tab").click(function(){
        showPDFBenhAnTongHop({
            key: "PHIEUTRUYENMAU",
            dsPhieu: thongtinhsba.thongtinbn.linkTruyenMau,
            idWrapPdf: "pdfWrap",
            idWrapLoader: "wrap_modalBATong"
        });
    });

    $("#hsba-giaycamdoanpttt-tab").click(function(){
        showPDFBenhAnTongHop({
            key: "CAMDOANPHAUTHUAT",
            dsPhieu: thongtinhsba.thongtinbn.linkGiayCamDoanPTTT,
            idWrapPdf: "pdfWrap",
            idWrapLoader: "wrap_modalBATong"
        });
    });

    $("#hsba-giaycamdoanchaythan-tab").click(function(){
        showPDFBenhAnTongHop({
            key: "CAMDOANCHAYTHAN",
            dsPhieu: thongtinhsba.thongtinbn.linkGiayCamDoanChayThan,
            idWrapPdf: "pdfWrap",
            idWrapLoader: "wrap_modalBATong"
        });
    });

    $("#hsba-giaycamdoangayme-tab").click(function(){
        showPDFBenhAnTongHop({
            key: "CAMDOANGAYME",
            dsPhieu: thongtinhsba.thongtinbn.linkGiayCamDoanGayMe,
            idWrapPdf: "pdfWrap",
            idWrapLoader: "wrap_modalBATong"
        });
    });

    $("#hsba-phieutdgiucsanh-tab").click(function(){
        showPDFBenhAnTongHop({
            key: "THEODOIGIUCSANH",
            dsPhieu: thongtinhsba.thongtinbn.linkTheoDoiGiucSanh,
            idWrapPdf: "pdfWrap",
            idWrapLoader: "wrap_modalBATong"
        });
    });

    $("#hsba-phieutd6hsaude-tab").click(function(){
        showPDFBenhAnTongHop({
            key: "THEODOI6HSAUDE",
            dsPhieu: thongtinhsba.thongtinbn.linkTheoDoi6hSauDe,
            idWrapPdf: "pdfWrap",
            idWrapLoader: "wrap_modalBATong"
        });
    });

    $("#hsba-phieutdtiensangiat-tab").click(function(){
        showPDFBenhAnTongHop({
            key: "THEODOITIENSANGIAT",
            dsPhieu: thongtinhsba.thongtinbn.linkTheoDoiTienSanGiat,
            idWrapPdf: "pdfWrap",
            idWrapLoader: "wrap_modalBATong"
        });
    });

    $("#hsba-phieuthuphanungthuoc-tab").click(function(){
        showPDFBenhAnTongHop({
            key: "PHIEUTHUPHANUNGTHUOC",
            dsPhieu: thongtinhsba.thongtinbn.linkThuPhanUngThuoc,
            idWrapPdf: "pdfWrap",
            idWrapLoader: "wrap_modalBATong"
        });
    });

    $("#hsba-phieupca-tab").click(function(){
        showPDFBenhAnTongHop({
            key: "PHIEUPCA",
            dsPhieu: thongtinhsba.thongtinbn.linkPCA,
            idWrapPdf: "pdfWrap",
            idWrapLoader: "wrap_modalBATong"
        });
    });

    $("#hsba-bienbanhop-tab").click(function(){
        showPDFBenhAnTongHop({
            key: "BIENBANHOP",
            dsPhieu: thongtinhsba.thongtinbn.linkBienBanHop,
            idWrapPdf: "pdfWrap",
            idWrapLoader: "wrap_modalBATong"
        });
    });

    $("#hsba-phieuluonggiahdcn-tab").click(function(){
        showPDFBenhAnTongHop({
            key: "LUONGIAHDCN",
            dsPhieu: thongtinhsba.thongtinbn.linkLuongGiaHDCN,
            idWrapPdf: "pdfWrap",
            idWrapLoader: "wrap_modalBATong"
        });
    });

    $("#hsba-phieukhamchidinhphcn-tab").click(function(){
        showPDFBenhAnTongHop({
            key: "KHAMCHIDINHPHCN",
            dsPhieu: thongtinhsba.thongtinbn.linkKhamChiDinhPHCN,
            idWrapPdf: "pdfWrap",
            idWrapLoader: "wrap_modalBATong"
        });
    });

    $("#hsba-phieukythuatphcn-tab").click(function(){
        showPDFBenhAnTongHop({
            key: "KYTHUATPHCN",
            dsPhieu: thongtinhsba.thongtinbn.linkKyThuatPHCN,
            idWrapPdf: "pdfWrap",
            idWrapLoader: "wrap_modalBATong"
        });
    });

    $("#hsba-phieuckphauthuatgmhs-tab").click(function(){
        showPDFBenhAnTongHop({
            key: "CAMKETPHAUTHUATGMHS",
            dsPhieu: thongtinhsba.thongtinbn.linkCamKetPhauThuatGMHS,
            idWrapPdf: "pdfWrap",
            idWrapLoader: "wrap_modalBATong"
        });
    });

    $("#hsba-phieukhambenhtheoyc-tab").click(function(){
        showPDFBenhAnTongHop({
            key: "KHAMBENHTHEOYC",
            dsPhieu: thongtinhsba.thongtinbn.linkKhamBenhTheoYC,
            idWrapPdf: "pdfWrap",
            idWrapLoader: "wrap_modalBATong"
        });
    });

    $("#hsba-phieucungcapttnb-tab").click(function(){
        showPDFBenhAnTongHop({
            key: "CUNGCAPTHONGTINNGUOIBENH",
            dsPhieu: thongtinhsba.thongtinbn.linkCungCapThongTinNguoiBenh,
            idWrapPdf: "pdfWrap",
            idWrapLoader: "wrap_modalBATong"
        });
    });

    $("#hsba-phieucktuchoiSDDV-tab").click(function(){
        showPDFBenhAnTongHop({
            key: "CAMKETTUCHOISDDV",
            dsPhieu: thongtinhsba.thongtinbn.linkCamKetTuChoiSDDV,
            idWrapPdf: "pdfWrap",
            idWrapLoader: "wrap_modalBATong"
        });
    });

    $("#hsba-phieuckchuyencosokb-tab").click(function(){
        showPDFBenhAnTongHop({
            key: "CHUYENCOSOKHAMBENH",
            dsPhieu: thongtinhsba.thongtinbn.linkChuyenCSKhamBenh,
            idWrapPdf: "pdfWrap",
            idWrapLoader: "wrap_modalBATong"
        });
    });

    $("#hsba-phieuckravienkhongbs-tab").click(function(){
        showPDFBenhAnTongHop({
            key: "RAVIENKHONGTHEOBS",
            dsPhieu: thongtinhsba.thongtinbn.linkCamKetRaVienKhongTheoBS,
            idWrapPdf: "pdfWrap",
            idWrapLoader: "wrap_modalBATong"
        });
    });

    $("#hsba-phieutomtathsba-tab").click(function(){
        showPDFBenhAnTongHop({
            key: "TOMTATHSBA",
            dsPhieu: thongtinhsba.thongtinbn.linkTomTatHSBA,
            idWrapPdf: "pdfWrap",
            idWrapLoader: "wrap_modalBATong"
        });
    });

    $("#hsba-phieucamketdthoaxatri-tab").click(function(){
        showPDFBenhAnTongHop({
            key: "CAMKETDTHOAXATRI",
            dsPhieu: thongtinhsba.thongtinbn.linkCamKetDTHoaXaTri,
            idWrapPdf: "pdfWrap",
            idWrapLoader: "wrap_modalBATong"
        });
    });

    $("#hsba-phieucamketdtxatri-tab").click(function(){
        showPDFBenhAnTongHop({
            key: "CAMKETDTXATRI",
            dsPhieu: thongtinhsba.thongtinbn.linkCamKetDTXaTri,
            idWrapPdf: "pdfWrap",
            idWrapLoader: "wrap_modalBATong"
        });
    });

    $("#hsba-phieuphanloainb-tab").click(function(){
        showPDFBenhAnTongHop({
            key: "PHANLOAINGUOIBENH",
            dsPhieu: thongtinhsba.thongtinbn.linkPhanLoaiNguoiBenh,
            idWrapPdf: "pdfWrap",
            idWrapLoader: "wrap_modalBATong"
        });
    });

    $("#hsba-phieubangiaobs-tab").click(function(){
        showPDFBenhAnTongHop({
            key: "BANGIAOBACSI",
            dsPhieu: thongtinhsba.thongtinbn.linkBanGiaoBS,
            idWrapPdf: "pdfWrap",
            idWrapLoader: "wrap_modalBATong"
        });
    });

    $("#hsba-phieudexuatpttt-tab").click(function(){
        showPDFBenhAnTongHop({
            key: "DEXUATPTTT",
            dsPhieu: thongtinhsba.thongtinbn.linkDeXuatPTTT,
            idWrapPdf: "pdfWrap",
            idWrapLoader: "wrap_modalBATong"
        });
    });

    $("#hsba-giaycamdoansudung-tab").click(function(){
        showPDFBenhAnTongHop({
            key: "GIAYCAMDOANSUDUNG",
            dsPhieu: thongtinhsba.thongtinbn.linkGiayCamDoanSuDung,
            idWrapPdf: "pdfWrap",
            idWrapLoader: "wrap_modalBATong"
        });
    });

    $("#hsba-phieudyxnhivghiten-tab").click(function(){
        showPDFBenhAnTongHop({
            key: "DONGYXETNGHIEMHIVGHITEN",
            dsPhieu: thongtinhsba.thongtinbn.linkDYXetNghiemHIVGhiTen,
            idWrapPdf: "pdfWrap",
            idWrapLoader: "wrap_modalBATong"
        });
    });

    $("#hsba-phieudangkykbtheoyc-tab").click(function(){
        showPDFBenhAnTongHop({
            key: "DANGKYKBTHEOYC",
            dsPhieu: thongtinhsba.thongtinbn.linkDangKyKBTheoYC,
            idWrapPdf: "pdfWrap",
            idWrapLoader: "wrap_modalBATong"
        });
    });

    $("#hsba-phieutdsdkhangsinhdp-tab").click(function(){
        showPDFBenhAnTongHop({
            key: "THEODOISDKHANGSINHDP",
            dsPhieu: thongtinhsba.thongtinbn.linkTheoDoiSDKhangSinhDP,
            idWrapPdf: "pdfWrap",
            idWrapLoader: "wrap_modalBATong"
        });
    });

    $("#hsba-phieubangiaodd-tab").click(function(){
        showPDFBenhAnTongHop({
            key: "BANGIAODIEUDUONG",
            dsPhieu: thongtinhsba.thongtinbn.linkBanGiaoDD,
            idWrapPdf: "pdfWrap",
            idWrapLoader: "wrap_modalBATong"
        });
    });

    $("#hsba-phieucsc1-tab").click(function(){
        showPDFBenhAnTongHop({
            key: "CHAMSOCCAP1",
            dsPhieu: thongtinhsba.thongtinbn.linkPCS1,
            idWrapPdf: "pdfWrap",
            idWrapLoader: "wrap_modalBATong"
        });
    });

    $("#hsba-phieucsc2-tab").click(function(){
        showPDFBenhAnTongHop({
            key: "CHAMSOCCAP2",
            dsPhieu: thongtinhsba.thongtinbn.linkPCS2,
            idWrapPdf: "pdfWrap",
            idWrapLoader: "wrap_modalBATong"
        });
    });

    $("#hsba-phieukiemtiemchungtress-tab").click(function(){
        showPDFBenhAnTongHop({
            key: "KIEMTIEMCHUNGTRESS",
            dsPhieu: thongtinhsba.thongtinbn.linkKiemTiemChungTreSS,
            idWrapPdf: "pdfWrap",
            idWrapLoader: "wrap_modalBATong"
        });
    });

    $("#hsba-phieukehoachchamsoc-tab").click(function(){
        showPDFBenhAnTongHop({
            key: "KEHOACHCHAMSOC",
            dsPhieu: thongtinhsba.thongtinbn.linkKeHoachChamSoc,
            idWrapPdf: "pdfWrap",
            idWrapLoader: "wrap_modalBATong"
        });
    });

    $("#hsba-phieucsc1khac-tab").click(function(){
        showPDFBenhAnTongHop({
            key: "CHAMSOCCAP1KHAC",
            dsPhieu: thongtinhsba.thongtinbn.linkCSC1Khac,
            idWrapPdf: "pdfWrap",
            idWrapLoader: "wrap_modalBATong"
        });
    });

    $("#hsba-bangkiemantoan-tab").click(function(){
        showPDFBenhAnTongHop({
            key: "BANGKIEMANTOAN",
            dsPhieu: thongtinhsba.thongtinbn.linkBangKiemAnToan,
            idWrapPdf: "pdfWrap",
            idWrapLoader: "wrap_modalBATong"
        });
    });

    $("#hsba-giaiphaubenhsinhthiet-tab").click(function(){
        $("#pdfWrap").html("");
        var dsLinkDownload = [...thongtinhsba.thongtinbn.linkGiaiPhauBenhSinhThiet, ...thongtinhsba.thongtinbn.linkKetQuaGiaiPhauBenhSinhThiet];
        showLoaderIntoWrapId("wrap_modalBATong")
        loadAndCombinePDFs(dsLinkDownload).then(data => {
            $("#pdfWrap").html('<iframe id="loadIframePreview" src="" width="100%" height="100%" style="overflow: auto;"></iframe>')
            $("#pdfWrap #loadIframePreview").prop('src', data);
            hideLoaderIntoWrapId("wrap_modalBATong")
        }).catch(error => {
            notifiToClient("Red", "Có lỗi xảy ra:", error);
            hideLoaderIntoWrapId("wrap_modalBATong")
        });
    });

    $("#hsba-bangkiemtruoc-tab").click(function(){
        showPDFBenhAnTongHop({
            key: "BANGKIEMTRUOC",
            dsPhieu: thongtinhsba.thongtinbn.linkKiemTruocPhauThuat,
            idWrapPdf: "pdfWrap",
            idWrapLoader: "wrap_modalBATong"
        });
    });

    $("#hsba-bangkiemgac-tab").click(function(){
        showPDFBenhAnTongHop({
            key: "BANGKIEMGAC",
            dsPhieu: thongtinhsba.thongtinbn.linkBangKiemGac,
            idWrapPdf: "pdfWrap",
            idWrapLoader: "wrap_modalBATong"
        });
    });

    $("#hsba-noiquybenhvien-tab").click(function(){
        showPDFBenhAnTongHop({
            key: "NOIQUYBENHVIEN",
            dsPhieu: thongtinhsba.thongtinbn.linkNoiQuyBenhVien,
            idWrapPdf: "pdfWrap",
            idWrapLoader: "wrap_modalBATong"
        });
    });

    $("#hsba-loettide-tab").click(function(){
        showPDFBenhAnTongHop({
            key: "PHIEULOETTIDE",
            dsPhieu: thongtinhsba.thongtinbn.linkLoetTiDe,
            idWrapPdf: "pdfWrap",
            idWrapLoader: "wrap_modalBATong"
        });
    });

    $("#hsba-khamtienme-tab").click(function(){
        showPDFBenhAnTongHop({
            key: "KHAMTIENME",
            dsPhieu: thongtinhsba.thongtinbn.linkKhamTienMe,
            idWrapPdf: "pdfWrap",
            idWrapLoader: "wrap_modalBATong"
        });
    });

    $("#hsba-gaymehoisuc-tab").click(function(){
        xemGayMeHoiSuc(thongtinhsba.thongtinbn.linkGayMeHoiSuc[0].chuoi, true)
        showPDFBenhAnTongHop({
            key: "GAYMEHOISUC",
            dsPhieu: thongtinhsba.thongtinbn.linkGayMeHoiSuc,
            idWrapPdf: "pdfWrap",
            idWrapLoader: "wrap_modalBATong"
        });
    });

    $("#hsba-tt50-tab").click(function() {
        showPDFBenhAnTongHop({
            key: "TT50",
            dsPhieu: thongtinhsba.thongtinbn.linkThongTu50,
            idWrapPdf: "pdfWrap",
            idWrapLoader: "wrap_modalBATong"
        });
    });

    $("#hsba-phieutruyendich-tab").click(function() {
        showPDFBenhAnTongHop({
            key: "PHIEUTRUYENDICH",
            dsPhieu: thongtinhsba.thongtinbn.linkTruyenDich,
            idWrapPdf: "pdfWrap",
            idWrapLoader: "wrap_modalBATong"
        });
    });

    $("#hsba-phieuglasgow-tab").click(function() {
        showPDFBenhAnTongHop({
            key: "PHIEUGLASGOW",
            dsPhieu: thongtinhsba.thongtinbn.linkPhieuGlasgow,
            idWrapPdf: "pdfWrap",
            idWrapLoader: "wrap_modalBATong"
        });
    });

    $("#hsba-tomtatdieutri-tab").click(function() {
        showPDFBenhAnTongHop({
            key: "TOMTATDIEUTRI",
            dsPhieu: thongtinhsba.thongtinbn.linkTomTatDieuTri,
            idWrapPdf: "pdfWrap",
            idWrapLoader: "wrap_modalBATong"
        });
    });

    $("#hsba-bienbankiemdiemtuvong-tab").click(function() {
        showPDFBenhAnTongHop({
            key: "BIENBANKIEMDIEMTUVONG",
            dsPhieu: thongtinhsba.thongtinbn.linkBienBanKiemDiemTuVong,
            idWrapPdf: "pdfWrap",
            idWrapLoader: "wrap_modalBATong"
        });
    });

    $("#hsba-bienbankiemthaotuvong-tab").click(function() {
        showPDFBenhAnTongHop({
            key: "BIENBANKIEMTHAOTUVONG",
            dsPhieu: thongtinhsba.thongtinbn.linkBienBanKiemThaoTuVong,
            idWrapPdf: "pdfWrap",
            idWrapLoader: "wrap_modalBATong"
        });
    });

    $("#hsba-chandoannguyennhantv-tab").click(function(){
        showPDFBenhAnTongHop({
            key: "CHANDOANNGUYENNHANTUVONG",
            dsPhieu: thongtinhsba.thongtinbn.linkChanDoanNguyenNhanTV,
            idWrapPdf: "pdfWrap",
            idWrapLoader: "wrap_modalBATong"
        });
    });

    $("#hsba-phieuxuatvien-tab").click(function() {
        showPDFBenhAnTongHop({
            key: "GIAYRAVIEN",
            dsPhieu: thongtinhsba.thongtinbn.linkGiayRaVien,
            idWrapPdf: "pdfWrap",
            idWrapLoader: "wrap_modalBATong"
        });
    });

    $("#hsba-phieuchuyentuyen-tab").click(function() {
        showPDFBenhAnTongHop({
            key: "PHIEUCHUYENTUYEN",
            dsPhieu: thongtinhsba.thongtinbn.linkPhieuChuyenTuyen,
            idWrapPdf: "pdfWrap",
            idWrapLoader: "wrap_modalBATong"
        });
    });

    $("#hsba-giaybaotu-tab").click(function() {
        showPDFBenhAnTongHop({
            key: "GIAYBAOTU",
            dsPhieu: thongtinhsba.thongtinbn.linkGiayBaoTu,
            idWrapPdf: "pdfWrap",
            idWrapLoader: "wrap_modalBATong"
        });
    });

    $("#hsba-phieuhenkhamlai-tab").click(function() {
        showPDFBenhAnTongHop({
            key: "PHIEUHENKHAMLAI",
            dsPhieu: thongtinhsba.thongtinbn.linkHenKhamLai,
            idWrapPdf: "pdfWrap",
            idWrapLoader: "wrap_modalBATong"
        });
    });

    $("#hsba-phieukhambenhvaovien-tab").click(function() {
        showPDFBenhAnTongHop({
            key: "PHIEUKHAMBENHVAOVIEN",
            dsPhieu: thongtinhsba.thongtinbn.linkKhamBenhVaoVien,
            idWrapPdf: "pdfWrap",
            idWrapLoader: "wrap_modalBATong"
        });
    });

    $("#hsba-tiensudiung-tab").click(function() {
        showPDFBenhAnTongHop({
            key: "TIENSUDIUNG",
            dsPhieu: thongtinhsba.thongtinbn.linkTienSuDiUng,
            idWrapPdf: "pdfWrap",
            idWrapLoader: "wrap_modalBATong"
        });
    });

    $("#hsba-phieuchuyenda-tab").click(function() {
        showPDFBenhAnTongHop({
            key: "PHIEUCHUYENDA",
            dsPhieu: thongtinhsba.thongtinbn.linkPhieuChuyenDa,
            idWrapPdf: "pdfWrap",
            idWrapLoader: "wrap_modalBATong"
        });
    });

    $("#hsba-phieubilan-tab").click(function() {
        showPDFBenhAnTongHop({
            key: "THEODOIBILAN",
            dsPhieu: thongtinhsba.thongtinbn.linkTheoDoiBilan,
            idWrapPdf: "pdfWrap",
            idWrapLoader: "wrap_modalBATong"
        });
    });

    $("#hsba-tthc-tong-tab").click(function () {
        showLoaderIntoWrapId("wrap_modalBATong");
        $(".hsba-thongtin_tong").html("");
        var dataBN  = thongtinhsba.thongtinbn;
        $("#hsba_hoten_tong").html(dataBN.TEN_BENH_NHAN);
        $("#hsba_sobenhan_tong").html(dataBN.SOBENHAN);
        $("#hsba_mayte_tong").html(dataBN.MABENHNHAN);
        $("#hsba_gioitinh_tong").html(dataBN.GIOI_TINH_NUM == 1? "Nam": "Nữ");
        $("#hsba_namsinh_tong").html(dataBN.NGAY_SINH);
        $("#hsba_dantoc_tong").html(dataBN.TEN_DANTOC);
        $("#hsba_diachi_tong").html(dataBN.DIA_CHI);
        $("#hsba_sdt_tong").html(dataBN.SO_DIEN_THOAI);
        $("#hsba_cccd_tong").html(dataBN.CMT_BENHNHAN);
        $("#hsba_doituong_tong").html(dataBN.DOITUONG_DIEUTRI == 1? "BHYT": "Thu phí");
        $("#hsba_sothebhyt_tong").html(dataBN.SOBAOHIEMYTE);
        $("#hsba_muchuong_tong").html(dataBN.TYLEBAOHIEM);
        $("#hsba_nguoilienhe_tong").html(dataBN.NGUOI_LIEN_HE);
        $("#hsba_cccd_tong").html(dataBN.CMT_BENHNHAN);
        if(dataBN.SOBAOHIEMYTE != null && dataBN.SOBAOHIEMYTE != "") {
            $("#hsba_hanthe_tong").html( dataBN.NGAYBATDAU_THEBHYT + " - " + dataBN.NGAYHETHAN_THEBHYT);
            $("#hsba_noidangky_tong").html(dataBN.NOIDANGKYBANDAU + " - " + dataBN.TENNOIDANGKYBANDAU);
        }
        $("#hsba_doituongbhyt_tong").html(dataBN.TEN_DOI_TUONG_BHYT);
        $("#hsba_trangthainhapvien_tong").html("<span style='color: green'>" +
            (dataBN.CAPCUU == 1? "Cấp cứu":  dataBN.THONGTUYEN_BHXH_XML4210 == 1? "Thông tuyến" : dataBN.DUNGTUYEN == 1? "Đúng tuyến": "Trái tuyến") + "</span>" )
        $("#hsba_makhuvuc_tong").html(dataBN.MA_KHUVUC);
        $("#hsba_lydonhapvien_tong").html(dataBN.LYDO_TRANGTHAI_BN_NHAPVIEN);
        $("#hsba_icdnhapvien_tong").html(dataBN.CHAN_DOAN);
        $("#hsba_ngaynhapvien_tong").html(dataBN.NGAYNHAPVIEN);
        $("#hsba_khoanhapvien_tong").html(dataBN.TENKHOA_NHAPVIENVAOKHOA);
        $("#hsba_ngaymiencungchitra_tong").html(dataBN.NGAY_MIEN_CUNG_CT);
        $("#hsba_ngaymiencungchitra_kgt_tong").html(dataBN.NT_THOIDIEM_MCCT_KH_GIAY);
        $("#hsba-huonggiaiquyet_tong").html(dataBN.HUONGGIAIQUYET);
        $("#hsba-icd-ravien_tong").html(dataBN.CHANDOAN_RAVIEN);
        $("#hsba-ngayxuatvien_tong").html(dataBN.THOIGIAN_RAVIEN);
        $("#hsba-khoaxuatvien_tong").html(dataBN.PHONGBAN_RAVIEN);
        $("#hsba-ketquadieutri_tong").html(dataBN.KETQUA_DIEUTRI);
        $("#hsba-tinhtrangravien_tong").html(dataBN.TINHTRANG_RAVIEN);
        $("#hsba-soluutru_tong").html(dataBN.SOLUUTRU_XUATVIEN);
        $("#hsba_vba_trang1").click();
        hideLoaderIntoWrapId("wrap_modalBATong");
    });

    $("#hsba-gui-emr-ds").click(function(){
        $("#modalFormHienThiFileKySo").modal('show')
        var arrayFile = [];
        var objThongTinBenhNhan = {
            "dvtt": singletonObject.dvtt,
            "bant": thongtinhsba.thongtinbn.BANT,
            "maBenhNhan": thongtinhsba.thongtinbn.MABENHNHAN,
            "soVaoVien": thongtinhsba.thongtinbn.SOVAOVIEN,
        }
        var dsFilekyso = [...thongtinhsba.thongtinbn.dsLinkDownload, ...thongtinhsba.thongtinbn.linkTDTV2];
        for (var i in dsFilekyso) {
            if (dsFilekyso[i].hasOwnProperty('keyEMR')){
                arrayFile.push({
                    "file": dsFilekyso[i].url[0].replace("data:application/pdf;base64,", ""),
                    "sendEmr": true,
                    ...dsFilekyso[i],
                    ...objThongTinBenhNhan
                });
            }
        }
        if (arrayFile.length == 0){
            return notifiToClient("Red", "Không tìm thấy file đã ký số để gửi EMR");
        }

        async function processData(arrayFile) {
            const seenIds = new Set();
            const filteredArray = arrayFile.filter(item => {
                if (seenIds.has(item.idPhieu)) return false;
                seenIds.add(item.idPhieu);
                return true;
            });

            return await Promise.all(filteredArray.map(async (item) => {
                const url = "cmu_getlist?url=" + convertArray([item.idPhieu, singletonObject.dvtt, "CMU_GET_STATUS_EMR"]);
                const data = await $.get(url);

                if (data && data.length > 0) {
                    const dataEmr = data[0];
                    return {
                        ...item,
                        group: groupTitlesEmr[item.keyEMR] || "Khác",
                        nguoiky: dataEmr.NGUOI_KY,
                        ngayky: dataEmr.NGAY_KY,
                        trangthai: dataEmr.TT_EMR,
                    };
                }
                return { ...item, group: "Khác", nguoiky: null, ngayky: null, trangthai: "Không có dữ liệu" };
            }));
        }

        (async () => {
            processedDataEmr = await processData(arrayFile);
            instanceGridDSKySoEMR();
        })();
    });

    $("#hsba-gui-emr").click(function(){
        var arrayFile = [];
        var objThongTinBenhNhan = {
            "dvtt": singletonObject.dvtt,
            "bant": thongtinhsba.thongtinbn.BANT,
            "maBenhNhan": thongtinhsba.thongtinbn.MABENHNHAN,
            "soVaoVien": thongtinhsba.thongtinbn.SOVAOVIEN,
        }
        var dsFilekyso = [...thongtinhsba.thongtinbn.dsLinkDownload, ...thongtinhsba.thongtinbn.linkTDTV2];
        for (var i in dsFilekyso) {
            if (dsFilekyso[i].hasOwnProperty('keyEMR')){
                arrayFile.push({
                    "file": dsFilekyso[i].url[0].replace("data:application/pdf;base64,", ""),
                    "sendEmr": true,
                    ...dsFilekyso[i],
                    ...objThongTinBenhNhan
                });
            }
        }
        if (arrayFile.length == 0){
            return notifiToClient("Red", "Không tìm thấy file đã ký số để gửi EMR");
        }
        console.log(arrayFile)

        async function processArray(arrayFile) {
            showSelfLoading("hsba-gui-emr");
            const promises = arrayFile.map(function (item) {
                // const item = arrayFile[i];
                return (async function(item){
                    const url = "upload-file-hsba-cmu";
                    const data = JSON.stringify(item);

                    try {
                        const response = await $.ajax({
                            type: "POST",
                            contentType: "application/json",
                            dataType: "json",
                            url: url,
                            data: data
                        });

                        if (response.error == '-1') {
                            const url = [item.idPhieu, singletonObject.dvtt, "2", "CMU_STATUS_EMR_UPD"]
                            await $.post("cmu_post", {url: url.join('```')});
                            notifiToClient("Red", "Có lỗi xảy ra: " + response.message)
                        } else {
                            const successUrl = [item.idPhieu, singletonObject.dvtt, "1", "CMU_STATUS_EMR_UPD"]
                            await $.post("cmu_post", {url: successUrl.join('```')});
                        }

                        const dataReturn = response.message.result;

                    } catch (error) {
                        const failUrl = [item.idPhieu, singletonObject.dvtt, "2", "CMU_STATUS_EMR_UPD"]
                        await $.post("cmu_post", {url: failUrl.join('```')});
                        notifiToClient("Red", "Có lỗi xảy ra: " + jqXHR)
                    }
                })(item);
            })
            await Promise.all(promises)
            hideSelfLoading("hsba-gui-emr");
            $("#hsba-gui-emr-ds").click();
        }
        processArray(arrayFile);
    });

    $("#hsba-kyso-truongkhoa").click(function(){
        var dataTrang1 = thongtinhsba.thongtinbn.VOBENHAN[0].TRANG1;
        var dataLinkTrang1 = thongtinhsba.thongtinbn.linkVBA[0];
        if(singletonObject.userId != dataTrang1.INFO.MATRUONGKHOA) {
            return notifiToClient("Red", "Bạn không phải người ký bệnh án này: " + dataTrang1.INFO.TRUONGKHOA);
        }

        kySoChung({
            dvtt: singletonObject.dvtt,
            userId: singletonObject.userId,
            url: dataLinkTrang1.url[0],
            loaiGiay: "PHIEU_NOITRU_VBATRANG1",
            maBenhNhan: String(thongtinhsba.thongtinbn.MABENHNHAN),
            soBenhAn: thongtinhsba.thongtinbn.STT_BENHAN,
            soPhieuDichVu: String(thongtinhsba.thongtinbn.VOBENHAN[0].ID),
            nghiepVu: thongtinhsba.thongtinbn.VOBENHAN[0].ID_VBA,
            soVaoVien: thongtinhsba.thongtinbn.SOVAOVIEN,
            soVaoVienDT: thongtinhsba.thongtinbn.SOVAOVIEN_DT,
            keyword: "TRƯỞNG KHOA",
            sttDotDieuTri: thongtinhsba.thongtinbn.STT_DOTDIEUTRI,
            fileName: "TRANG 1 " + thongtinhsba.thongtinbn.VOBENHAN[0].TRANG1.VBA_INF.TITLE_REPORT.toUpperCase() + " - " + thongtinhsba.thongtinbn.TEN_BENH_NHAN.toUpperCase(),
        }, function(dataKySo) {
            getMenuBenhAnTong([thongtinhsba.thongtinbn],
                function(){
                    $("#hsba-ttba-tab").click();
                    addTextTitleModal("titleXemBenhAnTong");
                    hideLoaderIntoWrapId("wrap_modalBATong");
                },
                function(error) {
                    notifiToClient("Red", "Lỗi load danh sách menu bệnh án tổng hợp!");
                });
        });
    });

    $("#hsba-huykyso-truongkhoa").click(function() {
        confirmToClient("Bạn có chắc sẽ hủy ký số phiếu này?", function() {
            huykysoFilesign769("PHIEU_NOITRU_VBATRANG1", String(thongtinhsba.thongtinbn.VOBENHAN[0].ID), singletonObject.userId, singletonObject.dvtt,
                thongtinhsba.thongtinbn.SOVAOVIEN, thongtinhsba.thongtinbn.SOVAOVIEN_DT, -1, function(data) {
                    getMenuBenhAnTong(thongtinhsba.thongtinbn.dsDotDieuTri,
                        function(){
                            $("#hsba-ttba-tab").click();
                            addTextTitleModal("titleXemBenhAnTong");
                            hideLoaderIntoWrapId("wrap_modalBATong");
                        },
                        function(error) {
                            notifiToClient("Red", "Lỗi load danh sách menu bệnh án tổng hợp!");
                        });
                })
        }, function () {

        })
    });

    $("#hsba-kyso-nguoigiao-confirm").click(function(){
        var dataLinkTrang3 = thongtinhsba.thongtinbn.linkVBA[2];
        if(!$("#formthongtinbangiaohs").valid()){
            return false;
        }
        var idButton = this.id;
        var voBenhAnId = thongtinhsba.thongtinbn.VOBENHAN[0].ID;
        showSelfLoading(idButton);
        kySoChung({
            dvtt: singletonObject.dvtt,
            userId: singletonObject.userId,
            url: dataLinkTrang3.url[0],
            loaiGiay: "PHIEU_NOITRU_VBATRANG3_NGUOIGIAO",
            maBenhNhan: String(thongtinhsba.thongtinbn.MABENHNHAN),
            soBenhAn: thongtinhsba.thongtinbn.STT_BENHAN,
            soPhieuDichVu: String(thongtinhsba.thongtinbn.VOBENHAN[0].ID),
            nghiepVu: thongtinhsba.thongtinbn.VOBENHAN[0].ID_VBA,
            soVaoVien: thongtinhsba.thongtinbn.SOVAOVIEN,
            soVaoVienDT: thongtinhsba.thongtinbn.SOVAOVIEN_DT,
            keyword: "Người giao hồ sơ:",
            sttDotDieuTri: thongtinhsba.thongtinbn.STT_DOTDIEUTRI,
            fileName: "KÝ SỐ NGƯỜI GIAO HỒ SƠ " + thongtinhsba.thongtinbn.VOBENHAN[0].TRANG1.VBA_INF.TITLE_REPORT.toUpperCase() + " - " + thongtinhsba.thongtinbn.TEN_BENH_NHAN.toUpperCase(),
        }, function(kySoRequest, kySoResponse) {
            if (kySoResponse.SUCCESS > 0) {
                updateKySoGiaoNhan(voBenhAnId, '1', '-1');
            }

            getMenuBenhAnTong(thongtinhsba.thongtinbn.dsDotDieuTri,
                function(){
                    $("#hsba-ttba-tab").click();
                    addTextTitleModal("titleXemBenhAnTong");
                    hideLoaderIntoWrapId("wrap_modalBATong");
                },
                function(error) {
                    notifiToClient("Red", "Lỗi load danh sách menu bệnh án tổng hợp!");
                });
            hideSelfLoading(idButton);
        });
    });

    $("#hsba-kyso-nguoigiao").click(function(){
        $("#modalFormthongtinbangiaohs").modal("show")
        addTextTitleModal("titleModalFormthongtinbangiaohs", "Ký số người giao hồ sơ");
        $("#formthongtinbangiaohs .clear-text").val("");
        hideSelfLoading("hsba-kyso-nguoigiao-confirm")
    });

    $("#hsba-huykyso-nguoigiao").click(function() {
        confirmToClient("Bạn có chắc sẽ hủy ký số phiếu này?", function() {
            huykysoFilesign769("PHIEU_NOITRU_VBATRANG3_NGUOIGIAO", String(thongtinhsba.thongtinbn.VOBENHAN[0].ID), singletonObject.userId, singletonObject.dvtt,
                thongtinhsba.thongtinbn.SOVAOVIEN, thongtinhsba.thongtinbn.SOVAOVIEN_DT, -1, function(data) {
                    if (_.get(data, 'SUCCESS') == 1) {
                        updateKySoGiaoNhan(thongtinhsba.thongtinbn.VOBENHAN[0].ID, '0', '-1');
                    }

                    getMenuBenhAnTong(thongtinhsba.thongtinbn.dsDotDieuTri,
                        function(){
                            $("#hsba-ttba-tab").click();
                            addTextTitleModal("titleXemBenhAnTong");
                            hideLoaderIntoWrapId("wrap_modalBATong");
                        },
                        function(error) {
                            notifiToClient("Red", "Lỗi load danh sách menu bệnh án tổng hợp!");
                        });
                })
        }, function () {

        })
    });

    $("#hsba-kyso-nguoinhan").click(function(){
        var dataLinkTrang3 = thongtinhsba.thongtinbn.linkVBA[2];
        var voBenhAnId = thongtinhsba.thongtinbn.VOBENHAN[0].ID;
        kySoChung({
            dvtt: singletonObject.dvtt,
            userId: singletonObject.userId,
            url: dataLinkTrang3.url[0],
            loaiGiay: "PHIEU_NOITRU_VBATRANG3_NGUOINHAN",
            maBenhNhan: String(thongtinhsba.thongtinbn.MABENHNHAN),
            soBenhAn: thongtinhsba.thongtinbn.STT_BENHAN,
            soPhieuDichVu: String(thongtinhsba.thongtinbn.VOBENHAN[0].ID),
            nghiepVu: thongtinhsba.thongtinbn.VOBENHAN[0].ID_VBA,
            soVaoVien: thongtinhsba.thongtinbn.SOVAOVIEN,
            soVaoVienDT: thongtinhsba.thongtinbn.SOVAOVIEN_DT,
            keyword: "Người nhận hồ sơ:",
            sttDotDieuTri: thongtinhsba.thongtinbn.STT_DOTDIEUTRI,
            fileName: "KÝ SỐ NGƯỜI NHẬN HỒ SƠ " + thongtinhsba.thongtinbn.VOBENHAN[0].TRANG1.VBA_INF.TITLE_REPORT.toUpperCase() + " - " + thongtinhsba.thongtinbn.TEN_BENH_NHAN.toUpperCase(),
        }, function(dataKySo) {
            updateKySoGiaoNhan(voBenhAnId, '-1', '1');

            getMenuBenhAnTong(thongtinhsba.thongtinbn.dsDotDieuTri,
                function(){
                    $("#hsba-ttba-tab").click();
                    addTextTitleModal("titleXemBenhAnTong");
                    hideLoaderIntoWrapId("wrap_modalBATong");
                },
                function(error) {
                    notifiToClient("Red", "Lỗi load danh sách menu bệnh án tổng hợp!");
                });
        });
    });

    $("#hsba-huykyso-nguoinhan").click(function() {
        confirmToClient("Bạn có chắc sẽ hủy ký số phiếu này?", function() {
            huykysoFilesign769("PHIEU_NOITRU_VBATRANG3_NGUOINHAN", String(thongtinhsba.thongtinbn.VOBENHAN[0].ID), singletonObject.userId, singletonObject.dvtt,
                thongtinhsba.thongtinbn.SOVAOVIEN, thongtinhsba.thongtinbn.SOVAOVIEN_DT, -1, function(data) {
                     if (_.get(data, 'SUCCESS') == 1) {
                         updateKySoGiaoNhan(thongtinhsba.thongtinbn.VOBENHAN[0].ID, '-1', '0');
                     }

                    getMenuBenhAnTong(thongtinhsba.thongtinbn.dsDotDieuTri,
                        function(){
                            $("#hsba-ttba-tab").click();
                            addTextTitleModal("titleXemBenhAnTong");
                            hideLoaderIntoWrapId("wrap_modalBATong");
                        },
                        function(error) {
                            notifiToClient("Red", "Lỗi load danh sách menu bệnh án tổng hợp!");
                        });
                })
        }, function () {

        })
    });

    $("#formthongtinbangiaohs").validate({})

    // FUNCTION
    function showPDFBenhAnTongHop(dataPDF) {
        showLoaderIntoWrapId(dataPDF.idWrapLoader)
        $("#" + dataPDF.idWrapPdf).html("");
        var ds = dataPDF.dsPhieu.filter(function(item) {
            return item.key === dataPDF.key;
        });
        loadAndCombinePDFs(ds).then(data => {
            $("#" + dataPDF.idWrapPdf).html('<iframe id="loadIframePreview" src="" width="100%" height="100%" style="overflow: auto;"></iframe>')
            $("#" + dataPDF.idWrapPdf + " #loadIframePreview").prop('src', data);
            hideLoaderIntoWrapId(dataPDF.idWrapLoader)
        }).catch(error => {
            notifiToClient("Red", "Có lỗi xảy ra:", error);
            hideLoaderIntoWrapId(dataPDF.idWrapLoader)
        });
    }

    function updateKySoGiaoNhan(voBenhAnId, daKyGiao, daKyNhan) {
        $.ajax({
            url: 'cmu_post',
            data: { url: [voBenhAnId, daKyGiao, daKyNhan, 'CMU_UPDATE_KY_SO_GIAO_NHAN'].join('```') },
            type: "POST",
            async: false
        })
    }

    function instanceGridDSKySoEMR() {
        if (!$("#list_dsfilekyso")[0].grid) {
            $("#list_dsfilekyso").jqGrid({
                datatype: "local",
                data: processedDataEmr,
                colModel: [
                    { label: "Nhóm", name: "group", hidden: true },
                    { label: "Số phiếu", name: "idPhieu", width: 200, align: "center" },
                    { label: "Ký số", name: "kySo", width: 100, align: "center",formatter: function (cellValue, options, rowData) {
                            if (rowData.keyEMR){
                                return '<span class="cellWithoutBackground" style="font-weight:bold ;color: green">Đã ký</span>';
                            } else {
                                return '<span class="cellWithoutBackground" style="font-weight:bold ;color: red">Chưa ký</span>';
                            }
                        }
                    },
                    { label: "Key", name: "keyEMR", width: 400, align: "center", hidden: true },
                    { label: "Người ký", name: "nguoiky", width: 300, align: "center" },
                    { label: "Ngày ký", name: "ngayky", width: 300, align: "center" },
                    { label: "URL", name: "url", width: 200, align: "center", hidden: true },
                    { label: "Loại", name: "loai", width: 200, align: "center",formatter: function (cellValue, options, rowData) {
                            if (rowData.url[0].includes("application/pdf")){
                                return '<span class="cellWithoutBackground" style="font-weight:bold ;color: orange">PDF</span>';
                            } else {
                                return '<span class="cellWithoutBackground" style="font-weight:bold">Khác</span>';
                            }
                        }
                    },
                    { label: "Trạng thái", name: "trangthai", width: 200, align: "center",formatter: function (cellValue, options, rowData) {
                            if (cellValue == "1"){
                                return '<span class="cellWithoutBackground" style="font-weight:bold ;color: green">Thành công</span>';
                            } else if (cellValue == "2"){
                                return '<span class="cellWithoutBackground" style="font-weight:bold ;color: red">Thất bại</span>';
                            } else {
                                return '<span class="cellWithoutBackground" style="font-weight:bold">Chưa gửi</span>';
                            }
                        }
                    },
                ],
                viewrecords: true, // Hiển thị số bản ghi
                height: 400,
                width: "100%",
                pager: "#emrGridPager",
                rowNum: 10000, // Số hàng mỗi trang
                grouping: true,
                groupingView: {
                    groupField: ["group"], // Nhóm theo cột "group"
                    groupColumnShow: [false], // Ẩn cột "group" khỏi bảng chính
                    groupText: ["<b>{0}</b> (<span>{1}</span>)"], // Hiển thị tên nhóm và số lượng bản ghi
                    groupCollapse: true, // Mặc định các nhóm bị thu gọn
                    groupOrder: ["asc"], // Sắp xếp tăng dần
                },
                onRightClickRow: function (id1) {
                    if (id1) {
                        var ret = getThongtinRowSelected("list_dsfilekyso");
                        var items = {
                            "xem": {name: '<p><i class="fa fa-eye text-primary" aria-hidden="true"></i> Xem</p>'},
                        }
                        $.contextMenu('destroy', '#list_dsfilekyso tr');
                        $.contextMenu({
                            selector: '#list_dsfilekyso tr',
                            callback: function (key, options) {
                                if (key == "xem") {
                                    $("#modalHienThiPdf").modal("show");
                                    $('#modalIframePdf').attr('src', ret.url);
                                }
                            },
                            items: items
                        });
                    }
                },
                footerrow: true,
                gridComplete: function () {
                    var sl = $("#list_dsfilekyso").jqGrid("getGridParam", "records");
                    $("#list_dsfilekyso").jqGrid("footerData", "set", {
                        idPhieu: "Tổng số: " + sl + " phiếu",
                    });
                },
            });
        } else {
            $("#list_dsfilekyso")
                .jqGrid("clearGridData")
                .jqGrid("setGridParam", { data: processedDataEmr })
                .trigger("reloadGrid");
        }
    }
});