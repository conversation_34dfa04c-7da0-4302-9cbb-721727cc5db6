create or replace PROCEDURE                   CMU_PHIEU_CBI_TPHAU_PROCED(
    p_dvtt              IN     VARCHAR2,
    p_id               IN     NUMBER,
    p_ma_benh_nhan      IN     NUMBER,
    cur OUT SYS_REFCURSOR
) AS
BEGIN
open cur for
select
    phieu.ID,
    phieu.DVTT,
    phieu.SOVAOVIEN,
    phieu.MA_BENH_NHAN,
    phieu.DOI_TUONG,
--     TO_CHAR(phieu.THOI_GIAN_MO, 'hh24:mi:ss dd/MM/yyyy') THOI_GIAN_MO,
    phieu.THOI_GIAN_MO,
    phieu.TOM_TAT_BENH_AN,
    phieu.CHAN_DOAN,
    phieu.CACH_MO,
    phieu.BAC_SI,
    phieu.PHU_MO,
    phieu.TONG_TRANG,
    phieu.HA,
    phieu.MACH,
    phieu.CAN_NANG,
    phieu.TINH_TRANG_TIM,
    phieu.TINH_TRANG_PHOI,
    phieu.HC,
    phieu.MC,
    phieu.HBSAG,
    phieu.MACLAGAN,
    phieu.BC,
    phieu.BUN,
    phieu.DUONG_HUYET,
    phieu.SGOT,
    phieu.DTHC,
    phieu.CREATININE,
    phieu.PROTID,
    phieu.SGPT,
    phieu.MD,
    phieu.HIV,
    phieu.GROS,
    phieu.KHAC,
    phieu.NUOC_TIEU,
    phieu.DAM,
    phieu.KHAC_2,
    phieu.BENH_SAN,
    phieu.MAU_DU_TRU,
    phieu.MAU,
    phieu.NHOM,
    phieu.RH,
    phieu.X_QUANG_PHOI,
    phieu.X_QUANG_TIEU_HOA,
    phieu.X_QUANG_XUONG,
    phieu.X_QUANG_BO_NIEU,
    phieu.SOI_BANG_QUANG,
    phieu.SOI_TRUC_TRANG,
    phieu.CAC_DIEN_DO,
    phieu.THE_NAM,
    phieu.TREO,
    phieu.NOI_RUA,
    phieu.BAO_TAY,
    phieu.BAO_CHAN,
    phieu.BAN_XUONG,
    phieu.DOT,
    phieu.HUT,
    phieu.BOT,
    phieu.NOI_THAT_MACH,
    phieu.ESMARCH,
    phieu.DUNG_CU,
    phieu.DUNG_CU_DAC_BIET,
    phieu.X_QUANG_PHONG_MO,
    phieu.BAC_SI_DIEU_TRI,
    phieu.DE_NGHI,
    bn.TEN_BENH_NHAN,
    nv.TEN_NHANVIEN TEN_BAC_SI,
    nv1.TEN_NHANVIEN TEN_PHU_MO,
    nv2.TEN_NHANVIEN TEN_BAC_SI_DIEU_TRI,

    bn.GIOI_TINH,
    bn.TUOI,
--        nvnt.CAN_NANG,
    nt.TENKHOA_NHAPVIENVAOKHOA KHOA,
    TO_CHAR(nt.NGAY_NHAPVIEN, 'DD/MM/YYYY') NGAY_NHAPVIEN


FROM CMU_PHIEUCHUANBITIENPHAU phieu
         LEFT JOIN his_public_list.dm_benh_nhan bn ON phieu.MA_BENH_NHAN = bn.ma_benh_nhan
         LEFT JOIN HIS_FW.DM_NHANVIEN nv ON phieu.BAC_SI = nv.MA_NHANVIEN
         LEFT JOIN HIS_FW.DM_NHANVIEN nv1 ON phieu.PHU_MO = nv1.MA_NHANVIEN
         LEFT JOIN HIS_FW.DM_NHANVIEN nv2 ON phieu.BAC_SI_DIEU_TRI = nv2.MA_NHANVIEN
         LEFT JOIN his_manager.noitru_benhan nt ON phieu.MA_BENH_NHAN = nt.MABENHNHAN
--            LEFT JOIN his_manager.kb_phieunhapviennoitru nvnt ON phieu.MA_BENH_NHAN = nvnt.MA_BENH_NHAN

WHERE phieu.ID = p_id
order by phieu.NGAY_TAO_PHIEU;
END;