$(function (){
    var thongTinLuongGiaHDChucNangMoiNhat = {};
    var thongTinLuongGiaHDChucNangTruocChinhSua = {};
    var formLuongGiaHDChucNang;

    $("#ttchamsoc-phieukhac").click(function () {
        instanceGridLuongGiaHDChucNang();
        reloadDSLuongGiaHDChucNang();
        loadLuongGiaHDItem();
        showFormLuongGiaHDChucNang();
        if (singletonObject.dvtt === "96029" || singletonObject.dvtt === "96001"){
            $("#luonggiahdchucnang_mauchon").show()
        } else {
            $("#luonggiahdchucnang_mauchon").hide()
        }
    });
    $("#luonggiahdchucnang_lammoi").click(function () {
        reloadDSLuongGiaHDChucNang();
    });
    $("#luonggiahdchucnang_mauchon").click(function () {
        $("#modalLuongGiaHDItem").modal("show");
        instanceGridLuongGiaHDItem()
    });
    $("#luonggiahditem_them").click(function () {
        themLuongGiaHDItem()
    });

    $(".themluonggiahdchucnang").click(function () {
        $("#modalFormLuongGiaHDChucNang").modal("show");
        addTextTitleModal("titleFormLuongGiaHDChucNang", " Phiếu lượng giá hoạt động chức năng và sự tham gia");
        showFormLuongGiaHDChucNang();
        $("#luonggiahdchucnang_luu").attr("data-action", "THEM");
    });

    $("#luonggiahdchucnang_luu").click(function () {
        var btnAction = $('#luonggiahdchucnang_luu').attr("data-action");
        if (btnAction == "THEM"){
            themLuongGiaHDChucNang();
        } else {
            updateLuongGiaHDChucNang();
        }
    });
    $(document).on('click', '#luonggiahdcn_kyso_action', function() {
        kySoChung({
            dvtt: singletonObject.dvtt,
            userId: singletonObject.userId,
            url: $('#iframePreviewAndSign').attr('src'),
            loaiGiay: "PHIEU_NOITRU_LUONGGIAHDCN",
            maBenhNhan: thongtinhsba.thongtinbn.MA_BENH_NHAN,
            soBenhAn: thongtinhsba.thongtinbn.STT_BENHAN,
            soPhieuDichVu: thongTinLuongGiaHDChucNangTruocChinhSua.ID,
            soVaoVien: thongtinhsba.thongtinbn.SOVAOVIEN,
            soVaoVienDT: thongtinhsba.thongtinbn.SOVAOVIEN_DT,
            keyword: "và ghi rõ họ",
            fileName: "Phiếu lượng giá hoạt động chức năng và sự tham gia: " + thongtinhsba.thongtinbn.TEN_BENH_NHAN + " - Mã phiếu: " + thongTinLuongGiaHDChucNangTruocChinhSua.ID,
        }, function(dataKySo) {
            $("#modalPreviewAndSignPDF").modal("hide");
            reloadDSLuongGiaHDChucNang();
        });
    });

    $('#view_single_luonggiahdchucnang').click(function () {
        xemLuongGiaHDChucNang(thongTinLuongGiaHDChucNangMoiNhat)
    })

    $('#edit_single_luonggiahdchucnang').click(function () {
        showUpdateLuongGiaHDChucNang(thongTinLuongGiaHDChucNangMoiNhat)
    });

    $('#delete_single_luonggiahdchucnang').click(function () {
        deleteLuongGiaHDChucNang(thongTinLuongGiaHDChucNangMoiNhat)
    })

    function loadLuongGiaHDItem(){
        $.get("cmu_getlist?url="+convertArray([singletonObject.dvtt, 'VAN_DONG', "CMU_LUONGGIAHD_ITEM_SELECT"])).done(function(data){
            singletonObject.danhsachvandongluonggia = data.map(function(object) {
                return {
                    label: object.NOIDUNG,
                    value: object.NOIDUNG.toString(),
                }
            });
        });
        $.get("cmu_getlist?url="+convertArray([singletonObject.dvtt, 'SINH_HOAT', "CMU_LUONGGIAHD_ITEM_SELECT"])).done(function(data){
            singletonObject.danhsachsinhhoatluonggia = data.map(function(object) {
                return {
                    label: object.NOIDUNG,
                    value: object.NOIDUNG.toString(),
                }
            });
        });
        $.get("cmu_getlist?url="+convertArray([singletonObject.dvtt, 'NHAN_THUC', "CMU_LUONGGIAHD_ITEM_SELECT"])).done(function(data){
            singletonObject.danhsachnhanthucluonggia = data.map(function(object) {
                return {
                    label: object.NOIDUNG,
                    value: object.NOIDUNG.toString(),
                }
            });
        });
        $.get("cmu_getlist?url="+convertArray([singletonObject.dvtt, 'CHUC_NANG_KHAC', "CMU_LUONGGIAHD_ITEM_SELECT"])).done(function(data){
            singletonObject.danhsachchucnangkhacluonggia = data.map(function(object) {
                return {
                    label: object.NOIDUNG,
                    value: object.NOIDUNG.toString(),
                }
            });
        });
        $.get("cmu_getlist?url="+convertArray([singletonObject.dvtt, 'THAM_GIA_HOAT_DONG', "CMU_LUONGGIAHD_ITEM_SELECT"])).done(function(data){
            singletonObject.danhsachthamgiahdluonggia = data.map(function(object) {
                return {
                    label: object.NOIDUNG,
                    value: object.NOIDUNG.toString(),
                }
            });
        });
        $.get("cmu_getlist?url="+convertArray([singletonObject.dvtt, 'MOI_TRUONG', "CMU_LUONGGIAHD_ITEM_SELECT"])).done(function(data){
            singletonObject.danhsachmoitruongluonggia = data.map(function(object) {
                return {
                    label: object.NOIDUNG,
                    value: object.NOIDUNG.toString(),
                }
            });
        });
        $.get("cmu_getlist?url="+convertArray([singletonObject.dvtt, 'CA_NHAN', "CMU_LUONGGIAHD_ITEM_SELECT"])).done(function(data){
            singletonObject.danhsachcanhanluonggia = data.map(function(object) {
                return {
                    label: object.NOIDUNG,
                    value: object.NOIDUNG.toString(),
                }
            });
        });
    }

    function showFormLuongGiaHDChucNang() {
        var optionsDanToc = singletonObject.danhsachdantoc.filter(function(object) {
            return object.MA_DANTOC != 0
        }).map(function(object) {
            return {
                label: object.MA_DANTOC + " - "+ object.TEN_DANTOC,
                value: object.MA_DANTOC
            }
        })
        var optionsNgheNghiep = singletonObject.danhsachnghenghiep.filter(function(object) {
            return object.MA_NGHE_NGHIEP != 0
        }).map(function(object) {
            return {
                label: object.MA_NGHE_NGHIEP + " - "+ object.TEN_NGHE_NGHIEP,
                value: object.MA_NGHE_NGHIEP
            }
        })
        var jsonForm = getJSONObjectForm([
            {
                "label": "Người nhà",
                "columns": [
                    {
                        "components": [
                            {
                                "label": "Ngày tạo phiếu",
                                "key": "NGAY_TAO_PHIEU",
                                "type": "datetime",
                                format: "dd/MM/yyyy",
                                enableTime: false,
                                customClass: "pr-2",
                                minDate: moment(thongtinhsba.thongtinbn.NGAY_VAO_VIEN, ['DD/MM/YYYY']).format("YYYY-MM-DD"),
                                maxDate: moment().format("YYYY-MM-DD"),
                                "validate": {
                                    "required": true
                                },
                            },
                        ],
                        "width": 6,
                    },
                ],
                "customClass": "ml-0 mr-0",
                "key": "THONGTINNGUOINHANHANVIEN",
                "type": "columns",
            },
            ...(singletonObject.dvtt === "96029" || singletonObject.dvtt === "96001" ? [
                {
                    "label": "Người nhà",
                    "columns": [
                        {
                            "components": [
                                {
                                    "label": "Vận động và di chuyển",
                                    "key": "VAN_DONG",
                                    "type": "select",
                                    customClass: "pr-2",
                                    others: {
                                        "tooltip": "thay đổi vị trí, tư thế; di chuyển độc lập hay cần dụng cụ trợ giúp, người trợ giúp; đi lại....",
                                        "data": {
                                            "values": singletonObject.danhsachvandongluonggia
                                        },
                                        "multiple": true
                                    },
                                },
                            ],
                            "width": 6,
                        },
                        {
                            "components": [
                                {
                                    "label": "Chức năng sinh hoạt hàng ngày",
                                    "key": "SINH_HOAT",
                                    "type": "select",
                                    customClass: "pr-2",
                                    others: {
                                        "tooltip": "mức độ độc lập trong hoạt động: ăn uống; tắm rửa; vệ sinh cá nhân; mặc quần áo; sử dụng nhà vệ sinh; kiểm soát đại tiện - tiểu tiện...",
                                        "data": {
                                            "values": singletonObject.danhsachsinhhoatluonggia
                                        },
                                        "multiple": true
                                    },
                                },
                            ],
                            "width": 6,
                        },

                    ],
                    "customClass": "ml-0 mr-0",
                    "key": "THONGTINNGUOINHANHANVIEN",
                    "type": "columns",
                },
                {
                    "label": "Người nhà",
                    "columns": [
                        {
                            "components": [
                                {
                                    "label": "Nhận thức, giao tiếp",
                                    "key": "NHAN_THUC",
                                    "type": "select",
                                    customClass: "pr-2",
                                    others: {
                                        "tooltip": "định hướng; tập trung chú ý; trí nhớ; thờ ơ lãng quên; chức năng điều hành; giao tiếp, ngôn ngữ...",
                                        "data": {
                                            "values": singletonObject.danhsachnhanthucluonggia
                                        },
                                        "multiple": true
                                    },
                                },
                            ],
                            "width": 6,
                        },
                        {
                            "components": [
                                {
                                    "label": "Các chức năng khác",
                                    "key": "CHUC_NANG_KHAC",
                                    "type": "select",
                                    customClass: "pr-2",
                                    others: {
                                        "tooltip": "rối loạn nuốt, tiết niệu, sinh dục, da, các giác quan…….",
                                        "data": {
                                            "values": singletonObject.danhsachchucnangkhacluonggia
                                        },
                                        "multiple": true
                                    },
                                },
                            ],
                            "width": 6,
                        },

                    ],
                    "customClass": "ml-0 mr-0",
                    "key": "THONGTINNGUOINHANHANVIEN",
                    "type": "columns",
                },
                {
                    "label": "Sự tham gia các hoạt động trong gia đình và xã hội",
                    "key": "THAM_GIA_HOAT_DONG",
                    "type": "select",
                    customClass: "pr-2",
                    others: {
                        "tooltip": "chuẩn bị bữa ăn, công việc nội trợ, dọn dẹp nơi sinh hoạt/nhà cửa, đi chợ, mua sắm, tham gia hoạt động xã hội ….",
                        "data": {
                            "values": singletonObject.danhsachthamgiahdluonggia
                        },
                        "multiple": true
                    },
                },
                {
                    "label": "Yếu tố môi trường",
                    "key": "MOI_TRUONG",
                    "type": "select",
                    customClass: "pr-2",
                    others: {
                        "tooltip": "Đánh giá tiếp cận môi trường của trẻ khuyết tật/NB: tình trạng nơi sinh hoạt/ điều trị, nhà vệ sinh, dụng cụ PHCN đang sử dụng; sự hỗ trợ và quan tâm của những người xung quanh; thái độ và cách ứng xử của gia đình, xã hội...",
                        "data": {
                            "values": singletonObject.danhsachmoitruongluonggia
                        },
                        "multiple": true
                    },
                },
                {
                    "label": "Yếu tố cá nhân",
                    "key": "CA_NHAN",
                    "type": "select",
                    customClass: "pr-2",
                    others: {
                        "tooltip": "tình trạng hôn nhân, trình độ học vấn, tình trạng việc làm, thể lực, tâm lý, sở thích, lối sống, thói quen, kỹ năng xử lý tình huống, tính cách,…",
                        "data": {
                            "values": singletonObject.danhsachcanhanluonggia
                        },
                        "multiple": true
                    },
                },
            ] : [
                {
                    "label": "Người nhà",
                    "columns": [
                        {
                            "components": [
                                {
                                    "label": "Vận động và di chuyển",
                                    "key": "VAN_DONG",
                                    "type": "textarea",
                                    customClass: "pr-2",
                                    others: {
                                        "tooltip": "thay đổi vị trí, tư thế; di chuyển độc lập hay cần dụng cụ trợ giúp, người trợ giúp; đi lại....",
                                    },
                                },
                            ],
                            "width": 6,
                        },
                        {
                            "components": [
                                {
                                    "label": "Chức năng sinh hoạt hàng ngày",
                                    "key": "SINH_HOAT",
                                    "type": "textarea",
                                    customClass: "pr-2",
                                    others: {
                                        "tooltip": "mức độ độc lập trong hoạt động: ăn uống; tắm rửa; vệ sinh cá nhân; mặc quần áo; sử dụng nhà vệ sinh; kiểm soát đại tiện - tiểu tiện...",
                                    },
                                },
                            ],
                            "width": 6,
                        },

                    ],
                    "customClass": "ml-0 mr-0",
                    "key": "THONGTINNGUOINHANHANVIEN",
                    "type": "columns",
                },
                {
                    "label": "Người nhà",
                    "columns": [
                        {
                            "components": [
                                {
                                    "label": "Nhận thức, giao tiếp",
                                    "key": "NHAN_THUC",
                                    "type": "textarea",
                                    customClass: "pr-2",
                                    others: {
                                        "tooltip": "định hướng; tập trung chú ý; trí nhớ; thờ ơ lãng quên; chức năng điều hành; giao tiếp, ngôn ngữ...",
                                    },
                                },
                            ],
                            "width": 6,
                        },
                        {
                            "components": [
                                {
                                    "label": "Các chức năng khác",
                                    "key": "CHUC_NANG_KHAC",
                                    "type": "textarea",
                                    customClass: "pr-2",
                                    others: {
                                        "tooltip": "rối loạn nuốt, tiết niệu, sinh dục, da, các giác quan…….",
                                    },
                                },
                            ],
                            "width": 6,
                        },

                    ],
                    "customClass": "ml-0 mr-0",
                    "key": "THONGTINNGUOINHANHANVIEN",
                    "type": "columns",
                },
                {
                    "label": "Sự tham gia các hoạt động trong gia đình và xã hội",
                    "key": "THAM_GIA_HOAT_DONG",
                    "type": "textarea",
                    customClass: "pr-2",
                    others: {
                        "tooltip": "chuẩn bị bữa ăn, công việc nội trợ, dọn dẹp nơi sinh hoạt/nhà cửa, đi chợ, mua sắm, tham gia hoạt động xã hội ….",
                    },
                },
                {
                    "label": "Yếu tố môi trường",
                    "key": "MOI_TRUONG",
                    "type": "textarea",
                    customClass: "pr-2",
                    others: {
                        "tooltip": "Đánh giá tiếp cận môi trường của trẻ khuyết tật/NB: tình trạng nơi sinh hoạt/ điều trị, nhà vệ sinh, dụng cụ PHCN đang sử dụng; sự hỗ trợ và quan tâm của những người xung quanh; thái độ và cách ứng xử của gia đình, xã hội...",
                    },
                },
                {
                    "label": "Yếu tố cá nhân",
                    "key": "CA_NHAN",
                    "type": "textarea",
                    customClass: "pr-2",
                    others: {
                        "tooltip": "tình trạng hôn nhân, trình độ học vấn, tình trạng việc làm, thể lực, tâm lý, sở thích, lối sống, thói quen, kỹ năng xử lý tình huống, tính cách,…",
                    },
                },
            ]),
        ])
        Formio.createForm(document.getElementById('formNhapLuongGiaHDChucNang'),
            jsonForm,{}
        ).then(function(form) {
            formLuongGiaHDChucNang = form;
        });
    }

    function reloadDSLuongGiaHDChucNang(){
        var url = "cmu_getlist?url=" + convertArray([singletonObject.dvtt, thongtinhsba.thongtinbn.SOVAOVIEN, "CMU_GET_LUONGGIAHDCN"]);
        $.get(url).done(function(data){
            if (data && data.length > 0) {
                $("#data_luonggiahdchucnang").html(thongtinhsba.thongtinbn.TEN_PHONGBAN + ' - ' + data[0].NGAY_TAO_PHIEU);
                thongTinLuongGiaHDChucNangMoiNhat = data[0];
                if (data[0].KEYSIGN){
                    $('#edit_single_luonggiahdchucnang').css('visibility', 'hidden');
                    $('#delete_single_luonggiahdchucnang').css('visibility', 'hidden');
                }else{
                    $('#handle_icon_luonggiahdchucnang').css('visibility', 'unset');
                    $('#view_single_luonggiahdchucnang').css('visibility', 'unset');
                    $('#edit_single_luonggiahdchucnang').css('visibility', 'unset');
                    $('#delete_single_luonggiahdchucnang').css('visibility', 'unset');
                }
            } else  {
                $("#data_luonggiahdchucnang").html('Không có dữ liệu');
                $('#handle_icon_luonggiahdchucnang').css('visibility', 'hidden');
            }
        });
        $("#list_luonggiahdchucnang").jqGrid('setGridParam', {
            datatype: 'json',
            url: url
        }).trigger('reloadGrid')
        hideLoaderIntoWrapId("list_ttcs-bdcdct-wrap");
    }

    function themLuongGiaHDChucNang() {
        showSelfLoading("luonggiahdchucnang_luu");
        formLuongGiaHDChucNang.emit("checkValidity");
        if (!formLuongGiaHDChucNang.checkValidity(null, false, null, true)) {
            hideSelfLoading("luonggiahdchucnang_luu");
            return;
        }
        var actionUrl;
        var url;
        var dataSubmit = formLuongGiaHDChucNang.submission.data;
        actionUrl = "cmu_post";
        if (singletonObject.dvtt === "96029" || singletonObject.dvtt === "96001"){
            url = [
                singletonObject.dvtt,
                thongtinhsba.thongtinbn.SOVAOVIEN,
                thongtinhsba.thongtinbn.MA_BENH_NHAN,
                dataSubmit.VAN_DONG.join(".;"),
                dataSubmit.SINH_HOAT.join(".;"),
                dataSubmit.NHAN_THUC.join(".;"),
                dataSubmit.CHUC_NANG_KHAC.join(".;"),
                dataSubmit.THAM_GIA_HOAT_DONG.join(".;"),
                dataSubmit.MOI_TRUONG.join(".;"),
                dataSubmit.CA_NHAN.join(".;"),
                moment(dataSubmit.NGAY_TAO_PHIEU).format("DD/MM/YYYY"),
                singletonObject.userId,
                "CMU_LUONGGIAHDCN_INSERT"
            ];
        } else {
            url = [
                singletonObject.dvtt,
                thongtinhsba.thongtinbn.SOVAOVIEN,
                thongtinhsba.thongtinbn.MA_BENH_NHAN,
                dataSubmit.VAN_DONG,
                dataSubmit.SINH_HOAT,
                dataSubmit.NHAN_THUC,
                dataSubmit.CHUC_NANG_KHAC,
                dataSubmit.THAM_GIA_HOAT_DONG,
                dataSubmit.MOI_TRUONG,
                dataSubmit.CA_NHAN,
                moment(dataSubmit.NGAY_TAO_PHIEU).format("DD/MM/YYYY"),
                singletonObject.userId,
                "CMU_LUONGGIAHDCN_INSERT"
            ];
        }


        $.post(actionUrl, {
            url: url.join('```')
        }).done(function (data) {
            if(data > 0){
                var noidung = ["Số phiếu:"+ data]
                for (const key in dataSubmit) {
                    noidung.push(formLuongGiaHDChucNang.getComponent(key).label.trim(":") + ": " + getValueOfFormIO(formLuongGiaHDChucNang.getComponent(key)));
                }
                var dataLog = {
                    SOVAOVIEN: thongtinhsba.thongtinbn.SOVAOVIEN,
                    LOAI: LOGHSBALOAI.LUONGGIAHDCN.KEY,
                    NOIDUNGBANDAU: "",
                    NOIDUNGMOI: noidung.join("; "),
                    USERID: singletonObject.userId,
                    ACTION: LOGHSBAACTION.INSERT.KEY,
                }
                luuLogHSBATheoBN(dataLog);
                notifiToClient('Green', 'Thêm phiếu thành công');
                $("#modalFormLuongGiaHDChucNang").modal("hide");
            } else {
                notifiToClient("Red",MESSAGEAJAX.ERROR);
            }
        }).fail(function(error) {
            notifiToClient("Red",MESSAGEAJAX.ERROR);
        }).always(function() {
            hideSelfLoading("luonggiahdchucnang_luu");
            setTimeout(function(){
                reloadDSLuongGiaHDChucNang();
            })
        });
    }

    function updateLuongGiaHDChucNang() {
        showSelfLoading("luonggiahdchucnang_luu");
        formLuongGiaHDChucNang.emit("checkValidity");
        if (!formLuongGiaHDChucNang.checkValidity(null, false, null, true)) {
            hideSelfLoading("luonggiahdchucnang_luu");
            return;
        }
        var actionUrl;
        var url;
        var dataSubmit = formLuongGiaHDChucNang.submission.data;
        actionUrl = "cmu_post";
        if (singletonObject.dvtt === "96029" || singletonObject.dvtt === "96001"){
            url = [
                thongTinLuongGiaHDChucNangTruocChinhSua.ID,
                singletonObject.dvtt,
                dataSubmit.VAN_DONG.join(".;"),
                dataSubmit.SINH_HOAT.join(".;"),
                dataSubmit.NHAN_THUC.join(".;"),
                dataSubmit.CHUC_NANG_KHAC.join(".;"),
                dataSubmit.THAM_GIA_HOAT_DONG.join(".;"),
                dataSubmit.MOI_TRUONG.join(".;"),
                dataSubmit.CA_NHAN.join(".;"),
                moment(dataSubmit.NGAY_TAO_PHIEU).format("DD/MM/YYYY"),
                "CMU_LUONGGIAHDCN_UPDATE"
            ];
        } else {
            url = [
                thongTinLuongGiaHDChucNangTruocChinhSua.ID,
                singletonObject.dvtt,
                dataSubmit.VAN_DONG,
                dataSubmit.SINH_HOAT,
                dataSubmit.NHAN_THUC,
                dataSubmit.CHUC_NANG_KHAC,
                dataSubmit.THAM_GIA_HOAT_DONG,
                dataSubmit.MOI_TRUONG,
                dataSubmit.CA_NHAN,
                moment(dataSubmit.NGAY_TAO_PHIEU).format("DD/MM/YYYY"),
                "CMU_LUONGGIAHDCN_UPDATE"
            ];
        }

        $.post(actionUrl, {
            url: url.join('```')
        }).done(function (data) {
            if(data > 0){
                var noidungold = []
                var noidungnew = []
                var luutru = ""
                dataSubmit.NGAY_TAO_PHIEU = moment(dataSubmit.NGAY_TAO_PHIEU).format("DD/MM/YYYY")
                var diffObject = findDifferencesBetweenObjects(thongTinLuongGiaHDChucNangTruocChinhSua, dataSubmit);
                for (const key in diffObject) {
                    luutru = formLuongGiaHDChucNang.getComponent(key).label
                    noidungold.push(luutru.trim(":") + ": " + thongTinLuongGiaHDChucNangTruocChinhSua[key]);
                    noidungnew.push(luutru.trim(":") + ": " + getValueOfFormIO(formLuongGiaHDChucNang.getComponent(key)));
                }
                var dataLog = {
                    SOVAOVIEN: thongtinhsba.thongtinbn.SOVAOVIEN,
                    LOAI: LOGHSBALOAI.LUONGGIAHDCN.KEY,
                    NOIDUNGBANDAU: noidungold.join("; "),
                    NOIDUNGMOI: noidungnew.join("; "),
                    USERID: singletonObject.userId,
                    ACTION: LOGHSBAACTION.EDIT.KEY,
                }
                luuLogHSBATheoBN(dataLog);
                notifiToClient('Green', 'Cập nhật phiếu thành công');
                $("#modalFormLuongGiaHDChucNang").modal("hide");
            } else {
                notifiToClient("Red",MESSAGEAJAX.ERROR);
            }
        }).fail(function(error) {
            notifiToClient("Red",MESSAGEAJAX.ERROR);
        }).always(function() {
            hideSelfLoading("luonggiahdchucnang_luu");
            setTimeout(function(){
                reloadDSLuongGiaHDChucNang();
            })
        });
    }

    function instanceGridLuongGiaHDChucNang(){
        if (!$("#list_luonggiahdchucnang")[0].grid) {
            $("#list_luonggiahdchucnang").jqGrid({
                datatype: "local",
                loadonce: false,
                height: 100,
                width: null,
                shrinkToFit: false,
                colModel: [
                    {
                        name: "KYSO",
                        label: "Ký số",
                        align: 'left',
                        width: 100,
                        formatter: function (cellValue, options, rowData) {
                            if (rowData.KEYSIGN) {
                                return '<span class="cellWithoutBackground" style="font-weight:bold ;color: green">Đã ký</span>';
                            } else {
                                return '<span class="cellWithoutBackground" style="font-weight:bold ;color: red">Chưa ký</span>';
                            }
                        }
                    },
                    {label: 'ID',name: 'ID', index: 'ID', width: 50, align: 'center'},
                    {name: "VAN_DONG", label: "Vận động di chuyển", align: 'center', width: 150},
                    {name: "SINH_HOAT", label: "Chức năng sinh hoạt", align: 'center', width: 80},
                    {name: "NHAN_THUC", label: "Nhận thức, giao tiếp", align: 'center', width: 150, hidden: true},
                    {name: "CHUC_NANG_KHAC", label: "Chức năng khác", align: 'center', width: 150, hidden: true},
                    {name: "THAM_GIA_HOAT_DONG", label: "Tham gia hoạt động", align: 'center', width: 150, hidden: true},
                    {name: "MOI_TRUONG", label: "Yếu tố môi trường", align: 'center', width: 150, hidden: true},
                    {name: "CA_NHAN", label: "Yếu tố cá nhân", align: 'center', width: 150, hidden: true},
                    {name: "TENNGUOITHUCHIEN", label: "Người thực hiện", align: 'center', width: 250},
                    {label: 'Ngày tạo phiếu',name: 'NGAY_TAO_PHIEU', index: 'NGAY_TAO_PHIEU', width: 250, align: 'center'},
                    {name: "KEYSIGN", label: "KEYSIGN", align: 'center', width: 150, hidden: true},
                ],
                rowNum: 1000000,
                caption: "Danh sách phiếu lượng giá hoạt động chức năng và sự tham gia",
                onRightClickRow: function (id1) {
                    showFormLuongGiaHDChucNang();
                    if (id1) {
                        var ret = getThongtinRowSelected("list_luonggiahdchucnang");
                        var items = {
                            "xem": {name: '<p><i class="fa fa-eye text-primary" aria-hidden="true"></i> Xem</p>'},
                        }
                        $.contextMenu('destroy', '#list_luonggiahdchucnang tr');
                        if (ret.KEYSIGN) {
                            items = {
                                ...items,
                                "huykyso": {name: '<p><i class="fa fa-key text-danger" aria-hidden="true"></i> Hủy ký số</p>'},
                            }
                        } else {
                            items = {
                                "kyso": {name: '<p><i class="fa fa-key text-success" aria-hidden="true"></i> Ký số</p>'},
                                ...items,
                                "sua": {name: '<i class="fa fa-pencil-square-o text-primary" aria-hidden="true"></i> Sửa</p>'},
                                "xoa": {name: '<p><i class="fa fa-trash-o text-danger" aria-hidden="true"></i> Xóa</p>'},
                            }
                        }
                        $.contextMenu({
                            selector: '#list_luonggiahdchucnang tr',
                            callback: function (key, options) {
                                var id = $("#list_luonggiahdchucnang").jqGrid('getGridParam', 'selrow');
                                var ret = $("#list_luonggiahdchucnang").jqGrid('getRowData', id);
                                var params = {
                                    magiay: ret.ID,
                                    mabenhnhan: thongtinhsba.thongtinbn.MA_BENH_NHAN,
                                    tennguoibenh: thongtinhsba.thongtinbn.TEN_BENH_NHAN,
                                    sobenhan: thongtinhsba.thongtinbn.SOBENHAN,
                                    sovaovien: thongtinhsba.thongtinbn.SOVAOVIEN,
                                    gioitinh: thongtinhsba.thongtinbn.GIOI_TINH,
                                    tuoi: thongtinhsba.thongtinbn.TUOI,
                                    khoa: thongtinhsba.thongtinbn.TEN_PHONGBAN,
                                    stt_benhan: thongtinhsba.thongtinbn.STT_BENHAN,
                                    stt_dotdieutri: thongtinhsba.thongtinbn.STT_DOTDIEUTRI,
                                }
                                var url = 'cmu_in_cmu_phieuluonggiahdchucnang?type=pdf&' + $.param(params);
                                if (key == "kyso") {
                                    thongTinLuongGiaHDChucNangTruocChinhSua = ret
                                    previewAndSignPdfDefaultModal({
                                        url: url,
                                        idButton: 'luonggiahdcn_kyso_action',
                                    }, function(){

                                    });
                                }
                                if (key == "huykyso") {
                                    // if(ret.BAC_SI != singletonObject.userId) {
                                    //     return notifiToClient("Red", MESSAGEAJAX.PERMISSION);
                                    // }
                                    confirmToClient("Bạn có chắc sẽ hủy ký số phiếu này?", function() {
                                        huykysoFilesign769("PHIEU_NOITRU_LUONGGIAHDCN", ret.ID, singletonObject.userId, singletonObject.dvtt,
                                            thongtinhsba.thongtinbn.SOVAOVIEN, thongtinhsba.thongtinbn.SOVAOVIEN_DT, -1, function(data) {
                                                reloadDSLuongGiaHDChucNang();
                                            })
                                    }, function () {

                                    })
                                }
                                if (key == "xem") {
                                    xemLuongGiaHDChucNang(ret);
                                }
                                if (key== "sua"){
                                    showUpdateLuongGiaHDChucNang(ret)
                                }
                                if (key == "xoa") {
                                    deleteLuongGiaHDChucNang(ret)
                                }
                            },
                            items: items
                        });
                    }
                }

            });
            $("#list_luonggiahdchucnang").jqGrid('filterToolbar', {stringResult: true, searchOnEnter: false, defaultSearch: "cn"});
        }
    }

    function xemLuongGiaHDChucNang(ret){
        var params = {
            ID: ret.ID,
        }
        getUrlLuongGiaHDCN(params).then(objReturn => {
            if (objReturn.isError == 0) {
                previewPdfDefaultModal(objReturn.url, 'preview_phieuluonggiahdchucnang');
            } else {
                notifiToClient("Red", objReturn.message);
            }
        }).catch(error => {
            notifiToClient("Red", error.message || "Lỗi không xác định");
        });
    }

    function showUpdateLuongGiaHDChucNang(ret){
        $("#modalFormLuongGiaHDChucNang").modal("show");
        $("#luonggiahdchucnang_luu").attr("data-action", "CAP_NHAT");
        addTextTitleModal('titleFormLuongGiaHDChucNang', "Phiếu lượng giá hoạt động chức năng và sự tham gia");
        thongTinLuongGiaHDChucNangTruocChinhSua = ret
        if (singletonObject.dvtt === "96029" || singletonObject.dvtt === "96001"){
            formLuongGiaHDChucNang.submission =  {
                data: {
                    ...ret,
                    NGAY_TAO_PHIEU:  (ret.NGAY_TAO_PHIEU? moment(ret.NGAY_TAO_PHIEU, ['DD/MM/YYYY']): moment()).toISOString(),
                    VAN_DONG:  ret.VAN_DONG? ret.VAN_DONG.split(".;"): [],
                    SINH_HOAT:  ret.SINH_HOAT? ret.SINH_HOAT.split(".;"): [],
                    NHAN_THUC:  ret.NHAN_THUC? ret.NHAN_THUC.split(".;"): [],
                    CHUC_NANG_KHAC:  ret.CHUC_NANG_KHAC? ret.CHUC_NANG_KHAC.split(".;"): [],
                    THAM_GIA_HOAT_DONG:  ret.THAM_GIA_HOAT_DONG? ret.THAM_GIA_HOAT_DONG.split(".;"): [],
                    CA_NHAN:  ret.CA_NHAN? ret.CA_NHAN.split(".;"): [],
                    MOI_TRUONG:  ret.MOI_TRUONG? ret.MOI_TRUONG.split(".;"): [],
                }
            };
        } else {
            formLuongGiaHDChucNang.submission =  {
                data: {
                    ...ret,
                    NGAY_TAO_PHIEU:  (ret.NGAY_TAO_PHIEU? moment(ret.NGAY_TAO_PHIEU, ['DD/MM/YYYY']): moment()).toISOString(),
                }
            };
        }
    }

    function deleteLuongGiaHDChucNang(ret){
        var maGiay = ret.ID;
        thongTinLuongGiaHDChucNangTruocChinhSua = ret
        confirmToClient("Bạn có chắc chắn muốn xóa phiếu này?", function() {
            var arr = [maGiay, singletonObject.dvtt]
            var url = "cmu_post_CMU_LUONGGIAHDCN_DELETE";
            $.post(url, {
                url: arr.join("```")
            }).done(function (data) {
                if (data === "1") {
                    var formData = { ...formLuongGiaHDChucNang.submission.data}
                    var noidung = ["Số phiếu:"+ thongTinLuongGiaHDChucNangTruocChinhSua.ID]
                    for (const key in formData) {
                        try {
                            var label = formLuongGiaHDChucNang.getComponent(key).label;
                            if (label) {
                                noidung.push(formLuongGiaHDChucNang.getComponent(key).label + ": " + getValueOfFormIO(formLuongGiaHDChucNang.getComponent(key)));
                            }
                        } catch (error) {
                            // console.log("Error: ", error);
                        }
                    }
                    var dataLog = {
                        SOVAOVIEN: thongtinhsba.thongtinbn.SOVAOVIEN,
                        LOAI: LOGHSBALOAI.LUONGGIAHDCN.KEY,
                        NOIDUNGBANDAU: noidung.join("; "),
                        NOIDUNGMOI: "",
                        USERID: singletonObject.userId,
                        ACTION: LOGHSBAACTION.DELETE.KEY,
                    }
                    luuLogHSBATheoBN(dataLog);                    reloadDSLuongGiaHDChucNang();
                    notifiToClient("Green", MESSAGEAJAX.DEL_SUCCESS)

                } else {
                    notifiToClient("Red", MESSAGEAJAX.ERROR)
                }
            }).fail(function() {
                notifiToClient("Red", MESSAGEAJAX.ERROR)
            })
        }, function () {

        })
    }

    function instanceGridLuongGiaHDItem() {
        var list = $("#list_luonggiahditem");
        if(!list[0].grid) {
            list.jqGrid({
                url: '',
                datatype: "local",
                loadonce: true,
                height: 450,
                width: null,
                shrinkToFit: false,
                ignoreCase: true,
                colModel: [
                    {label: "ID",name: 'ID', index: 'ID', width: 50},
                    {label: "Loại",name: 'LOAI', index: 'LOAI', width: 600, hidden: true},
                    {label: "Loại",name: 'LOAI_HT', index: 'LOAI_HT', width: 600, formatter: function (cellValue, options, rowData) {
                            if (rowData.LOAI == "VAN_DONG") {
                                return 'Vận động và di chuyển';
                            } else if (rowData.LOAI == "SINH_HOAT") {
                                return 'Chức năng sinh hoạt hàng ngày';
                            } else if (rowData.LOAI == "NHAN_THUC") {
                                return 'Nhận thức, giao tiếp';
                            } else if (rowData.LOAI == "CHUC_NANG_KHAC") {
                                return 'Các chức năng khác';
                            } else if (rowData.LOAI == "THAM_GIA_HOAT_DONG") {
                                return 'Sự tham gia các hoạt động trong gia đình và xã hội';
                            } else if (rowData.LOAI == "MOI_TRUONG") {
                                return 'Yếu tố môi trường';
                            } else if (rowData.LOAI == "CA_NHAN") {
                                return 'Yếu tố cá nhân';
                            }
                        }},
                    {label: "Nội dung",name: 'NOIDUNG', index: 'NOIDUNG', width: 600},
                    {label: "KHOA_TAO",name: 'KHOA_TAO', index: 'KHOA_TAO', width: 50, hidden: true},
                    {label: "Người tạo",name: 'TEN_NGUOI_TAO', index: 'TEN_NGUOI_TAO', width: 200},
                    {label: "NGUOITAO",name: 'NGUOITAO', index: 'NGUOITAO', width: 50, hidden: true},
                ],
                rowNum: 1000000,
                caption: "Danh sách mẫu chọn",
                onSelectRow: function (id) {
                },
                onRightClickRow: function(id) {
                    if (id) {
                        var ret = getThongtinRowSelected("list_luonggiahditem");
                        var items = {
                            "xoa": {name: '<p class="text-danger"><i class="fa fa-remove text-danger" aria-hidden="true"></i> Xoá</p>'},
                        }
                        $.contextMenu('destroy', '#list_luonggiahditem tr');
                        $.contextMenu({
                            selector: '#list_luonggiahditem tr',
                            reposition : false,
                            callback: function (key, options) {
                                if(key == 'xoa'){
                                    if(ret.NGUOITAO != singletonObject.userId) {
                                        return notifiToClient("Red", MESSAGEAJAX.PERMISSION);
                                    }
                                    confirmToClient("Xác nhận xóa thông tin này?", function (confirm) {
                                        $.ajax({
                                            url: "cmu_post",
                                            method: "POST",
                                            data: {url:[singletonObject.dvtt, ret.ID, "CMU_LUONGGIAHD_ITEM_DEL"].join("```")},
                                            success: function (data) {
                                                if(data > 0){
                                                    notifiToClient("Green",MESSAGEAJAX.DEL_SUCCESS);
                                                    loadLuongGiaHDItem();
                                                    reloadDSLuongGiaHDItem();
                                                } else {
                                                    notifiToClient("Red",MESSAGEAJAX.ERROR);
                                                }
                                            },
                                            error: function (error) {
                                                notifiToClient("Red",MESSAGEAJAX.ERROR);
                                            }
                                        });
                                    });
                                }
                            },
                            items: items
                        });
                    }
                }
            });
            list.jqGrid('filterToolbar', {stringResult: true, searchOnEnter: false, defaultSearch: "cn"});
        }
        reloadDSLuongGiaHDItem();
    }

    function reloadDSLuongGiaHDItem() {
        var url = "cmu_getlist?url=" + convertArray([singletonObject.dvtt, -1, "CMU_LUONGGIAHD_ITEM_SELECT"]);
        $("#list_luonggiahditem").jqGrid('clearGridData');
        $.get(url).done(function (data) {
            if (data && data.length > 0) {
                $("#list_luonggiahditem").jqGrid('setGridParam', {
                    datatype: 'local',
                    data: data
                }).trigger('reloadGrid');
            }
        });
    }

    function themLuongGiaHDItem() {
        var idButton = "luonggiahditem_them";
        if ($("#luonggiahditem_noidung").val()){
            showSelfLoading(idButton);
            $.post("cmu_post", {url:[singletonObject.dvtt,
                    $("#luonggiahditem_loai").val(),
                    $("#luonggiahditem_noidung").val(),
                    singletonObject.makhoa,
                    singletonObject.userId,
                    "CMU_LUONGGIAHD_ITEM_INS"].join("```")}).done(function (id) {
                if(id > 0){
                    $("#luonggiahditem_noidung").val("");
                    reloadDSLuongGiaHDItem();
                    loadLuongGiaHDItem();
                    notifiToClient("Green",MESSAGEAJAX.SUCCESS);
                } else {
                    if(id == '-1'){
                        return notifiToClient("Red",MESSAGEAJAX.ERROR);
                    }
                    if (id == '-2'){
                        return notifiToClient("Red", "Dữ liệu đã tồn tại");
                    }
                }
            }).fail(function(error) {
                notifiToClient("Red",MESSAGEAJAX.ERROR);
            }).always(function() {
                hideSelfLoading(idButton);
            });
        } else {
            notifiToClient("Red", "Vui lòng nhập nội dung");
        }
    }

    // Bổ sung Mẫu chuẩn bị
    $("#mauChuanBiPhieuLuongGiaHDChucNang").click(function() {
        let element = $("#mau_danhsachmaujson_wrap");
        element.attr("function-add", 'insertMauCBPhieuLuongGiaHDChucNang');
        element.attr("function-chinhsua", 'editMauCBPhieuLuongGiaHDChucNang');
        element.attr("function-select", 'selectMauCBPhieuLuongGiaHDChucNang');
        element.attr("function-getdata", 'getdataMauCBPhieuLuongGiaHDChucNang');
        element.attr("function-validate", 'formioCBPhieuLuongGiaHDChucNangValidate');
        element.attr("data-key", 'MAUCBPHIEULUONGGIAHDCHUCNANG');
        $("#modalMauChungJSON").modal("show");
        $.loadDanhSachMauChungJSON('MAUCBPHIEULUONGGIAHDCHUCNANG')
    }); $.extend({
        insertMauCBPhieuLuongGiaHDChucNang: function () {
            generateFormMauCBPhieuLuongGiaHDChucNang({})
        },
        editMauCBPhieuLuongGiaHDChucNang: function (rowSelect) {
            let json = JSON.parse(rowSelect.NOIDUNG);
            let dataMau = {}
            json.forEach(function(item) {
                dataMau[item.key] = item.value
            })
            generateFormMauCBPhieuLuongGiaHDChucNang({
                ID: rowSelect.ID,
                TENMAU: rowSelect.TENMAU,
                ...dataMau
            })
        },
        selectMauCBPhieuLuongGiaHDChucNang: function (rowSelect) {
            let json = JSON.parse(rowSelect.NOIDUNG);
            json.forEach(function(item) {
                $(`#formNhapLuongGiaHDChucNang [name="data[${item.key}]"]`).val(item.value)
                formLuongGiaHDChucNang.data[item.key] = item.value
            })
            $("#modalMauChungJSON").modal("hide");
        },
        getdataMauCBPhieuLuongGiaHDChucNang: function () {
            let objectNoidung = [];
            getObjectMauCBPhieuLuongGiaHDChucNang().forEach(function(item) {
                if (item.key !== 'ID' && item.key !== 'TENMAU') {
                    objectNoidung.push({
                        "label": item.label,
                        "value": formioMauHSBA.submission.data[item.key],
                        "key": item.key,
                    })
                }
            })
            return {
                ID: formioMauHSBA.submission.data.ID,
                TENMAU: formioMauHSBA.submission.data.TENMAU,
                NOIDUNG: JSON.stringify(objectNoidung),
                KEYMAUCHUNG: 'MAUCBPHIEULUONGGIAHDCHUCNANG'
            };
        },
        formioCBPhieuLuongGiaHDChucNangValidate: function() {
            formioMauHSBA.emit("checkValidity");
            return formioMauHSBA.checkValidity(null, false, null, true);

        },
    });

    function generateFormMauCBPhieuLuongGiaHDChucNang(dataForm) {
        let jsonForm = getJSONObjectForm(getObjectMauCBPhieuLuongGiaHDChucNang());
        Formio.createForm(document.getElementById('formChiTietMauChungJSON'),
            jsonForm,{}
        ).then(function(form) {
            formioMauHSBA = form;
            formioMauHSBA.submission = { data: { ...dataForm }}
        });
    }

    function getObjectMauCBPhieuLuongGiaHDChucNang() {
        return [
            {
                "label": "ID",
                "key": "ID",
                "type": "textfield",
                others: {
                    hidden: true
                }
            },
            {
                "label": "Tên mẫu",
                "key": "TENMAU",
                "type": "textarea",
                validate: {
                    required: true
                },
                others: {
                    "labelPosition": "left-left",
                    "labelWidth": 10
                }
            },
            {
                "label": "Vận động và di chuyển",
                "key": "VAN_DONG",
                "type": "textarea",
                others: {
                    "labelPosition": "left-left",
                    "labelWidth": 10
                }
            },
            {
                "label": "Chức năng sinh hoạt hàng ngày",
                "key": "SINH_HOAT",
                "type": "textarea",
                others: {
                    "labelPosition": "left-left",
                    "labelWidth": 10
                }
            },
            {
                "label": "Nhận thức, giao tiếp",
                "key": "NHAN_THUC",
                "type": "textarea",
                others: {
                    "labelPosition": "left-left",
                    "labelWidth": 10
                }
            },
            {
                "label": "Các chức năng khác",
                "key": "CHUC_NANG_KHAC",
                "type": "textarea",
                others: {
                    "labelPosition": "left-left",
                    "labelWidth": 10
                }
            },
            {
                "label": "Sự tham gia các hoạt động trong gia đình và xã hội",
                "key": "THAM_GIA_HOAT_DONG",
                "type": "textarea",
                others: {
                    "labelPosition": "left-left",
                    "labelWidth": 10
                }
            },
            {
                "label": "Yếu tố môi trường",
                "key": "MOI_TRUONG",
                "type": "textarea",
                others: {
                    "labelPosition": "left-left",
                    "labelWidth": 10
                }
            },
            {
                "label": "Yếu tố cá nhân",
                "key": "CA_NHAN",
                "type": "textarea",
                others: {
                    "labelPosition": "left-left",
                    "labelWidth": 10
                }
            }
        ];
    }
})