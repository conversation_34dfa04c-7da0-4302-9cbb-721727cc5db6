const phieuChuanDoanNguyenNhanTuVongJS = {};
let formPhieuChuanDoanNguyenNhanTuVong;
let formPhieuChuanDoanNguyenNhanTuVongTruocChinhSua;
let formPhieuChuanDoanNguyenNhanTuVongMoiNhat;

$(function () {
    let validateCDNNTV = [
        {className: "formio-js-validate-1", cusMsg: "Ngày tạo phiếu là bắt buộc", type: "input"},
        {className: "formio-js-validate-2", cusMsg: "Ngày tử vong là bắt buộc", type: "input"},
        {className: "formio-js-validate-3", cusMsg: "Chuẩn đoán nguyên nhân tử vong (a) là bắt buộc", type: "input"}
    ]

    let kyHieuCDNNTV = [
        "PHIEU_CD_NGUYEN_NHAN_TU_VONG_NGUOI_LAP_PHIEU",
        "PHIEU_CD_NGUYEN_NHAN_TU_VONG_THU_TRUONG_BAO_TU",
    ].join(",");

    $(".themPhieuChuanDoanNguyenNhanTuVong").click(function () {
        loadFormPhieuChuanDoanNguyenNhanTuVong();
        initModalPhieuChuanDoanNguyenNhanTuVong("THEM");
    });

    $("#xemThemDSPhieuChuanDoanNguyenNhanTuVong, #hsba_chandoannntuvong").click(function(){
        $("#modalDSPhieuChuanDoanNguyenNhanTuVong").modal("show");
        initGridPhieuChuanDoanNguyenNhanTuVong();
        phieuChuanDoanNguyenNhanTuVongJS.reloadDSPhieuChuanDoanNguyenNhanTuVong();
    });

    $("#iconXemPhieuChuanDoanNguyenNhanTuVong").click(function () {
        getFilesign769V2(
            kyHieuCDNNTV, formPhieuChuanDoanNguyenNhanTuVongMoiNhat.ID,
            -1, singletonObject.dvtt,
            thongtinhsba.thongtinbn.SOVAOVIEN,
            thongtinhsba.thongtinbn.SOVAOVIEN_DT, -1,
            function(dataKySo) {
                viewPhieuChuanDoanNguyenNhanTuVong(
                    Number(formPhieuChuanDoanNguyenNhanTuVongMoiNhat.ID), false, dataKySo
                );
            });
    });

    $("#iconSuaPhieuChuanDoanNguyenNhanTuVong").click(function () {
        if (formPhieuChuanDoanNguyenNhanTuVongMoiNhat.NGUOI_TAO !== singletonObject.userId) {
            return notifiToClient('Red', MESSAGEAJAX.PERMISSION);
        }
        formPhieuChuanDoanNguyenNhanTuVongMoiNhat.DATA_PHIEU.ID = formPhieuChuanDoanNguyenNhanTuVongMoiNhat.ID
        loadFormPhieuChuanDoanNguyenNhanTuVong(formPhieuChuanDoanNguyenNhanTuVongMoiNhat.DATA_PHIEU);
        initModalPhieuChuanDoanNguyenNhanTuVong('SUA', formPhieuChuanDoanNguyenNhanTuVongMoiNhat);
    });

    $("#iconXoaPhieuChuanDoanNguyenNhanTuVong").click(function () {
        if (formPhieuChuanDoanNguyenNhanTuVongMoiNhat.NGUOI_TAO !== singletonObject.userId) {
            return notifiToClient('Red', MESSAGEAJAX.PERMISSION);
        }
        delelePhieuChuanDoanNguyenNhanTuVong(formPhieuChuanDoanNguyenNhanTuVongMoiNhat.ID);
    })

    $("#taoPhieuChuanDoanNguyenNhanTuVong").click(function () {
        formPhieuChuanDoanNguyenNhanTuVong.emit("checkValidity");
        if (!formPhieuChuanDoanNguyenNhanTuVong.checkValidity(null, false, null, true)) {
            xuLyValidateNotWorking(validateCDNNTV);
            return;
        }
        let formData = formPhieuChuanDoanNguyenNhanTuVong.submission.data
        let thisBtn = this.id;
        showSelfLoading(thisBtn);
        try {
            $.post("cmu_post_CMU_CD_NGUYEN_NHAN_TU_VONG_INS", {
                url: [
                    singletonObject.dvtt,
                    thongtinhsba.thongtinbn.MABENHNHAN,
                    JSON.stringify(formData),
                    thongtinhsba.thongtinbn.SOVAOVIEN,
                    thongtinhsba.thongtinbn.SOVAOVIEN_DT,
                    thongtinhsba.thongtinbn.STT_BENHAN,
                    singletonObject.userId,
                    singletonObject.makhoa,
                ].join("```")
            }).fail(function() {
                notifiToClient('Red', MESSAGEAJAX.ERROR);
            }).done(function(data) {
                if (data > 0) {
                    let subData = getSubCDNNTV()
                    luuLogHSBAInsertFormioV4(
                        formPhieuChuanDoanNguyenNhanTuVong,
                        LOGHSBALOAI.CHUANDOANNGUYENNHANTUVONG.KEY,
                        "Tạo " + LOGHSBALOAI.CHUANDOANNGUYENNHANTUVONG.VALUE + " - ID: " + data + " - ",
                        subData
                    );
                    notifiToClient('Green', MESSAGEAJAX.ADD_SUCCESS);
                    phieuChuanDoanNguyenNhanTuVongJS.reloadDSPhieuChuanDoanNguyenNhanTuVong();
                    $("#modalPhieuChuanDoanNguyenNhanTuVong").modal('hide');
                }
                else {
                    notifiToClient('Red', MESSAGEAJAX.FAIL);
                }
            }).always(function() {
                hideSelfLoading(thisBtn);
            });
        }
        catch (err) {
            console.error(err)
        }
    });

    $("#suaPhieuChuanDoanNguyenNhanTuVong").click(function () {
        let formData = $.extend(true, {}, formPhieuChuanDoanNguyenNhanTuVong.submission.data);
        formPhieuChuanDoanNguyenNhanTuVong.emit("checkValidity");
        if (!formPhieuChuanDoanNguyenNhanTuVong.checkValidity(null, false, null, true)) {
            xuLyValidateNotWorking(validateCDNNTV);
            return;
        }
        let dataToSave = $.extend(true, {}, formPhieuChuanDoanNguyenNhanTuVong.submission.data);
        delete dataToSave.ID;
        let thisBtn = this.id;
        showSelfLoading(thisBtn);
        $.post("cmu_post_CMU_CD_NGUYEN_NHAN_TU_VONG_UPD", {
            url: [
                formData.ID,
                singletonObject.dvtt,
                JSON.stringify(dataToSave),
            ].join("```")
        }).fail(function() {
            notifiToClient('Red', MESSAGEAJAX.ERROR);
        }).done(function(data) {
            if (data > 0) {
                let subData = getSubCDNNTV()
                luuLogHSBAChinhSuaFormioV4(
                    formPhieuChuanDoanNguyenNhanTuVongTruocChinhSua.DATA_PHIEU,
                    formPhieuChuanDoanNguyenNhanTuVong,
                    LOGHSBALOAI.CHUANDOANNGUYENNHANTUVONG.KEY,
                    "Cập nhật " + LOGHSBALOAI.CHUANDOANNGUYENNHANTUVONG.VALUE + " - ID: " + formData.ID + " - ",
                    subData
                );
                notifiToClient('Green', MESSAGEAJAX.EDIT_SUCCESS);
                phieuChuanDoanNguyenNhanTuVongJS.reloadDSPhieuChuanDoanNguyenNhanTuVong();
                $('#modalPhieuChuanDoanNguyenNhanTuVong').modal('hide');
            }
            else {
                notifiToClient('Red', MESSAGEAJAX.FAIL);
            }
        }).always(function() {
            hideSelfLoading(thisBtn);
        });
    });

    function autoScaleTextarea(textarea) {
        $(textarea).css('height', 'auto');
        $(textarea).css('height', textarea.scrollHeight + 'px');
    }

    function delelePhieuChuanDoanNguyenNhanTuVong(id) {
        confirmToClient(MESSAGEAJAX.CONFIRM, function() {
            $.post("cmu_post_CMU_CD_NGUYEN_NHAN_TU_VONG_DEL", {
                url: [id, singletonObject.dvtt].join("```")
            }).fail(function() {
                notifiToClient("Red", MESSAGEAJAX.ERROR);
            }).done(function(data) {
                if (data === '1') {
                    luuLogHSBADeleteFormio(
                        LOGHSBALOAI.CHUANDOANNGUYENNHANTUVONG.KEY,
                        "Xóa " + LOGHSBALOAI.CHUANDOANNGUYENNHANTUVONG.VALUE + " - ID: " + id
                    );
                    notifiToClient('Green', MESSAGEAJAX.DEL_SUCCESS);
                    phieuChuanDoanNguyenNhanTuVongJS.reloadDSPhieuChuanDoanNguyenNhanTuVong();
                } else {
                    notifiToClient('Red', MESSAGEAJAX.ERROR);
                }
            }).always(function() {});
        }, function() {});
    }

    function dinhKemSuKienPhieuChuanDoanNguyenNhanTuVong(isInit) {
        let textarea = $('.formio-js-scale-textarea textarea[type="text"]');
        textarea.each(function() {
            $(this).attr('rows', 1);
            $(this).on('input', function() { autoScaleTextarea(this); });
            autoScaleTextarea(this);
        });

        $.each([
            {
                SELECT_KEY: "PHAU_THUAT_4_TUAN_QUA", INPUT_KEY: "GHI_RO_LY_DO_PHAU_THUAT",
                CONDITION: "CO", TYPE: "textarea"
            },
            {
                SELECT_KEY: "PHAU_THUAT_4_TUAN_QUA", INPUT_KEY: "NGAY_PHAU_THUAT",
                CONDITION: "CO", TYPE: "datetime"
            },
            {
                SELECT_KEY: "DA_KHAM_NGHIEM_TU_THI", INPUT_KEY: "NEU_CO_KHAM_NGHIEM_TU_THI",
                CONDITION: "CO", TYPE: "select"
            },
            {
                SELECT_KEY: "NOI_XAY_RA_TU_VONG", INPUT_KEY: "DIA_DIEM_KHAC_GHI_RO",
                CONDITION: "DIA_DIEM_KHAC", TYPE: "textarea"
            },
            {
                SELECT_KEY: "NGUOI_CHET_MANG_THAI_KHONG", INPUT_KEY: "THOI_DIEM_NGUOI_CHET_MANG_THAI",
                CONDITION: "CO", TYPE: "select"
            },
            {
                SELECT_KEY: "NGUOI_CHET_MANG_THAI_KHONG", INPUT_KEY: "MANG_THAI_CO_GAY_TU_VONG",
                CONDITION: "CO", TYPE: "select"
            }
        ], function(_, v) {
            let selectTag = $('#phieuChuanDoanNguyenNhanTuVong [name="data[' + v.SELECT_KEY + ']"]');
            let inputTag = $('#phieuChuanDoanNguyenNhanTuVong [name="data[' + v.INPUT_KEY + ']"]');
            if (v.TYPE === "text" || v.TYPE === "textarea") {
                if (formPhieuChuanDoanNguyenNhanTuVong.data[v.SELECT_KEY] !== v.CONDITION) {
                    inputTag.prop('disabled', true);
                }
                selectTag.on('change', function () {
                    if (formPhieuChuanDoanNguyenNhanTuVong.data[v.SELECT_KEY] === v.CONDITION) {
                        inputTag.prop('disabled', false);
                    } else {
                        inputTag.val(""); inputTag.prop('disabled', true);
                        if (v.TYPE === "textarea") { autoScaleTextarea(inputTag); }
                        formPhieuChuanDoanNguyenNhanTuVong.data[v.INPUT_KEY] = "";
                    }
                });
            }
            else {
                if (formPhieuChuanDoanNguyenNhanTuVong.data[v.SELECT_KEY] !== v.CONDITION) {
                    formPhieuChuanDoanNguyenNhanTuVong.getComponent(v.INPUT_KEY).parentDisabled = true;
                }
                selectTag.on('change', function () {
                    if (formPhieuChuanDoanNguyenNhanTuVong.data[v.SELECT_KEY] === v.CONDITION) {
                        formPhieuChuanDoanNguyenNhanTuVong.getComponent(v.INPUT_KEY).parentDisabled = false;
                    } else {
                        formPhieuChuanDoanNguyenNhanTuVong.getComponent(v.INPUT_KEY).parentDisabled = true;
                        formPhieuChuanDoanNguyenNhanTuVong.data[v.INPUT_KEY] = "";
                    }
                    formPhieuChuanDoanNguyenNhanTuVong.redraw(); dinhKemSuKienPhieuChuanDoanNguyenNhanTuVong();
                });
            }
        });

        $.each([
            "formio-js-collap-tt-yte"
        ], function(_, value) {
            $("." + value + " .card-header").on('click', function() {
                let isExpanded = $(this).attr('aria-expanded') === 'true';
                if (isExpanded) { $(document).trigger("ctrlCollapInPCDNNTV"); }
            });
        });

        if (isInit) {
            formPhieuChuanDoanNguyenNhanTuVong.redraw();
            dinhKemSuKienPhieuChuanDoanNguyenNhanTuVong();
        }
    }

    function initGridPhieuChuanDoanNguyenNhanTuVong() {
        let listData = $('#phieuChuanDoanNguyenNhanTuVongGrid');
        if (!listData[0].grid) {
            listData.jqGrid({
                datatype: 'local',
                data: [],
                loadonce: true,
                height: 400,
                width: null,
                shrinkToFit: false,
                colModel: [
                    // show
                    {
                        name: "KYSO1",
                        label: "Ký số 1",
                        align: 'left',
                        width: 100,
                        formatter: function (cellValue, options, rowData) {
                            if (rowData.KEYSIGN_1) {
                                return '<span class="cellWithoutBackground" style="font-weight:bold; color: green">Đã ký</span>';
                            } else {
                                return '<span class="cellWithoutBackground" style="font-weight:bold; color: red">Chưa ký</span>';
                            }
                        },
                        cellattr: function () {
                            return ' title="Ký số Người lập phiếu"';
                        }
                    },
                    {
                        name: "KYSO2",
                        label: "Ký số 2",
                        align: 'left',
                        width: 100,
                        formatter: function (cellValue, options, rowData) {
                            if (rowData.KEYSIGN_2) {
                                return '<span class="cellWithoutBackground" style="font-weight:bold; color: green">Đã ký</span>';
                            } else {
                                return '<span class="cellWithoutBackground" style="font-weight:bold; color: red">Chưa ký</span>';
                            }
                        },
                        cellattr: function () {
                            return ' title="Ký số Thủ trưởng cơ quan/tổ chức báo tử"';
                        }
                    },
                    {name: 'ID', label: "ID", width: 100},
                    {
                        name: 'DATA_PARSED.CHUAN_DOAN_NGUYEN_NHAN_TU_VONG_A',
                        label: 'Nguyên nhân tử vong trực tiếp', width: 300
                    },
                    {name: 'DATA_PARSED.NGUOI_LAP_PHIEU.tennhanvien', label: 'Người lập phiếu', width: 250},
                    {name: 'DATA_PARSED.THU_TRUONG_BAO_TU.TEXT', label: 'Thủ trưởng báo tử', width: 250},
                    {name: 'TENNGUOITAO', label: 'Người tạo', width: 250},
                    {name: 'NGAYTAO', label: 'Ngày tạo', width: 150},

                    // hidden
                    {name: 'NGUOI_TAO', hidden: true},
                    {name: 'DATA_PHIEU', hidden: true},
                    {name: "KEYSIGN_1", hidden: true},
                    {name: "KEYSIGN_2", hidden: true},
                ],
                onRightClickRow: function () {
                    let ret = getThongtinRowSelected("phieuChuanDoanNguyenNhanTuVongGrid");
                    let items = {
                        "xem": { name: '<p><i class="fa fa-eye text-primary" aria-hidden="true"></i> Xem</p>' },
                    }
                    $.contextMenu('destroy', '#phieuChuanDoanNguyenNhanTuVongGrid tr');
                    if (ret.KEYSIGN_1 && ret.KEYSIGN_2) {
                        getFilesign769V2(
                            kyHieuCDNNTV, ret.ID, -1, singletonObject.dvtt,
                            thongtinhsba.thongtinbn.SOVAOVIEN,
                            thongtinhsba.thongtinbn.SOVAOVIEN_DT, -1,
                            function(dataKySo) {
                                if (dataKySo[0].KY_HIEU_PHIEU === 'PHIEU_CD_NGUYEN_NHAN_TU_VONG_NGUOI_LAP_PHIEU') {
                                    items = {
                                        "huykyso1": {name: '<p><i class="fa fa-key text-danger" aria-hidden="true"></i> Hủy ký số Người lập phiếu</p>'},
                                        ...items,
                                    }
                                }
                                else {
                                    items = {
                                        "huykyso2": {name: '<p><i class="fa fa-key text-danger" aria-hidden="true"></i> Hủy ký số Thủ trưởng</p>'},
                                        ...items,
                                    }
                                }
                            });
                    }
                    else if (ret.KEYSIGN_1){
                        items = {
                            "huykyso1": {name: '<p><i class="fa fa-key text-danger" aria-hidden="true"></i> Hủy ký số Người lập phiếu</p>'},
                            "kyso2": {name: '<p><i class="fa fa-key text-success" aria-hidden="true"></i> Ký số Thủ trưởng</p>'},
                            ...items,
                        }
                    }
                    else if (ret.KEYSIGN_2){
                        items = {
                            "kyso1": {name: '<p><i class="fa fa-key text-success" aria-hidden="true"></i> Ký số Người lập phiếu</p>'},
                            "huykyso2": {name: '<p><i class="fa fa-key text-danger" aria-hidden="true"></i> Hủy ký số Thủ trưởng</p>'},
                            ...items,
                        }
                    }
                    else {
                        items = {
                            "kyso1": {name: '<p><i class="fa fa-key text-success" aria-hidden="true"></i> Ký số Người lập phiếu</p>'},
                            "kyso2": {name: '<p><i class="fa fa-key text-success" aria-hidden="true"></i> Ký số Thủ trưởng</p>'},
                            ...items,
                            "sua": {
                                name: '<p><i class="fa fa-pencil-square-o text-primary" aria-hidden="true"></i> Sửa</p>'
                            },
                            "xoa": {
                                name: '<p><i class="fa fa-trash-o text-danger" aria-hidden="true"></i> Xóa</p>'
                            },
                        }
                    }

                    $.contextMenu({
                        selector: '#phieuChuanDoanNguyenNhanTuVongGrid tr',
                        callback: function (key) {
                            let ret = getThongtinRowSelected("phieuChuanDoanNguyenNhanTuVongGrid");
                            let dataJson = JSON.parse(ret.DATA_PHIEU);
                            dataJson.ID = Number(ret.ID); ret.DATA_PHIEU = dataJson;
                            if (key === 'kyso1') {
                                formPhieuChuanDoanNguyenNhanTuVongTruocChinhSua = $.extend(true, {}, {
                                    ID: ret.ID, DATA_PHIEU: dataJson
                                });
                                getFilesign769V2(
                                    kyHieuCDNNTV, ret.ID, -1, singletonObject.dvtt,
                                    thongtinhsba.thongtinbn.SOVAOVIEN,
                                    thongtinhsba.thongtinbn.SOVAOVIEN_DT, -1,
                                    function(dataKySo) {
                                        viewPhieuChuanDoanNguyenNhanTuVong(
                                            Number(ret.ID), true, dataKySo, "NguoiLapPhieu"
                                        );
                                    });
                            }
                            if (key === 'kyso2') {
                                formPhieuChuanDoanNguyenNhanTuVongTruocChinhSua = $.extend(true, {}, {
                                    ID: ret.ID, DATA_PHIEU: dataJson
                                });
                                getFilesign769V2(
                                    kyHieuCDNNTV, ret.ID, -1, singletonObject.dvtt,
                                    thongtinhsba.thongtinbn.SOVAOVIEN,
                                    thongtinhsba.thongtinbn.SOVAOVIEN_DT, -1,
                                    function(dataKySo) {
                                        viewPhieuChuanDoanNguyenNhanTuVong(
                                            Number(ret.ID), true, dataKySo, "ThuTruong"
                                        );
                                    });
                            }
                            if (key === 'huykyso1') {
                                confirmToClient("Bạn có chắc sẽ hủy ký số phiếu này?",
                                    function() {
                                        huykysoFilesign769V2(
                                            kyHieuCDNNTV, ret.ID, singletonObject.userId, singletonObject.dvtt,
                                            thongtinhsba.thongtinbn.SOVAOVIEN,
                                            thongtinhsba.thongtinbn.SOVAOVIEN_DT, -1,
                                            function(_) {
                                                phieuChuanDoanNguyenNhanTuVongJS.reloadDSPhieuChuanDoanNguyenNhanTuVong();
                                            }
                                        );
                                    }, function () {});
                            }
                            if (key === 'huykyso2') {
                                confirmToClient("Bạn có chắc sẽ hủy ký số phiếu này?",
                                    function() {
                                        huykysoFilesign769V2(
                                            kyHieuCDNNTV, ret.ID, singletonObject.userId, singletonObject.dvtt,
                                            thongtinhsba.thongtinbn.SOVAOVIEN,
                                            thongtinhsba.thongtinbn.SOVAOVIEN_DT, -1,
                                            function(_) {
                                                phieuChuanDoanNguyenNhanTuVongJS.reloadDSPhieuChuanDoanNguyenNhanTuVong();
                                            }
                                        );
                                    }, function () {});
                            }
                            if (key === 'xem') {
                                getFilesign769V2(
                                    kyHieuCDNNTV, ret.ID, -1, singletonObject.dvtt,
                                    thongtinhsba.thongtinbn.SOVAOVIEN,
                                    thongtinhsba.thongtinbn.SOVAOVIEN_DT, -1,
                                    function(dataKySo) {
                                        viewPhieuChuanDoanNguyenNhanTuVong(Number(ret.ID), false, dataKySo);
                                    });
                            }
                            if (key === 'sua') {
                                if (ret.NGUOI_TAO !== singletonObject.userId) {
                                    return notifiToClient('Red', MESSAGEAJAX.PERMISSION);
                                }
                                loadFormPhieuChuanDoanNguyenNhanTuVong(dataJson);
                                initModalPhieuChuanDoanNguyenNhanTuVong('SUA', ret);
                            }
                            if (key === 'xoa') {
                                if(ret.NGUOI_TAO !== singletonObject.userId) {
                                    return notifiToClient('Red', MESSAGEAJAX.PERMISSION);
                                }
                                delelePhieuChuanDoanNguyenNhanTuVong(Number(ret.ID));
                            }
                        },
                        items: items
                    })
                },
            });
            listData.jqGrid('filterToolbar', {stringResult: true, searchOnEnter: false, defaultSearch: 'cn'});
        }
    }

    function initModalPhieuChuanDoanNguyenNhanTuVong(action, data) {
        let titleName = " Phiếu chẩn đoán nguyên nhân tử vong";
        $('#modalPhieuChuanDoanNguyenNhanTuVong').modal('show');
        addTextTitleModal('titlePhieuChuanDoanNguyenNhanTuVong', titleName);
        if(action === 'THEM') {
            $('#taoPhieuChuanDoanNguyenNhanTuVong').show();
            $('#suaPhieuChuanDoanNguyenNhanTuVong').hide();
        } else {
            formPhieuChuanDoanNguyenNhanTuVongTruocChinhSua = $.extend(true, {}, data);
            $('#taoPhieuChuanDoanNguyenNhanTuVong').hide();
            $('#suaPhieuChuanDoanNguyenNhanTuVong').show();
        }
    }

    function loadFormPhieuChuanDoanNguyenNhanTuVong(data, callback) {
        try {
            $.getJSON(
                "resources/camau/js/formioPhieuChuanDoanNguyenNhanTuVong.json?v="+moment().format('YYYYMMDDHHmmss')
            ).done(function(rs){
                Formio.createForm(document.getElementById('phieuChuanDoanNguyenNhanTuVong'),rs, {
                    disableAlerts: true,
                }).then(function(instance) {
                    formPhieuChuanDoanNguyenNhanTuVong = instance;
                    let nguoiLapPhieu = formPhieuChuanDoanNguyenNhanTuVong.getComponent("NGUOI_LAP_PHIEU").component;
                    nguoiLapPhieu.data.json = singletonObject.danhsachtatcanhanvien;
                    let nguoiBaoTu = formPhieuChuanDoanNguyenNhanTuVong.getComponent("THU_TRUONG_BAO_TU").component;
                    nguoiBaoTu.data.json = singletonObject.danhsachthutruong;
                    let ngayTaoPhieu = formPhieuChuanDoanNguyenNhanTuVong.getComponent("NGAY_TAO_PHIEU").component;
                    ngayTaoPhieu.widget.minDate = moment(
                        thongtinhsba.thongtinbn.NGAY_VAO_VIEN + " " + thongtinhsba.thongtinbn.GIO_VAO_VIEN,
                        ['DD/MM/YYYY HH:mm:ss']).format("YYYY-MM-DD HH:mm");
                    ngayTaoPhieu.widget.maxDate = moment().format("YYYY-MM-DD HH:mm");

                    let initData = { data: {} };
                    if (data) {
                        initData.data = data;
                    }
                    else {
                        initData.data = {
                            NGUOI_LAP_PHIEU: singletonObject.danhsachtatcanhanvien.find(
                                (item) => item.value === singletonObject.userId)
                        }
                    }

                    formPhieuChuanDoanNguyenNhanTuVong.setSubmission(initData).then(function () {
                        dinhKemSuKienPhieuChuanDoanNguyenNhanTuVong(true);
                    }).catch(function(err) {
                        notifiToClient('Red', 'Lỗi cài đặt dữ liệu'); console.error(err);
                    });
                    callback && callback();
                });
            })
        } catch (err) {
            console.error(err)
        }
    }

    function getSubCDNNTV() {
        return {
            CO: "Có", KHONG: "Không", KHONG_BIET: "Không biết",
            BENH: "Bệnh", BI_TAN_CONG: "Bị tấn công", KHONG_THE_XAC_DINH: "Không thể xác định",
            TAI_NAN: "Tai nạn", LIEN_QUAN_PHAP_LUAT: "Liên quan đến pháp luật...", CHO_DIEU_TRA: "Chờ điều tra",
            CO_Y_HAI_BAN_THAN: "Cố ý hại bản thân", CHIEN_TRANH: "Chiến tranh",
            TAI_NHA: "Tại nhà", KHU_DAN_CU: "Khu dân cư", TRUONG_HOC_KHU_KHAC: "Trường học, khu hành chính khác",
            KHU_THE_THAO: "Khu thể thao", TREN_DUONG_DI: "Trên đường đi",
            KHU_THUONG_MAI_DICH_VU: "Khu thương mại và dịch vụ", KHU_CONG_NGHIEP: "Khu công nghiệp",
            NONG_TRAI: "Nông trại", DIA_DIEM_KHAC: "Địa điểm khác",
            TAI_THOI_DIEM_TU_VONG: "Tại thời điểm tử vong", TRONG_42_NGAY: "Trong vòng 42 ngày trước khi tử vong",
            TU_43_NGAY_DEN_1_NAM: "Từ 43 ngày đến 1 năm trước khi chết",
        }
    }

    function viewPhieuChuanDoanNguyenNhanTuVong(idPhieu, sign, dataKySo, sufKey) {
        let params = {
            ID_PHIEU: idPhieu
        }
        let url = 'cmu_in_rp_phieu_chuan_doan_nguyen_nhan_tu_vong?type=pdf&' + $.param(params);
        if (sign) {
            if (dataKySo && dataKySo.length > 0) {
                let keyMinio = getCMUFileSigned769GetLinkV2(dataKySo[0].KEYMINIO, 'pdf');
                url = keyMinio !== '-1' ? keyMinio : url;
            }
            previewAndSignPdfDefaultModal({url: url, idButton: "kySo" + sufKey + "CDNNTV",}, function(){});
        }
        else {
            if (dataKySo && dataKySo.length > 0) { getCMUFileSigned769(dataKySo[0].KEYMINIO,"pdf"); }
            else { previewPdfDefaultModal(url, "framePhieuChuanDoanNguyenNhanTuVong"); }
        }
    }

    function xuLyValidateNotWorking(validateList) {
        $.each(validateList, function (_, v) {
            let className = v.className, cusMsg = v.cusMsg, type = v.type;
            let inputTag = $("." + className + " " + type),
                labelTag = $("." + className + " label"),
                errorTag = $("." + className + " .formio-errors");
            if (inputTag.val().length === 0 && errorTag.contents().length === 0
                && errorTag.next('.error-msg-cdnntv').length === 0) {
                inputTag.css({"border-color": "#dc3545"}); labelTag.css({"color": "#721c24"});
                errorTag.after('<div class="error-msg-cdnntv" style="display: block; color: #c20000; font-size: 80%; margin-top: .25rem">' + cusMsg + '</div>');
            }
            else if (inputTag.val().length > 0) {
                inputTag.removeAttr("style"); labelTag.css({"color": "#222222"});
                errorTag.next('.error-msg-cdnntv').remove();
            }
            inputTag.on("change", function () {
                if (inputTag.val().length > 0) {
                    inputTag.removeAttr("style"); labelTag.css({"color": "#222222"});
                    errorTag.next('.error-msg-cdnntv').remove();
                }
            });
        })
    }

    phieuChuanDoanNguyenNhanTuVongJS.reloadDSPhieuChuanDoanNguyenNhanTuVong = function () {
        $("#phieuChuanDoanNguyenNhanTuVongGrid").jqGrid('clearGridData');
        let url = 'cmu_getlist?url=' + convertArray([
            singletonObject.dvtt,
            thongtinhsba.thongtinbn.SOVAOVIEN,
            'CMU_CD_NGUYEN_NHAN_TU_VONG_LST'
        ]);
        $.get(url).done(function(data){
            if (data && data.length > 0) {
                let dataGanNhat = data[data.length - 1]
                let formData = JSON.parse(dataGanNhat.DATA_PHIEU);
                let khoaTao = singletonObject.danhsachphongban.find(
                    (item) => item.MAKHOA === dataGanNhat.KHOA_TAO);
                $("#dataPhieuChuanDoanNguyenNhanTuVong").html(
                    khoaTao.TENKHOA + " - " + moment(dataGanNhat.NGAYTAO, ["DD/MM/YYYY HH:mm:ss"]).format("DD/MM/YYYY")
                );
                formPhieuChuanDoanNguyenNhanTuVongMoiNhat = {
                    ...dataGanNhat, DATA_PHIEU: formData
                };
                if (dataGanNhat.KEYSIGN_1 || dataGanNhat.KEYSIGN_2){
                    $("#iconSuaPhieuChuanDoanNguyenNhanTuVong").hide();
                    $("#iconXoaPhieuChuanDoanNguyenNhanTuVong").hide();
                }
                else {
                    $("#xuLyIconPhieuChuanDoanNguyenNhanTuVong").css('visibility', 'unset');
                    $("#iconXemPhieuChuanDoanNguyenNhanTuVong").show();
                    $("#iconSuaPhieuChuanDoanNguyenNhanTuVong").show();
                    $("#iconXoaPhieuChuanDoanNguyenNhanTuVong").show();
                }
            } else  {
                $("#dataPhieuChuanDoanNguyenNhanTuVong").html("Không có dữ liệu");
                $("#xuLyIconPhieuChuanDoanNguyenNhanTuVong").css('visibility', 'hidden');
            }
            $("#phieuChuanDoanNguyenNhanTuVongGrid").jqGrid('setGridParam', {
                datatype: 'local',
                data: data.map((item) => {
                    let parseData = JSON.parse(item.DATA_PHIEU);
                    return { ...item, DATA_PARSED: parseData }
                })
            }).trigger("reloadGrid");
        });
    }

    $(document).on("ctrlCollapInPCDNNTV", function (_) {
        $.each([
            "formio-js-collap-tt-yte"
        ], function(_, value) {
            $("." + value + " .card-header").on('click', function() {
                let isExpanded = $(this).attr('aria-expanded') === 'true';
                if (!isExpanded) { dinhKemSuKienPhieuChuanDoanNguyenNhanTuVong(); }
            });
        });
    });

    $(document).on('click', '#kySoNguoiLapPhieuCDNNTV', function() {
        kySoChung({
            dvtt: singletonObject.dvtt,
            userId: singletonObject.userId,
            url: $('#iframePreviewAndSign').attr('src'),
            loaiGiay: "PHIEU_CD_NGUYEN_NHAN_TU_VONG_NGUOI_LAP_PHIEU",
            maBenhNhan: thongtinhsba.thongtinbn.MA_BENH_NHAN,
            soBenhAn: thongtinhsba.thongtinbn.STT_BENHAN,
            soPhieuDichVu: formPhieuChuanDoanNguyenNhanTuVongTruocChinhSua.ID,
            soVaoVien: thongtinhsba.thongtinbn.SOVAOVIEN,
            soVaoVienDT: thongtinhsba.thongtinbn.SOVAOVIEN_DT,
            keyword: "SIGNATURE_1",
            fileName: LOGHSBALOAI.CHUANDOANNGUYENNHANTUVONG.VALUE + ": " +
                thongtinhsba.thongtinbn.TEN_BENH_NHAN + " - Mã phiếu: " +
                formPhieuChuanDoanNguyenNhanTuVongTruocChinhSua.ID,
        }, function(_) {
            $("#modalPreviewAndSignPDF").modal("hide");
            phieuChuanDoanNguyenNhanTuVongJS.reloadDSPhieuChuanDoanNguyenNhanTuVong();
        });
    });

    $(document).on('click', '#kySoThuTruongCDNNTV', function() {
        kySoChung({
            dvtt: singletonObject.dvtt,
            userId: singletonObject.userId,
            url: $('#iframePreviewAndSign').attr('src'),
            loaiGiay: "PHIEU_CD_NGUYEN_NHAN_TU_VONG_THU_TRUONG_BAO_TU",
            maBenhNhan: thongtinhsba.thongtinbn.MA_BENH_NHAN,
            soBenhAn: thongtinhsba.thongtinbn.STT_BENHAN,
            soPhieuDichVu: formPhieuChuanDoanNguyenNhanTuVongTruocChinhSua.ID,
            soVaoVien: thongtinhsba.thongtinbn.SOVAOVIEN,
            soVaoVienDT: thongtinhsba.thongtinbn.SOVAOVIEN_DT,
            keyword: "SIGNATURE_2",
            fileName: LOGHSBALOAI.CHUANDOANNGUYENNHANTUVONG.VALUE + ": " +
                thongtinhsba.thongtinbn.TEN_BENH_NHAN + " - Mã phiếu: " +
                formPhieuChuanDoanNguyenNhanTuVongTruocChinhSua.ID,
        }, function(_) {
            $("#modalPreviewAndSignPDF").modal("hide");
            phieuChuanDoanNguyenNhanTuVongJS.reloadDSPhieuChuanDoanNguyenNhanTuVong();
        });
    });

    // Bổ sung Mẫu chuẩn bị v2
    $("#mauChuanBiPhieuChuanDoanNguyenNhanTuVong").click(function() {
        let element = $("#mau_danhsachmaujson_wrap");
        element.attr("function-add", 'insertMauCBPhieuChuanDoanNguyenNhanTuVong');
        element.attr("function-chinhsua", 'editMauCBPhieuChuanDoanNguyenNhanTuVong');
        element.attr("function-select", 'selectMauCBPhieuChuanDoanNguyenNhanTuVong');
        element.attr("function-getdata", 'getdataMauCBPhieuChuanDoanNguyenNhanTuVong');
        element.attr("function-validate", 'formioCBPhieuChuanDoanNguyenNhanTuVongValidate');
        element.attr("data-key", 'MAUCBPHIEUCHUANDOANNGUYENNHANTUVONG');
        $("#modalMauChungJSON").modal("show");
        $.loadDanhSachMauChungJSON('MAUCBPHIEUCHUANDOANNGUYENNHANTUVONG')
    }); $.extend({
        insertMauCBPhieuChuanDoanNguyenNhanTuVong: function () {
            generateFormMauCBPhieuChuanDoanNguyenNhanTuVong({});
        },
        editMauCBPhieuChuanDoanNguyenNhanTuVong: function (rowSelect) {
            let json = JSON.parse(rowSelect.NOIDUNG);
            let dataMau = {}
            json.forEach(function(item) {
                dataMau[item.key] = item.value
            });
            generateFormMauCBPhieuChuanDoanNguyenNhanTuVong({
                ID: rowSelect.ID,
                TENMAU: rowSelect.TENMAU,
                ...dataMau
            });
        },
        selectMauCBPhieuChuanDoanNguyenNhanTuVong: function (rowSelect) {
            let json = JSON.parse(rowSelect.NOIDUNG);
            json.forEach(function(item) {
                $('#phieuChuanDoanNguyenNhanTuVong [name="data[' + item.key + ']"]').val(item.value)
                formPhieuChuanDoanNguyenNhanTuVong.data[item.key] = item.value
            });
            dinhKemSuKienPhieuChuanDoanNguyenNhanTuVong();
            $("#modalMauChungJSON").modal("hide");
        },
        getdataMauCBPhieuChuanDoanNguyenNhanTuVong: function () {
            let objectNoidung = [];
            getObjectMauCBPhieuChuanDoanNguyenNhanTuVong().forEach(function(item) {
                console.log(item)
                if (item.key !== 'ID' && item.key !== 'TENMAU' &&
                    item.label !== 'Columns' && item.label !== 'Titles'
                ) {
                    objectNoidung.push({
                        "label": item.label,
                        "value": formioMauHSBA.submission.data[item.key],
                        "key": item.key,
                    });
                }
                else if (item.label === 'Columns') {
                    item.columns.forEach(function (v) {
                        objectNoidung.push({
                            "label": v.components[0].label,
                            "value": formioMauHSBA.submission.data[v.components[0].key],
                            "key": v.components[0].key,
                        });
                    });
                }
            })
            return {
                ID: formioMauHSBA.submission.data.ID,
                TENMAU: formioMauHSBA.submission.data.TENMAU,
                NOIDUNG: JSON.stringify(objectNoidung),
                KEYMAUCHUNG: 'MAUCBPHIEUCHUANDOANNGUYENNHANTUVONG'
            };
        },
        formioCBPhieuChuanDoanNguyenNhanTuVongValidate: function() {
            formioMauHSBA.emit("checkValidity");
            return formioMauHSBA.checkValidity(null, false, null, true);
        },
    });

    function generateFormMauCBPhieuChuanDoanNguyenNhanTuVong(dataForm) {
        let jsonForm = getJSONObjectForm(getObjectMauCBPhieuChuanDoanNguyenNhanTuVong());
        Formio.createForm(document.getElementById('formChiTietMauChungJSON'),
            jsonForm, {}
        ).then(function(form) {
            formioMauHSBA = form;
            formioMauHSBA.submission = { data: { ...dataForm }}
        });
    }

    function getObjectMauCBPhieuChuanDoanNguyenNhanTuVong() {
        return [
            {
                "label": "ID",
                "key": "ID",
                "type": "textfield",
                others: {
                    hidden: true
                }
            },
            {
                "label": "Tên mẫu",
                "key": "TENMAU",
                "type": "textarea",
                validate: {
                    required: true
                },
                others: {
                    "labelPosition": "left-left",
                    "labelWidth": 10
                }
            },
            {
                "label": "Titles",
                "type": "htmlelement",
                "tag": "p",
                "content": "<strong>Phần A: Thông tin về Y tế</strong>",
                "input": false
            },
            {
                "label": "Titles",
                "type": "htmlelement",
                "tag": "p",
                "content": "<strong>Mục 1</strong>",
                "input": false
            },
            {
                "label": "Columns",
                "columns": [
                    {
                        "components": [
                            {
                                "label": "Chuẩn đoán nguyên nhân tử vong (a)",
                                "customClass": "mr-2",
                                "key": "CHUAN_DOAN_NGUYEN_NHAN_TU_VONG_A",
                                "type": "textfield"
                            }
                        ],
                        "width": 7, "size": "md", "currentWidth": 7
                    },
                    {
                        "components": [
                            {
                                "label": "Khoảng thời gian ước tính (a)",
                                "key": "KHOANG_THOI_GIAN_UOC_TINH_A",
                                "type": "textfield"
                            }
                        ],
                        "width": 5, "size": "md", "currentWidth": 5
                    }
                ],
                "key": "columns",
                "type": "columns",
                "customClass": "ml-0 mr-0"
            },
            {
                "label": "Columns",
                "columns": [
                    {
                        "components": [
                            {
                                "label": "Chuẩn đoán nguyên nhân tử vong (b)",
                                "customClass": "mr-2",
                                "key": "CHUAN_DOAN_NGUYEN_NHAN_TU_VONG_B",
                                "type": "textfield"
                            }
                        ],
                        "width": 7, "size": "md", "currentWidth": 7
                    },
                    {
                        "components": [
                            {
                                "label": "Khoảng thời gian ước tính (b)",
                                "key": "KHOANG_THOI_GIAN_UOC_TINH_B",
                                "type": "textfield"
                            }
                        ],
                        "width": 5, "size": "md", "currentWidth": 5
                    }
                ],
                "key": "columns",
                "type": "columns",
                "customClass": "ml-0 mr-0"
            },
            {
                "label": "Columns",
                "columns": [
                    {
                        "components": [
                            {
                                "label": "Chuẩn đoán nguyên nhân tử vong (c)",
                                "customClass": "mr-2",
                                "key": "CHUAN_DOAN_NGUYEN_NHAN_TU_VONG_C",
                                "type": "textfield"
                            }
                        ],
                        "width": 7, "size": "md", "currentWidth": 7
                    },
                    {
                        "components": [
                            {
                                "label": "Khoảng thời gian ước tính (c)",
                                "key": "KHOANG_THOI_GIAN_UOC_TINH_C",
                                "type": "textfield"
                            }
                        ],
                        "width": 5, "size": "md", "currentWidth": 5
                    }
                ],
                "key": "columns",
                "type": "columns",
                "customClass": "ml-0 mr-0"
            },
            {
                "label": "Columns",
                "columns": [
                    {
                        "components": [
                            {
                                "label": "Chuẩn đoán nguyên nhân tử vong (d)",
                                "customClass": "mr-2",
                                "key": "CHUAN_DOAN_NGUYEN_NHAN_TU_VONG_D",
                                "type": "textfield"
                            }
                        ],
                        "width": 7, "size": "md", "currentWidth": 7
                    },
                    {
                        "components": [
                            {
                                "label": "Khoảng thời gian ước tính (d)",
                                "key": "KHOANG_THOI_GIAN_UOC_TINH_D",
                                "type": "textfield"
                            }
                        ],
                        "width": 5, "size": "md", "currentWidth": 5
                    }
                ],
                "key": "columns",
                "type": "columns",
                "customClass": "ml-0 mr-0"
            },
            {
                "label": "Titles",
                "type": "htmlelement",
                "tag": "p",
                "content": "<strong>Mục 2</strong>",
                "input": false
            },
            {
                "label": "Columns",
                "columns": [
                    {
                        "components": [
                            {
                                "label": "Chuẩn đoán nguyên nhân tử vong",
                                "customClass": "mr-2",
                                "key": "CHUAN_DOAN_NGUYEN_NHAN_TU_VONG",
                                "type": "textfield"
                            }
                        ],
                        "width": 7, "size": "md", "currentWidth": 7
                    },
                    {
                        "components": [
                            {
                                "label": "Khoảng thời gian ước tính",
                                "key": "KHOANG_THOI_GIAN_UOC_TINH",
                                "type": "textfield"
                            }
                        ],
                        "width": 5, "size": "md", "currentWidth": 5
                    }
                ],
                "key": "columns",
                "type": "columns",
                "customClass": "ml-0 mr-0"
            },
            {
                "label": "Titles",
                "type": "htmlelement",
                "tag": "p",
                "content": "<strong>Phần B: Các thông tin y tế khác</strong>",
                "input": false
            },
            {
                "label": "Nếu là chết chu sinh, xin vui lòng cho biết tình trạng của người mẹ có ảnh hưởng đến thai nhi và trẻ sơ sinh",
                "key": "TINH_TRANG_ME_ANH_HUONG_THAI",
                "type": "textarea"
            },
            {
                "label": "Columns",
                "columns": [
                    {
                        "components": [
                            {
                                "label": "Kết luận: Chẩn đoán nguyên nhân chính gây tử vong",
                                "customClass": "mr-2",
                                "key": "CHUAN_DOAN_NGUYEN_NHAN_CHINH",
                                "type": "textfield"
                            }
                        ],
                        "width": 9, "size": "md", "currentWidth": 9
                    },
                    {
                        "components": [
                            {
                                "label": "Mã TCD 10",
                                "key": "MA_TCD_10",
                                "type": "textfield"
                            }
                        ],
                        "width": 3, "size": "md", "currentWidth": 3
                    }
                ],
                "key": "columns",
                "type": "columns",
                "customClass": "ml-0 mr-0"
            },
        ];
    }
});

// formio-css-textarea formio-js-scale-textarea
