create or replace FUNCTION                              CMU_PHIEU_CBI_TPHAU_UPDATE(
    p_id                        IN NUMBER,
    p_dvtt                      IN VARCHAR2,
--    p_so_vao_vien               IN NUMBER,
--    p_ma_benh_<PERSON>han              IN NUMBER,
    p_doi_tuong                    IN VARCHAR2,
    p_thoi_gian_mo                 IN VARCHAR2,
    p_tom_tat_benh_an                IN VARCHAR2,
    p_chan_doan                  IN VARCHAR2,
    p_cach_mo          IN VARCHAR2,
    p_bac_si              IN NUMBER,
    p_phu_mo                IN NUMBER,
    p_tong_trang                IN VARCHAR2,
    p_ha                IN VARCHAR2,
    p_mach           IN VARCHAR2,
    p_can_nang                  IN NUMBER,
    p_tinh_trang_tim                    IN VARCHAR2,
    p_tinh_trang_phoi                 IN VARCHAR2,
    p_hc                IN VARCHAR2,
    p_mc                  IN VARCHAR2,
    p_hbsag          IN VARCHAR2,
    p_mac<PERSON>an                    IN VARCHAR2,
    p_bc                 IN VARCHAR2,
    p_bun                IN VARCHAR2,
    p_duong_huyet                  IN VARCHAR2,
    p_sgot          IN VARCHAR2,
    p_dthc                    IN VARCHAR2,
    p_creatinine                 IN VARCHAR2,
    p_protid                IN VARCHAR2,
    p_sgpt                  IN VARCHAR2,
    p_md          IN VARCHAR2,
    p_hiv                    IN VARCHAR2,
    p_gros                 IN VARCHAR2,
    p_khac                IN VARCHAR2,
    p_nuoc_tieu                  IN VARCHAR2,
    p_dam          IN VARCHAR2,
    p_khac_2                    IN VARCHAR2,
    p_benh_san                 IN VARCHAR2,
    p_mau_du_tru                IN VARCHAR2,
    p_mau                  IN VARCHAR2,
    p_nhom          IN VARCHAR2,
    p_rh                    IN VARCHAR2,
    p_x_quang_phoi                 IN VARCHAR2,
    p_x_quang_tieu_hoa                IN VARCHAR2,
    p_x_quang_xuong                  IN VARCHAR2,
    p_x_quang_bo_nieu          IN VARCHAR2,
    p_soi_bang_quang                  IN VARCHAR2,
    p_soi_truc_trang          IN VARCHAR2,
    p_cac_dien_do                    IN VARCHAR2,
    p_the_nam                 IN VARCHAR2,
    p_treo                IN VARCHAR2,
    p_noi_rua                  IN VARCHAR2,
    p_bao_tay          IN VARCHAR2,
    p_bao_chan                IN VARCHAR2,
    p_ban_xuong                  IN NUMBER,
    p_dot          IN NUMBER,
    p_hut                  IN NUMBER,
    p_bot          IN NUMBER,
    p_noi_that_mach                    IN VARCHAR2,
    p_esmarch                 IN VARCHAR2,
    p_dung_cu                IN VARCHAR2,
    p_dung_cu_dac_biet                  IN VARCHAR2,
    p_x_quang_phong_mo          IN NUMBER,
    p_bac_si_dieu_tri          IN NUMBER,
    p_de_nghi          IN NUMBER
)
RETURN NUMBER IS
--     v_thoi_gian_mo   DATE := TO_DATE(p_thoi_gian_mo, 'dd/mm/yyyy hh24:mi:ss');
--         v_ngay_tao_phieu   DATE := TO_DATE(p_ngay_tao_phieu, 'dd/mm/yyyy');
BEGIN
UPDATE CMU_PHIEUCHUANBITIENPHAU
SET
    DOI_TUONG = p_doi_tuong,
    THOI_GIAN_MO = p_thoi_gian_mo,
    TOM_TAT_BENH_AN = p_tom_tat_benh_an,
    CHAN_DOAN = p_chan_doan,
    CACH_MO = p_cach_mo,
    BAC_SI = p_bac_si,
    PHU_MO = p_phu_mo,
    TONG_TRANG = p_tong_trang,
    HA = p_ha,
    MACH = p_mach,
    CAN_NANG = p_can_nang,
    TINH_TRANG_TIM = p_tinh_trang_tim,
    TINH_TRANG_PHOI = p_tinh_trang_phoi,
    HC = p_hc,
    MC = p_mc,
    HBSAG = p_hbsag,
    MACLAGAN = p_maclagan,
    BC = p_bc,
    BUN = p_bun,
    DUONG_HUYET = p_duong_huyet,
    SGOT = p_sgot,
    DTHC = p_dthc,
    CREATININE = p_creatinine,
    PROTID = p_protid,
    SGPT = p_sgpt,
    MD = p_md,
    HIV = p_hiv,
    GROS = p_gros,
    KHAC = p_khac,
    NUOC_TIEU = p_nuoc_tieu,
    DAM = p_dam,
    KHAC_2 = p_khac_2,
    BENH_SAN = p_benh_san,
    MAU_DU_TRU = p_mau_du_tru,
    MAU = p_mau,
    NHOM = p_nhom,
    RH = p_rh,
    X_QUANG_PHOI = p_x_quang_phoi,
    X_QUANG_TIEU_HOA = p_x_quang_tieu_hoa,
    X_QUANG_XUONG = p_x_quang_xuong,
    X_QUANG_BO_NIEU = p_x_quang_bo_nieu,
    SOI_BANG_QUANG = p_soi_bang_quang,
    SOI_TRUC_TRANG = p_soi_truc_trang,
    CAC_DIEN_DO = p_cac_dien_do,
    THE_NAM = p_the_nam,
    TREO = p_treo,
    NOI_RUA = p_noi_rua,
    BAO_TAY = p_bao_tay,
    BAO_CHAN = p_bao_chan,
    BAN_XUONG = p_ban_xuong,
    DOT = p_dot,
    HUT = p_hut,
    BOT = p_bot,
    NOI_THAT_MACH = p_noi_that_mach,
    ESMARCH = p_esmarch,
    DUNG_CU = p_dung_cu,
    DUNG_CU_DAC_BIET = p_dung_cu_dac_biet,
    X_QUANG_PHONG_MO = p_x_quang_phong_mo,
    BAC_SI_DIEU_TRI = p_bac_si_dieu_tri,
    DE_NGHI = p_de_nghi

WHERE ID = p_id AND DVTT = p_dvtt;
RETURN 2;
END;