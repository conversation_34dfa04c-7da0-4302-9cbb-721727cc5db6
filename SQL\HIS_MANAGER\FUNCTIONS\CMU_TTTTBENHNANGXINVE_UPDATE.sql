create or replace FUNCTION      HIS_MANAGER.CMU_TTTTBENHNANGXINVE_UPDATE(
    p_id                        IN NUMBER,
    p_dvtt                      IN VARCHAR2,
    p_thongtin           IN VARCHAR2,
    p_nntuvong           IN VARCHAR2,
    p_ben<PERSON><PERSON>c           IN VARCHAR2,
    p_phauth<PERSON>t<PERSON>tuan           IN VARCHAR2,
    p_hinhthuctv           IN VARCHAR2,
    p_nnbenngoai           IN VARCHAR2,
    p_tuvongthainhi           IN VARCHAR2,
    p_phunu           IN VARCHAR2,
    p_ketluan           IN VARCHAR2,
    p_bacsi           IN VARCHAR2,
    p_truongkhoa           IN VARCHAR2,
    p_thutruong           IN VARCHAR2,
    p_ngaytao           IN VARCHAR2
)
RETURN NUMBER IS
    v_ngaytao   DATE := TO_DATE(p_ngaytao, 'dd/mm/yyyy');
BEGIN
UPDATE CMU_TTTTBENHNANGXINVE
SET
    THONGTINBENHJSON = p_thong<PERSON>,
    NNTUVONGJSON = p_nntuvong,
    BENHKHACJSON = p_benhkhac,
    PHAUTHUAT4TUANJSON = p_phauthuat4tuan,
    HINHTHUCTV = p_hinhthuctv,
    NNBENNGOAIJSON = p_nnbenngoai,
    TUVONGTHAINHIJSON = p_tuvongthainhi,
    PHUNUJSON = p_phunu,
    KETLUANJSON = p_ketluan,
    BSDIEUTRI = p_bacsi,
    TRUONGKHOA = p_truongkhoa,
    THUTRUONG = p_thutruong,
    NGAY_TAO_PHIEU = v_ngaytao

WHERE
        ID = p_id AND DVTT = p_dvtt;
RETURN 2;
END;