function getBANGOAITRUCHUNGJSON () {
    var loaiBA = 'NGOAITRU_CHUNG'
    var form;
    var formTongket;
    let keyMauHSBANGOAITRU_CHUNG = "MAUHSBANGOAITRU_CHUNG";
    let keyMauHSBANGOAITRU_CHUNGTongket = "MAUHSBANGOAITRU_CHUNGTONGKET";

    return {
        script: {},
        scriptTongket: {},
        initObjectFormPage2: function() {
            return getJSONObjectForm([
                {
                    "collapsible": true,
                    "key": "p-lydovaovien",
                    "type": "panel",
                    "label": "Lý do vào viện",
                    "title": "BỆNH ÁN VÀ HỎI BỆNH",
                    "collapsed": false,
                    "input": false,
                    "tableView": false,
                    "customClass": "hsba-tabs-wrap",
                    "components": [
                        {
                            "label": "Lý do vào viện",
                            "key": "LYDOVAOVIEN",
                            "type": "textarea",
                            customClass: "pr-2",
                            rows: 2,
                            "validate": {
                                "required": true
                            }
                        },
                        {
                            "label": "<PERSON>u<PERSON> trình bệnh lý",
                            others: {
                                "tooltip": "<PERSON>u<PERSON> trình bệnh lý: (khởi phát, di<PERSON>n biến, chẩn đoán, điều trị tuyến dưới, v.v...)",
                            },
                            "key": "BENHSU",
                            "type": "textarea",
                            "validate": {
                                "minLength": 5,
                                "maxLength": 3000,
                                "required": true
                            }
                        },
                        {
                            "label": "Tiền sử bệnh bản thân",
                            "key": "TIENSUBANTHAN",
                            "type": "textarea",
                            "validate": {
                                "minLength": 5,
                                "maxLength": 3000,
                                "required": true
                            }
                        },
                        {
                            "label": "Tiền sử bệnh gia đình",
                            "key": "TIENSUGIADINH",
                            "type": "textarea",
                            "validate": {
                                "minLength": 5,
                                "maxLength": 3000,
                                "required": true
                            }
                        },
                    ]
                },
                {
                    "collapsible": true,
                    "key": "p-khambenh",
                    "type": "panel",
                    "label": "Khám bệnh",
                    "title": "KHÁM BỆNH",
                    "collapsed": false,
                    "input": false,
                    "tableView": false,
                    "customClass": "hsba-tabs-wrap",
                    "components": [
                        {
                            "label": "left",
                            "columns": [
                                {
                                    "components": [
                                        {
                                            "label": "Toàn thân",
                                            "key": "KHAMTOANTHAN",
                                            "type": "textarea",
                                            "rows": 2,
                                            "input": true,
                                            "validate": {
                                                "minLength": 5,
                                                "maxLength": 3000,
                                                "required": true
                                            }
                                        },
                                        {
                                            "label": "Các bộ phận",
                                            "key": "CACBOPHAN",
                                            "type": "textarea",
                                            "rows": 2,
                                            "input": true,
                                            "validate": {
                                                "minLength": 5,
                                                "maxLength": 3000,
                                            }
                                        },
                                        {
                                            "label": "Tóm tắt kết quả cận lâm sàng",
                                            "key": "TOMTATKETQUACLS",
                                            "type": "textarea",
                                            "rows": 2,
                                            "input": true,
                                            "validate": {
                                                "minLength": 5,
                                                "maxLength": 3000,
                                            }
                                        },
                                    ],
                                    "width": 8,
                                    "offset": 0,
                                    "push": 0,
                                    "pull": 0,
                                    "size": "md",
                                    "currentWidth": 8
                                },
                                {
                                    "components": [
                                        {
                                            "label": "Tabs",
                                            "components": [
                                                {
                                                    "label": "Chỉ số sinh tồn",
                                                    "key": "sinhhieu",
                                                    "components": [
                                                        {
                                                            "label": "chisocothe",
                                                            "columns": [
                                                                {
                                                                    "components": [
                                                                        {
                                                                            "label": "Mạch",
                                                                            "customClass": "pr-2",
                                                                            "validate": {
                                                                                "maxLength": 20,
                                                                                required: true
                                                                            },
                                                                            "key": "MACH",
                                                                            "type": "textarea",
                                                                        }
                                                                    ],
                                                                    "width": 4,
                                                                    "offset": 0,
                                                                    "push": 0,
                                                                    "pull": 0,
                                                                    "size": "md",
                                                                    "currentWidth": 4
                                                                },
                                                                {
                                                                    "components": [
                                                                        {
                                                                            "label": "Nhiệt độ",
                                                                            "customClass": "pr-2",
                                                                            "validate": {
                                                                                "min": 35,
                                                                                "max": 43,
                                                                                required: true
                                                                            },
                                                                            "key": "NHIETDO",
                                                                            "type": "number",
                                                                        }
                                                                    ],
                                                                    "width": 4,
                                                                    "offset": 0,
                                                                    "push": 0,
                                                                    "pull": 0,
                                                                    "size": "md",
                                                                    "currentWidth": 4
                                                                },
                                                                {
                                                                    "components": [
                                                                        {
                                                                            "label": "Nhịp thở",
                                                                            "customClass": "pr-2",
                                                                            "validate": {
                                                                                "maxLength": 20,
                                                                                required: true
                                                                            },
                                                                            "key": "NHIPTHO",
                                                                            "type": "textarea",
                                                                        }
                                                                    ],
                                                                    "width": 4,
                                                                    "offset": 0,
                                                                    "push": 0,
                                                                    "pull": 0,
                                                                    "size": "md",
                                                                    "currentWidth": 4
                                                                },

                                                            ],
                                                            "customClass": "ml-0 mr-0",
                                                            "key": "chisocothe",
                                                            "type": "columns",
                                                        },
                                                        {
                                                            "label": "chisocothe2",
                                                            "columns": [
                                                                {
                                                                    "components": [
                                                                        {
                                                                            "label": "Huyết áp trên",
                                                                            "customClass": "pr-2",
                                                                            "validate": {
                                                                                "maxLength": 20,
                                                                                required: true
                                                                            },
                                                                            "key": "HUYETAPTREN",
                                                                            "type": "textarea",
                                                                        }
                                                                    ],
                                                                    "width": 6,
                                                                    "offset": 0,
                                                                    "push": 0,
                                                                    "pull": 0,
                                                                    "size": "md",
                                                                    "currentWidth": 6
                                                                },
                                                                {
                                                                    "components": [
                                                                        {
                                                                            "label": "Huyết áp dưới",
                                                                            "customClass": "pr-2",
                                                                            "validate": {
                                                                                "maxLength": 20,
                                                                                required: true
                                                                            },
                                                                            "key": "HUYETAPDUOI",
                                                                            "type": "textarea",
                                                                        }
                                                                    ],
                                                                    "width": 6,
                                                                    "offset": 0,
                                                                    "push": 0,
                                                                    "pull": 0,
                                                                    "size": "md",
                                                                    "currentWidth": 6
                                                                },
                                                            ],
                                                            "customClass": "ml-0 mr-0",
                                                            "key": "chisocothe2",
                                                            "type": "columns",
                                                        },
                                                        {
                                                            "label": "chisocothe3",
                                                            "columns": [
                                                                {
                                                                    "components": [
                                                                        {
                                                                            "label": "Cân nặng(kg)",
                                                                            "customClass": "pr-2",
                                                                            "validate": {
                                                                                "min": 0,
                                                                                "max": 400,
                                                                                required: true
                                                                            },
                                                                            "key": "CANNANG",
                                                                            "type": "number",
                                                                        }
                                                                    ],
                                                                    "width": 4,
                                                                    "offset": 0,
                                                                    "push": 0,
                                                                    "pull": 0,
                                                                    "size": "md",
                                                                    "currentWidth": 4
                                                                },
                                                                {
                                                                    "components": [
                                                                        {
                                                                            "label": "Chiều cao(cm)",
                                                                            "customClass": "pr-2",
                                                                            "validate": {
                                                                                "min": 1,
                                                                                "max": 400,
                                                                                required: true
                                                                            },
                                                                            "key": "CHIEUCAO",
                                                                            "type": "number",
                                                                        }
                                                                    ],
                                                                    "width": 4,
                                                                    "offset": 0,
                                                                    "push": 0,
                                                                    "pull": 0,
                                                                    "size": "md",
                                                                    "currentWidth": 4
                                                                },
                                                                {
                                                                    "components": [
                                                                        {
                                                                            "label": "BMI",
                                                                            "key": "BMI",
                                                                            others: {
                                                                                "disabled": true,
                                                                                "attributes": {
                                                                                    "readonly": "true"
                                                                                },
                                                                            },
                                                                            "type": "number",
                                                                        }
                                                                    ],
                                                                    "size": "md",
                                                                    "width": 4,
                                                                    "offset": 0,
                                                                    "push": 0,
                                                                    "pull": 0,
                                                                    "currentWidth": 4
                                                                }

                                                            ],
                                                            "customClass": "ml-0 mr-0",
                                                            "key": "chisocothe3",
                                                            "type": "columns",
                                                        },

                                                    ]
                                                }
                                            ],
                                            "customClass": "hsba-tabs-wrap pl-3",
                                            "key": "tabs",
                                            "type": "tabs",
                                        },
                                    ],
                                    "width": 4,
                                    "offset": 0,
                                    "push": 0,
                                    "pull": 0,
                                    "size": "md",
                                    customClass: "pl-2",
                                    "currentWidth": 4
                                },

                            ],
                            "customClass": "ml-0 mr-0",
                            "key": "kb-column",
                            "type": "columns",
                            "input": false,
                            "tableView": false
                        },
                        {
                            "label": "Chẩn đoán ban đầu",
                            "key": "CHANDOAN_BANDAU",
                            "type": "textarea",
                            "rows": 2,
                            validate: {
                                required: true
                            }
                        },
                        {
                            "label": "Đã xử lý(thuốc, chăm sóc)",
                            "key": "DAXULY",
                            "type": "textarea",
                            "rows": 2,
                            validate: {
                                required: true
                            }
                        },
                        {
                            label: "",
                            key: "wrap_chandoanravien",
                            columns: [
                                {
                                    "components": [
                                        {
                                            "tag": "label",
                                            "content": "Chẩn đoán ra viện",
                                            "refreshOnChange": false,
                                            "key": "htmllabel_chandoan",
                                            "type": "htmlelement",
                                        },
                                    ],
                                    "width": 12,
                                    "size": "md",
                                },
                                {
                                    "components": [
                                        {
                                            "label": "",
                                            "key": "CHANDOAN_RAVIEN",
                                            "type": "textfield",
                                            customClass: "pr-2",
                                            others: {
                                                "placeholder": "ICD",
                                            }
                                        },
                                    ],
                                    "width": 2,
                                    "size": "md",
                                },
                                {
                                    "components": [
                                        {
                                            "label": "",
                                            "key": "TEN_CHANDOAN_RAVIEN",
                                            "type": "textfield",
                                            others: {
                                                "placeholder": "Tên bệnh",
                                            },
                                            validate: {
                                                required: true
                                            }
                                        },
                                    ],
                                    "width": 10,
                                    "size": "md",
                                },

                            ],
                            "customClass": "ml-0 mr-0",
                            "type": "columns",
                        },
                        {
                            "html": "<p>Giám đốc bệnh viện</p>",
                            "label": "Tên giám đốc bệnh viện",
                            "refreshOnChange": false,
                            "key": "GIAMDOCBENHVIENTT",
                            "type": "content",
                            "input": false,
                            "tableView": false
                        },
                        getBacsiAllKhoaFormio("MAKHOAGIAMDOC", "GIAMDOC_BV"),
                    ]
                },

                getObjectThoigianBacsilambenhanFormio()
            ])
        },
        initObjectFormPage3: function() {
            return getJSONObjectForm([
                {
                    "collapsible": true,
                    "key": "p-tongketdieutri",
                    "type": "panel",
                    "label": "TỔNG KẾT BỆNH ÁN",
                    "title": "TỔNG KẾT BỆNH ÁN",
                    "collapsed": false,
                    "input": false,
                    "tableView": false,
                    "customClass": "hsba-tabs-wrap",
                    components: [
                        {
                            "label": "Quá trình bệnh lý và diễn biến lâm sàng",
                            "key": "quaTrinhBenhLy",
                            "type": "textarea",
                            rows: 2,
                            validate: {
                                "minLength": 5,
                                "maxLength": 3000,
                                required: true,
                            }
                        },
                        {
                            "label": "Copy cận lâm sàng",
                            "customClass": "text-right form-control-sm line-height-1",
                            "key": "copytomtatcls",
                            "type": "button",
                            others: {
                                "leftIcon": "fa fa-ellipsis-v",
                                "action": "event",
                                "showValidations": false,
                                "event": "openmodalcopytomtatcls",
                                "type": "button",
                            }

                        },
                        {
                            "label": "Tóm tắt kết quả xét nghiệm cận lâm sàng có giá trị chẩn đoán",
                            "key": "tomTatKetQuaXNCLS",
                            "type": "textarea",
                            rows: 2,
                            validate: {
                                "minLength": 5,
                                "maxLength": 3000,
                                required: true,
                            }
                        },
                        {
                            label: "",
                            key: "wrap_benhchinh",
                            columns: [
                                {
                                    "components": [
                                        {
                                            "tag": "label",
                                            "content": "Bệnh chính",
                                            "refreshOnChange": false,
                                            "key": "htmllabel_benhchinh",
                                            "type": "htmlelement",
                                        },
                                    ],
                                    "width": 12,
                                    "size": "md",
                                },
                                {
                                    "components": [
                                        {
                                            "label": "",
                                            "key": "ICD_BENHCHINH",
                                            "type": "textfield",
                                            customClass: "pr-2",
                                            others: {
                                                "placeholder": "ICD",
                                            }
                                        },
                                    ],
                                    "width": 2,
                                    "size": "md",
                                },
                                {
                                    "components": [
                                        {
                                            "label": "",
                                            "key": "TEN_BENHCHINH",
                                            "type": "textfield",
                                            others: {
                                                "placeholder": "Tên bệnh chính",
                                            },
                                            validate: {
                                                required: true
                                            }
                                        },
                                    ],
                                    "width": 10,
                                    "size": "md",
                                },

                            ],
                            "customClass": "ml-0 mr-0",
                            "type": "columns",
                        },
                        {
                            label: "",
                            key: "wrap_benhphu",
                            columns: [
                                {
                                    "components": [
                                        {
                                            "tag": "label",
                                            "attrs": [
                                                {
                                                    "attr": "",
                                                    "value": ""
                                                }
                                            ],
                                            "content": "Bệnh phụ",
                                            "key": "htmllabel_benhphu",
                                            "type": "htmlelement",
                                        },
                                    ],
                                    "width": 12,
                                    "size": "md",
                                },
                                {
                                    "components": [
                                        {
                                            "label": "",
                                            "key": "ICD_BENHPHU",
                                            "type": "textfield",
                                            customClass: "pr-2",
                                            others: {
                                                "placeholder": "ICD",
                                            }
                                        },
                                    ],
                                    "width": 2,
                                    "size": "md",
                                },
                                {
                                    "components": [
                                        {
                                            "label": "",
                                            "key": "TENICD_BENHPHU",
                                            "type": "textfield",
                                            others: {
                                                "placeholder": "Tên bệnh",
                                            }
                                        },
                                    ],
                                    "width": 10,
                                    "size": "md",
                                },
                                {
                                    "components": [
                                        {
                                            "label": "",
                                            "key": "TEN_BENHPHU",
                                            "type": "textarea",
                                            "rows": 2,
                                            "input": true
                                        },
                                    ],
                                    "width": 12,
                                    "size": "md",
                                },
                            ],
                            "customClass": "ml-0 mr-0",
                            "type": "columns",
                        },
                        {
                            "label": "Phương pháp điều trị",
                            "key": "phuongPhapDieuTri",
                            "type": "textarea",
                            rows: 2,
                            validate: {
                                "minLength": 5,
                                "maxLength": 3000,
                                required: true,
                            }
                        },

                        {
                            "label": "Tình trạng người bệnh ra viện",
                            "key": "tinhTrangNguoiBenhRaVien",
                            "type": "textarea",
                            rows: 2,
                            validate: {
                                "minLength": 5,
                                "maxLength": 3000,
                                required: true,
                            }
                        },
                        {
                            "label": "Hướng điều trị và các chế độ tiếp theo",
                            "key": "huongDieuTriVaCacCheDo",
                            "type": "textarea",
                            rows: 2,
                            validate: {
                                "minLength": 5,
                                "maxLength": 3000,
                                required: true,
                            }
                        },
                    ]
                },
                getObjectThoigianTongketFormio()
            ]);
        },
        callbackAfterLoad: function (instance) {
            form = instance;
            var tenBenhchinhElement = form.getComponent('TEN_CHANDOAN_RAVIEN');
            var icdBenhchinhElement = form.getComponent('CHANDOAN_RAVIEN');
            var bacsilambenhanElement = form.getComponent('MABACSILAMBENHAN');
            var bmiElement = form.getComponent('BMI');
            var cannangElement = form.getComponent('CANNANG');
            var chieucaoElement = form.getComponent('CHIEUCAO');
            var giamdocElement = form.getComponent('GIAMDOC_BV');

            $("#"+getIdElmentFormio(form,'CHANDOAN_RAVIEN')).on('keypress', function(event) {
                var mabenhICD = $(this).val();
                if(event.keyCode == 13 && mabenhICD != "") {
                    mabenhICD = mabenhICD.toUpperCase();
                    getMotabenhly(mabenhICD, function(data) {
                        var splitIcd = data.split("!!!")
                        tenBenhchinhElement.setValue(splitIcd[1]);
                        icdBenhchinhElement.setValue(mabenhICD)
                    })
                }
            })
            combgridTenICD(getIdElmentFormio(form,'TEN_CHANDOAN_RAVIEN'), function(item) {
                icdBenhchinhElement.setValue(item.ICD);
                tenBenhchinhElement.setValue(item.MO_TA_BENH_LY);
            });

            $("#" + getIdElmentFormio(form, 'MAKHOAGIAMDOC')).change(function () {
                if (!$(this).val()) {
                    return;
                }
                getBacsiByKhoaFormio($(this).val(), giamdocElement)
            })

            $("#"+getIdElmentFormio(form,'MAKHOA')).change(function() {
                if(!$(this).val()) {
                    return;
                }
                getBacsiByKhoaFormio($(this).val(), bacsilambenhanElement)
            })

            $("#"+getIdElmentFormio(form,'CANNANG')).change(function() {
                if(!$(this).val() || !chieucaoElement.getValue()) {
                    return;
                }
                bmiElement.setValue((chieucaoElement.getValue()/Math.pow($(this).val()/100, 2)).toFixed(2))
            })

            $("#"+getIdElmentFormio(form,'CHIEUCAO')).change(function() {
                if(!$(this).val() || !cannangElement.getValue()) {
                    return;
                }
                bmiElement.setValue((cannangElement.getValue()/Math.pow($(this).val()/100, 2)).toFixed(2))
            })

            var idWrap = "hsba_vba_trang2-tab";
            showLoaderIntoWrapId(idWrap)
            getThongtinBenhan(thongtinhsba.thongtinbn.VOBENHAN[0].ID, loaiBA, function(dataTrang2) {
                console.log(dataTrang2)
                hideLoaderIntoWrapId(idWrap)
                delete dataTrang2.ID;
                dataTrang2.MAKHOAGIAMDOC = dataTrang2.MAKHOAGIAMDOC ? dataTrang2.MAKHOAGIAMDOC : singletonObject.makhoa;
                getBacsiByKhoaFormio(dataTrang2.MAKHOAGIAMDOC, giamdocElement);
                if (dataTrang2.MACH == null || dataTrang2.MACH == undefined || dataTrang2.MACH == "") {
                    var res = $.ajax({
                        url:"cmu_list_CMU_HSBA_GETDEF?url="+
                            convertArray([singletonObject.dvtt, thongtinhsba.thongtinbn.SOVAOVIEN, thongtinhsba.thongtinbn.SOVAOVIEN_DT]),
                        type:"GET",
                        async: false
                    }).responseText;

                    var dataDef = JSON.parse(res);
                    dataTrang2.MACH = dataDef[0].MACH;
                    dataTrang2.NHIETDO = dataDef[0].NHIETDO;
                    dataTrang2.NHIPTHO = dataDef[0].NHIPTHO;
                    dataTrang2.HUYETAPTREN = dataDef[0].HUYETAPTREN;
                    dataTrang2.HUYETAPDUOI = dataDef[0].HUYETAPDUOI;
                    dataTrang2.CANNANG = dataDef[0].CANNANG;
                    dataTrang2.CHIEUCAO = dataDef[0].CHIEUCAO;
                    if(!isNaN(dataTrang2.CANNANG) && !isNaN(dataTrang2.CHIEUCAO)) {
                        dataTrang2.BMI = (dataTrang2.CANNANG/Math.pow(dataTrang2.CHIEUCAO/100, 2)).toFixed(2);
                    }
                }
                dataTrang2.MAKHOA = dataTrang2.MAKHOA? dataTrang2.MAKHOA: singletonObject.makhoa;
                console.log(dataTrang2.MAKHOA)
                dataTrang2.GIAMDOC_BV = dataTrang2.GIAMDOC_BV ? dataTrang2.GIAMDOC_BV.split(" - ")[0] : "";
                dataTrang2.MABACSILAMBENHAN = dataTrang2.BACSY_KHAMBENH ? dataTrang2.BACSY_KHAMBENH.split(" - ")[0] : "";
                dataTrang2.NGAYBSLAMBENHAN =  (dataTrang2.NGAYBSLAMBENHAN? moment(dataTrang2.NGAYBSLAMBENHAN, "DD/MM/YYYY"): moment()).toISOString()
                getBacsiByKhoaFormio(dataTrang2.MAKHOA, bacsilambenhanElement);
                form.submission =  {
                    data: {
                        ...dataTrang2
                    }
                };
            }, function() {
                hideLoaderIntoWrapId(idWrap)
                notifiToClient("Red", "Lỗi load thông tin bệnh án")
            });
        },
        save: function(element) {
            var idButton = element.id;
            form.emit("checkValidity");
            if (!form.checkValidity(null, false, null, true)) {
                hideSelfLoading(idButton);
                return;
            }
            var dataSubmit = form.submission.data;
            delete dataSubmit.copyclstdt
            dataSubmit.ID = thongtinhsba.thongtinbn.VOBENHAN[0].ID;
            var ngayba = moment(dataSubmit.NGAYBSLAMBENHAN)
            dataSubmit.NGAYLAMBENHAN = "Ngày " + ngayba.format("DD") + " tháng " + ngayba.format("MM") + " năm " + ngayba.format("YYYY");
            dataSubmit.BACSILAMBENHAN = getTextSelectedFormio(form.getComponent('MABACSILAMBENHAN'));
            dataSubmit.GIAMDOCBENHVIEN = getTextSelectedFormio(form.getComponent('GIAMDOC_BV'));
            dataSubmit.MAKHOA = getTextSelectedFormio(form.getComponent('MAKHOA')).split(" - ")[0];
            var benhAnNgoaiTruChung = {
                lyDoNhapVien: dataSubmit.LYDOVAOVIEN,
                quaTrinhBenhLy: dataSubmit.BENHSU,
                banThan: dataSubmit.TIENSUBANTHAN,
                giaDinh: dataSubmit.TIENSUGIADINH,
                khamBenhToanThan: dataSubmit.KHAMTOANTHAN,
                Mach: dataSubmit.MACH,
                nhietDo: dataSubmit.NHIETDO,
                huyetApTren: dataSubmit.HUYETAPTREN,
                huyetApThap: dataSubmit.HUYETAPDUOI,
                nhipTho: dataSubmit.NHIPTHO,
                canNang: dataSubmit.CANNANG,
                chieuCao: dataSubmit.CHIEUCAO,
                Bmi: dataSubmit.BMI,
                cacBoPhan: dataSubmit.CACBOPHAN,
                tomTatKetQuaCanLamSang: dataSubmit.TOMTATKETQUACLS,
                chanDoanBanDau: dataSubmit.CHANDOAN_BANDAU,
                daXuLy: dataSubmit.DAXULY,
                maKhoaGiamDoc: dataSubmit.MAKHOAGIAMDOC,
                giamDocBenhVien: dataSubmit.GIAMDOCBENHVIEN,
                bacSyKhamBenh: dataSubmit.BACSILAMBENHAN,
                chanDoanRaVien: dataSubmit.CHANDOAN_RAVIEN,
                tenChanDoanRaVien: dataSubmit.TEN_CHANDOAN_RAVIEN,
            }
            var jsonData = {
                ID: dataSubmit.ID,
                ngayKhamBenh: ngayba.format("DD/MM/YYYY"),
                benhAnNgoaiTruChung: JSON.stringify(benhAnNgoaiTruChung)
            }
            $.post("update-benhan-ngoai-tru-chung", jsonData)
                .done(function (data) {
                    notifiToClient("Green", "Lưu thành công");
                    updateNgaylamVaBSHSBA({
                        ...dataSubmit,
                        NGAYBA: ngayba.format("DD/MM/YYYY"),
                    });
                    var Logbandau = []
                    var Logmoi = []
                    var newdata = {};
                    dataSubmit.NGAYBSLAMBENHAN = moment(dataSubmit.NGAYBSLAMBENHAN).format("MM/DD/YYYY")
                    assignNonNullValuesBA(newdata,dataSubmit);
                    var diffObject = findDifferencesBetweenObjects(oldDataTrang2, newdata);
                    for (let key in diffObject) {
                        if (keyLuuLogTrang2.hasOwnProperty(key)) {
                            Logbandau.push(getLabelValueBATrang2(key, oldDataTrang2))
                            Logmoi.push(getLabelValueBATrang2(key, newdata))
                        }
                    }
                    if (Logbandau.length != 0 || Logmoi.length != 0){
                        luuLogHSBATheoBN({
                            SOVAOVIEN: thongtinhsba.thongtinbn.SOVAOVIEN,
                            LOAI: LOGHSBALOAI.NGOAITRU_CHUNG.KEY,
                            NOIDUNGBANDAU: Logbandau.join(";"),
                            NOIDUNGMOI: Logmoi.join(";"),
                            USERID: singletonObject.userId,
                            ACTION: LOGHSBAACTION.EDIT.KEY,
                        })
                    }
                }).fail(function () {
                notifiToClient("Red", "Lỗi lưu thông tin")
            }).always(function () {
                hideSelfLoading(idButton);
            })
        },
        callbackAfterLoadTongket: function (instance) {
            formTongket = instance;
            var bacsiketthucBAElement = formTongket.getComponent('MABACSIDIEUTRI');
            var tenBenhchinhElement = formTongket.getComponent('TEN_BENHCHINH');
            var icdBenhchinhElement = formTongket.getComponent('ICD_BENHCHINH');
            var tenBenhphuElement = formTongket.getComponent('TENICD_BENHPHU');
            var icdBenhphuElement = formTongket.getComponent('ICD_BENHPHU');
            var textBenhphuElement = formTongket.getComponent('TEN_BENHPHU');
            var idWrap = "hsba_vba_trang3-tab";
            $("#"+getIdElmentFormio(formTongket,'MAKHOA_KETHUC')).change(function() {
                if(!$(this).val()) {
                    return;
                }
                getBacsiByKhoaFormio($(this).val(), bacsiketthucBAElement)
            })

            $("#" + getIdElmentFormio(formTongket, 'ICD_BENHCHINH')).on('keypress', function (event) {
                var mabenhICD = $(this).val();
                if (event.keyCode == 13 && mabenhICD != "") {
                    mabenhICD = mabenhICD.toUpperCase();
                    getMotabenhly(mabenhICD, function (data) {
                        var splitIcd = data.split("!!!")
                        tenBenhchinhElement.setValue(splitIcd[1]);
                        icdBenhchinhElement.setValue(mabenhICD)
                    })
                }
            })
            $("#"+getIdElmentFormio(formTongket,'ICD_BENHPHU')).on('keypress', function(event) {
                var mabenhICD = $(this).val();
                if(event.keyCode == 13 && mabenhICD != "") {
                    mabenhICD = mabenhICD.toUpperCase();
                    getMotabenhly(mabenhICD, function(data) {
                        var splitIcd = data.split("!!!")
                        tenBenhphuElement.setValue(splitIcd[1]);
                        tenBenhphuElement.focus()
                        icdBenhphuElement.setValue(mabenhICD)
                    })
                }
            })
            $("#"+getIdElmentFormio(formTongket,'TENICD_BENHPHU')).on('keypress', function(event) {
                if(event.keyCode == 13) {
                    var stringIcd = textBenhphuElement.getValue();
                    var mabenhICD = icdBenhphuElement.getValue()
                    if(!stringIcd.includes(mabenhICD)) {
                        textBenhphuElement.setValue( stringIcd + "; (" +mabenhICD.toUpperCase() + ") " + tenBenhphuElement.getValue());
                    }
                    icdBenhphuElement.setValue("")
                    tenBenhphuElement.setValue("")
                    icdBenhphuElement.focus()
                }
            })
            instance.on('openmodalcopytomtatcls', function(click) {
                addTextTitleModal("titleModalTomtatketCLSDieutri")
                $("#modalTomtatketCLSDieutri").modal("show");
                $(document).trigger("reloadDSTomtatCLS");
                $("#tomtatketCLSDieutriTabs").attr("data-function-copy", "copyTomtatKetquaCLSPage3")
            });
            bindAutocompleteDienBienBenh(formTongket, 'quaTrinhBenhLy')
            showLoaderIntoWrapId(idWrap)
            getThongtinTongket(thongtinhsba.thongtinbn.VOBENHAN[0].ID, loaiBA, function(dataTrang3) {
                hideLoaderIntoWrapId(idWrap)
                dataTrang3.MAKHOA_KETHUC = dataTrang3.MAKHOA_KETHUC? dataTrang3.MAKHOA_KETHUC: singletonObject.makhoa;
                let jsonBenhPhu = dataTrang3.BENHPHU_RAVIEN ? JSON.parse(dataTrang3.BENHPHU_RAVIEN) : {};
                formTongket.submission =  {
                    data: {
                        quaTrinhBenhLy: dataTrang3.QUATRINH_BENHLY,
                        ICD_BENHCHINH: dataTrang3.ICD_BENHCHINH ? $.trim(dataTrang3.ICD_BENHCHINH) : "",
                        TEN_BENHCHINH: dataTrang3.TEN_BENHCHINH,
                        ICD_BENHPHU: dataTrang3.ICD_BENHPHU ? $.trim(dataTrang3.ICD_BENHPHU) : "",
                        TEN_BENHPHU: jsonBenhPhu.tenIcdBenhPhu,
                        tomTatKetQuaXNCLS: dataTrang3.TOMTAT_KETQUA,
                        phuongPhapDieuTri: dataTrang3.PHUONGPHAP_DIEUTRI,
                        tinhTrangNguoiBenhRaVien: dataTrang3.TINHTRANG_RAVIEN,
                        huongDieuTriVaCacCheDo: dataTrang3.HUONG_DIEUTRI,
                        soToXQuang: dataTrang3.SOTO_XQUANG,
                        soToCTScanner: dataTrang3.SOTO_CTSCANNER,
                        soToSieuAm: dataTrang3.SOTO_SIEUAM,
                        soToXetNghiem: dataTrang3.SOTO_XETNGHIEM,
                        soToKhac: dataTrang3.SOTO_KHAC,
                        toanBoHoSo: dataTrang3.SOTO_TOANBOHS,
                        loaiGiayToKhac: dataTrang3.LOAI_GIAYTO_KHAC,
                        MAKHOA_KETHUC: dataTrang3.MAKHOA_KETHUC,
                        MABACSIDIEUTRI: dataTrang3.MABACSIDIEUTRI,
                        NGAY_TONGKET: (dataTrang3.NGAY_TONGKET_DATETIME? moment(dataTrang3.NGAY_TONGKET_DATETIME, ['DD/MM/YYYY HH:mm']): moment()).toISOString(),
                    }
                };
                getBacsiByKhoaFormio(dataTrang3.MAKHOA_KETHUC, bacsiketthucBAElement);

            }, function() {
                hideLoaderIntoWrapId(idWrap)
                notifiToClient("Red", "Lỗi load thông tin bệnh án")
            });
        },
        saveTongket: function(element) {
            var idButton = element.id;
            formTongket.emit("checkValidity");
            if (!formTongket.checkValidity(null, false, null, true)) {
                hideSelfLoading(idButton);
                return;
            }
            var dataSubmit = formTongket.submission.data;

            let xuLyTachICD = function (tenBenh) {
                let icdDauTien = tenBenh.match(/\(([^)]+)\)/g);
                if (icdDauTien && icdDauTien.length > 0) {
                    let icd = icdDauTien[0].replace(/[()]/g, '');
                    let result = [];
                    let chars = icd.split('');
                    for (let i = 0; i < chars.length; i++) {
                        if (chars[i] === '.') {
                            if (result.length > 0) {
                                result[result.length - 1] += '.';
                            }
                        } else {
                            result.push(chars[i]);
                        }
                    }
                    while (result.length < 4) {
                        result.push('');
                    }
                    let [p1, p2, p3, p4] = result;
                    return { p1, p2, p3, p4 };
                }
                return { p1: '', p2: '', p3: '', p4: '' };
            };

            let kyTuICD = xuLyTachICD(dataSubmit.TEN_BENHPHU);

            var dataBenhChinh = {
                icdBenhChinh: dataSubmit.ICD_BENHCHINH,
                tenIcdBenhChinh: dataSubmit.TEN_BENHCHINH,
            }
            var dataBenhPhu = {
                icdBenhPhu: dataSubmit.ICD_BENHPHU,
                tenIcdBenhPhu: dataSubmit.TEN_BENHPHU,
                icdBenhPhu1: kyTuICD.p1,
                icdBenhPhu2: kyTuICD.p2,
                icdBenhPhu3: kyTuICD.p3,
                icdBenhPhu4: kyTuICD.p4,
            }
            dataSubmit.id = thongtinhsba.thongtinbn.VOBENHAN[0].ID;
            dataSubmit.chiTietThuThuatPhauThuat = "";
            dataSubmit.benhChinhRaVien = JSON.stringify(dataBenhChinh);
            dataSubmit.benhPhuRaVien = JSON.stringify(dataBenhPhu);
            dataSubmit.giaiPhauBenh = "";
            dataSubmit.thuThuatPhauThuat = "";
            dataSubmit.benh = "";
            var ngayba = moment(dataSubmit.NGAY_TONGKET)
            dataSubmit.ngayTongKet =  ngayba.format("DD/MM/YYYY HH:mm");
            dataSubmit.bacSiDieuTri =  getTextSelectedFormio(formTongket.getComponent('MABACSIDIEUTRI')).split(" - ")[1];
            dataSubmit.nguoiGiaoHoSo = getTextSelectedFormio(formTongket.getComponent('MANHANVIEN_GIAOHOSO'));
            dataSubmit.nguoiNhanHoSo = getTextSelectedFormio(formTongket.getComponent('MANHANVIEN_NHANHOSO'));
            $.ajax({
                url: "TongKetBenhAn_Update",
                type: 'POST',
                data: JSON.stringify(dataSubmit),
                contentType: 'application/json',
                success: function (data) {
                    if (data.SUCCESS == 1) {
                        notifiToClient("Green", "Lưu thành công")
                        var Logbandau = []
                        var Logmoi = []
                        var newdata = {};
                        var dataSubmitnew = formTongket.submission.data;

                        dataSubmitnew.BACSIDIEUTRI = dataSubmitnew.bacSiDieuTri;
                        dataSubmitnew.BENH = dataSubmitnew.benh;
                        dataSubmitnew.CHITIET_THUTHUAT_PHAUTHUAT = dataSubmitnew.chiTietThuThuatPhauThuat;
                        dataSubmitnew.GIAIPHAU_BENH = dataSubmitnew.giaiPhauBenh;
                        dataSubmitnew.HUONG_DIEUTRI = dataSubmitnew.huongDieuTriVaCacCheDo;
                        dataSubmitnew.ID = dataSubmitnew.id;
                        dataSubmitnew.LOAI_GIAYTO_KHAC = dataSubmitnew.loaiGiayToKhac;
                        var ngaytknew = moment(dataSubmitnew.NGAY_TONGKET)
                        dataSubmitnew.NGAY_TONGKET = "Ngày " + ngaytknew.format("DD") + " tháng " + ngaytknew.format("MM") + " năm " + ngaytknew.format("YYYY");
                        dataSubmitnew.NGUOIGIAO_HOSO = dataSubmitnew.nguoiGiaoHoSo;
                        dataSubmitnew.NGUOINHAN_HOSO = dataSubmitnew.nguoiNhanHoSo;
                        dataSubmitnew.PHUONGPHAP_DIEUTRI = dataSubmitnew.phuongPhapDieuTri;
                        dataSubmitnew.QUATRINH_BENHLY = dataSubmitnew.quaTrinhBenhLy;
                        dataSubmitnew.SOTO_CTSCANNER = dataSubmitnew.soToCTScanner;
                        dataSubmitnew.SOTO_KHAC = dataSubmitnew.soToKhac;
                        dataSubmitnew.SOTO_SIEUAM = dataSubmitnew.soToSieuAm;
                        dataSubmitnew.SOTO_XQUANG = dataSubmitnew.soToXQuang;
                        dataSubmitnew.SOTO_XETNGHIEM = dataSubmitnew.soToXetNghiem;
                        dataSubmitnew.THUTHUAT_PHAUTHUAT = dataSubmitnew.thuThuatPhauThuat;
                        dataSubmitnew.TINHTRANG_RAVIEN = dataSubmitnew.tinhTrangNguoiBenhRaVien;
                        dataSubmitnew.SOTO_TOANBOHS = dataSubmitnew.toanBoHoSo;
                        dataSubmitnew.TOMTAT_KETQUA = dataSubmitnew.tomTatKetQuaXNCLS;
                        // dataSubmitnew.NGAY_TONGKET_DATE = dataSubmitnew.ngayTongKet;
                        // dataSubmit.ngayTongKet =  ngayba.format("DD/MM/YYYY HH:mm");

                        assignNonNullValuesBA(newdata,dataSubmitnew);
                        var diffObject = findDifferencesBetweenObjects(oldDataTrang3, newdata);
                        console.log(diffObject)
                        for (let key in diffObject) {
                            if (keyLuuLogTrang3.hasOwnProperty(key)) {
                                Logbandau.push(getLabelValueBATrang3(key, oldDataTrang3))
                                Logmoi.push(getLabelValueBATrang3(key, newdata))
                            }
                        }
                        console.log(Logbandau)
                        console.log(Logmoi)
                        if (Logbandau.length != 0 || Logmoi.length != 0){
                            luuLogHSBATheoBN({
                                SOVAOVIEN: thongtinhsba.thongtinbn.SOVAOVIEN,
                                LOAI: LOGHSBALOAI.NGOAITRU_CHUNG.KEY,
                                NOIDUNGBANDAU: Logbandau.join(";"),
                                NOIDUNGMOI: Logmoi.join(";"),
                                USERID: singletonObject.userId,
                                ACTION: LOGHSBAACTION.EDIT.KEY,
                            })
                        }
                        updateThongtinPage3(dataSubmit, function (_) {});
                    } else {
                        notifiToClient("Red", "Lưu thông tin bệnh án không thành công")
                    }
                    hideSelfLoading(idButton);
                },
                error: function (error) {
                    notifiToClient("Red", "Lỗi lưu thông tin")
                    hideSelfLoading(idButton);
                }
            })

        },
        saveThongtinHC: function(element) {
            var idButton = element.id;
            showSelfLoading(idButton);
            var dataSubmit = convertDataFormToJson("formHsbatthcqlnb");
            updateQuanlynbvaChandoan(dataSubmit, function() {
                hideSelfLoading(idButton);
                notifiToClient("Green", "Lưu thành công")
            }, function() {
                hideSelfLoading(idButton);
                notifiToClient("Red", "Lỗi lưu thông tin")
            });
        },
        loadThongtinPage1: function() {
            var idWrap = "hsba_vba_trang1-tab";
            showLoaderIntoWrapId(idWrap)
            getThongtinPage1Benhan(thongtinhsba.thongtinbn.VOBENHAN[0].ID, function(response) {

                hideLoaderIntoWrapId(idWrap)
            }, function() {
                hideLoaderIntoWrapId(idWrap)
                notifiToClient("Red", "Lỗi load thông tin")
            });
        },
        copyChidinhCLS: function(cls) {
            console.log("form.submission", form.submission)
            form.submission = {
                data: {
                    ...form.submission.data,
                    CLS: cls
                }
            }
        },

        getInfoMauHSBA: function() {
            this.extendFunctionMau();
            return {
                keyMauHSBA: keyMauHSBANGOAITRU_CHUNG,
                insertMau: "insertMauHSBANGOAITRU_CHUNG",
                editMau: "editMauHSBANGOAITRU_CHUNG",
                selectMau: "selectMauHSBANGOAITRU_CHUNG",
                getdataMau: "getdataMauHSBANGOAITRU_CHUNG",
                formioValidate: "formioHSBANGOAITRU_CHUNGValidate",
            };
        },
        extendFunctionMau: function() {
            let self = this
            $.extend({
                insertMauHSBANGOAITRU_CHUNG: function () {
                    self.generateFormMauHSBA({})
                },
                editMauHSBANGOAITRU_CHUNG: function (rowSelect) {
                    let json = JSON.parse(rowSelect.NOIDUNG);
                    let dataMau = {}
                    json.forEach(function(item) {
                        dataMau[item.key] = item.value
                    })

                    self.generateFormMauHSBA({
                        ID: rowSelect.ID,
                        TENMAU: rowSelect.TENMAU,
                        ...dataMau
                    })
                },
                selectMauHSBANGOAITRU_CHUNG: function (rowSelect) {
                    let json = JSON.parse(rowSelect.NOIDUNG);
                    let dataMau = {
                        ...form.submission.data,
                    }
                    json.forEach(function(item) {
                        dataMau[item.key] = item.value
                    })
                    form.submission = {
                        data: {
                            ...dataMau
                        }
                    }
                    $("#modalMauChungJSON").modal("hide");
                },
                getdataMauHSBANGOAITRU_CHUNG: function () {
                    let objectNoidung = [];
                    self.getObjectMauHSBA().forEach(function(item) {
                        if(item.key !== 'ID' && item.key !== 'TENMAU') {
                            objectNoidung.push({
                                "label": item.label,
                                "value": formioMauHSBA.submission.data[item.key],
                                "key": item.key,
                            })
                        }
                    })
                    return {
                        ID: formioMauHSBA.submission.data.ID,
                        TENMAU: formioMauHSBA.submission.data.TENMAU,
                        NOIDUNG: JSON.stringify(objectNoidung),
                        KEYMAUCHUNG: keyMauHSBANGOAITRU_CHUNG
                    };
                },
                formioHSBANGOAITRU_CHUNGValidate: function() {
                    formioMauHSBA.emit("checkValidity");
                    return formioMauHSBA.checkValidity(null, false, null, true);
                }
            })
        },
        generateFormMauHSBA: function(dataForm) {
            let self = this;
            let jsonForm = getJSONObjectForm(self.getObjectMauHSBA());
            Formio.createForm(document.getElementById('formChiTietMauChungJSON'),
                jsonForm,{}
            ).then(function(form) {
                formioMauHSBA = form;
                formioMauHSBA.submission = {
                    data: {
                        ...dataForm
                    }
                }
            });
        },
        getObjectMauHSBA: function() {
            return getObjectMauPage2NGOAITRU_CHUNG();
        },

        getInfoMauHSBATongket: function() {
            this.extendFunctionMauTongket();
            return {
                keyMauHSBA: keyMauHSBANGOAITRU_CHUNGTongket,
                insertMau: "insertMauHSBANGOAITRU_CHUNGTongket",
                editMau: "editMauHSBANGOAITRU_CHUNGTongket",
                selectMau: "selectMauHSBANGOAITRU_CHUNGTongket",
                getdataMau: "getdataMauHSBANGOAITRU_CHUNGTongket",
                formioValidate: "formioHSBANGOAITRU_CHUNGTongketValidate",
            };
        },
        extendFunctionMauTongket: function() {
            let self = this
            $.extend({
                insertMauHSBANGOAITRU_CHUNGTongket: function () {
                    self.generateFormMauHSBATongket({})
                },
                editMauHSBANGOAITRU_CHUNGTongket: function (rowSelect) {
                    let json = JSON.parse(rowSelect.NOIDUNG);
                    let dataMau = {}
                    json.forEach(function(item) {
                        dataMau[item.key] = item.value
                    })

                    self.generateFormMauHSBATongket({
                        ID: rowSelect.ID,
                        TENMAU: rowSelect.TENMAU,
                        ...dataMau
                    })
                },
                selectMauHSBANGOAITRU_CHUNGTongket: function (rowSelect) {
                    let json = JSON.parse(rowSelect.NOIDUNG);
                    let dataMau = {
                        ...formTongket.submission.data,
                    }
                    json.forEach(function(item) {
                        dataMau[item.key] = item.value
                    })
                    formTongket.submission = {
                        data: {
                            ...dataMau
                        }
                    }
                    $("#modalMauChungJSON").modal("hide");
                },
                getdataMauHSBANGOAITRU_CHUNGTongket: function () {
                    let objectNoidung = [];
                    self.getObjectMauHSBATongket().forEach(function(item) {
                        if(item.key !== 'ID' && item.key !== 'TENMAU') {
                            objectNoidung.push({
                                "label": item.label,
                                "value": formioMauHSBATongket.submission.data[item.key],
                                "key": item.key,
                            })
                        }
                    })
                    return {
                        ID: formioMauHSBATongket.submission.data.ID,
                        TENMAU: formioMauHSBATongket.submission.data.TENMAU,
                        NOIDUNG: JSON.stringify(objectNoidung),
                        KEYMAUCHUNG: keyMauHSBANGOAITRU_CHUNGTongket
                    };
                },
                formioHSBANGOAITRU_CHUNGTongketValidate: function() {
                    formioMauHSBATongket.emit("checkValidity");
                    return formioMauHSBATongket.checkValidity(null, false, null, true);
                }
            })
        },
        generateFormMauHSBATongket: function(dataForm) {
            let self = this;
            let jsonForm = getJSONObjectForm(self.getObjectMauHSBATongket());
            Formio.createForm(document.getElementById('formChiTietMauChungJSON'),
                jsonForm,{}
            ).then(function(form) {
                formioMauHSBATongket = form;
                formioMauHSBATongket.submission = {
                    data: {
                        ...dataForm
                    }
                }
            });
        },
        getObjectMauHSBATongket: function() {
            return getObjectMauTongketNGOAITRURHM();
        }
    }
}