CREATE OR REPLACE FUNCTION "F_CMUKTRAXML3_VTYT" (
    p_dvtt       VARCHAR2,
    p_tungay     VARCHAR2,
    p_denngay    VARCHAR2,
    p_khoa       VARCHAR2,
    p_hinhthuc   VARCHAR2
) RETURN SYS_REFCURSOR IS

    v_tungay    DATE := TO_DATE(p_tungay, 'dd/mm/yyyy');
    v_denngay   DATE := TO_DATE(p_denngay, 'dd/mm/yyyy');
    cur         SYS_REFCURSOR;
BEGIN
    IF p_hinhthuc = 0 THEN --Nội trú
        OPEN cur FOR SELECT
                                TO_CHAR(NGAYGIO_RA,'DD/MM/YYYY HH24:MI') ngay_ra_vien,
                                ho_ten               tenben<PERSON>han,
                                kcb.ma_bn            maben<PERSON>han,
                                kt.ten_dich_vu
                                    || '('
                                    || kt.ma_vat_tu
                                    || ')' congkham,
                                kt.ma_bac_si,
                                nv.ten_nhanvien      bacsi,
                                chungchi_hanh<PERSON>he    chung<PERSON>,
                                kcb.sovaovien_noi sovaovien,
                                kt.don_gia,
                                pb.ten_phongban
                                    || ' ('
                                    || ma_khoa_cb
                                    || ')' makhoa,
                                quyetdinh_b3         quyetdinh,
                                so_cv_gui_bhxh_b3    socongvan,
                                mabaocao_byt_vattu   mabaocao,
                                phantram_bhxh_tt     tyle_tt,
                                TO_CHAR(to_date(ngay_yl_cv130,'YYYYMMDDHH24MI'), 'DD/MM/YYYY HH24:MI') ngayylenh,
                                so_luong             soluong
                     FROM
                                his_synchronization.b1_chitieutonghop_kcb            kcb
                                    JOIN his_synchronization.b3_chitieuchitiet_kythuatvattu   kt ON kcb.dvtt = kt.dvtt
                                    AND kcb.ma_bn = kt.ma_bn
                                    AND kt.sovaovien_noitru = kcb.sovaovien_noi
                                    AND kt.sovaovien_dt_noitru = kcb.sovaovien_dt_noi
                                    LEFT JOIN his_fw.dm_phongban                                   pb ON kt.ma_khoa = pb.ma_phongban
                                    JOIN his_fw.dm_nhanvien                                   nv ON kt.ma_bac_si = nv.ma_nhanvien
                     WHERE
                                kcb.dvtt = kt.dvtt
                       AND ngay_quyet_toan_bh BETWEEN v_tungay AND v_denngay
                       --AND TRANG_THAI_BN_TN != 7
                       AND loai_kythuat = 'noitru_toavattu'
                       AND ti_le_mien_giam_the > 0
                       AND kcb.dvtt = p_dvtt
                       AND ma_loaikcb != 1
                         AND ( p_khoa = '-1'
                               OR kcb.b_makhoahoantatkham = p_khoa )
                     ORDER BY
                                ngay_quyet_toan_bh,
                                nv.ten_nhanvien;

ELSIF p_hinhthuc = 1 THEN --Ngoại trú
        OPEN cur FOR SELECT
                               TO_CHAR(ngay_quyet_toan_bh, 'DD/MM/YYYY') ngay_ra_vien,
                               ho_ten              tenbenhnhan,
                               kcb.ma_bn           mabenhnhan,
                               kt.ten_dich_vu
                                   || '('
                                   || kt.ma_dichvu_kythuat_dmdc
                                   || ')' congkham,
                               kt.ma_bac_si,
                               nv.ten_nhanvien     bacsi,
                               chungchi_hanhnghe   chungchihanhnghe,
                               kt.sovaovien,
                               kt.don_gia,
                               kt.ma_khoa          makhoa
                     FROM
                               his_synchronization.b1_chitieutonghop_kcb            kcb
                                   JOIN his_synchronization.b3_chitieuchitiet_kythuatvattu   kt ON kcb.dvtt = kt.dvtt
                                   AND kcb.sovaovien = kt.sovaovien
                                   AND kcb.ma_bn = kt.ma_bn
                                   AND loai_kythuat IN (
                                                        'ngoaitru_toavattu',
                                                        'TOAVATTU'
                                       )
                                   --left join HIS_FW.DM_PHONGBAN pb on KT.MA_PHONG_BAN_INT = pb.MA_PHONGBAN
                                   JOIN his_fw.dm_nhanvien                                   nv ON kt.ma_bac_si = nv.ma_nhanvien
                     WHERE
                               kcb.dvtt = kt.dvtt
                       AND ngay_quyet_toan_bh BETWEEN v_tungay AND v_denngay
                       AND trang_thai_bn_tn != 7
                         AND trang_thai_bn_tn >= 3
                         AND ti_le_mien_giam_the > 0
                         AND kcb.dvtt = p_dvtt
                         AND ma_loaikcb = 1
                         AND ( p_khoa = '-1'
                               OR kcb.b_makhoahoantatkham = p_khoa )
                     ORDER BY
                               ngay_quyet_toan_bh,
                               nv.ten_nhanvien;

END IF;

RETURN cur;
END;