<%@page import="l2.ThamSoManager" %>
<%@taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>
<%@ taglib uri="http://www.springframework.org/tags/form" prefix="form"%>
<%@page contentType="text/html" pageEncoding="UTF-8"  %>
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8"/>
    <meta name="google-site-verification" content="u6uNEfD4cb3gidezi4r_6aI8Wb1E07-ufBeCQpvmlqQ" />
    <meta http-equiv="Cache-Control" content="no-store" />
    <title>Hệ thống chăm sóc sức khỏe</title>
    <link rel="icon" href="<c:url value="/resources/images/favicon.ico" />" type="image/x-icon"/>
    <link rel="shortcut icon" href="<c:url value="/resources/images/favicon.ico" />" type="image/x-icon"/>
    <link href="<c:url value="/resources/css/divheader.css" />" rel="stylesheet"/>
    <link href="<c:url value="/resources/css/style_new.css" />" rel="stylesheet"/>

    <link rel="stylesheet" href="<c:url value="/resources/css/jquery-ui-redmond.1.9.1.css" />" />
    <script src="<c:url value="/resources/js/jquery.min.1.8.3.js" />"></script>
    <script src="<c:url value="/resources/js/jquery-ui.1.9.1.js" />"></script>
    <link href="<c:url value="/resources/bootstrap-4.1.3/dist/css/bootstrap.min.css" />" rel="stylesheet"/>
    <link href="<c:url value="/resources/jqgrid/css/ui.jqgrid.css" />" rel="stylesheet"/>
    <script src="<c:url value="/resources/jqgrid/js/i18n/grid.locale-en.js" />"></script>
    <script src="<c:url value="/resources/jqgrid/js/jquery.jqGrid.src.js" />"></script>
    <script src="<c:url value="/resources/js/common_function.js" />"></script>
    <script src="<c:url value="/resources/js/jquery.inputmask.bundle.min.js" />"></script>
    <script src="<c:url value="/resources/blockUI/jquery.blockUI.js" />"></script>
    <script src="<c:url value="/resources/dialog/jquery.alerts.js" />"></script>
    <link href="<c:url value="/resources/dialog/jquery.alerts.1.css" />" rel="stylesheet"/>
    <script src="<c:url value="/resources/camau/popper.1.11.0.js" />"></script>
    <script src="<c:url value="/resources/camau/js/lodash.min.js" />"></script>
    <script src="<c:url value="/resources/camau/js/common.js?timestamp=${System.currentTimeMillis()}" />"></script>
    <link rel="stylesheet" href="<c:url value="/resources/font-awesome-4.7.0/css/font-awesome.min.css"/>">
    <script src="<c:url value="/resources/bootstrap-4.4.1-dist/js/bootstrap.min.js"/>" ></script>
    <link href="<c:url value="/resources/camau/css/khambenhnoitru.css" />" rel="stylesheet"/>
    <script src="<c:url value="/resources/js/read_file.js" />"></script>
    <link href="<c:url value="/resources/camau/css/formio.full.min.css" />" rel="stylesheet"/>
    <link href="<c:url value="/resources/camau/css/loader.css" />" rel="stylesheet"/>
    <link href="<c:url value="/resources/camau/css/custom.css" />" rel="stylesheet"/>
    <script src="<c:url value="/resources/ckeditor/ckeditor.js" />"></script>
    <script src="<c:url value="/resources/ckeditor/adapters/jquery.js" />"></script>
    <link rel="stylesheet" href="<c:url value="/resources/camau/css/select2.min.css" />" />
    <script src="<c:url value="/resources/camau/js/select2.min.js" />"></script>
    <script src="<c:url value="/resources/js/datetimepicker.js" />"></script>
    <link rel="stylesheet" href="<c:url value="/resources/css/datetimepicker.css" />" />
    <script src="<c:url value="/resources/js/jquery-confirm.min.js" />"></script>
    <link href="<c:url value="/resources/css/jquery-confirm.min.css" />" rel="stylesheet"/>
    <link href="<c:url value="/resources/combogrid/css/smoothness/jquery.ui.combogrid.css" />" rel="stylesheet"/>
    <script src="<c:url value="/resources/combogrid/plugin/jquery.ui.combogrid-1.6.3.js" />"></script>
    <script src="<c:url value="/resources/contextmenu/jquery.contextMenu.js" />"></script>
    <link href="<c:url value="/resources/contextmenu/jquery.contextMenu.css" />" rel="stylesheet"/>
    <script src="<c:url value="/resources/camau/smartca769.js?timestamp=${System.currentTimeMillis()}" />"></script>
    <script src="<c:url value="/resources/camau/js/jquery.validate.min.js" />"></script>
    <script src="<c:url value="/resources/camau/js/formio.full.min.js" />"></script>
    <script src="<c:url value="/resources/camau/material/moment.js" />"></script>
    <script src="<c:url value="/resources/print-js/v1.0.43/print.min.js" />"></script>
    <script src="<c:url value="/resources/smartca/js/pdf.js" />"></script>
    <link href="<c:url value="/resources/smartca/css/smartca-loading.css" />" rel="stylesheet"/>
    <script src="<c:url value="/resources/smartca/js/smartca-loading.js?timestamp=${System.currentTimeMillis()}"/>"></script>
    <script src="<c:url value="/resources/js/jquery.md5.js?timestamp=${System.currentTimeMillis()}" />"></script>
    <link href="<c:url value="/resources/smartca/css/scanner-loading.css" />" rel="stylesheet"/>
    <script src="<c:url value="/resources/smartca/js/scanner-loading.js?timestamp=${System.currentTimeMillis()}"/>"></script>
    <script src="<c:url value="/resources/camau/js/pdf-lib.min.js"/>"></script>
    <script src="<c:url value="/resources/camau/js/jquery.toast.min.js" />"></script>
    <link href="<c:url value="/resources/camau/css/jquery.toast.min.css" />" rel="stylesheet"/>
    <script src="<c:url value="/resources/camau/vnpt-plugin_v692020.js" />"></script>
    <script src="<c:url value="/resources/camau/keytichhop.js" />"></script>
    <script src="<c:url value="/resources/camau/js/kiemtradulieuthoigiankham.js?timestamp=${System.currentTimeMillis()}" />"></script>
<%--    <link rel="stylesheet" href="<c:url value="/resources/camau/css/swiper-bundle.min.css" />" />--%>
<%--    <script src="<c:url value="/resources/camau/js/swiper-bundle.min.js" />"></script>--%>

    <script>
        var urlCheckBHXH = "<%= ThamSoManager.instance(session).getThamSoString("960564","0")%>";
        var url = "cmu_ktra_thongtuyen_2024?";
        if(urlCheckBHXH == 1) {
            url = "https://apikysohis.vnptcamau.vn/kiem-tra-thong-tuyen?"
        }
        var singletonObject = {
            khoaKham: "${KHOAKHAM}" ? "${KHOAKHAM}" : 0,
            phongDuocSet: "${Sess_PhongDuocSet}",
            makhoa: "${Sess_PhongBan}",
            maphongbenh: "${Sess_Phong}",
            tenphongbenh: "${Sess_TenPhong}",
            dvtt: "${Sess_DVTT}",
            userId: "${Sess_UserID}",
            user: "${Sess_User}",
            admin: "${Sess_Admin}",
            danhsachphongban: [],
            danhsachphongbenh: [],
            danhsachnhanvienkhoa: [],
            ngayhientai: "${ngayhientai}",
            danhsachloaittpt: [],
            danhsachkhothuocBHYT: [],
            danhsachkhovattu: [],
            danhsachkhomienphi: [],
            danhsachkhomuataiquay: [],
            danhsachkhodongy: [],
            danhsachkhodichvu: [],
            danhsachgiuongbenh: [],
            danhsachnghenghiep: [],
            danhsachdantoc: [],
            danhsachthutruong: [],
            danhsachtruongkhoa: [],
            danhsachbvchuyentuyen: [],
            danhsachxangdau: [],
            danhsachnhanvien: [],
            danhsachquanhe: [],
            danhsachnguyennhantv: [],
            danhsachnnoituvong: [],
            danhsachtinh: [],
            danhsachquyenchungtu: [],
            danhsachloaibenhan: [],
            objkhoanhanvien: {},
            urlCheckBHXH: url,
            thamso82816: 0,
            thamso42001: 0,
            thamso960518: 0,
            thamso960484: 0,
            hgi_tt50_pdf: "${hgi_tt50_pdf}",
            kgggoiso: "<%= ThamSoManager.instance(session).getThamSoString("91100","0")%>",
            thanhtoannhieunac: "<%= ThamSoManager.instance(session).getThamSoString("68","0")%>",
            locDsBnTheoPhong: "<%= ThamSoManager.instance(session).getThamSoString("91087","0")%>",
            thuchienclsphaidongtienvienphi: "<%= ThamSoManager.instance(session).getThamSoString("89","0")%>",
            BN_MIENPHI_KBH_KHHONGCANDONGTIEN: "<%= ThamSoManager.instance(session).getThamSoString("820343","0")%>",
            nhapketluan_cls: "<%= ThamSoManager.instance(session).getThamSoString("40005","0")%>",
            hienThiMauSacBN: "<%= ThamSoManager.instance(session).getThamSoString("31008","0")%>",
            hienThiThemCLS: "<%= ThamSoManager.instance(session).getThamSoString("31009","0")%>",
            timKiemCLS: "<%= ThamSoManager.instance(session).getThamSoString("228","0")%>",
            tsktvhuyKQ: "<%= ThamSoManager.instance(session).getThamSoString("94336","0")%>",
            THAMSO_960627:"<%= ThamSoManager.instance(session).getThamSoString("960627", "0")%>",
            THAMSO_960632:"<%= ThamSoManager.instance(session).getThamSoString("960632", "0")%>",
            THAMSO_960633:"<%= ThamSoManager.instance(session).getThamSoString("960633", "0")%>",
        }
        var thongtinhsba = {};
    </script>
    <style>
        .v-menu-tab .nav-link {
             margin-top: 0px !important;
        }
        .dt-thongtin {
             padding-left: 0.5rem;
             opacity: 0.8;
        }
        .label2 {
            display: block;
            margin-bottom: 0.5rem;
        }

        /*#v-pdf-tong {*/
        /*    display: grid;*/
        /*    grid-template-rows: auto 1fr;*/
        /*    height: 100%;*/
        /*}*/
    </style>
</head>
<body>
<div id="panel_all" style="background: white">
    <%@include file="../../../../resources/Theme/include_pages/menu.jsp"%>
    <div class="p-4">
        <div id="hsba_tabs">
            <ul>
                <li><a href="#hsba_tab0" id="hsba_tab0_header">Tiếp nhận</a></li>
                <li><a href="#hsba_tab1" id="hsba_tab1_header">Kết quả</a></li>
            </ul>
            <div id="hsba_tab0">
                <div>
                    <div class="row ml-0 mr-0 mt-3 mb-3 pl-2 pr-2">
                        <div class="col-sm-12">
                            <div class="row ml-0 mr-0">
                                <div class="col-sm-1 pr-2 mb-2">
                                    <label>Ngày</label>
                                    <input type="text" class="form-control form-control-sm input-only-date input-custom" id="xn_ngaychidinh">
                                </div>
                                <div class="col-sm-1 pr-2">
                                    <label>Loại</label>
                                    <select class=" form-control form-control-sm" id="xn_loaidt" data-show-subtext="true" data-live-search="true">
                                        <option value="-1"> -- Tất cả -- </option>
                                        <option value="DIENTIM"> Điện tim </option>
                                        <option value="LUUHUYETNAO">Lưu huyết não</option>
                                        <option value="DIENNAO">Điện não</option>
                                        <option value="DIENCO">Điện cơ</option>
                                    </select>
                                </div>
                                <div class="col-sm-2 pr-2">
                                    <label>Khoa</label>
                                    <select class=" form-control form-control-sm" id="xn_khoa" data-show-subtext="true" data-live-search="true">
                                    </select>
                                </div>
                                <div class="col-sm-2 pr-2">
                                    <label>Phòng</label>
                                    <select class=" form-control form-control-sm" id="xn_phong" data-show-subtext="true" data-live-search="true">
                                    </select>
                                </div>
                                <div class="col-sm-1 pr-2">
                                    <label>Đối tượng</label>
                                    <select class=" form-control form-control-sm" id="xn_doituong" data-show-subtext="true" data-live-search="true">
                                        <option value="-1">--Tất cả--</option>
                                        <option value="1">Có BHYT</option>
                                        <option value="0">Không BHYT</option>
                                    </select>
                                </div>
                                <div class="col-sm-1 pr-2">
                                    <label>Trạng thái</label>
                                    <select class=" form-control form-control-sm" id="xn_trangthai" data-show-subtext="true" data-live-search="true">
                                        <option value="0">Chưa thực hiện</option>
                                        <option value="1">Đã thực hiện</option>
                                    </select>
                                </div>
                                <div class="col-sm-1">
                                    <label>&nbsp;</label>
                                    <button id="xn_lammoi" class="btn btn-primary form-control form-control-sm line-height-1">
                                        <i class="fa fa-refresh"></i> Làm mới
                                    </button>
                                </div>
                                <div class="col-sm-12">
                                    <div id="list_benhnhan_wrap" class="wrap-jqgrid">
                                        <table id="list_benhnhan"></table>
                                    </div>
                                </div>
                                <div class="col-sm-12 mt-2">
                                    <label style="color:red;font-weight: normal;">BN cấp cứu</label>
                                    <label style="color:#137c13;margin-left:20px;font-weight: normal;">BN &lt; 6 tuổi</label><br>
                                    <label style="color:#bf00ff;font-weight: normal;">Bệnh nhân VP</label><br>
                                    <label style="color:#EE7600;font-weight: normal;">Bệnh nhân BHYT chưa đóng VP</label>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div id="hsba_tab1" class="pl-4 pr-4 pt-2 pb-2">
                <div class="row ml-0 mr-0 mt-2">
                    <div class="col-sm-3 dash-bt-border">
                        <span class="label2">Họ tên</span>
                        <span id="dt_hoten" data-key="TENBENHNHAN" class="dt-thongtin"></span>
                    </div>
                    <div class="col-sm-3 dash-bt-border">
                        <span class="label2">Số vào viện</span>
                        <span id="dt_sovaovien" data-key="SOVAOVIEN" class="dt-thongtin"></span>
                    </div>
                    <div class="col-sm-2 dash-bt-border">
                        <span class="label2">Tuổi</span>
                        <span id="dt_tuoi" data-key="TUOI" class="dt-thongtin"></span>
                    </div>
                    <div class="col-sm-2 dash-bt-border">
                        <span class="label2">Giới tính</span>
                        <span id="dt_gioitinh" data-key="GIOITINH_HT" class="dt-thongtin"></span>
                    </div>
                    <div class="col-sm-2 dash-bt-border">
                        <span class="label2">Mã y tế</span>
                        <span id="dt_mayte" data-key="MABENHNHAN" class="dt-thongtin"></span>
                    </div>
                </div>
                <div class="row ml-0 mr-0 mt-2">
                    <div class="col-sm-3 dash-bt-border">
                        <span class="label2">BHYT</span>
                        <span id="dt_bhyt" data-key="SOTHEBHYT" class="dt-thongtin"></span>
                    </div>
                    <div class="col-sm-3 dash-bt-border">
                        <span class="label2">Cân nặng (kg)</span>
                        <span id="dt_cannang" data-key="CANNANG" class="dt-thongtin"></span>
                    </div>
                    <div class="col-sm-2 dash-bt-border">
                        <span class="label2">Chiều cao (cm)</span>
                        <span id="dt_chieucao" data-key="CHIEUCAO" class="dt-thongtin"></span>
                    </div>
                    <div class="col-sm-2 dash-bt-border">
                        <span class="label2">Buồng</span>
                        <span id="dt_buong" data-key="BUONG" class="dt-thongtin"></span>
                    </div>
                    <div class="col-sm-2 dash-bt-border">
                        <span class="label2">Giường</span>
                        <span id="dt_giuong" data-key="GIUONG" class="dt-thongtin"></span>
                    </div>
                </div>
                <div class="row ml-0 mr-0 mt-2">
                    <div class="col-sm-3 dash-bt-border">
                        <span class="label2">Khoa</span>
                        <span id="dt_khoa" data-key="TENKHOA" class="dt-thongtin"></span>
                    </div>
                    <div class="col-sm-3 dash-bt-border">
                        <span class="label2">Địa chỉ</span>
                        <span id="dt_diachi" data-key="DIACHI" class="dt-thongtin"></span>
                    </div>
                    <div class="col-sm-3 dash-bt-border">
                        <span class="label2">Chẩn đoán</span>
                        <span id="dt_chandoan" data-key="CHUANDOANICD" class="dt-thongtin"></span>
                    </div>
                    <div class="col-sm-3 dash-bt-border">
                        <span class="label2">Ngày giờ chỉ định</span>
                        <span id="dt_ngaygiocd" data-key="NGAY_CHI_DINH" class="dt-thongtin"></span>
                    </div>
                </div>
                <div class="row ml-0 mr-0 mt-2">
                    <div class="col-sm-9">
                        <div class="col-sm-12">
                            <div id="formKetQua"></div>
                        </div>
                        <div class="text-center col-sm-12 pb-2">
                            <button type="button" class="btn btn-sm btn-primary chuakyshow" id="xn_luuthongtin"><i class="fa fa-save"></i> Lưu thông tin</button>
                            <button type="button" class="btn btn-sm btn-success" id="dt_ekip"><i class="fa fa-users"></i> EKIP</button>
                            <button type="button" class="btn btn-sm btn-success chuakyshow" id="dt_kysosm"><i class="fa fa-key"></i> Ký Số</button>
                            <button type="button" class="btn btn-sm btn-danger kyso" id="dt_huykyso"><i class="fa fa-key"></i> Huỷ ký số</button>
                            <button type="button" class="btn btn-sm btn-warning" id="dt_lichsu"><i class="fa fa-book"></i> Lịch sử DT</button>
                            <select class="form-control-sm" name="dt_loaimauin" id="dt_loaimauin">
                                <option value="DIENTIM">Điện tim</option>
                                <option value="DONHANAP">Đo nhãn áp</option>
                                <option value="GHIDIENCO">Ghi điện cơ</option>
                                <option value="DIENNAO">Ghi điện não</option>
                                <option value="DOTOCDOTHANKINH">ĐO TỐC ĐỘ DẪN TRUYỀN THẦN KINH</option>
                            </select>
                            <button type="button" class="btn btn-sm btn-success" id="dt_inphieu"><i class="fa fa-eye"></i> Xem phiếu</button>
                            <button type="button" class="btn btn-sm btn-success" id="dt_inphieucd"><i class="fa fa-eye"></i> Xem phiếu CĐ</button>
                        </div>
<%--                        <div class="text-center col-sm-12 pb-2">--%>
<%--                            <button type="button" class="btn btn-sm btn-success" id="dt_ekip"><i class="fa fa-users"></i> EKIP</button>--%>
<%--                            <button type="button" class="btn btn-sm btn-success chuakyshow" id="dt_kysosm"><i class="fa fa-key"></i> Ký Số</button>--%>
<%--                            <button type="button" class="btn btn-sm btn-danger kyso" id="dt_huykyso"><i class="fa fa-key"></i> Huỷ ký số</button>--%>
<%--                            <button type="button" class="btn btn-sm btn-warning" id="dt_lichsu"><i class="fa fa-book"></i> Lịch sử DT</button>--%>
<%--                        </div>--%>
                    </div>
                    <div class="col-sm-3 text-center">
                        <div class="col-sm-12 mt-5">
                            <div id="say-cheese-snapshotsdt"><img class="width100" id="preview-imagedt"
                                                                  src = "<c:url value="/resources/webcam/camera_png.jpg" />"
                                                                  width="300px" height="300px"/></div>
                        </div>
                        <div class="col-sm-12 mt-2">
                            <label class="btn btn-sm btn-success mt-2 shownutanh_dt" style="font-size: 1em"> Duyệt ảnh <input
                                    type="file" class="img-fluid shadow-lg" accept="image/png, image/gif, image/jpeg" name="browse-file" id="hinhanhdt_duyet"
                                    style="display: none;"></label>
                            <input type="button" name="snapshot-rotate" id="snapshot-rotate" value="Xoay ảnh"
                                   class="btn btn-sm btn-success shownutanh_dt"/>
                            <input type="hidden" name="snapshot-sttdt" id="snapshot-sttdt"
                                   class="button_shadow"/>
                            <canvas id="canvas" style="display: none;"></canvas>
                        </div>
                        <div class="col-sm-12 mt-2">
                            <input type="button" name="save-snapshot" id="hinhanhdt_luu" value="Lưu ảnh"
                                   class="btn btn-sm btn-success shownutanh_dt"/>
                            <input type="button" name="dt_xemanh" id="dt_xemanh" value="Xem ảnh"
                                   class="btn btn-sm btn-success"/>
                            <input type="button" name="snapshot-rotate" id="hinhanhdt_xoa" value="Xóa ảnh"
                                   class="btn btn-sm btn-danger shownutanh_dt"/>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>


<div class="modal fade" id="modalDSLichSuDT" role="dialog"
     aria-labelledby="modalDSLichSuDT" aria-hidden="true"
     data-keyboard="false" data-backdrop="static">
    <div class="modal-dialog modal-dialog-centered" style="max-width: 50%; width:50%;" role="document">
        <div class="modal-content">
            <div class="modal-header modal-header-sticky py-2 d-flex justify-content-center align-items-center">
                <h6 class="modal-title text-primary font-weight-bold">Lịch sử điện tim</h6>
                <button type="button" class="close close-modal" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <div id="dslichsudt-wrap">
                    <table id="list_dslichsudt"></table>
                </div>
            </div>
            <div class="modal-footer modal-footer-sticky py-2">
                <div class="col-md-6 text-right">
                    <button class="btn btn-default ml-2 form-control-sm line-height-1 close-modal clear-shadow" type="button" data-dismiss="modal">
                        Đóng
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="modal fade" id="modalFormEkipDienTim" role="dialog"
     aria-labelledby="modalFormEkipDienTim" aria-hidden="true"
     data-keyboard="false" data-backdrop="static">
    <div class="modal-dialog modal-dialog-centered" style="max-width: 62%;width:62%;" role="document">
        <div class="modal-content">
            <div class="modal-header modal-header-sticky py-2 d-flex justify-content-center align-items-center">
                <h6 class="modal-title text-primary font-weight-bold" id="titleFormEkipDienTim">EKIP Bác sĩ</h6>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <div id="wrapFormEkipDienTim">
                    <div class="row ml-0 mr-0 mt-2">
<%--                        <div class="col-md-12" id="formNhapEkipDienTim"></div>--%>
                        <div class="col-md-2">
                            Tên bệnh nhân
                        </div>
                        <div class="col-md-10 pl-2">
                            <input type="text" class="form-control form-control-sm" id="dt_ekiptenbn" readonly name="TEN_BENH_NHAN">
                        </div>
                    </div>
                    <div class="row ml-0 mr-0 mt-2">
                        <div class="col-md-2">
                            Thời gian
                        </div>
                        <div class="col-md-4 pl-2">
                            <input class="form-control form-control-sm clear-input input-date" name="THOI_GIAN_EKIP"
                                   id="dt_ekipthoigian"/>
                        </div>
                    </div>
                    <div class="row ml-0 mr-0 mt-2">
                        <div class="col-md-2">
                            Bác sĩ
                        </div>
                        <div class="col-md-4 pl-2">
                            <select class="form-control form-control-sm" id="dt_ekipbacsi"
                                    name="BAC_SI_EKIP">
                            </select>
                        </div>
                    </div>
                    <div class="row ml-0 mr-0 mt-2">
                        <div class="col-md-2">
                            Dụng cụ/ TTV Phụ
                        </div>
                        <div class="col-md-4 pl-2">
                            <select class="form-control form-control-sm" id="dt_ekipdungcu"
                                    name="DUNG_CU_EKIP">
                            </select>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer modal-footer-sticky py-2">
                <div class="col-md-6 text-right">
                    <button class="btn btn-success form-control-sm line-height-1 btn-loading" type="button" id="ekipdientim_luu">
                        <span class="spinner-border spinner-border-sm spinner" role="status" aria-hidden="true"></span>
                        Lưu
                    </button>
                    <button class="btn btn-success form-control-sm line-height-1 btn-loading" type="button" id="ekipdientim_huy">
                        <span class="spinner-border spinner-border-sm spinner" role="status" aria-hidden="true"></span>
                        Hủy Ekip
                    </button>
                    <button class="btn btn-default ml-2 form-control-sm line-height-1" type="button"
                            data-dismiss="modal">
                        Đóng
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<audio id="fingerAudio" hidden>
    <source src="resources/smartca/audio/fingerAudio.mp3" type="audio/mpeg">
</audio>
<div class="modal fade" id="modalHienThiPdf" role="dialog"
     aria-labelledby="modalHienThiPdf" aria-hidden="true"
     data-keyboard="false" data-backdrop="static">
    <div class="modal-dialog modal-dialog-centered" style="max-width: 92%; width:40%" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h6 class="modal-title text-primary font-weight-bold" id="titleHienThiPdf">Tài liệu</h6>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <div id="myModalHienThiPdf" class="modalImgClass">
                    <div class="modalImg-content">
                        <iframe id="modalIframePdf" src="" width="100%" height="400px" style="border: none;"></iframe>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <div class="col-md-12 text-right">
                    <button class="btn btn-default ml-2 form-control-sm line-height-1" type="button" data-dismiss="modal">
                        Đóng
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="modal fade" id="modalHienThiHinhAnh" role="dialog"
     aria-labelledby="modalHienThiHinhAnh" aria-hidden="true"
     data-keyboard="false" data-backdrop="static">
    <div class="modal-dialog modal-dialog-centered" style="max-width: 92%; width:40%" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h6 class="modal-title text-primary font-weight-bold" id="titleHinhAnh">Hình ảnh</h6>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <div id="myModalImg" class="modalImgClass">
                    <div class="modalImg-content">
                        <img id="modalImage" src="" alt="Image" style="width:100%">
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <div class="col-md-12 text-right">
                    <button class="btn btn-default ml-2 form-control-sm line-height-1" type="button" data-dismiss="modal">
                        Đóng
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<jsp:include page="../../../../resources/Theme/include_pages/footerBT.jsp"/>
<%--<jsp:include page="../canlamsang/cmuekipdientim.jsp"/>--%>
<jsp:include page="loader.jsp"/>
<jsp:include page="emr_lichsubenhan_badt.jsp"/>

<%--<script src="<c:url value="/resources/camau/js/quanlyhosobenhan.js?timestamp=${System.currentTimeMillis()}"/>" ></script>--%>


<script src="<c:url value="/resources/camau/js/jsonform.js?timestamp=${System.currentTimeMillis()}"/>" ></script>
<%--<script src="<c:url value="/resources/camau/keytichhop.js" />"></script>--%>
<script src="<c:url value="/resources/camau/js/kiemtradulieuthoigiankham.js?timestamp=${System.currentTimeMillis()}" />"></script>
<script src="<c:url value="/resources/camau/js/luulog.js?timestamp=${System.currentTimeMillis()}"/>" ></script>
<script src="<c:url value="/resources/js/libControl.js?timestamp=${System.currentTimeMillis()}"/>" ></script>
<script src="<c:url value="/resources/camau/js/vobenhanCommon.js?timestamp=${System.currentTimeMillis()}"/>" ></script>
<script src="<c:url value="/resources/camau/js/kysonoitru.js?timestamp=${System.currentTimeMillis()}"/>" ></script>
<%--<script src="<c:url value="/resources/camau/js/thongtinbenhan.js?timestamp=${System.currentTimeMillis()}"/>" ></script>--%>
<script src="<c:url value="/resources/camau/vnptmoney.js?timestamp=${System.currentTimeMillis()}"/>" ></script>
<script src="<c:url value="/resources/camau/js/lichsubenhan.js?timestamp=${System.currentTimeMillis()}"/>" ></script>
<script src="<c:url value="/resources/camau/js/geturlxemcacphieu.js?timestamp=${System.currentTimeMillis()}"/>" ></script>
<script src="<c:url value="/resources/camau/js/dientim.js?timestamp=${System.currentTimeMillis()}"/>" ></script>
</body>
</html>