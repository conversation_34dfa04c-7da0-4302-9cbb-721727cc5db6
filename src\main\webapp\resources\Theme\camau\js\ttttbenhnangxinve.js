
$(function (){
    var thongTinTTTTBenhNangXinVeMoiNhat = {};
    var thongTinTTTTBenhNangXinVeTruocChinhSua = {};
    var formTTTTBenhNangXinVe;

    $("#ttchamsoc-phieukhac").click(function () {
        instanceGridTTTTBenhNangXinVe();
        reloadDSTTTTBenhNangXinVe();
        showFormTTTTBenhNangXinVe();
    });
    $("#ttttbenhnangxinve_lammoi").click(function () {
        reloadDSTTTTBenhNangXinVe();
    });

    $(".themttttbenhnangxinve").click(function () {
        $("#modalFormTTTTBenhNangXinVe").modal("show");
        addTextTitleModal("titleFormTTTTBenhNangXinVe", " Phiếu tóm tắt thông tin người bệnh nặng xin về");
        showFormTTTTBenhNangXinVe();
        $("#ttttbenhnangxinve_luu").attr("data-action", "THEM");
    });

    $("#ttttbenhnangxinve_luu").click(function () {
        var btnAction = $('#ttttbenhnangxinve_luu').attr("data-action");
        if (btnAction == "THEM"){
            themTTTTBenhNangXinVe();
        } else {
            updateTTTTBenhNangXinVe();
        }
    });

    $(document).on('click', '#ttttbenhnangxinve_kysobs_action', function() {
        kySoChung({
            dvtt: singletonObject.dvtt,
            userId: singletonObject.userId,
            url: $('#iframePreviewAndSign').attr('src'),
            loaiGiay: "PHIEU_NOITRU_TTTTBENHNANGXINVE_BACSI",
            maBenhNhan: thongtinhsba.thongtinbn.MA_BENH_NHAN,
            soBenhAn: thongtinhsba.thongtinbn.STT_BENHAN,
            soPhieuDichVu: thongTinTTTTBenhNangXinVeTruocChinhSua.ID,
            soVaoVien: thongtinhsba.thongtinbn.SOVAOVIEN,
            soVaoVienDT: thongtinhsba.thongtinbn.SOVAOVIEN_DT,
            keyword: "Bác sĩ điều trị",
            fileName: "Phiếu tóm tắt thông tin người bệnh nặng xin về: " + thongtinhsba.thongtinbn.TEN_BENH_NHAN + " - Mã phiếu: " + thongTinTTTTBenhNangXinVeTruocChinhSua.ID,
        }, function(dataKySo) {
            $("#modalPreviewAndSignPDF").modal("hide");
            reloadDSTTTTBenhNangXinVe();
        });
    });

    $(document).on('click', '#ttttbenhnangxinve_kysotk_action', function() {
        kySoChung({
            dvtt: singletonObject.dvtt,
            userId: singletonObject.userId,
            url: $('#iframePreviewAndSign').attr('src'),
            loaiGiay: "PHIEU_NOITRU_TTTTBENHNANGXINVE_TRUONGKHOA",
            maBenhNhan: thongtinhsba.thongtinbn.MA_BENH_NHAN,
            soBenhAn: thongtinhsba.thongtinbn.STT_BENHAN,
            soPhieuDichVu: thongTinTTTTBenhNangXinVeTruocChinhSua.ID,
            soVaoVien: thongtinhsba.thongtinbn.SOVAOVIEN,
            soVaoVienDT: thongtinhsba.thongtinbn.SOVAOVIEN_DT,
            keyword: "Trưởng khoa điều trị",
            fileName: "Phiếu tóm tắt thông tin người bệnh nặng xin về: " + thongtinhsba.thongtinbn.TEN_BENH_NHAN + " - Mã phiếu: " + thongTinTTTTBenhNangXinVeTruocChinhSua.ID,
        }, function(dataKySo) {
            $("#modalPreviewAndSignPDF").modal("hide");
            reloadDSTTTTBenhNangXinVe();
        });
    });

    $('#view_single_ttttbenhnangxinve').click(function () {
        xemTTTTBenhNangXinVe(thongTinTTTTBenhNangXinVeMoiNhat)
    })

    $('#edit_single_ttttbenhnangxinve').click(function () {
        showUpdateTTTTBenhNangXinVe(thongTinTTTTBenhNangXinVeMoiNhat)
    });

    $('#delete_single_ttttbenhnangxinve').click(function () {
        deleteTTTTBenhNangXinVe(thongTinTTTTBenhNangXinVeMoiNhat)
    })

    function showFormTTTTBenhNangXinVe() {
        var jsonForm = getJSONObjectForm([
            {
                "label": "",
                "columns": [
                    {
                        "components": [
                            {
                                "label": "Bệnh án",
                                "key": "BENHAN",
                                "customClass": "pr-2",
                                "type": "textfield",
                            },
                        ],
                        "width": 3,
                        "size": "md",
                    },
                    {
                        "components": [
                            {
                                "label": "Mã HSBA",
                                "key": "MAHSBA",
                                "customClass": "pr-2",
                                "type": "textfield",
                            },
                        ],
                        "width": 3,
                        "size": "md",
                    },
                    {
                        "components": [
                            {
                                "label": "Số định danh cá nhân",
                                "key": "SODINHDANH",
                                "customClass": "pr-2",
                                "type": "textfield",
                                others: {
                                    defaultValue: thongtinhsba.thongtinbn.CMT_BENHNHAN
                                }
                            },
                        ],
                        "width": 3,
                        "size": "md",
                    },
                    {
                        "components": [
                            {
                                "label": "Loại",
                                "key": "LOAI",
                                "type": "textfield",
                            },
                        ],
                        "width": 3,
                        "size": "md",
                    },
                ],
                "key": "columns_nhin",
                "type": "columns",
                "customClass": "ml-0 mr-0 pb-2",
            },
            {
                "label": "",
                "columns": [
                    {
                        "components": [
                            {
                                "label": "Số thẻ BHYT",
                                "key": "SOTHEBHYT",
                                "customClass": "pr-2",
                                "type": "textfield",
                                others: {
                                    defaultValue: thongtinhsba.thongtinbn.SOBAOHIEMYTE,
                                }
                            },
                        ],
                        "width": 3,
                        "size": "md",
                    },
                    {
                        "components": [
                            {
                                "label": "Giá trị từ",
                                "key": "GIATRITU",
                                "type": "datetime",
                                format: "dd/MM/yyyy",
                                "customClass": "pr-2",
                                enableTime: false,
                                // "validate": {
                                //     "required": true
                                // },
                            },
                        ],
                        "width": 3,
                        "size": "md",
                    },
                    {
                        "components": [
                            {
                                "label": "Đến",
                                "key": "GIATRIDEN",
                                "type": "datetime",
                                format: "dd/MM/yyyy",
                                "customClass": "pr-2",
                                enableTime: false,
                            },
                        ],
                        "width": 3,
                        "size": "md",
                    },
                    {
                        "components": [
                            {
                                "label": "Nơi đăng ký ban đầu",
                                "key": "NOIDANGKY",
                                "type": "textfield",
                            },
                        ],
                        "width": 3,
                        "size": "md",
                    },
                ],
                "key": "columns_nhin",
                "type": "columns",
                "customClass": "ml-0 mr-0 pb-2",
            },
            {
                "label": "",
                "columns": [
                    {
                        "components": [
                            {
                                "label": "Thời điểm đủ 5 năm liên tục từ ngày",
                                "key": "THOIGIAN5NAM",
                                "type": "datetime",
                                format: "dd/MM/yyyy",
                                "customClass": "pr-2",
                                enableTime: false,
                            },
                        ],
                        "width": 6,
                        "size": "md",
                    },
                    {
                        "components": [
                            {
                                "label": "Miễn cùng chi trả trong năm từ ngày",
                                "key": "MIENCUNGCHITRA",
                                "type": "datetime",
                                format: "dd/MM/yyyy",
                                enableTime: false,
                            },
                        ],
                        "width": 6,
                        "size": "md",
                    },
                ],
                "key": "columns_nhin",
                "type": "columns",
                "customClass": "ml-0 mr-0 pb-2",
            },
            {
                "label": "",
                "columns": [
                    {
                        "components": [
                            {
                                "label": "Địa chỉ hiện tại: Tỉnh",
                                "key": "DIACHIHTTINH",
                                "customClass": "pr-2",
                                "type": "textfield",
                            },
                        ],
                        "width": 3,
                        "size": "md",
                    },
                    {
                        "components": [
                            {
                                "label": "Huyện",
                                "key": "DIACHIHTHUYEN",
                                "customClass": "pr-2",
                                "type": "textfield",
                            },
                        ],
                        "width": 3,
                        "size": "md",
                    },
                    {
                        "components": [
                            {
                                "label": "Xã",
                                "key": "DIACHIHTXA",
                                "customClass": "pr-2",
                                "type": "textfield",
                            },
                        ],
                        "width": 3,
                        "size": "md",
                    },
                    {
                        "components": [
                            {
                                "label": "Thôn xóm, số nhà",
                                "key": "DIACHIHTSONHA",
                                "type": "textfield",
                            },
                        ],
                        "width": 3,
                        "size": "md",
                    },
                ],
                "key": "columns_nhin",
                "type": "columns",
                "customClass": "ml-0 mr-0 pb-2",
            },
            {
                "label": "",
                "columns": [
                    {
                        "components": [
                            {
                                "label": "Địa chỉ thường trú: Tỉnh",
                                "key": "DIACHITRTINH",
                                "customClass": "pr-2",
                                "type": "textfield",
                            },
                        ],
                        "width": 3,
                        "size": "md",
                    },
                    {
                        "components": [
                            {
                                "label": "Huyện",
                                "key": "DIACHITRHUYEN",
                                "customClass": "pr-2",
                                "type": "textfield",
                            },
                        ],
                        "width": 3,
                        "size": "md",
                    },
                    {
                        "components": [
                            {
                                "label": "Xã",
                                "key": "DIACHITRXA",
                                "customClass": "pr-2",
                                "type": "textfield",
                            },
                        ],
                        "width": 3,
                        "size": "md",
                    },
                    {
                        "components": [
                            {
                                "label": "Thôn xóm, số nhà",
                                "key": "DIACHITRSONHA",
                                "type": "textfield",
                            },
                        ],
                        "width": 3,
                        "size": "md",
                    },
                ],
                "key": "columns_nhin",
                "type": "columns",
                "customClass": "ml-0 mr-0 pb-2",
            },
            {
                "label": "",
                "columns": [
                    {
                        "components": [
                            {
                                "label": "Nơi ghi nhận bệnh: Tỉnh",
                                "key": "DIACHIBENHTINH",
                                "customClass": "pr-2",
                                "type": "textfield",
                            },
                        ],
                        "width": 3,
                        "size": "md",
                    },
                    {
                        "components": [
                            {
                                "label": "Huyện",
                                "key": "DIACHIBENHHUYEN",
                                "customClass": "pr-2",
                                "type": "textfield",
                            },
                        ],
                        "width": 3,
                        "size": "md",
                    },
                    {
                        "components": [
                            {
                                "label": "Xã",
                                "key": "DIACHIBENHXA",
                                "customClass": "pr-2",
                                "type": "textfield",
                            },
                        ],
                        "width": 3,
                        "size": "md",
                    },
                    {
                        "components": [
                            {
                                "label": "Thôn xóm, số nhà",
                                "key": "DIACHIBENHSONHA",
                                "type": "textfield",
                            },
                        ],
                        "width": 3,
                        "size": "md",
                    },
                ],
                "key": "columns_nhin",
                "type": "columns",
                "customClass": "ml-0 mr-0 pb-2",
            },
            {
                "label": "",
                "columns": [
                    {
                        "components": [
                            {
                                "label": "Tôn giáo",
                                "key": "TONGIAO",
                                "customClass": "pr-2",
                                "type": "textfield",
                            },
                        ],
                        "width": 3,
                        "size": "md",
                    },
                    {
                        "components": [
                            {
                                "label": "Số ngày vắng mặt",
                                "key": "SONGAYVANGMAT",
                                "customClass": "pr-2",
                                "type": "textfield",
                            },
                        ],
                        "width": 3,
                        "size": "md",
                    },
                    {
                        "components": [
                            {
                                "label": "Loại vào viện",
                                "key": "LOAIVAOVIEN",
                                "customClass": "pr-2",
                                "type": "textfield",
                            },
                        ],
                        "width": 3,
                        "size": "md",
                    },
                    {
                        "components": [
                            {
                                "label": "Ngày ra viện/ TV",
                                "key": "NGAYRAVIEN",
                                "type": "datetime",
                                format: "dd/MM/yyyy",
                                enableTime: false,
                            },
                        ],
                        "width": 3,
                        "size": "md",
                    },
                ],
                "key": "columns_nhin",
                "type": "columns",
                "customClass": "ml-0 mr-0 pb-2",
            },
            {
                "label": "",
                "columns": [
                    {
                        "components": [
                            {
                                "label": "Chuyển từ",
                                "key": "CHUYENTU",
                                "customClass": "pr-2",
                                "type": "textfield",
                            },
                        ],
                        "width": 6,
                        "size": "md",
                    },
                    {
                        "components": [
                            {
                                "label": "Chuyển đến",
                                "key": "CHUYENDEN",
                                "type": "textfield",
                            },
                        ],
                        "width": 6,
                        "size": "md",
                    },
                ],
                "key": "columns_nhin",
                "type": "columns",
                "customClass": "ml-0 mr-0 pb-2",
            },
            {
                "label": "",
                "columns": [
                    {
                        "components": [
                            {
                                "label": "Tình trạng ra viện",
                                "key": "TINHTRANGRV",
                                "customClass": "pr-2",
                                "type": "textfield",
                            },
                        ],
                        "width": 6,
                        "size": "md",
                    },
                    {
                        "components": [
                            {
                                "label": "Kết quả điều trị",
                                "key": "KETQUADT",
                                "type": "textfield",
                            },
                        ],
                        "width": 6,
                        "size": "md",
                    },
                ],
                "key": "columns_nhin",
                "type": "columns",
                "customClass": "ml-0 mr-0 pb-2",
            },
            {
                "label": "",
                "columns": [
                    {
                        "components": [
                            {
                                "label": "Số ngày nằm ICU",
                                "key": "SONGAYICU",
                                "customClass": "pr-2",
                                "type": "textfield",
                            },
                        ],
                        "width": 6,
                        "size": "md",
                    },
                    {
                        "components": [
                            {
                                "label": "Cân nặng trẻ <1 tuổi(gr)",
                                "key": "CANNANGTRE",
                                "type": "textfield",
                            },
                        ],
                        "width": 6,
                        "size": "md",
                    },
                ],
                "key": "columns_nhin",
                "type": "columns",
                "customClass": "ml-0 mr-0 pb-2",
            },
            {
                "label": "Đối tượng người bệnh: Tiên lượng nặng xin về",
                others: {
                    "data": {
                        "values": [
                            {
                                "label": "Không",
                                "value": "1"
                            },
                            {
                                "label": "Có",
                                "value": "2"
                            },
                        ]
                    },
                    defaultValue: 1,
                },
                "key": "TIENLUONG",
                "type": "select",
            },
            {
                "label": "Nguyên nhân TV, chuỗi sự kiện",
                "tableView": false,
                "rowDrafts": false,
                "key": "NNTUVONGJSON",
                "type": "editgrid",
                "displayAsTable": false,
                "input": true,
                "components": [
                    {
                        "label": "Columns",
                        "columns": [
                            {
                                "components": [
                                    {
                                        "label": "Nguyên nhân TV, chuỗi sự kiện",
                                        "key": "BENHLY",
                                        "type": "textfield",
                                        customClass: "pr-2",
                                    }
                                ],
                                "width": 5,
                                "size": "md"
                            },
                            {
                                "components": [
                                    {
                                        "label": "Mã ICD",
                                        "key": "ICD",
                                        "type": "textfield",
                                        customClass: "pr-2",
                                    }
                                ],
                                "width": 2,
                                "size": "md"
                            },
                            {
                                "components": [
                                    {
                                        "label": "Thời gian",
                                        "key": "THOIGIAN",
                                        "type": "number",
                                        customClass: "pr-2",
                                    }
                                ],
                                "width": 2,
                                "size": "md"
                            },
                            {
                                "components": [
                                    {
                                        "label": "Đơn vị tính",
                                        "key": "DONVI",
                                        "type": "textfield",
                                    }
                                ],
                                "size": "md",
                                "width": 2
                            },
                        ],
                        "key": "columns",
                        "type": "columns",
                    }
                ],
                others: {
                    "tooltip": "Chuỗi bệnh lý, sự kiện từ khi khởi phát nguyên nhân đến khi nặng xin về",
                    "templates": {
                        "header": "<div class=\"row\">\n" +
                            "  {% util.eachComponent(components, function(component) { %}\n" +
                            "    {% if (component.key == 'BENHLY') { %}\n" +
                            "      <div class=\"col-sm-5\">{{ t(component.label) }}</div>\n" +
                            "    {% } %}\n" +
                            "    {% if (component.key == 'ICD') { %}\n" +
                            "      <div class=\"col-sm-2\">{{ t(component.label) }}</div>\n" +
                            "    {% } %}\n" +
                            "    {% if (component.key == 'THOIGIAN') { %}\n" +
                            "      <div class=\"col-sm-2\">{{ t(component.label) }}</div>\n" +
                            "    {% } %}\n" +
                            "    {% if (component.key == 'DONVI') { %}\n" +
                            "      <div class=\"col-sm-2\">{{ t(component.label) }}</div>\n" +
                            "    {% } %}\n" +
                            "  {% }) %}\n" +
                            "</div>",
                        "row": "<div class=\"row\">\n" +
                            "  {% util.eachComponent(components, function(component) { %}\n" +
                            "    {% if (component.key == 'BENHLY') { %}\n" +
                            "      <div class=\"col-sm-5\">\n" +
                            "        {{ isVisibleInRow(component) ? getView(component, row[component.key]) : ''}}\n" +
                            "      </div>\n" +
                            "    {% } %}\n" +
                            "    {% if (component.key == 'ICD') { %}\n" +
                            "      <div class=\"col-sm-2\">\n" +
                            "        {{ isVisibleInRow(component) ? getView(component, row[component.key]) : ''}}\n" +
                            "      </div>\n" +
                            "    {% } %}\n" +
                            "    {% if (component.key == 'THOIGIAN') { %}\n" +
                            "      <div class=\"col-sm-2\">\n" +
                            "        {{ isVisibleInRow(component) ? getView(component, row[component.key]) : ''}}\n" +
                            "      </div>\n" +
                            "    {% } %}\n" +
                            "    {% if (component.key == 'DONVI') { %}\n" +
                            "      <div class=\"col-sm-2\">\n" +
                            "        {{ isVisibleInRow(component) ? getView(component, row[component.key]) : ''}}\n" +
                            "      </div>\n" +
                            "    {% } %}\n" +
                            "  {% }) %}\n" +
                            "  {% if (!instance.options.readOnly && !instance.disabled) { %}\n" +
                            "    <div class=\"col-sm-1\">\n" +
                            "      <div class=\"btn-group pull-right\">\n" +
                            "        <button class=\"btn btn-default btn-primary btn-sm editRow\"><i class=\"fa fa-pencil-square-o\"></i></button>\n" +
                            "        {% if (!instance.hasRemoveButtons || instance.hasRemoveButtons()) { %}\n" +
                            "          <button class=\"btn btn-danger btn-sm removeRow\"><i class=\"fa fa-trash-o\"></i></button>\n" +
                            "        {% } %}\n" +
                            "      </div>\n" +
                            "    </div>\n" +
                            "  {% } %}\n" +
                            "</div>"
                    },
                    "addAnother": "Thêm mới",
                    "saveRow": "Lưu",
                    "removeRow": "Hủy",
                }
            },
            {
                "label": "Bệnh lý khác",
                "tableView": false,
                "rowDrafts": false,
                "key": "BENHKHACJSON",
                "type": "editgrid",
                "displayAsTable": false,
                "input": true,
                "components": [
                    {
                        "label": "Columns",
                        "columns": [
                            {
                                "components": [
                                    {
                                        "label": "Bệnh lý kèm theo",
                                        "key": "BENHLY",
                                        "type": "textfield",
                                        customClass: "pr-2",
                                    }
                                ],
                                "width": 5,
                                "size": "md"
                            },
                            {
                                "components": [
                                    {
                                        "label": "Mã ICD",
                                        "key": "ICD",
                                        "type": "textfield",
                                        customClass: "pr-2",
                                    }
                                ],
                                "width": 2,
                                "size": "md"
                            },
                            {
                                "components": [
                                    {
                                        "label": "Thời gian",
                                        "key": "THOIGIAN",
                                        "type": "number",
                                        customClass: "pr-2",
                                    }
                                ],
                                "width": 2,
                                "size": "md"
                            },
                            {
                                "components": [
                                    {
                                        "label": "Đơn vị tính",
                                        "key": "DONVI",
                                        "type": "textfield",
                                    }
                                ],
                                "size": "md",
                                "width": 2
                            },
                        ],
                        "key": "columns",
                        "type": "columns",
                    }
                ],
                others: {
                    "tooltip": "Bệnh lý, nguy cơ quan trọng khác góp phần vào TV",
                    "templates": {
                        "header": "<div class=\"row\">\n" +
                            "  {% util.eachComponent(components, function(component) { %}\n" +
                            "    {% if (component.key == 'BENHLY') { %}\n" +
                            "      <div class=\"col-sm-5\">{{ t(component.label) }}</div>\n" +
                            "    {% } %}\n" +
                            "    {% if (component.key == 'ICD') { %}\n" +
                            "      <div class=\"col-sm-2\">{{ t(component.label) }}</div>\n" +
                            "    {% } %}\n" +
                            "    {% if (component.key == 'THOIGIAN') { %}\n" +
                            "      <div class=\"col-sm-2\">{{ t(component.label) }}</div>\n" +
                            "    {% } %}\n" +
                            "    {% if (component.key == 'DONVI') { %}\n" +
                            "      <div class=\"col-sm-2\">{{ t(component.label) }}</div>\n" +
                            "    {% } %}\n" +
                            "  {% }) %}\n" +
                            "</div>",
                        "row": "<div class=\"row\">\n" +
                            "  {% util.eachComponent(components, function(component) { %}\n" +
                            "    {% if (component.key == 'BENHLY') { %}\n" +
                            "      <div class=\"col-sm-5\">\n" +
                            "        {{ isVisibleInRow(component) ? getView(component, row[component.key]) : ''}}\n" +
                            "      </div>\n" +
                            "    {% } %}\n" +
                            "    {% if (component.key == 'ICD') { %}\n" +
                            "      <div class=\"col-sm-2\">\n" +
                            "        {{ isVisibleInRow(component) ? getView(component, row[component.key]) : ''}}\n" +
                            "      </div>\n" +
                            "    {% } %}\n" +
                            "    {% if (component.key == 'THOIGIAN') { %}\n" +
                            "      <div class=\"col-sm-2\">\n" +
                            "        {{ isVisibleInRow(component) ? getView(component, row[component.key]) : ''}}\n" +
                            "      </div>\n" +
                            "    {% } %}\n" +
                            "    {% if (component.key == 'DONVI') { %}\n" +
                            "      <div class=\"col-sm-2\">\n" +
                            "        {{ isVisibleInRow(component) ? getView(component, row[component.key]) : ''}}\n" +
                            "      </div>\n" +
                            "    {% } %}\n" +
                            "  {% }) %}\n" +
                            "  {% if (!instance.options.readOnly && !instance.disabled) { %}\n" +
                            "    <div class=\"col-sm-1\">\n" +
                            "      <div class=\"btn-group pull-right\">\n" +
                            "        <button class=\"btn btn-default btn-primary btn-sm editRow\"><i class=\"fa fa-pencil-square-o\"></i></button>\n" +
                            "        {% if (!instance.hasRemoveButtons || instance.hasRemoveButtons()) { %}\n" +
                            "          <button class=\"btn btn-danger btn-sm removeRow\"><i class=\"fa fa-trash-o\"></i></button>\n" +
                            "        {% } %}\n" +
                            "      </div>\n" +
                            "    </div>\n" +
                            "  {% } %}\n" +
                            "</div>"
                    },
                    "addAnother": "Thêm mới",
                    "saveRow": "Lưu",
                    "removeRow": "Hủy",
                }
            },
            {
                "label": "Có phẫu thuật trong 4 tuần",
                others: {
                    "data": {
                        "values": [
                            {
                                "label": "Không",
                                "value": "1"
                            },
                            {
                                "label": "Có",
                                "value": "2"
                            },
                            {
                                "label": "Không biết",
                                "value": "3"
                            },
                        ]
                    },
                    defaultValue: 1,
                },
                "customClass": "pr-2",
                "key": "PHAUTHUAT4TUAN",
                "type": "select",
                validate: {
                    required: true
                }
            },
            {
                "label": "Columns",
                "columns": [
                    {
                        "components": [
                            {
                                "label": "Lý do phẫu thuật",
                                "key": "LYDOPHAUTHUAT4TUAN",
                                "type": "textfield",
                                customClass: "pr-2",
                            }
                        ],
                        "width": 6,
                        "size": "md"
                    },
                    {
                        "components": [
                            {
                                "label": "Ngày phẫu thuật",
                                "key": "THOIGIAN4TUAN",
                                "type": "datetime",
                                format: "dd/MM/yyyy",
                                "customClass": "pr-2",
                                enableTime: true,
                            },
                        ],
                        "width": 6,
                        "size": "md"
                    },
                ],
                others: {
                    "customConditional": "show = data.PHAUTHUAT4TUAN == 2;",
                },
                "key": "columns",
                "type": "columns",
                "customClass": "ml-0 mr-0 pb-2",
            },
            {
                "label": "Hình thức nhập viện",
                others: {
                    "data": {
                        "values": [
                            {
                                "label": "Bệnh tật",
                                "value": "1"
                            },
                            {
                                "label": "Bị tấn công, đánh nhau",
                                "value": "2"
                            },
                            {
                                "label": "Không thể xác định",
                                "value": "3"
                            },
                            {
                                "label": "Tai nạn",
                                "value": "4"
                            },
                            {
                                "label": "Can thiệp pháp lý",
                                "value": "5"
                            },
                            {
                                "label": "Chờ điều tra",
                                "value": "6"
                            },
                            {
                                "label": "Cố tình tự hại (tự tử)",
                                "value": "7"
                            },
                            {
                                "label": "Chiến tranh",
                                "value": "8"
                            },
                            {
                                "label": "Không biết",
                                "value": "9"
                            },
                        ]
                    },
                    defaultValue: 1,
                },
                "customClass": "pr-2",
                "key": "HINHTHUCTV",
                "type": "select",
                validate: {
                    required: true
                }
            },
            {
                "label": "",
                "columns": [
                    {
                        "components": [
                            {
                                "label": "Tên nguyên nhân bên ngoài, độc chất",
                                "key": "TENNNBENNGOAI",
                                "type": "textfield",
                                "customClass": "pr-2",
                                others: {
                                    "tooltip": "(tai nạn, ngộ độc, đánh nhau, đuối nước...)",
                                },
                            },
                        ],
                        "width": 3,
                        "size": "md",
                    },
                    {
                        "components": [
                            {
                                "label": "Mô tả xảy ra như nào",
                                "key": "MOTANNBENNGOAI",
                                "type": "textfield",
                                "customClass": "pr-2",
                                others: {
                                    "tooltip": "(tai nạn, ngộ độc, đánh nhau, đuối nước...)",
                                },
                            },
                        ],
                        "width": 3,
                        "size": "md",
                    },
                    {
                        "components": [
                            {
                                "label": "Mã ICD",
                                "key": "MAICDNNBENNGOAI",
                                "customClass": "pr-2",
                                "type": "textfield",
                            },
                        ],
                        "width": 2,
                        "size": "md",
                    },
                    {
                        "components": [
                            {
                                "label": "Tên theo ICD",
                                "key": "TENICDNNBENNGOAI",
                                "customClass": "pr-2",
                                "type": "textfield",
                            },
                        ],
                        "width": 2,
                        "size": "md",
                    },
                    {
                        "components": [
                            {
                                "label": "Ngày xảy ra",
                                "key": "NGAYXAYRA",
                                "type": "datetime",
                                format: "dd/MM/yyyy",
                                "customClass": "pr-2",
                                enableTime: false,
                            },
                        ],
                        "width": 2,
                        "size": "md",
                    },
                ],
                "key": "columns_nhin",
                "type": "columns",
                "customClass": "ml-0 mr-0 pb-2",
            },
            {
                "label": "Nơi xảy ra tai nạn",
                others: {
                    "data": {
                        "values": [
                            {
                                "label": "Tại nhà",
                                "value": "1"
                            },
                            {
                                "label": "Khu dân cư",
                                "value": "2"
                            },
                            {
                                "label": "Trường học, khu hành chính khác",
                                "value": "3"
                            },
                            {
                                "label": "Khu thể thao, luyện tập",
                                "value": "4"
                            },
                            {
                                "label": "Trên đường đi",
                                "value": "5"
                            },
                            {
                                "label": "Khu thương mại, dịch vụ",
                                "value": "6"
                            },
                            {
                                "label": "Khu công nghiệp hoặc công trường xây dựng",
                                "value": "7"
                            },
                            {
                                "label": "Nông trại",
                                "value": "8"
                            },
                            {
                                "label": "Địa điểm khác",
                                "value": "9"
                            },
                            {
                                "label": "Không biết",
                                "value": "10"
                            },
                        ]
                    },
                    defaultValue: 1,
                },
                "key": "NOIXAYRATAINAN",
                "type": "select",
                validate: {
                    required: true
                }
            },
            {
                "label": "Địa điểm",
                "key": "DIADIEM",
                "type": "textfield",
                others: {
                    "customConditional": "show = data.NOIXAYRATAINAN == 9;",
                }
            },
            {
                "label": "Người bệnh là thai nhi hoặc trẻ sơ sinh",
                others: {
                    "data": {
                        "values": [
                            {
                                "label": "Không",
                                "value": "1"
                            },
                            {
                                "label": "Có",
                                "value": "2"
                            },
                        ]
                    },
                    defaultValue: 1,
                },
                "key": "TUVONGTHAINHI",
                "type": "select",
                validate: {
                    required: true
                }
            },
            {
                "label": "",
                "columns": [
                    {
                        "components": [
                            {
                                "label": "Đa thai",
                                others: {
                                    "data": {
                                        "values": [
                                            {
                                                "label": "Không",
                                                "value": "1"
                                            },
                                            {
                                                "label": "Có",
                                                "value": "2"
                                            },
                                            {
                                                "label": "Không biết",
                                                "value": "3"
                                            },
                                        ]
                                    },
                                    defaultValue: 1,
                                },
                                "customClass": "pr-2",
                                "key": "DATHAI",
                                "type": "select",
                                validate: {
                                    required: true
                                }
                            },
                        ],
                        "width": 6,
                        "size": "md",
                    },
                    {
                        "components": [
                            {
                                "label": "Sinh non",
                                others: {
                                    "data": {
                                        "values": [
                                            {
                                                "label": "Không",
                                                "value": "1"
                                            },
                                            {
                                                "label": "Có",
                                                "value": "2"
                                            },
                                            {
                                                "label": "Không biết",
                                                "value": "3"
                                            },
                                        ]
                                    },
                                    defaultValue: 1,
                                },
                                "key": "SINHNON",
                                "type": "select",
                                validate: {
                                    required: true
                                }
                            },
                        ],
                        "width": 6,
                        "size": "md",
                    },
                ],
                others: {
                    "customConditional": "show = data.TUVONGTHAINHI == 2;",
                },
                "key": "columns_nhin",
                "type": "columns",
                "customClass": "ml-0 mr-0 pb-2",
            },
            {
                "label": "Người nhà",
                "columns": [
                    {
                        "components": [
                            {
                                "label": "Số giờ (Nếu xin về trong 24h)",
                                "key": "SOGIOSONG",
                                "customClass": "pr-2",
                                "type": "textfield",
                            },
                        ],
                        "width": 3,
                    },
                    {
                        "components": [
                            {
                                "label": "Tuổi thai (tuần)",
                                "key": "TUOITHAI",
                                "customClass": "pr-2",
                                "type": "textfield",
                            },
                        ],
                        "width": 3,
                    },
                    {
                        "components": [
                            {
                                "label": "Tuổi mẹ (năm)",
                                "key": "TUOIME",
                                "customClass": "pr-2",
                                "type": "textfield",
                            },
                        ],
                        "width": 3,
                    },
                    {
                        "components": [
                            {
                                "label": "Cân nặng khi sinh (gr)",
                                "key": "CANNANGKHISINH",
                                "type": "textfield",
                            },
                        ],
                        "width": 3,
                    },
                ],
                others: {
                    "customConditional": "show = data.TUVONGTHAINHI == 2;",
                },
                "customClass": "ml-0 mr-0",
                "key": "THOIGIANTUVAN",
                "type": "columns",
            },
            {
                "label": "",
                "columns": [
                    {
                        "components": [
                            {
                                "label": "Bệnh lý của mẹ",
                                "key": "BENHLYCUAME",
                                "type": "textfield",
                                "customClass": "pr-2",
                                others: {
                                    "tooltip": "Bệnh lý của mẹ ảnh hưởng đến thai nhi, trẻ sơ sinh",
                                },
                            },
                        ],
                        "width": 6,
                        "size": "md",
                    },
                    {
                        "components": [
                            {
                                "label": "Mã ICD",
                                "key": "MAICDBENHLYME",
                                "customClass": "pr-2",
                                "type": "textfield",
                            },
                        ],
                        "width": 3,
                        "size": "md",
                    },
                    {
                        "components": [
                            {
                                "label": "Tên theo ICD",
                                "key": "TENICDBENHLYME",
                                "type": "textfield",
                            },
                        ],
                        "width": 3,
                        "size": "md",
                    },
                ],
                others: {
                    "customConditional": "show = data.TUVONGTHAINHI == 2;",
                },
                "key": "columns_nhin",
                "type": "columns",
                "customClass": "ml-0 mr-0 pb-2",
            },
            {
                "tag": "label",
                "content": "<b>Đối với phụ nữ</b>",
                "refreshOnChange": false,
                "key": "htmllabel_chandoan",
                "type": "htmlelement",
            },
            {
                "label": "Có đang mang thai không",
                others: {
                    "data": {
                        "values": [
                            {
                                "label": "Không",
                                "value": "1"
                            },
                            {
                                "label": "Có",
                                "value": "2"
                            },
                            {
                                "label": "Không biết",
                                "value": "3"
                            },
                        ]
                    },
                    defaultValue: 1,
                },
                "key": "DANGMANGTHAI",
                "type": "select",
                validate: {
                    required: true
                }
            },
            {
                "label": "",
                "columns": [
                    {
                        "components": [
                            {
                                "label": "Thời điểm mang thai",
                                others: {
                                    "data": {
                                        "values": [
                                            {
                                                "label": "Tại thời điểm xin về",
                                                "value": "1"
                                            },
                                            {
                                                "label": "Trong vòng 42 ngày trước khi xin về",
                                                "value": "2"
                                            },
                                            {
                                                "label": "Từ ngày thứ 43 đến 1 năm trước khi xin về",
                                                "value": "3"
                                            },
                                            {
                                                "label": "Không biết",
                                                "value": "4"
                                            },
                                        ]
                                    },
                                    defaultValue: 1,
                                },
                                "customClass": "pr-2",
                                "key": "THOIDIEMMANGTHAI",
                                "type": "select",
                                validate: {
                                    required: true
                                }
                            },
                        ],
                        "width": 6,
                        "size": "md",
                    },
                    {
                        "components": [
                            {
                                "label": "Việc mang thai có góp phần gây tử vong",
                                others: {
                                    "data": {
                                        "values": [
                                            {
                                                "label": "Không",
                                                "value": "1"
                                            },
                                            {
                                                "label": "Có",
                                                "value": "2"
                                            },
                                            {
                                                "label": "Không biết",
                                                "value": "3"
                                            },
                                        ]
                                    },
                                    defaultValue: 1,
                                },
                                "key": "GOPPHANNANG",
                                "type": "select",
                                validate: {
                                    required: true
                                }
                            },
                        ],
                        "width": 6,
                        "size": "md",
                    },
                ],
                others: {
                    "customConditional": "show = data.DANGMANGTHAI == 2;",
                },
                "key": "columns_nhin",
                "type": "columns",
                "customClass": "ml-0 mr-0 pb-2",
            },
            {
                "tag": "label",
                "content": "<b>Kết luận nguyên nhân chính</b>",
                "refreshOnChange": false,
                "key": "htmllabel_chandoan",
                "type": "htmlelement",
            },
            {
                "label": "",
                "columns": [
                    {
                        "components": [
                            {
                                "label": "Kết luận",
                                "key": "KETLUANTENBL",
                                "type": "textfield",
                                "customClass": "pr-2",
                            },
                        ],
                        "width": 6,
                        "size": "md",
                    },
                    {
                        "components": [
                            {
                                "label": "Mã ICD",
                                "key": "KETLUANMAICD",
                                "type": "textfield",
                                "customClass": "pr-2",
                            },
                        ],
                        "width": 3,
                        "size": "md",
                    },
                    {
                        "components": [
                            {
                                "label": "Tên theo ICD",
                                "key": "KETLUANTENICD",
                                "type": "textfield",
                            },
                        ],
                        "width": 3,
                        "size": "md",
                    },
                ],
                "key": "columns_nhin",
                "type": "columns",
                "customClass": "ml-0 mr-0 pb-2",
            },

            {
                "label": "",
                "columns": [
                    {
                        "components": [
                            {
                                "label": "Bác sĩ điều trị",
                                others: {
                                    "data": {
                                        "values": singletonObject.danhsachnhanvienFormio
                                    },
                                    defaultValue: singletonObject.userId,
                                },
                                "customClass": "pr-2",
                                "key": "BSDIEUTRI",
                                "type": "select",
                            },
                        ],
                        "width": 6,
                        "size": "md",
                    },
                    {
                        "components": [
                            {
                                "label": "Trưởng khoa điều trị",
                                others: {
                                    "data": {
                                        "values": singletonObject.danhsachnhanvienFormio
                                    },
                                    defaultValue: singletonObject.userId,
                                },
                                "customClass": "pr-2",
                                "key": "TRUONGKHOA",
                                "type": "select",
                            },
                        ],
                        "width": 6,
                        "size": "md",
                    },
                ],
                "key": "columns_nhin",
                "type": "columns",
                "customClass": "ml-0 mr-0 pb-2",
            },
            {
                "label": "",
                "columns": [
                    {
                        "components": [
                            {
                                "label": "Thủ trưởng đơn vị",
                                others: {
                                    "data": {
                                        "values": singletonObject.danhsachnhanvienFormio
                                    },
                                    defaultValue: singletonObject.userId,
                                },
                                "customClass": "pr-2",
                                "key": "THUTRUONG",
                                "type": "select",
                            },
                        ],
                        "width": 6,
                        "size": "md",
                    },
                    {
                        "components": [
                            {
                                "label": "Ngày tạo phiếu",
                                "key": "NGAY_TAO_PHIEU",
                                "type": "datetime",
                                format: "dd/MM/yyyy",
                                "customClass": "pr-2",
                                enableTime: false,
                                minDate: moment(thongtinhsba.thongtinbn.NGAY_VAO_VIEN, ['DD/MM/YYYY']).format("YYYY-MM-DD"),
                                "validate": {
                                    "required": true
                                },
                            },
                        ],
                        "width": 6,
                        "size": "md",
                    },
                ],
                "key": "columns_nhin",
                "type": "columns",
                "customClass": "ml-0 mr-0 pb-2",
            },


        ])
        Formio.createForm(document.getElementById('formNhapTTTTBenhNangXinVe'),
            jsonForm,{}
        ).then(function(form) {
            formTTTTBenhNangXinVe = form;
            // var tenBenhphuElement = form.getComponent('TENICD_CHANDOAN');
            // var icdBenhphuElement = form.getComponent('ICD_CHANDOAN');
            var chuoiBenhLyComponent = form.getComponent('NNTUVONGJSON');
            var benhLyKhacComponent = form.getComponent('BENHKHACJSON');
            // var PTTTComponent = form.getComponent('PHAUTHUATTHUTHUAT');

            if (chuoiBenhLyComponent) {
                var originalAddRow = chuoiBenhLyComponent.addRow;
                chuoiBenhLyComponent.addRow = function() {
                    var currentRows = this.dataValue || [];
                    if (currentRows.length >= 4) {
                        notifiToClient("Red", "Chỉ được phép thêm tối đa 4 dòng dữ liệu!");
                        return Promise.resolve();
                    }
                    return originalAddRow.call(this);
                };
            }
            if (benhLyKhacComponent) {
                var originalAddRow = benhLyKhacComponent.addRow;
                benhLyKhacComponent.addRow = function() {
                    var currentRows = this.dataValue || [];
                    if (currentRows.length >= 1) {
                        notifiToClient("Red", "Chỉ được phép thêm tối đa 1 dòng dữ liệu!");
                        return Promise.resolve();
                    }
                    return originalAddRow.call(this);
                };
            }
            // if (PTTTComponent) {
            //     var originalAddRow = PTTTComponent.addRow;
            //     PTTTComponent.addRow = function() {
            //         var currentRows = this.dataValue || [];
            //         if (currentRows.length >= 1) {
            //             notifiToClient("Red", "Chỉ được phép thêm tối đa 1 dòng dữ liệu!");
            //             return Promise.resolve();
            //         }
            //         return originalAddRow.call(this);
            //     };
            // }

            // $("#"+getIdElmentFormio(form,'ICD_CHANDOAN')).on('keypress', function(event) {
            //     var mabenhICD = $(this).val();
            //     if(event.keyCode == 13 && mabenhICD != "") {
            //         mabenhICD = mabenhICD.toUpperCase();
            //         getMotabenhly(mabenhICD, function(data) {
            //             var splitIcd = data.split("!!!")
            //             tenBenhphuElement.setValue(splitIcd[1]);
            //             tenBenhphuElement.focus()
            //             icdBenhphuElement.setValue(mabenhICD)
            //         })
            //     }
            // })
            // combgridTenICD(getIdElmentFormio(form,'TENICD_CHANDOAN'), function(item) {
            //     icdBenhphuElement.setValue(item.ICD);
            //     tenBenhphuElement.setValue(item.MO_TA_BENH_LY);
            // });

        });
    }

    function reloadDSTTTTBenhNangXinVe(){
        var url = "cmu_getlist?url=" + convertArray([singletonObject.dvtt, thongtinhsba.thongtinbn.SOVAOVIEN, "CMU_GET_TTTTBENHNANGXINVE"]);
        $.get(url).done(function(data){
            if (data && data.length > 0) {
                $("#data_ttttbenhnangxinve").html(thongtinhsba.thongtinbn.TEN_PHONGBAN + ' - ' + data[0].NGAY_TAO_PHIEU);
                thongTinTTTTBenhNangXinVeMoiNhat = data[0];
                if (data[0].KEYSIGN){
                    $('#edit_single_ttttbenhnangxinve').css('visibility', 'hidden');
                    $('#delete_single_ttttbenhnangxinve').css('visibility', 'hidden');
                }else{
                    $('#handle_icon_ttttbenhnangxinve').css('visibility', 'unset');
                    $('#view_single_ttttbenhnangxinve').css('visibility', 'unset');
                    $('#edit_single_ttttbenhnangxinve').css('visibility', 'unset');
                    $('#delete_single_ttttbenhnangxinve').css('visibility', 'unset');
                }
            } else  {
                $("#data_ttttbenhnangxinve").html('Không có dữ liệu');
                $('#handle_icon_ttttbenhnangxinve').css('visibility', 'hidden');
            }
        });
        $("#list_ttttbenhnangxinve").jqGrid('setGridParam', {
            datatype: 'json',
            url: url
        }).trigger('reloadGrid')
        hideLoaderIntoWrapId("list_ttcs-bdcdct-wrap");
        // showFormTTTTBenhNangXinVe();
    }

    function themTTTTBenhNangXinVe() {
        showSelfLoading("ttttbenhnangxinve_luu");
        formTTTTBenhNangXinVe.emit("checkValidity");
        if (!formTTTTBenhNangXinVe.checkValidity(null, false, null, true)) {
            hideSelfLoading("ttttbenhnangxinve_luu");
            return;
        }
        var url;
        var dataSubmit = formTTTTBenhNangXinVe.submission.data;
        var jsonThongTinBenh = {
            BENHAN: dataSubmit.BENHAN,
            MAHSBA: dataSubmit.MAHSBA,
            SODINHDANH: dataSubmit.SODINHDANH,
            LOAI: dataSubmit.LOAI,
            SOTHEBHYT: dataSubmit.SOTHEBHYT,
            GIATRITU: dataSubmit.GIATRITU ? moment(dataSubmit.GIATRITU).format("DD/MM/YYYY") : "",
            GIATRIDEN: dataSubmit.GIATRIDEN ? moment(dataSubmit.GIATRIDEN).format("DD/MM/YYYY") : "",
            NOIDANGKY: dataSubmit.NOIDANGKY,
            THOIGIAN5NAM: dataSubmit.THOIGIAN5NAM ? moment(dataSubmit.THOIGIAN5NAM).format("DD/MM/YYYY") : "",
            MIENCUNGCHITRA: dataSubmit.MIENCUNGCHITRA ? moment(dataSubmit.MIENCUNGCHITRA).format("DD/MM/YYYY") : "",
            DIACHIHTTINH: dataSubmit.DIACHIHTTINH,
            DIACHIHTHUYEN: dataSubmit.DIACHIHTHUYEN,
            DIACHIHTXA: dataSubmit.DIACHIHTXA,
            DIACHIHTSONHA: dataSubmit.DIACHIHTSONHA,
            DIACHITRTINH: dataSubmit.DIACHITRTINH,
            DIACHITRHUYEN: dataSubmit.DIACHITRHUYEN,
            DIACHITRXA: dataSubmit.DIACHITRXA,
            DIACHITRSONHA: dataSubmit.DIACHITRSONHA,
            DIACHIBENHTINH: dataSubmit.DIACHIBENHTINH,
            DIACHIBENHHUYEN: dataSubmit.DIACHIBENHHUYEN,
            DIACHIBENHXA: dataSubmit.DIACHIBENHXA,
            DIACHIBENHSONHA: dataSubmit.DIACHIBENHSONHA,
            TONGIAO: dataSubmit.TONGIAO,
            SONGAYVANGMAT: dataSubmit.SONGAYVANGMAT,
            LOAIVAOVIEN: dataSubmit.LOAIVAOVIEN,
            NGAYRAVIEN: dataSubmit.NGAYRAVIEN ? moment(dataSubmit.NGAYRAVIEN).format("DD/MM/YYYY") : "",
            CHUYENTU: dataSubmit.CHUYENTU,
            CHUYENDEN: dataSubmit.CHUYENDEN,
            TINHTRANGRV: dataSubmit.TINHTRANGRV,
            KETQUADT: dataSubmit.KETQUADT,
            SONGAYICU: dataSubmit.SONGAYICU,
            CANNANGTRE: dataSubmit.CANNANGTRE,
            TIENLUONG: dataSubmit.TIENLUONG,
        }
        var jsonPhauThuat4Tuan = {
            PHAUTHUAT4TUAN: dataSubmit.PHAUTHUAT4TUAN,
            LYDOPHAUTHUAT4TUAN: dataSubmit.LYDOPHAUTHUAT4TUAN,
            THOIGIAN4TUAN: dataSubmit.THOIGIAN4TUAN ? moment(dataSubmit.THOIGIAN4TUAN).format("DD/MM/YYYY") : "",
        }
        var jsonNguyenNhanBN = {
            TENNNBENNGOAI: dataSubmit.TENNNBENNGOAI,
            MOTANNBENNGOAI: dataSubmit.MOTANNBENNGOAI,
            MAICDNNBENNGOAI: dataSubmit.MAICDNNBENNGOAI,
            TENICDNNBENNGOAI: dataSubmit.TENICDNNBENNGOAI,
            NGAYXAYRA: dataSubmit.NGAYXAYRA ? moment(dataSubmit.NGAYXAYRA).format("DD/MM/YYYY") : "",
            NOIXAYRATAINAN: dataSubmit.NOIXAYRATAINAN,
            DIADIEM: dataSubmit.DIADIEM,
        }
        var jsonThaiNhiSS = {
            TUVONGTHAINHI: dataSubmit.TUVONGTHAINHI,
            DATHAI: dataSubmit.DATHAI,
            SINHNON: dataSubmit.SINHNON,
            TUOITHAI: dataSubmit.TUOITHAI,
            TUOIME: dataSubmit.TUOIME,
            SOGIOSONG: dataSubmit.SOGIOSONG,
            CANNANGKHISINH: dataSubmit.CANNANGKHISINH,
            BENHLYCUAME: dataSubmit.BENHLYCUAME,
            MAICDBENHLYME: dataSubmit.MAICDBENHLYME,
            TENICDBENHLYME: dataSubmit.TENICDBENHLYME,
        }
        var jsonDangMangThai = {
            DANGMANGTHAI: dataSubmit.DANGMANGTHAI,
            THOIDIEMMANGTHAI: dataSubmit.THOIDIEMMANGTHAI,
            GOPPHANNANG: dataSubmit.GOPPHANNANG,
        }
        var jsonKetLuan = {
            KETLUANTENBL: dataSubmit.KETLUANTENBL,
            KETLUANMAICD: dataSubmit.KETLUANMAICD,
            KETLUANTENICD: dataSubmit.KETLUANTENICD,
        }
        url = [
            singletonObject.dvtt,
            thongtinhsba.thongtinbn.SOVAOVIEN,
            thongtinhsba.thongtinbn.MA_BENH_NHAN,
            JSON.stringify(jsonThongTinBenh),
            JSON.stringify(dataSubmit.NNTUVONGJSON),
            JSON.stringify(dataSubmit.BENHKHACJSON),
            JSON.stringify(jsonPhauThuat4Tuan),
            dataSubmit.HINHTHUCTV,
            JSON.stringify(jsonNguyenNhanBN),
            JSON.stringify(jsonThaiNhiSS),
            JSON.stringify(jsonDangMangThai),
            JSON.stringify(jsonKetLuan),
            dataSubmit.BSDIEUTRI,
            dataSubmit.TRUONGKHOA,
            dataSubmit.THUTRUONG,
            moment(dataSubmit.NGAY_TAO_PHIEU).format("DD/MM/YYYY"),
            singletonObject.userId,
            singletonObject.makhoa,
            "CMU_TTTTBENHNANGXINVE_INSERT"
        ];

        $.post("cmu_post", {
            url: url.join('```')
        }).done(function (data) {
            if(data > 0){
                notifiToClient('Green', MESSAGEAJAX.SUCCESS);
                $("#modalFormTTTTBenhNangXinVe").modal("hide");
                var noidung = ["Số phiếu:"+ data]
                for (const key in dataSubmit) {
                    noidung.push(formTTTTBenhNangXinVe.getComponent(key).label.trim(":") + ": " + getValueOfFormIO(formTTTTBenhNangXinVe.getComponent(key)));
                }
                var dataLog = {
                    SOVAOVIEN: thongtinhsba.thongtinbn.SOVAOVIEN,
                    LOAI: LOGHSBALOAI.TTTTBENHNANGXINVE.KEY,
                    NOIDUNGBANDAU: "",
                    NOIDUNGMOI: noidung.join("; "),
                    USERID: singletonObject.userId,
                    ACTION: LOGHSBAACTION.INSERT.KEY,
                }
                luuLogHSBATheoBN(dataLog);
            } else {
                notifiToClient("Red",MESSAGEAJAX.ERROR);
            }
        }).fail(function(error) {
            notifiToClient("Red",MESSAGEAJAX.ERROR);
        }).always(function() {
            hideSelfLoading("ttttbenhnangxinve_luu");
            setTimeout(function(){
                reloadDSTTTTBenhNangXinVe();
            })
        });
    }

    function updateTTTTBenhNangXinVe() {
        showSelfLoading("ttttbenhnangxinve_luu");
        formTTTTBenhNangXinVe.emit("checkValidity");
        if (!formTTTTBenhNangXinVe.checkValidity(null, false, null, true)) {
            hideSelfLoading("ttttbenhnangxinve_luu");
            return;
        }
        var url;
        var dataSubmit = formTTTTBenhNangXinVe.submission.data;
        var jsonThongTinBenh = {
            BENHAN: dataSubmit.BENHAN,
            MAHSBA: dataSubmit.MAHSBA,
            SODINHDANH: dataSubmit.SODINHDANH,
            LOAI: dataSubmit.LOAI,
            SOTHEBHYT: dataSubmit.SOTHEBHYT,
            GIATRITU: moment(dataSubmit.GIATRITU).format("DD/MM/YYYY"),
            GIATRIDEN: moment(dataSubmit.GIATRIDEN).format("DD/MM/YYYY"),
            NOIDANGKY: dataSubmit.NOIDANGKY,
            THOIGIAN5NAM: moment(dataSubmit.THOIGIAN5NAM).format("DD/MM/YYYY"),
            MIENCUNGCHITRA: moment(dataSubmit.MIENCUNGCHITRA).format("DD/MM/YYYY"),
            DIACHIHTTINH: dataSubmit.DIACHIHTTINH,
            DIACHIHTHUYEN: dataSubmit.DIACHIHTHUYEN,
            DIACHIHTXA: dataSubmit.DIACHIHTXA,
            DIACHIHTSONHA: dataSubmit.DIACHIHTSONHA,
            DIACHITRTINH: dataSubmit.DIACHITRTINH,
            DIACHITRHUYEN: dataSubmit.DIACHITRHUYEN,
            DIACHITRXA: dataSubmit.DIACHITRXA,
            DIACHITRSONHA: dataSubmit.DIACHITRSONHA,
            DIACHIBENHTINH: dataSubmit.DIACHIBENHTINH,
            DIACHIBENHHUYEN: dataSubmit.DIACHIBENHHUYEN,
            DIACHIBENHXA: dataSubmit.DIACHIBENHXA,
            DIACHIBENHSONHA: dataSubmit.DIACHIBENHSONHA,
            TONGIAO: dataSubmit.TONGIAO,
            SONGAYVANGMAT: dataSubmit.SONGAYVANGMAT,
            LOAIVAOVIEN: dataSubmit.LOAIVAOVIEN,
            NGAYRAVIEN: moment(dataSubmit.NGAYRAVIEN).format("DD/MM/YYYY"),
            CHUYENTU: dataSubmit.CHUYENTU,
            CHUYENDEN: dataSubmit.CHUYENDEN,
            TINHTRANGRV: dataSubmit.TINHTRANGRV,
            KETQUADT: dataSubmit.KETQUADT,
            SONGAYICU: dataSubmit.SONGAYICU,
            CANNANGTRE: dataSubmit.CANNANGTRE,
            TIENLUONG: dataSubmit.TIENLUONG,
        }
        var jsonPhauThuat4Tuan = {
            PHAUTHUAT4TUAN: dataSubmit.PHAUTHUAT4TUAN,
            LYDOPHAUTHUAT4TUAN: dataSubmit.LYDOPHAUTHUAT4TUAN,
            THOIGIAN4TUAN: moment(dataSubmit.THOIGIAN4TUAN).format("DD/MM/YYYY"),
            TRUNGCAU: dataSubmit.TRUNGCAU,
            SUDUNGKETQUA: dataSubmit.SUDUNGKETQUA,
        }
        var jsonNguyenNhanBN = {
            TENNNBENNGOAI: dataSubmit.TENNNBENNGOAI,
            MOTANNBENNGOAI: dataSubmit.MOTANNBENNGOAI,
            MAICDNNBENNGOAI: dataSubmit.MAICDNNBENNGOAI,
            TENICDNNBENNGOAI: dataSubmit.TENICDNNBENNGOAI,
            NGAYXAYRA: moment(dataSubmit.NGAYXAYRA).format("DD/MM/YYYY"),
            NOIXAYRATAINAN: dataSubmit.NOIXAYRATAINAN,
            DIADIEM: dataSubmit.DIADIEM,
        }
        var jsonThaiNhiSS = {
            TUVONGTHAINHI: dataSubmit.TUVONGTHAINHI,
            DATHAI: dataSubmit.DATHAI,
            SINHNON: dataSubmit.SINHNON,
            TUVONGTHAINHI: dataSubmit.TUVONGTHAINHI,
            TUOITHAI: dataSubmit.TUOITHAI,
            TUOIME: dataSubmit.TUOIME,
            SOGIOSONG: dataSubmit.SOGIOSONG,
            CANNANGKHISINH: dataSubmit.CANNANGKHISINH,
            BENHLYCUAME: dataSubmit.BENHLYCUAME,
            MAICDBENHLYME: dataSubmit.MAICDBENHLYME,
            TENICDBENHLYME: dataSubmit.TENICDBENHLYME,
        }
        var jsonDangMangThai = {
            DANGMANGTHAI: dataSubmit.DANGMANGTHAI,
            THOIDIEMMANGTHAI: dataSubmit.THOIDIEMMANGTHAI,
            GOPPHANNANG: dataSubmit.GOPPHANNANG,
        }
        var jsonKetLuan = {
            KETLUANTENBL: dataSubmit.KETLUANTENBL,
            KETLUANMAICD: dataSubmit.KETLUANMAICD,
            KETLUANTENICD: dataSubmit.KETLUANTENICD,
        }
        url = [
            thongTinTTTTBenhNangXinVeTruocChinhSua.ID,
            singletonObject.dvtt,
            JSON.stringify(jsonThongTinBenh),
            JSON.stringify(dataSubmit.NNTUVONGJSON),
            JSON.stringify(dataSubmit.BENHKHACJSON),
            JSON.stringify(jsonPhauThuat4Tuan),
            dataSubmit.HINHTHUCTV,
            JSON.stringify(jsonNguyenNhanBN),
            JSON.stringify(jsonThaiNhiSS),
            JSON.stringify(jsonDangMangThai),
            JSON.stringify(jsonKetLuan),
            dataSubmit.BSDIEUTRI,
            dataSubmit.TRUONGKHOA,
            dataSubmit.THUTRUONG,
            moment(dataSubmit.NGAY_TAO_PHIEU).format("DD/MM/YYYY"),
            "CMU_TTTTBENHNANGXINVE_UPDATE"
        ];
        $.post("cmu_post", {
            url: url.join('```')
        }).done(function (data) {
            if(data > 0){
                notifiToClient('Green', 'Cập nhật phiếu thành công');
                $("#modalFormTTTTBenhNangXinVe").modal("hide");
                var noidungold = []
                var noidungnew = []
                var luutru = ""

                dataSubmit.NNTUVONGJSON = JSON.stringify(dataSubmit.NNTUVONGJSON)
                dataSubmit.BENHKHACJSON = JSON.stringify(dataSubmit.BENHKHACJSON)
                dataSubmit.NGAY_TAO_PHIEU = moment(dataSubmit.NGAY_TAO_PHIEU).format("DD/MM/YYYY")
                dataSubmit.GIATRITU = moment(dataSubmit.GIATRITU).format("DD/MM/YYYY")
                dataSubmit.GIATRIDEN = moment(dataSubmit.GIATRIDEN).format("DD/MM/YYYY")
                dataSubmit.THOIGIAN5NAM = moment(dataSubmit.THOIGIAN5NAM).format("DD/MM/YYYY")
                dataSubmit.MIENCUNGCHITRA = moment(dataSubmit.MIENCUNGCHITRA).format("DD/MM/YYYY")
                dataSubmit.NGAYRAVIEN = moment(dataSubmit.NGAYRAVIEN).format("DD/MM/YYYY")
                dataSubmit.THOIGIAN4TUAN = moment(dataSubmit.THOIGIAN4TUAN).format("DD/MM/YYYY")
                dataSubmit.NGAYXAYRA = moment(dataSubmit.NGAYXAYRA).format("DD/MM/YYYY")
                var diffObject = findDifferencesBetweenObjects(thongTinTTTTBenhNangXinVeTruocChinhSua, dataSubmit);
                for (const key in diffObject) {
                    try {
                        luutru = formTTTTBenhNangXinVe.getComponent(key).label
                        if (luutru) {
                            noidungold.push(luutru.trim(":") + ": " + thongTinTTTTBenhNangXinVeTruocChinhSua[key]);
                            noidungnew.push(luutru.trim(":") + ": " + getValueOfFormIO(formTTTTBenhNangXinVe.getComponent(key)));                        }
                    } catch (error) {
                        // console.log("Error: ", key);
                    }
                }
                var dataLog = {
                    SOVAOVIEN: thongtinhsba.thongtinbn.SOVAOVIEN,
                    LOAI: LOGHSBALOAI.TTTTBENHNANGXINVE.KEY,
                    NOIDUNGBANDAU: noidungold.join("; "),
                    NOIDUNGMOI: noidungnew.join("; "),
                    USERID: singletonObject.userId,
                    ACTION: LOGHSBAACTION.EDIT.KEY,
                }
                luuLogHSBATheoBN(dataLog);
            } else {
                notifiToClient("Red",MESSAGEAJAX.ERROR);
            }
        }).fail(function(error) {
            notifiToClient("Red",MESSAGEAJAX.ERROR);
        }).always(function() {
            hideSelfLoading("ttttbenhnangxinve_luu");
            setTimeout(function(){
                reloadDSTTTTBenhNangXinVe();
            })
        });
    }

    function instanceGridTTTTBenhNangXinVe(){
        if (!$("#list_ttttbenhnangxinve")[0].grid) {
            $("#list_ttttbenhnangxinve").jqGrid({
                datatype: "local",
                loadonce: false,
                height: 100,
                width: null,
                shrinkToFit: false,
                colModel: [
                    {
                        name: "KYSO",
                        label: "Ký số BS",
                        align: 'left',
                        width: 100,
                        formatter: function (cellValue, options, rowData) {
                            if (rowData.KEYSIGN_BACSI) {
                                return '<span class="cellWithoutBackground" style="font-weight:bold ;color: green">Đã ký</span>';
                            } else {
                                return '<span class="cellWithoutBackground" style="font-weight:bold ;color: red">Chưa ký</span>';
                            }
                        }
                    },
                    {
                        name: "KYSO",
                        label: "Ký số TK",
                        align: 'left',
                        width: 100,
                        formatter: function (cellValue, options, rowData) {
                            if (rowData.KEYSIGN_TRUONGKHOA) {
                                return '<span class="cellWithoutBackground" style="font-weight:bold ;color: green">Đã ký</span>';
                            } else {
                                return '<span class="cellWithoutBackground" style="font-weight:bold ;color: red">Chưa ký</span>';
                            }
                        }
                    },
                    {label: 'ID',name: 'ID', index: 'ID', width: 50, align: 'center'},
                    {label: 'BENHAN',name: 'BENHAN', index: 'BENHAN', width: 250, align: 'center', hidden: true},
                    {label: 'MAHSBA',name: 'MAHSBA', index: 'MAHSBA', width: 250, align: 'center', hidden: true},
                    {label: 'SODINHDANH',name: 'SODINHDANH', index: 'SODINHDANH', width: 250, align: 'center', hidden: true},
                    {label: 'LOAI',name: 'LOAI', index: 'LOAI', width: 250, align: 'center', hidden: true},
                    {label: 'SOTHEBHYT',name: 'SOTHEBHYT', index: 'SOTHEBHYT', width: 250, align: 'center', hidden: true},
                    {label: 'GIATRITU',name: 'GIATRITU', index: 'GIATRITU', width: 250, align: 'center', hidden: true},
                    {label: 'GIATRIDEN',name: 'GIATRIDEN', index: 'GIATRIDEN', width: 250, align: 'center', hidden: true},
                    {label: 'NOIDANGKY',name: 'NOIDANGKY', index: 'NOIDANGKY', width: 250, align: 'center', hidden: true},
                    {label: 'THOIGIAN5NAM',name: 'THOIGIAN5NAM', index: 'THOIGIAN5NAM', width: 250, align: 'center', hidden: true},
                    {label: 'MIENCUNGCHITRA',name: 'MIENCUNGCHITRA', index: 'MIENCUNGCHITRA', width: 250, align: 'center', hidden: true},
                    {label: 'DIACHIHTTINH',name: 'DIACHIHTTINH', index: 'DIACHIHTTINH', width: 250, align: 'center', hidden: true},
                    {label: 'DIACHIHTHUYEN',name: 'DIACHIHTHUYEN', index: 'DIACHIHTHUYEN', width: 250, align: 'center', hidden: true},
                    {label: 'DIACHIHTXA',name: 'DIACHIHTXA', index: 'DIACHIHTXA', width: 250, align: 'center', hidden: true},
                    {label: 'DIACHIHTSONHA',name: 'DIACHIHTSONHA', index: 'DIACHIHTSONHA', width: 250, align: 'center', hidden: true},
                    {label: 'DIACHITRTINH',name: 'DIACHITRTINH', index: 'DIACHITRTINH', width: 250, align: 'center', hidden: true},
                    {label: 'DIACHITRHUYEN',name: 'DIACHITRHUYEN', index: 'DIACHITRHUYEN', width: 250, align: 'center', hidden: true},
                    {label: 'DIACHITRXA',name: 'DIACHITRXA', index: 'DIACHITRXA', width: 250, align: 'center', hidden: true},
                    {label: 'DIACHITRSONHA',name: 'DIACHITRSONHA', index: 'DIACHITRSONHA', width: 250, align: 'center', hidden: true},
                    {label: 'DIACHIBENHTINH',name: 'DIACHIBENHTINH', index: 'DIACHIBENHTINH', width: 250, align: 'center', hidden: true},
                    {label: 'DIACHIBENHHUYEN',name: 'DIACHIBENHHUYEN', index: 'DIACHIBENHHUYEN', width: 250, align: 'center', hidden: true},
                    {label: 'DIACHIBENHXA',name: 'DIACHIBENHXA', index: 'DIACHIBENHXA', width: 250, align: 'center', hidden: true},
                    {label: 'DIACHIBENHSONHA',name: 'DIACHIBENHSONHA', index: 'DIACHIBENHSONHA', width: 250, align: 'center', hidden: true},
                    {label: 'TONGIAO',name: 'TONGIAO', index: 'TONGIAO', width: 250, align: 'center', hidden: true},
                    {label: 'SONGAYVANGMAT',name: 'SONGAYVANGMAT', index: 'SONGAYVANGMAT', width: 250, align: 'center', hidden: true},
                    {label: 'LOAIVAOVIEN',name: 'LOAIVAOVIEN', index: 'LOAIVAOVIEN', width: 250, align: 'center', hidden: true},
                    {label: 'NGAYRAVIEN',name: 'NGAYRAVIEN', index: 'NGAYRAVIEN', width: 250, align: 'center', hidden: true},
                    {label: 'CHUYENTU',name: 'CHUYENTU', index: 'CHUYENTU', width: 250, align: 'center', hidden: true},
                    {label: 'CHUYENDEN',name: 'CHUYENDEN', index: 'CHUYENDEN', width: 250, align: 'center', hidden: true},
                    {label: 'TINHTRANGRV',name: 'TINHTRANGRV', index: 'TINHTRANGRV', width: 250, align: 'center', hidden: true},
                    {label: 'KETQUADT',name: 'KETQUADT', index: 'KETQUADT', width: 250, align: 'center', hidden: true},
                    {label: 'SONGAYICU',name: 'SONGAYICU', index: 'SONGAYICU', width: 250, align: 'center', hidden: true},
                    {label: 'CANNANGTRE',name: 'CANNANGTRE', index: 'CANNANGTRE', width: 250, align: 'center', hidden: true},
                    {label: 'TIENLUONG',name: 'TIENLUONG', index: 'TIENLUONG', width: 250, align: 'center', hidden: true},
                    {label: 'NNTUVONGJSON',name: 'NNTUVONGJSON', index: 'NNTUVONGJSON', width: 250, align: 'center', hidden: true},
                    {label: 'BENHKHACJSON',name: 'BENHKHACJSON', index: 'BENHKHACJSON', width: 250, align: 'center', hidden: true},
                    {label: 'PHAUTHUAT4TUAN',name: 'PHAUTHUAT4TUAN', index: 'PHAUTHUAT4TUAN', width: 250, align: 'center', hidden: true},
                    {label: 'LYDOPHAUTHUAT4TUAN',name: 'LYDOPHAUTHUAT4TUAN', index: 'LYDOPHAUTHUAT4TUAN', width: 250, align: 'center', hidden: true},
                    {label: 'TRUNGCAU',name: 'TRUNGCAU', index: 'TRUNGCAU', width: 250, align: 'center', hidden: true},
                    {label: 'THOIGIAN4TUAN',name: 'THOIGIAN4TUAN', index: 'THOIGIAN4TUAN', width: 250, align: 'center', hidden: true},
                    {label: 'SUDUNGKETQUA',name: 'SUDUNGKETQUA', index: 'SUDUNGKETQUA', width: 250, align: 'center', hidden: true},
                    {label: 'HINHTHUCTV',name: 'HINHTHUCTV', index: 'HINHTHUCTV', width: 250, align: 'center', hidden: true},
                    {label: 'TENNNBENNGOAI',name: 'TENNNBENNGOAI', index: 'TENNNBENNGOAI', width: 250, align: 'center', hidden: true},
                    {label: 'MOTANNBENNGOAI',name: 'MOTANNBENNGOAI', index: 'MOTANNBENNGOAI', width: 250, align: 'center', hidden: true},
                    {label: 'MAICDNNBENNGOAI',name: 'MAICDNNBENNGOAI', index: 'MAICDNNBENNGOAI', width: 250, align: 'center', hidden: true},
                    {label: 'TENICDNNBENNGOAI',name: 'TENICDNNBENNGOAI', index: 'TENICDNNBENNGOAI', width: 250, align: 'center', hidden: true},
                    {label: 'NGAYXAYRA',name: 'NGAYXAYRA', index: 'NGAYXAYRA', width: 250, align: 'center', hidden: true},
                    {label: 'NOIXAYRATAINAN',name: 'NOIXAYRATAINAN', index: 'NOIXAYRATAINAN', width: 250, align: 'center', hidden: true},
                    {label: 'DIADIEM',name: 'DIADIEM', index: 'DIADIEM', width: 250, align: 'center', hidden: true},
                    {label: 'DATHAI',name: 'DATHAI', index: 'DATHAI', width: 250, align: 'center', hidden: true},
                    {label: 'SINHNON',name: 'SINHNON', index: 'SINHNON', width: 250, align: 'center', hidden: true},
                    {label: 'TUVONGTHAINHI',name: 'TUVONGTHAINHI', index: 'TUVONGTHAINHI', width: 250, align: 'center', hidden: true},
                    {label: 'TUOITHAI',name: 'TUOITHAI', index: 'TUOITHAI', width: 250, align: 'center', hidden: true},
                    {label: 'TUOIME',name: 'TUOIME', index: 'TUOIME', width: 250, align: 'center', hidden: true},
                    {label: 'SOGIOSONG',name: 'SOGIOSONG', index: 'SOGIOSONG', width: 250, align: 'center', hidden: true},
                    {label: 'CANNANGKHISINH',name: 'CANNANGKHISINH', index: 'CANNANGKHISINH', width: 250, align: 'center', hidden: true},
                    {label: 'BENHLYCUAME',name: 'BENHLYCUAME', index: 'BENHLYCUAME', width: 250, align: 'center', hidden: true},
                    {label: 'MAICDBENHLYME',name: 'MAICDBENHLYME', index: 'MAICDBENHLYME', width: 250, align: 'center', hidden: true},
                    {label: 'TENICDBENHLYME',name: 'TENICDBENHLYME', index: 'TENICDBENHLYME', width: 250, align: 'center', hidden: true},
                    {label: 'TUVONGTHAINHI',name: 'TUVONGTHAINHI', index: 'TUVONGTHAINHI', width: 250, align: 'center', hidden: true},
                    {label: 'DANGMANGTHAI',name: 'DANGMANGTHAI', index: 'DANGMANGTHAI', width: 250, align: 'center', hidden: true},
                    {label: 'THOIDIEMMANGTHAI',name: 'THOIDIEMMANGTHAI', index: 'THOIDIEMMANGTHAI', width: 250, align: 'center', hidden: true},
                    {label: 'GOPPHANNANG',name: 'GOPPHANNANG', index: 'GOPPHANNANG', width: 250, align: 'center', hidden: true},
                    {label: 'KETLUANTENBL',name: 'KETLUANTENBL', index: 'KETLUANTENBL', width: 250, align: 'center', hidden: true},
                    {label: 'KETLUANMAICD',name: 'KETLUANMAICD', index: 'KETLUANMAICD', width: 250, align: 'center', hidden: true},
                    {label: 'KETLUANTENICD',name: 'KETLUANTENICD', index: 'KETLUANTENICD', width: 250, align: 'center', hidden: true},
                    {label: 'BSDIEUTRI',name: 'BSDIEUTRI', index: 'BSDIEUTRI', width: 250, align: 'center', hidden: true},
                    {label: 'TRUONGKHOA',name: 'TRUONGKHOA', index: 'TRUONGKHOA', width: 250, align: 'center', hidden: true},
                    {label: 'THUTRUONG',name: 'THUTRUONG', index: 'THUTRUONG', width: 250, align: 'center', hidden: true},
                    {label: 'Bác sĩ điều trị',name: 'TENBACSI', index: 'TENBACSI', width: 250, align: 'center'},
                    {label: 'Ngày tạo phiếu',name: 'NGAY_TAO_PHIEU', index: 'NGAY_TAO_PHIEU', width: 250, align: 'center'},
                    {name: "KEYSIGN_BACSI", label: "KEYSIGN_BACSI", align: 'center', width: 150, hidden: true},
                    {name: "KEYSIGN_TRUONGKHOA", label: "KEYSIGN_TRUONGKHOA", align: 'center', width: 150, hidden: true},
                ],
                rowNum: 1000000,
                caption: "Danh sách phiếu tóm tắt thông tin người bệnh nặng xin về",
                onRightClickRow: function (id1) {
                    if (id1) {
                        var ret = getThongtinRowSelected("list_ttttbenhnangxinve");
                        var items = {
                            "xem": {name: '<p><i class="fa fa-eye text-primary" aria-hidden="true"></i> Xem</p>'},
                        }
                        $.contextMenu('destroy', '#list_ttttbenhnangxinve tr');
                        if (ret.KEYSIGN_BACSI && ret.KEYSIGN_TRUONGKHOA){
                            var daybs, dayld;
                            getFilesign769("PHIEU_NOITRU_TTTTBENHNANGXINVE_BACSI", ret.ID, -1, singletonObject.dvtt,
                                thongtinhsba.thongtinbn.SOVAOVIEN, thongtinhsba.thongtinbn.SOVAOVIEN_DT, -1, function(dataKySo) {
                                    if (dataKySo.length > 0) {
                                        daybs = moment(dataKySo[0].CREATE_DATE_STRING, "DD/MM/YYYY HH:mm:ss");
                                    }
                                });
                            getFilesign769("PHIEU_NOITRU_TTTTBENHNANGXINVE_TRUONGKHOA", ret.ID, -1, singletonObject.dvtt,
                                thongtinhsba.thongtinbn.SOVAOVIEN, thongtinhsba.thongtinbn.SOVAOVIEN_DT, -1, function(dataKySo) {
                                    if (dataKySo.length > 0) {
                                        dayld = moment(dataKySo[0].CREATE_DATE_STRING, "DD/MM/YYYY HH:mm:ss");
                                    }
                                });
                            if (daybs.isAfter(dayld)){
                                items = {
                                    "huykysobs": {name: '<p><i class="fa fa-key text-danger" aria-hidden="true"></i> Hủy ký số BS</p>'},
                                    ...items
                                }
                            }else{
                                items = {
                                    "huykysotk": {name: '<p><i class="fa fa-key text-danger" aria-hidden="true"></i> Hủy ký số TK</p>'},
                                    ...items
                                }
                            }
                        }else if (ret.KEYSIGN_BACSI){
                            items = {
                                "huykysobs": {name: '<p><i class="fa fa-key text-danger" aria-hidden="true"></i> Hủy ký số BS</p>'},
                                "kysotk": {name: '<p><i class="fa fa-key text-danger" aria-hidden="true"></i> Ký số TK</p>'},
                                ...items
                            }
                        }else if (ret.KEYSIGN_TRUONGKHOA){
                            items = {
                                "huykysotk": {name: '<p><i class="fa fa-key text-danger" aria-hidden="true"></i> Hủy ký số TK</p>'},
                                "kysobs": {name: '<p><i class="fa fa-key text-danger" aria-hidden="true"></i> Ký số BS</p>'},
                                ...items
                            }
                        }else{
                            items = {
                                "kysobs": {name: '<p><i class="fa fa-key text-success" aria-hidden="true"></i> Ký số BS</p>'},
                                "kysotk": {name: '<p><i class="fa fa-key text-danger" aria-hidden="true"></i> Ký số TK</p>'},
                                "sua": {name: '<p><i class="fa fa-pencil-square-o text-primary" aria-hidden="true"></i> Sửa</p>'},
                                "xoa": {name: '<p><i class="fa fa-trash-o text-danger" aria-hidden="true"></i> Xóa</p>'},
                                ...items
                            }
                        }
                        $.contextMenu({
                            selector: '#list_ttttbenhnangxinve tr',
                            callback: function (key, options) {
                                var id = $("#list_ttttbenhnangxinve").jqGrid('getGridParam', 'selrow');
                                var ret = $("#list_ttttbenhnangxinve").jqGrid('getRowData', id);
                                var params = {
                                    ID: ret.ID,
                                }
                                getUrlTTTTBenhNangXinVe(params).then(objReturn => {
                                    if (objReturn.isError == 0) {
                                        if (key == "kysobs") {
                                            // if(ret.DIEU_DUONG != singletonObject.userId) {
                                            //     return notifiToClient("Red", MESSAGEAJAX.PERMISSION);
                                            // }
                                            thongTinTTTTBenhNangXinVeTruocChinhSua = ret
                                            previewAndSignPdfDefaultModal({
                                                url: objReturn.url,
                                                idButton: 'ttttbenhnangxinve_kysobs_action',
                                            }, function(){

                                            });
                                        }
                                        if (key == "kysotk") {
                                            // if(ret.DIEU_DUONG != singletonObject.userId) {
                                            //     return notifiToClient("Red", MESSAGEAJAX.PERMISSION);
                                            // }
                                            thongTinTTTTBenhNangXinVeTruocChinhSua = ret
                                            previewAndSignPdfDefaultModal({
                                                url: objReturn.url,
                                                idButton: 'ttttbenhnangxinve_kysotk_action',
                                            }, function(){

                                            });
                                        }
                                        if (key == "xem") {
                                            previewPdfDefaultModal(objReturn.url, 'preview_phieuttttbenhnangxinve');
                                        }
                                    } else {
                                        notifiToClient("Red", objReturn.message);
                                    }
                                }).catch(error => {
                                    notifiToClient("Red", error.message || "Lỗi không xác định");
                                });

                                if (key == "huykysobs") {
                                    // if(ret.BAC_SI != singletonObject.userId) {
                                    //     return notifiToClient("Red", MESSAGEAJAX.PERMISSION);
                                    // }
                                    confirmToClient("Bạn có chắc sẽ hủy ký số phiếu này?", function() {
                                        huykysoFilesign769( "PHIEU_NOITRU_TTTTBENHNANGXINVE_BACSI", ret.ID, singletonObject.userId, singletonObject.dvtt,
                                            thongtinhsba.thongtinbn.SOVAOVIEN, thongtinhsba.thongtinbn.SOVAOVIEN_DT, -1, function(data) {
                                                reloadDSTTTTBenhNangXinVe();
                                            })
                                    }, function () {

                                    })
                                }
                                if (key == "huykysotk") {
                                    // if(ret.BAC_SI != singletonObject.userId) {
                                    //     return notifiToClient("Red", MESSAGEAJAX.PERMISSION);
                                    // }
                                    confirmToClient("Bạn có chắc sẽ hủy ký số phiếu này?", function() {
                                        huykysoFilesign769( "PHIEU_NOITRU_TTTTBENHNANGXINVE_TRUONGKHOA", ret.ID, singletonObject.userId, singletonObject.dvtt,
                                            thongtinhsba.thongtinbn.SOVAOVIEN, thongtinhsba.thongtinbn.SOVAOVIEN_DT, -1, function(data) {
                                                reloadDSTTTTBenhNangXinVe();
                                            })
                                    }, function () {

                                    })
                                }
                                
                                if (key== "sua"){
                                    showUpdateTTTTBenhNangXinVe(ret)
                                }
                                if (key == "xoa") {
                                    deleteTTTTBenhNangXinVe(ret)
                                }
                            },
                            items: items
                        });
                    }
                }

            });
            $("#list_ttttbenhnangxinve").jqGrid('filterToolbar', {stringResult: true, searchOnEnter: false, defaultSearch: "cn"});
        }
    }

    function xemTTTTBenhNangXinVe(ret){
        var params = {
            ID: ret.ID,
        }
        getUrlTTTTBenhNangXinVe(params).then(objReturn => {
            if (objReturn.isError == 0) {
                previewPdfDefaultModal(objReturn.url, 'preview_phieuttttbenhnangxinve');
            } else {
                notifiToClient("Red", objReturn.message);
            }
        }).catch(error => {
            notifiToClient("Red", error.message || "Lỗi không xác định");
        });
    }

    function showUpdateTTTTBenhNangXinVe(ret){
        $("#modalFormTTTTBenhNangXinVe").modal("show");
        $("#ttttbenhnangxinve_luu").attr("data-action", "CAP_NHAT");
        addTextTitleModal('titleFormTTTTBenhNangXinVe', "Phiếu tóm tắt thông tin người bệnh nặng xin về");
        thongTinTTTTBenhNangXinVeTruocChinhSua = ret
        formTTTTBenhNangXinVe.submission =  {
            data: {
                ...ret,
                NNTUVONGJSON: ret.NNTUVONGJSON ? JSON.parse(ret.NNTUVONGJSON) : [],
                BENHKHACJSON: ret.BENHKHACJSON ? JSON.parse(ret.BENHKHACJSON) : [],
                NGAY_TAO_PHIEU:  (ret.NGAY_TAO_PHIEU? moment(ret.NGAY_TAO_PHIEU, ['DD/MM/YYYY']): moment()).toISOString(),
                GIATRITU:  ret.GIATRITU? moment(ret.GIATRITU, ['DD/MM/YYYY']).toISOString() : "",
                GIATRIDEN:  ret.GIATRIDEN? moment(ret.GIATRIDEN, ['DD/MM/YYYY']).toISOString() : "",
                THOIGIAN5NAM:  ret.THOIGIAN5NAM? moment(ret.THOIGIAN5NAM, ['DD/MM/YYYY']).toISOString() : "",
                MIENCUNGCHITRA:  ret.MIENCUNGCHITRA? moment(ret.MIENCUNGCHITRA, ['DD/MM/YYYY']).toISOString() : "",
                NGAYRAVIEN:  ret.NGAYRAVIEN? moment(ret.NGAYRAVIEN, ['DD/MM/YYYY']).toISOString() : "",
                THOIGIAN4TUAN:  ret.THOIGIAN4TUAN? moment(ret.THOIGIAN4TUAN, ['DD/MM/YYYY']).toISOString() : "",
                NGAYXAYRA:  ret.NGAYXAYRA? moment(ret.NGAYXAYRA, ['DD/MM/YYYY']).toISOString() : "",
            }
        };
    }

    function deleteTTTTBenhNangXinVe(ret){
        var maGiay = ret.ID;
        thongTinTTTTBenhNangXinVeTruocChinhSua = ret
        confirmToClient("Bạn có chắc chắn muốn xóa phiếu này?", function() {
            var arr = [maGiay, singletonObject.dvtt]
            var url = "cmu_post_CMU_TTTTBENHNANGXINVE_DELETE";
            $.post(url, {
                url: arr.join("```")
            }).done(function (data) {
                if (data === "1") {
                    notifiToClient("Green", MESSAGEAJAX.DEL_SUCCESS)
                    reloadDSTTTTBenhNangXinVe();
                    var formData = { ...formTTTTBenhNangXinVe.submission.data}
                    var noidung = ["Số phiếu:"+ thongTinTTTTBenhNangXinVeTruocChinhSua.ID]
                    for (const key in formData) {
                        try {
                            var label = formTTTTBenhNangXinVe.getComponent(key).label;
                            if (label) {
                                noidung.push(formTTTTBenhNangXinVe.getComponent(key).label + ": " + getValueOfFormIO(formTTTTBenhNangXinVe.getComponent(key)));
                            }
                        } catch (error) {
                            // console.log("Error: ", error);
                        }
                    }
                    var dataLog = {
                        SOVAOVIEN: thongtinhsba.thongtinbn.SOVAOVIEN,
                        LOAI: LOGHSBALOAI.TTTTBENHNANGXINVE.KEY,
                        NOIDUNGBANDAU: noidung.join("; "),
                        NOIDUNGMOI: "",
                        USERID: singletonObject.userId,
                        ACTION: LOGHSBAACTION.DELETE.KEY,
                    }
                    luuLogHSBATheoBN(dataLog);

                } else {
                    notifiToClient("Red", MESSAGEAJAX.ERROR)
                }
            }).fail(function() {
                notifiToClient("Red", MESSAGEAJAX.ERROR)
            })
        }, function () {

        })
    }

    
})