create or replace FUNCTION                           "CMU_PHIEU_CBI_TPHAU_INSERT" (
    p_dvtt                      IN VARCHAR2,
    p_so_vao_vien               IN NUMBER,
    p_ma_benh_<PERSON>han              IN NUMBER,
    p_doi_tuong                    IN VARCHAR2,
    p_thoi_gian_mo                 IN VARCHAR2,
    p_tom_tat_benh_an                IN VARCHAR2,
    p_chan_doan                  IN VARCHAR2,
    p_cach_mo          IN VARCHAR2,
    p_bac_si              IN NUMBER,
    p_phu_mo                IN NUMBER,
    p_tong_trang                IN VARCHAR2,
    p_ha                IN VARCHAR2,
    p_mach           IN VARCHAR2,
    p_can_nang                  IN NUMBER,
    p_tinh_trang_tim                    IN VARCHAR2,
    p_tinh_trang_phoi                 IN VARCHAR2,
    p_hc                IN VARCHAR2,
    p_mc                  IN VARCHAR2,
    p_hbsag          IN VARCHAR2,
    p_mac<PERSON>an                    IN VARCHAR2,
    p_bc                 IN VARCHAR2,
    p_bun                IN VARCHAR2,
    p_duong_huyet                  IN VARCHAR2,
    p_sgot          IN VARCHAR2,
    p_dthc                    IN VARCHAR2,
    p_creatinine                 IN VARCHAR2,
    p_protid                IN VARCHAR2,
    p_sgpt                  IN VARCHAR2,
    p_md          IN VARCHAR2,
    p_hiv                    IN VARCHAR2,
    p_gros                 IN VARCHAR2,
    p_khac                IN VARCHAR2,
    p_nuoc_tieu                  IN VARCHAR2,
    p_dam          IN VARCHAR2,
    p_khac_2                    IN VARCHAR2,
    p_benh_san                 IN VARCHAR2,
    p_mau_du_tru                IN VARCHAR2,
    p_mau                  IN VARCHAR2,
    p_nhom          IN VARCHAR2,
    p_rh                    IN VARCHAR2,
    p_x_quang_phoi                 IN VARCHAR2,
    p_x_quang_tieu_hoa                IN VARCHAR2,
    p_x_quang_xuong                  IN VARCHAR2,
    p_x_quang_bo_nieu          IN VARCHAR2,
    p_soi_bang_quang                  IN VARCHAR2,
    p_soi_truc_trang          IN VARCHAR2,
    p_cac_dien_do                    IN VARCHAR2,
    p_the_nam                 IN VARCHAR2,
    p_treo                IN VARCHAR2,
    p_noi_rua                  IN VARCHAR2,
    p_bao_tay          IN VARCHAR2,
    p_bao_chan                IN VARCHAR2,
    p_ban_xuong                  IN NUMBER,
    p_dot          IN NUMBER,
    p_hut                  IN NUMBER,
    p_bot          IN NUMBER,
    p_noi_that_mach                    IN VARCHAR2,
    p_esmarch                 IN VARCHAR2,
    p_dung_cu                IN VARCHAR2,
    p_dung_cu_dac_biet                  IN VARCHAR2,
    p_x_quang_phong_mo          IN NUMBER,
    p_bac_si_dieu_tri          IN NUMBER,
--    p_ngay_tao_phieu            IN VARCHAR2,
    p_nguoi_tao                 IN NUMBER,
    p_de_nghi          IN NUMBER
)
return number IS
--     v_thoi_gian_mo   DATE := TO_DATE(p_thoi_gian_mo, 'dd/mm/yyyy hh24:mi:ss');
--    v_ngay_tao_phieu   DATE := TO_DATE(p_ngay_tao_phieu, 'dd/mm/yyyy');
BEGIN
INSERT INTO CMU_PHIEUCHUANBITIENPHAU
(
    DVTT,
    SOVAOVIEN,
    MA_BENH_NHAN,
    DOI_TUONG,
    THOI_GIAN_MO,
    TOM_TAT_BENH_AN,
    CHAN_DOAN,
    CACH_MO,
    BAC_SI,
    PHU_MO,
    TONG_TRANG,
    HA,
    MACH,
    CAN_NANG,
    TINH_TRANG_TIM,
    TINH_TRANG_PHOI,
    HC,
    MC,
    HBSAG,
    MACLAGAN,
    BC,
    BUN,
    DUONG_HUYET,
    SGOT,
    DTHC,
    CREATININE,
    PROTID,
    SGPT,
    MD,
    HIV,
    GROS,
    KHAC,
    NUOC_TIEU,
    DAM,
    KHAC_2,
    BENH_SAN,
    MAU_DU_TRU,
    MAU,
    NHOM,
    RH,
    X_QUANG_PHOI,
    X_QUANG_TIEU_HOA,
    X_QUANG_XUONG,
    X_QUANG_BO_NIEU,
    SOI_BANG_QUANG,
    SOI_TRUC_TRANG,
    CAC_DIEN_DO,
    THE_NAM,
    TREO,
    NOI_RUA,
    BAO_TAY,
    BAO_CHAN,
    BAN_XUONG,
    DOT,
    HUT,
    BOT,
    NOI_THAT_MACH,
    ESMARCH,
    DUNG_CU,
    DUNG_CU_DAC_BIET,
    X_QUANG_PHONG_MO,
    BAC_SI_DIEU_TRI,
    NGAY_TAO_PHIEU,
    NGUOI_TAO,
    DE_NGHI
)
VALUES
    (
        p_dvtt,
        p_so_vao_vien,
        p_ma_benh_nhan,
        p_doi_tuong,
        p_thoi_gian_mo,
        p_tom_tat_benh_an,
        p_chan_doan,
        p_cach_mo,
        p_bac_si,
        p_phu_mo,
        p_tong_trang,
        p_ha,
        p_mach,
        p_can_nang,
        p_tinh_trang_tim,
        p_tinh_trang_phoi,
        p_hc,
        p_mc,
        p_hbsag,
        p_maclagan,
        p_bc,
        p_bun,
        p_duong_huyet,
        p_sgot,
        p_dthc,
        p_creatinine,
        p_protid,
        p_sgpt,
        p_md,
        p_hiv,
        p_gros,
        p_khac,
        p_nuoc_tieu,
        p_dam,
        p_khac_2,
        p_benh_san,
        p_mau_du_tru,
        p_mau,
        p_nhom,
        p_rh,
        p_x_quang_phoi,
        p_x_quang_tieu_hoa,
        p_x_quang_xuong,
        p_x_quang_bo_nieu,
        p_soi_bang_quang,
        p_soi_truc_trang,
        p_cac_dien_do,
        p_the_nam,
        p_treo,
        p_noi_rua,
        p_bao_tay,
        p_bao_chan,
        p_ban_xuong,
        p_dot,
        p_hut,
        p_bot,
        p_noi_that_mach,
        p_esmarch,
        p_dung_cu,
        p_dung_cu_dac_biet,
        p_x_quang_phong_mo,
        p_bac_si_dieu_tri,
        sysdate,
        p_nguoi_tao,
        p_de_nghi
    );
return
sql%rowcount;
END;