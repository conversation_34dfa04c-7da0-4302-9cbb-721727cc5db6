$(function () {
    var thongTinPhieuMoiNhat;
    var formKiemTruocTiemChungTreSoSinh;
    var thongTinPhieuTruocKhiChinhSua;
    $('#modalDSbangkiemtruoctiemchungsosinh').on('show.bs.modal', function() {
        initGridBangKiemTruocTiemChungSoSinh();
        loadDSKiemTruocTiemChungTreSoSinh();
    });
    $('#modalbangkiemtruoctiemchungsosinh').on('hidden.bs.modal', function () {
        thongTinPhieuTruocKhiChinhSua = {};
    })
    $("#ttchamsoc-phieukhac").click(function() {
        loadDSKiemTruocTiemChungTreSoSinh();
    });
    $("#bangkiemtruoctiemchungsosinhXemDanhsach").click(function () {
        $("#modalDSbangkiemtruoctiemchungsosinh").modal("show");
    });

    $(".bangkiemtruoctiemchungsosinh_them").click(function () {
        $("#modalbangkiemtruoctiemchungsosinh .btn-them").show();
        $("#modalbangkiemtruoctiemchungsosinh .btn-sua").hide();
        initFormioKiemTruocTiemChungTreSoSinh({});
    });

    $("#bangkiemtruoctiemchungsosinh_themmoi").click(function () {
        var idButton = this.id;
        themBangKiemTruocTiemChungTreSoSinh(idButton,
            function (data) {
                if (data.ID > 0) {
                    notifiToClient("Green", MESSAGEAJAX.ADD_SUCCESS);
                    $("#modalbangkiemtruoctiemchungsosinh").modal("hide");
                    loadDSKiemTruocTiemChungTreSoSinh();
                } else {
                    notifiToClient("Green", MESSAGEAJAX.FAIL);
                }
                hideSelfLoading(idButton);
            },
            function () {
                notifiToClient("Red", MESSAGEAJAX.ERROR);
                hideSelfLoading(idButton);
            });
    });

    $("#bangkiemtruoctiemchungsosinh_themkyso").click(function () {
        var idButton = this.id;
        themBangKiemTruocTiemChungTreSoSinh(idButton,
            function (data) {
                if (data.ID > 0) {
                    xemKySoBangKiemTruocTiemChungTreSoSinh(data);
                } else {
                    notifiToClient("Green", MESSAGEAJAX.FAIL);
                }
                hideSelfLoading(idButton);
            },
            function () {
                notifiToClient("Red", MESSAGEAJAX.ERROR);
                hideSelfLoading(idButton);
            });
    });

    $("#edit_single_bangkiemtruoctiemchungsosinh").click(function () {
        $("#modalbangkiemtruoctiemchungsosinh .btn-them").hide();
        $("#modalbangkiemtruoctiemchungsosinh .btn-sua").show();
        initFormioKiemTruocTiemChungTreSoSinh(thongTinPhieuMoiNhat);
    });
    $("#bangkiemtruoctiemchungsosinh_luu").click(function () {
        var idButton = this.id;
        suaBangKiemTruocTiemChungTreSoSinh(idButton,
            function (data) {
                if (data.TT > 0) {
                    notifiToClient("Green", MESSAGEAJAX.ADD_SUCCESS);
                    $("#modalbangkiemtruoctiemchungsosinh").modal("hide");
                } else {
                    notifiToClient("Green", MESSAGEAJAX.FAIL);
                }
                hideSelfLoading(idButton);
                loadDSKiemTruocTiemChungTreSoSinh();
            },
            function () {
                notifiToClient("Red", MESSAGEAJAX.ERROR);
                hideSelfLoading(idButton);
            });
    });

    $("#bangkiemtruoctiemchungsosinh_luukyso").click(function () {
        var idButton = this.id;
        suaBangKiemTruocTiemChungTreSoSinh(idButton,
            function (data) {
                if (data.TT > 0) {
                    xemKySoBangKiemTruocTiemChungTreSoSinh(data);
                    loadDSKiemTruocTiemChungTreSoSinh();
                } else {
                    notifiToClient("Green", MESSAGEAJAX.FAIL);
                }
                hideSelfLoading(idButton);
            },
            function () {
                notifiToClient("Red", MESSAGEAJAX.ERROR);
                hideSelfLoading(idButton);
            });
    });

    $("#delete_single_bangkiemtruoctiemchungsosinh").click(function () {
        var idButton = this.id;
        xoaBangKiemTruocTiemChungTreSoSinh(idButton, thongTinPhieuMoiNhat);
    });

    $("#view_single_bangkiemtruoctiemchungsosinh").click(function () {
        var idButton = this.id;
        xemBangKiemTruocTiemChungTreSoSinh(idButton, thongTinPhieuMoiNhat);
    });

    $("#sign_single_bangkiemtruoctiemchungsosinh").click(function () {
        xemKySoBangKiemTruocTiemChungTreSoSinh(thongTinPhieuMoiNhat);
    });

    $("#huysign_single_bangkiemtruoctiemchungsosinh").click(function () {
        huyKySoBangKiemTruocTiemChungTreSoSinh(thongTinPhieuMoiNhat);
    });

    $("#bangkiemtruoctiemchungsosinh_mau").click(function() {
        var element = $("#mau_danhsachmaujson_wrap");
        element.attr("function-add", 'insertMauHSBAKTTIEMCHUNGTRESS');
        element.attr("function-chinhsua", 'editMauHSBAKTTIEMCHUNGTRESS');
        element.attr("function-select", 'selectMauHSBAKTTIEMCHUNGTRESS');
        element.attr("function-getdata", 'getdataMauHSBAKTTIEMCHUNGTRESS');
        element.attr("function-validate", 'formioHSBAKTTIEMCHUNGTRESSValidate');
        element.attr("data-key", 'MAUKTTIEMCHUNGTRESS');
        $("#modalMauChungJSON").modal("show");
        $.loadDanhSachMauChungJSON('MAUKTTIEMCHUNGTRESS')
    })

    // FUNCTION
    function initGridBangKiemTruocTiemChungSoSinh() {
        var list = $("#list_bangkiemtruoctiemchungsosinh");
        if(!list[0].grid) {
            $("#list_bangkiemtruoctiemchungsosinh").jqGrid({
                datatype: "local",
                data: [],
                loadonce: true,
                height: 300,
                width: null,
                shrinkToFit: false,
                caption: "Danh sách bảng kiểm trước tiêm chủng đối với trẻ sơ sinh",
                colModel: [
                    {
                        name: "KYSO",
                        label: "Ký số",
                        align: 'left',
                        width: 100,
                        formatter: function (cellValue, options, rowData) {
                            if (rowData.KEYSIGN) {
                                return '<span class="cellWithoutBackground" style="font-weight:bold ;color: green">Đã ký</span>';
                            } else {
                                return '<span class="cellWithoutBackground" style="font-weight:bold ;color: red">Chưa ký</span>';
                            }
                        }
                    },
                    {name: "ID", label: "ID", align: 'center', width: 50, hidden: true},
                    {name: "SOVAOVIEN", label: "Số vào viện", hidden: true},
                    {name: "SOVAOVIEN_DT", label: "SOVAOVIEN_DT", hidden: true},
                    {name: "STT_BENHAN", label: "STT_BENHAN", hidden: true},
                    {name: "SOBENHAN", label: "SOBENHAN", hidden: true},
                    {name: "SOBENHAN", label: "SOBENHAN", hidden: true},
                    {name: "MABENHNHAN", label: "MABENHNHAN", hidden:true},
                    {name: "HO_TEN_CHA", label: "HO_TEN_CHA", hidden:true},
                    {name: "SDT_CHA", label: "SDT_CHA", hidden:true},
                    {name: "HO_TEN_ME", label: "HO_TEN_ME", hidden:true},
                    {name: "SDT_ME", label: "SDT_ME", hidden:true},
                    {name: "TUOI_THAI_KHI_SINH", label: "TUOI_THAI_KHI_SINH", hidden:true},
                    {name: "CAN_NANG", label: "CAN_NANG", hidden:true},
                    {name: "THAN_NHIET", label: "THAN_NHIET", hidden:true},
                    {name: "ME_XN_HBSAG", label: "ME_XN_HBSAG", hidden:true},
                    {name: "KET_QUA_ME_XN_HBSAG", label: "KET_QUA_ME_XN_HBSAG", hidden:true},
                    {name: "SUC_KHOE_CHUA_ON_DINH", label: "SUC_KHOE_CHUA_ON_DINH", hidden:true},
                    {name: "SOC_HA_THAN_NHIET", label: "SOC_HA_THAN_NHIET", hidden:true},
                    {name: "KHOC_BE_KHONG_KHOC", label: "KHOC_BE_KHONG_KHOC", hidden:true},
                    {name: "DA_MOI_KHONG_HONG", label: "DA_MOI_KHONG_HONG", hidden:true},
                    {name: "BU_KEM_BO_BU", label: "BU_KEM_BO_BU", hidden:true},
                    {name: "TUOI_THAI_28_TUAN", label: "TUOI_THAI_28_TUAN", hidden:true},
                    {name: "TRE_34_TUAN_TUOI", label: "TRE_34_TUAN_TUOI", hidden:true},
                    {name: "TRE_2000_ME_CO_HBSAG", label: "TRE_2000_ME_CO_HBSAG", hidden:true},
                    {name: "IS_CHI_DINH_KHAC", label: "IS_CHI_DINH_KHAC", hidden:true},
                    {name: "CHI_DINH_KHAC", label: "CHI_DINH_KHAC", hidden:true},
                    {name: "IS_SANG_LOC_CHUYEN_KHOA", label: "IS_SANG_LOC_CHUYEN_KHOA", hidden:true},
                    {name: "SANG_LOC_CHUYEN_KHOA", label: "SANG_LOC_CHUYEN_KHOA", hidden:true},
                    {name: "LY_DO", label: "LY_DO", hidden:true},
                    {name: "KET_QUA", label: "KET_QUA", hidden:true},
                    {name: "KET_LUAN", label: "KET_LUAN", hidden:true},
                    {name: "LOAI_VAC_XIN", label: "LOAI_VAC_XIN", hidden:true},
                    {name: "NGUOI_THUC_HIEN", label: "NGUOI_THUC_HIEN", hidden:true},
                    {name: "NGUOI_TAO", label: "NGUOI_TAO", hidden:true},
                    {name: "NGAYTAO_TEXT", label: "NGAYTAO_TEXT", hidden:true},
                    {name: "KEYSIGN", label: "KEYSIGN", hidden:true},

                    {name: "THONG_TIN_CHA", label: "Thông tin cha", width: 250},
                    {name: "THONG_TIN_ME", label: "Thông tin mẹ", width: 250},
                    {name: "NGAY_GIO_SINH_TEXT", label: "Ngày sinh", align: 'center', width: 130},
                    {name: "TEN_NGUOI_THUC_HIEN", label: "Người thực hiện", align: 'center', width: 200},
                    {name: "NGAY_LAP_PHIEU_TEXT", label: "Ngày lập", align: 'center', width: 130},
                    {name: "TEN_NGUOI_TAO", label: "Người tạo", align: 'center', width: 200},
                ],
                onRightClickRow: function (id1) {
                    if (id1) {
                        $("#list_bangkiemtruoctiemchungsosinh").jqGrid('setSelection', id1);
                        $.contextMenu('destroy', '#list_bangkiemtruoctiemchungsosinh tr');
                        var rowData = getThongtinRowSelected("list_bangkiemtruoctiemchungsosinh");
                        var items = {
                            "xem": {name: '<p><i class="fa fa-eye text-primary" aria-hidden="true"></i> Xem</p>'},
                        }
                        if (rowData.KEYSIGN) {
                            items = {
                                ...items,
                                "huykyso": {name: '<p style="color:red"><i class="fa fa-key text-danger" aria-hidden="true"></i> Huỷ ký số</p>'},
                            }
                        } else {
                            items = {
                                "kyso": {name: '<p><i class="fa fa-key text-primary" aria-hidden="true"></i> Ký số</p>'},
                                ...items,
                                "sua": {name: '<p><i class="fa fa-pencil-square-o text-primary" aria-hidden="true"></i> Sửa</p>'},
                                "xoa": {name: '<p><i class="fa fa-trash-o text-danger" aria-hidden="true"></i> Xóa</p>'},
                            }
                        }
                        $.contextMenu({
                            selector: '#list_bangkiemtruoctiemchungsosinh tr',
                            callback: function (key, options) {
                                if (key == "xem") {
                                    xemBangKiemTruocTiemChungTreSoSinh("", rowData);
                                }
                                if (key == "sua") {
                                    $("#modalbangkiemtruoctiemchungsosinh .btn-them").hide();
                                    $("#modalbangkiemtruoctiemchungsosinh .btn-sua").show();
                                    initFormioKiemTruocTiemChungTreSoSinh(rowData);
                                }
                                if (key == "xoa") {
                                    xoaBangKiemTruocTiemChungTreSoSinh("", rowData);
                                }
                                if (key == "huykyso") {
                                    huyKySoBangKiemTruocTiemChungTreSoSinh(rowData);
                                }
                                if (key == "kyso") {
                                    xemKySoBangKiemTruocTiemChungTreSoSinh(rowData);
                                }
                            },
                            items: items
                        })
                    }
                }
            });
        }
        $('#list_bangkiemtruoctiemchungsosinh').jqGrid('filterToolbar', {stringResult: true, searchOnEnter: false, defaultSearch: 'cn'});
    }

    function loadDSKiemTruocTiemChungTreSoSinh() {
        $("#list_bangkiemtruoctiemchungsosinh").jqGrid('clearGridData');
        var url = "cmu_list_CMU_KTTIEMCHUNGTRESOSINH_GET?url=" +
            convertArray([singletonObject.dvtt, thongtinhsba.thongtinbn.SOVAOVIEN, thongtinhsba.thongtinbn.SOVAOVIEN_DT]);
        $.ajax({
            url: url,
            type: 'GET',
            async: false,
            success: function (data) {
                if (data && data.length > 0) {
                    thongTinPhieuMoiNhat = data[0];
                    $("#dataBangkiemtruoctiemchungsosinh").html("Người thực hiện: " + data[0].TEN_NGUOI_THUC_HIEN + " - " + data[0].NGAY_LAP_PHIEU_TEXT);
                    $("#list_bangkiemtruoctiemchungsosinh").jqGrid('setGridParam', {
                        datatype: 'local',
                        data: data
                    }).trigger("reloadGrid");
                    $('#handle_icon_bangkiemtruoctiemchungsosinh').css('visibility', 'unset');
                    if (data[0].KEYSIGN){
                        $('#handle_icon_bangkiemtruoctiemchungsosinh .daky-hidden').hide();
                        $('#handle_icon_bangkiemtruoctiemchungsosinh .chuaky-hidden').show();
                    } else {
                        $('#handle_icon_bangkiemtruoctiemchungsosinh .daky-hidden').show();
                        $('#handle_icon_bangkiemtruoctiemchungsosinh .chuaky-hidden').hide();
                    }
                } else  {
                    thongTinPhieuMoiNhat = {};
                    $("#dataBangkiemtruoctiemchungsosinh").html('Không có dữ liệu');
                    $('#handle_icon_bangkiemtruoctiemchungsosinh').css('visibility', 'hidden');
                }
            }
        });
    }

    function initFormioKiemTruocTiemChungTreSoSinh(data) {
        addTextTitleModal('titlebangkiemtruoctiemchungsosinh', "Bảng kiểm trước tiêm chủng đối với trẻ sơ sinh");
        $("#modalbangkiemtruoctiemchungsosinh").modal("show");
        var jsonForm = getJSONObjectForm([
            {
                label: "bangkiemtruoctiemchungsosinh1",
                key: "bangkiemtruoctiemchungsosinh1",
                columns: [
                    {
                        "components": [
                            JSONDateTime("Ngày giờ sinh", "NGAY_GIO_SINH_TEXT_FORMIO", "pr-2", "dd/MM/yyyy HH:mm",
                                true, moment(thongtinhsba.thongtinbn.NGAY_VAO_VIEN, ['DD/MM/YYYY']).format("YYYY-MM-DD"),
                                null, {}, {
                                    others: {
                                        "validate": {
                                            "required": true
                                        },
                                    }
                                }),
                        ],
                        "width": 3,
                        "size": "md",
                    },
                    {
                        "components": [
                            JSONTextField("Tuổi thai", "TUOI_THAI_KHI_SINH", "pr-2", {}, {
                                others: {
                                    "tooltip": "Tuổi thai khi sinh",
                                    "validate": {
                                        "required": true
                                    },
                                }
                            }),
                        ],
                        "width": 3,
                        "size": "md",
                    },
                    {
                        "components": [
                            JSONSelect("Người thực hiện", "NGUOI_THUC_HIEN", "pr-2", {}, {}, {
                                others:
                                    {
                                        "data": {
                                            "values": singletonObject.danhsachtatcanhanvien
                                        },
                                        "defaultValue": singletonObject.userId,
                                        "validate": {
                                            "required": true
                                        },
                                    }
                            }),
                        ],
                        "width": 3,
                        "size": "md",
                    },
                    {
                        "components": [
                            JSONDateTime("Ngày lập phiếu", "NGAY_LAP_PHIEU_TEXT_FORMIO", "pr-2", "dd/MM/yyyy HH:mm",
                                true, moment(thongtinhsba.thongtinbn.NGAY_VAO_VIEN, ['DD/MM/YYYY']).format("YYYY-MM-DD"),
                                null, {}, {
                                    others: {
                                        "validate": {
                                            "required": true
                                        },
                                    }
                                }),
                        ],
                        "width": 3,
                        "size": "md",
                    },
                ],
                "customClass": "ml-0 mr-0",
                "type": "columns",
            },
            {
                label: "bangkiemtruoctiemchungsosinh2",
                key: "bangkiemtruoctiemchungsosinh2",
                columns: [
                    {
                        "components": [
                            JSONTextField("Họ tên cha", "HO_TEN_CHA", "pr-2", {}, {
                                others:
                                    {
                                        // "disabled": true,
                                    }
                            }),
                        ],
                        "width": 3,
                        "size": "md",
                    },
                    {
                        "components": [
                            JSONPhoneNumber("SĐT Cha", "SDT_CHA", "pr-2", {
                                others: {
                                    "inputMask": "",
                                }
                            }),
                        ],
                        "width": 3,
                        "size": "md",
                    },
                    {
                        "components": [
                            JSONTextField("Họ tên mẹ", "HO_TEN_ME", "pr-2", {}, {
                                others:
                                    {
                                        // "disabled": true,
                                    }
                            }),
                        ],
                        "width": 3,
                        "size": "md",
                    },
                    {
                        "components": [
                            JSONPhoneNumber("SĐT Mẹ", "SDT_ME", "", {
                                others: {
                                    "inputMask": "",
                                }
                            }),
                        ],
                        "width": 3,
                        "size": "md",
                    },
                ],
                "customClass": "ml-0 mr-0",
                "type": "columns",
            },
            {
                label: "bangkiemtruoctiemchungsosinh2",
                key: "bangkiemtruoctiemchungsosinh2",
                columns: [
                    {
                        "components": [
                            JSONNumber("Cân nặng", "CAN_NANG", "pr-2", {}, {
                                others:
                                    {
                                        // "disabled": true,
                                        "validate": {
                                            "required": true
                                        },
                                    }
                            }),
                        ],
                        "width": 3,
                        "size": "md",
                    },
                    {
                        "components": [
                            JSONNumber("Thân nhiệt", "THAN_NHIET", "pr-2", {}, {}),
                        ],
                        "width": 3,
                        "size": "md",
                    },
                    {
                        "components": [
                            JSONSelect("Mẹ đã xét nghiệm HbsAg", "ME_XN_HBSAG", "pr-2", {}, {}, {
                                others:
                                    {
                                        "data": {
                                            "values": [
                                                {
                                                    "label": "Có",
                                                    "value": '1'
                                                },
                                                {
                                                    "label": "Không",
                                                    "value": '0'
                                                },
                                            ]
                                        },
                                        "defaultValue": 0,
                                        "validate": {
                                            "required": true
                                        },
                                    }
                            }),
                        ],
                        "width": 3,
                        "size": "md",
                    },
                    {
                        "components": [
                            JSONSelect("Kết quả", "KET_QUA_ME_XN_HBSAG", "", {}, {}, {
                                others:
                                    {
                                        "data": {
                                            "values": [
                                                {
                                                    "label": "Dương tính",
                                                    "value": '1'
                                                },
                                                {
                                                    "label": "Âm tính",
                                                    "value": '0'
                                                },
                                            ]
                                        },
                                        "customConditional": "show = data.ME_XN_HBSAG == 1;",
                                        "defaultValue": '0',
                                    }
                            }),
                        ],
                        "width": 3,
                        "size": "md",
                    },
                ],
                "customClass": "ml-0 mr-0",
                "type": "columns",
            },
            {
                label: "bangkiemtruoctiemchungsosinh3",
                key: "bangkiemtruoctiemchungsosinh3",
                columns: [
                    {
                        "components": [
                            JSONHtml("1. Tình trạng sức khỏe chưa ổn định", "label", "LABEL1", {}),
                        ],
                        "width": 9,
                        "size": "md",
                    },
                    {
                        "components": [
                            JSONSelect("", "SUC_KHOE_CHUA_ON_DINH", "", {}, {}, {
                                others:
                                    {
                                        "data": {
                                            "values": [
                                                {
                                                    "label": "Không",
                                                    "value": '0'
                                                },
                                                {
                                                    "label": "Có",
                                                    "value": '1'
                                                },
                                            ]
                                        },
                                        "defaultValue": 0,
                                        "validate": {
                                            "required": true
                                        },
                                    }
                            }),
                        ],
                        "width": 3,
                        "size": "md",
                    },
                ],
                "customClass": "ml-0 mr-0",
                "type": "columns",
            },
            {
                label: "bangkiemtruoctiemchungsosinh3",
                key: "bangkiemtruoctiemchungsosinh3",
                columns: [
                    {
                        "components": [
                            JSONHtml("2. Sốt/Hạ thân nhiệt (Sốt: nhiệt độ &ge; 37,5&#8451;; Hạ thân nhiệt: nhiệt độ &le; 35,5&#8451;", "label", "LABEL2", {}),
                        ],
                        "width": 9,
                        "size": "md",
                    },
                    {
                        "components": [
                            JSONSelect("", "SOC_HA_THAN_NHIET", "", {}, {}, {
                                others: {
                                    "data": {
                                        "values": [
                                            {
                                                "label": "Không",
                                                "value": '0'
                                            },
                                            {
                                                "label": "Có",
                                                "value": '1'
                                            },
                                        ]
                                    },
                                    "defaultValue": 0,
                                    "validate": {
                                        "required": true
                                    },
                                }
                            }),
                        ],
                        "width": 3,
                        "size": "md",
                    },
                ],
                "customClass": "ml-0 mr-0",
                "type": "columns",
            },
            {
                label: "bangkiemtruoctiemchungsosinh4",
                key: "bangkiemtruoctiemchungsosinh4",
                columns: [
                    {
                        "components": [
                            JSONHtml("3. Khóc bé hoặc không khóc", "label", "LABEL3", {}),
                        ],
                        "width": 9,
                        "size": "md",
                    },
                    {
                        "components": [
                            JSONSelect("", "KHOC_BE_KHONG_KHOC", "", {}, {}, {
                                others: {
                                    "data": {
                                        "values": [
                                            {
                                                "label": "Không",
                                                "value": '0'
                                            },
                                            {
                                                "label": "Có",
                                                "value": '1'
                                            },
                                        ]
                                    },
                                    "defaultValue": '0',
                                    "validate": {
                                        "required": true
                                    },
                                }
                            }),
                        ],
                        "width": 3,
                        "size": "md",
                    },
                ],
                "customClass": "ml-0 mr-0",
                "type": "columns",
            },
            {
                label: "bangkiemtruoctiemchungsosinh5",
                key: "bangkiemtruoctiemchungsosinh5",
                columns: [
                    {
                        "components": [
                            JSONHtml("4. Da, môi không hồng", "label", "LABEL4", {}),
                        ],
                        "width": 9,
                        "size": "md",
                    },
                    {
                        "components": [
                            JSONSelect("", "DA_MOI_KHONG_HONG", "", {}, {}, {
                                others: {
                                    "data": {
                                        "values": [
                                            {
                                                "label": "Không",
                                                "value": '0'
                                            },
                                            {
                                                "label": "Có",
                                                "value": '1'
                                            },
                                        ]
                                    },
                                    "defaultValue": 0,
                                    "validate": {
                                        "required": true
                                    },
                                }
                            }),
                        ],
                        "width": 3,
                        "size": "md",
                    },
                ],
                "customClass": "ml-0 mr-0",
                "type": "columns",
            },
            {
                label: "bangkiemtruoctiemchungsosinh6",
                key: "bangkiemtruoctiemchungsosinh6",
                columns: [
                    {
                        "components": [
                            JSONHtml("5. Bú kém hoặc bỏ bú", "label", "LABEL5", {}),
                        ],
                        "width": 9,
                        "size": "md",
                    },
                    {
                        "components": [
                            JSONSelect("", "BU_KEM_BO_BU", "", {}, {}, {
                                others: {
                                    "data": {
                                        "values": [
                                            {
                                                "label": "Không",
                                                "value": '0'
                                            },
                                            {
                                                "label": "Có",
                                                "value": '1'
                                            },
                                        ]
                                    },
                                    "defaultValue": 0,
                                    "validate": {
                                        "required": true
                                    },
                                }
                            }),
                        ],
                        "width": 3,
                        "size": "md",
                    },
                ],
                "customClass": "ml-0 mr-0",
                "type": "columns",
            },
            {
                label: "bangkiemtruoctiemchungsosinh7",
                key: "bangkiemtruoctiemchungsosinh7",
                columns: [
                    {
                        "components": [
                            JSONHtml("6. Tuổi thai &lt; 28 tuần", "label", "LABEL6", {}),
                        ],
                        "width": 9,
                        "size": "md",
                    },
                    {
                        "components": [
                            JSONSelect("", "TUOI_THAI_28_TUAN", "", {}, {}, {
                                others: {
                                    "data": {
                                        "values": [
                                            {
                                                "label": "Không",
                                                "value": '0'
                                            },
                                            {
                                                "label": "Có",
                                                "value": '1'
                                            },
                                        ]
                                    },
                                    "defaultValue": 0,
                                    "validate": {
                                        "required": true
                                    },
                                }
                            }),
                        ],
                        "width": 3,
                        "size": "md",
                    },
                ],
                "customClass": "ml-0 mr-0",
                "type": "columns",
            },
            {
                label: "bangkiemtruoctiemchungsosinh8",
                key: "bangkiemtruoctiemchungsosinh8",
                columns: [
                    {
                        "components": [
                            JSONHtml("7. Trẻ &lt; 34 tuần tuổi", "label", "LABEL7", {}),
                        ],
                        "width": 9,
                        "size": "md",
                    },
                    {
                        "components": [
                            JSONSelect("", "TRE_34_TUAN_TUOI", "", {}, {}, {
                                others: {
                                    "data": {
                                        "values": [
                                            {
                                                "label": "Không",
                                                "value": '0'
                                            },
                                            {
                                                "label": "Có",
                                                "value": '1'
                                            },
                                        ]
                                    },
                                    "defaultValue": 0,
                                    "validate": {
                                        "required": true
                                    },
                                }
                            }),
                        ],
                        "width": 3,
                        "size": "md",
                    },
                ],
                "customClass": "ml-0 mr-0",
                "type": "columns",
            },
            {
                label: "bangkiemtruoctiemchungsosinh9",
                key: "bangkiemtruoctiemchungsosinh9",
                columns: [
                    {
                        "components": [
                            JSONHtml("8. Suy giảm miễn dịch chưa xác định mức độ hoặc mức độ nặng, có biểu hiện lâm sàng nghi nhiễm HIV", "label", "LABEL10", {}),
                        ],
                        "width": 9,
                        "size": "md",
                    },
                    {
                        "components": [
                            JSONSelect("", "TRE_2000_ME_CO_HBSAG", "", {}, {}, {
                                others: {
                                    "data": {
                                        "values": [
                                            {
                                                "label": "Không",
                                                "value": '0'
                                            },
                                            {
                                                "label": "Có",
                                                "value": '1'
                                            },
                                        ]
                                    },
                                    "defaultValue": 0,
                                    "validate": {
                                        "required": true
                                    },
                                }
                            }),
                        ],
                        "width": 3,
                        "size": "md",
                    },
                ],
                "customClass": "ml-0 mr-0",
                "type": "columns",
            },
            {
                label: "bangkiemtruoctiemchungsosinh10",
                key: "bangkiemtruoctiemchungsosinh10",
                columns: [
                    {
                        "components": [
                            JSONHtml("9. Các chống chỉ định khác, nếu có ghi rõ", "label", "LABEL11", {}),
                        ],
                        "width": 9,
                        "size": "md",
                    },
                    {
                        "components": [
                            JSONSelect("", "IS_CHI_DINH_KHAC", "", {}, {}, {
                                others: {
                                    "data": {
                                        "values": [
                                            {
                                                "label": "Không",
                                                "value": '0'
                                            },
                                            {
                                                "label": "Có",
                                                "value": '1'
                                            },
                                        ]
                                    },
                                    "defaultValue": 0,
                                    "validate": {
                                        "required": true
                                    },
                                }
                            }),
                        ],
                        "width": 3,
                        "size": "md",
                    },
                    {
                        "components": [
                            JSONTextField("", "CHI_DINH_KHAC", "", null, {
                                others:
                                    {
                                        "customConditional": "show = data.IS_CHI_DINH_KHAC == 1;",
                                        "validate": {
                                            "maxLength": 250
                                        },
                                    }
                            }),
                        ],
                        "width": 12,
                        "size": "md",
                    },
                ],
                "customClass": "ml-0 mr-0",
                "type": "columns",
            },
            {
                label: "bangkiemtruoctiemchungsosinh10",
                key: "bangkiemtruoctiemchungsosinh10",
                columns: [
                    {
                        "components": [
                            JSONSelect("Khám sàng lọc theo chuyên khoa", "IS_SANG_LOC_CHUYEN_KHOA", "pr-2", {}, {}, {
                                others: {
                                    "data": {
                                        "values": [
                                            {
                                                "label": "Không",
                                                "value": '0'
                                            },
                                            {
                                                "label": "Có",
                                                "value": '1'
                                            },
                                        ]
                                    },
                                    "defaultValue": 0,
                                    "labelPosition": "left-left",
                                    "labelWidth": 60,
                                    "validate": {
                                        "required": true
                                    },
                                }
                            }),
                        ],
                        "width": 4,
                        "size": "md",
                    },
                    {
                        "components": [
                            JSONTextField("", "SANG_LOC_CHUYEN_KHOA", "", {}, {
                                others:
                                    {
                                        "customConditional": "show = data.IS_SANG_LOC_CHUYEN_KHOA == 1;",
                                    }
                            }),
                        ],
                        "width": 8,
                        "size": "md",
                    },
                    {
                        "components": [
                            JSONTextField("Lý do", "LY_DO", "", {}, {
                                others:
                                    {
                                        "customConditional": "show = data.IS_SANG_LOC_CHUYEN_KHOA == 1;",
                                        "labelPosition": "left-left",
                                        "labelWidth": 5
                                    }
                            }),
                        ],
                        "width": 12,
                        "size": "md",
                    },
                    {
                        "components": [
                            JSONTextField("Kết quả", "KET_QUA", "", {}, {
                                others:
                                    {
                                        "customConditional": "show = data.IS_SANG_LOC_CHUYEN_KHOA == 1;",
                                        "labelPosition": "left-left",
                                        "labelWidth": 5
                                    }
                            }),
                        ],
                        "width": 12,
                        "size": "md",
                    },
                    {
                        "components": [
                            JSONTextField("Kết luận", "KET_LUAN", "", {}, {
                                others:
                                    {
                                        "customConditional": "show = data.IS_SANG_LOC_CHUYEN_KHOA == 1;",
                                        "labelPosition": "left-left",
                                        "labelWidth": 5
                                    }
                            }),
                        ],
                        "width": 12,
                        "size": "md",
                    },
                ],
                "customClass": "ml-0 mr-0",
                "type": "columns",
            },
            {
                "collapsible": true,
                "key": "p-lydovaovien",
                "type": "panel",
                "label": "II. KẾT LUẬN",
                "title": "II. KẾT LUẬN",
                "collapsed": false,
                "input": false,
                "tableView": false,
                "customClass": "hsba-tabs-wrap",
                "components": [
                    {
                        "label": "",
                        "key": "columns_nhin",
                        "type": "columns",
                        "customClass": "ml-0 mr-0",
                        "columns": [
                            {
                                "components": [
                                    JSONTextField("", "DU_DIEU_KIEN_TIEM", "", {}, {
                                        others:
                                            {
                                                "disabled": true,
                                                "calculateValue": "value = data.SUC_KHOE_CHUA_ON_DINH == 0 && data.SOC_HA_THAN_NHIET == 0" +
                                                    "&& data.KHOC_BE_KHONG_KHOC == 0 && data.DA_MOI_KHONG_HONG == 0 && data.BU_KEM_BO_BU == 0" +
                                                    "&& data.TUOI_THAI_28_TUAN == 0 && data.TRE_34_TUAN_TUOI == 0 && data.TRE_2000_ME_CO_HBSAG == 0" +
                                                    "&& data.IS_CHI_DINH_KHAC == 0 ? 'Đủ điều kiện tiêm chủng ngay' : 'Không đủ điều kiện tiêm chủng';",
                                                "labelPosition": "left-left",
                                                "labelWidth": 10
                                            }
                                    }),
                                ],
                                "width": 12,
                                "size": "md",
                            },
                            {
                                "components": [
                                    JSONTextField("Loại vắc xin", "LOAI_VAC_XIN", "", {}, {
                                        others:
                                            {
                                                "customConditional": "show = data.SUC_KHOE_CHUA_ON_DINH == 0 && data.SOC_HA_THAN_NHIET == 0" +
                                                    "&& data.KHOC_BE_KHONG_KHOC == 0 && data.DA_MOI_KHONG_HONG == 0 && data.BU_KEM_BO_BU == 0" +
                                                    "&& data.TUOI_THAI_28_TUAN == 0 && data.TRE_34_TUAN_TUOI == 0 && data.TRE_2000_ME_CO_HBSAG == 0" +
                                                    "&& data.IS_CHI_DINH_KHAC == 0;",
                                            }
                                    }),
                                ],
                                "width": 12,
                                "size": "md",
                            },
                            {
                                "components": [
                                    JSONTextField("", "CHONG_CHI_DINH_TIEM", "", {}, {
                                        others:
                                            {
                                                "disabled": true,
                                                "calculateValue": "value = data.IS_CHI_DINH_KHAC == 0 ? 'Không chống chỉ định tiêm chủng (Khi có điểm bất thường tại mục 9)' : 'Chống chỉ định tiêm chủng (Khi có điểm bất thường tại mục 9)';",
                                                "labelPosition": "left-left",
                                                "labelWidth": 10
                                            }
                                    }),
                                ],
                                "width": 12,
                                "size": "md",
                            },
                            {
                                "components": [
                                    JSONTextField("", "TAM_HOAN_TIEM_CHUNG", "", {}, {
                                        others:
                                            {
                                                "disabled": true,
                                                "calculateValue": "value = data.SUC_KHOE_CHUA_ON_DINH == 0 && data.SOC_HA_THAN_NHIET == 0" +
                                                    "&& data.KHOC_BE_KHONG_KHOC == 0 && data.DA_MOI_KHONG_HONG == 0 && data.BU_KEM_BO_BU == 0" +
                                                    "&& data.TUOI_THAI_28_TUAN == 0 && data.TRE_34_TUAN_TUOI == 0 && data.TRE_2000_ME_CO_HBSAG == 0 " +
                                                    "? 'Không tạm hoãn tiêm chủng (Khi CÓ bất ký một điểm bất thường tại mục 1,2,3,4,5,6,7,8)' : 'Tạm hoãn tiêm chủng (Khi CÓ bất ký một điểm bất thường tại mục 1,2,3,4,5,6,7,8)';",
                                                "labelPosition": "left-left",
                                                "labelWidth": 10
                                            }
                                    }),
                                ],
                                "width": 12,
                                "size": "md",
                            },
                        ]
                    },
                ]
            },
        ])
        Formio.createForm(document.getElementById('bangkiemtruoctiemchungsosinh_form'),
            jsonForm
        ).then(function(form) {
            formKiemTruocTiemChungTreSoSinh = form;
            if (!data.KEYSIGN){
                data.HO_TEN_CHA = data.HO_TEN_CHA ?? thongtinhsba.thongtinbn.HO_TEN_CHA;
                data.HO_TEN_ME = data.HO_TEN_ME ?? thongtinhsba.thongtinbn.HO_TEN_ME;
                data.CAN_NANG = data.CAN_NANG ?? thongtinhsba.thongtinbn.CANNANG;
            }
            thongTinPhieuTruocKhiChinhSua = data;
            data.NGAY_GIO_SINH_TEXT_FORMIO = data.NGAY_GIO_SINH_TEXT ?
                                            moment(data.NGAY_GIO_SINH_TEXT, ['DD/MM/YYYY HH:mm']).toISOString() :
                                            moment(
                                                thongtinhsba.thongtinbn.NGAY_SINH + " " + thongtinhsba.thongtinbn.GIO_SINH_CV130,
                                                ['DD/MM/YYYY HH:mm']
                                            ).toISOString() ;
            data.NGAY_LAP_PHIEU_TEXT_FORMIO = moment(data.NGAY_LAP_PHIEU_TEXT, ['DD/MM/YYYY HH:mm']).toISOString();
            for (var key in data) {
                if(data[key] != null && data[key] != undefined){
                    data[key] = String(data[key]);
                }
            }
            formKiemTruocTiemChungTreSoSinh.submission = {
                data: data
            };
        });
    }

    function themBangKiemTruocTiemChungTreSoSinh(idButton, callBackSuccess, callBackFail) {
        showSelfLoading(idButton);
        formKiemTruocTiemChungTreSoSinh.emit("checkValidity");
        if (!formKiemTruocTiemChungTreSoSinh.checkValidity(null, false, null, true)) {
            hideSelfLoading(idButton);
            return;
        }
        var dataKT = formKiemTruocTiemChungTreSoSinh.submission.data;
        dataKT.NGAY_GIO_SINH_TEXT = moment(dataKT.NGAY_GIO_SINH_TEXT_FORMIO).format("DD/MM/YYYY HH:mm");
        dataKT.NGAY_LAP_PHIEU_TEXT = moment(dataKT.NGAY_LAP_PHIEU_TEXT_FORMIO).format("DD/MM/YYYY HH:mm");
        $.post('cmu_post_CMU_KTTIEMCHUNGTRESOSINH_INS', {
            url: [
                singletonObject.dvtt,
                thongtinhsba.thongtinbn.SOVAOVIEN,
                thongtinhsba.thongtinbn.SOVAOVIEN_DT,
                thongtinhsba.thongtinbn.STT_BENHAN,
                thongtinhsba.thongtinbn.SOBENHAN,
                thongtinhsba.thongtinbn.MA_BENH_NHAN,
                dataKT.SDT_CHA,
                dataKT.SDT_ME,
                dataKT.NGAY_GIO_SINH_TEXT, //NGAY_GIO_SINH
                dataKT.TUOI_THAI_KHI_SINH,
                dataKT.CAN_NANG,
                dataKT.THAN_NHIET,
                dataKT.ME_XN_HBSAG,
                dataKT.KET_QUA_ME_XN_HBSAG,
                dataKT.SUC_KHOE_CHUA_ON_DINH,
                dataKT.SOC_HA_THAN_NHIET,
                dataKT.KHOC_BE_KHONG_KHOC,
                dataKT.DA_MOI_KHONG_HONG,
                dataKT.BU_KEM_BO_BU,
                dataKT.TUOI_THAI_28_TUAN,
                dataKT.TRE_34_TUAN_TUOI,
                dataKT.TRE_2000_ME_CO_HBSAG,
                dataKT.IS_CHI_DINH_KHAC,
                dataKT.CHI_DINH_KHAC,
                dataKT.IS_SANG_LOC_CHUYEN_KHOA,
                dataKT.SANG_LOC_CHUYEN_KHOA,
                dataKT.LY_DO,
                dataKT.KET_QUA,
                dataKT.KET_LUAN,
                dataKT.LOAI_VAC_XIN,
                dataKT.NGUOI_THUC_HIEN,
                dataKT.NGAY_LAP_PHIEU_TEXT, //NGAY_LAP_PHIEU
                singletonObject.makhoa,
                singletonObject.userId
            ].join('```')
        }).done(function (data) {
            dataKT.ID = data;
            callBackSuccess && callBackSuccess(dataKT);
            if(data > 0){
                luuLogHSBATheoBN({
                    SOVAOVIEN: thongtinhsba.thongtinbn.SOVAOVIEN,
                    LOAI: "KTTIEMCHUNGTRESOSINH",
                    NOIDUNGBANDAU: "Thêm mới bảng kiểm trước tiêm chủng trẻ sơ sinh" + data,
                    NOIDUNGMOI: "Thêm mới bảng kiểm trước tiêm chủng trẻ sơ sinh" + data,
                    USERID: singletonObject.userId,
                    ACTION: "ADD",
                });
            }
        }).fail(function () {
            callBackFail && callBackFail();
        });
    }

    function suaBangKiemTruocTiemChungTreSoSinh(idButton, callBackSuccess, callBackFail) {
        showSelfLoading(idButton);
        formKiemTruocTiemChungTreSoSinh.emit("checkValidity");
        if (!formKiemTruocTiemChungTreSoSinh.checkValidity(null, false, null, true)) {
            hideSelfLoading(idButton);
            return;
        }
        var dataKT = formKiemTruocTiemChungTreSoSinh.submission.data;
        dataKT.NGAY_GIO_SINH_TEXT = moment(dataKT.NGAY_GIO_SINH_TEXT_FORMIO).format("DD/MM/YYYY HH:mm");
        dataKT.NGAY_LAP_PHIEU_TEXT = moment(dataKT.NGAY_LAP_PHIEU_TEXT_FORMIO).format("DD/MM/YYYY HH:mm");
        $.post('cmu_post_CMU_KTTIEMCHUNGTRESOSINH_UPD', {
            url: [
                singletonObject.dvtt,
                dataKT.ID,
                dataKT.SDT_CHA,
                dataKT.SDT_ME,
                dataKT.NGAY_GIO_SINH_TEXT, //NGAY_GIO_SINH
                dataKT.TUOI_THAI_KHI_SINH,
                dataKT.CAN_NANG,
                dataKT.THAN_NHIET,
                dataKT.ME_XN_HBSAG,
                dataKT.KET_QUA_ME_XN_HBSAG,
                dataKT.SUC_KHOE_CHUA_ON_DINH,
                dataKT.SOC_HA_THAN_NHIET,
                dataKT.KHOC_BE_KHONG_KHOC,
                dataKT.DA_MOI_KHONG_HONG,
                dataKT.BU_KEM_BO_BU,
                dataKT.TUOI_THAI_28_TUAN,
                dataKT.TRE_34_TUAN_TUOI,
                dataKT.TRE_2000_ME_CO_HBSAG,
                dataKT.IS_CHI_DINH_KHAC,
                dataKT.CHI_DINH_KHAC,
                dataKT.IS_SANG_LOC_CHUYEN_KHOA,
                dataKT.SANG_LOC_CHUYEN_KHOA,
                dataKT.LY_DO,
                dataKT.KET_QUA,
                dataKT.KET_LUAN,
                dataKT.LOAI_VAC_XIN,
                dataKT.NGUOI_THUC_HIEN,
                dataKT.NGAY_LAP_PHIEU_TEXT //NGAY_LAP_PHIEU
            ].join('```')
        }).done(function (data) {
            dataKT.TT = data;
            callBackSuccess && callBackSuccess(dataKT);
            if(data > 0){
                luuLogHSBAChinhSua(thongTinPhieuTruocKhiChinhSua, dataKT, "KTTIEMCHUNGTRESOSINH");
            }
        }).fail(function () {
            callBackFail && callBackFail();
        });
    }

    function xoaBangKiemTruocTiemChungTreSoSinh(idButton, dataKT) {
        idButton && showSelfLoading(idButton);
        if(dataKT.KEYSIGN){
            notifiToClient("Red", "Phiếu đã ký, không thể xóa");
            idButton && hideSelfLoading(idButton);
            return;
        }
        confirmToClient(MESSAGEAJAX.CONFIRM, function(confirm) {
            $.post('cmu_post_CMU_KTTIEMCHUNGTRESOSINH_DEL', {
                url: [
                    singletonObject.dvtt,
                    dataKT.ID,
                ].join('```')
            }).done(function (data) {
                if(data > 0){
                    luuLogHSBATheoBN({
                        SOVAOVIEN: thongtinhsba.thongtinbn.SOVAOVIEN,
                        LOAI: "KTTIEMCHUNGTRESOSINH",
                        NOIDUNGBANDAU: "Xóa bảng kiểm trước tiêm chủng trẻ sơ sinh" + dataKT.ID,
                        NOIDUNGMOI: "Xóa bảng kiểm trước tiêm chủng trẻ sơ sinh" + dataKT.ID,
                        USERID: singletonObject.userId,
                        ACTION: "DELETE",
                    });
                    loadDSKiemTruocTiemChungTreSoSinh();
                    idButton && hideSelfLoading(idButton);
                }
            }).fail(function () {
                idButton && hideSelfLoading(idButton);
            });
        }, function() {
            idButton && hideSelfLoading(idButton);
        });
    }

    function xemBangKiemTruocTiemChungTreSoSinh(idButton, dataKT) {
        idButton && showSelfLoading(idButton);
        getFilesign769("PHIEU_NOITRU_KIEMTRUOCTIEMCHUNGTRESOSINH", dataKT.ID, -1, singletonObject.dvtt,
            thongtinhsba.thongtinbn.SOVAOVIEN, thongtinhsba.thongtinbn.SOVAOVIEN_DT, -1, function(dataKySo) {
                if (dataKySo.length > 0) {
                    getCMUFileSigned769(dataKySo[0].KEYMINIO, "pdf")
                } else {
                    var url = 'cmu_in_cmu_bangkiemtruoctiemdoivoitresosinh?type=pdf&' + $.param({
                        dvtt: singletonObject.dvtt,
                        id: dataKT.ID
                    });
                    previewPdfDefaultModal(url, "preview");
                }
            });
        idButton && hideSelfLoading(idButton);
    }

    function xemKySoBangKiemTruocTiemChungTreSoSinh(dataKT) {
        var url = 'cmu_in_cmu_bangkiemtruoctiemdoivoitresosinh?type=pdf&' + $.param({
            dvtt: singletonObject.dvtt,
            id: dataKT.ID
        });
        previewAndSignPdfDefaultModal({
            url: url,
            idButton: 'kiemtruoctiemchungtresosinh_kyso_action',
        }, function(){
            $("#kiemtruoctiemchungtresosinh_kyso_action").click(function() {
                if(dataKT.NGUOI_THUC_HIEN != singletonObject.userId){
                    notifiToClient("Red", "Phiếu này không phải do bạn thực hiện, không thể ký");
                    return;
                }
                kySoChung({
                    dvtt: singletonObject.dvtt,
                    userId: singletonObject.userId,
                    url: url,
                    loaiGiay: "PHIEU_NOITRU_KIEMTRUOCTIEMCHUNGTRESOSINH",
                    maBenhNhan: thongtinhsba.thongtinbn.MA_BENH_NHAN,
                    soBenhAn: thongtinhsba.thongtinbn.STT_BENHAN,
                    soPhieuDichVu: dataKT.ID,
                    soVaoVien: thongtinhsba.thongtinbn.SOVAOVIEN,
                    soVaoVienDT: thongtinhsba.thongtinbn.SOVAOVIEN_DT,
                    keyword: "Người thực hiện sàng lọc",
                    fileName: "Bảng kiểm trước tiêm chủng đối với trẻ sơ sinh " + thongtinhsba.thongtinbn.TEN_BENH_NHAN,
                }, function(dataKySo) {
                    $("#modalPreviewAndSignPDF").modal("hide");
                    $("#modalbangkiemtruoctiemchungsosinh").modal("hide");
                    loadDSKiemTruocTiemChungTreSoSinh()
                });
            });
        });
    }

    function huyKySoBangKiemTruocTiemChungTreSoSinh(dataKT) {
        if (singletonObject.userId == dataKT.NGUOI_THUC_HIEN) {
            confirmToClient("Bạn có chắc sẽ hủy ký số phiếu này?", function() {
                huykysoFilesign769("PHIEU_NOITRU_KIEMTRUOCTIEMCHUNGTRESOSINH", dataKT.ID, singletonObject.userId, singletonObject.dvtt,
                    thongtinhsba.thongtinbn.SOVAOVIEN, thongtinhsba.thongtinbn.SOVAOVIEN_DT, -1, function(data) {
                        loadDSKiemTruocTiemChungTreSoSinh();
                    })
            }, function () {

            })
        } else {
            notifiToClient("Red", "Bạn không có quyền hủy ký số phiếu này");
        }
    }

    $.extend({
        insertMauHSBAKTTIEMCHUNGTRESS: function () {
            generateFormMauKTTIEMCHUNGTRESS({})
        },
        editMauHSBAKTTIEMCHUNGTRESS: function (rowSelect) {
            var json = JSON.parse(rowSelect.NOIDUNG);
            var dataMau = {}
            json.forEach(function(item) {
                dataMau[item.key] = item.value
            })

            generateFormMauKTTIEMCHUNGTRESS({
                ID: rowSelect.ID,
                TENMAU: rowSelect.TENMAU,
                ...dataMau
            })
        },
        selectMauHSBAKTTIEMCHUNGTRESS: function (rowSelect) {
            var json = JSON.parse(rowSelect.NOIDUNG);
            var dataMau = {
                ...formKiemTruocTiemChungTreSoSinh.submission.data,
            }
            json.forEach(function(item) {
                dataMau[item.key] = item.value
            })
            formKiemTruocTiemChungTreSoSinh.submission = {
                data: {
                    ...dataMau
                }
            }
            $("#modalMauChungJSON").modal("hide");
        },
        getdataMauHSBAKTTIEMCHUNGTRESS: function () {
            var objectNoidung = [];
            getObjectMauKTTIEMCHUNGTRESS().forEach(function(item) {
                if(item.key != 'ID' && item.key != 'TENMAU') {
                    if (formioMauHSBA.submission.data[item.key]){
                        objectNoidung.push({
                            "label": item.label,
                            "value": formioMauHSBA.submission.data[item.key],
                            "key": item.key,
                        })
                    }
                }
            })
            return {
                ID: formioMauHSBA.submission.data.ID,
                TENMAU: formioMauHSBA.submission.data.TENMAU,
                NOIDUNG: JSON.stringify(objectNoidung),
                KEYMAUCHUNG: 'MAUKTTIEMCHUNGTRESS'
            };
        },
        formioHSBAKTTIEMCHUNGTRESSValidate: function() {
            formioMauHSBA.emit("checkValidity");
            if (!formioMauHSBA.checkValidity(null, false, null, true)) {
                return false;
            }
            return true;
        },
    })
    function generateFormMauKTTIEMCHUNGTRESS(dataForm) {
        var jsonForm = getJSONObjectForm(getObjectMauKTTIEMCHUNGTRESS());
        Formio.createForm(document.getElementById('formChiTietMauChungJSON'),
            jsonForm,{}
        ).then(function(form) {
            formioMauHSBA = form;
            formioMauHSBA.submission = {
                data: {
                    ...dataForm
                }
            }
        });
    };
    function getObjectMauKTTIEMCHUNGTRESS() {
        return [
            {
                "label": "ID",
                "key": "ID",
                "type": "textfield",
                others: {
                    hidden: true
                }
            },
            {
                "label": "Tên mẫu",
                "key": "TENMAU",
                "type": "textarea",
                validate: {
                    required: true
                },
                others: {
                    "labelPosition": "left-left",
                    "labelWidth": 10
                }
            },
            {
                "label": "Tuổi thai",
                "key": "TUOI_THAI_KHI_SINH",
                "type": "textfield",
                others: {
                    "labelPosition": "left-left",
                    "labelWidth": 10
                }
            },
            {
                "label": "Thân nhiệt",
                "key": "THAN_NHIET",
                "type": "number",
                others: {
                    "labelPosition": "left-left",
                    "labelWidth": 10
                }
            },
            {
                "label": "Mẹ đã xét nghiệm HbsAg",
                "key": "ME_XN_HBSAG",
                "type": "select",
                others: {
                    "data": {
                        "values": [
                            {
                                "label": "Không",
                                "value": "0"
                            },
                            {
                                "label": "Có",
                                "value": "1"
                            },

                        ]
                    },
                    "labelPosition": "left-left",
                    "labelWidth": 10
                },
            },
            {
                "label": "Tình trạng sức khỏe chưa ổn định",
                "key": "SUC_KHOE_CHUA_ON_DINH",
                "type": "select",
                others: {
                    "data": {
                        "values": [
                            {
                                "label": "Không",
                                "value": "0"
                            },
                            {
                                "label": "Có",
                                "value": "1"
                            },

                        ]
                    },
                    "labelPosition": "left-left",
                    "labelWidth": 10
                },
            },
            {
                "label": "Sốt/Hạ thân nhiệt",
                "key": "SOC_HA_THAN_NHIET",
                "type": "select",
                others: {
                    "data": {
                        "values": [
                            {
                                "label": "Không",
                                "value": "0"
                            },
                            {
                                "label": "Có",
                                "value": "1"
                            },

                        ]
                    },
                    "labelPosition": "left-left",
                    "labelWidth": 10
                },
            },
            {
                "label": "Khóc bé hoặc không khóc",
                "key": "KHOC_BE_KHONG_KHOC",
                "type": "select",
                others: {
                    "data": {
                        "values": [
                            {
                                "label": "Không",
                                "value": "0"
                            },
                            {
                                "label": "Có",
                                "value": "1"
                            },

                        ]
                    },
                    "labelPosition": "left-left",
                    "labelWidth": 10
                },
            },
            {
                "label": "Da, môi không hồng",
                "key": "DA_MOI_KHONG_HONG",
                "type": "select",
                others: {
                    "data": {
                        "values": [
                            {
                                "label": "Không",
                                "value": "0"
                            },
                            {
                                "label": "Có",
                                "value": "1"
                            },

                        ]
                    },
                    "labelPosition": "left-left",
                    "labelWidth": 10
                },
            },
            {
                "label": "Bú kém hoặc bỏ bú",
                "key": "BU_KEM_BO_BU",
                "type": "select",
                others: {
                    "data": {
                        "values": [
                            {
                                "label": "Không",
                                "value": "0"
                            },
                            {
                                "label": "Có",
                                "value": "1"
                            },

                        ]
                    },
                    "labelPosition": "left-left",
                    "labelWidth": 10
                },
            },
            {
                "label": "Tuổi thai < 28 tuần",
                "key": "TUOI_THAI_28_TUAN",
                "type": "select",
                others: {
                    "data": {
                        "values": [
                            {
                                "label": "Không",
                                "value": "0"
                            },
                            {
                                "label": "Có",
                                "value": "1"
                            },

                        ]
                    },
                    "labelPosition": "left-left",
                    "labelWidth": 10
                },
            },
            {
                "label": "Trẻ < 34 tuần tuổi",
                "key": "TRE_34_TUAN_TUOI",
                "type": "select",
                others: {
                    "data": {
                        "values": [
                            {
                                "label": "Không",
                                "value": "0"
                            },
                            {
                                "label": "Có",
                                "value": "1"
                            },

                        ]
                    },
                    "labelPosition": "left-left",
                    "labelWidth": 10
                },
            },
            {
                "label": "8. Suy giảm miễn dịch chưa xác định mức độ hoặc mức độ nặng, có biểu hiện lâm sàng nghi nhiễm HIV",
                "key": "TRE_2000_ME_CO_HBSAG",
                "type": "select",
                others: {
                    "data": {
                        "values": [
                            {
                                "label": "Không",
                                "value": "0"
                            },
                            {
                                "label": "Có",
                                "value": "1"
                            },

                        ]
                    },
                    "labelPosition": "left-left",
                    "labelWidth": 10
                },
            },
            {
                "label": "Các chống chỉ định khác",
                "key": "IS_CHI_DINH_KHAC",
                "type": "select",
                others: {
                    "data": {
                        "values": [
                            {
                                "label": "Không",
                                "value": "0"
                            },
                            {
                                "label": "Có",
                                "value": "1"
                            },

                        ]
                    },
                    "labelPosition": "left-left",
                    "labelWidth": 10
                },
            },
            {
                "label": "Ghi rõ",
                "key": "CHI_DINH_KHAC",
                "type": "textfield",
                others: {
                    "customConditional": "show = data.IS_CHI_DINH_KHAC == 1;",
                    "labelPosition": "left-left",
                    "labelWidth": 10
                },
            },
            {
                "label": "Khám sàng lọc theo chuyên khoa",
                "key": "IS_SANG_LOC_CHUYEN_KHOA",
                "type": "select",
                others: {
                    "data": {
                        "values": [
                            {
                                "label": "Không",
                                "value": "0"
                            },
                            {
                                "label": "Có",
                                "value": "1"
                            },

                        ]
                    },
                    "labelPosition": "left-left",
                    "labelWidth": 10
                },
            },
            {
                "label": "Ghi rõ",
                "key": "SANG_LOC_CHUYEN_KHOA",
                "type": "textfield",
                others: {
                    "customConditional": "show = data.IS_SANG_LOC_CHUYEN_KHOA == 1;",
                    "labelPosition": "left-left",
                    "labelWidth": 10
                }
            },
            {
                "label": "Lý do",
                "key": "LY_DO",
                "type": "textfield",
                others: {
                    "customConditional": "show = data.IS_SANG_LOC_CHUYEN_KHOA == 1;",
                    "labelPosition": "left-left",
                    "labelWidth": 10
                }
            },
            {
                "label": "Kết quả",
                "key": "KET_QUA",
                "type": "textfield",
                others: {
                    "customConditional": "show = data.IS_SANG_LOC_CHUYEN_KHOA == 1;",
                    "labelPosition": "left-left",
                    "labelWidth": 10
                }
            },
            {
                "label": "Kết luận",
                "key": "KET_LUAN",
                "type": "textfield",
                others: {
                    "customConditional": "show = data.IS_SANG_LOC_CHUYEN_KHOA == 1;",
                    "labelPosition": "left-left",
                    "labelWidth": 10
                }
            },
        ];
    };
})



