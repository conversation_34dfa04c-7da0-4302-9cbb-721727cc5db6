CREATE OR REPLACE PROCEDURE ris_update_ketqua_cdha_ct (
    p_so_phieu_cdha            VARCHAR2,
    p_dvtt                     VARCHAR2,
    p_ma_cdha                  NUMBER,
    p_ket_qua                  CLOB,
    p_ket_luan                 CLOB,
    p_loidan                   VARCHAR2,
    p_bacsithuchien            VARCHAR2 DEFAULT NULL,
    p_kythuatvien              VARCHAR2 DEFAULT NULL,
    p_thoigianbatdauthuchien   VARCHAR2 DEFAULT NULL,
    cur                        OUT                        SYS_REFCURSOR
) IS

    vcount                     NUMBER(11);
    p_noitru                   NUMBER;
    v_tenbacsithuchien         VARCHAR2(300);
    v_thamsocmu                VARCHAR2(255) := 0;
    v_khoa                     NUMBER(11) := 0;
    v_sovaovien                NUMBER(11) := 0;
    v_sovaovien_dt             NUMBER(11) := 0;
    v_ketqua                   CLOB := replace(replace(p_ket_qua, chr(13)
                                                || chr(13), chr(13)), chr(10)
                                                                      || chr(10), chr(10));
    v_ketluan                  CLOB := replace(replace(p_ket_luan, chr(13)
                                                  || chr(13), chr(13)), chr(10)
                                                                        || chr(10), chr(10));
    v_thoigianbatdauthuchien   DATE := TO_DATE(p_thoigianbatdauthuchien, 'dd/mm/yyyy HH24:MI:SS');
    v_result                   NUMBER;
    p_mabenhnhan               NUMBER;
    v_id_dieutri number;
	v_stt_dotdieutri number;
BEGIN
    vcount := 0;
BEGIN
SELECT
    COUNT(1)
INTO p_noitru
FROM
    his_manager.noitru_cd_cdha t
WHERE
    t.so_phieu_cdha = p_so_phieu_cdha
  AND t.dvtt = p_dvtt;

EXCEPTION
        WHEN no_data_found THEN
            p_noitru := 0;
END;

BEGIN
SELECT
    nv.ten_nhanvien
INTO v_tenbacsithuchien
FROM
    his_fw.dm_nhanvien nv
WHERE
    nv.ma_nhanvien = p_bacsithuchien;

EXCEPTION
        WHEN no_data_found THEN
            v_tenbacsithuchien := '';
END;

    IF p_noitru = 0 THEN
SELECT
    sovaovien,
    mabenhnhan
INTO
    v_sovaovien,
    p_mabenhnhan
FROM
    his_manager.kb_cd_cdha
WHERE
    dvtt = p_dvtt
  AND so_phieu_cdha = p_so_phieu_cdha;

IF v_thamsocmu = '1' THEN
SELECT
    khoa_thanhtoan
INTO v_khoa
FROM
    kb_phieuthanhtoan
WHERE
    dvtt = p_dvtt
  AND sovaovien = v_sovaovien;

IF v_khoa = 1 THEN
                OPEN cur FOR SELECT
                                    vcount AS tt
                             FROM
                                    dual;

return;
END IF;

END IF;

UPDATE his_manager.kb_cd_cdha
SET
    trang_thai_chuan_doan = 1,
    ky_thuat_vien = p_kythuatvien
WHERE
    dvtt = p_dvtt
  AND so_phieu_cdha = p_so_phieu_cdha;

UPDATE kb_cd_cdha_ct ct
SET
    ket_qua = v_ketqua,
    mo_ta = v_ketluan,
    mo_ta_xml5 = utl_i18n.unescape_reference(regexp_replace(v_ketluan, '<.*?>', '')),
    ket_qua_xml5 = utl_i18n.unescape_reference(regexp_replace(v_ketqua, '<.*?>', '')),
    loidanbacsi = p_loidan,
    da_chan_doan = 1,
    kythuatvien = p_kythuatvien,
    bacsi_thuchien = v_tenbacsithuchien,
    mabs_thuchien = p_bacsithuchien,
    nguoi_thuc_hien = p_bacsithuchien,
    ngay_th_yl = v_thoigianbatdauthuchien,
    ma_bs_doc_kq = p_bacsithuchien,
    ngay_thuc_hien = SYSDATE--NVL(NGAY_THUC_HIEN,SYSDATE)
WHERE
    dvtt = p_dvtt
  AND so_phieu_cdha = p_so_phieu_cdha
  AND ma_cdha = p_ma_cdha;

v_result := smartca_save_signed_grv(p_dvtt, uuid_generate(p_dvtt), 0, /*1: Nội trú + BANT, 0: Ngoại trú*/ p_mabenhnhan, v_sovaovien, 0, NULL,--v_sobenhan,

         NULL, 'PHIEUKQ_CDHA_RISPACS', NULL, p_so_phieu_cdha, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, p_bacsithuchien, p_bacsithuchien

        , 1, 0, 1, p_ma_cdha, NULL, 0);

        vcount := SQL%rowcount;
COMMIT;
ELSE
SELECT
    sovaovien,
    sovaovien_dt,
    mabenhnhan,
    stt_dotdieutri,id_dieutri
INTO
    v_sovaovien,
    v_sovaovien_dt,
    p_mabenhnhan,
    v_stt_dotdieutri,
    v_id_dieutri
FROM
    his_manager.noitru_cd_cdha
WHERE
    dvtt = p_dvtt
  AND so_phieu_cdha = p_so_phieu_cdha;

IF v_thamsocmu = '1' THEN
SELECT
    khoa_thanhtoan
INTO v_khoa
FROM
    noitru_phieuthanhtoan
WHERE
    dvtt = p_dvtt
  AND sovaovien = v_sovaovien
  AND sovaovien_dt = v_sovaovien_dt;

IF v_khoa = 1 THEN
                OPEN cur FOR SELECT
                                    vcount AS tt
                             FROM
                                    dual;

return;
END IF;

END IF;

UPDATE his_manager.noitru_cd_cdha
SET
    trang_thai_chuan_doan = 1,
    ky_thuat_vien = p_kythuatvien
WHERE
    dvtt = p_dvtt
  AND so_phieu_cdha = p_so_phieu_cdha;

UPDATE noitru_cd_cdha_chi_tiet ct
SET
    ket_qua = v_ketqua,
    mo_ta = v_ketluan,
    ket_qua_xml5 = regexp_replace(v_ketqua, '<[^>]+>', ''),
    mo_ta_xml5 = regexp_replace(v_ketluan, '<[^>]+>', ''),
    loidanbacsi = p_loidan,
    da_chan_doan = 1,
    kythuatvien = p_kythuatvien,
    bacsi_thuchien = v_tenbacsithuchien,
    mabs_thuchien = p_bacsithuchien,
    nguoi_thuc_hien = p_bacsithuchien,
    ngay_th_yl = v_thoigianbatdauthuchien,
    ma_bs_doc_kq = p_bacsithuchien,
    ngay_thuc_hien = SYSDATE --NVL(NGAY_THUC_HIEN,SYSDATE)
WHERE
    dvtt = p_dvtt
  AND so_phieu_cdha = p_so_phieu_cdha
  AND ma_cdha = p_ma_cdha;

v_result := smartca_save_signed_grv(p_dvtt, uuid_generate(p_dvtt), 1, /*1: Nội trú + BANT, 0: Ngoại trú*/ p_mabenhnhan, v_sovaovien, v_sovaovien_dt, NULL,--v_sobenhan,

         v_stt_dotdieutri, 'PHIEUKQ_CDHA_RISPACS', v_id_dieutri, p_so_phieu_cdha, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, p_bacsithuchien

        , p_bacsithuchien, 1, 0, 1, p_ma_cdha, NULL, 0);

        vcount := SQL%rowcount;
COMMIT;
END IF;

    v_result := cmu_thoigian_nhanvien_ins(p_dvtt, p_bacsithuchien, v_sovaovien, p_mabenhnhan, p_noitru, TO_CHAR(v_thoigianbatdauthuchien

    , 'DD/MM/YYYY/ HH24:MI'), TO_CHAR(SYSDATE, 'DD/MM/YYYY/ HH24:MI'), p_so_phieu_cdha, p_ma_cdha, 'CDHA');

    v_result := cmu_thoigian_maycls_ins(p_dvtt, v_sovaovien, p_noitru, p_so_phieu_cdha, p_ma_cdha, 'CDHA');
OPEN cur FOR SELECT
                     vcount AS tt
                 FROM
                     dual;

END;