function getUrlPrintCDHA(listId, listIcd) {
    var ret = getThongtinRowSelected(listId)
    var icd_khoadt = "";
    var ten_khoadt = "";
    var icdphu = "";
    var allICD = getAllRowDataJqgrid(listIcd? listIcd: "tdt_list_icd");
    allICD.forEach(function(item) {
        if(item.BENHCHINH == '1') {
            icd_khoadt = item.ICD;
            ten_khoadt = item.TENBENH;

        } else {
            icdphu = icdphu + item.ICD + "-"+ item.TENBENH + ";";
        }
    })
    var arr = [thongtinhsba.thongtinbn.MA_BENH_NHAN,
        ret.CO_BHYT == 1? 0: 1,
        ret.SO_PHIEU_CDHA, singletonObject.dvtt,
        thongtinhsba.thongtinbn.SOBENHAN,
        thongtinhsba.thongtinbn.STT_BENHAN,
        todieutriObject.STT_DOTDIEUTRI,
        todieutriObject.STT_DIEUTRI,
        icd_khoadt,
        ten_khoadt,
        icdphu,
        "0",
        todieutriObject.SOVAOVIEN,
        todieutriObject.SOVAOVIEN_DT, 0, 1];
    var url = "noitru_inphieucdha_svv?url=" + convertArray(arr);
    return url;
}
function getUrlPrintXN(listId, listIcd) {
    var ret = getThongtinRowSelected(listId)
    var icd_khoadt = "";
    var ten_khoadt = "";
    var icdphu = "";
    var allICD = getAllRowDataJqgrid(listIcd? listIcd: "tdt_list_icd");
    allICD.forEach(function(item) {
        if(item.BENHCHINH == '1') {
            icd_khoadt = item.ICD;
            ten_khoadt = item.TENBENH;

        } else {
            icdphu = icdphu + item.ICD + "-"+ item.TENBENH + ";";
        }
    })
    var arr = [thongtinhsba.thongtinbn.MA_BENH_NHAN,
        ret.CO_BHYT == 1? 0: 1,
        ret.SO_PHIEU_XN, singletonObject.dvtt,
        thongtinhsba.thongtinbn.SOBENHAN,
        thongtinhsba.thongtinbn.STT_BENHAN,
        todieutriObject.STT_DOTDIEUTRI,
        todieutriObject.STT_DIEUTRI,
        icd_khoadt,
        ten_khoadt,
        icdphu,
        "0",
        todieutriObject.SOVAOVIEN,
        todieutriObject.SOVAOVIEN_DT, 0];
    var url = "noitru_inphieuxetnghiem_svv?url=" + convertArray(arr);
    return url;
}

function getURLPrintTTPT(listId, listIcd) {
    var ret = getThongtinRowSelected(listId)
    var icd_khoadt = "";
    var ten_khoadt = "";
    var icdphu = "";
    var allICD = getAllRowDataJqgrid(listIcd? listIcd: "tdt_list_icd");
    allICD.forEach(function(item) {
        if(item.BENHCHINH == '1') {
            icd_khoadt = item.ICD;
            ten_khoadt = item.TENBENH;

        } else {
            icdphu = icdphu + item.ICD + "-"+ item.TENBENH + ";";
        }
    })
    var arr = [thongtinhsba.thongtinbn.MA_BENH_NHAN,
        ret.CO_BHYT == 1? 0: 1,
        ret.SO_PHIEU_DICHVU,
        ret.MA_LOAI_DICHVU,
        0, //ret.CHUYEN_KHOA
        0, //ret.CHI_TIET_CHUYEN_KHOA
        singletonObject.dvtt,
        thongtinhsba.thongtinbn.SOBENHAN,
        thongtinhsba.thongtinbn.STT_BENHAN,
        todieutriObject.STT_DOTDIEUTRI,
        todieutriObject.STT_DIEUTRI,
        icd_khoadt,
        ten_khoadt,
        icdphu,
        "0",
        todieutriObject.SOVAOVIEN,
        todieutriObject.SOVAOVIEN_DT, 0];
    var url = "noitru_inphieuttpt_svv?url=" + convertArray(arr);
    return url;
}

function getURLPrintTTPTQuyTrinh2(listId, phauThuatObj) {
    let ret = getThongtinRowSelected(listId)
    let arr = [
        thongtinhsba.thongtinbn.MA_BENH_NHAN,
        ret.CO_BHYT == 1 ? 0 : 1,
        ret.SO_PHIEU_DICHVU,
        ret.MA_LOAI_DICHVU,
        0, //ret.CHUYEN_KHOA
        0, //ret.CHI_TIET_CHUYEN_KHOA
        singletonObject.dvtt,
        thongtinhsba.thongtinbn.SOBENHAN,
        thongtinhsba.thongtinbn.STT_BENHAN,
        phauThuatObj.STT_DOTDIEUTRI,
        phauThuatObj.STT_DIEUTRI,
        "", "", "", "0",
        phauThuatObj.SOVAOVIEN,
        phauThuatObj.SOVAOVIEN_DT, 0
    ];
    return "noitru_inphieuttpt_svv?url=" + convertArray(arr);
}

function hsbaFilekysoXQUANGCTMRI(rowData) {
    var kyhieuphieu = rowData.KEYSIGNCONFIRM? "PHIEUKQ_CT_XQUANG_MRI_CONFIRM": "PHIEUKQ_CT_XQUANG_MRI";
    if(rowData.MOTA_LOAI_CDHA == 'TDCN') {
        kyhieuphieu = rowData.KEYSIGNCONFIRM? "PHIEUKQ_DIENTIM_CONFIRM": "PHIEUKQ_DIENTIM";
    }

    getFilesign769(
        kyhieuphieu,
        rowData.SO_PHIEU_CDHA,
        -1,
        singletonObject.dvtt,
        rowData.SOVAOVIEN,
        rowData.SOVAOVIEN_DT,
        rowData.MA_CDHA,
        function(dataFile) {
            if(dataFile.ERROR) {
                notifiToClient("Red", "Lỗi xem file ký số")
            } else {
                var url = "smartca-get-signed-file-minio?keyminio=" + dataFile[0].KEYMINIO + "&type=pdf";
                $.ajax({
                    method: "POST", url: url, contentType: "charset=utf-8"
                }).done(function (data) {
                    var pdf = 'data:application/pdf;base64,' +data.FILE;
                    $("#modalFormketquacls").modal("show")
                    var xhr = new XMLHttpRequest();
                    xhr.open("GET", pdf, true);

                    xhr.responseType = "blob";
                    xhr.onload = function (e) {
                        if (this.status === 200) {
                            var file = window.URL.createObjectURL(this.response);
                            $("#hsba_kq_cls_kyso").prop('src', file)
                        }
                    };
                    xhr.send();
                }).fail(function() {
                    notifiToClient("Red", "Không tìm thấy file đã ký số")
                });
            }
        }
    )
}

function getThongtinTTPTGlobal(data, callback) {
    var arr = [
        data.SO_PHIEU_DICHVU,
        1,
        data.STT_BENHAN,
        data.STT_DOTDIEUTRI,
        data.STT_DIEUTRI,
        '',
        data.MA_DV,
        singletonObject.dvtt, "0",
        0,
        data.SOVAOVIEN,
        data.SOVAOVIEN_DT];
    var url3 = "pttt_select_ketqua_svv?url=" + convertArray(arr);
    $.getJSON(url3, function (result) {
        callback(result);
    })
}

var idContainerTuongTrinhTTPT = "phauthuat_tuongtrinh_container";
var idCanvasTuongTrinhTTPT = 'phauthuat_tuongtrinh_canvas';
var painterObjectTuongTrinhTTPT;
$(function() {

    var danhsachGoidichvu = {}
    var goiBHYT = 0;

    let dataChungChiHanhNghe = {};
    $.get("cmu_list_LAYDS_CHUNGCHI_HANHNGHE_NV?url="+convertArray(
        [singletonObject.dvtt, '-1'])
    ).done(function(data) {
        $.each(data, function(_, v) {
            v.CHUNGCHI_HANHNGHE = v.CHUNGCHI_HANHNGHE ? v.CHUNGCHI_HANHNGHE : '';
            dataChungChiHanhNghe[v.VALUE.toString()] = v;
        });
    });

    $('#tdt-xn-dropdown p').click(function () {
        var attrId = $(this).attr('data-id');
        if(attrId == 'xoaphieu') {
            confirmToClient("Bạn có chắc sẽ xóa phiếu chỉ định này?", function() {
                showLoaderIntoWrapId("tdt-xetnghiem-tab");
                var ret = getThongtinRowSelected("tdt_list_phieuxetnghiem")
                var dataBN = thongtinhsba.thongtinbn;
                if(checkThanhtoanXetnghiem(ret.SO_PHIEU_XN, dataBN) == 1) {
                    hideLoaderIntoWrapId("tdt-xetnghiem-tab");
                    return notifiToClient("Red", "Phiếu xét nghiệm đã được thanh toán, không thể cập nhật.")
                }
                var url = "noitru_xetnghiem_delete_bangcha";
                var arr = [ret.STT_DIEUTRI, dataBN.STT_BENHAN, dataBN.STT_DOTDIEUTRI, ret.SO_PHIEU_XN, singletonObject.dvtt];
                $.post(url, {
                    url: convertArray(arr)
                }).done(function (data) {
                    if (data == "1") {
                        return notifiToClient("Red","Bệnh nhân đã thanh toán");
                    }
                    else if (data == "2") {
                        return notifiToClient("Red","Bệnh nhân đã được thực hiện xét nghiệm");
                    } else if (data == "3") {
                        return notifiToClient("Red","Bệnh nhân đã được nhập kết quả xét nghiệm");
                    }
                    else {
                        var logXetNghiem = [
                            "Thời gian y lệnh: " + ret.NGAY_CHI_DINH + " " + ret.GIO_CHI_DINH,
                            "Tờ điều trị số: " + todieutriObject.STT_DIEUTRI,
                            "Bác sĩ điều trị: "+ todieutriObject['TENBSDIEUTRI'],
                            "Hình thức: "+ ($("#tdt_xn_hinhthuc").val() == 0? "BHYT": "Thu phí"),
                            "Số phiếu xét nghiệm: "+ ret.SO_PHIEU_XN,
                        ]
                        var dsXN = []
                        var listDataPhieuXN = getAllRowDataJqgrid("tdt_list_phieuxetnghiem");
                        listDataPhieuXN.forEach(function (item) {
                            if(item.SO_PHIEU_XN == ret.SO_PHIEU_XN){
                                dsXN.push(item.MA_XN + " - "+ item.TEN_XN + " - Số lượng: "+ item.SO_LUONG + " lần")
                            }

                        })
                        logXetNghiem.push("Danh sách xét nghiệm: "+ dsXN.join("; "))
                        luuLogHSBATheoBN({
                            SOVAOVIEN: thongtinhsba.thongtinbn.SOVAOVIEN,
                            LOAI: LOGHSBALOAI.XETNGHIEM.KEY,
                            NOIDUNGBANDAU: logXetNghiem.join("; "),
                            NOIDUNGMOI: "",
                            USERID: singletonObject.userId,
                            ACTION: LOGHSBAACTION.DELETE.KEY,
                        })
                        notifiToClient("Green","Xóa thành công");
                        loadDSXetnghiem(" ", dataBN);
                        loadDSPhieuXetnghiem(dataBN)
                        enableModeButtonXN('add')
                    }
                    $("#tdt_xn_hinhthuc").prop("disabled", false);
                }).always(function() {
                    hideLoaderIntoWrapId("tdt-xetnghiem-tab");
                }).fail(function() {
                    return notifiToClient("Red","Xóa thất bại");
                });
            }, function () {

            })

        }
        if(attrId == 'xemphieu') {
            var ret = getThongtinRowSelected("tdt_list_phieuxetnghiem")
            getFilesign769(
                "PHIEUCD_NOITRU_XN",
                ret.SO_PHIEU_XN,
                singletonObject.userId,
                singletonObject.dvtt,
                thongtinhsba.thongtinbn.SOVAOVIEN,
                todieutriObject.SOVAOVIEN_DT,
                -1,
                function(data) {
                    if(data.length > 0) {
                        getCMUFileSigned769(data[0].KEYMINIO,"pdf")
                    } else {
                        previewPdfDefaultModal(getUrlPrintXN("tdt_list_phieuxetnghiem"), "previewtdt_list_phieuxetnghiem");
                    }
                })
        }
        if(attrId == 'huykyso') {
            confirmToClient("Bạn có chắc sẽ hủy ký số chỉ định này?", function() {
                showLoaderIntoWrapId("tdt-xetnghiem-tab");
                var ret = getThongtinRowSelected("tdt_list_phieuxetnghiem")
                huykysoFilesign769("PHIEUCD_NOITRU_XN",
                    ret.SO_PHIEU_XN, singletonObject.userId, singletonObject.dvtt, todieutriObject.SOVAOVIEN, todieutriObject.SOVAOVIEN_DT, -1, function() {
                        hideLoaderIntoWrapId("tdt-xetnghiem-tab");
                    })
            }, function () {

            })
        }
    });

    $('#tdt-cdha-dropdown p').click(function () {
        var attrId = $(this).attr('data-id');
        if(attrId == 'xoaphieu') {
            confirmToClient("Bạn có chắc sẽ xóa phiếu chỉ định này?", function() {
                showLoaderIntoWrapId("tdt-chandoanhinhanh-tab");
                var ret = getThongtinRowSelected("tdt_list_phieucdha")
                var dataBN = thongtinhsba.thongtinbn;
                showSelfLoading("action_tdt_xoacdha")
                if(checkThanhtoanCDHA(ret.SO_PHIEU_CDHA, dataBN) == 1) {
                    hideLoaderIntoWrapId("tdt-chandoanhinhanh-tab");
                    return notifiToClient("Red", "Phiếu CDHA đã được thanh toán, không thể cập nhật.")
                }
                var url = "noitru_cdha_delete_bangcha";
                var arr = [ret.SO_PHIEU_CDHA, singletonObject.dvtt, ret.STT_DIEUTRI, dataBN.STT_BENHAN, dataBN.STT_DOTDIEUTRI];
                $.post(url, {
                    url: convertArray(arr)
                }).done(function (data) {
                    if(data == "RIS.1"){
                        hideLoaderIntoWrapId("tdt-chandoanhinhanh-tab");
                        return notifiToClient("Red","Trạng thái hiện tại trên RIS không cho phép xóa phiếu chỉ định này");
                    }
                    if (data == "ERRLOGIN") {
                        hideLoaderIntoWrapId("tdt-chandoanhinhanh-tab");
                        return notifiToClient("Red","Xác thực đăng nhập RIS Connector thất bại, Vui lòng kiểm tra lại thông tin cấu hình kết nối RIS");
                    }
                    if (data == "RISFAIL") {

                        $.confirm({
                            title: 'Xác nhận!',
                            type: 'orange',
                            content: 'Hủy phiếu trên RIS thất bại. Bạn có muốn xóa phiếu?',
                            buttons: {
                                warning: {
                                    btnClass: 'btn-warning',
                                    text: "Tiếp tục",
                                    action: function(){
                                        $.post("noitru_cdha_delete_bangcha_his", {
                                            url: convertArray(arr)
                                        }).done(function (dt) {
                                            if (dt == "1") {
                                                return notifiToClient("Red", "Bệnh nhân đã thanh toán");
                                            }
                                            if (dt == "2") {
                                                return notifiToClient("Red", "Bệnh nhân đã được thực hiện chẩn đoán hình ảnh");
                                            }
                                            if (dt == "3") {
                                                return notifiToClient("Red", "Phiếu có ca chụp đang được thực hiện bởi MCAP");
                                            }
                                            logXoaCDHATodieutriTungDV(ret)
                                            notifiToClient("Green","Xóa thành công");
                                            loadDSCDHA(" ", dataBN);
                                            loadDSPhieuCDHA(dataBN)
                                            enableModeButtonCDHA('add')
                                        }).always(function() {
                                            hideLoaderIntoWrapId("tdt-chandoanhinhanh-tab");
                                        });
                                    }
                                },
                                cancel: function () {
                                    hideSelfLoading("action_tdt_xoacdha")
                                }
                            }
                        });
                    }
                    if (data == "1") {
                        hideLoaderIntoWrapId("tdt-chandoanhinhanh-tab");
                        return notifiToClient("Red","Bệnh nhân đã thanh toán");
                    }
                    if (data == "2") {
                        hideLoaderIntoWrapId("tdt-chandoanhinhanh-tab");
                        return notifiToClient("Red","Bệnh nhân đã được thực hiện cdha");
                    }
                    if (data == "3") {
                        hideLoaderIntoWrapId("tdt-chandoanhinhanh-tab");
                        return notifiToClient("Red","Phiếu có ca chụp đang được thực hiện bởi MCAP");
                    }
                    else {
                        logXoaCDHATodieutriTungDV(ret)
                        notifiToClient("Green","Xóa thành công");
                        loadDSCDHA(" ", dataBN);
                        loadDSPhieuCDHA(dataBN)
                        enableModeButtonCDHA('add')
                    }
                    hideLoaderIntoWrapId("tdt-chandoanhinhanh-tab");
                    $("#tdt_cdha_hinhthuc").prop("disabled", false);
                    $("#tdt_cdha_phongcdha").prop("disabled", false);
                }).fail(function() {
                    notifiToClient("Green","Xóa thất bại");
                })
            }, function () {

            })

        }
        if(attrId == 'xemphieu') {

            var ret = getThongtinRowSelected("tdt_list_phieucdha")
            getFilesign769(
                "PHIEUCD_NOITRU_CDHA",
                ret.SO_PHIEU_CDHA,
                singletonObject.userId,
                singletonObject.dvtt,
                thongtinhsba.thongtinbn.SOVAOVIEN,
                todieutriObject.SOVAOVIEN_DT,
                -1,
                function(data) {
                    if(data.length > 0) {
                        getCMUFileSigned769(data[0].KEYMINIO,"pdf")
                    } else {
                        previewPdfDefaultModal(getUrlPrintCDHA("tdt_list_phieucdha"), "previewtdt_list_phieucdha");
                    }
                })
        }
        if(attrId == 'huykyso') {
            confirmToClient("Bạn có chắc sẽ hủy ký số chỉ định này?", function() {
                showLoaderIntoWrapId("tdt-chandoanhinhanh-tab");
                var ret = getThongtinRowSelected("tdt_list_phieucdha")
                huykysoFilesign769("PHIEUCD_NOITRU_CDHA",
                    ret.SO_PHIEU_CDHA, singletonObject.userId, singletonObject.dvtt,  todieutriObject.SOVAOVIEN, todieutriObject.SOVAOVIEN_DT, -1, function() {
                        hideLoaderIntoWrapId("tdt-chandoanhinhanh-tab");
                    })
            }, function () {

            })
        }
    });

    $('#tdt-ttpt-dropdown p').click(function () {
        var attrId = $(this).attr('data-id');
        if(attrId == 'xoaphieu') {
            confirmToClient("Bạn có chắc sẽ xóa phiếu chỉ định này?", function() {
                showLoaderIntoWrapId("tdt-ttpt-tab");
                var ret = getThongtinRowSelected("tdt_list_phieuttpt")
                var dataBN = thongtinhsba.thongtinbn;
                if(checkThanhtoanTTPT(ret.SO_PHIEU_DICHVU, dataBN) == 1) {
                    hideLoaderIntoWrapId("tdt-ttpt-tab");
                    return notifiToClient("Red", "Phiếu xét nghiệm đã được thanh toán, không thể cập nhật.")
                }
                var url = "noitru_ttpt_delete_bangcha_svv";
                var arr = [ ret.SO_PHIEU_DICHVU, singletonObject.dvtt, ret.STT_DIEUTRI, dataBN.STT_BENHAN, dataBN.STT_DOTDIEUTRI, ret.SOVAOVIEN, ret.SOVAOVIEN_DT];
                $.post(url, {
                    url: convertArray(arr)
                }).done(function (data) {
                    if (data == "1") {
                        return notifiToClient("Red","Bệnh nhân đã thanh toán");
                    }
                    else if (data == "2") {
                        return notifiToClient("Red","Bệnh nhân đã được thực hiện xét nghiệm");
                    } else if (data == "3") {
                        return notifiToClient("Red","Bệnh nhân đã được nhập kết quả xét nghiệm");
                    }
                    else {
                        var logTTPT = [
                            "Thời gian y lệnh: " + ret.NGAY_CHI_DINH + " " + ret.GIO_CHI_DINH,
                            "Tờ điều trị số: " + todieutriObject.STT_DIEUTRI,
                            "Bác sĩ điều trị: "+ todieutriObject['TENBSDIEUTRI'],
                            "Hình thức: "+ ($("#tdt_ttpt_hinhthuc").val() == 0? "BHYT": "Thu phí"),
                            "Số phiếu thủ thuật/phẫu thuật: "+ ret.SO_PHIEU_DICHVU,
                        ]
                        var dsTTPT = []
                        var listDataPhieuTTPT = getAllRowDataJqgrid("tdt_list_phieuttpt");
                        listDataPhieuTTPT.forEach(function (item) {
                            if(item.SO_PHIEU_DICHVU == ret.SO_PHIEU_DICHVU){
                                dsTTPT.push(item.MA_CDHA + " - "+ item.TEN_CDHA + " - Số lượng: "+ item.SO_LUONG + " lần")
                            }

                        })
                        logTTPT.push("Danh sách thủ thuật/phẫu thuật: "+ dsTTPT.join("; "))
                        luuLogHSBATheoBN({
                            SOVAOVIEN: thongtinhsba.thongtinbn.SOVAOVIEN,
                            LOAI: LOGHSBALOAI.TTPT.KEY,
                            NOIDUNGBANDAU: logTTPT.join("; "),
                            NOIDUNGMOI: "",
                            USERID: singletonObject.userId,
                            ACTION: LOGHSBAACTION.DELETE.KEY,
                        })
                        notifiToClient("Green","Xóa thành công");
                        loadDSTTPT(" ", dataBN);
                        loadDSPhieuTTPT(dataBN)
                        enableModeButtonTTPT('add')
                    }
                    $("#tdt_ttpt_hinhthuc").prop("disabled", false);
                    $("#tdt_ttpt_phongttpt").prop("disabled", false);
                    $("#tdt_ttpt_loaittpt").prop("disabled", false);
                }).always(function() {
                    hideLoaderIntoWrapId("tdt-ttpt-tab");
                }).fail(function() {
                    notifiToClient("Red","Xóa thất bại");
                });
            }, function () {

            })

        }
        if(attrId == 'xemphieu') {
            var ret = getThongtinRowSelected("tdt_list_phieuttpt")
            getFilesign769(
                "PHIEUCD_NOITRU_TTPT",
                ret.SO_PHIEU_DICHVU,
                singletonObject.userId,
                singletonObject.dvtt,
                thongtinhsba.thongtinbn.SOVAOVIEN,
                todieutriObject.SOVAOVIEN_DT ,
                -1,
                function(data) {
                    if(data.length > 0) {
                        getCMUFileSigned769(data[0].KEYMINIO,"pdf")
                    } else {
                        previewPdfDefaultModal(getURLPrintTTPT("tdt_list_phieuttpt"), "previewtdt_list_phieuttpt");
                    }
                })
        }
        if(attrId == 'huykyso') {
            confirmToClient("Bạn có chắc sẽ hủy ký số chỉ định này?", function() {
                showLoaderIntoWrapId("tdt-ttpt-tab");
                var ret = getThongtinRowSelected("tdt_list_phieuttpt")
                huykysoFilesign769("PHIEUCD_NOITRU_TTPT",
                    ret.SO_PHIEU_DICHVU, singletonObject.userId, singletonObject.dvtt,  todieutriObject.SOVAOVIEN, todieutriObject.SOVAOVIEN_DT, -1, function() {
                        hideLoaderIntoWrapId("tdt-ttpt-tab");
                    })
            }, function () {

            })
        }
    });

    $("#tdt-xetnghiem").click(function() {
        loadDSXetnghiem("", thongtinhsba.thongtinbn);
        loadDSPhieuXetnghiem(thongtinhsba.thongtinbn);
        enableModeButtonXN("add")
    })

    $("#tdt-chandoanhinhanh").click(function() {
        loadDSCDHA("", thongtinhsba.thongtinbn);
        loadDSPhieuCDHA(thongtinhsba.thongtinbn);
        enableModeButtonCDHA("add")
    })

    $("#tdt-ttpt").click(function() {
        loadDSTTPT("", thongtinhsba.thongtinbn);
        loadDSPhieuTTPT(thongtinhsba.thongtinbn);
        enableModeButtonTTPT("add")
    })

    $("#action_tdt_copyttpt").click(function() {
        initGridTTPTYlenh();
        $("#action_tdt_huyttpt").click();
        $("#modalTodieutriCopyTTPT").modal("show")
        $("#tdt_ttpt_loaicu").val($("#tdt_ttpt_loaittpt option:selected").text())
        $("#tdt_ttpt_hinhthuccu").val($("#tdt_ttpt_hinhthuc option:selected").text())
        var url = "noitru_ds_dotdieutri?stt_benhan=" + thongtinhsba.thongtinbn.STT_BENHAN +
            "&stt_dotdieutri=" + thongtinhsba.thongtinbn.STT_DOTDIEUTRI + "&dvtt="+singletonObject.dvtt;

        $.get(url).done(function(dt) {
            var selectTdt = $("#tdt_ttpt_todieutricu");
            selectTdt.html("");
            dt.forEach(function(val, index) {
                var selected = index == 0? "selected":   "";
                selectTdt.append("<option "+selected+" value='"+val.ID_DIEUTRI+"'>"+val.STT_DIEUTRI
                    + " - " + val.NGAYGIOLAP_TDT
                    + " - " + val.TEN_NHANVIEN
                    +"</option>");
            })
            if (selectTdt.hasClass("select2-hidden-accessible")) {
                selectTdt.select2("destroy");
            }
            selectTdt.select2();
            loadDSTTPTYlenh(selectTdt.val())

        }).fail(function() {
            notifiToClient("Red", "Lỗi lấy dữ liệu đợt điều trị");
        })
    })

    $("#tdt_ttpt_todieutricu").change(function() {
        loadDSTTPTYlenh($(this).val())
    })
    $("#tdt_ttpt_copy_ylenh").click(function() {
        insertDVTTPTSelected()
        clearToolbarJqgrid("tdt_list_ttpt");
        $("#modalTodieutriCopyTTPT").modal("hide")
    })
    $("#tdt_ttpt_copyvathemmoi_ylenh").click(function() {
        insertDVTTPTSelected()
        $("#modalTodieutriCopyTTPT").modal("hide")
        $("#action_tdt_themoittpt").click()
    })
    $("#action_tdt_goixn").click(function() {
        $("#modalTodieutriGoidichvu").modal("show")
        initGridChitietGoidichvu()
        $("#tdt_list_ctgoidv").jqGrid("clearGridData");
        $("#tdt_gdv_themmoi_loaidv").val("XN")
        $("#tdt_gdv_loaidv").val("XN")
        danhsachGoidichvu = {};
        goiBHYT = $("#tdt_xn_hinhthuc").val() == 0? 1: 0;
        getDSGoiDVTheoBS()
    })
    $("#action_tdt_goittpt").click(function() {
        $("#modalTodieutriGoidichvu").modal("show")
        initGridChitietGoidichvu()
        $("#tdt_list_ctgoidv").jqGrid("clearGridData");
        $("#tdt_gdv_themmoi_loaidv").val($("#tdt_ttpt_loaittpt").val())
        $("#tdt_gdv_loaidv").val($("#tdt_ttpt_loaittpt").val())
        danhsachGoidichvu = {};
        goiBHYT = $("#tdt_ttpt_hinhthuc").val() == 0? 1: 0;
        getDSGoiDVTheoBS()
    })
    $("#action_tdt_goicdha").click(function() {
        $("#modalTodieutriGoidichvu").modal("show")
        initGridChitietGoidichvu()
        $("#tdt_list_ctgoidv").jqGrid("clearGridData");
        var textSelected = $("#tdt_cdha_phongcdha option:selected").text();
        var loaiCDHA = textSelected.includes("CT") || textSelected.toUpperCase().includes("QUANG") ? 'CTMRI' : 'SANS';
        $("#tdt_gdv_themmoi_loaidv").val(loaiCDHA)
        $("#tdt_gdv_loaidv").val(loaiCDHA)
        console.log("action_tdt_goicdha", loaiCDHA)
        danhsachGoidichvu = {};
        goiBHYT = $("#tdt_cdha_hinhthuc").val() == 0? 1: 0;
        getDSGoiDVTheoBS()
    })
    $("#tdt_xn_themmoi_goidichvu").click(function() {
        $("#modalGoidichvuThemmoi").modal("show")
        initDulieu($("#tdt_gdv_loaidv").val())
    })
    $("#tdt_themmoi_goidichvu").click(function() {
        var idButton = this.id
        clearToolbarJqgrid("tdt_list_ctgoidv_dichvu")
        var allDichvu = getAllRowDataJqgrid("tdt_list_ctgoidv_dichvu");
        var selectedDV = [];
        allDichvu.forEach(function(item) {
            if(item.CHON == 1) {
                selectedDV.push(item.MA_DV);
            }
        })
        if(selectedDV.length == 0) {
            return notifiToClient("Red", "Vui lòng chọn dịch vụ")
        }
        if(!$("#formGoidichvuThemmoi").valid()) {
            return false;
        }

        showSelfLoading(idButton)
        try {
            $.post("cmu_post_CMU_GOIDV_BS_INS", {
                url: [singletonObject.dvtt, $("#tdt_gdv_themmoi_tengoi").val(),
                    $("#tdt_gdv_themmoi_hinhthuc").val(), selectedDV.join(","),
                    $("#tdt_gdv_themmoi_loaidv").val(),
                    singletonObject.userId].join("```")
            }).done(function() {
                notifiToClient("Green", "Thêm mới thành công")
                loadDSGoiDVTheoBS()
                loadDSDichvuGoiDV()

            }).always(function() {
                hideSelfLoading(idButton)
            }).fail(function() {
                notifiToClient("Red", "Thêm mới thất bại")
            })
        } catch(ex) {
            hideSelfLoading(idButton)
        }
    })

    $("#tdt_gdv_themmoi_hinhthuc").change(function() {
        loadDSDichvuGoiDV();
    })
    $("#tdt_huy_goidichvu").click(function() {
        showButtonByMode("add")
        initDulieu("XN")
        $("#tdt_gdv_themmoi_hinhthuc").prop("disabled", false)
    })
    $("#tdt_ma_goidichvu").change(function() {
        loadDataIntoGridDV($(this).val())
    })
    $('#modalGoidichvuThemmoi').on('hidden.bs.modal', function () {
        $("#tdt_list_ctgoidv").jqGrid("clearGridData");
        getDSGoiDVTheoBS();
    });
    $("#tdt_copy_goidichvu").click(function() {
        insertDVGoidichvuSelected()
        $("#modalTodieutriGoidichvu").modal("hide")
    })
    $("#tdt_copyvathemmoi_goidichvu").click(function() {
        insertDVGoidichvuSelected()
        var loaidv = $("#tdt_gdv_loaidv").val();
        if(loaidv == "XN") {
            $("#action_tdt_themoixn").click()
        }else if(loaidv == "TT" || loaidv == "PT") {
            $("#action_tdt_themoittpt").click()
        } else {
            $("#action_tdt_themoicdha").click()
        }
        $("#modalTodieutriGoidichvu").modal("hide")
    })
    $("#tdt_capnhat_goidichvu").click(function() {
        var idButton = this.id
        clearToolbarJqgrid("tdt_list_ctgoidv_dichvu")
        var allDichvu = getAllRowDataJqgrid("tdt_list_ctgoidv_dichvu");
        var selectedDV = [];
        allDichvu.forEach(function(item) {
            if(item.CHON == 1) {
                selectedDV.push(item.MA_DV);
            }
        })
        if(selectedDV.length == 0) {
            return notifiToClient("Red", "Vui lòng chọn dịch vụ")
        }
        if(!$("#formGoidichvuThemmoi").valid()) {
            return false;
        }

        showSelfLoading(idButton)
        try {
            var rowData = getThongtinRowSelected("tdt_list_goidv_th")
            $.post("cmu_post_CMU_GOIDV_BS_UPD", {
                url: [singletonObject.dvtt,
                    rowData.MA_GOI_DV,
                    $("#tdt_gdv_themmoi_tengoi").val(),
                    selectedDV.join(","),
                    $("#tdt_gdv_themmoi_loaidv").val(),
                    singletonObject.userId].join("```")
            }).done(function() {
                notifiToClient("Green", "Cập nhật thành công")
                loadDSGoiDVTheoBS()
                loadDSDichvuGoiDV()
                showButtonByMode("add")
            }).always(function() {
                hideSelfLoading(idButton)
            }).fail(function() {
                notifiToClient("Red", "Cập nhật thất bại")
            })
        } catch(ex) {
            hideSelfLoading(idButton)
        }
    })
    $("#tdt_xoa_goidichvu").click(function() {
        var idButton = this.id
        showSelfLoading(idButton);
        confirmToClient("Bạn có chắc chắn muốn xóa gói dịch vụ này không?", function() {
            var rowData = getThongtinRowSelected("tdt_list_goidv_th")
            $.post("cmu_post_CMU_GOIDV_BS_DEL", {
                url: [singletonObject.dvtt,
                    rowData.MA_GOI_DV].join("```")
            }).done(function() {
                notifiToClient("Green", "Xóa thành công")
                loadDSGoiDVTheoBS()
                loadDSDichvuGoiDV()
                showButtonByMode("add")
            }).always(function() {
                hideSelfLoading(idButton)
            }).fail(function() {
                notifiToClient("Red", "Xóa thất bại")
            })
        }, function () {
            hideSelfLoading(idButton);
        })

    })
    $("#action_tdt_laydlngoaitruxn").click(function() {
        initFormlaydulieungoaitru("XN")
    })
    $("#action_tdt_laydlngoaitrucdha").click(function() {
        initFormlaydulieungoaitru("CDHA")
    })
    $("#action_tdt_laydlngoaitruttpt").click(function() {
        initFormlaydulieungoaitru("TTPT")
    })
    $("#tdt-cls-laydulieungoaitru").click(function() {
        var loai = $(this).attr("data-id");
        var idButton = this.id
        if(!thongtinhsba.thongtinbn.MAKHAMBENHNGOAITRU_NHAPVIEN || thongtinhsba.thongtinbn.MAKHAMBENHNGOAITRU_NHAPVIEN.length < 5) {
            return  notifiToClient("Red", "Bệnh nhân không phải nhập viện từ ngoại trú");
        }
        if(moment(thongtinhsba.thongtinbn.NGAY_VAO_VIEN, ['DD/MM/YYYY']).format("DD/MM/YYYY")
            != moment(todieutriObject.NGAYGIO, ['DD/MM/YYYY HH:mm']).format("DD/MM/YYYY")) {
            return  notifiToClient("Red", "Ngày điều trị khác ngày nhập viện từ ngoại trú ");
        }
        showSelfLoading(idButton)
        confirmToClient("Bạn có chắc chắn muốn lấy dữ liệu ngoại trú không?", function() {
            if(loai == 'XN') {
                laydulieuXNNgoaitru(idButton)
            } else if(loai == 'CDHA') {
                laydulieuCDHANgoaitru(idButton)
            } else {
                laydulieuTTPTNgoaitru(idButton)
            }
        }, function() {
            hideSelfLoading(idButton)
        })

    })
    $("#tdt_kysotatca").click(function() {
        var idButton = this.id;
        showSelfLoading(idButton);
        var isSigned = true;
        $("#wrapLoadPreviewDSPHIEUTDT .item").each(function() {
            var object = JSON.parse($(this).attr("data-object"));

            if(!object.KEYSIGN) {
                isSigned = false;
                $(this).find(".spinner").show();
                if(object.SO_PHIEU_DV.includes("xn")) {
                    kysoTatcaXNTDT(object, idButton, $(this))
                } else if(object.SO_PHIEU_DV.includes("CD")) {
                    kysoTatcaCDHATDT(object, idButton, $(this))
                } else if(object.SO_PHIEU_DV.includes("dv")) {
                    kysoTatcaTTPTTDT(object, idButton, $(this))
                } else {
                    kysoTatcaTDT(object, idButton, $(this))
                }
            }
        })
        if(isSigned) {
            hideSelfLoading(idButton);
        }
    })
    $(document).on("click", "#wrapLoadPreviewDSPHIEUTDT .viewPhieuTDT", function() {
        var object = JSON.parse($(this).parent().attr("data-object"));
        getFilesign769(
            object.KY_HIEU_PHIEU,
            object.SO_PHIEU_DV,
            singletonObject.userId,
            singletonObject.dvtt,
            thongtinhsba.thongtinbn.SOVAOVIEN,
            object.SOVAOVIEN_DT,
            -1,
            function(data) {
                if(data.length > 0) {
                    getCMUFileSigned769(data[0].KEYMINIO,"pdf")
                } else {
                    notifiToClient("Red", "Không tìm thấy file ký số")
                }
            })
    })
    $("#modalKysotatcaTDT").on("hidden.bs.modal", function() {
        if(!$("#modalTodieutri").hasClass("show")) {
            $("#ttin_dieutri_btnlammoi").click()
        }
    })
    $("#action_kysotodieutritatca").click(function() {
        $.loadDSTatcaPhieuTDT(todieutriObject)
    })
    $("#formGoidichvuThemmoi").validate({})

    $("#thuthuat_tuongtrinh_mautuongtrinh").combogrid({
        url: 'select_maupttt_theodvtt',
        debug: true,
        width: "670px",
        colModel: [{'columnName': 'MA_MAUPTT', 'label': 'MA_MAUPTT', hidden: true},
            {'columnName': 'TEN_MAUPTTT', 'width': '100%', 'label': 'Tên Mẫu PTTT', 'align': 'left'},
            {'columnName': 'NOIDUNG','label': 'NOIDUNG', hidden: true}
        ],
        select: function (event, ui) {
            $("#thuthuat_tuongtrinh_mautuongtrinh").val(ui.item.TEN_MAUPTTT);
            CKEDITOR.instances['thuthuat_tuongtrinh_noidung'].setData(ui.item.NOIDUNG)
            return false;
        }
    });
    $("#thuthuat_vitrittpt").combogrid({
        url: 'sel-danh-muc-vi-tri-thu-thuat-phau-thuat-search?tamNgung=0',
        debug: true,
        width: "600px",
        colModel: [
            {'columnName': 'ID', 'label': 'ID', hidden: true},
            {'columnName': 'MA', 'label': 'Mã', 'width': '20'},
            {'columnName': 'TEN', 'label': 'Tên', 'width': '20'},
            {'columnName': 'GHI_CHU', 'label': 'Ghi Chú', 'width': '20'}
        ],
        select: function (event, ui) {
            $("#thuthuat_vitrittpt").val(ui.item.TEN);
            $("#thuthuat_vitrittpt_ma").val(ui.item.MA);
            $("#thuthuat_vitrittpt_ten").val(ui.item.TEN);
            return false;
        }
    });
    $("#thuthuat_tuongtrinh_icd").keypress(function(e) {
        var icd = $(this).val();
        if(e.keyCode == 13 && icd.trim() != "") {
            getMotabenhly(icd.toUpperCase(), function(data) {
                var splitIcd = data.split("!!!");
                var icdCur = $("#thuthuat_tuongtrinh_tenicd").val().trim();
                if(!icdCur.includes("("+icd.toUpperCase()+")")) {
                    var mabenh = "("+ icd.toUpperCase() + ") " + splitIcd[1];
                    $("#thuthuat_tuongtrinh_tenicd").val(icdCur == ''? mabenh: icdCur + "; " + mabenh);
                }
                $("#thuthuat_tuongtrinh_icd").val("")
            })
        }
    })
    $("#thuthuat_tuongtrinh_icd_truoc").keypress(function(e) {
        var icd = $(this).val();
        if(e.keyCode == 13 && icd.trim() != "") {
            getMotabenhly(icd.toUpperCase(), function(data) {
                var splitIcd = data.split("!!!");
                var icdCur = $("#thuthuat_tuongtrinh_tenicd_truoc").val().trim();
                if(!icdCur.includes("("+icd.toUpperCase()+")")) {
                    var mabenh = "("+ icd.toUpperCase() + ") " + splitIcd[1];
                    $("#thuthuat_tuongtrinh_tenicd_truoc").val(icdCur == ''? mabenh: icdCur + "; " + mabenh);
                }
                $("#thuthuat_tuongtrinh_icd_truoc").val("")
            })
        }
    })

    $("#modalPhauThuatTuongtrinhTT").on('show.bs.modal', function() {

    });

    $("#modalPhauThuatTuongtrinhTT").on('hide.bs.modal', function() {
        $("#tuongTrinhThuThuatForm")[0].reset();
        lanPhauThuatObject['CHITIETDVKT'] = {};
    })

    $("#thuthuat_tuongtrinhpt_copynoidung").click(function() {
        var trinhtupttt = CKEDITOR.instances['thuthuat_tuongtrinh_noidung'].getData().trim();
        var trinhtupttt_xml5 = decodeHTMLEntities($('<textarea />').html(trinhtupttt).text());
        $("#thuthuat_tuongtrinh_cachthupt").val(trinhtupttt_xml5.replaceAll(/\n\n/g, '\n'))
    });

    $("#thuthuat_tuongtrinh_themhinhanh").click(function () {
        $("#modalPhauThuatTuongtrinhPTHinhanh").modal("show");
        addTextTitleModal("titleModalPhauThuatTuongtrinhPTHinhanh", "Thêm hình ảnh")
        $("#phauthuat_tuongtrinh_sttfile").val("")
    });

    $("#lanphauthuat_tuongtrinhpt_luuanh").click(function() {
        var jqGrid = "phauthuat_tuongtrinh_dsanh";
        if (lanPhauThuatObject.TYPE && lanPhauThuatObject.TYPE == "TT") {
            jqGrid = "thuthuat_tuongtrinh_dsanh";
        }
        var allData = getAllRowDataJqgrid(jqGrid)
        var sttAnh =  $("#phauthuat_tuongtrinh_sttfile").val();
        if(allData.length < 2 || sttAnh) {
            var list = $("#"+jqGrid);
            var ret = lanPhauThuatObject.CHITIETDVKT
            var maxlength = 0;
            allData.forEach(function(item) {
                if (item.STT == sttAnh) {
                    item.HINHANH = $("#"+idCanvasTuongTrinhTTPT).get(0).toDataURL()
                    item.DUONGDAN_HT = $("#"+idCanvasTuongTrinhTTPT).get(0).toDataURL()
                }
                if(item.STT > maxlength) {
                    maxlength = item.STT
                }

            });
            if(!sttAnh) {
                allData.push({
                    STT: maxlength+1,
                    SO_PHIEU_DICHVU: ret.SO_PHIEU_DICHVU,
                    MA_DV: ret.MA_DV,
                    DUONGDAN_HT: $("#"+idCanvasTuongTrinhTTPT).get(0).toDataURL(),
                    HINHANH: $("#"+idCanvasTuongTrinhTTPT).get(0).toDataURL()
                })
            }

            list.jqGrid('setGridParam', { data: allData});
            list[0].grid.endReq();
            list.trigger('reloadGrid');
            $("#modalPhauThuatTuongtrinhPTHinhanh").modal("hide");
        } else {
            notifiToClient("Red", "Chỉ được thêm 2 hình ảnh")
        }
    })

    $("#modalPhauThuatTuongtrinhPTHinhanh").on('hidden.bs.modal', function() {
        $("#"+idCanvasTuongTrinhTTPT).get(0).getContext('2d').clearRect(0, 0, $("#"+idCanvasTuongTrinhTTPT).get(0).width, $("#"+idCanvasTuongTrinhTTPT).get(0).height);
    });

    $("#thuthuat_tuongtrinhpt_luu").click(async function () {
        var idButton = this.id;
        var rowSelected = lanPhauThuatObject.CHITIETDVKT;
        if (!moment($("#thuthuat_tuongtrinh_ngayylenh").val(), 'DD/MM/YYYY HH:mm', true).isValid() ||
            !moment($("#thuthuat_tuongtrinh_ngayketqua").val(), 'DD/MM/YYYY HH:mm', true).isValid()) {
            return notifiToClient("Red", "Ngày giờ không hợp lệ");
        } else {
            if ($("#thuthuat_tuongtrinh_bhytkchi").val() == 0) {
                var momentChiDinh = moment($("#thuthuat_tuongtrinh_ngaychidinh").val(), 'DD/MM/YYYY HH:mm');
                var momentThucHienYLenh = moment($("#thuthuat_tuongtrinh_ngayylenh").val(), 'DD/MM/YYYY HH:mm');
                var momentKetQua = moment($("#thuthuat_tuongtrinh_ngayketqua").val(), 'DD/MM/YYYY HH:mm');
                var thoiGianChiDinh = $("#thuthuat_tuongtrinh_ngaychidinh").val();
                var thoiGianTHYL = $("#thuthuat_tuongtrinh_ngayylenh").val();
                var thoiGianKQ = $("#thuthuat_tuongtrinh_ngayketqua").val();
                if (momentThucHienYLenh.diff(momentChiDinh, 'minutes') < 1) {
                    notifiToClient("Red", 'THỜI GIAN THỰC HIỆN Y LỆNH : ' + thoiGianTHYL + '<br> KHÔNG ĐƯỢC NHỎ HƠN HOẶC BẰNG' + '<br>THỜI GIAN CHỈ ĐỊNH : ' + thoiGianChiDinh);
                    return;
                }
                if (momentKetQua.diff(momentThucHienYLenh, 'minutes') < 1) {
                    notifiToClient('Red', 'THỜI GIAN KẾT QUẢ : ' + thoiGianKQ + '<br> KHÔNG ĐƯỢC NHỎ HƠN HOẶC BẰNG' + '<br>THỜI GIAN THỰC HIỆN Y LỆNH : ' + thoiGianTHYL);
                    return;
                }
                if (momentKetQua.diff(momentThucHienYLenh, 'minutes') < 5) {
                    notifiToClient("Red", 'THỜI GIAN THỰC HIỆN Y LỆNH: ' + thoiGianTHYL + '<br> ĐẾN GIỜ ' + '<br>THỜI GIAN KẾT QUẢ : ' + thoiGianKQ + " KHÔNG ĐƯỢC NHỎ HƠN 5 PHÚT");
                    return;
                }
            }
        }
        if (!$("#thuthuat_tuongtrinh_bsphauthuat").val()) {
            return notifiToClient("Red", "Vui lòng chọn bác sĩ phẫu thuật");
        }
        if ($("#thuthuat_tuongtrinh_nguoithuchien").val() && $("#thuthuat_tuongtrinh_nguoithuchien").val() != singletonObject.userId) {
            return notifiToClient("Red", MESSAGEAJAX.PERMISSION + ": " + getThongTinNhanVienByMaNhanVien($("#thuthuat_tuongtrinh_nguoithuchien").val()).TEN_NHANVIEN);
        }

        if (!$("#tuongTrinhThuThuatForm").valid()) return;

        showSelfLoading(idButton);

        var textViTri = $("#thuthuat_vitrittpt").val();
        var maViTri = $("#thuthuat_vitrittpt_ma").val();
        var tenViTri = $("#thuthuat_vitrittpt_ten").val();
        var enableAlert = maViTri == undefined || textViTri != tenViTri;

        try {
            if (enableAlert) {
                let isConfirmViTri = await confirmToClientPromise("Chỉ lưu tên vị trí và lưu không theo danh mục?");
                if (!isConfirmViTri) {
                    hideSelfLoading(idButton);
                    return;
                }
            }

            if ($("#thuthuat_tuongtrinh_tinhtienekip").val() == 0) {
                let isConfirmEkip = await confirmToClientPromise("Bạn có chắc không tính tiền ekip?");
                if (!isConfirmEkip) {
                    hideSelfLoading(idButton);
                    return;
                }
            }

            await luuViTriTuongTrinh({
                MA_DV: rowSelected.MA_DV,
                SO_PHIEU_DICHVU: rowSelected.SO_PHIEU_DICHVU,
                TEXT_VITRI: textViTri,
                THOIGIAN_TTPT_VITRI: $("#thuthuat_thoigianttpt").val(),
                MA_VITRI: enableAlert ? null : maViTri
            }, function (dataReturn) {

            }, function (error) {
                notifiToClient("Red", "Lỗi hệ thống: " + error);
            });

            var objectEkip = {};
            $('#tuongTrinhThuThuatForm select').each(function (i, kv) {
                if (['BSPHAUTHUAT', 'BSGAYME', 'KTVGAYMETE', 'PHUMO1', 'PHUMO2', 'DUNGCU1', 'DUNGCU2'].includes(kv.name)) {
                    let selectedValue = $(kv).val();
                    let matchedItem = singletonObject.danhsachnhanvien.find(item => item.MA_NHANVIEN == selectedValue);
                    if (matchedItem) objectEkip[kv.name + '_TEN'] = matchedItem.TEN_NHANVIEN;
                }
            });

            var ekipBSPT = [
                _.get(objectEkip, 'BSPHAUTHUAT_TEN', ''),
                _.get(objectEkip, 'PHUMO1_TEN', '') + (_.get(objectEkip, 'PHUMO2_TEN', '') ? "," + _.get(objectEkip, 'PHUMO2_TEN', '') : ""),
                _.get(objectEkip, 'PHUMO3_TEN', '') + (_.get(objectEkip, 'PHUMO4_TEN', '') ? "," + _.get(objectEkip, 'PHUMO4_TEN', '') : "")
            ].join(":");

            var ekipBSGM = [
                _.get(objectEkip, 'BSGAYME_TEN', ''),
                _.get(objectEkip, 'KTVGAYMETE_TEN', ''),
                _.get(objectEkip, 'DUNGCU1_TEN', ''),
                _.get(objectEkip, 'DUNGCU2_TEN', '')
            ].join(":");
            var trinhTuPTTT = CKEDITOR.instances['thuthuat_tuongtrinh_noidung'].getData().trim();
            var trinhTuPTTT_XML5 = decodeHTMLEntities($('<textarea />').html(trinhTuPTTT).text());

            luuTuongTrinhTTPT({
                ...rowSelected,
                ...convertDataFormToJson("tuongTrinhThuThuatForm"),
                CATCHISAU7NGAY: '',
                STRING_EKIPBSGM: ekipBSGM,
                STRING_EKIPBSPT: ekipBSPT,
                TRINHTUPTTT: trinhTuPTTT,
                TRINHTUPTTT_XML5: trinhTuPTTT_XML5,
                TEN_DV_JOIN: $("#thuthuat_tuongtrinh_pppt").val().join(";")
            }, function () {
                var allHinhanh = getAllRowDataJqgrid("thuthuat_tuongtrinh_dsanh");
                uploadHinhAnh(allHinhanh, rowSelected, function() {
                    console.log("Upload hình ảnh xong.");
                    hideSelfLoading(idButton);
                    notifiToClient("Green", "Lưu thành công");
                    $("#thuthuat_tuongtrinhpt_xoa, #thuthuat_tuongtrinhpt_xem, #thuthuat_tuongtrinhpt_kyso").show();
                    $("#thuthuat_tuongtrinhpt_huykyso").hide();
                });

                luuThongTinEkip(rowSelected);
            }, function (error) {
                notifiToClient("Red", "Lỗi hệ thống: " + error);
            });
        } catch (error) {
            hideSelfLoading(idButton);
            notifiToClient("Red", "Lỗi hệ thống: " + error);
        }
    });

    $("#thuthuat_tuongtrinhpt_xem").click(function() {
        var ret = lanPhauThuatObject.CHITIETDVKT
        getFilesign769(
            "PHIEU_NOITRU_TRUONGTRINHPT",
            ret.MA_DV,
            -1,//singletonObject.userId,
            singletonObject.dvtt,
            thongtinhsba.thongtinbn.SOVAOVIEN,
            thongtinhsba.thongtinbn.SOVAOVIEN_DT,
            -1,
            function(data) {
                if(data.length > 0) {
                    getCMUFileSigned769(data[0].KEYMINIO,"pdf")
                } else {
                    var arr = [
                        ret.MA_DV,
                        ret.SO_PHIEU_DICHVU,
                        '',
                        singletonObject.dvtt,
                        1,
                        thongtinhsba.thongtinbn.STT_BENHAN,
                        lanPhauThuatObject.STT_DOTDIEUTRI,
                        ret.STT_DIEUTRI,
                        ret.SOVAOVIEN,
                        thongtinhsba.thongtinbn.TEN_BENH_NHAN,
                        thongtinhsba.thongtinbn.TUOI,
                        thongtinhsba.thongtinbn.GIOI_TINH_HT,
                        "0"];
                    var url = "inphieuthuthuatphauthuat_hinhmau?url=" + convertArray(arr)+"&&typefile=pdf";
                    previewPdfDefaultModal(url, 'frame-intuongtrinh')
                }
            }
        )
    });

    $("#thuthuat_tuongtrinhpt_xoa").click(function() {
        var idButton = this.id;
        var ret = lanPhauThuatObject.CHITIETDVKT
        if($("#thuthuat_tuongtrinh_nguoithuchien").val() != singletonObject.userId) {
            return notifiToClient("Red", MESSAGEAJAX.PERMISSION + ": " + getThongTinNhanVienByMaNhanVien($("#thuthuat_tuongtrinh_nguoithuchien").val()).TEN_NHANVIEN);
        }
        confirmToClient(MESSAGEAJAX.CONFIRM, function() {
            showSelfLoading(idButton)
            $.post("cmu_post_CMU_HUYKETQUA_TTPT_F_V2",{
                url: [
                    singletonObject.dvtt,
                    ret.SO_PHIEU_DICHVU,
                    ret.MA_DV,
                    thongtinhsba.thongtinbn.STT_BENHAN,
                    ret.STT_DOTDIEUTRI,
                    ret.STT_DIEUTRI,
                    thongtinhsba.thongtinbn.SOVAOVIEN
                ].join("```")
            }).done(function() {
                notifiToClient("Green", "Xóa thành công!")
                $("#thuthuat_tuongtrinhpt_xoa").hide();
                $("#thuthuat_tuongtrinhpt_xem").hide();
                $("#thuthuat_tuongtrinhpt_kyso").hide();
                $("#thuthuat_tuongtrinhpt_huykyso").hide();
                $.ajax({
                    url:"cmu_post_CMU_IMAGES_DVKT_DELALL",
                    method: "POST",
                    data: {
                        url: [singletonObject.dvtt, thongtinhsba.thongtinbn.SOVAOVIEN, ret.SO_PHIEU_DICHVU, ret.MA_DV].join("```")
                    },
                    async: false
                })
                loadDSHinhanh();

            }).fail(function() {
                notifiToClient("Red", MESSAGEAJAX.ERROR)
            }).always(function () {
                hideSelfLoading(idButton)
            });
        })
    });

    $("#thuthuat_tuongtrinhpt_copynoidung").click(function() {
        var trinhtupttt = CKEDITOR.instances['thuthuat_tuongtrinh_noidung'].getData().trim();
        var trinhtupttt_xml5 = decodeHTMLEntities($('<textarea />').html(trinhtupttt).text());
        $("#thuthuat_tuongtrinh_cachthupt").val(trinhtupttt_xml5.replaceAll(/\n\n/g, '\n'))
    });

    $("#thuthuat_tuongtrinhpt_kyso").click(function() {
        var ret = lanPhauThuatObject.CHITIETDVKT;
        getThongtinTTPTGlobal(ret, function(result) {
            if (result && result.length > 0) {
                if (result[0].MA_BS_PTTP == singletonObject.userId) {
                    var arr = [
                        ret.MA_DV,
                        ret.SO_PHIEU_DICHVU,
                        '',
                        singletonObject.dvtt,
                        1,
                        thongtinhsba.thongtinbn.STT_BENHAN,
                        ret.STT_DOTDIEUTRI,
                        ret.STT_DIEUTRI,
                        ret.SOVAOVIEN,
                        thongtinhsba.thongtinbn.TEN_BENH_NHAN,
                        thongtinhsba.thongtinbn.TUOI,
                        thongtinhsba.thongtinbn.GIOI_TINH_HT,
                        "0"
                    ];
                    var url = "inphieuthuthuatphauthuat_hinhmau?url=" + convertArray(arr)+"&&typefile=pdf";
                    kySoChung({
                        dvtt: singletonObject.dvtt,
                        userId: singletonObject.userId,
                        url: url,
                        loaiGiay: "PHIEU_NOITRU_TRUONGTRINHPT",
                        maBenhNhan: thongtinhsba.thongtinbn.MA_BENH_NHAN,
                        soBenhAn: thongtinhsba.thongtinbn.STT_BENHAN,
                        soPhieuDichVu: ret.MA_DV,
                        maDichVu:lanPhauThuatObject.ID_DIEUTRI,
                        fileName:  'Tường trình thủ thuật: '+ thongtinhsba.thongtinbn.TEN_BENH_NHAN + " - "+ ret.MA_DV,
                        soVaoVien: thongtinhsba.thongtinbn.SOVAOVIEN,
                        soVaoVienDT: thongtinhsba.thongtinbn.SOVAOVIEN_DT,
                        keyword: "PHẪU THUẬT/THỦ THUẬT VIÊN",
                    }, function(dataKySo) {
                        checkKySoTuongTrinhTT(ret);
                    });
                } else {
                    notifiToClient("Red", MESSAGEAJAX.PERMISSION + ": " + getThongTinNhanVienByMaNhanVien(result[0].MA_BS_PTTP).TEN_NHANVIEN);
                }
            }
        });
    });

    $("#thuthuat_tuongtrinhpt_huykyso").click(function() {
        var idButton = this.id
        var rowSelected = lanPhauThuatObject.CHITIETDVKT
        showSelfLoading(idButton);
        getThongtinTTPTGlobal(rowSelected, function(result) {
            if (result && result.length > 0) {
                if (result[0].MA_BS_PTTP == singletonObject.userId) {
                    confirmToClient("Bạn có chắc sẽ hủy ký số phiếu này?", function() {
                        huykysoFilesign769(
                            "PHIEU_NOITRU_TRUONGTRINHPT",
                            rowSelected.MA_DV,
                            singletonObject.userId,
                            singletonObject.dvtt,
                            thongtinhsba.thongtinbn.SOVAOVIEN,
                            thongtinhsba.thongtinbn.SOVAOVIEN_DT,
                            -1, function(data) {
                                if(data && data.ERROR) {
                                    notifiToClient("Red", "Hủy ký số thất bại");
                                } else {
                                    checkKySoTuongTrinhTT(rowSelected)
                                }
                                hideSelfLoading(idButton);
                            })
                    }, function () {
                        hideSelfLoading(idButton);
                    })
                } else {
                    notifiToClient("Red", MESSAGEAJAX.PERMISSION + ": " + getThongTinNhanVienByMaNhanVien(result[0].MA_BS_PTTP).TEN_NHANVIEN);
                }
            }
        });
    })

    $.extend({
        loadKetquakysoPacs: function() {
            $.get("cmu_list_HSBA_DSCDHA_TONGHOP?url="+convertArray([
                singletonObject.dvtt, thongtinhsba.thongtinbn.STT_BENHAN,
                thongtinhsba.thongtinbn.MA_BENH_NHAN
            ])).then(function(data) {
                data.forEach(function(item) {
                    if(item.MOTA_LOAI_CDHA != 'TDCN') {
                        var x = new XMLHttpRequest();
                        x.onload = function() {
                            // Check if the response is a PDF
                            console.log("x.response.size", x.response.size)
                            if (x.response.size == 15) {
                                return;
                            }
                            console.log("x.response.size11", x.response.size)
                            var reader = new FileReader();
                            reader.readAsDataURL(x.response);
                            reader.onloadend = function() {
                                var uuid = uuidv4();
                                $.post("cmu_post", {
                                    url: [
                                        singletonObject.dvtt,
                                        uuid,
                                        thongtinhsba.thongtinbn.BANT,
                                        thongtinhsba.thongtinbn.MABENHNHAN,
                                        thongtinhsba.thongtinbn.SOVAOVIEN,
                                        thongtinhsba.thongtinbn.SOVAOVIEN_DT,
                                        thongtinhsba.thongtinbn.SOBENHAN,
                                        thongtinhsba.thongtinbn.STT_DOTDIEUTRI,
                                        "PHIEUKQ_CDHA_RISPACS",
                                        item.ID_DIEUTRI,
                                        item.SO_PHIEU_CDHA,
                                        "",
                                        "",
                                        "",
                                        singletonObject.makhoa,
                                        item.KHOA_CHI_DINH,
                                        "",
                                        "",
                                        0,
                                        item.NGUOI_THUC_HIEN,
                                        item.NGUOI_THUC_HIEN,
                                        1,
                                        0,
                                        -1,
                                        item.MA_CDHA,
                                        "",
                                        0,
                                        'SMARTCA_SAVE_SIGNED_GRV'
                                    ].join("```")
                                })
                            }
                        }
                        x.responseType = 'blob';    // <-- This is necessary!
                        x.open('GET', "download-result-ris-pacs?maPhieuChiDinh="+item.SO_PHIEU_CDHA+"&maDichVu="+item.MA_CDHA, true);
                        x.send();
                    }
                })

            })
        },
        loadDSTatcaPhieuTDT: function(rowData) {
            showLoaderIntoWrapId("hsba_list_todieutri")
            $.get("cmu_list_CMU_DSPHIEUTDT?url="+convertArray([
                singletonObject.dvtt,
                thongtinhsba.thongtinbn.STT_BENHAN,
                thongtinhsba.thongtinbn.STT_DOTDIEUTRI,
                rowData.STT_DIEUTRI
            ])).then(function(data) {
                $("#wrapLoadPreviewDSPHIEUTDT .body").html("")
                var body = "";
                data.forEach(function (object, pos) {
                    var prefix = 'Tờ điều trị';
                    if(object.SO_PHIEU_DV.includes("xn")) {
                        prefix = "XN";
                    }
                    if(object.SO_PHIEU_DV.includes("CD")) {
                        prefix = "CDHA";
                    }
                    if(object.SO_PHIEU_DV.includes("dv")) {
                        prefix = "TTPT";
                    }
                    var signed = "";
                    if(object.KEYSIGN) {
                        signed = '<i class="fa fa-check"></i>'
                    }
                    body += '<a data-object=\''+JSON.stringify(object)+'\' id="wrapItemSign_'+pos+'" class="nav-link item" >' + prefix+ ":"+ object.SO_PHIEU_DV +
                        '<button id="btnSignviewTDT_'+pos+'" onclick="return false" class="btn btn-success  ml-2 viewPhieuTDT" type="button">\n' +
                        '<span class="spinner-border spinner-border-sm spinner" role="status" aria-hidden="true"></span>' +
                        signed+'</button>' +
                        '</a>'
                })
                $("#wrapLoadPreviewDSPHIEUTDT .body").html(body)
                $("#wrapLoadPreviewDSPHIEUTDT .spinner").hide();
                $("#modalKysotatcaTDT").modal("show")
                addTextTitleModal("titleModalKysotatcaTDT", "Ký số tất cả phiếu tờ điều trị")
            }).fail(function() {
                notifiToClient("Red", MESSAGEAJAX.ERROR)
            }).always(function() {
                hideLoaderIntoWrapId("hsba_list_todieutri")
            })
        }
    })

    function showButtonByMode(mode) {
        if(mode == 'add') {
            $("#modalGoidichvuThemmoi .edit").hide()
            $("#modalGoidichvuThemmoi .add").show()
            $("#tdt_gdv_themmoi_tengoi").val("")
        } else {
            $("#modalGoidichvuThemmoi .edit").show()
            $("#modalGoidichvuThemmoi .add").hide()
        }
    }
    function insertDVTTPTSelected() {
        var listTTPT = $("#tdt_list_ttpt")
        var listTTPTYlenh = $("#tdt_list_ttpt_ylenhcu")
        var selRowIds = listTTPTYlenh.jqGrid("getGridParam", "selarrrow")
        var selectedRows = [];
        for (var i=0 ; i < selRowIds.length; i++) {
            var rowData = listTTPTYlenh.jqGrid("getRowData", selRowIds[i]);
            selectedRows.push(Number(rowData.MA_DV))
        }
        var allData = listTTPT.jqGrid('getGridParam','data');
        allData.map(function(item){
            console.log("item.MA_DV", item.MA_DV)
            if(selectedRows.indexOf(item.MA_DV) > -1){
                item.CHON = 1;
            }
            return item;
        })
        listTTPT.jqGrid('setGridParam', { data: allData});

    }
    function initGridTTPTYlenh() {
        var listPhieuTTPT = $("#tdt_list_ttpt_ylenhcu");
        if(!listPhieuTTPT[0].grid) {
            listPhieuTTPT.jqGrid({
                url: '',
                datatype: "local",
                loadonce: true,
                height: 300,
                width: null,
                shrinkToFit: false,
                colModel: [
                    {label: "SO_PHIEU_DICHVU", name: 'SO_PHIEU_DICHVU', index: 'SO_PHIEU_DICHVU', width: 150},
                    {label: "MA_PHONG_DICHVU",name: 'MA_PHONG_DICHVU', index: 'MA_PHONG_DICHVU', width: 60, hidden: true},
                    {label: "STT_DIEUTRI",name: 'STT_DIEUTRI', index: 'STT_DIEUTRI', width: 60, hidden: true},
                    {label: "Khoa",name: 'KHOA_CHI_DINH', index: 'KHOA_CHI_DINH', width: 120},
                    {label: "Tên loại", name: 'TEN_LOAI_DV', index: 'TEN_LOAI_DV', width: 10, hidden: true},
                    {label: "MA_LOAI", name: 'MA_LOAI_DICHVU', index: 'MA_LOAI_DICHVU', width: 10, hidden: true},
                    {label: "Mã DV", name: 'MA_DV', index: 'MA_DV', width: 100},
                    {label: "Tên DV", name: 'TEN_DV', index: 'TEN_DV', width: 215, cellattr: function (rowId, tv, rawObject, cm, rdata) {
                            return 'style="white-space: normal;"';
                        }},
                    {label: "Cấp cứu",name: 'CAPCUU', index: 'CAPCUU', width: 60},
                    {label: "BHYT", name: 'CO_BHYT', index: 'CO_BHYT', width: 60},
                    {label: "Số lượng", name: 'SO_LUONG', index: 'SO_LUONG', width: 50, editable: true, edittype: 'text', align: "center"},
                    {label: "Đơn giá", name: 'GIA_CDHA', index: 'GIA_CDHA', width: 50, align: "right"},
                    {label: "Thành tiền", name: 'THANH_TIEN', index: 'THANH_TIEN', width: 50, align: "right"},
                    {label: "Bác sĩ điều trị",name: 'TEN_NHANVIEN', index: 'TEN_NHANVIEN', width: 150},
                    {label: "Ngày chỉ định",name: 'NGAY_CHI_DINH', index: 'NGAY_CHI_DINH', width: 100},
                    {label: "Giờ chỉ định",name: 'GIO_CHI_DINH', index: 'GIO_CHI_DINH', width: 60},
                    {label: "SOVAOVIEN",name: 'SOVAOVIEN', index: 'SOVAOVIEN', width: 60, hidden: true},
                    {label: "SOVAOVIEN_DT",name: 'SOVAOVIEN_DT', index: 'SOVAOVIEN_DT', width: 60, hidden: true},
                    {label: "CHON", name: 'CHON', index: 'CHON', width: 60, hidden: true},

                ],
                rowNum: 1000000,
                caption: "Danh sách phiếu chỉ định",
                grouping: true,
                multiselect: true,
                footerrow: true,
                loadComplete: function () {
                    var $self = $(this);
                    var sum_tt = $self.jqGrid("getCol", "THANH_TIEN", false, "sum");
                    $self.jqGrid("footerData", "set", {THANH_TIEN: sum_tt});
                    $self.jqGrid("footerData", "set", {TEN_CDHA: "Tổng tiền:"});
                },
                onSelectRow: function(id) {
                }
            });
        }
    }

    function initGridDichvuGoidichvu() {
        var list = $("#tdt_list_ctgoidv_dichvu");
        if(!list[0].grid) {
            list.jqGrid({
                url: '',
                datatype: "local",
                loadonce: true,
                height: 400,
                width: null,
                shrinkToFit: false,
                colModel: [
                    {label: "Mã DV", name: 'MA_DV', index: 'MA_DV', width: 100},
                    {label: "Tên DV", name: 'TEN_DV', index: 'TEN_DV', width: 315},
                    {label: "Chọn", name: 'CHON', index: 'CHON', width: 100},

                ],
                rowNum: 1000000,
                caption: "Danh sách dịch vụ",
                multiselect: true,
                gridComplete: function () {
                    var str = list.jqGrid('getDataIDs');
                    if (str != "") {
                        for (var i = 0; i < str.length; i++) {
                            var ret1 = list.jqGrid('getRowData', str[i]);
                            if (ret1.CHON.toString() == "1")
                                list.jqGrid('setSelection', str[i]);
                        }
                    }
                },
                onSelectRow: function(id, selected) {
                    if(id){
                        var rowData = list.jqGrid("getRowData", id);
                        var allData = list.jqGrid('getGridParam','data');
                        allData.map(function(item){
                            if(item.MA_DV == rowData.MA_DV){
                                item.CHON = selected? 1: 0;
                            }
                            return item;
                        })
                        list.jqGrid('setGridParam', { data: allData});
                    }
                }
            });
            list.jqGrid('filterToolbar', {
                stringResult: true,
                searchOnEnter: false,
                defaultSearch: "cn",
            });
        }
    }
    function initGridTonghopGoidichvu() {
        var list = $("#tdt_list_goidv_th");
        if(!list[0].grid) {
            list.jqGrid({
                url: '',
                datatype: "local",
                loadonce: true,
                height: 400,
                width: null,
                shrinkToFit: false,
                colModel: [
                    {label: "Mã gói", name: 'MA_GOI_DV', index: 'MA_GOI_DV', width: 50},
                    {label: "Tên gói", name: 'TEN_GOI_DV', index: 'TEN_GOI_DV', width: 100},
                    {label: "Mã DV", name: 'MA_DV', index: 'MA_DV', width: 80},
                    {label: "Tên DV", name: 'TEN_DV', index: 'TEN_DV', width: 265},
                    {label: "BHYT", name: 'BHYT', index: 'BHYT', width: 80},
                ],
                rowNum: 1000000,
                caption: "Danh sách gói dịch vụ",
                grouping: true,
                groupingView: {
                    groupField: ["TEN_GOI_DV"],
                    groupColumnShow: [false],
                    groupText: ['<b>{0}</b>'],
                    groupCollapse: false
                },
                onSelectRow: function(id) {
                    var rowData = getThongtinRowSelected("tdt_list_goidv_th");
                    $("#tdt_gdv_themmoi_tengoi").val(rowData.TEN_GOI_DV)
                    $("#tdt_gdv_themmoi_hinhthuc").val(rowData.BHYT).prop("disabled", true)
                    $.get(getUrlLoadDSDichvu()).done(function (allDichvu) {
                        var allDichvuTonghop = getAllRowDataJqgrid("tdt_list_goidv_th")
                        var selectedDichvu = []
                        allDichvuTonghop.map(function (item) {
                            if(item.MA_GOI_DV == rowData.MA_GOI_DV) {
                                selectedDichvu.push(Number(item.MA_DV))
                            }
                        })
                        allDichvu = allDichvu.map(function (item) {
                            if(selectedDichvu.indexOf(Number(item.MA_DV)) != -1) {
                                item.CHON = 1;
                            }
                            return item;
                        })
                        showButtonByMode("edit")
                        $("#tdt_list_ctgoidv_dichvu").jqGrid('setGridParam', { data: allDichvu});
                        $("#tdt_list_ctgoidv_dichvu").trigger("reloadGrid");
                    }).fail(function() {
                        notifiToClient("Red", "Lỗi lấy danh sách dịch vụ theo nhóm")
                    })
                }
            });
            list.jqGrid('filterToolbar', {
                stringResult: true,
                searchOnEnter: false,
                defaultSearch: "cn",
            });
        }
    }

    function initGridChitietGoidichvu() {
        var list = $("#tdt_list_ctgoidv");
        if(!list[0].grid) {
            list.jqGrid({
                url: '',
                datatype: "local",
                loadonce: true,
                height: 400,
                width: null,
                shrinkToFit: false,
                colModel: [
                    {label: "Mã gói", name: 'MA_GOI_DV', index: 'MA_GOI_DV', width: 50},
                    {label: "Tên gói", name: 'TEN_GOI_DV', index: 'TEN_GOI_DV', width: 100},
                    {label: "Mã DV", name: 'MA_DV', index: 'MA_DV', width: 80},
                    {label: "Tên DV", name: 'TEN_DV', index: 'TEN_DV', width: 265},
                    {label: "BHYT", name: 'BHYT', index: 'BHYT', width: 80},
                ],
                rowNum: 1000000,
                multiselect: true,
                caption: "Danh sách chi tiết gói dịch vụ",
                onSelectRow: function(id) {

                }
            });
            list.jqGrid('filterToolbar', {
                stringResult: true,
                searchOnEnter: false,
                defaultSearch: "cn",
            });
        }
    }

    function loadDSTTPTYlenh(idDieutri) {
        var arr = [ singletonObject.dvtt, idDieutri,
            todieutriObject.SOVAOVIEN, $("#tdt_ttpt_loaittpt").val(), $("#tdt_ttpt_hinhthuc").val()];
        var url = "cmu_list_CMU_HSBA_DSTTPT_YLENH_F?url=" + convertArray(arr);
        var list = $("#tdt_list_ttpt_ylenhcu");
        loadDataGridGroupBy(list, url);
    }
    function loadDSGoiDVTheoBS() {
        var arr = [ singletonObject.dvtt, singletonObject.userId, $("#tdt_gdv_themmoi_loaidv").val()];
        var url = "cmu_list_CMU_DANHSACHGOIDV_BS?url=" + convertArray(arr);
        var list = $("#tdt_list_goidv_th");
        loadDataGridGroupBy(list, url);
    }
    function loadDSDichvuGoiDV() {

        var list = $("#tdt_list_ctgoidv_dichvu");
        loadDataGridGroupBy(list, getUrlLoadDSDichvu());
    }

    function getUrlLoadDSDichvu() {
        var arr = [ singletonObject.dvtt, $("#tdt_gdv_themmoi_hinhthuc").val(), $("#tdt_gdv_themmoi_loaidv").val()];
        var url = "cmu_list_CMU_DANHSACHDV_THEONHOM?url=" + convertArray(arr);
        return url;
    }

    function initDulieu(loai) {
        initGridTonghopGoidichvu()
        initGridDichvuGoidichvu()
        loadDSGoiDVTheoBS()
        loadDSDichvuGoiDV()
        showButtonByMode("add")
        $("#tdt_gdv_themmoi_loaidv").val(loai)
        $("#tdt_gdv_themmoi_hinhthuc").prop("disabled", false)
    }

    function getDSGoiDVTheoBS() {
        var arr = [ singletonObject.dvtt, singletonObject.userId, $("#tdt_gdv_themmoi_loaidv").val(), goiBHYT];
        var url = "cmu_list_CMU_DANHSACHGOIDV_BS_HT?url=" + convertArray(arr);
        $.get(url).done(function (data) {
            danhsachGoidichvu = _.groupBy(data, "MA_GOI_DV")
            $("#tdt_ma_goidichvu").html("")
            Object.keys(danhsachGoidichvu).map(function (item, index) {
                $("#tdt_ma_goidichvu").append("<option value='"+item+"'>"+danhsachGoidichvu[item][0].TEN_GOI_DV+"</option>")
                if(index == 0) {
                    $("#tdt_ma_goidichvu").val(item)
                    loadDataIntoGridDV(item);
                }
            })
        }).fail(function() {
            notifiToClient("Red", "Lỗi lấy danh sách gói dịch vụ")
        })
    }

    function loadDataIntoGridDV(item) {
        var list = $("#tdt_list_ctgoidv")
        list[0].grid.beginReq();
        list.jqGrid("clearGridData");
        list.jqGrid('setGridParam', { data: danhsachGoidichvu[item]});
        list[0].grid.endReq();
        list.trigger('reloadGrid');
    }

    function insertDVGoidichvuSelected() {
        var loaidv = $("#tdt_gdv_loaidv").val();
        var listDichvu = "tdt_list_cdha"
        if(loaidv == "XN") {
            listDichvu = "tdt_list_xetnghiem"
        }
        if(loaidv == "TT" || loaidv == "PT") {
            listDichvu = "tdt_list_ttpt"
        }
        var $listDichvu = $("#"+listDichvu);
        var listCTGoiDichvu = $("#tdt_list_ctgoidv")
        var selRowIds = listCTGoiDichvu.jqGrid("getGridParam", "selarrrow")
        var objectDichvu = {};
        for (var i=0 ; i < selRowIds.length; i++) {
            var rowData = listCTGoiDichvu.jqGrid("getRowData", selRowIds[i]);
            objectDichvu[rowData.MA_DV] = rowData;
        }
        var allData = $listDichvu.jqGrid('getGridParam','data');
        allData.map(function(item){
            var madv = item.MA_CDHA
            if(loaidv == "XN") {
                madv = item.MA_XN
            }
            if(loaidv == "TT" || loaidv == "PT") {
                madv = item.MA_DV
            }
            if(objectDichvu[madv]){
                item.CHON = 1;
                item.SO_LUONG = 1;
                item.SOLUONG_AN = 1;
                item.THANHTIEN_BHYT = Number(1)*Number(item.GIA_BHYT);
                item.THANHTIEN_KBHYT = Number(1)*Number(item.GIA_KBHYT);
                console.log("â", objectDichvu[madv])
                if(loaidv == "XN") {
                    item.THANH_TIEN = Number(1)*Number(item.GIA_XN);
                }
                if(loaidv == "TT" || loaidv == "PT") {
                    item.THANH_TIEN = Number(1)*Number(item.GIA_DV);
                }

            }
            return item;
        })
        $listDichvu.jqGrid('setGridParam', { data: allData});
        clearToolbarJqgrid(listDichvu)
    }

    function initFormlaydulieungoaitru(type) {
        $("#modalXacnhandulieungoaitru").modal("show");
        $("#dt-cls-hinhthuc-dlngoaitru").val("0");
        $("#tdt-cls-laydulieungoaitru").attr("data-id", type);
        hideSelfLoading("tdt-cls-laydulieungoaitru")
    }

    function laydulieuXNNgoaitru(idButton) {
        var url_k = "noitru_xetnghiem_insert_dulieu_ngoaitru";
        var dataBN = thongtinhsba.thongtinbn
        var arr1 = [
            todieutriObject.STT_DIEUTRI,
            dataBN.STT_BENHAN,
            dataBN.STT_DOTDIEUTRI,
            singletonObject.dvtt,
            dataBN.MA_BENH_NHAN,
            dataBN.MAKHAMBENHNGOAITRU_NHAPVIEN,
            dataBN.SOVAOVIEN,
            dataBN.SOVAOVIEN_DT];
        $.post(url_k, {
            url: convertArray(arr1),
            loaihinh: $("#dt-cls-hinhthuc-dlngoaitru").val()
        }).done(function (field) {
            if (field.SAISOT.toString() == "0") {
                if (field.THANHTOAN.toString() == "1") {
                    notifiToClient("Red", "Đã có chỉ định được thanh toán. Chỉ lấy được những chỉ định chưa thanh toán. Phải hủy thanh toán viện phí của chỉ định!");
                } else if (field.KIEMTRA.toString() == "1") {
                    notifiToClient("Red", "Đã có chỉ định được lấy vào nội trú. Chỉ lấy được những chỉ định chưa được lấy vào nội trú.");

                } else if (field.COCDKBHYT.toString() == "1") {
                    notifiToClient("Red", "Trong dữ liệu có chỉ định không bảo hiểm đã được lấy vào.");
                } else {
                    notifiToClient("Green","Lấy dữ liệu thành công!",);
                }
                loadDSPhieuXetnghiem();
            } else {
                notifiToClient("Red","Không có dữ liệu!");
            }
        }).fail(function() {
            notifiToClient("Red", "Lấy dữ liệu thất bại!");
        }).always(function() {
            $("#modalXacnhandulieungoaitru").modal("hide");
            hideSelfLoading(idButton)
        });
    }

    function laydulieuCDHANgoaitru(idButton) {
        var url_k = "noitru_cdha_insert_dulieu_ngoaitru";
        var dataBN = thongtinhsba.thongtinbn
        var arr1 = [
            todieutriObject.STT_DIEUTRI,
            dataBN.STT_BENHAN,
            dataBN.STT_DOTDIEUTRI,
            singletonObject.dvtt,
            dataBN.MA_BENH_NHAN,
            dataBN.MAKHAMBENHNGOAITRU_NHAPVIEN,
            dataBN.SOVAOVIEN,
            dataBN.SOVAOVIEN_DT];
        $.post(url_k, {
            url: convertArray(arr1),
            loaihinh: $("#dt-cls-hinhthuc-dlngoaitru").val()
        }).done(function (field) {
            if (field.SAISOT.toString() == "0") {
                if (field.THANHTOAN.toString() == "1") {
                    notifiToClient("Red", "Đã có chỉ định được thanh toán. Chỉ lấy được những chỉ định chưa thanh toán. Phải hủy thanh toán viện phí của chỉ định!");
                } else if (field.KIEMTRA.toString() == "1") {
                    notifiToClient("Red", "Đã có chỉ định được lấy vào nội trú. Chỉ lấy được những chỉ định chưa được lấy vào nội trú.");

                } else if (field.COCDKBHYT.toString() == "1") {
                    notifiToClient("Red", "Trong dữ liệu có chỉ định không bảo hiểm đã được lấy vào.");
                } else {
                    notifiToClient("Green","Lấy dữ liệu thành công!",);
                }
                loadDSPhieuCDHA();
            } else {
                notifiToClient("Red","Không có dữ liệu!");
            }
        }).fail(function() {
            notifiToClient("Red", "Lấy dữ liệu thất bại!");
        }).always(function() {
            $("#modalXacnhandulieungoaitru").modal("hide");
            hideSelfLoading(idButton)
        });
    }
    function laydulieuTTPTNgoaitru(idButton) {
        var url_k = "noitru_ttpt_insert_dulieu_ngoaitru";
        var dataBN = thongtinhsba.thongtinbn
        var arr1 = [
            todieutriObject.STT_DIEUTRI,
            dataBN.STT_BENHAN,
            dataBN.STT_DOTDIEUTRI,
            singletonObject.dvtt,
            dataBN.MA_BENH_NHAN,
            dataBN.MAKHAMBENHNGOAITRU_NHAPVIEN,
            dataBN.SOVAOVIEN,
            dataBN.SOVAOVIEN_DT];
        $.post(url_k, {
            url: convertArray(arr1),
            loaihinh: $("#dt-cls-hinhthuc-dlngoaitru").val()
        }).done(function (field) {
            if (field.SAISOT.toString() == "0") {
                if (field.THANHTOAN.toString() == "1") {
                    notifiToClient("Red", "Đã có chỉ định được thanh toán. Chỉ lấy được những chỉ định chưa thanh toán. Phải hủy thanh toán viện phí của chỉ định!");
                } else if (field.KIEMTRA.toString() == "1") {
                    notifiToClient("Red", "Đã có chỉ định được lấy vào nội trú. Chỉ lấy được những chỉ định chưa được lấy vào nội trú.");

                } else if (field.COCDKBHYT.toString() == "1") {
                    notifiToClient("Red", "Trong dữ liệu có chỉ định không bảo hiểm đã được lấy vào.");
                } else {
                    notifiToClient("Green","Lấy dữ liệu thành công!",);
                }
                loadDSPhieuTTPT();
            } else {
                notifiToClient("Red","Không có dữ liệu!");
            }
        }).fail(function() {
            notifiToClient("Red", "Lấy dữ liệu thất bại!");
        }).always(function() {
            $("#modalXacnhandulieungoaitru").modal("hide");
            hideSelfLoading(idButton)
        });
    }

    function logXoaCDHATodieutriTungDV(ret) {
        var logCDHA = [
            "Thời gian y lệnh: " + ret.NGAY_CHI_DINH + " " + ret.GIO_CHI_DINH,
            "Tờ điều trị số: " + todieutriObject.STT_DIEUTRI,
            "Bác sĩ điều trị: "+ todieutriObject['TENBSDIEUTRI'],
            "Hình thức: "+ ($("#tdt_cdha_hinhthuc").val() == 0? "BHYT": "Thu phí"),
            "Số phiếu CDHA: "+ ret.SO_PHIEU_CDHA,
        ]
        var dsCDHA = []
        var listDataPhieuCDHA = getAllRowDataJqgrid("tdt_list_phieucdha");
        listDataPhieuCDHA.forEach(function (item) {
            if(item.SO_PHIEU_CDHA == ret.SO_PHIEU_CDHA){
                dsCDHA.push(item.MA_CDHA + " - "+ item.TEN_CDHA + " - Số lượng: "+ item.SO_LUONG + " lần")
            }

        })
        logCDHA.push("Danh sách CDHA: "+ dsCDHA.join("; "))
        luuLogHSBATheoBN({
            SOVAOVIEN: thongtinhsba.thongtinbn.SOVAOVIEN,
            LOAI: LOGHSBALOAI.CDHA.KEY,
            NOIDUNGBANDAU: logCDHA.join("; "),
            NOIDUNGMOI: "",
            USERID: singletonObject.userId,
            ACTION: LOGHSBAACTION.DELETE.KEY,
        })
    }

    function kysoTatcaTDT(object, idButton, element) {
        doSignCLSNoitruSmartca(getUrlToDieutriById(object), singletonObject.userId,
            singletonObject.dvtt, "TODIEUTRI_NOITRU", object.ID_DIEUTRI, function() {
                $("#action_huykysotodieutri").show()
                $("#footer_todieutri .add").hide()
                checkKysoAllTDT(idButton, element)

            }, null, object);
    }

    function kysoTatcaXNTDT(object, idButton, element) {
        var icd_khoadt = "";
        var ten_khoadt = "";
        var icdphu = "";
        var allICD = getAllRowDataJqgrid("tdt_list_icd") || [];
        allICD.forEach(function(item) {
            if(item.BENHCHINH == '1') {
                icd_khoadt = item.ICD;
                ten_khoadt = item.TENBENH;

            } else {
                icdphu = icdphu + item.ICD + "-"+ item.TENBENH + ";";
            }
        })
        var arr = [thongtinhsba.thongtinbn.MA_BENH_NHAN,
            object.CO_BHYT == 1? 0: 1,
            object.SO_PHIEU_DV, singletonObject.dvtt,
            thongtinhsba.thongtinbn.SOBENHAN,
            thongtinhsba.thongtinbn.STT_BENHAN,
            thongtinhsba.thongtinbn.STT_DOTDIEUTRI,
            object.STT_DIEUTRI,
            icd_khoadt,
            ten_khoadt,
            icdphu,
            "0",
            object.SOVAOVIEN,
            object.SOVAOVIEN_DT, 0];
        var url = "noitru_inphieuxetnghiem_svv?url=" + convertArray(arr);
        doSignCLSNoitruSmartca(url, singletonObject.userId,
            singletonObject.dvtt, "PHIEUCD_NOITRU_XN", object.SO_PHIEU_DV, function() {
                checkKysoAllTDT(idButton, element)
            }, null, object);
    }

    function kysoTatcaCDHATDT(object, idButton, element) {
        var icd_khoadt = "";
        var ten_khoadt = "";
        var icdphu = "";
        var allICD = getAllRowDataJqgrid( "tdt_list_icd") || [];
        allICD.forEach(function(item) {
            if(item.BENHCHINH == '1') {
                icd_khoadt = item.ICD;
                ten_khoadt = item.TENBENH;

            } else {
                icdphu = icdphu + item.ICD + "-"+ item.TENBENH + ";";
            }
        })
        var arr = [thongtinhsba.thongtinbn.MA_BENH_NHAN,
            object.CO_BHYT == 1? 0: 1,
            object.SO_PHIEU_DV, singletonObject.dvtt,
            thongtinhsba.thongtinbn.SOBENHAN,
            thongtinhsba.thongtinbn.STT_BENHAN,
            thongtinhsba.thongtinbn.STT_DOTDIEUTRI,
            object.STT_DIEUTRI,
            icd_khoadt,
            ten_khoadt,
            icdphu,
            "0",
            object.SOVAOVIEN,
            object.SOVAOVIEN_DT, 0, 1];
        var url = "noitru_inphieucdha_svv?url=" + convertArray(arr);
        doSignCLSNoitruSmartca(url, singletonObject.userId,
            singletonObject.dvtt, "PHIEUCD_NOITRU_CDHA", object.SO_PHIEU_DV, function() {
                checkKysoAllTDT(idButton, element)
            },null , object);
    }

    function kysoTatcaTTPTTDT(object, idButton, element) {
        var icd_khoadt = "";
        var ten_khoadt = "";
        var icdphu = "";
        var allICD = getAllRowDataJqgrid("tdt_list_icd") || [];
        allICD.forEach(function(item) {
            if(item.BENHCHINH == '1') {
                icd_khoadt = item.ICD;
                ten_khoadt = item.TENBENH;

            } else {
                icdphu = icdphu + item.ICD + "-"+ item.TENBENH + ";";
            }
        })
        var arr = [thongtinhsba.thongtinbn.MA_BENH_NHAN,
            object.CO_BHYT == 1? 0: 1,
            object.SO_PHIEU_DV,
            object.MA_LOAI_DICHVU,
            0, //ret.CHUYEN_KHOA
            0, //ret.CHI_TIET_CHUYEN_KHOA
            singletonObject.dvtt,
            thongtinhsba.thongtinbn.SOBENHAN,
            thongtinhsba.thongtinbn.STT_BENHAN,
            thongtinhsba.thongtinbn.STT_DOTDIEUTRI,
            object.STT_DIEUTRI,
            icd_khoadt,
            ten_khoadt,
            icdphu,
            "0",
            object.SOVAOVIEN,
            object.SOVAOVIEN_DT, 0];
        var url = "noitru_inphieuttpt_svv?url=" + convertArray(arr);
        doSignCLSNoitruSmartca(url, singletonObject.userId,
            singletonObject.dvtt, "PHIEUCD_NOITRU_TTPT", object.SO_PHIEU_DV, function() {
                checkKysoAllTDT(idButton, element)
            }, null, object);
    }

    function checkKysoAllTDT(idButton, element) {
        element.find(".spinner").hide();
        element.find(".viewPhieuTDT").html('<i class="fa fa-check"></i>');
        if($("#wrapLoadPreviewDSPHIEUTDT .item").length == $("#wrapLoadPreviewDSPHIEUTDT .fa-check").length) {
            hideSelfLoading(idButton);
        }
    }

    function loadEditor(id) {
        var instance = CKEDITOR.instances[id];
        if(!instance)
        {
            CKEDITOR.replace(id);
        }
    }

    $.extend({
        openModalTuongTrinhTT: function(idWrap, rowSelected) {
            showLoaderIntoWrapId(idWrap);
            initGridDSHinhAnhTT();
            loadEditor("thuthuat_tuongtrinh_noidung")
            if(!painterObjectTuongTrinhTTPT) {
                painterObjectTuongTrinhTTPT = vehinhanhbenhan('', idContainerTuongTrinhTTPT, idCanvasTuongTrinhTTPT);
            }
            $.get("cmu_getlist?url="+convertArray([
                singletonObject.dvtt,
                rowSelected.ID_DIEUTRI,
                thongtinhsba.thongtinbn.SOVAOVIEN,
                thongtinhsba.thongtinbn.SOVAOVIEN_DT,
                'HSBA_CMU_TDT_GETBYID'])
            ).done(function(dataTDT){
                lanPhauThuatObject = dataTDT[0];
                lanPhauThuatObject['CHITIETDVKT'] = rowSelected;
                lanPhauThuatObject['TYPE'] = "TT";
                getThongtinTTPTGlobal(rowSelected, function(result) {
                    var data = {
                        ...result[0],
                        EKIP: result[0].TINHTIENEKIP,
                        NGAYGIOTHYL: result[0].NGAYPTTT_1 ? result[0].NGAYPTTT_1 + " " + result[0].GIOPTTT_1 : moment().format("DD/MM/YYYY HH:mm"),
                        NGAYGIOKETQUA: result[0].NGAYPTTT_KT1 ? result[0].NGAYPTTT_KT1 + " " + result[0].GIOPTTT_KT1 : moment().format("DD/MM/YYYY HH:mm"),
                        NGAYCHIDINH: rowSelected.NGAY_CHI_DINH_CT,
                        BHYTKCHI: rowSelected.BHYTKCHI,
                        TENCHANDOAN_SAUPT: result[0].TENCHANDOAN_SAUPT ? result[0].TENCHANDOAN_SAUPT : result[0].CHANDOANSAUPTTT,
                        TENCHANDOAN_TRUOCPT: result[0].TENCHANDOAN_TRUOCPT ? result[0].TENCHANDOAN_TRUOCPT : result[0].CHANDOANTRUOCPTTT
                    }

                    $("#thuthuat_tuongtrinh_mautuongtrinh").combogrid({
                        url: 'select_maupttt_theodvtt',
                        debug: true,
                        width: "670px",
                        colModel: [{'columnName': 'MA_MAUPTT', 'label': 'MA_MAUPTT', hidden: true},
                            {'columnName': 'TEN_MAUPTTT', 'width': '100%', 'label': 'Tên Mẫu PTTT', 'align': 'left'},
                            {'columnName': 'NOIDUNG','label': 'NOIDUNG', hidden: true}
                        ],
                        select: function (event, ui) {
                            $("#thuthuat_tuongtrinh_mautuongtrinh").val(ui.item.TEN_MAUPTTT);
                            CKEDITOR.instances['thuthuat_tuongtrinh_noidung'].setData(ui.item.NOIDUNG)
                            return false;
                        }
                    });

                    $.get("cmu_getlist?url="+convertArray([
                        singletonObject.dvtt,
                        rowSelected.SO_PHIEU_DICHVU,
                        thongtinhsba.thongtinbn.SOVAOVIEN,
                        'CMU_GETALL_PPPT_SOPHIEU'])
                    ).done(function(listPPPT) {
                        $("#thuthuat_tuongtrinh_pppt").empty();
                        var list =  listPPPT.map(function(item) {
                            return {
                                ID: item.TEN_DV,
                                LABEL: item.TEN_DV
                            }
                        })
                        initSelect2IfnotIntance("thuthuat_tuongtrinh_pppt", list,
                            "ID", "LABEL", false, false,
                            data['PHUONGPHAP_TT_PT']? data['PHUONGPHAP_TT_PT'].split(";"): [], true)
                    });
                    Object.keys(data).forEach(function(key) {
                        $("#tuongTrinhThuThuatForm [name='"+key+"']").val(data[key]);
                        if(key == 'DA_CHAN_DOAN') {
                            if (data[key] == 0) {
                                $("#thuthuat_tuongtrinhpt_xoa").hide();
                                $("#thuthuat_tuongtrinhpt_xem").hide();
                                $("#thuthuat_tuongtrinhpt_kyso").hide();
                                $("#thuthuat_tuongtrinhpt_huykyso").hide();
                            } else {
                                console.log('rowSelected', rowSelected)
                                checkKySoTuongTrinhTT(rowSelected);
                            }
                        }
                    });
                    $.get("cmu_list_DANHSACH_MAYTTPT_DMTTPT?url="+convertArray([singletonObject.dvtt])).done(function(data) {
                        $("#thuthuat_sttMayTTPT").empty()
                        $("#thuthuat_sttMayTTPT").append("<option value=''>Không sử dụng</option>")
                        data.forEach(function(obj){
                            $("#thuthuat_sttMayTTPT").append("<option value='"+obj.STT+"'>"+ obj.TEN_MAY +"</option>")
                        })
                        $("#thuthuat_sttMayTTPT").select2();
                        $("#thuthuat_sttMayTTPT").val(data[0].STT).trigger("change")
                    })
                    CKEDITOR.instances['thuthuat_tuongtrinh_noidung'].setData(data['TRINHTU_TT_PT'])
                    loadDSHinhanh(rowSelected);
                    initSelect2IfnotIntance("thuthuat_tuongtrinh_bsphauthuat", singletonObject.danhsachtatcanhanvien, 'value', 'label', 0, 1, result[0].MA_BS_PTTP, 1);
                    initSelect2IfnotIntance("thuthuat_tuongtrinh_bsgayme", singletonObject.danhsachtatcanhanvien, 'value', 'label', 0, 1, result[0].MA_BS_GAYME, 1);
                    initSelect2IfnotIntance("thuthuat_tuongtrinh_pmvongtrong", singletonObject.danhsachtatcanhanvien, 'value', 'label', 0, 1, result[0].MA_PHU_MO_VONGTRONG, 1);
                    initSelect2IfnotIntance("thuthuat_tuongtrinh_dcvongtrong", singletonObject.danhsachtatcanhanvien, 'value', 'label', 0, 1, result[0].MA_BS_DC_VONGTRONG, 1);
                    initSelect2IfnotIntance("thuthuat_tuongtrinh_ktvgayme", singletonObject.danhsachtatcanhanvien, 'value', 'label', 0, 1, result[0].MA_KTV_GAYME, 1);
                    initSelect2IfnotIntance("thuthuat_tuongtrinh_pmvongngoai", singletonObject.danhsachtatcanhanvien, 'value', 'label', 0, 1, result[0].MA_PHU_MO_VONGNGOAI, 1);
                    initSelect2IfnotIntance("thuthuat_tuongtrinh_dcvongngoai", singletonObject.danhsachtatcanhanvien, 'value', 'label', 0, 1, result[0].MA_BS_DC_VONGNGOAI, 1);
                });
                // getFilesign769(
                //     "PHIEU_NOITRU_GIAYCHUNGNHAN_PTV",
                //     rowSelected.SO_PHIEU_DICHVU,
                //     -1,//singletonObject.userId,
                //     singletonObject.dvtt,
                //     thongtinhsba.thongtinbn.SOVAOVIEN,
                //     thongtinhsba.thongtinbn.SOVAOVIEN_DT,
                //     -1,
                //     function(data) {
                //         if(data.length > 0) {
                //             $("#thuthuat_tuongtrinhpt_kysochungnhan_ptv").hide();
                //             $("#thuthuat_tuongtrinhpt_huykysochungnhan_ptv").show();
                //         } else {
                //             $("#thuthuat_tuongtrinhpt_kysochungnhan_ptv").show();
                //             $("#thuthuat_tuongtrinhpt_huykysochungnhan_ptv").hide();
                //         }
                //     }
                // )
                $("#modalPhauThuatTuongtrinhTT").modal("show")
                hideLoaderIntoWrapId(idWrap);
                addTextTitleModal("titleModalPhauThuatTuongtrinhTT", "Tường trình thủ thuật")
            });
        }
    });

    function initGridDSHinhAnhTT() {
        var idList = "thuthuat_tuongtrinh_dsanh"
        var list = $("#"+idList);
        if(!list[0].grid) {
            list.jqGrid({
                url: "",
                datatype: "local",
                loadonce: true,
                height: 150,
                width: null,
                shrinkToFit: false,
                colModel: [
                    {label: "STT",name: 'STT', index: 'STT', width: 50},
                    {label: "Số phiếu",name: 'SO_PHIEU_DICHVU', index: 'SO_PHIEU_DICHVU', width: 400, hidden: true},
                    {label: "MA_DV",name: 'MA_DV', index: 'MA_DV', width: 400, hidden: true},
                    {label: "HINHANH",name: 'HINHANH', index: 'HINHANH', width: 400, hidden: true},
                    {label: "Hình ảnh",name: 'HINHANH_HT', index: 'HINHANH_HT', width: 400, formatter: function (cellvalue, options, rowObject) {
                            return "<img src='" + rowObject.HINHANH + "' style='width: 100px; height: 100px;'>";
                        }
                    },
                    {label: "dvtt",name: 'DVTT', index: 'DVTT', hidden: true},
                    {label: "makhambenh",name: 'MA_KHAM_BENH', index: 'MA_KHAM_BENH', hidden: true},
                    {label: "Tập tin",name: 'TEN', index: 'TEN', width: 300, hidden: true},
                    {
                        label: "Tải ảnh",
                        name: 'DUONGDAN_HT',
                        index: 'DUONGDAN_HT',
                        align: "center",
                        width: 150,
                        formatter: function (cellvalue, options, rowObject) {
                            return "<a target='_blank' href='" + cellvalue + "'>Tải</a>";
                        }
                    },
                    {label: "path",name: 'DUONGDAN', index: 'DUONGDAN', hidden: true}
                ],
                caption: "Hình ảnh",
                ignoreCase: true,
                rowNum: 1000000,
                onRightClickRow: function(id) {
                    if (id) {

                        $.contextMenu({
                            selector: '#'+idList+' tr',
                            reposition : false,
                            callback: function (key, options) {
                                var rowData = getThongtinRowSelected(idList);
                                if (key == "xoa") {
                                    confirmToClient(MESSAGEAJAX.CONFIRM, function() {
                                        var id = list.jqGrid("getGridParam", "selrow");
                                        list.jqGrid('delRowData',id);
                                    })
                                }
                                if (key == "sua") {
                                    $("#modalPhauThuatTuongtrinhPTHinhanh").modal("show");
                                    addTextTitleModal("titleModalPhauThuatTuongtrinhPTHinhanh", "Hình ảnh")
                                    $("#phauthuat_tuongtrinh_sttfile").val(rowData.STT)
                                    painterObjectTuongTrinhTTPT.changeImageSrc(rowData.HINHANH, idCanvasTuongTrinhTTPT)
                                }
                            },
                            items: {
                                "sua": {name: '<p><i class="fa fa-pencil-square-o text-primary" aria-hidden="true"></i> Sửa</p>'},
                                "xoa": {name: '<p><i class="fa fa-trash-o text-danger" aria-hidden="true"></i> Xóa</p>'}
                            }
                        });

                    }
                }
            });
        }
    }
    function loadDSHinhanh(data) {
        $("#thuthuat_tuongtrinh_dsanh").jqGrid('setGridParam', {
            datatype: 'json',
            url: "ttpt_select_files",
            postData: {
                soPhieu: data.SO_PHIEU_DICHVU,
                maDichVu: data.MA_DV,
                noiTru: 1,
                maKhamBenh: "",
                sttBenhAn: thongtinhsba.thongtinbn.STT_BENHAN,
                sttDieuTri: data.STT_DIEUTRI,
                sttDotDieuTri: data.STT_DOTDIEUTRI
            }
        }).trigger('reloadGrid');
    }

    function luuViTriTuongTrinh(data, callBackDone, callBackFail) {
        $.ajax({
            url: "noittru_ttpt_update_vitri_thoigian",
            method: "POST",
            data: {
                ma_dichvu: data.MA_DV,
                sophieu_thuthuatphauthuat: data.SO_PHIEU_DICHVU,
                dvtt: singletonObject.dvtt,
                vitrittpt: data.TEXT_VITRI,
                thoigianttpt: data.THOIGIAN_TTPT_VITRI,
                mabenhnhan: thongtinhsba.thongtinbn.MA_BENH_NHAN,
                sovaovien: thongtinhsba.thongtinbn.SOVAOVIEN,
                sovaovien_dt: thongtinhsba.thongtinbn.SOVAOVIEN_DT,
                idvitrittpt: data.MA_VITRI
            },
            async: true,
            dataType: 'json',
            success: function (data) {
                callBackDone(data);
            },
            error: function (e) {
                callBackFail(e);
            }
        });
    }

    function luuTuongTrinhTTPT(data, callBackDone, callBackFail) {
        $.post('cmu_post', {
            url: [
                data.SO_PHIEU_DICHVU,
                singletonObject.dvtt,
                data.MA_DV,
                data.TENCHANDOAN_SAUPT,
                thongtinhsba.thongtinbn.STT_BENHAN,
                data.STT_DOTDIEUTRI,
                data.STT_DIEUTRI,
                data.PHUONGPHAP_VOCAM,
                data.TEN_DV_JOIN,
                data.TRINHTUPTTT,
                data.CATCHISAU7NGAY,
                data.NGAYGIOTHYL + ":00",
                data.TAIBIEN,
                data.TUVONG,
                thongtinhsba.thongtinbn.SOVAOVIEN,
                thongtinhsba.thongtinbn.SOVAOVIEN_DT,
                singletonObject.userId,
                '',
                '',
                data.TRINHTUPTTT_XML5,
                thongtinhsba.thongtinbn.MA_BENH_NHAN,
                data.PHONG_CHI_DINH,
                singletonObject.maphongbenh,
                data.PP_VO_CAM,
                data.NGAYGIOKETQUA + ":00",
                data.TENCHANDOAN_TRUOCPT,
                data.DAN_LUU,
                data.BAC,
                data.NGAY_CAT_CHI,
                data.NGAY_RUT,
                data.KHAC,
                '',
                data.STT_MAMAY,
                data.CACHTHUCPHAUTHUAT,
                "CLS_TTPT_VLTL_CN_KQ_F_CMU_V5"
            ].join('```')
        }).done(function (dt) {
            if (dt > 0) {
                $.ajax({
                    url:"cmu_post_CMU_IMAGES_DVKT_DELALL",
                    method: "POST",
                    data: {
                        url: [singletonObject.dvtt, thongtinhsba.thongtinbn.SOVAOVIEN, data.SO_PHIEU_DICHVU, data.MA_DV].join("```")
                    },
                    async: false
                }).done(function() {
                    $.post('cmu_post_CMU_TTPT_EKIP_INS_V2', {
                        url: [
                            data.SO_PHIEU_DICHVU,
                            singletonObject.dvtt,
                            data.MA_DV,
                            thongtinhsba.thongtinbn.STT_BENHAN,
                            data.STT_DOTDIEUTRI,
                            data.STT_DIEUTRI,
                            data.STRING_EKIPBSPT,
                            data.STRING_EKIPBSGM,
                            thongtinhsba.thongtinbn.SOVAOVIEN,
                            thongtinhsba.thongtinbn.SOVAOVIEN_DT,
                            data.BSPHAUTHUAT,
                            data.BSGAYME,
                            data.KTVGAYMETE,
                            data.PHUMO1,
                            data.PHUMO2,
                            data.DUNGCU1,
                            data.DUNGCU2,
                            data.EKIP,
                            thongtinhsba.thongtinbn.MA_BENH_NHAN,
                            data.PHONG_CHI_DINH,
                            singletonObject.maphongbenh,
                            '', //PHUMO3,
                            '', //PHUMO4,
                            data.NGAYGIOKETQUA + ":00",
                            '-1'
                        ].join('```')
                    }).done(function (dt) {
                        callBackDone();
                    }).fail(function () {
                        callBackFail("Lỗi cập nhật ekip");
                    });
                });
            } else {
                callBackFail("Lỗi cập nhật kết quả");
            }
        }).fail(function (e) {
            callBackFail(e);
        });
    }

    function uploadHinhAnh(allHinhanh, data, callback) {
        let index = 0;

        function uploadNext() {
            if (index >= allHinhanh.length) {
                if (typeof callback === "function") callback();
                return;
            }

            $.ajax({
                url: "ttpt_insert_file",
                method: "POST",
                data: {
                    soPhieu: data.SO_PHIEU_DICHVU,
                    maDichVu: data.MA_DV,
                    noiTru: 1,
                    maKhamBenh: "",
                    sttBenhAn: thongtinhsba.thongtinbn.STT_BENHAN,
                    sttDieuTri: data.STT_DIEUTRI,
                    sttDotDieuTri: data.STT_DOTDIEUTRI,
                    hinhAnh: allHinhanh[index].HINHANH
                },
                dataType: 'json',
                success: function() {
                    index++;
                    uploadNext();
                },
                error: function() {
                    index++;
                    uploadNext();
                }
            });
        }

        uploadNext();
    }

    function luuThongTinEkip(rowSelected) {
        let dscchn = '';
        let manhanvien = '';
        let tennhan = '';
        let vitri = '';
        if($("#thuthuat_tuongtrinh_bsphauthuat").val().trim() !== '') {
            let bacsiptttInfo = dataChungChiHanhNghe[$("#thuthuat_tuongtrinh_bsphauthuat").val()];
            dscchn+=bacsiptttInfo.CHUNGCHI_HANHNGHE+';'
            manhanvien+=bacsiptttInfo.VALUE.toString()+';'
            tennhan+=bacsiptttInfo.TEXT+';'
            vitri += 'bacsipttt;';
        }
        if($("#thuthuat_tuongtrinh_pmvongtrong").val().trim() != '') {
            let bacsipttt_1Info = dataChungChiHanhNghe[$("#thuthuat_tuongtrinh_pmvongtrong").val()];
            dscchn+=bacsipttt_1Info.CHUNGCHI_HANHNGHE+';'
            manhanvien+=bacsipttt_1Info.VALUE.toString()+';'
            tennhan+=bacsipttt_1Info.TEXT+';'
            vitri += 'bacsipttt_1;';
        }
        if($("#thuthuat_tuongtrinh_pmvongngoai").val().trim() != '') {
            let bacsipttt_2Info = dataChungChiHanhNghe[$("#thuthuat_tuongtrinh_pmvongngoai").val()];
            dscchn+=bacsipttt_2Info.CHUNGCHI_HANHNGHE+';'
            manhanvien+=bacsipttt_2Info.VALUE.toString()+';'
            tennhan+=bacsipttt_2Info.TEXT+';'
            vitri += 'bacsipttt_2;';
        }
        if($("#thuthuat_tuongtrinh_bsgayme").val().trim() != '') {
            let bacsigaymeInfo = dataChungChiHanhNghe[$("#thuthuat_tuongtrinh_bsgayme").val()];
            dscchn+=bacsigaymeInfo.CHUNGCHI_HANHNGHE+';'
            manhanvien+=bacsigaymeInfo.VALUE.toString()+';'
            tennhan+=bacsigaymeInfo.TEXT+';'
            vitri += 'bacsigayme;';
        }
        if($("#thuthuat_tuongtrinh_ktvgayme").val().trim() != '') {
            let bacsigayme_1Info = dataChungChiHanhNghe[$("#thuthuat_tuongtrinh_ktvgayme").val()];
            dscchn+=bacsigayme_1Info.CHUNGCHI_HANHNGHE+';'
            manhanvien+=bacsigayme_1Info.VALUE.toString()+';'
            tennhan+=bacsigayme_1Info.TEXT+';'
            vitri += 'bacsigayme_1;';
        }
        if($("#thuthuat_tuongtrinh_dcvongtrong").val().trim() != '') {
            let dcvongtrongInfo = dataChungChiHanhNghe[$("#thuthuat_tuongtrinh_dcvongtrong").val()];
            dscchn+=dcvongtrongInfo.CHUNGCHI_HANHNGHE+';'
            manhanvien+=dcvongtrongInfo.VALUE.toString()+';'
            tennhan+=dcvongtrongInfo.TEXT+';'
            vitri += 'dcvongtrong;';
        }
        if($("#thuthuat_tuongtrinh_dcvongngoai").val().trim() != '') {
            let dcvongngoaiInfo = dataChungChiHanhNghe[$("#thuthuat_tuongtrinh_dcvongngoai").val()];
            dscchn+=dcvongngoaiInfo.CHUNGCHI_HANHNGHE+';'
            manhanvien+=dcvongngoaiInfo.VALUE.toString()+';'
            tennhan+=dcvongngoaiInfo.TEXT+';'
            vitri += 'dcvongngoai;';
        }
        // Form nội trú không có vị trí này
        // if($("#cmupt_pm4").val().trim() != '') {
        //     dscchn+=$("#cmupt_pm4_cnhh").val()+';'
        //     manhanvien+=$("#ma_cmupt_pm4_cnhh").val()+';'
        //     tennhan+=$("#cmupt_pm4").val()+';'
        //     vitri += 'cmupt_pm4;';
        // }
        $.post("cmu_post", {
            url: [
                singletonObject.dvtt,
                rowSelected.MA_DV,
                rowSelected.SO_PHIEU_DICHVU,
                thongtinhsba.thongtinbn.SOVAOVIEN,
                thongtinhsba.thongtinbn.SOVAOVIEN_DT,
                dscchn,
                manhanvien,
                tennhan,
                vitri,
                'CMU_LUUCCHN_EKIP'
            ].join("```")
        }).done(function () {
            notifiToClient("Green", "Lưu Ekip XML3 thành công");
        }).fail(function() {
            notifiToClient("Red", "Lưu Ekip XML3 error");
        });
    }

    function checkKySoTuongTrinhTT(data) {
        getFilesign769(
            "PHIEU_NOITRU_TRUONGTRINHPT",
            data.MA_DV,
            -1,//singletonObject.userId,
            singletonObject.dvtt,
            thongtinhsba.thongtinbn.SOVAOVIEN,
            thongtinhsba.thongtinbn.SOVAOVIEN_DT,
            data.ID_DIEUTRI,
            function(dataReturn) {
                if (dataReturn.length > 0) {
                    $("#thuthuat_tuongtrinhpt_kyso").hide()
                    $("#thuthuat_tuongtrinhpt_luu").hide()
                    $("#thuthuat_tuongtrinhpt_xoa").hide()
                    $("#thuthuat_tuongtrinhpt_huykyso").show()
                } else {
                    $("#thuthuat_tuongtrinhpt_kyso").show()
                    $("#thuthuat_tuongtrinhpt_luu").show()
                    $("#thuthuat_tuongtrinhpt_xoa").show()
                    $("#thuthuat_tuongtrinhpt_huykyso").hide()
                }
                $("#thuthuat_tuongtrinhpt_xem").show()
            });
    }
})