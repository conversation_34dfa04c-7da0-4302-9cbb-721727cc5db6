<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="report name" pageWidth="421" pageHeight="595" whenNoDataType="AllSectionsNoDetail" columnWidth="401" leftMargin="10" rightMargin="10" topMargin="10" bottomMargin="10" uuid="094bb44e-c3a4-4e55-be44-2d9dcaeab41b">
	<property name="ireport.zoom" value="1.5"/>
	<property name="ireport.x" value="0"/>
	<property name="ireport.y" value="0"/>
	<subDataset name="Table Dataset 1" uuid="fa03ad92-620f-4db4-822a-bb9c323e476f"/>
	<parameter name="tenbenhvien" class="java.lang.String"/>
	<parameter name="hovaten" class="java.lang.String"/>
	<parameter name="tuoiht" class="java.lang.String"/>
	<parameter name="gioitinh" class="java.lang.String"/>
	<parameter name="diachi" class="java.lang.String"/>
	<parameter name="sothebhyt" class="java.lang.String"/>
	<parameter name="chandoan" class="java.lang.String"/>
	<parameter name="ngay" class="java.lang.String"/>
	<parameter name="sttthuoc" class="java.lang.String"/>
	<parameter name="tenthuoc" class="java.lang.String"/>
	<parameter name="matoathuoc" class="java.lang.String">
		<defaultValueExpression><![CDATA[]]></defaultValueExpression>
	</parameter>
	<parameter name="dvtt" class="java.lang.String">
		<defaultValueExpression><![CDATA[]]></defaultValueExpression>
	</parameter>
	<parameter name="nghiepvu" class="java.lang.String">
		<defaultValueExpression><![CDATA[]]></defaultValueExpression>
	</parameter>
	<parameter name="mabacsi" class="java.lang.String">
		<defaultValueExpression><![CDATA[]]></defaultValueExpression>
	</parameter>
	<parameter name="bacsidieutri" class="java.lang.String"/>
	<parameter name="thang" class="java.lang.String"/>
	<parameter name="nam" class="java.lang.String"/>
	<parameter name="tentoathuoc" class="java.lang.String"/>
	<parameter name="namsinh" class="java.lang.String"/>
	<parameter name="gioitinh_nam" class="java.lang.String"/>
	<parameter name="gioitinh_nu" class="java.lang.String"/>
	<parameter name="tuoithang" class="java.lang.String"/>
	<parameter name="sophieu" class="java.lang.String"/>
	<parameter name="tuoi" class="java.lang.String"/>
	<parameter name="loidantoathuoc" class="java.lang.String"/>
	<parameter name="ngayhen" class="java.lang.String"/>
	<parameter name="sovaovien" class="java.lang.String">
		<defaultValueExpression><![CDATA[]]></defaultValueExpression>
	</parameter>
	<parameter name="chuky" class="java.lang.String"/>
	<parameter name="nvtiepnhan" class="java.lang.String"/>
	<parameter name="parameter1" class="java.lang.String"/>
	<parameter name="masonguoibenh" class="java.lang.String"/>
	<parameter name="mathe_kythu3" class="java.lang.String"/>
	<parameter name="the8910" class="java.lang.String"/>
	<parameter name="mathe_5kytucuoi" class="java.lang.String"/>
	<parameter name="the45" class="java.lang.String"/>
	<parameter name="mathe_2kytudau" class="java.lang.String"/>
	<parameter name="the67" class="java.lang.String"/>
	<queryString language="plsql">
		<![CDATA[{CALL CMU_KB_TOATHUOC_TT05($P{matoathuoc}, $P{dvtt}, $P{nghiepvu}, $P{mabacsi},$P{sovaovien},$P{ORACLE_REF_CURSOR})}]]>
	</queryString>
	<field name="ten_hethong" class="java.lang.String">
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<field name="STT_order" class="java.lang.Integer">
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<field name="ten_nhanvien" class="java.lang.String">
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<field name="DVT" class="java.lang.String">
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<field name="TenVatTu" class="java.lang.String">
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<field name="MOTA_CHITIET1" class="java.lang.String"/>
	<field name="SO_LUONG_UONG" class="java.lang.String"/>
	<field name="SANG_UONG" class="java.lang.String">
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<field name="TRUA_UONG" class="java.lang.String">
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<field name="CHIEU_UONG" class="java.lang.String">
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<field name="TOI_UONG" class="java.lang.String">
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<field name="GHI_CHU_CT_TOA_THUOC" class="java.lang.String">
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<field name="MA_TOA_THUOC" class="java.lang.String">
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<field name="SO_LUONG" class="java.math.BigDecimal">
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<field name="MA_BAC_SI_THEMTHUOC" class="java.lang.Integer">
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<field name="SO_LUONG_THUC_LINH" class="java.math.BigDecimal">
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<field name="THANHTIEN_THUOC" class="java.math.BigDecimal">
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<field name="CACH_SU_DUNG" class="java.lang.String">
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<field name="GHI_CHU_CT_TOA_THUOC1" class="java.lang.String"/>
	<field name="tt" class="java.lang.Integer">
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<field name="TUOI" class="java.lang.Integer">
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<field name="nguoilienhe" class="java.lang.String"/>
	<field name="tile" class="java.lang.Integer"/>
	<field name="Hoatchat" class="java.lang.String"/>
	<field name="MOTA_CHITIET" class="java.lang.String"/>
	<field name="nvtiepnhan" class="java.lang.String"/>
	<field name="tuoiht" class="java.lang.String">
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<field name="soCMT" class="java.lang.String"/>
	<field name="sodienthoai" class="java.lang.String"/>
	<field name="chuoingayhen" class="java.lang.String"/>
	<field name="CANNANG" class="java.lang.String"/>
	<field name="STT_HTK" class="java.lang.String"/>
	<field name="MA_DON_THUOC" class="java.lang.String"/>
	<field name="diachi" class="java.lang.String"/>
	<field name="SDTNGUOILH" class="java.lang.String"/>
	<field name="NGAY_SINH" class="java.lang.String"/>
	<field name="ngayhen" class="java.lang.String"/>
	<field name="NGUOICHIDINH" class="java.lang.String"/>
	<field name="CCCD" class="java.lang.String"/>
	<variable name="v_tile" class="java.lang.Integer">
		<variableExpression><![CDATA[$F{tile} <= 20 ? $F{tile}: 
(
    $P{mathe_kythu3}.equals("3")? 5: 
    (
        $P{mathe_kythu3}.equals("4") ? 20 : 0    
    )
)]]></variableExpression>
	</variable>
	<group name="G_ten_nhanvien">
		<groupExpression><![CDATA[$F{ten_nhanvien}]]></groupExpression>
		<groupHeader>
			<band height="18">
				<printWhenExpression><![CDATA[$F{tt}>1]]></printWhenExpression>
				<textField>
					<reportElement positionType="Float" x="0" y="0" width="401" height="18" uuid="3ee0cdce-cb75-44f4-ab33-cf61b7be58f4">
						<printWhenExpression><![CDATA[$F{tt} > 1]]></printWhenExpression>
					</reportElement>
					<box leftPadding="5" bottomPadding="0">
						<bottomPen lineWidth="0.0" lineStyle="Dashed"/>
					</box>
					<textElement verticalAlignment="Middle">
						<font fontName="Times New Roman" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{ten_nhanvien}]]></textFieldExpression>
				</textField>
			</band>
		</groupHeader>
	</group>
	<background>
		<band splitType="Stretch"/>
	</background>
	<title>
		<band height="185" splitType="Stretch">
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement x="0" y="0" width="300" height="16" uuid="472eb788-3775-4c3e-b07a-6545b139d71f"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{tenbenhvien}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" x="47" y="104" width="354" height="16" uuid="841e28a9-54de-4296-b58e-2e3c48844e60"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" isBold="false" isItalic="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{diachi}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" x="0" y="136" width="401" height="33" isPrintWhenDetailOverflows="true" uuid="a923a76b-f2c9-4850-b670-f735913c090a"/>
				<textElement verticalAlignment="Top">
					<font fontName="Times New Roman" isBold="false" isItalic="false"/>
				</textElement>
				<textFieldExpression><![CDATA["- "+$P{chandoan}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" x="45" y="72" width="158" height="16" uuid="c17099d1-8f3f-49b3-beef-d516878bcf1e"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" isBold="true" isItalic="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{hovaten}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement positionType="Float" x="90" y="49" width="229" height="22" uuid="c93b3798-df56-4707-ba98-a7c51955ef8c"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="16" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[($P{mathe_2kytudau}.equals("") && !$P{dvtt}.equals("96014") || $P{dvtt}.equals("96172")) ? $P{tentoathuoc} : "ĐƠN THUỐC"]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" x="0" y="16" width="231" height="16" uuid="c095a7d4-ce46-4b94-97d8-813929f5b3a0"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="9" isBold="false" isItalic="false"/>
				</textElement>
				<textFieldExpression><![CDATA["Địa chỉ: "+$F{diachi}]]></textFieldExpression>
			</textField>
			<line>
				<reportElement positionType="Float" x="0" y="184" width="401" height="1" uuid="2ca37569-30e9-4ece-ae30-8f2aac9be584"/>
			</line>
			<staticText>
				<reportElement positionType="Float" mode="Transparent" x="0" y="72" width="45" height="16" forecolor="#000000" backcolor="#FFFFFF" uuid="4a196134-3b3e-4293-87d2-8e3b37a0268f"/>
				<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<text><![CDATA[- Họ tên:]]></text>
			</staticText>
			<staticText>
				<reportElement positionType="Float" mode="Transparent" x="0" y="104" width="47" height="16" forecolor="#000000" backcolor="#FFFFFF" uuid="27718cbd-34bf-485b-b6cb-e6ac57613f14"/>
				<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<text><![CDATA[- Địa chỉ:]]></text>
			</staticText>
			<staticText>
				<reportElement positionType="Float" mode="Transparent" x="0" y="88" width="164" height="16" forecolor="#000000" backcolor="#FFFFFF" uuid="3130bb12-d841-4e7f-a553-2c199494a2ea"/>
				<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<text><![CDATA[- Số thẻ bảo hiểm y tế (nếu có):]]></text>
			</staticText>
			<textField pattern="" isBlankWhenNull="true">
				<reportElement positionType="Float" mode="Transparent" x="194" y="90" width="30" height="13" forecolor="#000000" backcolor="#FFFFFF" uuid="efe561a6-772c-4ae1-8ff1-9e62923fd86c"/>
				<box leftPadding="2">
					<topPen lineWidth="1.0"/>
					<bottomPen lineWidth="1.0"/>
					<rightPen lineWidth="1.0"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{mathe_kythu3}]]></textFieldExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="true">
				<reportElement positionType="Float" mode="Transparent" x="294" y="90" width="40" height="13" forecolor="#000000" backcolor="#FFFFFF" uuid="447157d0-5218-4ef4-a258-eb7cc002f594"/>
				<box leftPadding="2">
					<topPen lineWidth="1.0"/>
					<bottomPen lineWidth="1.0"/>
					<rightPen lineWidth="1.0"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{the8910}]]></textFieldExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="true">
				<reportElement positionType="Float" mode="Transparent" x="334" y="90" width="50" height="13" forecolor="#000000" backcolor="#FFFFFF" uuid="6bc6c104-6958-483d-a4bf-a28f093f2674"/>
				<box leftPadding="2">
					<topPen lineWidth="1.0"/>
					<bottomPen lineWidth="1.0"/>
					<rightPen lineWidth="1.0"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{mathe_5kytucuoi}]]></textFieldExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="true">
				<reportElement positionType="Float" mode="Transparent" x="224" y="90" width="30" height="13" forecolor="#000000" backcolor="#FFFFFF" uuid="2db2e930-d2f6-4c54-b8e2-79681363f1c2"/>
				<box leftPadding="2">
					<topPen lineWidth="1.0"/>
					<bottomPen lineWidth="1.0"/>
					<rightPen lineWidth="1.0"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{the45}]]></textFieldExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="true">
				<reportElement positionType="Float" mode="Transparent" x="254" y="90" width="40" height="13" forecolor="#000000" backcolor="#FFFFFF" uuid="07fa8da3-7c41-40ab-b377-1cf0464d40aa"/>
				<box leftPadding="2">
					<topPen lineWidth="1.0"/>
					<bottomPen lineWidth="1.0"/>
					<rightPen lineWidth="1.0"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{the67}]]></textFieldExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="true">
				<reportElement positionType="Float" mode="Transparent" x="164" y="90" width="30" height="13" forecolor="#000000" backcolor="#FFFFFF" uuid="c58c9bc8-05e4-4830-9749-ccaac9f6b1ac"/>
				<box leftPadding="2">
					<topPen lineWidth="1.0"/>
					<leftPen lineWidth="1.0"/>
					<bottomPen lineWidth="1.0"/>
					<rightPen lineWidth="1.0"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{mathe_2kytudau}]]></textFieldExpression>
			</textField>
			<componentElement>
				<reportElement mode="Transparent" x="325" y="19" width="70" height="24" uuid="f7264a0c-b912-4f8d-b9dd-afe8e7c6371d"/>
				<jr:barbecue xmlns:jr="http://jasperreports.sourceforge.net/jasperreports/components" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports/components http://jasperreports.sourceforge.net/xsd/components.xsd" type="2of7" drawText="false" checksumRequired="false">
					<jr:codeExpression><![CDATA[$P{masonguoibenh}]]></jr:codeExpression>
				</jr:barbecue>
			</componentElement>
			<textField>
				<reportElement x="304" y="0" width="96" height="16" uuid="2a5cc5e2-8e9e-4c3a-8355-917c1ee8d8cf"/>
				<textElement textAlignment="Center" markup="none">
					<font fontName="Times New Roman" size="9" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{MA_DON_THUOC}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="319" y="44" width="82" height="13" uuid="c4be7ff0-93a8-4a87-a887-fdaf79a6b269"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{masonguoibenh}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement positionType="Float" x="203" y="72" width="197" height="16" uuid="18e622b1-fd52-41e3-a220-65d7a9375d5f"/>
				<textElement verticalAlignment="Middle" markup="none">
					<font fontName="Times New Roman" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA["Ngày sinh: " + $F{NGAY_SINH} + "; Cân nặng: "+$F{CANNANG}+" Kg; "+ "Giới tính:"+$P{gioitinh}]]></textFieldExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="true">
				<reportElement positionType="Float" mode="Transparent" x="268" y="17" width="50" height="32" forecolor="#000000" backcolor="#FFFFFF" uuid="11c4978c-fd13-4961-8513-4faf12d68104">
					<printWhenExpression><![CDATA[$F{STT_HTK} != null && !$F{STT_HTK}.equals("0")]]></printWhenExpression>
				</reportElement>
				<box leftPadding="2">
					<pen lineWidth="1.0"/>
					<topPen lineWidth="1.0"/>
					<leftPen lineWidth="1.0"/>
					<bottomPen lineWidth="1.0"/>
					<rightPen lineWidth="1.0"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{STT_HTK}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" x="1" y="32" width="231" height="16" uuid="d428e6b8-6c3f-4398-be21-c794d5aaaebb"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="9" isBold="false" isItalic="false"/>
				</textElement>
				<textFieldExpression><![CDATA["Số ĐT:  "  + (
    $F{sodienthoai} != null ? $F{sodienthoai}: ""
)]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" x="0" y="169" width="401" height="15" isPrintWhenDetailOverflows="true" uuid="ae881bbb-7994-49d0-a8f2-4203b6e1f44b"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" isBold="false" isItalic="false"/>
				</textElement>
				<textFieldExpression><![CDATA["Thuốc điều trị: "]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" mode="Transparent" x="0" y="120" width="400" height="16" isPrintWhenDetailOverflows="true" forecolor="#000000" backcolor="#FFFFFF" uuid="c3776b56-42aa-4d38-abfa-2493a61ffc6a"/>
				<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA["- Số định danh cá nhân/số căn cước công dân/số căn cước:" + ($F{CCCD} == null? "": $F{CCCD})]]></textFieldExpression>
			</textField>
		</band>
	</title>
	<detail>
		<band height="18" splitType="Stretch">
			<textField isStretchWithOverflow="true">
				<reportElement positionType="Float" mode="Transparent" x="20" y="0" width="311" height="18" forecolor="#000000" backcolor="#FFFFFF" uuid="3c840543-f49f-4ba9-9cd5-a8d0aab5ff6a"/>
				<box>
					<pen lineStyle="Dotted"/>
					<topPen lineWidth="0.0" lineStyle="Dotted"/>
					<leftPen lineStyle="Dotted"/>
					<bottomPen lineStyle="Dotted"/>
					<rightPen lineStyle="Dotted"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Top" rotation="None" markup="none">
					<font fontName="Times New Roman" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{MOTA_CHITIET1}]]></textFieldExpression>
			</textField>
			<textField pattern="###0" isBlankWhenNull="false">
				<reportElement positionType="Float" stretchType="RelativeToTallestObject" mode="Transparent" x="0" y="0" width="20" height="18" forecolor="#000000" backcolor="#FFFFFF" uuid="1b243031-963e-48fa-8e52-6156c7b6894f"/>
				<box leftPadding="5">
					<pen lineStyle="Dotted"/>
					<topPen lineWidth="0.0" lineStyle="Dotted"/>
					<leftPen lineStyle="Dotted"/>
					<bottomPen lineStyle="Dotted"/>
					<rightPen lineStyle="Dotted"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Top" rotation="None" markup="none">
					<font fontName="Times New Roman" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{REPORT_COUNT}+"."]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true">
				<reportElement positionType="Float" stretchType="RelativeToTallestObject" mode="Transparent" x="331" y="0" width="70" height="18" forecolor="#000000" backcolor="#FFFFFF" uuid="25bba590-0539-4d94-b84a-45f63cfaa272"/>
				<box>
					<pen lineStyle="Dotted"/>
					<topPen lineWidth="0.0" lineStyle="Dotted"/>
					<leftPen lineStyle="Dotted"/>
					<bottomPen lineStyle="Dotted"/>
					<rightPen lineStyle="Dotted"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Top" rotation="None" markup="none">
					<font fontName="Times New Roman" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{SO_LUONG_UONG}]]></textFieldExpression>
			</textField>
		</band>
		<band height="15">
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement mode="Transparent" x="109" y="0" width="76" height="15" isPrintWhenDetailOverflows="true" forecolor="#000000" backcolor="#FFFFFF" uuid="e75ebb8a-4873-4b26-8b20-5a60cf81b45c"/>
				<box leftPadding="3"/>
				<textElement textAlignment="Left" verticalAlignment="Top" rotation="None" markup="none">
					<font fontName="Times New Roman" isBold="false" isItalic="true" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{SANG_UONG}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement mode="Transparent" x="185" y="0" width="70" height="15" isPrintWhenDetailOverflows="true" forecolor="#000000" backcolor="#FFFFFF" uuid="e28f0bdb-d989-47d9-b1e8-d0923a0dbc2f"/>
				<box leftPadding="3"/>
				<textElement textAlignment="Left" verticalAlignment="Top" rotation="None" markup="none">
					<font fontName="Times New Roman" isBold="false" isItalic="true" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{TRUA_UONG}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement mode="Transparent" x="255" y="0" width="76" height="15" isPrintWhenDetailOverflows="true" forecolor="#000000" backcolor="#FFFFFF" uuid="6e1fe9af-b047-4d01-8927-912a173344d8"/>
				<box leftPadding="3"/>
				<textElement textAlignment="Left" verticalAlignment="Top" rotation="None" markup="none">
					<font fontName="Times New Roman" isBold="false" isItalic="true" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{CHIEU_UONG}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement mode="Transparent" x="20" y="0" width="89" height="15" isPrintWhenDetailOverflows="true" forecolor="#000000" backcolor="#FFFFFF" uuid="9763b33f-d424-41dc-a2d2-c530686f0d06"/>
				<textElement textAlignment="Left" verticalAlignment="Top" rotation="None" markup="none">
					<font fontName="Times New Roman" isBold="false" isItalic="true" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{GHI_CHU_CT_TOA_THUOC1}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement mode="Transparent" x="331" y="0" width="70" height="15" isPrintWhenDetailOverflows="true" forecolor="#000000" backcolor="#FFFFFF" uuid="60b420fb-9986-4ca4-8bd7-b4d4d9de4526"/>
				<box leftPadding="3"/>
				<textElement textAlignment="Left" verticalAlignment="Top" rotation="None" markup="none">
					<font fontName="Times New Roman" isBold="false" isItalic="true" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{TOI_UONG}]]></textFieldExpression>
			</textField>
		</band>
	</detail>
	<pageFooter>
		<band height="14">
			<textField pattern="&apos;Ngày giờ in:&apos; dd/MM/YYYY HH:mm:ss">
				<reportElement x="254" y="0" width="147" height="14" uuid="e73cad10-b4ff-40ae-99d1-b4bd9741e915"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="8" isItalic="true"/>
				</textElement>
				<textFieldExpression><![CDATA[new Date()]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement x="0" y="0" width="254" height="14" uuid="a03fb8cd-8d8d-40f0-bfb4-46b0a2a60893"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="8" isBold="false" isItalic="true"/>
				</textElement>
				<textFieldExpression><![CDATA["Nhân viên tiếp nhận: "+$F{nvtiepnhan}]]></textFieldExpression>
			</textField>
		</band>
	</pageFooter>
	<summary>
		<band height="171" splitType="Stretch">
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement x="203" y="4" width="198" height="18" uuid="e402f9bc-a9c8-4e37-9439-88bc5a1bad68"/>
				<box>
					<pen lineStyle="Dotted"/>
					<topPen lineWidth="0.0" lineStyle="Dotted"/>
					<leftPen lineStyle="Dotted"/>
					<bottomPen lineStyle="Dotted"/>
					<rightPen lineStyle="Dotted"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" isBold="false" isItalic="true" isUnderline="false"/>
				</textElement>
				<textFieldExpression><![CDATA["Ngày "+$P{ngay}+" tháng "+$P{thang}+" năm"+$P{nam}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="206" y="22" width="195" height="18" uuid="3a84ef4d-53ac-496b-a9ab-9b311ceb8ba8"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" isBold="false" isItalic="false"/>
				</textElement>
				<text><![CDATA[Bác sỹ khám bệnh]]></text>
			</staticText>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement x="206" y="109" width="195" height="18" uuid="3f26a080-ed74-457d-a98b-740e57f4a80f">
					<printWhenExpression><![CDATA[!$P{dvtt}.equals("96025")]]></printWhenExpression>
				</reportElement>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" isBold="true" isItalic="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{NGUOICHIDINH}.equals("0")?$P{bacsidieutri}:$F{NGUOICHIDINH}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" mode="Transparent" x="47" y="4" width="156" height="18" forecolor="#000000" backcolor="#FFFFFF" uuid="*************-4998-b2b7-ec29cef1af0d"/>
				<textElement verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" isBold="false" isItalic="true" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{loidantoathuoc}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="2" y="92" width="88" height="20" uuid="46ae8025-925e-4abc-8d17-2e992d11dc25">
					<printWhenExpression><![CDATA[$F{tile} != 0]]></printWhenExpression>
				</reportElement>
				<box>
					<topPen lineWidth="1.0"/>
					<leftPen lineWidth="1.0"/>
					<bottomPen lineWidth="1.0"/>
					<rightPen lineWidth="1.0"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA["Thu: " + $V{v_tile}.toString() + "%"]]></textFieldExpression>
			</textField>
			<image scaleImage="FillFrame" onErrorType="Blank">
				<reportElement positionType="Float" x="245" y="40" width="128" height="49" uuid="c0176a3a-fe8b-48a5-8335-f7960473dc4f">
					<printWhenExpression><![CDATA[false]]></printWhenExpression>
				</reportElement>
				<imageExpression><![CDATA[new java.io.ByteArrayInputStream(org.apache.commons.codec.binary.Base64.decodeBase64($P{chuky}.getBytes()))]]></imageExpression>
			</image>
			<staticText>
				<reportElement positionType="Float" mode="Transparent" x="0" y="112" width="180" height="16" forecolor="#000000" backcolor="#FFFFFF" uuid="348d4e74-4354-4fde-a5a9-c5a832694932"/>
				<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" isBold="false" isItalic="true" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<text><![CDATA[- Khám lại xin mang theo đơn này.]]></text>
			</staticText>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" mode="Transparent" x="1" y="128" width="401" height="16" forecolor="#000000" backcolor="#FFFFFF" uuid="3e9af07a-0106-43db-baa6-28e600c8f2a7">
					<printWhenExpression><![CDATA[($F{nguoilienhe} != null && !$F{nguoilienhe}.isEmpty() && !$F{nguoilienhe}.equals(" "))  || 
!$P{dvtt}.equals("96154")]]></printWhenExpression>
				</reportElement>
				<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" isBold="false" isItalic="true" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA["- Số điện thoại liên hệ: "+$F{SDTNGUOILH}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" mode="Transparent" x="1" y="22" width="202" height="16" forecolor="#000000" backcolor="#FFFFFF" uuid="bae15656-b5b5-4468-bd15-22cf844e7c9d">
					<printWhenExpression><![CDATA[$F{chuoingayhen} != null && 
!$F{chuoingayhen}.isEmpty()&&
!$F{chuoingayhen}.equals(" ")]]></printWhenExpression>
				</reportElement>
				<textElement verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" isBold="false" isItalic="true" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA["- Hẹn tái khám sau "+ $F{ngayhen} +" ngày. " +  $F{chuoingayhen}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" mode="Transparent" x="1" y="4" width="46" height="18" forecolor="#000000" backcolor="#FFFFFF" uuid="3b19b9d1-ae4d-44c3-b1d9-7537c103a331"/>
				<textElement verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" isBold="false" isItalic="true" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA["-Lời dặn: "]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" mode="Transparent" x="2" y="144" width="399" height="24" forecolor="#000000" backcolor="#FFFFFF" uuid="f6d9754c-097f-42ec-991e-3c59ab48bbc2">
					<printWhenExpression><![CDATA[($F{nguoilienhe} != null && !$F{nguoilienhe}.isEmpty() && !$F{nguoilienhe}.equals(" "))  || 
!$P{dvtt}.equals("96154")]]></printWhenExpression>
				</reportElement>
				<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" isBold="false" isItalic="true" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{TUOI} > 6 ?" ": "- Tên bố hoặc mẹ của trẻ hoặc người đưa trẻ đến khám bệnh, chữa bệnh:" + 
($F{nguoilienhe}!=null?$F{nguoilienhe}.toUpperCase():" ")]]></textFieldExpression>
			</textField>
		</band>
	</summary>
</jasperReport>
