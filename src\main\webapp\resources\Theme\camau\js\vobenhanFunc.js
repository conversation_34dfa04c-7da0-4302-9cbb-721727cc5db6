function getObjectMauHSBAPAGE2CamelCase() {
    return [
        {
            "label": "ID",
            "key": "ID",
            "type": "textfield",
            others: {
                hidden: true
            }
        },
        {
            "label": "Tên mẫu",
            "key": "TENMAU",
            "type": "textarea",
            validate: {
                required: true
            },
            others: {
                "labelPosition": "left-left",
                "labelWidth": 10
            }
        },
        {
            "label": "Lý do vào viện",
            "key": "lyDoVaoVien",
            "type": "textfield",
            others: {
                "labelPosition": "left-left",
                "labelWidth": 10
            }
        },
        {
            "label": "Qu<PERSON> trình bệnh lý",
            "key": "benhSu",
            "type": "textfield",
            others: {
                "labelPosition": "left-left",
                "labelWidth": 10
            }
        },
        {
            "label": "Tiền sử bệnh (bản thân)",
            "key": "tienSuBanThan",
            "type": "textfield",
            others: {
                "labelPosition": "left-left",
                "labelWidth": 10
            }
        },
        {
            "label": "Tiền sử Gia đình",
            "key": "tienSuGiaDinh",
            "type": "textfield",
            others: {
                "labelPosition": "left-left",
                "labelWidth": 10
            }
        },
        {
            "label": "Toàn thân",
            "customClass": "pr-2",
            "key": "khamToanThan",
            "type": "textarea",
            rows: 2,
            others: {

                "labelPosition": "left-left",
                "labelWidth": 10,
            },
        },
        {
            "label": "Bệnh chuyên khoa",
            "customClass": "pr-2",
            "key": "benhChuyenKhoa",
            "type": "textarea",
            rows: 2,
            others: {

                "labelPosition": "left-left",
                "labelWidth": 10,
            },
        },
        {
            "label": "Thần kinh",
            "customClass": "pr-2",
            "key": "thanKinh",
            "type": "textarea",
            rows: 2,
            others: {

                "labelPosition": "left-left",
                "labelWidth": 10,
            },
        },
        {
            "label": "Tuần hoàn",
            "customClass": "pr-2",
            "key": "tuanHoan",
            "type": "textfield",
            others: {

                "labelPosition": "left-left",
                "labelWidth": 10,
            },
        },
        {
            "label": "Hô hấp",
            "customClass": "pr-2",
            "key": "hoHap",
            "type": "textfield",
            others: {

                "labelPosition": "left-left",
                "labelWidth": 10,
            },
        },
        {
            "label": "Tiêu hóa",
            "customClass": "pr-2",
            "key": "tieuHoa",
            "type": "textfield",
            others: {

                "labelPosition": "left-left",
                "labelWidth": 10,
            },
        },
        {
            "label": "Da và mô dưới da",
            "customClass": "pr-2",
            "key": "daMoDuoiDa",
            "type": "textfield",
            others: {

                "labelPosition": "left-left",
                "labelWidth": 10,
            },
        },
        {
            "label": "Thận - Tiết niệu - Sinh dục",
            "customClass": "pr-2",
            "key": "tietNieuSinhDuc",
            "type": "textfield",
            others: {

                "labelPosition": "left-left",
                "labelWidth": 10,
            },
        },
        {
            "label": "Cơ - Xương - Khớp",
            "customClass": "pr-2",
            "key": "coXuongKhop",
            "type": "textfield",
            others: {

                "labelPosition": "left-left",
                "labelWidth": 10,
            },
        },
        {
            "label": "Tai - Mũi - Họng",
            "customClass": "pr-2",
            "key": "taiMuiHong",
            "type": "textfield",
            others: {

                "labelPosition": "left-left",
                "labelWidth": 10,
            },
        },
        {
            "label": "Răng - Hàm - Mặt",
            "customClass": "pr-2",
            "key": "rangHamMat",
            "type": "textfield",
            others: {

                "labelPosition": "left-left",
                "labelWidth": 10,
            },
        },
        {
            "label": "Mắt",
            "customClass": "pr-2",
            "key": "MAT",
            "type": "textfield",
            others: {

                "labelPosition": "left-left",
                "labelWidth": 10,
            },
        },
        {
            "label": "Khác",
            "customClass": "pr-2",
            "key": "khac",
            "type": "textfield",
            others: {

                "labelPosition": "left-left",
                "labelWidth": 10,
            },
        },
        {
            "label": "Nội tiết, dinh dưỡng và các bệnh lý khác",
            "customClass": "pr-2",
            "key": "noiTiet",
            "type": "textfield",
            others: {

                "labelPosition": "left-left",
                "labelWidth": 10,
            },
        },
        {
            "label": "Các xét nghiệm cận lâm sàng cần làm",
            "customClass": "pr-2",
            "key": "cls",
            "type": "textfield",
            others: {

                "labelPosition": "left-left",
                "labelWidth": 10,
            },
        },
        {
            "label": "Tóm tắt bệnh án",
            "customClass": "pr-2",
            "key": "tomTatBA",
            "type": "textarea",
            rows: 2,
            others: {

                "labelPosition": "left-left",
                "labelWidth": 10,
            },
        },
        {
            "label": "Phân biệt",
            "customClass": "pr-2",
            "key": "phanBiet",
            "type": "textfield",
            others: {

                "labelPosition": "left-left",
                "labelWidth": 10,
            },
        },
        {
            "label": "Tiên lượng",
            "customClass": "pr-2",
            "key": "tienLuong",
            "type": "textfield",
            others: {

                "labelPosition": "left-left",
                "labelWidth": 10,
            },
        },
        {
            "label": "Hướng điều trị",
            "customClass": "pr-2",
            "key": "huongDieuTri",
            "type": "textfield",
            others: {

                "labelPosition": "left-left",
                "labelWidth": 10,
            },
        }
    ]
}

function getObjectMauTongketUPPERCASE() {
    return [
        {
            "label": "ID",
            "key": "ID",
            "type": "textfield",
            others: {
                hidden: true
            }
        },
        {
            "label": "Tên mẫu",
            "key": "TENMAU",
            "type": "textarea",
            validate: {
                required: true
            },
            others: {
                "labelPosition": "left-left",
                "labelWidth": 10
            }
        },
        {
            "label": "Quá trình bệnh lý và diễn biến lâm sàng",
            "key": "QUATRINH_BENHLY",
            "type": "textfield",
            others: {
                "labelPosition": "left-left",
                "labelWidth": 10
            }
        },
        {
            "label": "Phương pháp điều trị",
            "key": "PHUONGPHAP_DIEUTRI",
            "type": "textfield",
            others: {
                "labelPosition": "left-left",
                "labelWidth": 10
            }
        },
        {
            "label": "Tình trạng người bệnh ra viện",
            "key": "TINHTRANG_RAVIEN",
            "type": "textfield",
            others: {
                "labelPosition": "left-left",
                "labelWidth": 10
            }
        },
        {
            "label": "Hướng điều trị và các chế độ tiếp theo",
            "key": "HUONG_DIEUTRI",
            "type": "textfield",
            others: {
                "labelPosition": "left-left",
                "labelWidth": 10
            }
        },
    ]
}

function getObjectMauHSBAPAGE2PHCN_NHI() {
    return [
        {
            "label": "ID",
            "key": "ID",
            "type": "textfield",
            others: {
                hidden: true
            }
        },
        {
            "label": "Tên mẫu",
            "key": "TENMAU",
            "type": "textarea",
            validate: {
                required: true
            },
            others: {
                "labelPosition": "left-left",
                "labelWidth": 10
            }
        },
        {
            "label": "Lý do vào viện",
            "key": "LYDO_VAOVIEN",
            "type": "textfield",
            others: {
                "labelPosition": "left-left",
                "labelWidth": 10
            }
        },
        {
            "label": "Quá trình bệnh lý",
            "key": "QUATRINH_BENHLY",
            "type": "textfield",
            others: {
                "labelPosition": "left-left",
                "labelWidth": 10
            }
        },
        {
            "label": "Tuổi khi sinh",
            "validate": {
                "min": 0,
            },
            "key": "NHI_TUOIME_KHISINH",
            "type": "number",
            others: {
                "labelPosition": "left-left",
                "labelWidth": 10
            }
        },
        {
            "label": "Tình trạng sức khỏe khi mang thai",
            "key": "NHI_TTME_KHIMANGTHAI",
            "type": "textfield",
            others: {
                "labelPosition": "left-left",
                "labelWidth": 10
            }
        },
        {
            "label": "Con thứ",
            "validate": {
                "min": 0,
            },
            "key": "NHI_CONTHU",
            "type": "number",
            others: {
                "labelPosition": "left-left",
                "labelWidth": 10
            }
        },
        {
            "label": "Tuổi thai(tuần)",
            "validate": {
                "min": 0,
            },
            "key": "NHI_TUOI_THAI",
            "type": "number",
            others: {
                "labelPosition": "left-left",
                "labelWidth": 10
            }
        },
        {
            "label": "Cân nặng khi sinh (gam)",
            "validate": {
                "min": 0,
            },
            "key": "NHI_CANNANGCON_KHISINH",
            "type": "number",
            others: {
                "labelPosition": "left-left",
                "labelWidth": 10
            }
        },
        {
            "label": "Tình trạng sau sinh",
            "key": "NHI_TINHTRANG_SAUSINH",
            "type": "textfield",
            others: {
                "labelPosition": "left-left",
                "labelWidth": 10
            }
        },
        {
            "label": "Tiêm phòng vacxin",
            "customClass": "pr-2",
            "key": "NHI_TIEMPHONG_VACXIN",
            "type": "textfield",
            others: {
                "labelPosition": "left-left",
                "labelWidth": 10
            }
        },
        {
            "label": "Số con trong gia đình",
            "validate": {
                "min": 0,
            },
            "key": "NHI_SOCONTRONG_GIADINH",
            "type": "number",
            others: {
                "labelPosition": "left-left",
                "labelWidth": 10
            }
        },
        {
            "label": "Số trẻ có bất thường",
            "key": "NHI_SOTRE_BATTHUONG",
            "type": "number",
            others: {
                "labelPosition": "left-left",
                "labelWidth": 10
            }
        },
        {
            "label": "Gia đình có người nhiễm chất độc da cam",
            "key": "NHI_GD_CONGUOI_NHIEM_CDMDC",
            "type": "number",
            others: {
                "labelPosition": "left-left",
                "labelWidth": 10
            }
        },
        {
            "label": "Thể trạng chung",
            others: {
                "tooltip": " Thể trạng chung: (ý thức, da niêm mạc, hệ thống hạch, tuyến giáp)",
            },
            "key": "NHI_TINHTRANG_CHUNG",
            "type": "textarea",
            "rows": 2,
            others: {

                "labelPosition": "left-left",
                "labelWidth": 10,
            },
        },
        {
            "label": "Tâm thần, thần kinh",
            others: {
                "tooltip": "Tri giác, vận động, cảm giác; phản xạ bệnh lý, phản xạ gân xương, da, trương lực cơ; thần kinh sọ não; thăng bằng, điều hợp; hội chứng tiểu não, ngoại tháp,các hội chứng khác,...",
            },
            "key": "CQ_TAMTHAN_THANKINH",
            "type": "textarea",
            "rows": 2,
            others: {

                "labelPosition": "left-left",
                "labelWidth": 10,
            },
        },
        {
            "label": "Hệ cơ xương khớp, cột sống",
            others: {
                "tooltip": "Tình trạng cơ, xương, khớp cột sống: hình thể, chức năng; tầm vận động của khớp...",
            },
            "key": "CQ_CXK_COTSONG",
            "type": "textarea",
            "rows": 2,
            others: {

                "labelPosition": "left-left",
                "labelWidth": 10,
            },
        },
        {
            "label": "Các chuyên khoa khác",
            others: {
                "tooltip": "(tim mạch, hô hấp, tiêu hóa, tiết niệu, sinh dục, mắt, tai, mũi, họng, răng, thóp,...",
            },
            "key": "CQ_CHUYENKHOA_KHAC",
            "type": "textarea",
            "rows": 2,
            others: {

                "labelPosition": "left-left",
                "labelWidth": 10,
            },
        }
    ]
}

function getObjectMauHSBAPAGE2PHCN() {
    return [
        {
            "label": "ID",
            "key": "ID",
            "type": "textfield",
            others: {
                hidden: true
            }
        },
        {
            "label": "Tên mẫu",
            "key": "TENMAU",
            "type": "textarea",
            validate: {
                required: true
            },
            others: {
                "labelPosition": "left-left",
                "labelWidth": 10
            }
        },
        {
            "label": "Lý do vào viện",
            "key": "LYDOVAOVIEN",
            "type": "textfield",
            others: {
                "labelPosition": "left-left",
                "labelWidth": 10
            }
        },
        {
            "label": "Quá trình bệnh lý",
            "key": "BENHSU",
            "type": "textfield",
            others: {
                "labelPosition": "left-left",
                "labelWidth": 10
            }
        },
        {
            "label": "Tiền sử dị ứng",
            "key": "DIUNG",
            "type": "textarea",
            others: {
                "labelPosition": "left-left",
                "labelWidth": 10
            }
        },
        {
            "label": "Tiền sử bản thân",
            "key": "DIUNG",
            "type": "textarea",
            others: {
                "labelPosition": "left-left",
                "labelWidth": 10
            }
        },
        {
            "label": "Tiền sử gia đình",
            "key": "DIUNG",
            "type": "textarea",
            others: {
                "labelPosition": "left-left",
                "labelWidth": 10
            }
        },
        {
            "label": "Toàn thân",
            "key": "KHAMTOANTHAN",
            "type": "textarea",
            others: {
                "tooltip": "Ý thức, da niêm mạc, hệ thống hạch, tuyến giáp, vị trí, kích thước",
                "labelPosition": "left-left",
                "labelWidth": 10
            }
        },
        {
            "label": "Tình trạng đau",
            "key": "TINHTRANGDAU",
            "type": "textarea",
            others: {
                "tooltip": "Mô tả vị trí, tính chất, mức độ,...",
                "labelPosition": "left-left",
                "labelWidth": 10
            }
        },
        {
            "label": "Tâm thần, thần kinh",
            "key": "THANKINH",
            "type": "textarea",
            others: {
                "tooltip": "Tri giác; vận động; cảm giác; phản xạ gân xương, phản xạ da, phản xạ bệnh lý; trương lực cơ; thần kinh sọ não; thăng bằng, điều hợp; hội chứng tiểu não; hội chứng ngoại tháp; các hội chứng tâm thần, thần kinh khác,...",
                "labelPosition": "left-left",
                "labelWidth": 10
            }
        },

        {
            "label": "Cơ xương khớp, cột sống",
            "key": "COXUONGKHOP",
            "type": "textarea",
            others: {
                "tooltip": "hình thể, chức năng; tầm vận động của khớp; thử cơ bằng tay...",
                "labelPosition": "left-left",
                "labelWidth": 10,
            },
        },
        {
            "label": "Các chuyên khoa khác",
            "key": "CHUYENKHOAKHAC",
            "type": "textarea",
            others: {
                "tooltip": "Tim mạch, hô hấp, tiêu hóa, nội tiết, tiết niệu, sinh dục,...",
                "labelPosition": "left-left",
                "labelWidth": 10,
            },
        },
        {
            "label": "Tóm tắt bệnh án",
            "key": "TOMTATBA",
            "type": "textarea",
            others: {
                "labelPosition": "left-left",
                "labelWidth": 10,
            },
        },
        {
            "label": "Những khó khăn chính trong hoạt động chức năng của người bệnh",
            "key": "KHOKHAN",
            "type": "textarea",
            others: {
                "labelPosition": "left-left",
                "labelWidth": 10,
            },
        },
        {
            "label": "Mục tiêu điều trị PHCN",
            "key": "MUCTIEU",
            "type": "textarea",
            others: {
                "tooltip": "Mục tiêu cụ thể, đo lường được, thực tế, có thể đạt được và có thời gian hoàn thành",
                "labelPosition": "left-left",
                "labelWidth": 10,
            },
        },
        {
            "label": "Chương trình can thiệp PHCN",
            "key": "CHUONGTRINH",
            "type": "textarea",
            others: {
                "tooltip": "Những bài tập, kỹ thuật, điều trị về vật lý trị liệu, hoạt động trị liệu, ngôn ngữ trị liệu, tâm lý trị liệu, dụng cụ PHCN …nhằm đạt được các mục tiêu điều trị đề ra",
                "labelPosition": "left-left",
                "labelWidth": 10,
            },
        },
        {
            "label": "Điều trị các bệnh lý kèm theo và chế độ chăm sóc người bệnh",
            "key": "DIEUTRI",
            "type": "textarea",
            others: {
                "tooltip": "Cải tạo môi trường tiếp cận với trẻ khuyết tật: nhà ở và khả năng thích nghi môi trường sống, các hoạt động hướng nghiệp",
                "labelPosition": "left-left",
                "labelWidth": 10,
            },
        },
    ]
}

function getObjectMauTongketPHCN_NHI() {
    return [
        {
            "label": "ID",
            "key": "ID",
            "type": "textfield",
            others: {
                hidden: true
            }
        },
        {
            "label": "Tên mẫu",
            "key": "TENMAU",
            "type": "textarea",
            validate: {
                required: true
            },
            others: {
                "labelPosition": "left-left",
                "labelWidth": 10
            }
        },
        {
            "label": "Quá trình bệnh lý và diễn biến lâm sàng",
            "key": "quaTrinhBenhLy",
            "type": "textfield",
            others: {
                "labelPosition": "left-left",
                "labelWidth": 10
            }
        },
        {
            "label": "Tóm tắt kết quả xét nghiệm cận lâm sàng có giá trị chẩn đoán",
            "key": "tomTatKetQuaXNCLS",
            "type": "textarea",
            others: {
                "labelPosition": "left-left",
                "labelWidth": 10
            }
        },
        {
            "label": "Phương pháp điều trị",
            "key": "phuongPhapDieuTri",
            "type": "textfield",
            others: {
                "labelPosition": "left-left",
                "labelWidth": 10
            }
        },
        {
            "label": "Vận động và di chuyển",
            "key": "phcnVanDongDiChuyen",
            "type": "textarea",
            others: {
                "labelPosition": "left-left",
                "labelWidth": 10
            }
        },
        {
            "label": "Chức năng sinh hoạt hàng ngày",
            "key": "phcnChucNangSinhHoatHangNgay",
            "type": "textarea",
            others: {
                "labelPosition": "left-left",
                "labelWidth": 10
            }
        },
        {
            "label": "Nhận thức",
            "key": "phcnNhanThuc",
            "type": "textarea",
            others: {
                "labelPosition": "left-left",
                "labelWidth": 10
            }
        },
        {
            "label": "Các chức năng khác",
            "key": "phcnCacChucNangKhac",
            "type": "textarea",
            others: {
                "labelPosition": "left-left",
                "labelWidth": 10
            }
        },
        {
            "label": "Sự tham gia các hoạt động trong gia đình và xã hội",
            "key": "phcnThamGiaHoatDongGiaDinhXaHoi",
            "type": "textarea",
            others: {
                "labelPosition": "left-left",
                "labelWidth": 10
            }
        },
        {
            "label": "Yếu tố môi trường và cá nhân",
            "key": "phcnYeuToMoiTruongCaNhan",
            "type": "textarea",
            others: {
                "labelPosition": "left-left",
                "labelWidth": 10
            }
        },
        {
            "label": "Hướng điều trị và các chế độ tiếp theo",
            "key": "huongDieuTriVaCacCheDo",
            "type": "textfield",
            others: {
                "labelPosition": "left-left",
                "labelWidth": 10
            }
        },
    ]
}

function getObjectMauHSBAPAGE2_NTC() {
    return [
        {
            "label": "ID",
            "key": "ID",
            "type": "textfield",
            others: {
                hidden: true
            }
        },
        {
            "label": "Tên mẫu",
            "key": "TENMAU",
            "type": "textarea",
            validate: {
                required: true
            },
            others: {
                "labelPosition": "left-left",
                "labelWidth": 10
            }
        },
        {
            "label": "Lý do vào viện",
            "key": "LYDOVAOVIEN",
            "type": "textfield",
            others: {
                "labelPosition": "left-left",
                "labelWidth": 10
            }
        },
        {
            "label": "Quá trình bệnh lý",
            "key": "BENHSU",
            "type": "textfield",
            others: {
                "labelPosition": "left-left",
                "labelWidth": 10
            }
        },
        {
            "label": "Tiền sử bệnh (bản thân)",
            "key": "TIENSUBANTHAN",
            "type": "textfield",
            others: {
                "labelPosition": "left-left",
                "labelWidth": 10
            }
        },
        {
            "label": "Tiền sử Gia đình",
            "key": "TIENSUGIADINH",
            "type": "textfield",
            others: {
                "labelPosition": "left-left",
                "labelWidth": 10
            }
        },
        {
            "label": "Toàn thân",
            "customClass": "pr-2",
            "key": "KHAMTOANTHAN",
            "type": "textarea",
            rows: 2,
            others: {
                "labelPosition": "left-left",
                "labelWidth": 10,
            },
        },
        {
            "label": "Các bộ phận",
            "key": "CACBOPHAN",
            "type": "textarea",
            others: {
                "labelPosition": "left-left",
                "labelWidth": 10,
            },
        },
        {
            "label": "Tóm tắt kết quả cận lâm sàng",
            "key": "TOMTATKETQUACLS",
            "type": "textarea",
            others: {
                "labelPosition": "left-left",
                "labelWidth": 10,
            },
        },
        {
            "label": "Chẩn đoán ban đầu",
            "key": "CHANDOAN_BANDAU",
            "type": "textarea",
            others: {
                "labelPosition": "left-left",
                "labelWidth": 10,
            },
        },
        {
            "label": "Đã xử lý(thuốc, chăm sóc)",
            "key": "DAXULY",
            "type": "textarea",
            others: {
                "labelPosition": "left-left",
                "labelWidth": 10,
            },
        },
    ]
}

function getObjectMauHSBAMATBANPHANTRUOCPAGE2() {
    return [
        {
            "label": "ID",
            "key": "ID",
            "type": "textfield",
            others: {
                hidden: true
            }
        },
        {
            "label": "Tên mẫu",
            "key": "TENMAU",
            "type": "textarea",
            validate: {
                required: true
            },
            others: {
                "labelPosition": "left-left",
                "labelWidth": 10
            }
        },
        {
            "label": "Lý do vào viện",
            "key": "LYDOVAOVIEN",
            "type": "textfield",
            others: {
                "labelPosition": "left-left",
                "labelWidth": 10,
            }
        },
        {
            "label": "Thời gian xuất hiện bệnh",
            "key": "THOIGIANXUATHIENBENH",
            "type": "textfield",
            others: {
                "labelPosition": "left-left",
                "labelWidth": 10,
            }
        },
        {
            "label": "Nguyên nhân",
            "key": "NGUYENNHAN",
            "type": "textfield",
            others: {
                "labelPosition": "left-left",
                "labelWidth": 10,
            }
        },
        {
            "label": "Các phương pháp đã điều trị và diễn biến bệnh",
            "key": "CACPHUONGPHAPDADIEUTRI",
            "type": "textfield",
            others: {
                "labelPosition": "left-left",
                "labelWidth": 10,
            }
        },
        {
            "label": "",
            "content": "<center><b>MẮT PHẢI</b></center>",
            "refreshOnChange": false,
            "key": "MATPHAITEXT",
            "type": "htmlelement",
            "input": false,
            "tableView": false,
        },
        {
            "label": "<b>1. Thị lực</b> không kính",
            "key": "THILUCKHONGKINHMP",
            "type": "textfield",
            others: {
                "labelPosition": "left-left",
                "labelWidth": 10,
            }
        },
        {
            "label": "Qua lỗ",
            "key": "THILUCQUALOMP",
            "type": "textfield",
            others: {
                "labelPosition": "left-left",
                "labelWidth": 10,
            }
        },
        {
            "label": "Có chỉnh kính",
            "key": "THILUCCOCHINHKINHMP",
            "type": "textfield",
            others: {
                "labelPosition": "left-left",
                "labelWidth": 10,
            }
        },
        {
            "label": "Nhìn gần",
            "key": "THILUCNHINGANMP",
            "type": "textfield",
            others: {
                "labelPosition": "left-left",
                "labelWidth": 10,
            }
        },
        {
            "label": "<b>2. Nhãn áp (mmHg)</b>",
            "key": "NHANAPMP",
            "type": "textfield",
            others: {
                "labelPosition": "left-left",
                "labelWidth": 10,
            }
        },
        {
            "label": "<b>3. Lác và vận nhãn</b>",
            "key": "LACVANNHANMP",
            "type": "textfield",
            others: {
                "labelPosition": "left-left",
                "labelWidth": 10,
            }
        },
        JSONRadio("<b>4. Lệ đạo</b> Bơm lệ quản: Nước thoát tốt", "LEDAONUOCTHOATTOTMP", "", {
            others: {
                "optionsLabelPosition": "right",
                "inline": true,
                "labelPosition": "left-left",
                "optionsLabelPosition": "right",
                "tableView": false,
                "values": [
                    {
                        "label": "Không",
                        "value": "0",
                    },
                    {
                        "label": "Có",
                        "value": "1",
                    },
                ],
            }
        }),
        {
            "label": "Chi tiết",
            "key": "BOMLEQUAN_MP",
            "type": "textarea",
            customClass: "pr-2",
            others: {
                "labelPosition": "left-left",
                "labelWidth": 10,
                "customConditional": "show = data.LEDAONUOCTHOATTOTMP == 1;",
            }
        },
        JSONRadio("Trào lệ quản đối diên", "LEDAOTRAOLEQUANMP", "", {
            others: {
                "optionsLabelPosition": "right",
                "inline": true,
                "labelPosition": "left-left",
                "optionsLabelPosition": "right",
                "tableView": false,
                "values": [
                    {
                        "label": "Không",
                        "value": "0",
                    },
                    {
                        "label": "Có",
                        "value": "1",
                    },
                ],
            }
        }),
        {
            "label": "Chi tiết",
            "key": "TRAOLEQUANDOIDIEN_MP",
            "type": "textarea",
            others: {
                "labelPosition": "left-left",
                "labelWidth": 10,
                "customConditional": "show = data.LEDAOTRAOLEQUANMP == 1;",
            }
        },
        JSONRadio("Trào tại chỗ", "LEDAOTRAOTAICHOMP", "", {
            others: {
                "optionsLabelPosition": "right",
                "inline": true,
                "labelPosition": "left-left",
                "optionsLabelPosition": "right",
                "tableView": false,
                "values": [
                    {
                        "label": "Không",
                        "value": "0",
                    },
                    {
                        "label": "Có",
                        "value": "1",
                    },
                ],
            }
        }),
        {
            "label": "Chi tiết",
            "key": "TRAOTAICHO_MP",
            "type": "textarea",
            others: {
                "labelPosition": "left-left",
                "labelWidth": 10,
                "customConditional": "show = data.LEDAOTRAOTAICHOMP == 1;",
            }
        },
        JSONRadio("<b>5. Mi mắt</b>", "MIMATTINHTRANGMP", "", {
            others: {
                "optionsLabelPosition": "right",
                "inline": true,
                "labelPosition": "left-left",
                "optionsLabelPosition": "right",
                "tableView": false,
                "values": [
                    {
                        "label": "Bình thường",
                        "value": "1",
                        "shortcut": ""
                    },
                    {
                        "label": "Phù",
                        "value": "2",
                        "shortcut": ""
                    },
                    {
                        "label": "Chấp",
                        "value": "3",
                        "shortcut": ""
                    },
                    {
                        "label": "Lẹo",
                        "value": "4",
                        "shortcut": ""
                    },
                    {
                        "label": "Sẹo da mi",
                        "value": "5",
                        "shortcut": ""
                    },
                    {
                        "label": "Sụp mi",
                        "value": "6",
                        "shortcut": ""
                    },
                ],
            }
        }),
        {
            "label": "Khác",
            "key": "MIMATTINHTRANGKHACMP",
            "type": "textfield",
            others: {
                "labelPosition": "left-left",
                "labelWidth": 10,
            }
        },
        JSONRadio("U mi", "MIMATUMIMP", "", {
            others: {
                "optionsLabelPosition": "right",
                "inline": true,
                "labelPosition": "left-left",
                "optionsLabelPosition": "right",
                "tableView": false,
                "values": [
                    {
                        "label": "Không",
                        "value": "0",
                    },
                    {
                        "label": "Có",
                        "value": "1",
                    },
                ],
            }
        }),
        {
            "label": "Tính chất u",
            "key": "MIMATTINHCHATUMP",
            "type": "textarea",
            others: {
                "labelPosition": "left-left",
                "labelWidth": 10,
                "customConditional": "show = data.MIMATUMIMP == 1;",
            }
        },
        {
            "label": "Vị trí",
            "key": "MIMATVITRIMP",
            "type": "textfield",
            others: {
                "labelPosition": "left-left",
                "labelWidth": 10,
            }
        },
        {
            "label": "Kích thước",
            "key": "MIMATKICHTHUOCMP",
            "type": "textfield",
            others: {
                "labelPosition": "left-left",
                "labelWidth": 10,
            }
        },
        JSONRadio("Quặm", "MIMATQUAMMP", "", {
            others: {
                "optionsLabelPosition": "right",
                "inline": true,
                "labelPosition": "left-left",
                "optionsLabelPosition": "right",
                "tableView": false,
                "values": [
                    {
                        "label": "Có",
                        "value": "1",
                    },
                    {
                        "label": "Không",
                        "value": "2",
                    },
                ],
            }
        }),
        JSONRadio("Mi trên", "MIMATQUAMMITRENMP", "", {
            others: {
                "optionsLabelPosition": "right",
                "inline": true,
                "labelPosition": "left-left",
                "optionsLabelPosition": "right",
                "tableView": false,
                "values": [
                    {
                        "label": "1/3 trong",
                        "value": "1",
                    },
                    {
                        "label": "1/3 giữa",
                        "value": "2",
                    },
                    {
                        "label": "1/3 ngoài",
                        "value": "3",
                    },
                    {
                        "label": "Toàn bộ",
                        "value": "4",
                    },
                ],
            }
        }),
        JSONRadio("Mi dưới", "MIMATQUAMMIDUOIMP", "", {
            others: {
                "optionsLabelPosition": "right",
                "inline": true,
                "labelPosition": "left-left",
                "optionsLabelPosition": "right",
                "tableView": false,
                "values": [
                    {
                        "label": "1/3 trong",
                        "value": "1",
                    },
                    {
                        "label": "1/3 giữa",
                        "value": "2",
                    },
                    {
                        "label": "1/3 ngoài",
                        "value": "3",
                    },
                    {
                        "label": "Toàn bộ",
                        "value": "4",
                    },
                ],
            }
        }),
        JSONRadio("Hở mi", "HOMIMP", "", {
            others: {
                "optionsLabelPosition": "right",
                "inline": true,
                "labelPosition": "left-left",
                "optionsLabelPosition": "right",
                "tableView": false,
                "values": [
                    {
                        "label": "Không",
                        "value": "0",
                    },
                    {
                        "label": "Có",
                        "value": "1",
                    },
                ],
            }
        }),
        JSONRadio("Trễ mi", "TREMIMP", "", {
            others: {
                "optionsLabelPosition": "right",
                "inline": true,
                "labelPosition": "left-left",
                "optionsLabelPosition": "right",
                "tableView": false,
                "values": [
                    {
                        "label": "Không",
                        "value": "0",
                    },
                    {
                        "label": "Có",
                        "value": "1",
                    },
                ],
            }
        }),
        JSONRadio("Khuyết mi", "MIMATKHUYETMIMP", "", {
            others: {
                "optionsLabelPosition": "right",
                "inline": true,
                "labelPosition": "left-left",
                "optionsLabelPosition": "right",
                "tableView": false,
                "values": [
                    {
                        "label": "1/3 trong",
                        "value": "1",
                    },
                    {
                        "label": "1/3 giữa",
                        "value": "2",
                    },
                    {
                        "label": "1/3 ngoài",
                        "value": "3",
                    },
                ],
            }
        }),
        JSONRadio("Tuyến bờ mi", "MIMATTUYENBOMIMP", "", {
            others: {
                "optionsLabelPosition": "right",
                "inline": true,
                "labelPosition": "left-left",
                "optionsLabelPosition": "right",
                "tableView": false,
                "values": [
                    {
                        "label": "Bình thường",
                        "value": "1",
                    },
                    {
                        "label": "Viêm tắc nhẹ",
                        "value": "2",
                    },
                    {
                        "label": "Vừa",
                        "value": "3",
                    },
                    {
                        "label": "Nặng",
                        "value": "4",
                    },
                ],
            }
        }),
        JSONRadio("Viêm bờ mi: (chân lông mi)", "MIMATVIEMBOMIMP", "", {
            others: {
                "optionsLabelPosition": "right",
                "inline": true,
                "labelPosition": "left-left",
                "optionsLabelPosition": "right",
                "tableView": false,
                "values": [
                    {
                        "label": "Không",
                        "value": "0",
                    },
                    {
                        "label": "Có",
                        "value": "1",
                    },
                ],
            }
        }),
        {
            "label": "<b>6. Tình trạng nhãn cầu</b>: Tổn thương khác",
            "key": "MIMATTONTHUONGKHACMP",
            "type": "textfield",
            others: {
                "labelPosition": "left-left",
                "labelWidth": 10,
            },
        },
        JSONRadio("<b>7. Kết mạc</b>", "KETMAC_MATPHAI", "", {
            others: {
                "optionsLabelPosition": "right",
                "inline": true,
                "labelPosition": "left-left",
                "optionsLabelPosition": "right",
                "tableView": false,
                "values": [
                    {
                        "label": "Bình thường",
                        "value": "1",
                    },
                    {
                        "label": "Mộng",
                        "value": "2",
                    },
                ],
            }
        }),
        JSONRadio("Cương tụ", "KETMACCUONGTUMP", "", {
            others: {
                "optionsLabelPosition": "right",
                "inline": true,
                "labelPosition": "left-left",
                "optionsLabelPosition": "right",
                "tableView": false,
                "values": [
                    {
                        "label": "Tỏa lan",
                        "value": "1",
                    },
                    {
                        "label": "Rìa",
                        "value": "2",
                    },
                    {
                        "label": "Ở KM nhãn cầu",
                        "value": "3",
                    },
                    {
                        "label": "Ở rìa",
                        "value": "4",
                    },
                    {
                        "label": "Toàn bộ",
                        "value": "5",
                    },
                ],
            }
        }),
        JSONRadio("Phù nề", "KETMACPHUNEMP", "", {
            others: {
                "optionsLabelPosition": "right",
                "inline": true,
                "labelPosition": "left-left",
                "optionsLabelPosition": "right",
                "tableView": false,
                "values": [
                    {
                        "label": "Phù nề",
                        "value": "1",
                    },
                    {
                        "label": "Xuất huyết",
                        "value": "2",
                    },
                    {
                        "label": "Sừng hóa",
                        "value": "3",
                    },
                ],
            }
        }),
        JSONRadio("Nhú", "KETMACNHUMP", "", {
            others: {
                "optionsLabelPosition": "right",
                "inline": true,
                "labelPosition": "left-left",
                "optionsLabelPosition": "right",
                "tableView": false,
                "values": [
                    {
                        "label": "Nhú",
                        "value": "1",
                    },
                    {
                        "label": "Hột",
                        "value": "2",
                    },
                    {
                        "label": "Sẹo",
                        "value": "3",
                    },
                ],
            }
        }),
        JSONRadio("Tiết tố mủ", "KETMACTIETTOMUMP", "", {
            others: {
                "optionsLabelPosition": "right",
                "inline": true,
                "labelPosition": "left-left",
                "optionsLabelPosition": "right",
                "tableView": false,
                "values": [
                    {
                        "label": "Tiết tố mủ",
                        "value": "1",
                    },
                    {
                        "label": "Tiết tố trong",
                        "value": "2",
                    },
                    {
                        "label": "Giả mạc",
                        "value": "3",
                    },
                ],
            }
        }),
        JSONRadio("Bắt màu fluo", "KETMACFLUORMP", "", {
            others: {
                "optionsLabelPosition": "right",
                "inline": true,
                "labelPosition": "left-left",
                "optionsLabelPosition": "right",
                "tableView": false,
                "values": [
                    {
                        "label": "Không",
                        "value": "0",
                    },
                    {
                        "label": "Có",
                        "value": "1",
                    },
                ],
            }
        }),
        {
            "label": "U kết mạc (Tính chất)",
            "key": "KETMACUKETMACTINHCHATMP",
            "type": "textfield",
            others: {
                "labelPosition": "left-left",
                "labelWidth": 10,
            },
        },
        {
            "label": "U kết mạc (Vị trí)",
            "key": "KETMACUKETMACVITRIMP",
            "type": "textfield",
            others: {
                "labelPosition": "left-left",
                "labelWidth": 10,
            },
        },
        {
            "label": "U kết mạc (Kích thước)",
            "key": "KETMACUKETMACKICHTHUOCMP",
            "type": "textfield",
            others: {
                "labelPosition": "left-left",
                "labelWidth": 10,
            },
        },
        JSONRadio("Cùng đồ", "KETMACCUNGDOMP", "", {
            others: {
                "optionsLabelPosition": "right",
                "inline": true,
                "labelPosition": "left-left",
                "optionsLabelPosition": "right",
                "tableView": false,
                "values": [
                    {
                        "label": "Bình thường",
                        "value": "1",
                    },
                    {
                        "label": "Cạn",
                        "value": "2",
                    },
                    {
                        "label": "Dính",
                        "value": "3",
                    },
                ],
            }
        }),
        JSONRadio("Chiều cao cầu dính", "KETMACCHIEUCAOCUACAUDINHMP", "", {
            others: {
                "optionsLabelPosition": "right",
                "inline": true,
                "labelPosition": "left-left",
                "optionsLabelPosition": "right",
                "tableView": false,
                "values": [
                    {
                        "label": "Dính ở cùng đồ chưa vào sụn",
                        "value": "1",
                    },
                    {
                        "label": "Dính cùng đồ, vào sụn nhưng chưa hết chiều cao sụn",
                        "value": "2",
                    },
                    {
                        "label": "Dính hết chiều cao sụn mi",
                        "value": "3",
                    },
                    {
                        "label": "Dính cả bờ mi hoặc điểm lệ",
                        "value": "4",
                    },
                ],
            }
        }),
        JSONRadio("Độ rộng cầu dính", "KETMACDORONGCUACAUDINHMP", "", {
            others: {
                "optionsLabelPosition": "right",
                "inline": true,
                "labelPosition": "left-left",
                "optionsLabelPosition": "right",
                "tableView": false,
                "values": [
                    {
                        "label": "≤ 1/3 chiều dài mi",
                        "value": "1",
                    },
                    {
                        "label": "1/3 - 2/3 chiều dài mi",
                        "value": "2",
                    },
                    {
                        "label": "≥ 2/3 chiều dài mi",
                        "value": "3",
                    },
                ],
            }
        }),
        {
            "label": "Tổn thương khác",
            "key": "KETMACTONTHUONGKHACMP",
            "type": "textfield",
            others: {
                "labelPosition": "left-left",
                "labelWidth": 10,
            },
        },
        JSONRadio("<b>8. Giác mạc</b>: Ghép giác mạc", "GHEPGIACMAC_MP", "", {
            others: {
                "optionsLabelPosition": "right",
                "inline": true,
                "labelPosition": "left-left",
                "optionsLabelPosition": "right",
                "tableView": false,
                "values": [
                    {
                        "label": "Không",
                        "value": "0",
                    },
                    {
                        "label": "Có",
                        "value": "1",
                    },
                ],
            }
        }),
        JSONRadio("Kích thước", "GIACMACKICHTHUOCMP", "", {
            others: {
                "optionsLabelPosition": "right",
                "inline": true,
                "labelPosition": "left-left",
                "optionsLabelPosition": "right",
                "tableView": false,
                "values": [
                    {
                        "label": "Bình thường",
                        "value": "1",
                    },
                    {
                        "label": "To",
                        "value": "2",
                    },
                    {
                        "label": "Nhỏ",
                        "value": "3",
                    },
                ],
            }
        }),
        JSONRadio("Hình dạng", "GIACMACHINHDANGMP", "", {
            others: {
                "optionsLabelPosition": "right",
                "inline": true,
                "labelPosition": "left-left",
                "optionsLabelPosition": "right",
                "tableView": false,
                "values": [
                    {
                        "label": "Bình thường",
                        "value": "1",
                    },
                    {
                        "label": "Nón",
                        "value": "2",
                    },
                    {
                        "label": "Cầu",
                        "value": "3",
                    },
                ],
            }
        }),
        JSONRadio("Biểu mô", "GIACMACTONTHUONGDANCHAMMP", "", {
            others: {
                "optionsLabelPosition": "right",
                "inline": true,
                "labelPosition": "left-left",
                "optionsLabelPosition": "right",
                "tableView": false,
                "values": [
                    {
                        "label": "Bình thường",
                        "value": "1",
                    },
                    {
                        "label": "Tổn thương dạng chấm",
                        "value": "2",
                    },
                ],
            }
        }),
        JSONRadio("Phù bọng biểu mô", "GIACMACPHUBONGBIEUMOMP", "", {
            others: {
                "optionsLabelPosition": "right",
                "inline": true,
                "labelPosition": "left-left",
                "optionsLabelPosition": "right",
                "tableView": false,
                "values": [
                    {
                        "label": "Nhẹ",
                        "value": "1",
                    },
                    {
                        "label": "Vừa",
                        "value": "2",
                    },
                    {
                        "label": "Nặng",
                        "value": "3",
                    },
                ],
            }
        }),
        JSONRadio("Mất biểu mô", "GIACMACMATBIEUMOMP", "", {
            others: {
                "optionsLabelPosition": "right",
                "inline": true,
                "labelPosition": "left-left",
                "optionsLabelPosition": "right",
                "tableView": false,
                "values": [
                    {
                        "label": "< 1/3 d.tích",
                        "value": "1",
                    },
                    {
                        "label": "1/3 - 1/2 d.tích",
                        "value": "2",
                    },
                    {
                        "label": "> 1/2 d.tích",
                        "value": "3",
                    },
                ],
            }
        }),
        JSONRadio("Vị trí", "GIACMACVITRIMATBIEUMOMP", "", {
            others: {
                "optionsLabelPosition": "right",
                "inline": true,
                "labelPosition": "left-left",
                "optionsLabelPosition": "right",
                "tableView": false,
                "values": [
                    {
                        "label": "Trung tâm",
                        "value": "1",
                    },
                    {
                        "label": "Lệch tâm",
                        "value": "2",
                    },
                    {
                        "label": "Sát rìa",
                        "value": "3",
                    },
                ],
            }
        }),
        JSONRadio("Bờ tổn thương", "GIACMACBOTONTHUONGMP", "", {
            others: {
                "optionsLabelPosition": "right",
                "inline": true,
                "labelPosition": "left-left",
                "optionsLabelPosition": "right",
                "tableView": false,
                "values": [
                    {
                        "label": "Nham nhở",
                        "value": "1",
                    },
                    {
                        "label": "Trơn nhẵn",
                        "value": "2",
                    },
                    {
                        "label": "Đào rãnh",
                        "value": "3",
                    },
                ],
            }
        }),
        JSONRadio("Thoái hóa dải băng", "GIACMACTHOAIHOADAIBANGMP", "", {
            others: {
                "optionsLabelPosition": "right",
                "inline": true,
                "labelPosition": "left-left",
                "optionsLabelPosition": "right",
                "tableView": false,
                "values": [
                    {
                        "label": "Không",
                        "value": "0",
                    },
                    {
                        "label": "Có",
                        "value": "1",
                    },
                ],
            }
        }),
        JSONRadio("Lắng động thuốc", "GIACMACLANGDONGTHUOCMP", "", {
            others: {
                "optionsLabelPosition": "right",
                "inline": true,
                "labelPosition": "left-left",
                "optionsLabelPosition": "right",
                "tableView": false,
                "values": [
                    {
                        "label": "Không",
                        "value": "0",
                    },
                    {
                        "label": "Có",
                        "value": "1",
                    },
                ],
            }
        }),
        {
            "label": "Tổn thương khác",
            "key": "GIACMACBIEUMOTONTHUONGKHACMP",
            "type": "textfield",
            others: {
                "labelPosition": "left-left",
                "labelWidth": 10,
            },
        },
        JSONRadio("Nhu mô bình thường", "NHUMO_BTH_MP", "", {
            others: {
                "optionsLabelPosition": "right",
                "inline": true,
                "labelPosition": "left-left",
                "optionsLabelPosition": "right",
                "tableView": false,
                "values": [
                    {
                        "label": "Không",
                        "value": "0",
                    },
                    {
                        "label": "Có",
                        "value": "1",
                    },
                ],
            }
        }),
        JSONRadio("Phù", "GIACMACNHUMOPHUMP", "", {
            others: {
                "optionsLabelPosition": "right",
                "inline": true,
                "labelPosition": "left-left",
                "optionsLabelPosition": "right",
                "tableView": false,
                "values": [
                    {
                        "label": "Nhẹ",
                        "value": "1",
                    },
                    {
                        "label": "Vừa",
                        "value": "2",
                    },
                    {
                        "label": "Nặng",
                        "value": "3",
                    },
                ],
            }
        }),
        JSONRadio("Thẩm lậu", "GIACMACNHUMOTHAMLAUMP", "", {
            others: {
                "optionsLabelPosition": "right",
                "inline": true,
                "labelPosition": "left-left",
                "optionsLabelPosition": "right",
                "tableView": false,
                "values": [
                    {
                        "label": "Nông",
                        "value": "1",
                    },
                    {
                        "label": "Sâu",
                        "value": "2",
                    },
                    {
                        "label": "Rất sâu",
                        "value": "3",
                    },
                ],
            }
        }),
        JSONRadio("Vị trí thẩm lậu", "GIACMACNHUMOVITRITHAMLAUMP", "", {
            others: {
                "optionsLabelPosition": "right",
                "inline": true,
                "labelPosition": "left-left",
                "optionsLabelPosition": "right",
                "tableView": false,
                "values": [
                    {
                        "label": "Khu trú",
                        "value": "1",
                    },
                    {
                        "label": "Lan tỏa",
                        "value": "2",
                    },
                    {
                        "label": "Nhiều ổ vệ tinh",
                        "value": "3",
                    },
                ],
            }
        }),
        JSONRadio("Tiêu mỏng", "GIACMACNHUMOTIEUMONGMP", "", {
            others: {
                "optionsLabelPosition": "right",
                "inline": true,
                "labelPosition": "left-left",
                "optionsLabelPosition": "right",
                "tableView": false,
                "values": [
                    {
                        "label": "< 1/2 c/ dày",
                        "value": "1",
                    },
                    {
                        "label": "> 1/2 c/ dày",
                        "value": "2",
                    },
                ],
            }
        }),
        JSONRadio("Vị trí tiêu mỏng", "GIACMACNHUMOVITRITIEUMONGMP", "", {
            others: {
                "optionsLabelPosition": "right",
                "inline": true,
                "labelPosition": "left-left",
                "optionsLabelPosition": "right",
                "tableView": false,
                "values": [
                    {
                        "label": "Ở rìa",
                        "value": "1",
                    },
                    {
                        "label": "Lệch tâm",
                        "value": "2",
                    },
                    {
                        "label": "Ở trung tâm",
                        "value": "3",
                    },
                ],
            }
        }),
        {
            "label": "Tổn thương khác",
            "key": "GIACMACNHUMOTONTHUONGKHACMP",
            "type": "textfield",
            others: {
                "labelPosition": "left-left",
                "labelWidth": 10,
            },
        },
        JSONRadio("Nội mô & Descemet bình thường", "NOIMO_DESCEMET_BTH_MP", "", {
            others: {
                "optionsLabelPosition": "right",
                "inline": true,
                "labelPosition": "left-left",
                "optionsLabelPosition": "right",
                "tableView": false,
                "values": [
                    {
                        "label": "Không",
                        "value": "0",
                    },
                    {
                        "label": "Bình thường",
                        "value": "1",
                    },
                ],
            }
        }),
        JSONRadio("Tủa sắc tố mặt sau", "GIACMACNOIMOTUASACTOMP", "", {
            others: {
                "optionsLabelPosition": "right",
                "inline": true,
                "labelPosition": "left-left",
                "optionsLabelPosition": "right",
                "tableView": false,
                "values": [
                    {
                        "label": "GM",
                        "value": "1",
                    },
                    {
                        "label": "Mủ mặt sau",
                        "value": "2",
                    },
                ],
            }
        }),
        JSONRadio("Xuất tiết mặt sau", "GIACMACNOIMOXUATTIETMP", "", {
            others: {
                "optionsLabelPosition": "right",
                "inline": true,
                "labelPosition": "left-left",
                "optionsLabelPosition": "right",
                "tableView": false,
                "values": [
                    {
                        "label": "Mặt sau",
                        "value": "1",
                    },
                    {
                        "label": "Guttata",
                        "value": "2",
                    },
                ],
            }
        }),
        JSONRadio("Rạn màng", "GIACMACNOIMODESCEMETMP", "", {
            others: {
                "optionsLabelPosition": "right",
                "inline": true,
                "labelPosition": "left-left",
                "optionsLabelPosition": "right",
                "tableView": false,
                "values": [
                    {
                        "label": "Descemet",
                        "value": "1",
                    },
                    {
                        "label": "Cuộn Descemet",
                        "value": "2",
                    },
                ],
            }
        }),
        {
            "label": "Tổn thương khác",
            "key": "GIACMACNOIMOTONTHUONGKHACMP",
            "type": "textfield",
            others: {
                "labelPosition": "left-left",
                "labelWidth": 10,
            },
        },
        JSONRadio("Dọa thủng", "GIACMACDOATHUNGMP", "", {
            others: {
                "optionsLabelPosition": "right",
                "inline": true,
                "labelPosition": "left-left",
                "optionsLabelPosition": "right",
                "tableView": false,
                "values": [
                    {
                        "label": "Dọa thủng",
                        "value": "1",
                    },
                    {
                        "label": "Kẹt mống mắt",
                        "value": "2",
                    },
                ],
            }
        }),
        JSONRadio("Thủng giác mạc", "GIACMACTHUNGGIACMACMP", "", {
            others: {
                "optionsLabelPosition": "right",
                "inline": true,
                "labelPosition": "left-left",
                "optionsLabelPosition": "right",
                "tableView": false,
                "values": [
                    {
                        "label": "Trung tâm",
                        "value": "1",
                    },
                    {
                        "label": "Lệch tâm",
                        "value": "2",
                    },
                    {
                        "label": "Sát rìa",
                        "value": "3",
                    },
                ],
            }
        }),
        JSONRadio("Tân mạch", "GIACMACTANMACHMP", "", {
            others: {
                "optionsLabelPosition": "right",
                "inline": true,
                "labelPosition": "left-left",
                "optionsLabelPosition": "right",
                "tableView": false,
                "values": [
                    {
                        "label": "Nông, hướng tâm",
                        "value": "1",
                    },
                    {
                        "label": "Ly tâm",
                        "value": "2",
                    },
                    {
                        "label": "Sâu",
                        "value": "3",
                    },
                ],
            }
        }),
        JSONRadio("Mức độ", "GIACMACMUCDOMP", "", {
            others: {
                "optionsLabelPosition": "right",
                "inline": true,
                "labelPosition": "left-left",
                "optionsLabelPosition": "right",
                "tableView": false,
                "values": [
                    {
                        "label": "≤ 1/3 chu vi",
                        "value": "1",
                    },
                    {
                        "label": "1/3 - 2/3 chu vi",
                        "value": "2",
                    },
                    {
                        "label": "≥ 2/3 chu vi",
                        "value": "3",
                    },
                ],
            }
        }),
        {
            "label": "Bất thường khác",
            "key": "GIACMACBATTHUONGKHACMP",
            "type": "textfield",
            others: {
                "labelPosition": "left-left",
                "labelWidth": 10,
            },
        },
        {
            "label": "",
            "content": "<b>9. Củng mạc</b>",
            "refreshOnChange": false,
            "key": "TB_1",
            "type": "htmlelement",
            "input": false,
            "tableView": false,
        },
        // {
        //     "label": "Bình thường",
        //     "key": "CUNGMAC_BTH_MP",
        //     "type": "checkbox",
        //     others: {
        //         "labelPosition": "left-left",
        //         "labelWidth": 10,
        //     },
        // },
        JSONRadio("Viêm", "CUNGMACVIEMMP", "", {
            others: {
                "optionsLabelPosition": "right",
                "inline": true,
                "labelPosition": "left-left",
                "optionsLabelPosition": "right",
                "tableView": false,
                "values": [
                    {
                        "label": "Nốt",
                        "value": "1",
                    },
                    {
                        "label": "Lan tỏa",
                        "value": "2",
                    },
                    {
                        "label": "Áp xe",
                        "value": "3",
                    },
                ],
            }
        }),
        JSONRadio("Độ viêm", "CUNGMACDOVIEMMP", "", {
            others: {
                "optionsLabelPosition": "right",
                "inline": true,
                "labelPosition": "left-left",
                "optionsLabelPosition": "right",
                "tableView": false,
                "values": [
                    {
                        "label": "Nông",
                        "value": "1",
                    },
                    {
                        "label": "Sâu",
                        "value": "2",
                    },
                ],
            }
        }),
        JSONRadio("Giãn lối", "CUNGMACGIANLOIMP", "", {
            others: {
                "optionsLabelPosition": "right",
                "inline": true,
                "labelPosition": "left-left",
                "optionsLabelPosition": "right",
                "tableView": false,
                "values": [
                    {
                        "label": "Giãn lối",
                        "value": "1",
                    },
                    {
                        "label": "Tiêu mỏng",
                        "value": "2",
                    },
                    {
                        "label": "Hoại tử",
                        "value": "3",
                    },
                ],
            }
        }),
        {
            "label": "Chi tiết khác",
            "key": "CUNGMACCHITIETKHACMP",
            "type": "textfield",
            others: {
                "labelPosition": "left-left",
                "labelWidth": 10,
            },
        },
        JSONRadio("<b>10. Tiền phòng</b> (góc TP)", "TIENPHONGBINHTHUONGMP", "", {
            others: {
                "optionsLabelPosition": "right",
                "inline": true,
                "labelPosition": "left-left",
                "optionsLabelPosition": "right",
                "tableView": false,
                "values": [
                    {
                        "label": "Bình thường",
                        "value": "1",
                    },
                    {
                        "label": "Nông",
                        "value": "2",
                    },
                    {
                        "label": "Mất TP",
                        "value": "3",
                    },
                    {
                        "label": "Sâu",
                        "value": "4",
                    },
                ],
            }
        }),
        {
            "label": "Mù (mm)",
            "key": "TIENPHONGMUMP",
            "type": "textfield",
            others: {
                "labelPosition": "left-left",
                "labelWidth": 10,
            },
        },
        {
            "label": "Máu (mm)",
            "key": "TIENPHONGMAUMP",
            "type": "textfield",
            others: {
                "labelPosition": "left-left",
                "labelWidth": 10,
            },
        },
        {
            "label": "Tổn thương khác",
            "key": "TIENPHONGTONTHUONGKHACMP",
            "type": "textfield",
            others: {
                "labelPosition": "left-left",
                "labelWidth": 10,
            },
        },
        JSONRadio("<b>11. Mống mắt</b>", "MONGMATNAUXOPMP", "", {
            others: {
                "optionsLabelPosition": "right",
                "inline": true,
                "labelPosition": "left-left",
                "optionsLabelPosition": "right",
                "tableView": false,
                "values": [
                    {
                        "label": "Nâu xốp",
                        "value": "1",
                    },
                    {
                        "label": "Xơ teo",
                        "value": "2",
                    },
                ],
            }
        }),
        JSONRadio("Cương tụ", "MONGMATCUONGTUMP", "", {
            others: {
                "optionsLabelPosition": "right",
                "inline": true,
                "labelPosition": "left-left",
                "optionsLabelPosition": "right",
                "tableView": false,
                "values": [
                    {
                        "label": "Cương tụ",
                        "value": "1",
                    },
                    {
                        "label": "Tân mạch",
                        "value": "2",
                    },
                ],
            }
        }),
        JSONRadio("Phòi", "MONGMATPHOIMP", "", {
            others: {
                "optionsLabelPosition": "right",
                "inline": true,
                "labelPosition": "left-left",
                "optionsLabelPosition": "right",
                "tableView": false,
                "values": [
                    {
                        "label": "Phòi",
                        "value": "1",
                    },
                    {
                        "label": "Kẹt",
                        "value": "2",
                    },
                ],
            }
        }),
        {
            "label": "Khác",
            "key": "MONGMATKHAC_MP",
            "type": "textfield",
            others: {
                "labelPosition": "left-left",
                "labelWidth": 10,
            },
        },
        {
            "label": "<b>12. Đồng tử</b> đường kính",
            "key": "DONGTUDUONGKINHMP",
            "type": "textfield",
            others: {
                "labelPosition": "left-left",
                "labelWidth": 10,
                "placeholder": "mm",
            }
        },
        JSONRadio("Tròn", "DONGTUTRONMP", "", {
            others: {
                "optionsLabelPosition": "right",
                "inline": true,
                "labelPosition": "left-left",
                "optionsLabelPosition": "right",
                "tableView": false,
                "values": [
                    {
                        "label": "Tròn",
                        "value": "1",
                    },
                    {
                        "label": "Méo",
                        "value": "2",
                    },
                    {
                        "label": "Dính",
                        "value": "3",
                    },
                ],
            }
        }),
        JSONRadio("Phản xạ", "DONGTUPHANXAMP", "", {
            others: {
                "optionsLabelPosition": "right",
                "inline": true,
                "labelPosition": "left-left",
                "optionsLabelPosition": "right",
                "tableView": false,
                "values": [
                    {
                        "label": "Tốt",
                        "value": "1",
                    },
                    {
                        "label": "Kém",
                        "value": "2",
                    },
                    {
                        "label": "Mất",
                        "value": "3",
                    },
                ],
            }
        }),
        {
            "label": "Tổn thương khác",
            "key": "DONGTUTONTHUONGKHACMP",
            "type": "textfield",
            others: {
                "labelPosition": "left-left",
                "labelWidth": 10,
            },
        },
        JSONRadio("<b>13. Thủy tinh thể</b> bình thường", "THUYTINHTHEBINHTHUONGMP", "", {
            others: {
                "optionsLabelPosition": "right",
                "inline": true,
                "labelPosition": "left-left",
                "optionsLabelPosition": "right",
                "tableView": false,
                "values": [
                    {
                        "label": "Bình thường",
                        "value": "1",
                    },
                    {
                        "label": "Lệch",
                        "value": "2",
                    },
                ],
            }
        }),
        {
            "label": "Hình thái đục",
            "key": "THUYTINHTHEHINHTHAIDUCMP",
            "type": "textfield",
            others: {
                "labelPosition": "left-left",
                "labelWidth": 10,
            },
        },
        JSONRadio("IOL", "THUYTINHTHEIOLMP", "", {
            others: {
                "optionsLabelPosition": "right",
                "inline": true,
                "labelPosition": "left-left",
                "optionsLabelPosition": "right",
                "tableView": false,
                "values": [
                    {
                        "label": "Cân",
                        "value": "1",
                    },
                    {
                        "label": "Lệch",
                        "value": "2",
                    },
                    {
                        "label": "Đục bao sau",
                        "value": "3",
                    },
                    {
                        "label": "Trong TP",
                        "value": "4",
                    },
                    {
                        "label": "Trong HP",
                        "value": "5",
                    }
                ],
            }
        }),
        {
            "label": "Tổn thương khác",
            "key": "THUYTINHTHETONTHUONGKHACMP",
            "type": "textfield",
            others: {
                "labelPosition": "left-left",
                "labelWidth": 10,
            },
        },
        JSONRadio("<b>14. Ánh đồng tử</b>", "ANHDONGTUHONGMP", "", {
            others: {
                "optionsLabelPosition": "right",
                "inline": true,
                "labelPosition": "left-left",
                "optionsLabelPosition": "right",
                "tableView": false,
                "values": [
                    {
                        "label": "Hồng",
                        "value": "1",
                    },
                    {
                        "label": "Xám",
                        "value": "2",
                    },
                    {
                        "label": "Không soi được",
                        "value": "3",
                    },
                ],
            }
        }),
        JSONRadio("<b>15. Dịch kính</b>", "DICHKINHSACHMP", "", {
            others: {
                "optionsLabelPosition": "right",
                "inline": true,
                "labelPosition": "left-left",
                "optionsLabelPosition": "right",
                "tableView": false,
                "values": [
                    {
                        "label": "Sạch",
                        "value": "1",
                    },
                    {
                        "label": "Đục",
                        "value": "2",
                    },
                    {
                        "label": "Xuất huyết",
                        "value": "3",
                    },
                ],
            }
        }),
        {
            "label": "Tổn thương khác",
            "key": "DICHKINHTONTHUONGKHACMP",
            "type": "textfield",
            others: {
                "labelPosition": "left-left",
                "labelWidth": 10,
            },
        },
        JSONRadio("<b>16. Đáy mắt</b> đĩa thị", "DAYMATGAITHIMP", "", {
            others: {
                "optionsLabelPosition": "right",
                "inline": true,
                "labelPosition": "left-left",
                "optionsLabelPosition": "right",
                "tableView": false,
                "values": [
                    {
                        "label": "Không soi được",
                        "value": "1",
                    },
                    {
                        "label": "Bình thường",
                        "value": "2",
                    },
                    {
                        "label": "Lõm teo gai",
                        "value": "3",
                    },
                    {
                        "label": "Phù gai",
                        "value": "4",
                    },
                    {
                        "label": "Bạc màu gai thi",
                        "value": "5",
                    },
                ],
            }
        }),
        {
            "label": "C/D",
            "key": "DAYMATCDMP",
            "type": "textfield",
            others: {
                "labelPosition": "left-left",
                "labelWidth": 10,
            },
        },
        {
            "label": "Võng mạc",
            "key": "DAYMATVONGMACMP",
            "type": "textfield",
            others: {
                "labelPosition": "left-left",
                "labelWidth": 10,
            },
        },
        {
            "label": "Hệ mạch máu",
            "key": "DAYMATHEMACHMAUMP",
            "type": "textfield",
            others: {
                "labelPosition": "left-left",
                "labelWidth": 10,
            },
        },
        {
            "label": "Tổn thương khác",
            "key": "DAYMATTONTHUONGKHACMP",
            "type": "textfield",
            others: {
                "labelPosition": "left-left",
                "labelWidth": 10,
            },
        },


        {
            "label": "",
            "content": "<center><b>MẮT TRÁI</b></center>",
            "refreshOnChange": false,
            "key": "MATPHAITEXT",
            "type": "htmlelement",
            "input": false,
            "tableView": false,
        },
        {
            "label": "<b>1. Thị lực</b> không kính",
            "key": "THILUCKHONGKINHMT",
            "type": "textfield",
            others: {
                "labelPosition": "left-left",
                "labelWidth": 10,
            }
        },
        {
            "label": "Qua lỗ",
            "key": "THILUCQUALOMT",
            "type": "textfield",
            others: {
                "labelPosition": "left-left",
                "labelWidth": 10,
            }
        },
        {
            "label": "Có chỉnh kính",
            "key": "THILUCCOCHINHKINHMT",
            "type": "textfield",
            others: {
                "labelPosition": "left-left",
                "labelWidth": 10,
            }
        },
        {
            "label": "Nhìn gần",
            "key": "THILUCNHINGANMT",
            "type": "textfield",
            others: {
                "labelPosition": "left-left",
                "labelWidth": 10,
            }
        },
        {
            "label": "<b>2. Nhãn áp (mmHg)</b>",
            "key": "NHANAPMT",
            "type": "textfield",
            others: {
                "labelPosition": "left-left",
                "labelWidth": 10,
            }
        },
        {
            "label": "<b>3. Lác và vận nhãn</b>",
            "key": "LACVANNHANMT",
            "type": "textfield",
            others: {
                "labelPosition": "left-left",
                "labelWidth": 10,
            }
        },
        JSONRadio("<b>4. Lệ đạo</b> Bơm lệ quản: Nước thoát tốt", "LEDAONUOCTHOATTOTMT", "", {
            others: {
                "optionsLabelPosition": "right",
                "inline": true,
                "labelPosition": "left-left",
                "optionsLabelPosition": "right",
                "tableView": false,
                "values": [
                    {
                        "label": "Không",
                        "value": "0",
                    },
                    {
                        "label": "Có",
                        "value": "1",
                    },
                ],
            }
        }),
        {
            "label": "Chi tiết",
            "key": "BOMLEQUAN_MT",
            "type": "textarea",
            customClass: "pr-2",
            others: {
                "labelPosition": "left-left",
                "labelWidth": 10,
                "customConditional": "show = data.LEDAONUOCTHOATTOTMT == 1;",
            }
        },
        JSONRadio("Trào lệ quản đối diên", "LEDAOTRAOLEQUANMT", "", {
            others: {
                "optionsLabelPosition": "right",
                "inline": true,
                "labelPosition": "left-left",
                "optionsLabelPosition": "right",
                "tableView": false,
                "values": [
                    {
                        "label": "Không",
                        "value": "0",
                    },
                    {
                        "label": "Có",
                        "value": "1",
                    },
                ],
            }
        }),
        {
            "label": "Chi tiết",
            "key": "TRAOLEQUANDOIDIEN_MT",
            "type": "textarea",
            others: {
                "labelPosition": "left-left",
                "labelWidth": 10,
                "customConditional": "show = data.LEDAOTRAOLEQUANMT == 1;",
            }
        },
        JSONRadio("Trào tại chỗ", "LEDAOTRAOTAICHOMT", "", {
            others: {
                "optionsLabelPosition": "right",
                "inline": true,
                "labelPosition": "left-left",
                "optionsLabelPosition": "right",
                "tableView": false,
                "values": [
                    {
                        "label": "Không",
                        "value": "0",
                    },
                    {
                        "label": "Có",
                        "value": "1",
                    },
                ],
            }
        }),
        {
            "label": "Chi tiết",
            "key": "TRAOTAICHO_MT",
            "type": "textarea",
            others: {
                "labelPosition": "left-left",
                "labelWidth": 10,
                "customConditional": "show = data.LEDAOTRAOTAICHOMT == 1;",
            }
        },
        JSONRadio("<b>5. Mi mắt</b>", "MIMATTINHTRANGMT", "", {
            others: {
                "optionsLabelPosition": "right",
                "inline": true,
                "labelPosition": "left-left",
                "optionsLabelPosition": "right",
                "tableView": false,
                "values": [
                    {
                        "label": "Bình thường",
                        "value": "1",
                        "shortcut": ""
                    },
                    {
                        "label": "Phù",
                        "value": "2",
                        "shortcut": ""
                    },
                    {
                        "label": "Chấp",
                        "value": "3",
                        "shortcut": ""
                    },
                    {
                        "label": "Lẹo",
                        "value": "4",
                        "shortcut": ""
                    },
                    {
                        "label": "Sẹo da mi",
                        "value": "5",
                        "shortcut": ""
                    },
                    {
                        "label": "Sụp mi",
                        "value": "6",
                        "shortcut": ""
                    },
                ],
            }
        }),
        {
            "label": "Khác",
            "key": "MIMATTINHTRANGKHACMT",
            "type": "textfield",
            others: {
                "labelPosition": "left-left",
                "labelWidth": 10,
            }
        },
        JSONRadio("U mi", "MIMATUMIMT", "", {
            others: {
                "optionsLabelPosition": "right",
                "inline": true,
                "labelPosition": "left-left",
                "optionsLabelPosition": "right",
                "tableView": false,
                "values": [
                    {
                        "label": "Không",
                        "value": "0",
                    },
                    {
                        "label": "Có",
                        "value": "1",
                    },
                ],
            }
        }),
        {
            "label": "Tính chất u",
            "key": "MIMATTINHCHATUMT",
            "type": "textarea",
            others: {
                "labelPosition": "left-left",
                "labelWidth": 10,
                "customConditional": "show = data.MIMATUMIMT == 1;",
            }
        },
        {
            "label": "Vị trí",
            "key": "MIMATVITRIMT",
            "type": "textfield",
            others: {
                "labelPosition": "left-left",
                "labelWidth": 10,
            }
        },
        {
            "label": "Kích thước",
            "key": "MIMATKICHTHUOCMT",
            "type": "textfield",
            others: {
                "labelPosition": "left-left",
                "labelWidth": 10,
            }
        },
        JSONRadio("Quặm", "MIMATQUAMMT", "", {
            others: {
                "optionsLabelPosition": "right",
                "inline": true,
                "labelPosition": "left-left",
                "optionsLabelPosition": "right",
                "tableView": false,
                "values": [
                    {
                        "label": "Có",
                        "value": "1",
                    },
                    {
                        "label": "Không",
                        "value": "2",
                    },
                ],
            }
        }),
        JSONRadio("Mi trên", "MIMATQUAMMITRENMT", "", {
            others: {
                "optionsLabelPosition": "right",
                "inline": true,
                "labelPosition": "left-left",
                "optionsLabelPosition": "right",
                "tableView": false,
                "values": [
                    {
                        "label": "1/3 trong",
                        "value": "1",
                    },
                    {
                        "label": "1/3 giữa",
                        "value": "2",
                    },
                    {
                        "label": "1/3 ngoài",
                        "value": "3",
                    },
                    {
                        "label": "Toàn bộ",
                        "value": "4",
                    },
                ],
            }
        }),
        JSONRadio("Mi dưới", "MIMATQUAMMIDUOIMT", "", {
            others: {
                "optionsLabelPosition": "right",
                "inline": true,
                "labelPosition": "left-left",
                "optionsLabelPosition": "right",
                "tableView": false,
                "values": [
                    {
                        "label": "1/3 trong",
                        "value": "1",
                    },
                    {
                        "label": "1/3 giữa",
                        "value": "2",
                    },
                    {
                        "label": "1/3 ngoài",
                        "value": "3",
                    },
                    {
                        "label": "Toàn bộ",
                        "value": "4",
                    },
                ],
            }
        }),
        JSONRadio("Hở mi", "HOMIMT", "", {
            others: {
                "optionsLabelPosition": "right",
                "inline": true,
                "labelPosition": "left-left",
                "optionsLabelPosition": "right",
                "tableView": false,
                "values": [
                    {
                        "label": "Không",
                        "value": "0",
                    },
                    {
                        "label": "Có",
                        "value": "1",
                    },
                ],
            }
        }),
        JSONRadio("Trễ mi", "TREMIMT", "", {
            others: {
                "optionsLabelPosition": "right",
                "inline": true,
                "labelPosition": "left-left",
                "optionsLabelPosition": "right",
                "tableView": false,
                "values": [
                    {
                        "label": "Không",
                        "value": "0",
                    },
                    {
                        "label": "Có",
                        "value": "1",
                    },
                ],
            }
        }),
        JSONRadio("Khuyết mi", "MIMATKHUYETMIMT", "", {
            others: {
                "optionsLabelPosition": "right",
                "inline": true,
                "labelPosition": "left-left",
                "optionsLabelPosition": "right",
                "tableView": false,
                "values": [
                    {
                        "label": "1/3 trong",
                        "value": "1",
                    },
                    {
                        "label": "1/3 giữa",
                        "value": "2",
                    },
                    {
                        "label": "1/3 ngoài",
                        "value": "3",
                    },
                ],
            }
        }),
        JSONRadio("Tuyến bờ mi", "MIMATTUYENBOMIMT", "", {
            others: {
                "optionsLabelPosition": "right",
                "inline": true,
                "labelPosition": "left-left",
                "optionsLabelPosition": "right",
                "tableView": false,
                "values": [
                    {
                        "label": "Bình thường",
                        "value": "1",
                    },
                    {
                        "label": "Viêm tắc nhẹ",
                        "value": "2",
                    },
                    {
                        "label": "Vừa",
                        "value": "3",
                    },
                    {
                        "label": "Nặng",
                        "value": "4",
                    },
                ],
            }
        }),
        JSONRadio("Viêm bờ mi: (chân lông mi)", "MIMATVIEMBOMIMT", "", {
            others: {
                "optionsLabelPosition": "right",
                "inline": true,
                "labelPosition": "left-left",
                "optionsLabelPosition": "right",
                "tableView": false,
                "values": [
                    {
                        "label": "Không",
                        "value": "0",
                    },
                    {
                        "label": "Có",
                        "value": "1",
                    },
                ],
            }
        }),
        {
            "label": "<b>6. Tình trạng nhãn cầu</b>: Tổn thương khác",
            "key": "MIMATTONTHUONGKHACMT",
            "type": "textfield",
            others: {
                "labelPosition": "left-left",
                "labelWidth": 10,
            },
        },
        JSONRadio("<b>7. Kết mạc</b>", "KETMAC_MATPHAI", "", {
            others: {
                "optionsLabelPosition": "right",
                "inline": true,
                "labelPosition": "left-left",
                "optionsLabelPosition": "right",
                "tableView": false,
                "values": [
                    {
                        "label": "Bình thường",
                        "value": "1",
                    },
                    {
                        "label": "Mộng",
                        "value": "2",
                    },
                ],
            }
        }),
        JSONRadio("Cương tụ", "KETMACCUONGTUMT", "", {
            others: {
                "optionsLabelPosition": "right",
                "inline": true,
                "labelPosition": "left-left",
                "optionsLabelPosition": "right",
                "tableView": false,
                "values": [
                    {
                        "label": "Tỏa lan",
                        "value": "1",
                    },
                    {
                        "label": "Rìa",
                        "value": "2",
                    },
                    {
                        "label": "Ở KM nhãn cầu",
                        "value": "3",
                    },
                    {
                        "label": "Ở rìa",
                        "value": "4",
                    },
                    {
                        "label": "Toàn bộ",
                        "value": "5",
                    },
                ],
            }
        }),
        JSONRadio("Phù nề", "KETMACPHUNEMT", "", {
            others: {
                "optionsLabelPosition": "right",
                "inline": true,
                "labelPosition": "left-left",
                "optionsLabelPosition": "right",
                "tableView": false,
                "values": [
                    {
                        "label": "Phù nề",
                        "value": "1",
                    },
                    {
                        "label": "Xuất huyết",
                        "value": "2",
                    },
                    {
                        "label": "Sừng hóa",
                        "value": "3",
                    },
                ],
            }
        }),
        JSONRadio("Nhú", "KETMACNHUMT", "", {
            others: {
                "optionsLabelPosition": "right",
                "inline": true,
                "labelPosition": "left-left",
                "optionsLabelPosition": "right",
                "tableView": false,
                "values": [
                    {
                        "label": "Nhú",
                        "value": "1",
                    },
                    {
                        "label": "Hột",
                        "value": "2",
                    },
                    {
                        "label": "Sẹo",
                        "value": "3",
                    },
                ],
            }
        }),
        JSONRadio("Tiết tố mủ", "KETMACTIETTOMUMT", "", {
            others: {
                "optionsLabelPosition": "right",
                "inline": true,
                "labelPosition": "left-left",
                "optionsLabelPosition": "right",
                "tableView": false,
                "values": [
                    {
                        "label": "Tiết tố mủ",
                        "value": "1",
                    },
                    {
                        "label": "Tiết tố trong",
                        "value": "2",
                    },
                    {
                        "label": "Giả mạc",
                        "value": "3",
                    },
                ],
            }
        }),
        JSONRadio("Bắt màu fluo", "KETMACFLUORMT", "", {
            others: {
                "optionsLabelPosition": "right",
                "inline": true,
                "labelPosition": "left-left",
                "optionsLabelPosition": "right",
                "tableView": false,
                "values": [
                    {
                        "label": "Không",
                        "value": "0",
                    },
                    {
                        "label": "Có",
                        "value": "1",
                    },
                ],
            }
        }),
        {
            "label": "U kết mạc (Tính chất)",
            "key": "KETMACUKETMACTINHCHATMT",
            "type": "textfield",
            others: {
                "labelPosition": "left-left",
                "labelWidth": 10,
            },
        },
        {
            "label": "U kết mạc (Vị trí)",
            "key": "KETMACUKETMACVITRIMT",
            "type": "textfield",
            others: {
                "labelPosition": "left-left",
                "labelWidth": 10,
            },
        },
        {
            "label": "U kết mạc (Kích thước)",
            "key": "KETMACUKETMACKICHTHUOCMT",
            "type": "textfield",
            others: {
                "labelPosition": "left-left",
                "labelWidth": 10,
            },
        },
        JSONRadio("Cùng đồ", "KETMACCUNGDOMT", "", {
            others: {
                "optionsLabelPosition": "right",
                "inline": true,
                "labelPosition": "left-left",
                "optionsLabelPosition": "right",
                "tableView": false,
                "values": [
                    {
                        "label": "Bình thường",
                        "value": "1",
                    },
                    {
                        "label": "Cạn",
                        "value": "2",
                    },
                    {
                        "label": "Dính",
                        "value": "3",
                    },
                ],
            }
        }),
        JSONRadio("Chiều cao cầu dính", "KETMACCHIEUCAOCUACAUDINHMT", "", {
            others: {
                "optionsLabelPosition": "right",
                "inline": true,
                "labelPosition": "left-left",
                "optionsLabelPosition": "right",
                "tableView": false,
                "values": [
                    {
                        "label": "Dính ở cùng đồ chưa vào sụn",
                        "value": "1",
                    },
                    {
                        "label": "Dính cùng đồ, vào sụn nhưng chưa hết chiều cao sụn",
                        "value": "2",
                    },
                    {
                        "label": "Dính hết chiều cao sụn mi",
                        "value": "3",
                    },
                    {
                        "label": "Dính cả bờ mi hoặc điểm lệ",
                        "value": "4",
                    },
                ],
            }
        }),
        JSONRadio("Độ rộng cầu dính", "KETMACDORONGCUACAUDINHMT", "", {
            others: {
                "optionsLabelPosition": "right",
                "inline": true,
                "labelPosition": "left-left",
                "optionsLabelPosition": "right",
                "tableView": false,
                "values": [
                    {
                        "label": "≤ 1/3 chiều dài mi",
                        "value": "1",
                    },
                    {
                        "label": "1/3 - 2/3 chiều dài mi",
                        "value": "2",
                    },
                    {
                        "label": "≥ 2/3 chiều dài mi",
                        "value": "3",
                    },
                ],
            }
        }),
        {
            "label": "Tổn thương khác",
            "key": "KETMACTONTHUONGKHACMT",
            "type": "textfield",
            others: {
                "labelPosition": "left-left",
                "labelWidth": 10,
            },
        },
        JSONRadio("<b>8. Giác mạc</b>: Ghép giác mạc", "GHEPGIACMAC_MT", "", {
            others: {
                "optionsLabelPosition": "right",
                "inline": true,
                "labelPosition": "left-left",
                "optionsLabelPosition": "right",
                "tableView": false,
                "values": [
                    {
                        "label": "Không",
                        "value": "0",
                    },
                    {
                        "label": "Có",
                        "value": "1",
                    },
                ],
            }
        }),
        JSONRadio("Kích thước", "GIACMACKICHTHUOCMT", "", {
            others: {
                "optionsLabelPosition": "right",
                "inline": true,
                "labelPosition": "left-left",
                "optionsLabelPosition": "right",
                "tableView": false,
                "values": [
                    {
                        "label": "Bình thường",
                        "value": "1",
                    },
                    {
                        "label": "To",
                        "value": "2",
                    },
                    {
                        "label": "Nhỏ",
                        "value": "3",
                    },
                ],
            }
        }),
        JSONRadio("Hình dạng", "GIACMACHINHDANGMT", "", {
            others: {
                "optionsLabelPosition": "right",
                "inline": true,
                "labelPosition": "left-left",
                "optionsLabelPosition": "right",
                "tableView": false,
                "values": [
                    {
                        "label": "Bình thường",
                        "value": "1",
                    },
                    {
                        "label": "Nón",
                        "value": "2",
                    },
                    {
                        "label": "Cầu",
                        "value": "3",
                    },
                ],
            }
        }),
        JSONRadio("Biểu mô", "GIACMACTONTHUONGDANCHAMMT", "", {
            others: {
                "optionsLabelPosition": "right",
                "inline": true,
                "labelPosition": "left-left",
                "optionsLabelPosition": "right",
                "tableView": false,
                "values": [
                    {
                        "label": "Bình thường",
                        "value": "1",
                    },
                    {
                        "label": "Tổn thương dạng chấm",
                        "value": "2",
                    },
                ],
            }
        }),
        JSONRadio("Phù bọng biểu mô", "GIACMACPHUBONGBIEUMOMT", "", {
            others: {
                "optionsLabelPosition": "right",
                "inline": true,
                "labelPosition": "left-left",
                "optionsLabelPosition": "right",
                "tableView": false,
                "values": [
                    {
                        "label": "Nhẹ",
                        "value": "1",
                    },
                    {
                        "label": "Vừa",
                        "value": "2",
                    },
                    {
                        "label": "Nặng",
                        "value": "3",
                    },
                ],
            }
        }),
        JSONRadio("Mất biểu mô", "GIACMACMATBIEUMOMT", "", {
            others: {
                "optionsLabelPosition": "right",
                "inline": true,
                "labelPosition": "left-left",
                "optionsLabelPosition": "right",
                "tableView": false,
                "values": [
                    {
                        "label": "< 1/3 d.tích",
                        "value": "1",
                    },
                    {
                        "label": "1/3 - 1/2 d.tích",
                        "value": "2",
                    },
                    {
                        "label": "> 1/2 d.tích",
                        "value": "3",
                    },
                ],
            }
        }),
        JSONRadio("Vị trí", "GIACMACVITRIMATBIEUMOMT", "", {
            others: {
                "optionsLabelPosition": "right",
                "inline": true,
                "labelPosition": "left-left",
                "optionsLabelPosition": "right",
                "tableView": false,
                "values": [
                    {
                        "label": "Trung tâm",
                        "value": "1",
                    },
                    {
                        "label": "Lệch tâm",
                        "value": "2",
                    },
                    {
                        "label": "Sát rìa",
                        "value": "3",
                    },
                ],
            }
        }),
        JSONRadio("Bờ tổn thương", "GIACMACBOTONTHUONGMT", "", {
            others: {
                "optionsLabelPosition": "right",
                "inline": true,
                "labelPosition": "left-left",
                "optionsLabelPosition": "right",
                "tableView": false,
                "values": [
                    {
                        "label": "Nham nhở",
                        "value": "1",
                    },
                    {
                        "label": "Trơn nhẵn",
                        "value": "2",
                    },
                    {
                        "label": "Đào rãnh",
                        "value": "3",
                    },
                ],
            }
        }),
        JSONRadio("Thoái hóa dải băng", "GIACMACTHOAIHOADAIBANGMT", "", {
            others: {
                "optionsLabelPosition": "right",
                "inline": true,
                "labelPosition": "left-left",
                "optionsLabelPosition": "right",
                "tableView": false,
                "values": [
                    {
                        "label": "Không",
                        "value": "0",
                    },
                    {
                        "label": "Có",
                        "value": "1",
                    },
                ],
            }
        }),
        JSONRadio("Lắng động thuốc", "GIACMACLANGDONGTHUOCMT", "", {
            others: {
                "optionsLabelPosition": "right",
                "inline": true,
                "labelPosition": "left-left",
                "optionsLabelPosition": "right",
                "tableView": false,
                "values": [
                    {
                        "label": "Không",
                        "value": "0",
                    },
                    {
                        "label": "Có",
                        "value": "1",
                    },
                ],
            }
        }),
        {
            "label": "Tổn thương khác",
            "key": "GIACMACBIEUMOTONTHUONGKHACMT",
            "type": "textfield",
            others: {
                "labelPosition": "left-left",
                "labelWidth": 10,
            },
        },
        JSONRadio("Nhu mô bình thường", "NHUMO_BTH_MT", "", {
            others: {
                "optionsLabelPosition": "right",
                "inline": true,
                "labelPosition": "left-left",
                "optionsLabelPosition": "right",
                "tableView": false,
                "values": [
                    {
                        "label": "Không",
                        "value": "0",
                    },
                    {
                        "label": "Có",
                        "value": "1",
                    },
                ],
            }
        }),
        JSONRadio("Phù", "GIACMACNHUMOPHUMT", "", {
            others: {
                "optionsLabelPosition": "right",
                "inline": true,
                "labelPosition": "left-left",
                "optionsLabelPosition": "right",
                "tableView": false,
                "values": [
                    {
                        "label": "Nhẹ",
                        "value": "1",
                    },
                    {
                        "label": "Vừa",
                        "value": "2",
                    },
                    {
                        "label": "Nặng",
                        "value": "3",
                    },
                ],
            }
        }),
        JSONRadio("Thẩm lậu", "GIACMACNHUMOTHAMLAUMT", "", {
            others: {
                "optionsLabelPosition": "right",
                "inline": true,
                "labelPosition": "left-left",
                "optionsLabelPosition": "right",
                "tableView": false,
                "values": [
                    {
                        "label": "Nông",
                        "value": "1",
                    },
                    {
                        "label": "Sâu",
                        "value": "2",
                    },
                    {
                        "label": "Rất sâu",
                        "value": "3",
                    },
                ],
            }
        }),
        JSONRadio("Vị trí thẩm lậu", "GIACMACNHUMOVITRITHAMLAUMT", "", {
            others: {
                "optionsLabelPosition": "right",
                "inline": true,
                "labelPosition": "left-left",
                "optionsLabelPosition": "right",
                "tableView": false,
                "values": [
                    {
                        "label": "Khu trú",
                        "value": "1",
                    },
                    {
                        "label": "Lan tỏa",
                        "value": "2",
                    },
                    {
                        "label": "Nhiều ổ vệ tinh",
                        "value": "3",
                    },
                ],
            }
        }),
        JSONRadio("Tiêu mỏng", "GIACMACNHUMOTIEUMONGMT", "", {
            others: {
                "optionsLabelPosition": "right",
                "inline": true,
                "labelPosition": "left-left",
                "optionsLabelPosition": "right",
                "tableView": false,
                "values": [
                    {
                        "label": "< 1/2 c/ dày",
                        "value": "1",
                    },
                    {
                        "label": "> 1/2 c/ dày",
                        "value": "2",
                    },
                ],
            }
        }),
        JSONRadio("Vị trí tiêu mỏng", "GIACMACNHUMOVITRITIEUMONGMT", "", {
            others: {
                "optionsLabelPosition": "right",
                "inline": true,
                "labelPosition": "left-left",
                "optionsLabelPosition": "right",
                "tableView": false,
                "values": [
                    {
                        "label": "Ở rìa",
                        "value": "1",
                    },
                    {
                        "label": "Lệch tâm",
                        "value": "2",
                    },
                    {
                        "label": "Ở trung tâm",
                        "value": "3",
                    },
                ],
            }
        }),
        {
            "label": "Tổn thương khác",
            "key": "GIACMACNHUMOTONTHUONGKHACMT",
            "type": "textfield",
            others: {
                "labelPosition": "left-left",
                "labelWidth": 10,
            },
        },
        JSONRadio("Nội mô & Descemet bình thường", "NOIMO_DESCEMET_BTH_MT", "", {
            others: {
                "optionsLabelPosition": "right",
                "inline": true,
                "labelPosition": "left-left",
                "optionsLabelPosition": "right",
                "tableView": false,
                "values": [
                    {
                        "label": "Không",
                        "value": "0",
                    },
                    {
                        "label": "Bình thường",
                        "value": "1",
                    },
                ],
            }
        }),
        JSONRadio("Tủa sắc tố mặt sau", "GIACMACNOIMOTUASACTOMT", "", {
            others: {
                "optionsLabelPosition": "right",
                "inline": true,
                "labelPosition": "left-left",
                "optionsLabelPosition": "right",
                "tableView": false,
                "values": [
                    {
                        "label": "GM",
                        "value": "1",
                    },
                    {
                        "label": "Mủ mặt sau",
                        "value": "2",
                    },
                ],
            }
        }),
        JSONRadio("Xuất tiết mặt sau", "GIACMACNOIMOXUATTIETMT", "", {
            others: {
                "optionsLabelPosition": "right",
                "inline": true,
                "labelPosition": "left-left",
                "optionsLabelPosition": "right",
                "tableView": false,
                "values": [
                    {
                        "label": "Mặt sau",
                        "value": "1",
                    },
                    {
                        "label": "Guttata",
                        "value": "2",
                    },
                ],
            }
        }),
        JSONRadio("Rạn màng", "GIACMACNOIMODESCEMETMT", "", {
            others: {
                "optionsLabelPosition": "right",
                "inline": true,
                "labelPosition": "left-left",
                "optionsLabelPosition": "right",
                "tableView": false,
                "values": [
                    {
                        "label": "Descemet",
                        "value": "1",
                    },
                    {
                        "label": "Cuộn Descemet",
                        "value": "2",
                    },
                ],
            }
        }),
        {
            "label": "Tổn thương khác",
            "key": "GIACMACNOIMOTONTHUONGKHACMT",
            "type": "textfield",
            others: {
                "labelPosition": "left-left",
                "labelWidth": 10,
            },
        },
        JSONRadio("Dọa thủng", "GIACMACDOATHUNGMT", "", {
            others: {
                "optionsLabelPosition": "right",
                "inline": true,
                "labelPosition": "left-left",
                "optionsLabelPosition": "right",
                "tableView": false,
                "values": [
                    {
                        "label": "Dọa thủng",
                        "value": "1",
                    },
                    {
                        "label": "Kẹt mống mắt",
                        "value": "2",
                    },
                ],
            }
        }),
        JSONRadio("Thủng giác mạc", "GIACMACTHUNGGIACMACMT", "", {
            others: {
                "optionsLabelPosition": "right",
                "inline": true,
                "labelPosition": "left-left",
                "optionsLabelPosition": "right",
                "tableView": false,
                "values": [
                    {
                        "label": "Trung tâm",
                        "value": "1",
                    },
                    {
                        "label": "Lệch tâm",
                        "value": "2",
                    },
                    {
                        "label": "Sát rìa",
                        "value": "3",
                    },
                ],
            }
        }),
        JSONRadio("Tân mạch", "GIACMACTANMACHMT", "", {
            others: {
                "optionsLabelPosition": "right",
                "inline": true,
                "labelPosition": "left-left",
                "optionsLabelPosition": "right",
                "tableView": false,
                "values": [
                    {
                        "label": "Nông, hướng tâm",
                        "value": "1",
                    },
                    {
                        "label": "Ly tâm",
                        "value": "2",
                    },
                    {
                        "label": "Sâu",
                        "value": "3",
                    },
                ],
            }
        }),
        JSONRadio("Mức độ", "GIACMACMUCDOMT", "", {
            others: {
                "optionsLabelPosition": "right",
                "inline": true,
                "labelPosition": "left-left",
                "optionsLabelPosition": "right",
                "tableView": false,
                "values": [
                    {
                        "label": "≤ 1/3 chu vi",
                        "value": "1",
                    },
                    {
                        "label": "1/3 - 2/3 chu vi",
                        "value": "2",
                    },
                    {
                        "label": "≥ 2/3 chu vi",
                        "value": "3",
                    },
                ],
            }
        }),
        {
            "label": "Bất thường khác",
            "key": "GIACMACBATTHUONGKHACMT",
            "type": "textfield",
            others: {
                "labelPosition": "left-left",
                "labelWidth": 10,
            },
        },
        {
            "label": "",
            "content": "<b>9. Củng mạc</b>",
            "refreshOnChange": false,
            "key": "TB_1",
            "type": "htmlelement",
            "input": false,
            "tableView": false,
        },
        // {
        //     "label": "Bình thường",
        //     "key": "CUNGMAC_BTH_MT",
        //     "type": "checkbox",
        //     others: {
        //         "labelPosition": "left-left",
        //         "labelWidth": 10,
        //     },
        // },
        JSONRadio("Viêm", "CUNGMACVIEMMT", "", {
            others: {
                "optionsLabelPosition": "right",
                "inline": true,
                "labelPosition": "left-left",
                "optionsLabelPosition": "right",
                "tableView": false,
                "values": [
                    {
                        "label": "Nốt",
                        "value": "1",
                    },
                    {
                        "label": "Lan tỏa",
                        "value": "2",
                    },
                    {
                        "label": "Áp xe",
                        "value": "3",
                    },
                ],
            }
        }),
        JSONRadio("Độ viêm", "CUNGMACDOVIEMMT", "", {
            others: {
                "optionsLabelPosition": "right",
                "inline": true,
                "labelPosition": "left-left",
                "optionsLabelPosition": "right",
                "tableView": false,
                "values": [
                    {
                        "label": "Nông",
                        "value": "1",
                    },
                    {
                        "label": "Sâu",
                        "value": "2",
                    },
                ],
            }
        }),
        JSONRadio("Giãn lối", "CUNGMACGIANLOIMT", "", {
            others: {
                "optionsLabelPosition": "right",
                "inline": true,
                "labelPosition": "left-left",
                "optionsLabelPosition": "right",
                "tableView": false,
                "values": [
                    {
                        "label": "Giãn lối",
                        "value": "1",
                    },
                    {
                        "label": "Tiêu mỏng",
                        "value": "2",
                    },
                    {
                        "label": "Hoại tử",
                        "value": "3",
                    },
                ],
            }
        }),
        {
            "label": "Chi tiết khác",
            "key": "CUNGMACCHITIETKHACMT",
            "type": "textfield",
            others: {
                "labelPosition": "left-left",
                "labelWidth": 10,
            },
        },
        JSONRadio("<b>10. Tiền phòng</b> (góc TP)", "TIENPHONGBINHTHUONGMT", "", {
            others: {
                "optionsLabelPosition": "right",
                "inline": true,
                "labelPosition": "left-left",
                "optionsLabelPosition": "right",
                "tableView": false,
                "values": [
                    {
                        "label": "Bình thường",
                        "value": "1",
                    },
                    {
                        "label": "Nông",
                        "value": "2",
                    },
                    {
                        "label": "Mất TP",
                        "value": "3",
                    },
                    {
                        "label": "Sâu",
                        "value": "4",
                    },
                ],
            }
        }),
        {
            "label": "Mù (mm)",
            "key": "TIENPHONGMUMT",
            "type": "textfield",
            others: {
                "labelPosition": "left-left",
                "labelWidth": 10,
            },
        },
        {
            "label": "Máu (mm)",
            "key": "TIENPHONGMAUMT",
            "type": "textfield",
            others: {
                "labelPosition": "left-left",
                "labelWidth": 10,
            },
        },
        {
            "label": "Tổn thương khác",
            "key": "TIENPHONGTONTHUONGKHACMT",
            "type": "textfield",
            others: {
                "labelPosition": "left-left",
                "labelWidth": 10,
            },
        },
        JSONRadio("<b>11. Mống mắt</b>", "MONGMATNAUXOPMT", "", {
            others: {
                "optionsLabelPosition": "right",
                "inline": true,
                "labelPosition": "left-left",
                "optionsLabelPosition": "right",
                "tableView": false,
                "values": [
                    {
                        "label": "Nâu xốp",
                        "value": "1",
                    },
                    {
                        "label": "Xơ teo",
                        "value": "2",
                    },
                ],
            }
        }),
        JSONRadio("Cương tụ", "MONGMATCUONGTUMT", "", {
            others: {
                "optionsLabelPosition": "right",
                "inline": true,
                "labelPosition": "left-left",
                "optionsLabelPosition": "right",
                "tableView": false,
                "values": [
                    {
                        "label": "Cương tụ",
                        "value": "1",
                    },
                    {
                        "label": "Tân mạch",
                        "value": "2",
                    },
                ],
            }
        }),
        JSONRadio("Phòi", "MONGMATPHOIMT", "", {
            others: {
                "optionsLabelPosition": "right",
                "inline": true,
                "labelPosition": "left-left",
                "optionsLabelPosition": "right",
                "tableView": false,
                "values": [
                    {
                        "label": "Phòi",
                        "value": "1",
                    },
                    {
                        "label": "Kẹt",
                        "value": "2",
                    },
                ],
            }
        }),
        {
            "label": "Khác",
            "key": "MONGMATKHAC_MT",
            "type": "textfield",
            others: {
                "labelPosition": "left-left",
                "labelWidth": 10,
            },
        },
        {
            "label": "<b>12. Đồng tử</b> đường kính",
            "key": "DONGTUDUONGKINHMT",
            "type": "textfield",
            others: {
                "labelPosition": "left-left",
                "labelWidth": 10,
                "placeholder": "mm",
            }
        },
        JSONRadio("Tròn", "DONGTUTRONMT", "", {
            others: {
                "optionsLabelPosition": "right",
                "inline": true,
                "labelPosition": "left-left",
                "optionsLabelPosition": "right",
                "tableView": false,
                "values": [
                    {
                        "label": "Tròn",
                        "value": "1",
                    },
                    {
                        "label": "Méo",
                        "value": "2",
                    },
                    {
                        "label": "Dính",
                        "value": "3",
                    },
                ],
            }
        }),
        JSONRadio("Phản xạ", "DONGTUPHANXAMT", "", {
            others: {
                "optionsLabelPosition": "right",
                "inline": true,
                "labelPosition": "left-left",
                "optionsLabelPosition": "right",
                "tableView": false,
                "values": [
                    {
                        "label": "Tốt",
                        "value": "1",
                    },
                    {
                        "label": "Kém",
                        "value": "2",
                    },
                    {
                        "label": "Mất",
                        "value": "3",
                    },
                ],
            }
        }),
        {
            "label": "Tổn thương khác",
            "key": "DONGTUTONTHUONGKHACMT",
            "type": "textfield",
            others: {
                "labelPosition": "left-left",
                "labelWidth": 10,
            },
        },
        JSONRadio("<b>13. Thủy tinh thể</b> bình thường", "THUYTINHTHEBINHTHUONGMT", "", {
            others: {
                "optionsLabelPosition": "right",
                "inline": true,
                "labelPosition": "left-left",
                "optionsLabelPosition": "right",
                "tableView": false,
                "values": [
                    {
                        "label": "Bình thường",
                        "value": "1",
                    },
                    {
                        "label": "Lệch",
                        "value": "2",
                    },
                ],
            }
        }),
        {
            "label": "Hình thái đục",
            "key": "THUYTINHTHEHINHTHAIDUCMT",
            "type": "textfield",
            others: {
                "labelPosition": "left-left",
                "labelWidth": 10,
            },
        },
        JSONRadio("IOL", "THUYTINHTHEIOLMT", "", {
            others: {
                "optionsLabelPosition": "right",
                "inline": true,
                "labelPosition": "left-left",
                "optionsLabelPosition": "right",
                "tableView": false,
                "values": [
                    {
                        "label": "Cân",
                        "value": "1",
                    },
                    {
                        "label": "Lệch",
                        "value": "2",
                    },
                    {
                        "label": "Đục bao sau",
                        "value": "3",
                    },
                    {
                        "label": "Trong TP",
                        "value": "4",
                    },
                    {
                        "label": "Trong HP",
                        "value": "5",
                    }
                ],
            }
        }),
        {
            "label": "Tổn thương khác",
            "key": "THUYTINHTHETONTHUONGKHACMT",
            "type": "textfield",
            others: {
                "labelPosition": "left-left",
                "labelWidth": 10,
            },
        },
        JSONRadio("<b>14. Ánh đồng tử</b>", "ANHDONGTUHONGMT", "", {
            others: {
                "optionsLabelPosition": "right",
                "inline": true,
                "labelPosition": "left-left",
                "optionsLabelPosition": "right",
                "tableView": false,
                "values": [
                    {
                        "label": "Hồng",
                        "value": "1",
                    },
                    {
                        "label": "Xám",
                        "value": "2",
                    },
                    {
                        "label": "Không soi được",
                        "value": "3",
                    },
                ],
            }
        }),
        JSONRadio("<b>15. Dịch kính</b>", "DICHKINHSACHMT", "", {
            others: {
                "optionsLabelPosition": "right",
                "inline": true,
                "labelPosition": "left-left",
                "optionsLabelPosition": "right",
                "tableView": false,
                "values": [
                    {
                        "label": "Sạch",
                        "value": "1",
                    },
                    {
                        "label": "Đục",
                        "value": "2",
                    },
                    {
                        "label": "Xuất huyết",
                        "value": "3",
                    },
                ],
            }
        }),
        {
            "label": "Tổn thương khác",
            "key": "DICHKINHTONTHUONGKHACMT",
            "type": "textfield",
            others: {
                "labelPosition": "left-left",
                "labelWidth": 10,
            },
        },
        JSONRadio("<b>16. Đáy mắt</b> đĩa thị", "DAYMATGAITHIMT", "", {
            others: {
                "optionsLabelPosition": "right",
                "inline": true,
                "labelPosition": "left-left",
                "optionsLabelPosition": "right",
                "tableView": false,
                "values": [
                    {
                        "label": "Không soi được",
                        "value": "1",
                    },
                    {
                        "label": "Bình thường",
                        "value": "2",
                    },
                    {
                        "label": "Lõm teo gai",
                        "value": "3",
                    },
                    {
                        "label": "Phù gai",
                        "value": "4",
                    },
                    {
                        "label": "Bạc màu gai thi",
                        "value": "5",
                    },
                ],
            }
        }),
        {
            "label": "C/D",
            "key": "DAYMATCDMT",
            "type": "textfield",
            others: {
                "labelPosition": "left-left",
                "labelWidth": 10,
            },
        },
        {
            "label": "Võng mạc",
            "key": "DAYMATVONGMACMT",
            "type": "textfield",
            others: {
                "labelPosition": "left-left",
                "labelWidth": 10,
            },
        },
        {
            "label": "Hệ mạch máu",
            "key": "DAYMATHEMACHMAUMT",
            "type": "textfield",
            others: {
                "labelPosition": "left-left",
                "labelWidth": 10,
            },
        },
        {
            "label": "Tổn thương khác",
            "key": "DAYMATTONTHUONGKHACMT",
            "type": "textfield",
            others: {
                "labelPosition": "left-left",
                "labelWidth": 10,
            },
        },


        JSONRadio("Toàn thân", "BENHLY", "", {
            others: {
                "optionsLabelPosition": "right",
                "inline": true,
                "labelPosition": "left-left",
                "tableView": false,
                "values": [
                    {
                        "label": "Chưa biểu hiện bệnh lý",
                        "value": "0",
                        "shortcut": ""
                    },
                    {
                        "label": "Bệnh lý",
                        "value": "1",
                        "shortcut": ""
                    }
                ],
            }
        }),
        {
            "label": "Bệnh lý",
            "key": "BENHLY_TEXT",
            "type": "textarea",
            others: {
                "labelPosition": "left-left",
                "labelWidth": 10,
                "customConditional": "show = data.BENHLY == 1;",
            }
        },
        {
            "label": "Các xét nghiệm cần làm",
            "key": "CLS",
            "type": "textarea",
            others: {
                "labelPosition": "left-left",
                "labelWidth": 10,
            },
        },
        {
            "label": "Tóm tắt bệnh án",
            "key": "TOMTAT_BENHAN",
            "type": "textarea",
            others: {
                "labelPosition": "left-left",
                "labelWidth": 10,
            },
        },


    ]
}

function getObjectMauMATBANPHANTRUOCTongket() {
    return [
        {
            "label": "ID",
            "key": "ID",
            "type": "textfield",
            others: {
                hidden: true
            }
        },
        {
            "label": "Tên mẫu",
            "key": "TENMAU",
            "type": "textarea",
            validate: {
                required: true
            },
            others: {
                "labelPosition": "left-left",
                "labelWidth": 10
            }
        },
        {
            "html": "<b>Thị lực ra viện không kính</b>",
            "label": "Thị lực ra viện không kính",
            "refreshOnChange": false,
            "key": "THILUCRAVIENKHONGKINH",
            "type": "content",
            "input": false,
            "tableView": false
        },
        {
            "label": "MP",
            "key": "rVThiLucKhongKinhMP",
            "type": "textfield",
            others: {
                "labelPosition": "left-left",
                "labelWidth": 10
            }
        },
        {
            "label": "MT",
            "key": "rVThiLucKhongKinhMT",
            "type": "textfield",
            others: {
                "labelPosition": "left-left",
                "labelWidth": 10
            }
        },
        {
            "html": "<b>Thị lực ra viện có kính</b>",
            "label": "Thị lực ra viện có kính",
            "refreshOnChange": false,
            "key": "THILUCRAVIENCOKINH",
            "type": "content",
            "input": false,
            "tableView": false
        },
        {
            "label": "MP",
            "key": "rVThiLucCoKinhMP",
            "type": "textfield",
            others: {
                "labelPosition": "left-left",
                "labelWidth": 10
            }
        },
        {
            "label": "MT",
            "key": "rVThiLucCoKinhMT",
            "type": "textfield",
            others: {
                "labelPosition": "left-left",
                "labelWidth": 10
            }
        },
        {
            "html": "<b>Nhãn áp ra viện</b>",
            "label": "Nhãn áp ra viện",
            "refreshOnChange": false,
            "key": "NHANAPRAVIEN",
            "type": "content",
            "input": false,
            "tableView": false
        },
        {
            "label": "MP",
            "key": "rVNhanApMP",
            "type": "textfield",
            others: {
                "labelPosition": "left-left",
                "labelWidth": 10
            }
        },
        {
            "label": "MT",
            "key": "rVNhanApMT",
            "type": "textfield",
            others: {
                "labelPosition": "left-left",
                "labelWidth": 10
            }
        },
        {
            "label": "Hướng điều trị tiếp theo",
            "key": "huongDieuTriVaCacCheDo",
            "type": "textarea",
            others: {
                "labelPosition": "left-left",
                "labelWidth": 10
            }
        },
    ]
}

function getObjectMauPage2PHATHAI() {
    return [
        {
            "label": "ID",
            "key": "ID",
            "type": "textfield",
            others: {
                hidden: true
            }
        },
        {
            "label": "Tên mẫu",
            "key": "TENMAU",
            "type": "textarea",
            validate: {
                required: true
            },
            others: {
                "labelPosition": "left-left",
                "labelWidth": 10
            }
        },
        {
            "label": "Lý do phá thai",
            "key": "LYDOPHATHAI",
            "type": "textfield",
            others: {
                "labelPosition": "left-left",
                "labelWidth": 10
            }
        },
        {
            "label": "PARA",
            "key": "PARA",
            "type": "textfield",
            others: {
                "labelPosition": "left-left",
                "labelWidth": 10
            }
        },
        {
            "label": "Số con hiện có",
            "key": "SOCONHIENCO",
            "type": "number",
            others: {
                "labelPosition": "left-left",
                "labelWidth": 10
            }
        },
        {
            "label": "Các phẫu thuật TC khác",
            "key": "CACPTTCKHAC",
            "type": "textfield",
            others: {
                "labelPosition": "left-left",
                "labelWidth": 10
            }
        },
        {
            "label": "Tiền sử bệnh",
            "key": "TIENSUBENH",
            "type": "textarea",
            others: {
                "labelPosition": "left-left",
                "labelWidth": 10,
            },
        },

        {
            "label": "Toàn thân",
            "key": "KHAMTOANTHAN",
            "type": "textarea",
            others: {
                "labelPosition": "left-left",
                "labelWidth": 10,
            },
        },
        {
            "label": "Các bộ phận",
            "key": "CACBOPHAN",
            "type": "textarea",
            others: {
                "labelPosition": "left-left",
                "labelWidth": 10,
            },
        },
        {
            "label": "Các xét nghiệm cần làm",
            "key": "CACXNCANLAM",
            "type": "textarea",
            others: {
                "labelPosition": "left-left",
                "labelWidth": 10,
            },
        },

        {
            "label": "Chẩn đoán: Tuổi thai",
            "key": "TUOITHAI",
            "type": "number",
            others: {
                "labelPosition": "left-left",
                "labelWidth": 10,
            },
        },
        {
            "label": "Phương pháp phá thai",
            "key": "KLPPPHATHAI",
            "type": "textarea",
            others: {
                "labelPosition": "left-left",
                "labelWidth": 10,
            },
        },
    ]
}

function getObjectMauTongketPHATHAI() {
    return [
        {
            "label": "ID",
            "key": "ID",
            "type": "textfield",
            others: {
                hidden: true
            }
        },
        {
            "label": "Tên mẫu",
            "key": "TENMAU",
            "type": "textarea",
            validate: {
                required: true
            },
            others: {
                "labelPosition": "left-left",
                "labelWidth": 10
            }
        },
        {
            "label": "Chẩn đoán: Thai",
            "key": "THAI",
            "type": "number",
            others: {
                "labelPosition": "left-left",
                "labelWidth": 10
            }
        },
        {
            "label": "Thời gian ở cơ sở thực hiện kỹ thuật phá thai: tổng số",
            "key": "TSTHOIGIANOCOSOTH",
            "type": "number",
            others: {
                "labelPosition": "left-left",
                "labelWidth": 10
            }
        },
        {
            "label": "Khám lại bất thường",
            "key": "KLBATTHUONG",
            "type": "textarea",
            others: {
                "labelPosition": "left-left",
                "labelWidth": 10
            }
        },
        {
            "label": "Khám lại theo hẹn",
            "key": "KLTHEOHEN",
            "type": "textarea",
            others: {
                "labelPosition": "left-left",
                "labelWidth": 10
            }
        },
        {
            "label": "Kết luận",
            "key": "KETLUAN",
            "type": "textarea",
            others: {
                "labelPosition": "left-left",
                "labelWidth": 10
            }
        },
    ]
}

function getObjectMauPage2NGOAITRURHM() {
    return [
        {
            "label": "ID",
            "key": "ID",
            "type": "textfield",
            others: {
                hidden: true
            }
        },
        {
            "label": "Tên mẫu",
            "key": "TENMAU",
            "type": "textarea",
            validate: {
                required: true
            },
            others: {
                "labelPosition": "left-left",
                "labelWidth": 10
            }
        },
        {
            "label": "Lý do vào viện",
            "key": "LYDOVAOVIEN",
            "type": "textarea",
            "validate": {
                "maxLength": 3000,
            },
            others: {
                "labelPosition": "left-left",
                "labelWidth": 10
            }
        },
        {
            "label": "Quá trình bệnh lý",
            "key": "QUATRINHBENHLY",
            "type": "textarea",
            "validate": {
                "maxLength": 3000,
            },
            others: {
                "labelPosition": "left-left",
                "labelWidth": 10
            }
        },
        {
            "label": "Tiền sử bản thân",
            "key": "TIENSUBANTHAN",
            "type": "textarea",
            "validate": {
                "maxLength": 3000,
            },
            others: {
                "labelPosition": "left-left",
                "labelWidth": 10
            }
        },
        {
            "label": "Tiền sử Gia đình",
            "key": "TIENSUGIADINH",
            "type": "textarea",
            "validate": {
                "maxLength": 3000,
            },
            others: {
                "labelPosition": "left-left",
                "labelWidth": 10
            }
        },
        {
            "label": "Toàn thân",
            "key": "KHAMTOANTHAN",
            "type": "textarea",
            "validate": {
                "maxLength": 3000,
            },
            others: {
                "labelPosition": "left-left",
                "labelWidth": 10
            }
        },
        {
            "label": "Bệnh chuyên khoa",
            "key": "BENHCHUYENKHOA",
            "type": "textarea",
            "validate": {
                "maxLength": 3000,
            },
            others: {
                "labelPosition": "left-left",
                "labelWidth": 10
            }
        },
        {
            "label": "Tóm tắt bệnh án",
            "key": "TOMTATBA",
            "type": "textarea",
            "validate": {
                "maxLength": 3000,
            },
            others: {
                "labelPosition": "left-left",
                "labelWidth": 10
            }
        },
        {
            "label": "Chẩn đoán của khoa khám bệnh",
            "key": "CHANDOAN_KHOAKHAMBENH",
            "type": "textarea",
            "validate": {
                "maxLength": 3000,
            },
            others: {
                "labelPosition": "left-left",
                "labelWidth": 10
            }
        },
        {
            "label": "Đã xử lý của tuyến dưới",
            "key": "XULY_TUYENDUOI",
            "type": "textarea",
            "validate": {
                "maxLength": 3000,
            },
            others: {
                "labelPosition": "left-left",
                "labelWidth": 10
            }
        },
    ]
}

function getObjectMauTongketNGOAITRURHM() {
    return [
        {
            "label": "ID",
            "key": "ID",
            "type": "textfield",
            others: {
                hidden: true
            }
        },
        {
            "label": "Tên mẫu",
            "key": "TENMAU",
            "type": "textarea",
            validate: {
                required: true
            },
            others: {
                "labelPosition": "left-left",
                "labelWidth": 10
            }
        },
        {
            "label": "Quá trình bệnh lý và diễn biến lâm sàng",
            "key": "quaTrinhBenhLy",
            "type": "textarea",
            "validate": {
                "maxLength": 3000,
            },
            others: {
                "labelPosition": "left-left",
                "labelWidth": 10
            }
        },
        {
            "label": "Tóm tắt KQ CLS có giá trị chẩn đoán",
            "key": "tomTatKetQuaXNCLS",
            "type": "textarea",
            "validate": {
                "maxLength": 3000,
            },
            others: {
                "labelPosition": "left-left",
                "labelWidth": 10
            }
        },
        {
            "label": "Phương pháp điều trị",
            "key": "phuongPhapDieuTri",
            "type": "textarea",
            "validate": {
                "maxLength": 3000,
            },
            others: {
                "labelPosition": "left-left",
                "labelWidth": 10
            }
        },
        {
            "label": "Tình trạng người bệnh ra viện",
            "key": "tinhTrangNguoiBenhRaVien",
            "type": "textarea",
            "validate": {
                "maxLength": 3000,
            },
            others: {
                "labelPosition": "left-left",
                "labelWidth": 10
            }
        },
        {
            "label": "Hướng điều trị và các chế độ tiếp theo",
            "key": "huongDieuTriVaCacCheDo",
            "type": "textarea",
            "validate": {
                "maxLength": 3000,
            },
            others: {
                "labelPosition": "left-left",
                "labelWidth": 10
            }
        },
    ]
}

function bindAutocompleteDienBienBenh(formInstance, componentKey, endpointKey = "CMU_DIENBIENBENH_SEL") {
    const elementId = getIdElmentFormio(formInstance, componentKey);
    $("#" + elementId).on('click', function () {
        const $textarea = $(this);
        const url = "cmu_getlist?url=" + convertArray([
            singletonObject.dvtt,
            thongtinhsba.thongtinbn.STT_BENHAN,
            endpointKey
        ]);

        $.get(url).done(function (data) {
            if (data && data.length > 0) {
                const sourceData = data.map(function (item) {
                    return {
                        label: item.DIENBIEN + " (Tờ điều trị số: " + (item.STT_DIEUTRI || "N/A") + ")",
                        value: item.DIENBIEN,
                        id: item.ID_DIEUTRI,
                        ten_dien_bien: item.DIENBIEN,
                        nguoi_tao: item.TEN_NGUOI_TAO
                    };
                });

                let dropdownHtml = '<div class="autocomplete-dropdown" style="position: absolute; z-index: 9999; background: white; border: 1px solid #ccc; max-height: 200px; overflow-y: auto; width: 300px;">';
                sourceData.forEach(function (item) {
                    dropdownHtml += '<div class="autocomplete-item" style="padding: 8px; cursor: pointer; border-bottom: 1px solid #eee;" data-value="' + item.value + '" data-id="' + item.id + '">' + item.label + '</div>';
                });
                dropdownHtml += '</div>';

                $('.autocomplete-dropdown').remove();
                $('body').append(dropdownHtml);

                const textareaOffset = $textarea.offset();
                $('.autocomplete-dropdown').css({
                    top: textareaOffset.top - $('.autocomplete-dropdown').outerHeight() - 5,
                    left: textareaOffset.left,
                    width: $textarea.outerWidth()
                });

                $('.autocomplete-item').on('click', function () {
                    const selectedValue = $(this).data('value');
                    const component = formInstance.getComponent(componentKey);
                    if (component) {
                        const currentValue = (component.getValue() || '').trim();
                        const newValue = currentValue + (currentValue ? ', ' : '') + selectedValue;
                        component.setValue(newValue);
                    }
                    $('.autocomplete-dropdown').remove();
                });

                $(document).on('click.autocomplete', function (e) {
                    if (!$(e.target).closest('.autocomplete-dropdown, #' + elementId).length) {
                        $('.autocomplete-dropdown').remove();
                        $(document).off('click.autocomplete');
                    }
                });

            } else {
                // notifiToClient("Red", "Không có dữ liệu diễn biến bệnh");
            }
        }).fail(function () {
            // notifiToClient("Red", "Lỗi khi tải dữ liệu diễn biến bệnh");
        });
    });
}

function getObjectMauPage2NGOAITRU_CHUNG() {
    return [
        {
            "label": "ID",
            "key": "ID",
            "type": "textfield",
            others: {
                hidden: true
            }
        },
        {
            "label": "Tên mẫu",
            "key": "TENMAU",
            "type": "textarea",
            validate: {
                required: true
            },
            others: {
                "labelPosition": "left-left",
                "labelWidth": 10
            }
        },
        {
            "label": "Lý do vào viện",
            "key": "LYDOVAOVIEN",
            "type": "textarea",
            "validate": {
                "maxLength": 3000,
            },
            others: {
                "labelPosition": "left-left",
                "labelWidth": 10
            }
        },
        {
            "label": "Quá trình bệnh lý",
            "key": "BENHSU",
            "type": "textarea",
            "validate": {
                "maxLength": 3000,
            },
            others: {
                "labelPosition": "left-left",
                "labelWidth": 10
            }
        },
        {
            "label": "Tiền sử bản thân",
            "key": "TIENSUBANTHAN",
            "type": "textarea",
            "validate": {
                "maxLength": 3000,
            },
            others: {
                "labelPosition": "left-left",
                "labelWidth": 10
            }
        },
        {
            "label": "Tiền sử Gia đình",
            "key": "TIENSUGIADINH",
            "type": "textarea",
            "validate": {
                "maxLength": 3000,
            },
            others: {
                "labelPosition": "left-left",
                "labelWidth": 10
            }
        },
        ,
        {
            "label": "Toàn thân",
            "key": "KHAMTOANTHAN",
            "type": "textarea",
            "validate": {
                "maxLength": 3000,
            },
            others: {
                "labelPosition": "left-left",
                "labelWidth": 10
            }
        },
        {
            "label": "Các bộ phận",
            "key": "CACBOPHAN",
            "type": "textarea",
            "validate": {
                "maxLength": 3000,
            },
            others: {
                "labelPosition": "left-left",
                "labelWidth": 10
            }
        },
        {
            "label": "Tóm tắt kết quả cận lâm sàng",
            "key": "TOMTATKETQUACLS",
            "type": "textarea",
            "validate": {
                "maxLength": 3000,
            },
            others: {
                "labelPosition": "left-left",
                "labelWidth": 10
            }
        },
        {
            "label": "Chẩn đoán ban đầu",
            "key": "CHANDOAN_BANDAU",
            "type": "textarea",
            "validate": {
                "maxLength": 3000,
            },
            others: {
                "labelPosition": "left-left",
                "labelWidth": 10
            }
        },
        {
            "label": "Đã xử lý(thuốc, chăm sóc)",
            "key": "DAXULY",
            "type": "textarea",
            "validate": {
                "maxLength": 3000,
            },
            others: {
                "labelPosition": "left-left",
                "labelWidth": 10
            }
        }
    ]
}
