function reloadDSPhieuCBTienPhau() {
    var url = "cmu_getlist?url=" + convertArray([singletonObject.dvtt, thongtinhsba.thongtinbn.SOVAOVIEN, "CMU_GET_PHIEU_CBI_TPHAU"]);
    $("#list_phieucbtienphau").jqGrid('setGridParam', {
        datatype: 'json',
        url: url
    }).trigger('reloadGrid')
}
$(function(){

    $("#initDSPhieucbtienphau").click(function() {
        var listPhieucbtienphau = $("#list_phieucbtienphau");
        if(!listPhieucbtienphau[0].grid) {
            $("#list_phieucbtienphau").jqGrid({
                url: "cmu_getlist?url=" + convertArray([singletonObject.dvtt, thongtinhsba.thongtinbn.SOVAOVIEN, 'CMU_GET_PHIEU_CBI_TPHAU']),
                datatype: "json",
                loadonce: true,
                height: 400,
                width: null,
                shrinkToFit: false,
                // ignoreCase: true,
                // caption: "Danh sách phiếu kiểm gạc",
                colModel: [
                    {
                        name: "KYSO",
                        label: "Ký số",
                        align: 'left',
                        width: 100,
                        formatter: function (cellValue, options, rowData) {
                            if (rowData.KEYSIGN) {
                                return '<span class="cellWithoutBackground" style="font-weight:bold ;color: green">Đã ký</span>';
                            } else {
                                return '<span class="cellWithoutBackground" style="font-weight:bold ;color: red">Chưa ký</span>';
                            }
                        }
                    },
                    {name: "ID", label: "Mã giấy", align: 'center', width: 100},
                    {name: "SOVAOVIEN", label: "Số vào viện", width: 150},
                    {name: "MA_BENH_NHAN", label: "Mã bệnh nhân", align: 'center', width: 150},
                    {name: "DOI_TUONG", label: "Đối tượng", align: 'center', width: 150},
                    {name: "THOI_GIAN_MO", label: "Thời gian mổ", align: 'center', width: 250},
                    {name: "TOM_TAT_BENH_AN", label: "Tóm tắt bệnh án", align: 'center', width: 150, hidden: true},
                    {name: "CHAN_DOAN", label: "Chẩn đoán", align: 'center', width: 200},
                    {name: "CACH_MO", label: "Cách mổ", align: 'center', width: 150, hidden: true},
                    {name: "BAC_SI", label: "Bacsi", align: 'center', width: 150, hidden: true},
                    {name: "PHU_MO", label: "Phumo", align: 'center', width: 250, hidden: true},
                    {name: "TONG_TRANG", label: "Tổng trạng", align: 'center', width: 150, hidden: true},
                    {name: "HA", label: "HA", align: 'center', width: 200, hidden: true},
                    {name: "MACH", label: "Mạch", align: 'center', width: 150, hidden: true},
                    {name: "CAN_NANG", label: "Cân nặng", align: 'center', width: 150, hidden: true},
                    {name: "TINH_TRANG_TIM", label: "Tim", align: 'center', width: 250, hidden: true},
                    {name: "TINH_TRANG_PHOI", label: "Phổi", align: 'center', width: 150, hidden: true},
                    {name: "HC", label: "HC", align: 'center', width: 200, hidden: true},
                    {name: "MC", label: "MC", align: 'center', width: 150, hidden: true},
                    {name: "HBSAG", label: "Hbsag", align: 'center', width: 150, hidden: true},
                    {name: "MACLAGAN", label: "Maclagan", align: 'center', width: 250, hidden: true},
                    {name: "BC", label: "BC", align: 'center', width: 150, hidden: true},
                    {name: "BUN", label: "BUN", align: 'center', width: 200, hidden: true},
                    {name: "DUONG_HUYET", label: "Đường huyết", align: 'center', width: 150, hidden: true},
                    {name: "SGOT", label: "SGOT", align: 'center', width: 150, hidden: true},
                    {name: "DTHC", label: "DTHC", align: 'center', width: 250, hidden: true},
                    {name: "CREATININE", label: "Creatinine", align: 'center', width: 150, hidden: true},
                    {name: "PROTID", label: "Protid", align: 'center', width: 200, hidden: true},
                    {name: "SGPT", label: "SGPT", align: 'center', width: 150, hidden: true},
                    {name: "MD", label: "MD", align: 'center', width: 150, hidden: true},
                    {name: "HIV", label: "HIV", align: 'center', width: 250, hidden: true},
                    {name: "GROS", label: "GROS", align: 'center', width: 150, hidden: true},
                    {name: "KHAC", label: "Khác", align: 'center', width: 200, hidden: true},
                    {name: "NUOC_TIEU", label: "Nước tiểu", align: 'center', width: 150, hidden: true},
                    {name: "DAM", label: "Đàm", align: 'center', width: 150, hidden: true},
                    {name: "KHAC_2", label: "Khác", align: 'center', width: 250, hidden: true},
                    {name: "BENH_SAN", label: "Bệnh sẵn", align: 'center', width: 150, hidden: true},
                    {name: "MAU_DU_TRU", label: "Máu dự trù", align: 'center', width: 200, hidden: true},
                    {name: "MAU", label: "Máu", align: 'center', width: 150, hidden: true},
                    {name: "NHOM", label: "Nhóm", align: 'center', width: 150, hidden: true},
                    {name: "RH", label: "RH", align: 'center', width: 250, hidden: true},
                    {name: "X_QUANG_PHOI", label: "X Quang phổi", align: 'center', width: 150, hidden: true},
                    {name: "X_QUANG_TIEU_HOA", label: "X Quang tiêu hóa", align: 'center', width: 200, hidden: true},
                    {name: "X_QUANG_XUONG", label: "X Quang xương", align: 'center', width: 150, hidden: true},
                    {name: "X_QUANG_BO_NIEU", label: "X Quang bộ niệu", align: 'center', width: 150, hidden: true},
                    {name: "SOI_BANG_QUANG", label: "Soi trực tràng", align: 'center', width: 250, hidden: true},
                    {name: "SOI_TRUC_TRANG", label: "Soi trực tràng", align: 'center', width: 150, hidden: true},
                    {name: "CAC_DIEN_DO", label: "Điện đồ", align: 'center', width: 200, hidden: true},
                    {name: "THE_NAM", label: "Thế nằm", align: 'center', width: 150, hidden: true},
                    {name: "TREO", label: "Tréo", align: 'center', width: 150, hidden: true},
                    {name: "NOI_RUA", label: "Nơi rửa", align: 'center', width: 250, hidden: true},
                    {name: "BAO_TAY", label: "Bao tay", align: 'center', width: 150, hidden: true},
                    {name: "BAO_CHAN", label: "Bao chân", align: 'center', width: 200, hidden: true},
                    {name: "BAN_XUONG", label: "Bàn xương", align: 'center', width: 150, hidden: true},
                    {name: "DOT", label: "Đốt", align: 'center', width: 200, hidden: true},
                    {name: "HUT", label: "Hút", align: 'center', width: 150, hidden: true},
                    {name: "BOT", label: "Bột", align: 'center', width: 150, hidden: true},
                    {name: "NOI_THAT_MACH", label: "Nội thất mạch", align: 'center', width: 250, hidden: true},
                    {name: "ESMARCH", label: "ESMARCH", align: 'center', width: 150, hidden: true},
                    {name: "DUNG_CU", label: "Dụng cụ", align: 'center', width: 200, hidden: true},
                    {name: "DUNG_CU_DAC_BIET", label: "Dụng cụ đặc biệt", align: 'center', width: 150, hidden: true},
                    {name: "X_QUANG_PHONG_MO", label: "X Quang phòng mổ", align: 'center', width: 150, hidden: true},
                    {name: "BAC_SI_DIEU_TRI", label: "Chẩn đoán bệnh", align: 'center', width: 250, hidden: true},
                    {name: "DE_NGHI", label: "Phương pháp vô cảm", align: 'center', width: 200, hidden: true},
                    {name: "TEN_BAC_SI", label: "Bác sĩ thực hiện", align: 'center', width: 200},
                    {name: "TEN_PHU_MO", label: "Phụ mổ", align: 'center', width: 200},
                    {name: "TEN_BAC_SI_DIEU_TRI", label: "Bác sĩ điều trị", align: 'center', width: 150, hidden: true},
                    {name: "TEN_BENH_NHAN", label: "Tên bệnh nhân", align: 'center', width: 200, hidden: true},
                    {name: "NGAY_TAO_PHIEU", label: "Ngày tạo phiếu", align: 'center', width: 150},
                    {name: "KEYSIGN", label: "KEYSIGN", align: 'center', width: 150, hidden: true},
                ],
                onRightClickRow: function () {
                    var ret = getThongtinRowSelected("list_phieucbtienphau");
                    var items = {
                    }
                    $.contextMenu('destroy', '#list_phieucbtienphau tr');
                    if (ret.KEYSIGN) {
                        items = {
                            "xem": {name: '<p><i class="fa fa-eye text-primary" aria-hidden="true"></i> Xem</p>'},
                            "huykyso": {name: '<p><i class="fa fa-key text-danger" aria-hidden="true"></i> Hủy ký số</p>'},
                            ...items
                        }
                    } else {
                        items = {
                            "kyso": {name: '<p><i class="fa fa-key text-success" aria-hidden="true"></i> Ký số</p>'},
                            "xem": {name: '<p><i class="fa fa-eye text-primary" aria-hidden="true"></i> Xem</p>'},
                            "sua": {name: '<i class="fa fa-pencil-square-o text-primary" aria-hidden="true"></i> Sửa</p>'},
                            "xoa": {name: '<p><i class="fa fa-trash-o text-danger" aria-hidden="true"></i> Xóa</p>'},
                            ...items
                        }
                    }
                    $.contextMenu({
                        selector: '#list_phieucbtienphau tr',
                        callback: function (key, options) {
                            var id = $("#list_phieucbtienphau").jqGrid('getGridParam', 'selrow');
                            var ret = $("#list_phieucbtienphau").jqGrid('getRowData', id);
                            if (key == "kyso") {
                                var text_date_part = ret.NGAY_TAO_PHIEU.split("/");
                                var text_date = text_date_part[0];
                                var text_month = text_date_part[1];
                                var text_year = text_date_part[2];
                                var params = {
                                    magiay: ret.ID,
                                    mabenhnhan: ret.MA_BENH_NHAN,
                                    text_date,
                                    text_month,
                                    text_year
                                }
                                var url = 'cmu_in_cmu_phieuchuanbitienphau?type=pdf&' + $.param(params);
                                kySoChung({
                                    dvtt: singletonObject.dvtt,
                                    userId: singletonObject.userId,
                                    url: url,
                                    loaiGiay: "PHIEU_NOITRU_CHUANBITIENPHAU",
                                    maBenhNhan: ret.MA_BENH_NHAN,
                                    soBenhAn: thongtinhsba.thongtinbn.STT_BENHAN,
                                    soPhieuDichVu: ret.ID,
                                    // nghiepVu: "",
                                    soVaoVien: thongtinhsba.thongtinbn.SOVAOVIEN,
                                    soVaoVienDT: thongtinhsba.thongtinbn.SOVAOVIEN_DT,
                                    keyword: "Bác sĩ điều trị",
                                    userId: singletonObject.userId,
                                }, function(dataKySo) {
                                    reloadDSPhieuCBTienPhau();
                                });
                            }
                            if (key == "huykyso") {
                                confirmToClient("Bạn có chắc sẽ hủy ký số phiếu này?", function() {
                                    huykysoFilesign769("PHIEU_NOITRU_CHUANBITIENPHAU", ret.ID, singletonObject.userId, singletonObject.dvtt,
                                        thongtinhsba.thongtinbn.SOVAOVIEN, thongtinhsba.thongtinbn.SOVAOVIEN_DT, -1, function(data) {
                                            reloadDSPhieuCBTienPhau();
                                        })
                                }, function () {

                                })
                            }
                            if (key == "xem") {
                                getFilesign769(
                                    "PHIEU_NOITRU_CHUANBITIENPHAU",
                                    ret.ID,
                                    -1,//singletonObject.userId,
                                    singletonObject.dvtt,
                                    thongtinhsba.thongtinbn.SOVAOVIEN,
                                    thongtinhsba.thongtinbn.SOVAOVIEN_DT,
                                    -1,
                                    function(data) {
                                        // hideLoaderIntoWrapId("hsba_list_todieutri");
                                        if(data.length > 0) {
                                            getCMUFileSigned769(data[0].KEYMINIO,"pdf")
                                        } else {
                                            var text_date_part = ret.NGAY_TAO_PHIEU.split("/");
                                            var text_date = text_date_part[0];
                                            var text_month = text_date_part[1];
                                            var text_year = text_date_part[2];
                                            var params = {
                                                magiay: ret.ID,
                                                mabenhnhan: ret.MA_BENH_NHAN,
                                                text_date,
                                                text_month,
                                                text_year
                                            }
                                            var url = 'cmu_in_cmu_phieuchuanbitienphau?type=pdf&' + $.param(params);
                                            var idFrame = "preview_phieuchuanbitienphau";

                                            previewPdfDefaultModal(url, idFrame);
                                        }
                                    }
                                )
                            }
                            if (key == "sua") {
                                $("#modalPhieuCBTienPhau").modal("show");
                                addTextTitleModal("titlePhieuCBTienPhau", " Phiếu chuẩn bị tiền phẫu");
                                $("#phieucbtienphau_luuphieu").attr("data-action", "CAP_NHAT");
                                $("#phieucbtienphau_luuphieu").html("<i class=\"fa fa-floppy-o\" aria-hidden=\"true\"></i> Cập nhật");

                                var dsBacSi = singletonObject.danhsachnhanvien;
                                initSelect2IfnotIntance("phieucbtienphau_tenbs", dsBacSi, 'MA_NHANVIEN', 'TEN_NHANVIEN', 0, 0, singletonObject.userId);
                                initSelect2IfnotIntance("phieucbtienphau_phumo", dsBacSi, 'MA_NHANVIEN', 'TEN_NHANVIEN', 0, 0, singletonObject.userId);
                                initSelect2IfnotIntance("phieucbtienphau_bacsidt", dsBacSi, 'MA_NHANVIEN', 'TEN_NHANVIEN', 0, 0, singletonObject.userId);
                                // console.log(Object.keys(ret));

                                Object.keys(ret).forEach(function(value) {
                                    if (value == "BAC_SI" || value == "PHU_MO" || value == "BAC_SI_DIEU_TRI"){
                                        $("#formPhieucbtienphau [name='"+value+"']").val(ret[value]).select2({
                                            dropdownParent: $('#formPhieucbtienphau'),
                                            minimumResultsForSearch: 0
                                        });
                                    }else{
                                        $("#formPhieucbtienphau [name='"+value+"']").val(ret[value])
                                    }
                                })


                            }
                            if (key == "xoa") {
                                var maGiay = ret.ID;
                                confirmToClient("Bạn có chắc chắn muốn xóa phiếu chuẩn bị tiền phẫu này?", function() {
                                    var arr = [maGiay, singletonObject.dvtt]
                                    var url = "cmu_post_CMU_PHIEU_CBI_TPHAU_DELETE";

                                    $.post(url, {
                                        url: arr.join("```")
                                    }).done(function (data) {
                                        if (data === "1") {
                                            reloadDSPhieuCBTienPhau();
                                            reloadSoluongPhieuCBTienPhau();
                                            notifiToClient("Green", "Xóa phiếu chuẩn bị tiền phẫu thành công");
                                        } else {
                                            notifiToClient("Red", "Lỗi xóa phiếu chuẩn bị tiền phẫu")
                                        }
                                    }).fail(function() {
                                        notifiToClient("Red", "Thao tác thất bại");
                                    })
                                }, function () {

                                })

                            }
                        },
                        items: items
                    })
                }
            });
            $('#list_phieucbtienphau').jqGrid('filterToolbar', {stringResult: true, searchOnEnter: false, defaultSearch: 'cn'});
        }
    })

    $(".themphieucbtienphau").click(function(){
        addTextTitleModal("titlePhieuCBTienPhau", " Phiếu chuẩn bị tiền phẫu");
        $("#modalPhieuCBTienPhau").modal("show");
        initFormDataPhieuCBTienPhau();
    })

    $("#phieucbtienphau_xemallxn").click(function () {
        $("#modalDSPhieuCOPYXN").modal("show");
        instanceGridCopyxn();
        loadDanhsachCopydbxnCBTP();
    })

    $(".phieucbtienphau_copyallxn").click(function () {
        var url = "cmu_list_CMU_NOI_XETNGHIEMDATH_LASTEST?url="+
            convertArray([
                thongtinhsba.thongtinbn.STT_BENHAN,
                thongtinhsba.thongtinbn.SOVAOVIEN,
                singletonObject.dvtt
            ]);
        $.get(url).done(function(data){
            if (data && data.length > 0) {
                $('.phieucbtienphau_xncopyall').each(function() {
                    var actionValue = $(this).data('action');
                    if (actionValue !== undefined) {
                        var matchedItem = data.find(item => item.MA_XETNGHIEM.toString() === actionValue.toString());
                        if (matchedItem) {
                            $(this).val(matchedItem.KET_QUA);
                        }
                    }
                });
            }
        });
    })

    $("#ttchamsoc-phieukhac").click(function(){
        reloadSoluongPhieuCBTienPhau();
    })

    $("#phieucbtienphau_luuphieu").on("click", function () {
        if($("#formPhieucbtienphau").valid()) {
            var btnAction = $('#phieucbtienphau_luuphieu').attr("data-action");
            showSelfLoading("phieucbtienphau_luuphieu");
            var res = convertDataFormToJson("formPhieucbtienphau");

            var doituong = res.DOI_TUONG;
            var thoigian = res.THOI_GIAN_MO;
            var tomtat = res.TOM_TAT_BENH_AN;
            var chandoan = res.CHAN_DOAN;
            var denghi = res.DE_NGHI;
            var cachmo = res.CACH_MO;
            var bacsi = res.BAC_SI;
            var phumo = res.PHU_MO;
            var tongtrang = res.TONG_TRANG;
            var ha = res.HA;
            var mach = res.MACH;
            var cannang = res.CAN_NANG;
            var tim = res.TINH_TRANG_TIM;
            var phoi = res.TINH_TRANG_PHOI;
            var hc = res.HC;
            var bc = res.BC;
            var dthc = res.DTHC;
            var md = res.MD;
            var mc = res.MC;
            var bun = res.BUN;
            var creatinine = res.CREATININE;
            var hiv = res.HIV;
            var hbsag = res.HBSAG;
            var duonghuyet = res.DUONG_HUYET;
            var protid = res.PROTID;
            var gros = res.GROS;
            var maclagan = res.MACLAGAN;
            var sgot = res.SGOT;
            var sgpt = res.SGPT;
            var mauhhkhac = res.KHAC;
            var nuoctieu = res.NUOC_TIEU;
            var dam = res.DAM;
            var khac = res.KHAC_2;
            var benhsan = res.BENH_SAN;
            var maudutru = res.MAU_DU_TRU;
            var mau = res.MAU;
            var nhom = res.NHOM;
            var rh = res.RH;
            var xquangphoi = res.X_QUANG_PHOI;;
            var xquangbonieu = res.X_QUANG_BO_NIEU;
            var xquangtieuhoa = res.X_QUANG_TIEU_HOA;
            var soibangquang = res.SOI_BANG_QUANG;
            var xquangxuong = res.X_QUANG_XUONG;
            var soitructrang = res.SOI_TRUC_TRANG;
            var diendo = res.CAC_DIEN_DO;
            var thenam = res.THE_NAM;
            var banxuong = res.BAN_XUONG;
            var treo = res.TREO;
            var dot = res.DOT;
            var noirua = res.NOI_RUA;
            var hut = res.HUT;
            var baotay = res.BAO_TAY;
            var baochan = res.BAO_CHAN;
            var bot = res.BOT;
            var noithatmach = res.NOI_THAT_MACH;
            var esmarch = res.ESMARCH;
            var dungcu = res.DUNG_CU;
            var dungcudb = res.DUNG_CU_DAC_BIET;
            var xquangcanth = res.X_QUANG_PHONG_MO;
            var bacsidt = res.BAC_SI_DIEU_TRI;
            // var ngaytao = $("#ngay_phieuycsdksinh").val();
            // var bacsi = $("#phieuycsdksinh_tenbs").val();

            var actionUrl;
            var url;

            if (btnAction == "THEM") {
                actionUrl = "cmu_post_CMU_PHIEU_CBI_TPHAU_INSERT";
                url = [
                    singletonObject.dvtt,
                    thongtinhsba.thongtinbn.SOVAOVIEN,
                    thongtinhsba.thongtinbn.MA_BENH_NHAN,
                    doituong,
                    thoigian,
                    tomtat,
                    chandoan,
                    cachmo,
                    bacsi,
                    phumo,
                    tongtrang,
                    ha,
                    mach,
                    cannang,
                    tim,
                    phoi,
                    hc,
                    mc,
                    hbsag,
                    maclagan,
                    bc,
                    bun,
                    duonghuyet,
                    sgot,
                    dthc,
                    creatinine,
                    protid,
                    sgpt,
                    md,
                    hiv,
                    gros,
                    mauhhkhac,
                    nuoctieu,
                    dam,
                    khac,
                    benhsan,
                    maudutru,
                    mau,
                    nhom,
                    rh,
                    xquangphoi,
                    xquangtieuhoa,
                    xquangxuong,
                    xquangbonieu,
                    soibangquang,
                    soitructrang,
                    diendo,
                    thenam,
                    treo,
                    noirua,
                    baotay,
                    baochan,
                    Number(banxuong),
                    Number(dot),
                    Number(hut),
                    Number(bot),
                    noithatmach,
                    esmarch,
                    dungcu,
                    dungcudb,
                    Number(xquangcanth),
                    bacsidt,
                    singletonObject.userId,
                    Number(denghi)
                ];
            } else {
                var idphieucbitienphau = $("#idphieucbtienphau").val();
                actionUrl = "cmu_post_CMU_PHIEU_CBI_TPHAU_UPDATE";
                url = [
                    idphieucbitienphau,
                    singletonObject.dvtt,
                    doituong,
                    thoigian,
                    tomtat,
                    chandoan,
                    cachmo,
                    bacsi,
                    phumo,
                    tongtrang,
                    ha,
                    mach,
                    cannang,
                    tim,
                    phoi,
                    hc,
                    mc,
                    hbsag,
                    maclagan,
                    bc,
                    bun,
                    duonghuyet,
                    sgot,
                    dthc,
                    creatinine,
                    protid,
                    sgpt,
                    md,
                    hiv,
                    gros,
                    mauhhkhac,
                    nuoctieu,
                    dam,
                    khac,
                    benhsan,
                    maudutru,
                    mau,
                    nhom,
                    rh,
                    xquangphoi,
                    xquangtieuhoa,
                    xquangxuong,
                    xquangbonieu,
                    soibangquang,
                    soitructrang,
                    diendo,
                    thenam,
                    treo,
                    noirua,
                    baotay,
                    baochan,
                    Number(banxuong),
                    Number(dot),
                    Number(hut),
                    Number(bot),
                    noithatmach,
                    esmarch,
                    dungcu,
                    dungcudb,
                    Number(xquangcanth),
                    bacsidt,
                    Number(denghi)

                ];
            }

            $.post(actionUrl, {
                url: url.join('```')
            }).done(function (data) {
                if (data == "1") {
                    notifiToClient("Green", "Thêm phiếu chuẩn bị tiền phẫu thành công");
                } else {
                    notifiToClient("Green", "Cập nhật phiếu chuẩn bị tiền phẫu thành công");
                }
            }).fail(function() {
                notifiToClient("Red", "Thao tác thất bại");
            }).always(function () {
                $("#modalPhieuCBTienPhau").modal("hide");
                resetModalFormPhieuCBTienPhau("formPhieucbtienphau", "phieucbtienphau_luuphieu")
                reloadDSPhieuCBTienPhau();
                reloadSoluongPhieuCBTienPhau();
                hideSelfLoading("phieucbtienphau_luuphieu");
            });
        }
    });


    $('#modalPhieuCBTienPhau').on('show.bs.modal', function() {
        $(".close").click(function () {
            resetModalFormPhieuCBTienPhau("formPhieucbtienphau", "phieucbtienphau_luuphieu");
        });
    })

    $(".close_phieucbtienphau").click(function () {
        resetModalFormPhieuCBTienPhau("formPhieucbtienphau", "phieucbtienphau_luuphieu");
    });

    $("#phieucbtienphau_maucbtp").click(function() {
        var element = $("#mau_danhsachmaujson_wrap");
        element.attr("function-add", 'insertMauHSBACBTIENPHAU');
        element.attr("function-chinhsua", 'editMauHSBACBTIENPHAU');
        element.attr("function-select", 'selectMauHSBACBTIENPHAU');
        element.attr("function-getdata", 'getdataMauHSBACBTIENPHAU');
        element.attr("function-validate", 'formioHSBACBTIENPHAUValidate');
        element.attr("data-key", 'MAUCBTIENPHAU');
        $("#modalMauChungJSON").modal("show");
        $.loadDanhSachMauChungJSON('MAUCBTIENPHAU')
    })

    // $("#formPhieucbtienphau").validate({
    //     rules: {
    //         THOI_GIAN_MO:{
    //             validDateTime: true,
    //         },
    //
    //     },
    //
    // })

    function initFormDataPhieuCBTienPhau(){

        var dsBacSi = singletonObject.danhsachnhanvien;
        initSelect2IfnotIntance("phieucbtienphau_tenbs", dsBacSi, 'MA_NHANVIEN', 'TEN_NHANVIEN', 0, 0, singletonObject.userId);
        initSelect2IfnotIntance("phieucbtienphau_phumo", dsBacSi, 'MA_NHANVIEN', 'TEN_NHANVIEN', 0, 0, singletonObject.userId);
        initSelect2IfnotIntance("phieucbtienphau_bacsidt", dsBacSi, 'MA_NHANVIEN', 'TEN_NHANVIEN', 0, 0, singletonObject.userId);


        // $("#phieuycsdksinh_tenbs").val(singletonObject.userId);
        // $("#phieuycsdksinh_tenbs").select2();

        // $("#phieucbtienphau_tenbs").val(singletonObject.userId);

        let idPSl2 = $("#formPhieucbtienphau");
        $("#phieucbtienphau_tenbs").select2({
            dropdownParent: idPSl2,
            minimumResultsForSearch: 0
        });
        $("#phieucbtienphau_phumo").select2({
            dropdownParent: idPSl2,
            minimumResultsForSearch: 0
        });
        $("#phieucbtienphau_bacsidt").select2({
            dropdownParent: idPSl2,
            minimumResultsForSearch: 0
        });


        $("#phieucbtienphau_thoigian").val(singletonObject.ngayhientai + " 00:00");


    }

    function reloadSoluongPhieuCBTienPhau(){
        var url1 = "cmu_getlist?url=" + convertArray([singletonObject.dvtt, thongtinhsba.thongtinbn.SOVAOVIEN, "CMU_GET_PHIEU_CBI_TPHAU"]);
        $.get(url1).done(function(data) {
            if (data && data.length > 0) {
                $("#data_soluongcbtienphau").html("Số phiếu: " + data.length + " phiếu");
            }else{
                $("#data_soluongcbtienphau").html("Hiện chưa có phiếu nào");
            }
        })
    }

    function resetModalFormPhieuCBTienPhau(formId, btnId) {
        // $(".custom-select").val(null).trigger('change');
        // $(".checkbox-group").prop("checked", false);
        $("#" + formId).trigger("reset");
        $("#" + btnId).attr("data-action", "THEM");
        $('#' + formId).validate().resetForm();
        $("#" + btnId).html("<i class=\"fa fa-floppy-o\" aria-hidden=\"true\"></i> Lưu phiếu");
    }

    $.extend({
        insertMauHSBACBTIENPHAU: function () {
            generateFormMauCBTIENPHAU({})
        },
        editMauHSBACBTIENPHAU: function (rowSelect) {
            var json = JSON.parse(rowSelect.NOIDUNG);
            var dataMau = {}
            json.forEach(function(item) {
                dataMau[item.key] = item.value
            })

            generateFormMauCBTIENPHAU({
                ID: rowSelect.ID,
                TENMAU: rowSelect.TENMAU,
                ...dataMau
            })
        },
        selectMauHSBACBTIENPHAU: function (rowSelect) {
            var json = JSON.parse(rowSelect.NOIDUNG);
            json.forEach(function(item) {
                $("#formPhieucbtienphau [name="+item.key+"]").val(item.value)
            })
            $("#modalMauChungJSON").modal("hide");
        },
        getdataMauHSBACBTIENPHAU: function () {
            var objectNoidung = [];
            getObjectMauCBTIENPHAU().forEach(function(item) {
                if(item.key != 'ID' && item.key != 'TENMAU') {
                    objectNoidung.push({
                        "label": item.label,
                        "value": formioMauHSBA.submission.data[item.key],
                        "key": item.key,
                    })
                }
            })
            return {
                ID: formioMauHSBA.submission.data.ID,
                TENMAU: formioMauHSBA.submission.data.TENMAU,
                NOIDUNG: JSON.stringify(objectNoidung),
                KEYMAUCHUNG: 'MAUCBTIENPHAU'
            };
        },
        formioHSBACBTIENPHAUValidate: function() {
            formioMauHSBA.emit("checkValidity");
            if (!formioMauHSBA.checkValidity(null, false, null, true)) {
                return false;
            }
            return true;
        },
    })

    function generateFormMauCBTIENPHAU(dataForm) {
        var jsonForm = getJSONObjectForm(getObjectMauCBTIENPHAU());
        Formio.createForm(document.getElementById('formChiTietMauChungJSON'),
            jsonForm,{}
        ).then(function(form) {
            formioMauHSBA = form;
            formioMauHSBA.submission = {
                data: {
                    ...dataForm
                }
            }
        });
    };
    function getObjectMauCBTIENPHAU() {
        return [
            {
                "label": "ID",
                "key": "ID",
                "type": "textfield",
                others: {
                    hidden: true
                }
            },
            {
                "label": "Tên mẫu",
                "key": "TENMAU",
                "type": "textarea",
                validate: {
                    required: true
                },
                others: {
                    "labelPosition": "left-left",
                    "labelWidth": 10
                }
            },
            {
                "label": "Tóm tắt bệnh án",
                "key": "TOM_TAT_BENH_AN",
                "type": "textarea",
                others: {
                    "labelPosition": "left-left",
                    "labelWidth": 10
                }
            },
            {
                "label": "Tổng trạng",
                "key": "TONG_TRANG",
                "type": "textfield",
                others: {
                    "labelPosition": "left-left",
                    "labelWidth": 10
                }
            },
            {
                "label": "HA",
                "key": "HA",
                "type": "textfield",
                others: {
                    "labelPosition": "left-left",
                    "labelWidth": 10
                }
            },
            {
                "label": "Mạch",
                "key": "MACH",
                "type": "number",
                others: {
                    "labelPosition": "left-left",
                    "labelWidth": 10
                }
            },
            {
                "label": "Tim",
                "key": "TINH_TRANG_TIM",
                "type": "textfield",
                others: {
                    "labelPosition": "left-left",
                    "labelWidth": 10
                }
            },
            {
                "label": "Phổi",
                "key": "TINH_TRANG_PHOI",
                "type": "textfield",
                others: {
                    "labelPosition": "left-left",
                    "labelWidth": 10
                }
            },
            {
                "label": "Thế nằm",
                "key": "THE_NAM",
                "type": "textfield",
                others: {
                    "labelPosition": "left-left",
                    "labelWidth": 10
                }
            },
            {
                "label": "Tréo",
                "key": "TREO",
                "type": "textfield",
                others: {
                    "labelPosition": "left-left",
                    "labelWidth": 10
                }
            },
            {
                "label": "Nơi rửa",
                "key": "NOI_RUA",
                "type": "textfield",
                others: {
                    "labelPosition": "left-left",
                    "labelWidth": 10
                }
            },
            {
                "label": "Bao tay",
                "key": "BAO_TAY",
                "type": "textfield",
                others: {
                    "labelPosition": "left-left",
                    "labelWidth": 10
                }
            },
            {
                "label": "Bao chân",
                "key": "BAO_CHAN",
                "type": "textfield",
                others: {
                    "labelPosition": "left-left",
                    "labelWidth": 10
                }
            },
            {
                "label": "Nơi thắt mạch",
                "key": "NOI_THAT_MACH",
                "type": "textfield",
                others: {
                    "labelPosition": "left-left",
                    "labelWidth": 10
                }
            },
            {
                "label": "Dụng cụ",
                "key": "DUNG_CU",
                "type": "textfield",
                others: {
                    "labelPosition": "left-left",
                    "labelWidth": 10
                }
            },
            {
                "label": "Bắn ESMARCH",
                "key": "ESMARCH",
                "type": "textfield",
                others: {
                    "labelPosition": "left-left",
                    "labelWidth": 10
                }
            },
            {
                "label": "Dụng cụ đặc biệt",
                "key": "DUNG_CU_DAC_BIET",
                "type": "textfield",
                others: {
                    "labelPosition": "left-left",
                    "labelWidth": 10
                }
            },
            {
                "label": "Hướng điều trị",
                "key": "HUONGDIEUTRI",
                "type": "textfield",
                others: {
                    "labelPosition": "left-left",
                    "labelWidth": 10
                }
            }];
    }

    function instanceGridCopyxn() {
        var list = $("#list_cbtienphauxn_copy");
        if(!list[0].grid) {
            list.jqGrid({
                url: '',
                datatype: "local",
                loadonce: true,
                height: 450,
                width: null,
                shrinkToFit: false,
                colModel: [
                    {label: "STT_DOTDIEUTRI",name: 'STT_DOTDIEUTRI', index: 'STT_DOTDIEUTRI', width: 10, hidden: true},
                    {label: "Mã XN",name: 'MA_XETNGHIEM', index: 'MA_XETNGHIEM', width: 50},
                    {label: "Ngày chỉ định",name: 'NGAYGIO_PHIEU', index: 'NGAYGIO_PHIEU', width: 120},
                    {label: "STT_DIEUTRI",name: 'STT_DIEUTRI', index: 'STT_DIEUTRI', width: 10, hidden: true},
                    {label: "CANHBAO",name: 'CANHBAO', index: 'CANHBAO', width: 10, hidden: true},
                    {label: "Tên xét nghiệm", name: 'TEN_XETNGHIEM', index: 'TEN_XETNGHIEM', width: 300},
                    {label: "Kết quả",name: 'KET_QUA', index: 'KET_QUA', width: 100,
                        formatter: function(cellvalue, options, rowObject) {
                            var text = rowObject.CANHBAO == -1? " (L)": rowObject.CANHBAO == 0? " (H)" : "";
                            return rowObject.KET_QUA + text;
                        },
                        cellattr: function (rowId, cellValue, rowObject) {
                            var color = "black";
                            if(rowObject.CANHBAO == -1) {
                                color = "blue";
                            }
                            if(rowObject.CANHBAO == 0) {
                                color = "red";
                            }
                            return "style='text-align:center; font-weight: bold; color:" + color + ";";
                        },
                    },
                    {label: "Khoảng tham chiếu", name: 'CHISOBINHTHUONG', index: 'CHISOBINHTHUONG', width: 200, align: "center"},
                    {label: "Đơn vị", name: 'DVNGHIEPVU_XETNGHIEM', index: 'DVNGHIEPVU_XETNGHIEM', width: 200, align: "center"},
                    {label: "Bác sĩ chỉ định",name: 'BSDIEUTRI', index: 'BSDIEUTRI', width: 150},
                    {label: "Người duyệt KQ",name: 'NGUOIDOCKQ', index: 'NGUOIDOCKQ', width: 150}
                ],
                rowNum: 1000,
                caption: "Danh sách xét nghiệm đã thực hiện ngày gần nhất",
                gridComplete: function() {
                },
                onRightClickRow: function(id) {
                }
            });
            list.jqGrid('filterToolbar', {stringResult: true, searchOnEnter: false, defaultSearch: "cn"});
        }
    }

    function loadDanhsachCopydbxnCBTP() {
        var url = "cmu_list_CMU_NOI_XETNGHIEMDATH_LASTEST?url="+
            convertArray([
                thongtinhsba.thongtinbn.STT_BENHAN,
                thongtinhsba.thongtinbn.SOVAOVIEN,
                singletonObject.dvtt
            ]);
        loadDataGridGroupBy($("#list_cbtienphauxn_copy"), url)
    }

})

