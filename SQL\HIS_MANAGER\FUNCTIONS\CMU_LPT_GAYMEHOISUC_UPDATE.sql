CREATE OR REPLACE FUNCTION cmu_lpt_gaymehoisuc_update (
    p_dvtt              VARCHAR2,
    p_id_lanphauthuat   VARCHAR2,
    p_tongmatmau        VARCHAR2,
    p_tongnuoctieu      VARCHAR2,
    p_tongnhiptho       VARCHAR2,
    p_tongme            VARCHAR2,
    p_phuongphapme      VARCHAR2,
    p_tenthuocme        VARCHAR2,
    p_nhanxet           VARCHAR2,
    p_maygayme          VARCHAR2,
    p_quansat           VARCHAR2,
    p_phongmo           VARCHAR2
) RETURN VARCHAR2 IS
BEGIN
UPDATE cmu_lpt_gaymehoisuc
SET
    tongmatmau = p_tongmatmau,
    tongnuoctieu = p_tongnuoctieu,
    tongnhiptho = p_tongnhiptho,
    tongme = p_tongme,
    phuongphapme = p_phuongphapme,
    tenthuocme = p_tenthuocme,
    nhanxet = p_nhanxet,
    maygayme = p_maygayme,
    quansat = p_quansat,
    phongmo = p_phongmo
WHERE
    dvtt = p_dvtt
  AND id_lanphauthuat = p_id_lanphauthuat;

RETURN SQL%rowcount;
END;