CREATE OR REPLACE FUNCTION ds_chuakyxemcls_theobn (
    p_dvtt           IN               VARCHAR2,
    p_sovaovien      IN               NUMBER,
    p_sovaovien_dt   IN               NUMBER
) RETURN SYS_REFCURSOR IS
    cur              SYS_REFCURSOR;
    v_thamso960590   VARCHAR2(50) := cmu_tsdv(p_dvtt, 960590, '1');
BEGIN
OPEN cur FOR SELECT DISTINCT
                     a.sobenhan,
                     a.ky_hieu_phieu,
                     a.so_phieu_dv,
                     a.ma_dich_vu,
                     a.nguoi_ky_kq,
                     a.ngay_ketqua,
                     a.keyminio,
                     a.ngay_ky,
                     a.sophieu_ngoaitru,
                     a.urlgetpdf,
                     a.keyword,
                     a.x1,
                     a.x,
                     a.y1,
                     a.y2,
                     a.kyhieukyso,
                     v_thamso960590   visibletype,
                     CASE
                         WHEN a.id_dieutri IS NULL THEN
                             - 1
                         ELSE
                             a.id_dieutri
END id_dieutri,
                     p_dvtt           dvtt
                 FROM
                     (
                         SELECT
                             ba.stt_benhan,
                             ba.sob<PERSON>han,
                             ba.sovaovien,
                             signkcb.ky_hieu_phieu,
                             signkcb.so_phieu_dv,
                             signkcb.ma_dich_vu,
                             nv.ten_nhanvien_cd nguoi_ky_kq,
                             TO_CHAR(signkcb.ngay_ky, 'DD/MM/YYYY HH24:MI:SS') ngay_ketqua,
                             cmu_checkphieukqdakyxem(p_dvtt, ba.sovaovien, p_sovaovien_dt, signkcb.ky_hieu_phieu, signkcb.so_phieu_dv
                             , signkcb.ma_dich_vu) dakyxem,
                             minio.keyminio,
                             signkcb.ngay_ky,
                             cdha.sophieu_ngoaitru,
                             CASE
                                 WHEN signkcb.ky_hieu_phieu = 'PHIEUKQ_CDHA_RISPACS'
                                      AND minio.keyminio IS NULL THEN
                                     'kyso/get-rispacsfile-minio'
                                 ELSE
                                     'kyso/get-file-minio'
                             END urlgetpdf,
                             CASE
                                 WHEN signkcb.ky_hieu_phieu IN (
                                     'PHIEUKQ_CDHA_RISPACS'
                                 ) THEN
                                     'Lời dặn của BS chuyên khoa'
                                 ELSE
                                     'KYXEMKQ'
                             END keyword,
                             '0' x1,
                             '160' x,
                             CASE
                                 WHEN v_thamso960590 = '1' THEN
                                     '0'
                                 ELSE
                                     '-15'
                             END y1,
                             CASE
                                 WHEN v_thamso960590 = '1' THEN
                                     '-65'
                                 ELSE
                                     '-80'
                             END y2,
                             CASE
                                 WHEN signkcb.ky_hieu_phieu IN (
                                     'PHIEUKQ_DIENTIM',
                                     'PHIEUKQ_CDHA_RISPACS'
                                 ) THEN
                                     'PHIEUKQ_CDHA_KYXEM'
                                 ELSE
                                     'PHIEUKQ_XETNGHIEM_KYXEM'
                             END kyhieukyso,
                             signkcb.id_dieutri
                         FROM
                             noitru_benhan               ba
                             JOIN smartca_signed_kcb          signkcb ON ba.dvtt = signkcb.dvtt
                                                                AND ba.sovaovien = signkcb.sovaovien
                                                                AND signkcb.status = 0
                                                                AND signkcb.ky_hieu_phieu IN (
                                 'PHIEUKQ_XETNGHIEM',
                                 'PHIEUKQ_DIENTIM',
                                 'PHIEU_KETQUA_XETNGHIEMTAIKHOA',
                                 'PHIEUKQ_CDHA_RISPACS'
                             )
                                                                AND signkcb.sovaovien = p_sovaovien
                                                                AND signkcb.dvtt = p_dvtt
                                                                AND signkcb.sovaovien_dt = p_sovaovien_dt
                             LEFT JOIN smartca_signed_file_minio   minio ON signkcb.dvtt = minio.dvtt
                                                                          AND signkcb.keysign = minio.keysign
                             LEFT JOIN his_fw.dm_nhanvien_cd       nv ON signkcb.ma_nhanvien_ky = nv.ma_nhanvien
                             LEFT JOIN noitru_cd_cdha              cdha ON signkcb.dvtt = cdha.dvtt
                                                              AND cdha.sovaovien = signkcb.sovaovien
                                                              AND cdha.sovaovien = p_sovaovien
                                                              AND cdha.dvtt = p_dvtt
                                                              AND cdha.sovaovien_dt = signkcb.sovaovien_dt
                                                              AND cdha.so_phieu_cdha = signkcb.so_phieu_dv
                         WHERE
                             ba.dvtt = p_dvtt
                             AND ba.sovaovien = p_sovaovien
                     ) a
                 WHERE
                     dakyxem = 0
                 ORDER BY
                     ngay_ky,
                     kyhieukyso;

RETURN cur;
END;