var cmuSmartcaVisibleType ;
function showMessageSignSmartca769(message, type) {
    if(typeof notifiToClient == "function") {
        notifiToClient(type,message)
    } else {
        jAlert(message)
    }
}

async function getFilesign769Async(kyhieuphieu, sophieu, userId, dvtt, sovaovien, sovaovien_dt, madv) {
    return new Promise((resolve, reject) => {
        $.ajax({
            url: "cmu_getlist?url=" + convertArray([
                dvtt,
                sovaovien,
                sovaovien_dt,
                kyhieuphieu,
                sophieu,
                userId,
                madv ? madv : -1,
                'CMU_SMART769_GET'
            ]),
            method: "GET",
            async: false
        }).done(function (data) {
            resolve(data);
        }).fail(function () {
            resolve({ERROR: true});
        });
    });
}

function doSignSmartca769(object, callbackFunction) {
    if(!cmuSmartcaVisibleType) {
        cmuSmartcaVisibleType = $.ajax({
            url: "cmu_post_cmu_tsdv",
            data: {
                url: [object.dvtt, 960590, 1].join('```')
            },
            method: "post",
            async: false
        }).responseText;
    }
    var madv = -1;
    if(object.madv != undefined) {
        madv = object.madv;
    }
    if(object.maDichVu != undefined) {
        madv = object.maDichVu;
    }
    $.get("cmu_getlist?url="+convertArray([
        object.dvtt,
        object.soVaoVien,
        object.soVaoVienDT,
        object.kyHieuPhieu,
        object.soPhieuDichVu,
        object.userId,
        madv,
        'CMU_SMART769_CHECK']))
        .done(function(data){
            var dataObject = data[0]
            if(dataObject.CAUHINH == 0 ) {
                if(typeof callbackFunction == "function") {
                    callbackFunction();
                }
                showMessageSignSmartca769("Chưa được cấu hình ký số smartca, vui lòng cấu hình tài khoản.", "red")
                return false;
            }
            if(data[0].DAKY > 0) {
                if(typeof callbackFunction == "function") {
                    callbackFunction();
                }
                showMessageSignSmartca769("Phiếu đã được ký", "red")
                return false;
            }
            if(data[0].DANGKY > 0) {
                if(typeof callbackFunction == "function") {
                    callbackFunction();
                }
                showMessageSignSmartca769("Phiếu đang được ký, vui lòng chờ trong giây lát", "red")
                return false;
            }
            if(dataObject.VERSION == 2) {
                var str = [dataObject.USERNAME, dataObject.PASS, "0"];
                var url = "smartca-kiem-tra-dang-nhap?url=" + convertArray(str);
                var response = $.ajax({
                    url: url, type: "POST", async: false
                }).responseText;
                if (response < 0 ) {
                    if (response == -1) {
                        return showMessageSignSmartca769("Không tìm thấy tài khoản SmartCA", "red")
                    } else {
                        return showMessageSignSmartca769("Đăng nhập thất bại", "red");
                    }
                }
            }
            var x = new XMLHttpRequest();
            x.onload = function() {
                // Create a form
                var reader = new FileReader();
                reader.readAsDataURL(x.response);
                reader.onloadend = function() {
                    var base64data = reader.result;
                    var refTranId = uuidv4();
                    var t = new Date();

                    var fd = new FormData();


                    fd.append("upfile", base64data);
                    fd.append("mabenhnhan", object.mabenhnhan);
                    fd.append("tenfile", t.getTime()+object.dvtt+object.soVaoVien+object.userId);
                    fd.append("keyword", object.keyword);

                    // Upload to your server
                    var y = new XMLHttpRequest();
                    y.onreadystatechange = function() {
                        if (y.readyState == XMLHttpRequest.DONE) {
                            var signTemplateList = [];
                            var comment = [];
                            var listPos = y.responseText.split(";");
                            var posiy1 = cmuSmartcaVisibleType == 3 || cmuSmartcaVisibleType == 4 ? -15: 15 ;
                            var posiy2 = cmuSmartcaVisibleType == 3 || cmuSmartcaVisibleType == 4 ? -80: -50 ;
                            for(var i = 0; i < listPos.length; i++) {
                                var pos = listPos[i].split(",")
                                if(pos.length > 2) {
                                    var x = (object.x? object.x : 120) + Number(pos[0]);
                                    var y2 = (object.y2? object.y2 : posiy2) + Number(pos[1]);
                                    var x1 =  (object.x1? object.x1 : -20) + Number(pos[0]);
                                    var y1 = (object.y1? object.y1 : posiy1)  + Number(pos[1]);
                                    var rect = x+","+y2+","+x1+","+y1
                                    signTemplateList.push({"rectangle":rect,"pageNumber": Number(pos[2])})
                                    if(object.kyHieuPhieu == 'PHIEU_NOITRU_VBATRANG3_NGUOIGIAO') {
                                        var dataForm = convertDataFormToJson("formthongtinbangiaohs");
                                        var tongSoLuong = 0;
                                        Object.keys(dataForm).forEach(function(key) {
                                            if(key != 'KHACTEXT' && !isNaN(Number(dataForm[key]))) {
                                                tongSoLuong += Number(dataForm[key]);
                                            }
                                        })
                                        var xr = -40 + Number(pos[0]);
                                        var y2r = -30 + Number(pos[1]);
                                        var x1r =  -90 + Number(pos[0]);
                                        var y1r =  -60  + Number(pos[1]);
                                        comment.push({
                                            "rectangle": xr+","+y2r+","+x1r+","+y1r,
                                            "page": Number(pos[2]),
                                            "text": dataForm.XQUANG,
                                            "fontsize": 13
                                        })
                                        var y2r1 = -50 + Number(pos[1]);
                                        var y1r1 =  -80  + Number(pos[1]);
                                        comment.push({
                                            "rectangle": xr+","+y2r1+","+x1r+","+y1r1,
                                            "page": Number(pos[2]),
                                            "text": dataForm.CT,
                                            "fontsize": 13
                                        })
                                        var y2r2 = -70 + Number(pos[1]);
                                        var y1r2 =  -100  + Number(pos[1]);
                                        comment.push({
                                            "rectangle": xr+","+y2r2+","+x1r+","+y1r2,
                                            "page": Number(pos[2]),
                                            "text": dataForm.SIEUAM,
                                            "fontsize": 13
                                        })
                                        var y2r3 = -90 + Number(pos[1]);
                                        var y1r3 =  -110  + Number(pos[1]);
                                        comment.push({
                                            "rectangle": xr+","+y2r3+","+x1r+","+y1r3,
                                            "page": Number(pos[2]),
                                            "text": dataForm.XN,
                                            "fontsize": 13
                                        })
                                        var y2r4 = -115 + Number(pos[1]);
                                        var y1r4 =  -145  + Number(pos[1]);
                                        comment.push({
                                            "rectangle": xr+","+y2r4+","+x1r+","+y1r4,
                                            "page": Number(pos[2]),
                                            "text": dataForm.KHAC,
                                            "fontsize": 13
                                        })

                                        comment.push({
                                            "rectangle": (xr-75)+","+y2r4+","+(x1r-125)+","+y1r4,
                                            "page": Number(pos[2]),
                                            "text": dataForm.KHACTEXT,
                                            "fontsize": 12
                                        })

                                        var y2r5 = -140 + Number(pos[1]);
                                        var y1r5 =  -170  + Number(pos[1]);
                                        comment.push({
                                            "rectangle": xr+","+y2r5+","+x1r+","+y1r5,
                                            "page": Number(pos[2]),
                                            "text": tongSoLuong,
                                            "fontsize": 13
                                        })
                                    }

                                }

                            }
                            if (signTemplateList.length == 0) {
                                if (typeof object.kyGocPdf != 'undefined' && object.kyGocPdf == 1) {
                                    signTemplateList.push({
                                        "rectangle": "150,10,10,75",
                                        "pageNumber": 1
                                    });
                                } else {
                                    if(typeof callbackFunction == "function") {
                                        callbackFunction();
                                    }
                                    showMessageSignSmartca769("Không tìm thấy vị trí ký số", "red");
                                    return false;
                                }
                            }
                            kysosmartca769(
                                base64data.replace("data:application/pdf;base64,", "")
                                    .replace("data:application/pdf;charset=UTF-8;base64,", ""),
                                {"imageSrc": ""},
                                object.fileName,
                                refTranId,
                                object.userId,
                                {
                                    kyHieuPhieu: object.kyHieuPhieu,
                                    maBenhNhan: object.maBenhNhan,
                                    maDichVu: object.maDichVu,
                                    nghiepVu: object.nghiepVu,
                                    noiTru: object.noiTru,
                                    soBe:-1,
                                    soBenhAn: object.soBenhAn,
                                    soPhieuDichVu: object.soPhieuDichVu,
                                    soVaoVien : object.soVaoVien,
                                    soVaoVienDT: object.soVaoVienDT,
                                    sttDotDieuTri: object.sttDotDieuTri,
                                    idDieuTri: object.idDieuTri,
                                    toaThuoc: object.toaThuoc,
                                    visibleType: object.visibleType
                                },
                                object.userName,
                                signTemplateList,
                                callbackFunction,
                                object.signXML? {
                                    xmlFile: object.signXML,
                                    nameFile: object.fileName,
                                    keySign: refTranId,
                                    xmlKey: refTranId+"-xml",
                                    kyHieuPhieu: object.kyHieuPhieu,
                                    isSign: "0"
                                } : null,
                                JSON.stringify(object.comment? object.comment : comment)

                            )
                        }
                    }

                    y.open('POST', 'cmu_getposition');
                    y.send(fd);
                }

            };
            x.responseType = 'blob';    // <-- This is necessary!
            x.open('GET', object.url, true);
            x.send();
        }).fail(function(){
        if(typeof callbackFunction == "function") {
            callbackFunction();
        }
        showMessageSignSmartca769("Lấy dữ liệu thất bại", "red");
    }).always(function(){})

}

function kysosmartca769(dataFile, options, scaNameFile, refTranId, userId, keyKcb, tenUser, signTemplateList, callbackFunction, signXML, comment) {
    var jsonparam = {
        nameFile: scaNameFile,
        base64PdfSign: dataFile,
        base64ImageSign: options.imageSrc,
        keySign: refTranId,
        vaiTroKySo: 1,
        soChuKyDaKy: 0,
        tongSoChuKy: 1,
        visibleType: keyKcb.visibleType ? keyKcb.visibleType : (cmuSmartcaVisibleType ? cmuSmartcaVisibleType : 1),
        signTemplateList: signTemplateList,
        fileName:scaNameFile,
        fontSize: 9,
        fullName: tenUser,
        keyKcb: keyKcb,
        comment: comment
    }

    if(signXML) {
        jsonparam.signXML = signXML;
    }
    showMessageSignSmartca769("Gửi thành công, vui lòng kiểm tra xác nhận trên điện thoại.", "green");

    $.ajax({
        type: "POST",
        url: "cmu-smartca-signed-hash-qd-769",
        dataType: "json",
        contentType: 'application/json',
        data: JSON.stringify(jsonparam),
        success: function (data) {
            if(data.SUCCESS > 0) {
                showMessageSignSmartca769("Ký thành công", "green");
                var pdf = 'data:application/pdf;base64,' + data.DATA;
                var link = document.createElement('a');
                if(typeof previewPdfDefaultModal == "function" && $("#modalPreviewPDF").length > 0) {
                    previewPdfDefaultModal(pdf, refTranId);
                } else {

                    link.href = pdf;
                    var t = new Date();

                    link.download=   refTranId + ".pdf";
                    link.click();
                }
            } else {
                showMessageSignSmartca769("Kí số lỗi", "red");
            }
            if(typeof callbackFunction == "function") {
                callbackFunction(jsonparam, data);
            }
        },
        error: function () {
            if(typeof callbackFunction == "function") {
                callbackFunction(jsonparam, {ERROR: 1});
            }
            $.post("cmu_post", {
                url: [
                    singletonObject.dvtt,
                    keyKcb.idUniq,
                    "CMU_KYSO_UNIQ_DEL"
                ].join("```")
            });
            showMessageSignSmartca769("Kí số lỗi", "red");
        }
    });
}

function downloadkyso769(keySigned, type) {
    var keyMinio = "";
    $.ajax({
        method: "POST", url: 'smartca-check-user-file-login-signed?keysign=' + keySigned + "&type=" + type
    }).done(function (res) {
        keyMinio = res.KEYMINIO !== undefined ? res.KEYMINIO : "";
        getCMUFileSigned769(keyMinio, type);
    }).fail(function() {
        showMessageSignSmartca769("Không tìm thấy file đã ký", "red");
    })
}

function getCMUFileSigned769(keyMinio, type) {
    var url = "smartca-get-signed-file-minio?keyminio=" + keyMinio + "&type=" + type;
    $.ajax({
        method: "POST", url: url, contentType: "charset=utf-8"
    }).done(function (data) {
        var pdf = 'data:application/pdf;base64,' +data.FILE;
        if(typeof previewPdfDefaultModal == "function" && $("#modalPreviewPDF").length > 0) {
            previewPdfDefaultModal(pdf, keyMinio.replace("SMARTCA/",""));
        } else {
            var link = document.createElement('a');
            link.href = pdf;
            var t = new Date();

            link.download=   keyMinio.replace("SMARTCA/","") + ".pdf";
            link.click();
        }

    }).fail(function() {
        showMessageSignSmartca769("Không tìm thấy file đã ký số", "red");
    });
}

function getCMUFileSigned769GetLink(keyMinio, type) {
    return new Promise(function (resolve, reject) {
        var url = "smartca-get-signed-file-minio?keyminio=" + keyMinio + "&type=" + type;
        $.ajax({
            method: "POST", url: url, contentType: "charset=utf-8"
        }).done(function (data) {
            var pdf = 'data:application/pdf;base64,' + data.FILE;
            resolve(pdf);
        }).fail(function (error) {
            reject(error);
        });
    });
}

function getCMUFileSigned769GetLinkV2(keyMinio, type) {
    var url = "smartca-get-signed-file-minio?keyminio=" + keyMinio + "&type=" + type;
    try {
        var res = $.ajax({
            method: "POST", url: url, contentType: "charset=utf-8", async: false
        }).responseText;
        return 'data:application/pdf;base64,' + JSON.parse(res).FILE;
    } catch(error) {
        return '-1';
    }
}

function huykysoFilesign769(kyhieuphieu, sophieu, userId, dvtt, sovaovien, sovaovien_dt, madv, callbackFunction) {
    getFilesign769(kyhieuphieu, sophieu, userId, dvtt, sovaovien, sovaovien_dt, madv, function(data) {
        if(data.length == 0) {
            callbackFunction({SUCCESS: 1});
            return showMessageSignSmartca769("Không tìm thấy file đã ký số", "red");
        }
        $.post("smartca-capnhat-huykyso?keysign="+data[0].KEYSIGN).done(function() {
            showMessageSignSmartca769("Hủy thành công", "green");
            if(typeof callbackFunction == "function") {
                callbackFunction({SUCCESS: 1});
            }
        }).fail(function() {
            if(typeof callbackFunction == "function") {
                callbackFunction({ERROR: 1});
            }
        })
    })
}
function huykysoFilesign769V2(kyhieuphieu, sophieu, userId, dvtt, sovaovien, sovaovien_dt, madv, callbackFunction) {
    getFilesign769V2(
        kyhieuphieu, sophieu, userId, dvtt, sovaovien, sovaovien_dt, madv, function(data) {
        if(data.length === 0) {
            callbackFunction({SUCCESS: 1});
            return showMessageSignSmartca769("Không tìm thấy file đã ký số", "red");
        }
        $.post("smartca-capnhat-huykyso?keysign="+data[0].KEYSIGN).done(function() {
            showMessageSignSmartca769("Hủy thành công", "green");
            if(typeof callbackFunction == "function") {
                callbackFunction({SUCCESS: 1});
            }
        }).fail(function() {
            if(typeof callbackFunction == "function") {
                callbackFunction({ERROR: 1});
            }
        })
    })
}
function getFilesign769(kyhieuphieu, sophieu, userId, dvtt, sovaovien, sovaovien_dt, madv, callbackFunction) {
    $.ajax({
        url: "cmu_getlist?url="+convertArray([
            dvtt,
            sovaovien,
            sovaovien_dt ? sovaovien_dt : 0,
            kyhieuphieu,
            sophieu,
            userId,
            madv? madv: -1,
            'CMU_SMART769_GET']),
        method: "GET",
        async: false
    }).done(function (data) {
        callbackFunction(data)
    }).fail(function() {
        callbackFunction({ERROR: true})
    });
}
function getFilesign769V2(kyhieuphieu, sophieu, userId, dvtt, sovaovien, sovaovien_dt, madv, callbackFunction) {
    $.ajax({
        url: "cmu_getlist?url="+convertArray([
            dvtt,
            sovaovien,
            sovaovien_dt ? sovaovien_dt : 0,
            kyhieuphieu,
            sophieu,
            userId,
            madv? madv: -1,
            'CMU_SMART769_GET_V2']),
        method: "GET",
        async: false
    }).done(function (data) {
        callbackFunction(data)
    }).fail(function() {
        callbackFunction({ERROR: true})
    });
}

function getFilesign769V3(data, callbackFunction) {
    $.ajax({
        url: "cmu_getlist?url="+convertArray([
            data.dvtt,
            data.soVaoVien,
            data.soVaoVien_DT,
            data.kyHieuPhieu,
            data.soPhieu,
            data.userId,
            data.maDV? data.maDV: -1,
            data.soBenhAn ? data.soBenhAn : "-1",
            data.nghiepVu ? data.nghiepVu : "-1",
            'CMU_SMART769_GET_V2']
        ),
        method: "GET",
        async: false
    }).done(function (data) {
        callbackFunction(data)
    }).fail(function() {
        callbackFunction({ERROR: true})
    });
}

function huykysoclsngoaitru(kyhieuphieu, sophieu, userId, dvtt, sovaovien, sovaovien_dt, madv, callbackFunction) {
    getFilesign769(kyhieuphieu, sophieu, userId, dvtt, sovaovien, sovaovien_dt, madv, function(data) {
        if(data.length == 0) {
            callbackFunction({SUCCESS: 1});
            return showMessageSignSmartca769("Không tìm thấy file đã ký số", "red");
        }
        $.post("smartca-capnhat-huykyso?keysign="+data[0].KEYSIGN).done(function() {
            showMessageSignSmartca769("Hủy thành công", "green");
            if(typeof callbackFunction == "function") {
                callbackFunction({SUCCESS: 1});
            }
        }).fail(function() {
            if(typeof callbackFunction == "function") {
                callbackFunction({ERROR: 1});
            }
        })
    })
}

function getFilesign769v2(data, callbackFunction) {
    $.get("cmu_getlist?url="+convertArray([
        data.dvtt,
        data.soVaoVien,
        data.soVaoVien_DT,
        data.kyHieuPhieu,
        data.soPhieu,
        data.userId,
        data.maDV? data.maDV: -1,
        data.soBenhAn ? data.soBenhAn : "-1",
        data.nghiepVu ? data.nghiepVu : "-1",
        'CMU_SMART769_GET'])).done(function(data) {
        callbackFunction(data)
    }).fail(function() {
        callbackFunction({ERROR: true})
    })
}

function getFilesign769GetLink(data) {
    return new Promise((resolve, reject) => {
        var url = "";
        $.get("cmu_getlist?url="+convertArray([
            data.dvtt,
            data.soVaoVien,
            data.soVaoVien_DT,
            data.kyHieuPhieu,
            data.soPhieu,
            data.userId,
            data.maDV? data.maDV: -1,
            data.soBenhAn ? data.soBenhAn : -1,
            data.nghiepVu ? data.nghiepVu : -1,
            'CMU_SMART769_GET'])
        ).done(function(data) {
            if(data.length == 0) {
                reject({ERROR: true})
            } else {
                getCMUFileSigned769GetLink(data[0].KEYMINIO, 'pdf').then(pdfData => {
                    resolve(pdfData);
                })
            }
        }).fail(function() {
            reject({ERROR: true})
        })
    });
}

function doSignSmartca769FileScan(object, callbackFunction) {
    $.get("cmu_getlist?url="+convertArray([
        object.dvtt,
        object.soVaoVien,
        object.soVaoVienDT,
        object.kyHieuPhieu,
        object.soPhieuDichVu,
        object.userId,
        object.madv? object.madv : "-1",
        'CMU_SMART769_CHECK']))
        .done(function(data){
            var dataObject = data[0]
            if(dataObject.CAUHINH == 0 ) {
                if(typeof callbackFunction == "function") {
                    callbackFunction();
                }
                showMessageSignSmartca769("Chưa được cấu hình ký số smartca, vui lòng cấu hình tài khoản.", "red")
                return false;
            }
            if(data[0].DAKY > 0) {
                if(typeof callbackFunction == "function") {
                    callbackFunction();
                }
                showMessageSignSmartca769("Phiếu đã được ký", "red")
                return false;
            }
            if(dataObject.VERSION == 2) {
                var str = [dataObject.USERNAME, dataObject.PASS, "0"];
                var url = "smartca-kiem-tra-dang-nhap?url=" + convertArray(str);
                var response = $.ajax({
                    url: url, type: "POST", async: false
                }).responseText;
                if (response < 0 ) {
                    if (response == -1) {
                        return showMessageSignSmartca769("Không tìm thấy tài khoản SmartCA", "red")
                    } else {
                        return showMessageSignSmartca769("Đăng nhập thất bại", "red");
                    }
                }
            }
            if (object.url) {
                var x = new XMLHttpRequest();
                x.onload = function() {
                    // Create a form
                    var reader = new FileReader();
                    reader.readAsDataURL(x.response);
                    reader.onloadend = function() {
                        var base64data = reader.result;
                        var refTranId = uuidv4();
                        var t = new Date();

                        var fd = new FormData();
                        fd.append("upfile", base64data);
                        fd.append("mabenhnhan", object.mabenhnhan);
                        fd.append("tenfile", t.getTime());
                        fd.append("keyword", object.keyword);

                        // Upload to your server
                        var y = new XMLHttpRequest();
                        y.onreadystatechange = function() {
                            if (y.readyState == XMLHttpRequest.DONE) {
                                var signTemplateList = [];
                                var listPos = y.responseText.split(";");
                                for(var i = 0; i < listPos.length; i++) {
                                    var pos = listPos[i].split(",")
                                    if(pos.length > 2) {
                                        var x = (object.x? object.x : 120) + Number(pos[0]);
                                        var y2 = (object.y2? object.y2 : -50) + Number(pos[1]);
                                        var x1 =  (object.x1? object.x1 : -20) + Number(pos[0]);
                                        var y1 = (object.y1? object.y1 : 15)  + Number(pos[1]);
                                        var rect = x+","+y2+","+x1+","+y1
                                        signTemplateList.push({"rectangle":rect,"pageNumber": Number(pos[2])})
                                    }
                                }
                                console.log(signTemplateList)
                                var readerScan = new FileReader();
                                readerScan.onload = function(readerEvt) {
                                    var binaryString = readerEvt.target.result;
                                    var base64String = btoa(binaryString);
                                    kysosmartca769(base64String.replace("data:application/pdf;base64,", ""),
                                        {"imageSrc": ""},
                                        object.fileName,
                                        refTranId,
                                        object.userId,
                                        {
                                            kyHieuPhieu: object.kyHieuPhieu,
                                            maBenhNhan: object.mabenhnhan,
                                            maDichVu: object.maDichVu,
                                            nghiepVu: object.nghiepVu,
                                            noiTru: object.noiTru,
                                            soBe:-1,
                                            soBenhAn: object.soBenhAn,
                                            soPhieuDichVu: object.soPhieuDichVu,
                                            soVaoVien : object.soVaoVien,
                                            soVaoVienDT: object.soVaoVienDT,
                                            sttDotDieuTri: object.sttDotDieuTri,
                                            idDieuTri: object.idDieuTri,
                                            toaThuoc: object.toaThuoc
                                        },
                                        object.userName,
                                        signTemplateList,
                                        callbackFunction
                                    )
                                };
                                readerScan.readAsBinaryString(object.fileScan);
                            }
                        }
                        y.open('POST', 'cmu_getposition');
                        y.send(fd);
                    }
                };
                x.responseType = 'blob';    // <-- This is necessary!
                x.open('GET', object.url, true);
                x.send();
            } else {
                var signTemplateList = [
                    {
                        "rectangle": "170,728,30,793",
                        "pageNumber": 1
                    }
                ]
                var refTranId = uuidv4();
                var readerScan = new FileReader();
                readerScan.onload = function(readerEvt) {
                    var binaryString = readerEvt.target.result;
                    var base64String = btoa(binaryString);
                    kysosmartca769(base64String.replace("data:application/pdf;base64,", ""),
                        {"imageSrc": ""},
                        object.fileName,
                        refTranId,
                        object.userId,
                        {
                            kyHieuPhieu: object.kyHieuPhieu,
                            maBenhNhan: object.mabenhnhan,
                            maDichVu: object.maDichVu,
                            nghiepVu: object.nghiepVu,
                            noiTru: object.noiTru,
                            soBe:-1,
                            soBenhAn: object.soBenhAn,
                            soPhieuDichVu: object.soPhieuDichVu,
                            soVaoVien : object.soVaoVien,
                            soVaoVienDT: object.soVaoVienDT,
                            sttDotDieuTri: object.sttDotDieuTri,
                            idDieuTri: object.idDieuTri,
                            toaThuoc: object.toaThuoc
                        },
                        object.userName,
                        signTemplateList,
                        callbackFunction
                    )
                };
                readerScan.readAsBinaryString(object.fileScan);
            }
        }).fail(function(){
        if(typeof callbackFunction == "function") {
            callbackFunction();
        }
        showMessageSignSmartca769("Lấy dữ liệu thất bại", "red");
    }).always(function(){})

}

function getImageBase64FingerCMU() {
    return new Promise(function(resolve, reject) {
        let fingerAudio = document.getElementById('fingerAudio');
        fingerAudio.play();
        showLoadingFinger({idWrap: "wrapThongTu50", text: "Xin mời lăn ngón tay!", timeout: 15000, type: "finger" })
        $.ajax({
            type: "GET",
            url: "http://localhost:8080/app/get-image-base64",
            async: true,
            complete: function(response) {
                var data = JSON.parse(response.responseText);
                hideLoadingFinger({idWrap: "wrapThongTu50"});
                if (data.CODE === 0) {
                    resolve(data.RESULT);
                } else {
                    reject(new Error(data.MESSAGE));
                }
            },
            error: function(jqXHR, exception) {
                hideLoadingFinger({idWrap: "wrapThongTu50"});
                reject(new Error("Lấy vân tay thất bại!"));
            }
        });
    });
}

function doSignSmartca769Finger(object, callbackFunction, callBackDone) {
    $.get("cmu_getlist?url="+convertArray([
        object.dvtt,
        object.soVaoVien ? object.soVaoVien : "-1",
        object.soVaoVienDT ? object.soVaoVienDT : "-1",
        object.sttDotDieuTri ? object.sttDotDieuTri : "-1",
        object.maBenhNhan ? object.maBenhNhan : "-1",
        object.soBenhAn ? object.soBenhAn : "-1",
        object.kyHieuPhieu,
        object.soPhieuDichVu ? object.soPhieuDichVu : "-1",
        object.nghiepVu ? object.nghiepVu : "-1",
        object.noiTru ? object.noiTru : "-1",
        object.userId,
        object.madv ? object.madv : "-1",
        object.trangThai ? object.trangThai : "0",
        'CMU_SMART769_CHECK_V2']))
        .done(function(data) {
            var dataObject = data[0]
            if (dataObject.CAUHINH == 0) {
                if (typeof callbackFunction == "function") {
                    callbackFunction();
                }
                showMessageSignSmartca769("Chưa được cấu hình ký số smartca, vui lòng cấu hình tài khoản.", "red")
                return false;
            }
            if (data[0].DAKY > 0) {
                if (typeof callbackFunction == "function") {
                    callbackFunction();
                }
                showMessageSignSmartca769("Phiếu đã được ký", "red")
                return false;
            }
            if (dataObject.VERSION == 2) {
                var str = [dataObject.USERNAME, dataObject.PASS, "0"];
                var url = "smartca-kiem-tra-dang-nhap?url=" + convertArray(str);
                var response = $.ajax({
                    url: url, type: "POST", async: false
                }).responseText;
                if (response < 0) {
                    if (response == -1) {
                        return showMessageSignSmartca769("Không tìm thấy tài khoản SmartCA", "red")
                    } else {
                        return showMessageSignSmartca769("Đăng nhập thất bại", "red");
                    }
                }
            }
            var x = new XMLHttpRequest();
            x.onload = function() {
                // Create a form
                var reader = new FileReader();
                reader.readAsDataURL(x.response);
                reader.onloadend = function() {
                    var base64data = reader.result;
                    var refTranId = uuidv4();
                    var t = new Date();

                    var fd = new FormData();
                    fd.append("upfile", base64data);
                    fd.append("mabenhnhan", object.maBenhNhan);
                    fd.append("tenfile", t.getTime());
                    fd.append("keyword", object.keyword);

                    // Upload to your server
                    var y = new XMLHttpRequest();
                    y.onreadystatechange = function() {
                        if (y.readyState == XMLHttpRequest.DONE) {
                            var signTemplateList = [];
                            var listPos = y.responseText.split(";");
                            for(var i = 0; i < listPos.length; i++) {
                                var pos = listPos[i].split(",")
                                if(pos.length > 2) {
                                    var llx = (object.llx? object.llx : 120) + Number(pos[0]);
                                    var lly = (object.lly? object.lly : -50) + Number(pos[1]);
                                    var urx =  (object.urx? object.urx : -20) + Number(pos[0]);
                                    var ury = (object.ury? object.ury : 15)  + Number(pos[1]);
                                    var rect = [llx, lly, urx, ury].join(',')
                                    signTemplateList.push({"rectangle":rect,"pageNumber": Number(pos[2])})
                                }
                            }
                            object.keySign = $.md5(JSON.stringify({
                                noiTru: object.noiTru,
                                maBenhNhan: object.maBenhNhan,
                                soVaoVien: object.soVaoVien,
                                soVaoVien_DT: object.soVaoVienDT,
                                sttDotDieuTri: object.sttDotDieuTri,
                                soPhieuDichVu: object.soPhieuDichVu,
                                kyHieuPhieu: object.kyHieuPhieu,
                                signDate: new Date().getTime()
                            }));
                            object.base64data = base64data;
                            object.signTemplateList = signTemplateList;
                            kySoVanTayBenhNhan(object, function (data) {
                                callBackDone(data);
                            }, function () {

                            });
                        }
                    }
                    y.open('POST', 'cmu_getposition');
                    y.send(fd);
                }
            };
            x.responseType = 'blob';
            x.open('GET', object.url, true);
            x.send();
        }).fail(function(){
        if(typeof callbackFunction == "function") {
            callbackFunction();
        }
        showMessageSignSmartca769("Lấy dữ liệu thất bại", "red");
    }).always(function(){})
}

function kySoVanTayBenhNhan(object, callBackDone, callBackFail) {
    var data = {
        fullName: object.tenNguoiKy !== "" ? object.tenNguoiKy : object.hoTenBenhNhan,
        signDate: object.signDate ? object.signDate : new Date().getTime(),
        base64ImageSign: object.imageSource.replaceAll("data:image/png;base64,", ""),
        signTemplateList: object.signTemplateList,
        visibleType: "1",
        base64PdfSign: object.base64data.replace("data:application/pdf;base64,", ""),
        fontSize: 5,
        keySign: object.keySign,
        vaiTroKySo: "",
        soChuKyDaKy: 1,
        tongSoChuKy: 1,
        fileName: object.fileName,
        keyKcb: {
            noiTru: object.noiTru ? object.noiTru : -1,
            maBenhNhan: object.maBenhNhan ? object.maBenhNhan : -1,
            soVaoVien: object.soVaoVien ? object.soVaoVien : -1,
            soVaoVienDT: object.soVaoVienDT ? object.soVaoVienDT : -1,
            soBenhAn: object.soBenhAn ? object.soBenhAn : -1,
            sttDotDieuTri: object.sttDotDieuTri ? object.sttDotDieuTri : -1,
            kyHieuPhieu: object.kyHieuPhieu,
            idDieuTri: object.idDieuTri ? object.idDieuTri : -1,
            soPhieuDichVu: object.soPhieuDichVu ? object.soPhieuDichVu : -1,
            ngay: object.ngay ? object.ngay : null,
            tuNgay: object.tuNgay ? object.tuNgay : null,
            denNgay: object.denNgay ? object.denNgay : null,
            maKhoa: object.maKhoa ? object.maKhoa : -1,
            tenKhoa: object.tenKhoa ? object.tenKhoa : "",
            nghiepVu: object.nghiepVu ? object.nghiepVu : -1,
            toaThuoc: object.toaThuoc ? object.toaThuoc : -1,
            soBe: object.babyNumber ? object.babyNumber : -1,
            maDichVu: object.maDichVu ? object.maDichVu : -1,
            inTong: object.inTong,
        }
    };
    $.ajax({
        type: "POST",
        contentType: "application/json",
        dataType: "json",
        url: "smartca-save-file-patient-v2",
        data: JSON.stringify(data),
        async: true,
        complete: function (response) {
            if (response.responseJSON.SUCCESS !== null && response.responseJSON.SUCCESS > 0 && response.responseJSON.KEYMINIO !== "") {
                var urlGetFileMinio = "smartca-get-signed-file-minio?keyminio=" + response.responseJSON.KEYMINIO + "&type=PDF";
                $.ajax({
                    method: "POST", url: urlGetFileMinio
                }).done(function (data) {
                    notifiToClient("Green", "Ký số thành công!");
                    $.hideSmartCALoading();
                    try {
                        $.hideScanner();
                    } catch(exception) {
                        console.log(exception);
                    }
                    var pdf = 'data:application/pdf;base64,' + data.FILE;
                    callBackDone(pdf);
                });
            } else {
                $.hideSmartCALoading();
                $.hideScanner();
                notifiToClient("Red", "Ký số không thành công!");
            }
        },
        error: function (jqXHR, exception) {
            $.hideSmartCALoading();
            try {
                $.hideScanner();
            } catch(exception) {
                console.log(exception);
            }
            notifiToClient("Red", "Ký số không thành công!");
        }
    });
}

function getPositionByKeyword(object, callback) {
    var x = new XMLHttpRequest();
    x.onload = function() {
        var reader = new FileReader();
        reader.readAsDataURL(x.response);
        reader.onloadend = function() {
            var base64data = reader.result;
            var refTranId = uuidv4();
            var t = new Date();
            var fd = new FormData();
            fd.append("upfile", base64data);
            fd.append("mabenhnhan", object.mabenhnhan);
            fd.append("tenfile", t.getTime());
            fd.append("keyword", object.keyword);
            var y = new XMLHttpRequest();
            y.onreadystatechange = function() {
                if (y.readyState == XMLHttpRequest.DONE) {
                    var signTemplateList = [];
                    var listPos = y.responseText.split(";");

                    callback(listPos);
                }
            }
            y.open('POST', 'cmu_getposition');
            y.send(fd);
        }
    };
    x.responseType = 'blob';    // <-- This is necessary!
    x.open('GET', object.url, true);
    x.send();
}