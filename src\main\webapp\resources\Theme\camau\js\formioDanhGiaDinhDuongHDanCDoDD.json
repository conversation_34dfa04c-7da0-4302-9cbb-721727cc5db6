{"display": "form", "components": [{"label": "MA_PHIEU", "key": "MA_PHIEU", "type": "hidden", "input": true}, {"label": "Columns", "columns": [{"components": [{"label": "<PERSON><PERSON><PERSON>", "customClass": "mr-2 formio-css-datetime formio-css-suffix luulog", "key": "NGAY_KY_BS", "type": "datetime", "format": "dd/MM/yyyy HH:mm", "timePicker": {"showMeridian": false}, "widget": {"enableTime": true, "format": "dd/MM/yyyy HH:mm", "time_24hr": true}}], "width": 2, "size": "md", "currentWidth": 2}, {"components": [{"label": "<PERSON><PERSON><PERSON><PERSON> th<PERSON> hi<PERSON>n", "widget": "<PERSON><PERSON><PERSON>", "customClass": "mr-2 luulog", "tableView": true, "dataSrc": "json", "template": "<span>{{ item.tennhanvien }}</span>", "validateWhenHidden": false, "key": "BAC_SI_DANH_GIA", "type": "select", "input": true}], "width": 4, "size": "md", "currentWidth": 4}, {"components": [{"label": "Ngày, giờ", "customClass": "mr-2 formio-css-datetime formio-css-suffix luulog", "key": "NGAY_GIO", "type": "datetime", "format": "dd/MM/yyyy HH:mm", "timePicker": {"showMeridian": false}, "widget": {"enableTime": true, "format": "dd/MM/yyyy HH:mm", "time_24hr": true}}], "width": 2, "size": "md", "currentWidth": 2}, {"components": [{"label": "Cân nặng", "customClass": "mr-2 formio-css-suffix luulog", "suffix": "kg", "tableView": true, "validateWhenHidden": false, "key": "CANNANG", "type": "textfield", "input": true}], "width": 2, "size": "md", "currentWidth": 2}, {"components": [{"label": "<PERSON><PERSON> SDD", "widget": "<PERSON><PERSON><PERSON>", "customClass": "formio-css-selection luulog", "tableView": true, "data": {"values": [{"label": "K<PERSON>ô<PERSON>", "value": "1"}, {"label": "<PERSON><PERSON>", "value": "2"}]}, "validateWhenHidden": false, "key": "NGUYCO_SDD", "type": "select", "input": true}], "width": 2, "size": "md", "currentWidth": 2}], "key": "columns", "type": "columns", "customClass": "ml-0 mr-0"}, {"label": "Columns", "columns": [{"components": [{"label": "ICD", "customClass": "mr-2 luulog", "tableView": true, "validateWhenHidden": false, "key": "ICD", "type": "textfield", "input": true}], "width": 2, "size": "md", "currentWidth": 2}, {"components": [{"label": "<PERSON><PERSON><PERSON> đ<PERSON>", "customClass": "lu<PERSON><PERSON>", "tableView": true, "validateWhenHidden": false, "key": "CHAN_DOAN", "type": "textfield", "input": true}], "width": 10, "size": "md", "currentWidth": 10}], "key": "columns", "type": "columns", "customClass": "ml-0 mr-0"}, {"label": "Columns", "columns": [{"components": [{"label": "Toàn trạng/Cơ", "customClass": "mr-2 formio-css-textarea formio-js-scale-textarea luulog", "tableView": true, "validateWhenHidden": false, "key": "TOAN_TRANG_CO", "type": "textarea", "input": true}], "width": 4, "size": "md", "currentWidth": 4}, {"components": [{"label": "<PERSON><PERSON><PERSON> n<PERSON>ng", "tooltip": "dung nạp/ thu nạp thức ăn", "widget": "<PERSON><PERSON><PERSON>", "customClass": "mr-2 formio-css-selection luulog", "tableView": true, "data": {"values": [{"label": "Ăn hết suất ăn", "value": "1"}, {"label": "Hạn chế/ kém", "value": "2"}]}, "validateWhenHidden": false, "key": "KHANANG", "type": "select", "input": true}], "width": 2, "size": "md", "currentWidth": 2}, {"components": [{"label": "đ<PERSON><PERSON>", "tooltip": "% suất ăn", "suffix": "%", "customClass": "mr-2 formio-css-suffix luulog", "tableView": true, "validateWhenHidden": false, "key": "DAT", "type": "textfield", "input": true}], "width": 2, "size": "md", "currentWidth": 2}, {"components": [{"label": "do", "tooltip": "Hạn chế/ kém", "widget": "<PERSON><PERSON><PERSON>", "customClass": "mr-2 formio-css-selection luulog", "tableView": true, "data": {"values": [{"label": "<PERSON><PERSON>", "value": "1"}, {"label": "B<PERSON><PERSON>n nôn/nôn", "value": "2"}, {"label": "Đau/chướng bụng", "value": "3"}, {"label": "<PERSON><PERSON><PERSON><PERSON>", "value": "4"}, {"label": "<PERSON><PERSON><PERSON> tồn lưu dạ dày", "value": "5"}]}, "validateWhenHidden": false, "key": "DO", "type": "select", "input": true}], "width": 2, "size": "md", "currentWidth": 2}, {"components": [{"label": "DD qua ống thông", "tooltip": "<PERSON><PERSON><PERSON> tồn lưu dạ dày", "suffix": "mL", "customClass": "formio-css-suffix luulog", "tableView": true, "validateWhenHidden": false, "key": "DD_QUA_ONGTHONG", "type": "textfield", "input": true}], "width": 2, "size": "md", "currentWidth": 2}], "key": "columns", "type": "columns", "customClass": "ml-0 mr-0"}, {"label": "Columns", "columns": [{"components": [{"label": "K<PERSON><PERSON><PERSON>", "customClass": "mr-2 formio-css-textarea formio-js-scale-textarea luulog", "tableView": true, "validateWhenHidden": false, "key": "KHAC", "type": "textarea", "input": true}], "width": 4, "size": "md", "currentWidth": 4}, {"components": [{"label": "<PERSON><PERSON> độ dinh dưỡng qua tiêu hóa", "customClass": "mr-2 formio-css-textarea formio-js-scale-textarea luulog", "tableView": true, "validateWhenHidden": false, "key": "CDDD_TIEUHOA", "type": "textarea", "input": true}], "width": 4, "size": "md", "currentWidth": 4}, {"components": [{"label": "<PERSON><PERSON><PERSON><PERSON>/<PERSON><PERSON><PERSON> t<PERSON>n dinh dưỡng", "customClass": "formio-css-textarea formio-js-scale-textarea luulog", "tableView": true, "validateWhenHidden": false, "key": "TH_DT_DD", "type": "textarea", "input": true}], "width": 4, "size": "md", "currentWidth": 4}], "key": "columns", "type": "columns", "customClass": "ml-0 mr-0"}]}