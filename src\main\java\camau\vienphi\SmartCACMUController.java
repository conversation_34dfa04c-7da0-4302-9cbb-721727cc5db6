package camau.vienphi;

import HOSOSUCKHOE.DuocCongdulieuyteDAO;

import camau.*;
import com.google.gson.JsonObject;
import com.google.gson.JsonParser;

import com.vnpt.vnptkyso.signer.DocumentType;
import dangnhap.SessionFilter;
import dangnhap.UserDAO;

import dmthamsodonvi.thamsodonviDAO;

import java.io.*;
import java.net.URISyntaxException;
import java.net.URL;
import java.net.URLDecoder;
import java.security.MessageDigest;
import java.sql.SQLException;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.Base64;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;
import javax.sql.DataSource;
import javax.swing.filechooser.FileSystemView;
import javax.xml.parsers.DocumentBuilder;
import javax.xml.parsers.DocumentBuilderFactory;

import khambenh.KhambenhDAO;
import kiengiang.KGGUtil;
import l2.L2Utils;
import l2.ThamSoManager;
import lichsusudung.LichsusudungDAO;
import lichsusudung.LichsusudungObj;
import logAction.LogActionDAO;
import net.sf.jasperreports.engine.*;
import net.sf.jasperreports.engine.export.ooxml.JRDocxExporter;
import net.sf.jasperreports.engine.util.JRLoader;
import net.sf.jasperreports.engine.util.JRProperties;
import net.sf.jasperreports.export.ExporterInputItem;
import net.sf.jasperreports.export.SimpleExporterInput;
import net.sf.jasperreports.export.SimpleExporterInputItem;
import net.sf.jasperreports.export.SimpleOutputStreamExporterOutput;
import org.apache.http.impl.client.HttpClients;
import org.apache.pdfbox.pdmodel.PDDocument;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.jdbc.datasource.DataSourceUtils;
import org.springframework.messaging.simp.SimpMessageSendingOperations;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.util.LinkedCaseInsensitiveMap;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.servlet.ModelAndView;
import org.w3c.dom.Document;
import org.xml.sax.InputSource;
import phanquyen.PhanquyenDAO;
import smartca.model.*;
import smartca.model.Account;
import smartca.model.Certificate;
import smartca.model.response.SmartCAQD769GetCertificateResponseDTO;
import smartca.request.SignRequest;
import smartca.service.SmartCADAO;
import smartca.service.SmartCAService;
import socket.ChatMessage;
import soctrang.BangkekcbtheokhoaDAO;
import soctrang.BaocaochiphiduocDAO;
import soctrang.BaocaothongkecanlamsangDAO;
import soctrang.dmphongban.STG_phongbanDAO;
import soctrang.vienphi.STG_VienphiObj;
import thamsohethong.Thamsohethong;
import tienich.docso;
import tienich.tienich;
import vienphi.VienphiDAO;
import vienphi.xemthutienvienphiDAO;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
//import static vungtau.BaoCaoHDCLSListExcelView_Unicode.dvtt;
import java.util.HashMap;
import java.util.ArrayList;
import java.net.HttpURLConnection;
import java.net.URLConnection;
import java.net.URL;
import java.util.*;
import java.util.Base64.Decoder;
import java.nio.charset.StandardCharsets;
import java.util.Enumeration;
import java.net.*;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.gson.Gson;
//import com.vnpt.hashsignature.signer.SignatureParameter;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.concurrent.TimeUnit;

import org.json.JSONArray;
import org.json.JSONObject;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.util.EntityUtils;


@Controller
public class SmartCACMUController {

    @Autowired
    SessionFilter SessionFilter;

    @Autowired
    VienphiDAO vienphiDAO;

    @Autowired
    VienphiCMUDAO VienphiCMUDAO;

    @Autowired
    KhambenhDAO khambenhDAO;

    @Autowired
    BaocaothongkecanlamsangDAO BaocaothongkecanlamsangDAO;

    @Autowired
    BangkekcbtheokhoaDAO BangkekcbtheokhoaDAO;

    @Autowired
    BaocaochiphiduocDAO BaocaochiphiduocDAO;

    @Autowired
    KhambenhDAO KhambenhDAO;

    @Autowired
    xemthutienvienphiDAO xemthutienvienphiDAO;

    @Autowired
    LichsusudungDAO LichsusudungDAO;
    @Autowired
    thamsodonviDAO thamsodonviDAO;

    @Autowired
    @Resource(name = "dataSourceMNG")
    DataSource dataSourceMNG;

    @Autowired
    @Resource(name = "dataSource_baocao")
    DataSource dataSource_baocao;

    //Ân thêm
    @Autowired
    phanquyen.PhanquyenDAO PhanquyenDAO;

    @Autowired
    UserDAO userDAO;

    @Autowired
    soctrang.dmphongban.STG_phongbanDAO STG_phongbanDAO;

    @Autowired
    SmartCADAO smartCADAO;

    @Autowired
    private SimpMessageSendingOperations messagingTemplate;

    @Autowired
    DuocCongdulieuyteDAO duocCongdulieuyteDAO;


    @Autowired
    LogActionDAO logActionDAO;

    @Autowired
    CMUSmartCAService cmuSmartCAService;


    public @ResponseBody
    @RequestMapping(value = "/cmu-sign-smartca", method = RequestMethod.POST, produces = "application/json; charset=utf-8")
    Map signSmartCA(@RequestBody String jsonParam, HttpSession session, HttpServletResponse httpServletResponse) throws UnknownHostException, UnsupportedEncodingException, InterruptedException {
        Map response = new HashMap();
        JsonParser jsonParser = new JsonParser();
        JsonObject paramObject = new JsonObject();
        paramObject = jsonParser.parse(jsonParam).getAsJsonObject();
        String nameFile = paramObject.get("nameFile").getAsString();
        String dataSigned = paramObject.get("dataSigned").getAsString();
        String dataBase64 = dataSigned.replaceAll("data:application/pdf;filename=generated.pdf;base64,", "");
        String dvtt = L2Utils.getDvtt(session);
        String userId = paramObject.get("userId").toString();
        InetAddress inetAddress = InetAddress.getLocalHost();
        String ipAddress = inetAddress.getHostAddress();
        Map smartCAAuth = smartCADAO.smartcaLayThongTinCauHinh(dvtt, userId);
        String username = smartCAAuth.containsKey("USERNAME") ? smartCAAuth.get("USERNAME").toString() : "";
        Map smartcaAccessToken = smartCADAO.smartcaLayThongTinToken(dvtt, username);
        String smartcaToken = smartcaAccessToken.getOrDefault("ACCESS_TOKEN", "").toString();
        String smartcaUrl = smartCAAuth.getOrDefault("URL", "").toString();
        CMUSmartCADetail smartCADetail = new CMUSmartCADetail();
        smartCADetail.setUrl(smartcaUrl);
        smartCADetail.setAccessToken(smartcaToken);
        if(paramObject.get("tranId") != null) {
            smartCADetail.setTranId(paramObject.get("tranId").getAsString());
        }
        try {

            SmartCACredential smartCACredential;
            smartCACredential = cmuSmartCAService.getCredential(smartCADetail);
            String[] content = smartCACredential.getContent();
            String credentialId = content[0];
            smartCADetail.setCredentialId(credentialId);
            CMUFileSign fileSign = new CMUFileSign(paramObject.get("options").getAsString(), nameFile, dataBase64);
            List<CMUFileSign> fileSigns = new ArrayList<CMUFileSign>();
            fileSigns.add(fileSign);
            smartCADetail.setSignDatas(fileSigns);
            Sign res = this.sign(smartCADetail);
            response.put("RESPONSE", res);
        } catch (Exception ex) {
            ex.printStackTrace();
            response.put("SUCCESS", -3);
            response.put("MESSAGE", ex.getMessage());
            return response;
        }
        return response;
    }
    public Sign sign(CMUSmartCADetail smartCADetail) {
        String respContent;
        Gson gson = new Gson();
        CloseableHttpClient client = HttpClients.createDefault();
        String notifyUrl = "https://yte-camau.vnpthis.vn/web_his/confirm-smartca";
        String description = "VNPT-HIS";
        String refTranId = smartCADetail.getTranId();
        CMUSignRequest signRequest = new CMUSignRequest(smartCADetail.getSignDatas(), smartCADetail.getCredentialId(), notifyUrl, description, refTranId);
        String para = gson.toJson(signRequest);
        Sign sign;
        try {
            HttpPost request = new HttpPost(smartCADetail.getUrl() + "/csc/signature/sign");
            String oauth = "Bearer" + " " + smartCADetail.getAccessToken();
            request.addHeader("Authorization", oauth);
            request.setHeader(HttpHeaders.CONTENT_TYPE, "application/json; charset=utf-8");
            request.setHeader(HttpHeaders.ACCEPT_ENCODING, "UTF-8");
            StringEntity params = new StringEntity(para, "UTF-8");
            request.setEntity(params);

            CloseableHttpResponse response = client.execute(request);
            respContent = EntityUtils.toString(response.getEntity());
            System.out.println(respContent);
            int code = response.getStatusLine().getStatusCode();
            if (200 != code) {
                System.out.println(respContent);
                return null;
            }
            sign = gson.fromJson(respContent, Sign.class);
            return sign;
        } catch (Exception ex) {
            ex.printStackTrace();
        } finally {
            try {
                client.close();
            } catch (IOException e) {
                e.printStackTrace();
            }
        }

        return null;
    }


    public static void writeToFile(byte[] input, String pathname) {
        FileOutputStream outStream = null;
        try {
            outStream = new FileOutputStream(new File(pathname));
            outStream.write(input);
        } catch (FileNotFoundException e) {
            e.printStackTrace();
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            if (outStream != null) {
                try {
                    outStream.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
    }

    public static String sha256(String base) {
        try {
            MessageDigest digest = MessageDigest.getInstance("SHA-256");
            byte[] hash = digest.digest(base.getBytes(StandardCharsets.UTF_8));
            StringBuffer hexString = new StringBuffer();

            for (int i = 0; i < hash.length; i++) {
                String hex = Integer.toHexString(0xff & hash[i]);
                if (hex.length() == 1) hexString.append('0');
                hexString.append(hex);
            }

            return hexString.toString();
        } catch (Exception ex) {
            throw new RuntimeException(ex);
        }
    }

    private static Document convertStringToXMLDocument(String xmlString) {
        //Parser that produces DOM object trees from XML content
        DocumentBuilderFactory factory = DocumentBuilderFactory.newInstance();

        //API to obtain DOM Document instance
        DocumentBuilder builder = null;
        try {
            //Create DocumentBuilder with default configuration
            builder = factory.newDocumentBuilder();

            //Parse the content to Document object
            Document doc = builder.parse(new InputSource(new StringReader(xmlString)));
            return doc;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    /* ---------------------------- QUYẾT ĐỊNH 769 -----------------------------------------------*/



    public @ResponseBody
    @PostMapping(value = "/cmu-smartca-signed-hash-qd-769")
    Map smartcaSaveFileSignedQD769V1(@RequestBody SignObj signHashDTO,
                                     HttpSession session,
                                     HttpServletRequest request) {
        String dvtt = L2Utils.getDvtt(session);
        String userId = L2Utils.getMaUser(session);
        Map<String, Object> result = new HashMap<String, Object>();
        Map checkValid = signHashDTO.isValid();
        int idUniqDangKy = 0;
        try {
            if (!Boolean.valueOf(checkValid.get("valid").toString())) {
                result.put("SUCCESS", "-10");
                result.put("KEYMINIO", null);
                result.put("MESSAGE", checkValid.get("message").toString());
                return result;
            }
            String iconValid = request.getSession().getServletContext().getRealPath("/resources/Theme/camau/valid-icon.png");
            Date signedDateV2 = new Date();
            String nameSign = session.getAttribute("Sess_User").toString();
            String timesFont =session.getServletContext().getRealPath("/WEB-INF/classes/font/times.ttf");
            String base64SourceAfterAddImage = cmuSmartCAService.addImageBoxToPdfFromBase64ItextV2(signHashDTO.getBase64PdfSign(),
                    iconValid,
                    signHashDTO.getSignTemplateList(),
                    nameSign,
                    signedDateV2,
                    timesFont,
                    signHashDTO.getVisibleType(),
                    signHashDTO.getFontSize().equals("") ? "9" : signHashDTO.getFontSize()
            );
            byte[] bytes =java.util.Base64.getDecoder().decode(base64SourceAfterAddImage);
            Map smartCAAuth = smartCADAO.smartcaLayThongTinCauHinh(dvtt, userId);
            String userName = smartCAAuth.containsKey("USERNAME") ? smartCAAuth.get("USERNAME").toString() : "";
            Map smartcaAccessToken = smartCADAO.smartcaLayThongTinToken(dvtt, userName);
            if (smartcaAccessToken == null) {
                smartcaAccessToken = new HashMap();
            }
            String smartcaToken = smartcaAccessToken.getOrDefault("ACCESS_TOKEN", "").toString();
            String smartCAVersion = smartCAAuth.getOrDefault("SMARTCA_VERSION", "").toString();
            String smartCAUrl = smartCAAuth.getOrDefault("URL", "").toString();
            String smartCAGrantType = smartCAAuth.getOrDefault("GRANT_TYPE", "").toString();
            String smartCAClientId = smartCAAuth.getOrDefault("CLIENT_ID", "").toString();
            String smartCAClientSecret = smartCAAuth.getOrDefault("CLIENT_SECRET", "").toString();
            String password = smartCAAuth.getOrDefault("PASSWORD", "").toString();
            String urlQD769 = smartCAAuth.getOrDefault("URL_QD_769", "").toString();
            String serialSmartCA = smartCAAuth.getOrDefault("SERIAL_SMARTCA", "").toString();
            String tOtp = smartCAVersion.equals("2") ? smartCAAuth.getOrDefault("TOTP", "").toString() : null;
            String urlESeal = smartCAVersion.equals("2") ? smartCAAuth.getOrDefault("URL_ESEAL", "").toString() : null;
            Map validAccount = isValidAccount(smartCAAuth);
            if(!Boolean.valueOf(validAccount.get("valid").toString())) {
                result.put("SUCCESS", "-11");
                result.put("KEYMINIO", null);
                result.put("MESSAGE", validAccount.get("message").toString());
                return result;
            }
            SmartCADetail smartCADetail = new SmartCADetail(
                    smartCAGrantType,
                    userName,
                    password,
                    smartCAUrl,
                    smartCAClientId,
                    smartCAClientSecret,
                    smartcaToken,
                    null,
                    null,
                    null,
                    null,
                    urlESeal,
                    tOtp,
                    urlQD769,
                    serialSmartCA
            );
            try {
                Map<String, Object> signHashPdfResult = new HashMap<>();

                if (signHashDTO.getKeyKcb() != null) {
                    SignObj.SmartCAKeyKcb keyKcb = signHashDTO.getKeyKcb();
                    String soVaoVien = Objects.toString(keyKcb.getSoVaoVien(), null);
                    String soVaoVienDieuTri = Objects.toString(keyKcb.getSoVaoVienDT(), null);
                    String kyHieuPhieu = keyKcb.getKyHieuPhieu();
                    String soPhieuDichVu = keyKcb.getSoPhieuDichVu();
                    String maDichVu = Objects.toString(keyKcb.getMaDichVu(), "-1");
                    idUniqDangKy = smartCADAO.cmuKhoaPhieuDangKy(dvtt, soVaoVien, soVaoVienDieuTri, kyHieuPhieu, soPhieuDichVu, maDichVu, userId);
                }
                if(smartCAVersion.equals("2")) {
                    signHashPdfResult = cmuSmartCAService.signHashPdfV2(smartCADetail, bytes,
                            iconValid,
                            signHashDTO.getVisibleType(),
                            signHashDTO.getFullName(),
                            signHashDTO.getFontSize().equals("") ? "13" : signHashDTO.getFontSize(),
                            signHashDTO.getComment(),
                            signHashDTO.getSignTemplateList(), signHashDTO.getFileName(), dvtt, signHashDTO.getKeySign(), userId);
                } else  {
                    signHashPdfResult = cmuSmartCAService.signHashPdfV1(smartCADetail, bytes,
                            iconValid,
                            signHashDTO.getVisibleType(),
                            signHashDTO.getFullName(),
                            signHashDTO.getFontSize().equals("") ? "13" : signHashDTO.getFontSize(),
                            signHashDTO.getComment(),
                            signHashDTO.getSignTemplateList(), signHashDTO.getFileName(), dvtt, signHashDTO.getKeySign(), userId);
                }

                if (!(signHashPdfResult.containsKey("signData") && signHashPdfResult.containsKey("signDate") && signHashPdfResult.containsKey("certificate"))) {
                    result.put("SUCCESS", signHashPdfResult.get("errorCode"));
                    result.put("MESSAGE", signHashPdfResult.get("errorMessage"));
                    if (idUniqDangKy > 0) {
                        smartCADAO.cmuMoKhoaPhieuDangKy(dvtt, idUniqDangKy);
                    }
                    return result;
                }

                byte[] signedData = signHashPdfResult.get("signData") != null ? (byte[]) signHashPdfResult.get("signData") : null;
                Date signedDate = signHashPdfResult.get("signDate") != null ? (Date) signHashPdfResult.get("signDate") : null;

                String certificateString = "";
                SmartCAQD769GetCertificateResponseDTO.Data.Certificate certificateResult = signHashPdfResult.get("certificate") != null ? (SmartCAQD769GetCertificateResponseDTO.Data.Certificate) signHashPdfResult.get("certificate") : null;
                certificateString = new Gson().toJson(certificateResult);
                String base64Signed = java.util.Base64.getEncoder().encodeToString(signedData);
                String sha256Signed = sha256(base64Signed);
                String sha256File = tienich.getMD5(base64Signed);
                String keyMinio = smartCADAO.smartcaSaveFileSigned(dvtt, signHashDTO.getKeySign(), sha256Signed, base64Signed, sha256File, signHashDTO.getFileName());
                if (!keyMinio.isEmpty()) {
                    int idUserSigned = smartCADAO.smartcaSaveUserSigned(dvtt, signHashDTO.getKeySign(), userId, 1, signHashDTO.getVaiTroKySo(), certificateString, signHashDTO.getBase64ImageSign());
                    if (signHashDTO.getKeyKcb() != null) {
                        SignObj.SmartCAKeyKcb keyKcb = signHashDTO.getKeyKcb();
                        String noiTru = Objects.toString(keyKcb.getNoiTru(), null);
                        String maBenhNhan = Objects.toString(keyKcb.getMaBenhNhan(), null);
                        String soVaoVien = Objects.toString(keyKcb.getSoVaoVien(), null);
                        String soVaoVienDieuTri = Objects.toString(keyKcb.getSoVaoVienDT(), null);
                        String soBenhAn = Objects.toString(keyKcb.getSoBenhAn(), null);
                        String sttDotDieuTri = Objects.toString(keyKcb.getSttDotDieuTri(), null);
                        String kyHieuPhieu = keyKcb.getKyHieuPhieu();
                        String idDieuTri = Objects.toString(keyKcb.getIdDieuTri(), null);
                        String soPhieuDichVu = keyKcb.getSoPhieuDichVu();
                        String ngay = Objects.toString(keyKcb.getNgay(), null);
                        String tuNgay = Objects.toString(keyKcb.getTuNgay(), null);
                        String denNgay = Objects.toString(keyKcb.getDenNgay(), null);
                        String maKhoa = Objects.toString(keyKcb.getMaKhoa(), null);
                        String tenKhoa = keyKcb.getTenKhoa();
                        String nghiepVu = keyKcb.getNghiepVu();
                        String toaThuoc = keyKcb.getToaThuoc();
                        String soBe = Objects.toString(keyKcb.getSoBe(), "-1");
                        String maDichVu = Objects.toString(keyKcb.getMaDichVu(), "-1");
                        String inTong = Objects.toString(keyKcb.getInTong(), null);

                        int idSignedKcb = smartCADAO.smartcaSaveSignedKcb(dvtt, signHashDTO.getKeySign(), noiTru, maBenhNhan, soVaoVien, soVaoVienDieuTri,
                                soBenhAn, sttDotDieuTri, kyHieuPhieu, idDieuTri, soPhieuDichVu, ngay, tuNgay, denNgay, maKhoa, tenKhoa, nghiepVu, toaThuoc,
                                inTong, userName, userId, 1, 0, soBe, maDichVu, "", 0);
                        String pattern = "yyyy-MM-dd HH:mm:ss";
                        SimpleDateFormat simpleDateFormat = new SimpleDateFormat(pattern);
                        int resComplete = smartCADAO.smartcaUpdateFileComplete(dvtt, signHashDTO.getKeySign(), String.valueOf(signHashDTO.getSoChuKyDaKy()), String.valueOf(signHashDTO.getTongSoChuKy()), simpleDateFormat.format(signedDate), userId, 0);
                    }
                }
                if (idUniqDangKy > 0) {
                    smartCADAO.cmuMoKhoaPhieuDangKy(dvtt, idUniqDangKy);
                }
                /**init file sign XML
                 *Nếu là XML của EMR thì không gửi request ký số*/
                SignObj.SignXML signXML = signHashDTO.getSignXML();

                if (signXML != null && signXML.getIsSign().equals("0")){
                    InetAddress inetAddress = InetAddress.getLocalHost();
                    String ipAddress = inetAddress.getHostAddress();
                    Map certificate = smartCADAO.smartcaGetCertificate(dvtt, signHashDTO.getKeySign(), signHashDTO.getFileName(), userId);
                    Map responseFile = cmuSmartCAService.signFakeXml(dvtt, signXML, certificate);
                    try {
                        int idUserSigned = 0;
                        String dataSigned = responseFile.get("dataSigned").toString();
                        String base64Data = responseFile.get("base64Data").toString();
                        sha256File = tienich.getMD5(signXML.getXmlFile());
                        sha256Signed = tienich.getMD5(dataSigned);
                        int idSignedFile = smartCADAO.smartcaSaveFileSignedXml(dvtt, signXML.getKeySign(), sha256Signed, dataSigned, sha256File, signXML.getNameFile(), 1, signXML.getXmlKey(), base64Data);
                        if (idSignedFile > 0) {
                            int signed = 1;
                            idUserSigned = smartCADAO.smartcaSaveUserSignedXml(dvtt, signXML.getKeySign(), userId, 1);
                        }

                        if (idSignedFile > 0 && idUserSigned > 0) {
                            result.put("SUCCESS", idUserSigned);
                            result.put("STATUS_CODE", DocumentType.XML + ".EMR." + HttpServletResponse.SC_OK);
                            result.put("MESSAGE", "Ký số thành công");
                            smartCADAO.smartcaCapNhatLog(dvtt, userId, 3, "Cập nhật ký số XML EMR", "/smartca-sign-save-file-xml", signXML.toString(), result.toString());
                            logActionDAO.writeLogAction(dvtt, "Upload signature file", "smartcaSaveFileSigned", Integer.parseInt(userId), ipAddress, signXML.toString(), "", idUserSigned);
                        } else {
                            smartCADAO.smartcaCapNhatHuyKySo(dvtt, signXML.getKeySign(), userId);
                            result.put("SUCCESS", -999);
                            result.put("MESSAGE", "Ký số thất bại. Không lưu được xml");
                            return result;
                        }
                    } catch (Exception e) {
                        result.put("SUCCESS", -1);
                        result.put("MESSAGE", "" + e.getMessage());
                        logActionDAO.writeLogAction(dvtt, "Upload signature file", "smartcaSaveFileSigned", Integer.parseInt(userId), ipAddress, signXML.toString(), e.getMessage(), 0);
                        return result;
                    }
                }

                result.put("DATA", base64Signed);
                result.put("SUCCESS", 1);
                return result;
            } catch (Exception ex) {
                if (idUniqDangKy > 0) {
                    smartCADAO.cmuMoKhoaPhieuDangKy(dvtt, idUniqDangKy);
                }
                ex.printStackTrace();
                result.put("SUCCESS", "-13");
                result.put("KEYMINIO", null);
                result.put("MESSAGE", "Ký số thất bại. Gửi thông tin ký số sang SmartCA không thành công!");
                return result;
            }

        } catch (Exception ex) {
            ex.printStackTrace();
            result.put("SUCCESS", "-9");
            result.put("KEYMINIO", null);
            result.put("MESSAGE", "Ký số thất bại. Xảy ra lỗi ngoại lệ " + ex.getMessage());
            return result;
        }
    }

    public @ResponseBody
    @PostMapping(value = "/cmu-smartca-signed-hash-qd-769-ky-thay")
    Map smartcaSaveFileSignedQD769KyThayV1(@RequestBody SignObj signHashDTO,
                                           HttpSession session,
                                           HttpServletRequest request) {
        String dvtt = L2Utils.getDvtt(session);
        String userId = Objects.toString(signHashDTO.getKeyKcb().getUserId(), L2Utils.getMaUser(session));
        Map<String, Object> result = new HashMap<String, Object>();
        Map checkValid = signHashDTO.isValid();
        try {
            if (!Boolean.valueOf(checkValid.get("valid").toString())) {
                result.put("SUCCESS", "-10");
                result.put("KEYMINIO", null);
                result.put("MESSAGE", checkValid.get("message").toString());
                return result;
            }
            String iconValid = request.getSession().getServletContext().getRealPath("/resources/Theme/camau/valid-icon.png");
            String base64SourceAfterAddImage = cmuSmartCAService.addImageBoxToPdfFromBase64Itext(signHashDTO.getBase64PdfSign(),
                    iconValid,
                    signHashDTO.getSignTemplateList());
            byte[] bytes =java.util.Base64.getDecoder().decode(base64SourceAfterAddImage);
            Map smartCAAuth = smartCADAO.smartcaLayThongTinCauHinh(dvtt, userId);
            String userName = smartCAAuth.containsKey("USERNAME") ? smartCAAuth.get("USERNAME").toString() : "";
            Map smartcaAccessToken = smartCADAO.smartcaLayThongTinToken(dvtt, userName);
            if (smartcaAccessToken == null) {
                smartcaAccessToken = new HashMap();
            }
            String smartcaToken = smartcaAccessToken.getOrDefault("ACCESS_TOKEN", "").toString();
            String smartCAVersion = smartCAAuth.getOrDefault("SMARTCA_VERSION", "").toString();
            String smartCAUrl = smartCAAuth.getOrDefault("URL", "").toString();
            String smartCAGrantType = smartCAAuth.getOrDefault("GRANT_TYPE", "").toString();
            String smartCAClientId = smartCAAuth.getOrDefault("CLIENT_ID", "").toString();
            String smartCAClientSecret = smartCAAuth.getOrDefault("CLIENT_SECRET", "").toString();
            String password = smartCAAuth.getOrDefault("PASSWORD", "").toString();
            String urlQD769 = smartCAAuth.getOrDefault("URL_QD_769", "").toString();
            String serialSmartCA =  smartCAAuth.getOrDefault("SERIAL_SMARTCA", "").toString();
            String tOtp = smartCAVersion.equals("2") ? smartCAAuth.getOrDefault("TOTP", "").toString() : null;
            String urlESeal = smartCAVersion.equals("2") ? smartCAAuth.getOrDefault("URL_ESEAL", "").toString() : null;
            Map validAccount = isValidAccount(smartCAAuth);
            if(!Boolean.valueOf(validAccount.get("valid").toString())) {
                result.put("SUCCESS", "-11");
                result.put("KEYMINIO", null);
                result.put("MESSAGE", validAccount.get("message").toString());
                return result;
            }
            SmartCADetail smartCADetail = new SmartCADetail(
                    smartCAGrantType,
                    userName,
                    password,
                    smartCAUrl,
                    smartCAClientId,
                    smartCAClientSecret,
                    smartcaToken,
                    null,
                    null,
                    null,
                    null,
                    urlESeal,
                    tOtp,
                    urlQD769,
                    serialSmartCA
            );
            try {
                Map<String, Object> signHashPdfResult = new HashMap<>();
                int idUniqDangKy = 0;
                if (signHashDTO.getKeyKcb() != null) {
                    SignObj.SmartCAKeyKcb keyKcb = signHashDTO.getKeyKcb();
                    String soVaoVien = Objects.toString(keyKcb.getSoVaoVien(), null);
                    String soVaoVienDieuTri = Objects.toString(keyKcb.getSoVaoVienDT(), null);
                    String kyHieuPhieu = keyKcb.getKyHieuPhieu();
                    String soPhieuDichVu = keyKcb.getSoPhieuDichVu();
                    String maDichVu = Objects.toString(keyKcb.getMaDichVu(), "-1");
                    idUniqDangKy = smartCADAO.cmuKhoaPhieuDangKy(dvtt, soVaoVien, soVaoVienDieuTri, kyHieuPhieu, soPhieuDichVu, maDichVu, userId);
                }
                if(smartCAVersion.equals("2")) {
                    signHashPdfResult = cmuSmartCAService.signHashPdfV2(smartCADetail, bytes,
                            iconValid,
                            signHashDTO.getVisibleType(),
                            signHashDTO.getFullName(),
                            signHashDTO.getFontSize().equals("") ? "13" : signHashDTO.getFontSize(),
                            "",
                            signHashDTO.getSignTemplateList(), signHashDTO.getFileName(), dvtt, signHashDTO.getKeySign(), userId);

                } else  {
                    signHashPdfResult = cmuSmartCAService.signHashPdfV1(smartCADetail, bytes,
                            iconValid,
                            signHashDTO.getVisibleType(),
                            signHashDTO.getFullName(),
                            signHashDTO.getFontSize().equals("") ? "13" : signHashDTO.getFontSize(),
                            "",
                            signHashDTO.getSignTemplateList(), signHashDTO.getFileName(), dvtt, signHashDTO.getKeySign(), userId);
                }

                if (!(signHashPdfResult.containsKey("signData") && signHashPdfResult.containsKey("signDate") && signHashPdfResult.containsKey("certificate"))) {
                    result.put("SUCCESS", signHashPdfResult.get("errorCode"));
                    result.put("MESSAGE", signHashPdfResult.get("errorMessage"));
                    if (idUniqDangKy > 0) {
                        smartCADAO.cmuMoKhoaPhieuDangKy(dvtt, idUniqDangKy);
                    }
                    return result;
                }

                byte[] signedData = signHashPdfResult.get("signData") != null ? (byte[]) signHashPdfResult.get("signData") : null;
                Date signedDate = signHashPdfResult.get("signDate") != null ? (Date) signHashPdfResult.get("signDate") : null;

                String certificateString = "";
                if(smartCAVersion.equals("1")) {
                    SmartCAQD769GetCertificateResponseDTO.Data.Certificate certificate = signHashPdfResult.get("certificate") != null ? (SmartCAQD769GetCertificateResponseDTO.Data.Certificate) signHashPdfResult.get("certificate") : null;
                    certificateString = new Gson().toJson(certificate);
                } else {
                    smartca.model.Certificate certificate = signHashPdfResult.get("certificate") != null ? (Certificate) signHashPdfResult.get("certificate") : null;
                    certificateString = new Gson().toJson(certificate);
                }
                String base64Signed = Base64.getEncoder().encodeToString(signedData);
                String sha256Signed = sha256(base64Signed);
                String sha256File = tienich.getMD5(base64Signed);
                String keyMinio = smartCADAO.smartcaSaveFileSigned(dvtt, signHashDTO.getKeySign(), sha256Signed, base64Signed, sha256File, signHashDTO.getFileName());
                if (!keyMinio.isEmpty()) {
                    int idUserSigned = smartCADAO.smartcaSaveUserSigned(dvtt, signHashDTO.getKeySign(), userId, 1, signHashDTO.getVaiTroKySo(), certificateString, signHashDTO.getBase64ImageSign());
                    if (signHashDTO.getKeyKcb() != null) {
                        SignObj.SmartCAKeyKcb keyKcb = signHashDTO.getKeyKcb();
                        String noiTru = Objects.toString(keyKcb.getNoiTru(), null);
                        String maBenhNhan = Objects.toString(keyKcb.getMaBenhNhan(), null);
                        String soVaoVien = Objects.toString(keyKcb.getSoVaoVien(), null);
                        String soVaoVienDieuTri = Objects.toString(keyKcb.getSoVaoVienDT(), null);
                        String soBenhAn = Objects.toString(keyKcb.getSoBenhAn(), null);
                        String sttDotDieuTri = Objects.toString(keyKcb.getSttDotDieuTri(), null);
                        String kyHieuPhieu = keyKcb.getKyHieuPhieu();
                        String idDieuTri = Objects.toString(keyKcb.getIdDieuTri(), null);
                        String soPhieuDichVu = keyKcb.getSoPhieuDichVu();
                        String ngay = Objects.toString(keyKcb.getNgay(), null);
                        String tuNgay = Objects.toString(keyKcb.getTuNgay(), null);
                        String denNgay = Objects.toString(keyKcb.getDenNgay(), null);
                        String maKhoa = Objects.toString(keyKcb.getMaKhoa(), null);
                        String tenKhoa = keyKcb.getTenKhoa();
                        String nghiepVu = keyKcb.getNghiepVu();
                        String toaThuoc = keyKcb.getToaThuoc();
                        String soBe = Objects.toString(keyKcb.getSoBe(), "-1");
                        String maDichVu = Objects.toString(keyKcb.getMaDichVu(), "-1");
                        String inTong = Objects.toString(keyKcb.getInTong(), null);

                        int idSignedKcb = smartCADAO.smartcaSaveSignedKcb(dvtt, signHashDTO.getKeySign(), noiTru, maBenhNhan, soVaoVien, soVaoVienDieuTri,
                                soBenhAn, sttDotDieuTri, kyHieuPhieu, idDieuTri, soPhieuDichVu, ngay, tuNgay, denNgay, maKhoa, tenKhoa, nghiepVu, toaThuoc,
                                inTong, userName, userId, 1, 0, soBe, maDichVu, "", 0);
                        String pattern = "yyyy-MM-dd HH:mm:ss";
                        SimpleDateFormat simpleDateFormat = new SimpleDateFormat(pattern);
                        int resComplete = smartCADAO.smartcaUpdateFileComplete(dvtt, signHashDTO.getKeySign(), String.valueOf(signHashDTO.getSoChuKyDaKy()), String.valueOf(signHashDTO.getTongSoChuKy()), simpleDateFormat.format(signedDate), userId, 0);
                    }
                }
                /**init file sign XML
                 *Nếu là XML của EMR thì không gửi request ký số*/
                SignObj.SignXML signXML = signHashDTO.getSignXML();

                if (signXML != null && signXML.getIsSign().equals("0")){
                    InetAddress inetAddress = InetAddress.getLocalHost();
                    String ipAddress = inetAddress.getHostAddress();
                    Map certificate = smartCADAO.smartcaGetCertificate(dvtt, signHashDTO.getKeySign(), signHashDTO.getFileName(), userId);
                    Map responseFile = cmuSmartCAService.signFakeXml(dvtt, signXML, certificate);
                    try {
                        int idUserSigned = 0;
                        String dataSigned = responseFile.get("dataSigned").toString();
                        String base64Data = responseFile.get("base64Data").toString();
                        sha256File = tienich.getMD5(signXML.getXmlFile());
                        sha256Signed = tienich.getMD5(dataSigned);
                        int idSignedFile = smartCADAO.smartcaSaveFileSignedXml(dvtt, signXML.getKeySign(), sha256Signed, dataSigned, sha256File, signXML.getNameFile(), 1, signXML.getXmlKey(), base64Data);
                        if (idSignedFile > 0) {
                            int signed = 1;
                            idUserSigned = smartCADAO.smartcaSaveUserSignedXml(dvtt, signXML.getKeySign(), userId, 1);
                        }

                        if (idSignedFile > 0 && idUserSigned > 0) {
                            result.put("SUCCESS", idUserSigned);
                            result.put("STATUS_CODE", DocumentType.XML + ".EMR." + HttpServletResponse.SC_OK);
                            result.put("MESSAGE", "Ký số thành công");
                            smartCADAO.smartcaCapNhatLog(dvtt, userId, 3, "Cập nhật ký số XML EMR", "/smartca-sign-save-file-xml", signXML.toString(), result.toString());
                            logActionDAO.writeLogAction(dvtt, "Upload signature file", "smartcaSaveFileSigned", Integer.parseInt(userId), ipAddress, signXML.toString(), "", idUserSigned);
                        } else {
                            smartCADAO.smartcaCapNhatHuyKySo(dvtt, signXML.getKeySign(), userId);
                            result.put("SUCCESS", -999);
                            result.put("MESSAGE", "Ký số thất bại. Không lưu được xml");
                            return result;
                        }
                    } catch (Exception e) {
                        result.put("SUCCESS", -1);
                        result.put("MESSAGE", "" + e.getMessage());
                        logActionDAO.writeLogAction(dvtt, "Upload signature file", "smartcaSaveFileSigned", Integer.parseInt(userId), ipAddress, signXML.toString(), e.getMessage(), 0);
                        return result;
                    }
                }
                if (idUniqDangKy > 0) {
                    smartCADAO.cmuMoKhoaPhieuDangKy(dvtt, idUniqDangKy);
                }
                result.put("DATA", base64Signed);
                result.put("SUCCESS", 1);
                return result;
            } catch (Exception ex) {
                ex.printStackTrace();
                result.put("SUCCESS", "-13");
                result.put("KEYMINIO", null);
                result.put("MESSAGE", "Ký số thất bại. Gửi thông tin ký số sang SmartCA không thành công!");
                return result;
            }

        } catch (Exception ex) {
            ex.printStackTrace();
            result.put("SUCCESS", "-9");
            result.put("KEYMINIO", null);
            result.put("MESSAGE", "Ký số thất bại. Xảy ra lỗi ngoại lệ " + ex.getMessage());
            return result;
        }
    }

    public Map isValidAccount(Map smartCAAuth) {
        Map result = new HashMap();
        result.put("valid", true);
        String message = "";
        String smartCAVersion = smartCAAuth.getOrDefault("SMARTCA_VERSION", "").toString();
        String smartCAUrl = smartCAAuth.getOrDefault("URL", "").toString();
        String smartCAGrantType = smartCAAuth.getOrDefault("GRANT_TYPE", "").toString();
        String smartCAClientId = smartCAAuth.getOrDefault("CLIENT_ID", "").toString();
        String smartCAClientSecret = smartCAAuth.getOrDefault("CLIENT_SECRET", "").toString();
        String password = smartCAAuth.getOrDefault("PASSWORD", "").toString();
        String activate = smartCAAuth.getOrDefault("ACTIVATE", "0").toString();
        String urlQD769 = smartCAAuth.getOrDefault("URL_QD_769", "").toString();
        String serialSmartCA =  smartCAAuth.getOrDefault("SERIAL_SMARTCA", "").toString();
        String tOtp = smartCAVersion.equals("2") ? smartCAAuth.getOrDefault("TOTP", "").toString() : null;
        String urlESeal = smartCAVersion.equals("2") ? smartCAAuth.getOrDefault("URL_ESEAL", "").toString() : null;
        if(smartCAUrl.equals("")) {
            result.put("valid", false);
            message += "Smartca không được để trống; ";
        }
        if(smartCAGrantType.equals("")) {
            result.put("valid", false);
            message += "GRANT_TYPE không được để trống; ";
        }
        if(smartCAClientId.equals("")) {
            result.put("valid", false);
            message += "Client ID không được để trống; ";
        }
        if(smartCAClientSecret.equals("")) {
            result.put("valid", false);
            message += "Client Secret không được để trống; ";
        }
        if(password.equals("")) {
            result.put("valid", false);
            message += "Password không được để trống; ";
        }

        if(!activate.equals("1")) {
            result.put("valid", false);
            message += "Tài khoản chưa được cấu hình kích hoạt; ";
        }

        if(serialSmartCA.equals("")) {
            result.put("valid", false);
            message += "Serial number không được để trống;; ";
        }

        if(urlQD769.equals("")) {
            result.put("valid", false);
            message += "URL 769 không được để trống;; ";
        }

        if (smartCAVersion.equals("2")) {
            if(tOtp.equals("")) {
                result.put("valid", false);
                message += "TOTP không được để trống;; ";
            }
            if(urlESeal.equals("")) {
                result.put("valid", false);
                message += "URL Seal không được để trống;; ";
            }
        }

        result.put("message", message);
        return result;
    }
}
