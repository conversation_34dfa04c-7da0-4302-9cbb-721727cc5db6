create or replace FUNCTION      HIS_MANAGER."CMU_GET_TTTTBENHNANGXINVE" (
    p_dvtt IN VARCHAR2,
    p_sovaovien IN VARCHAR2
) RETURN SYS_REFCURSOR IS
    cur SYS_REFCURSOR;
BEGIN
OPEN cur FOR
SELECT
    phieu.ID,
    phieu.DVTT,
    phieu.SOVAOVIEN,
    phieu.MA_BENH_NHAN,
    JSON_VALUE(THONGTINBENHJSON, '$.BENHAN') AS BENHAN,
    JSON_VALUE(THONGTINBENHJSON, '$.MAHSBA') AS MAHSBA,
    JSON_VALUE(THONGTINBENHJSON, '$.SODINHDANH') AS SODINHDANH,
    JSON_VALUE(THONGTINBENHJSON, '$.LOAI') AS LOAI,
    JSON_VALUE(THONGTINBENHJSON, '$.SOTHEBHYT') AS SOTHEBHYT,
    JSON_VALUE(THONGTINBENHJSON, '$.GIATRITU') AS GIATRITU,
    JSON_VALUE(THONGTINBENHJSON, '$.GIATRIDEN') AS GIATRIDEN,
    JSON_VALUE(THONGTINBENHJSON, '$.NOIDANGKY') AS NOIDANGKY,
    JSON_VALUE(THONGTINBENHJSON, '$.THOIGIAN5NAM') AS THOIGIAN5NAM,
    JSON_VALUE(THONGTINBENHJSON, '$.MIENCUNGCHITRA') AS MIENCUNGCHITRA,
    JSON_VALUE(THONGTINBENHJSON, '$.DIACHIHTTINH') AS DIACHIHTTINH,
    JSON_VALUE(THONGTINBENHJSON, '$.DIACHIHTHUYEN') AS DIACHIHTHUYEN,
    JSON_VALUE(THONGTINBENHJSON, '$.DIACHIHTXA') AS DIACHIHTXA,
    JSON_VALUE(THONGTINBENHJSON, '$.DIACHIHTSONHA') AS DIACHIHTSONHA,
    JSON_VALUE(THONGTINBENHJSON, '$.DIACHITRTINH') AS DIACHITRTINH,
    JSON_VALUE(THONGTINBENHJSON, '$.DIACHITRHUYEN') AS DIACHITRHUYEN,
    JSON_VALUE(THONGTINBENHJSON, '$.DIACHITRXA') AS DIACHITRXA,
    JSON_VALUE(THONGTINBENHJSON, '$.DIACHITRSONHA') AS DIACHITRSONHA,
    JSON_VALUE(THONGTINBENHJSON, '$.DIACHIBENHTINH') AS DIACHIBENHTINH,
    JSON_VALUE(THONGTINBENHJSON, '$.DIACHIBENHHUYEN') AS DIACHIBENHHUYEN,
    JSON_VALUE(THONGTINBENHJSON, '$.DIACHIBENHXA') AS DIACHIBENHXA,
    JSON_VALUE(THONGTINBENHJSON, '$.DIACHIBENHSONHA') AS DIACHIBENHSONHA,
    JSON_VALUE(THONGTINBENHJSON, '$.TONGIAO') AS TONGIAO,
    JSON_VALUE(THONGTINBENHJSON, '$.SONGAYVANGMAT') AS SONGAYVANGMAT,
    JSON_VALUE(THONGTINBENHJSON, '$.LOAIVAOVIEN') AS LOAIVAOVIEN,
    JSON_VALUE(THONGTINBENHJSON, '$.NGAYRAVIEN') AS NGAYRAVIEN,
    JSON_VALUE(THONGTINBENHJSON, '$.CHUYENTU') AS CHUYENTU,
    JSON_VALUE(THONGTINBENHJSON, '$.CHUYENDEN') AS CHUYENDEN,
    JSON_VALUE(THONGTINBENHJSON, '$.TINHTRANGRV') AS TINHTRANGRV,
    JSON_VALUE(THONGTINBENHJSON, '$.KETQUADT') AS KETQUADT,
    JSON_VALUE(THONGTINBENHJSON, '$.SONGAYICU') AS SONGAYICU,
    JSON_VALUE(THONGTINBENHJSON, '$.CANNANGTRE') AS CANNANGTRE,
    JSON_VALUE(THONGTINBENHJSON, '$.TIENLUONG') AS TIENLUONG,
    phieu.NNTUVONGJSON,
    phieu.BENHKHACJSON,
    JSON_VALUE(PHAUTHUAT4TUANJSON, '$.PHAUTHUAT4TUAN') AS PHAUTHUAT4TUAN,
    JSON_VALUE(PHAUTHUAT4TUANJSON, '$.LYDOPHAUTHUAT4TUAN') AS LYDOPHAUTHUAT4TUAN,
    JSON_VALUE(PHAUTHUAT4TUANJSON, '$.THOIGIAN4TUAN') AS THOIGIAN4TUAN,
    phieu.PHAUTHUAT4TUANJSON,
    phieu.HINHTHUCTV,
    JSON_VALUE(NNBENNGOAIJSON, '$.TENNNBENNGOAI') AS TENNNBENNGOAI,
    JSON_VALUE(NNBENNGOAIJSON, '$.MOTANNBENNGOAI') AS MOTANNBENNGOAI,
    JSON_VALUE(NNBENNGOAIJSON, '$.MAICDNNBENNGOAI') AS MAICDNNBENNGOAI,
    JSON_VALUE(NNBENNGOAIJSON, '$.TENICDNNBENNGOAI') AS TENICDNNBENNGOAI,
    JSON_VALUE(NNBENNGOAIJSON, '$.NGAYXAYRA') AS NGAYXAYRA,
    JSON_VALUE(NNBENNGOAIJSON, '$.NOIXAYRATAINAN') AS NOIXAYRATAINAN,
    JSON_VALUE(NNBENNGOAIJSON, '$.DIADIEM') AS DIADIEM,
    phieu.NNBENNGOAIJSON,
    JSON_VALUE(TUVONGTHAINHIJSON, '$.DATHAI') AS DATHAI,
    JSON_VALUE(TUVONGTHAINHIJSON, '$.SINHNON') AS SINHNON,
    JSON_VALUE(TUVONGTHAINHIJSON, '$.TUVONGTHAINHI') AS TUVONGTHAINHI,
    JSON_VALUE(TUVONGTHAINHIJSON, '$.TUOITHAI') AS TUOITHAI,
    JSON_VALUE(TUVONGTHAINHIJSON, '$.TUOIME') AS TUOIME,
    JSON_VALUE(TUVONGTHAINHIJSON, '$.SOGIOSONG') AS SOGIOSONG,
    JSON_VALUE(TUVONGTHAINHIJSON, '$.CANNANGKHISINH') AS CANNANGKHISINH,
    JSON_VALUE(TUVONGTHAINHIJSON, '$.BENHLYCUAME') AS BENHLYCUAME,
    JSON_VALUE(TUVONGTHAINHIJSON, '$.MAICDBENHLYME') AS MAICDBENHLYME,
    JSON_VALUE(TUVONGTHAINHIJSON, '$.TENICDBENHLYME') AS TENICDBENHLYME,
    phieu.TUVONGTHAINHIJSON,
    JSON_VALUE(PHUNUJSON, '$.DANGMANGTHAI') AS DANGMANGTHAI,
    JSON_VALUE(PHUNUJSON, '$.THOIDIEMMANGTHAI') AS THOIDIEMMANGTHAI,
    JSON_VALUE(PHUNUJSON, '$.GOPPHANNANG') AS GOPPHANNANG,
    phieu.PHUNUJSON,
    JSON_VALUE(KETLUANJSON, '$.KETLUANTENBL') AS KETLUANTENBL,
    JSON_VALUE(KETLUANJSON, '$.KETLUANMAICD') AS KETLUANMAICD,
    JSON_VALUE(KETLUANJSON, '$.KETLUANTENICD') AS KETLUANTENICD,
    phieu.KETLUANJSON,
    phieu.BSDIEUTRI,
    phieu.TRUONGKHOA,
    phieu.THUTRUONG,
    nv1.TEN_NHANVIEN_CD TENBACSI,
    nv2.TEN_NHANVIEN_CD TENTRUONGKHOA,
    nv3.TEN_NHANVIEN_CD TENTHUTRUONG,
    TO_CHAR(phieu.NGAY_TAO_PHIEU, 'dd/MM/yyyy') NGAY_TAO_PHIEU,
    signkcb.KEYSIGN KEYSIGN_BACSI,
    signkcb2.KEYSIGN KEYSIGN_TRUONGKHOA
FROM CMU_TTTTBENHNANGXINVE phieu
         LEFT JOIN HIS_FW.DM_NHANVIEN_CD nv1 ON phieu.BSDIEUTRI = nv1.MA_NHANVIEN
         LEFT JOIN HIS_FW.DM_NHANVIEN_CD nv2 ON phieu.TRUONGKHOA = nv2.MA_NHANVIEN
         LEFT JOIN HIS_FW.DM_NHANVIEN_CD nv3 ON phieu.THUTRUONG = nv3.MA_NHANVIEN
         LEFT JOIN smartca_signed_kcb signkcb ON phieu.dvtt = signkcb.dvtt
    AND p_sovaovien = signkcb.SOVAOVIEN
    AND signkcb.so_phieu_dv = phieu.ID
    AND signkcb.status = 0
    AND signkcb.ky_hieu_phieu IN ('PHIEU_NOITRU_TTTTBENHNANGXINVE_BACSI')
         LEFT JOIN smartca_signed_kcb signkcb2 ON phieu.dvtt = signkcb.dvtt
    AND p_sovaovien = signkcb2.SOVAOVIEN
    AND signkcb2.so_phieu_dv = phieu.ID
    AND signkcb2.status = 0
    AND signkcb2.ky_hieu_phieu IN ('PHIEU_NOITRU_TTTTBENHNANGXINVE_TRUONGKHOA')
WHERE phieu.DVTT = p_dvtt AND phieu.SOVAOVIEN = p_sovaovien
ORDER BY phieu.ID desc;

RETURN cur;
END;