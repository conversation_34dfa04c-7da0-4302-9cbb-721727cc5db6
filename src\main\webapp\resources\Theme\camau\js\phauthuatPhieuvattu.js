
$(function() {
    $("#lanphauthuat_vattu_tenvattu").combogrid({
        url: "layvattu_theoloai?makhovt=" + $("#lanphauthuat_vattu_nghiepvuthuoc").val() + "&loaivattu=VT_TH&nhomvattu=",
        debug: true,
        width: "1110",
        colModel: [
            {'columnName': 'MAVATTU', 'label': 'ID', 'width': '5', 'align': 'left'},
            {'columnName': 'TENVATTU', 'width': '27', 'label': 'Tên thương mại', 'align': 'left'},
            {'columnName': 'TEN_HIEN_THI', 'label': 'Tên hiển thị', 'align': 'left', hidden: true},
            {'columnName': 'HOATCHAT', 'width': '12', 'label': 'Hoạt chất', 'align': 'left'},
            {'columnName': 'HAMLUONG', 'width': '8', 'label': 'Hàm lượng', 'align': 'left'}, //an giang chỉnh tỉ lệ từ 20 xuống 10
            {'columnName': 'DVT', 'label': 'ĐVT', 'width': '5'},
            {'columnName': 'SOLUONG', 'width': '6', 'label': 'Số lượng', 'align': 'center'},
            {'columnName': 'DONGIA', 'width': '8', 'label': 'Đơn giá', 'align': 'right'},
            {'columnName': 'CACHSUDUNG', 'label': 'cachsudung', hidden: true},
            {'columnName': 'DANGTHUOC', 'label': 'DANGTHUOC', hidden: true},
            {'columnName': 'GHICHUVATTU', 'width': '5', 'label': 'Ghi chú', 'align': 'right', hidden: true},
            {'columnName': 'MAKHOVATTU', 'label': 'Mã kho', 'align': 'right', hidden: true},
            {'columnName': 'TENKHOVATTU', 'label': 'Tên kho', 'align': 'right', hidden: true},
            {'columnName': 'NGAYHETHAN', 'width': '10', 'label': 'Ngày hết hạn', 'align': 'right'},
            {'columnName': 'NGOAIDANHMUCBHYT', 'width': '10', 'label': 'BHYT', 'align': 'right'},
            {'columnName': 'SOTHAU', 'width': '7', 'label': 'Số thầu', 'align': 'right', hidden: true},
            {'columnName': 'SOLOSANXUAT', 'width': '7', 'label': 'Số lô', 'align': 'right', hidden: true},
            {'columnName': 'TEN_NGUONDUOC', 'width': '5', 'label': 'Nguồn', 'align': 'right', hidden: true},
        ],
        select: function (event, ui) {
            Object.keys(ui.item).forEach(function(key) {
                if(key != 'SOLUONG') {
                    $("#lanphauthuatVattuForm [name='"+key+"']").val(ui.item[key]);
                }

            })
            $("#lanphauthuat_vattu_soluong").focus();
            return false;
        }
    });
    $("#lanphauthuat_vattu_nghiepvuthuoc").change(function() {
        var loaiVT = '';
        if($(this).val() == 'noitru_toathuoc') {
            loadKhoThuocVatTucommon(singletonObject.danhsachkhothuocBHYT, 'lanphauthuat_vattu_khothuoc');
            loaiVT = 'VT_TH'
        }
        if($(this).val() == 'noitru_toavattu') {
            loadKhoThuocVatTucommon(singletonObject.danhsachkhovattu, 'lanphauthuat_vattu_khothuoc');
            loaiVT = 'TH'
        }
        if($(this).val() == 'noitru_toamienphi') {
            loadKhoThuocVatTucommon(singletonObject.danhsachkhomienphi, 'lanphauthuat_vattu_khothuoc');
        }
        if($(this).val() == 'noitru_toaquaybanthuocbv') {
            loadKhoThuocVatTucommon(singletonObject.danhsachkhomuataiquay, 'lanphauthuat_vattu_khothuoc');
        }
        if($(this).val() == 'noitru_toadongy') {
            loadKhoThuocVatTucommon(singletonObject.danhsachkhodongy, 'lanphauthuat_vattu_khothuoc');
        }
        if($(this).val() == 'noitru_toadichvu') {
            loadKhoThuocVatTucommon(singletonObject.danhsachkhodichvu, 'lanphauthuat_vattu_khothuoc');
        }
        loadKhothuocmacdinhcommon("lanphauthuat_vattu_nghiepvuthuoc", "lanphauthuat_vattu_khothuoc");
        changeSourceComboGridcommon($("#lanphauthuat_vattu_khothuoc").val(), loaiVT, "lanphauthuat_vattu_tenvattu");
    })
    $("#lanphauthuat_vattu_khothuoc").change(function() {
        changeSourceComboGridcommon($(this).val(), $("#lanphauthuat_vattu_nghiepvuthuoc").val(), "lanphauthuat_vattu_tenvattu");
    })
    $("#lanphauthuat_vattu_soluong").keypress(function (e) {
        var idButton = this.id;
        if(e.keyCode == 13 && $("#lanphauthuatVattuForm").valid()) {
            var data = convertDataFormToJson("lanphauthuatVattuForm")
            showSelfLoading(idButton)
            $.post("cmu_post_CMU_LANPHAUTHUAT_VATTU_INS", {
                url: [
                    singletonObject.dvtt,
                    lanPhauThuatObject.ID,
                    data.MAVATTU,
                    data.TENVATTU,
                    data.HOATCHAT,
                    data.HAMLUONG,
                    data.DVT,
                    data.CACHSUDUNG,
                    data.SOLUONG,
                    data.DONGIA,
                    data.NGHIEPVU,
                    data.KHOVATTU,
                    singletonObject.userId
                ].join("```")
            }).always(function() {
                hideSelfLoading(idButton)
                loadDSGridVattu();

            }).done(function(data) {
                if(data == -1) {
                    notifiToClient("Red", "Thuốc vật tư đã tồn tại");
                    return;
                }
                if(data == 0 ) {
                    notifiToClient("Red", "Lỗi thêm thuốc vật tư");
                    return;
                }
                $("#lanphauthuatVattuForm .clear-text").val("");
                $("#lanphauthuat_vattu_tenvattu").focus()
            }).fail(function() {
                notifiToClient("Red", "Lỗi thêm thuốc vật tư");
            })
        }
    })
    $("#lanphauthuat-thuocdichtruyen").click(function() {
        initGridVattu();
        loadDSGridVattu();
        $("#lanphauthuat_vattu_nghiepvuthuoc").val('noitru_toadichvu').trigger("change");
        $("#loadChotduocProgressWrap").hide();
    })
    $('#lanphauthuat_vattu_btn_khac_dropdown p').click(function () {
        var type = $(this).attr('data-id');
        if(type == 'xemtoanoitru') {
            var url = 'cmu_in_rp_phieusudungvattu?type=pdf&' + $.param({
                id: lanPhauThuatObject.ID,
                hoten: thongtinhsba.thongtinbn.TEN_BENH_NHAN,
                tuoi: thongtinhsba.thongtinbn.TUOI,
                gioitinh: thongtinhsba.thongtinbn.GIOI_TINH_HT,
                sobenhan: thongtinhsba.thongtinbn.SOBENHAN,
            });
            previewPdfDefaultModal(url, 'frame-inphieuvattu')
        }
        if(type == 'chotdulieu') {
            confirmToClient(MESSAGEAJAX.CONFIRM, function() {
                var list = $("#lanphauthuatVattuList");
                var allData = list.jqGrid('getDataIDs');
                if(allData.length == 0) {
                    return notifiToClient("Red", "Chưa có dữ liệu để chốt");
                }
                if(!lanPhauThuatObject.BAC_SI_PHAU_THUAT) {
                    return notifiToClient("Red", "Chưa cập nhật ekip bác sĩ phẫu thuật");
                }
                if(!lanPhauThuatObject.BAT_DAU_ME || !lanPhauThuatObject.BAT_DAU_MO || !lanPhauThuatObject.KET_THUC_MO) {
                    return notifiToClient("Red", "Chưa cập nhật thời gian mê, bắt đầu mổ, kết thúc");
                } else {
                    var idButton = 'lanphauthuat_vattu_btn_khac';
                    if(!lanPhauThuatObject.ID_DIEUTRI) {
                        showSelfLoading(idButton)
                        $.taoPhieuDieuTriGMHS(thongtinhsba.thongtinbn, function(data) {
                            hideSelfLoading(idButton)
                        }, null, singletonObject.thamSo960637 == 1? lanPhauThuatObject.BAC_SI_GAY_ME:
                            lanPhauThuatObject.BAC_SI_PHAU_THUAT)
                    }
                    $.post("noitru_select_tt_phieudt", {url: convertArray(
                            [
                                lanPhauThuatTDTObject.STT_DIEUTRI,
                                singletonObject.dvtt,
                                lanPhauThuatTDTObject.STT_DIEUTRI,
                                thongtinhsba.thongtinbn.STT_BENHAN,
                                lanPhauThuatTDTObject.STT_DOTDIEUTRI
                            ]
                        )
                    }).done(function (dt) {
                        if (dt > 1) {
                            hideSelfLoading(idButton)
                            return notifiToClient("Red", "Phiếu điều trị đã được dự trù thuốc hoặc cập nhật trạng thái, không được thêm thuốc vào!");
                        }
                        $("#loadChotduocProgressWrap").show();
                        var $progressBar = $("#loadChotduocProgressWrap .progress-bar");
                        $progressBar.css("width", "0%");
                        $progressBar.attr("aria-valuenow", 0);
                        var tlmg = thongtinhsba.thongtinbn.TYLEBAOHIEM;
                        tlmg == "" ? tlmg = "0" : tlmg = tlmg;
                        var doneTask = 0;
                        for (var i = 0; i < allData.length; i++) {
                            var ret = $("#lanphauthuatVattuList").jqGrid('getRowData', allData[i]);
                            if(ret.TRANGTHAI == 1 || ret.NGHIEP_VU == 'noitru_toamuangoai' || ret.SOLUONG == 0) {
                                doneTask++;
                                $progressBar.css("width", (doneTask/allData.length*100) + "%");
                                if(doneTask == allData.length) {
                                    loadDSGridVattu();
                                }
                                continue;
                            }
                            var sl = parseInt(ret.SOLUONG);
                            var thanhtien = ret.DONGIA*ret.SOLUONG;
                            var mavattu = ret.MAVATTU;
                            var tenthuongmai = ret.TENVATTU;
                            var tengoc = ret.HOATCHAT;
                            var dvt = ret.DVT;
                            var dangthuoc = ret.CACHSUDUNG;
                            var cachdung = ret.CACHSUDUNG;
                            var songay = 1;
                            var sang = 0;
                            var trua = 0;
                            var chieu = 0;
                            var toi = 0;
                            var dongia_bv = ret.DONGIA;
                            var dongia_bh = ret.DONGIA;
                            var nghiepvu = ret.NGHIEPVU;
                            var arr = [singletonObject.dvtt,
                                lanPhauThuatTDTObject.STT_DIEUTRI, ret.KHOVATTU, mavattu,
                                encodeURIComponent(tenthuongmai), encodeURIComponent(tengoc), dvt, nghiepvu, sl, sl, dongia_bv,
                                dongia_bh, thanhtien, songay, sang, trua,
                                chieu, toi, cachdung+ "!!!"+singletonObject.userId, lanPhauThuatTDTObject.BACSIDIEUTRI, dangthuoc,
                                lanPhauThuatTDTObject.STT_DIEUTRI, thongtinhsba.thongtinbn.STT_BENHAN,
                                lanPhauThuatTDTObject.STT_DOTDIEUTRI, 0 /*tu_tuthuoc*/, tlmg,
                                thongtinhsba.thongtinbn.MA_BENH_NHAN, lanPhauThuatTDTObject.SOPHIEUTHANHTOAN,
                                singletonObject.makhoa, lanPhauThuatTDTObject.SOVAOVIEN, lanPhauThuatTDTObject.SOVAOVIEN_DT];
                            $.ajax({
                                url: "noitru_toathuoc_insert",
                                type: "POST",
                                data: {
                                    url: arr.join("```")
                                },
                                async: false,
                                success: function(data) {
                                    var message = "";
                                    switch (data) {
                                        case "3":
                                            message = "Thuốc đã có trong toa";
                                            doneTask++;
                                            break;
                                        case "4":
                                            message = "Bệnh nhân đã thanh toán rồi";
                                            list.jqGrid('setCell', allData[i], 'TRANGTHAI', '<span class="text-danger">Bệnh nhân đã thanh toán rồi<span>');
                                            doneTask++;
                                            break;
                                        case "5":
                                            message = "Bệnh nhân đã xuất thuốc rồi";
                                            doneTask++;
                                            break;
                                        case "6":
                                            message = "Thuốc đã hết trong kho";
                                            doneTask++;
                                            break;
                                        case "100":
                                            message = "Đã chốt báo cáo dược, không thể thêm/sửa/xóa"
                                            doneTask++;
                                            break;
                                        default:
                                            message = 'Thành công'
                                            doneTask++;
                                            break;
                                    }
                                    list.jqGrid('setCell', allData[i], 'TRANGTHAI', message != 'Thành công' ? '<span class="text-danger">'+message+'<span>': '<i style="font-size: 18px" class="fa fa-check-circle text-primary"></i>');
                                    $progressBar.css("width", (doneTask/allData.length*100) + "%");
                                    updateTrangthaiDuoc({
                                        ID: ret.ID,
                                        TRANGTHAI: message != 'Thành công' ? 2 : 1,
                                        LOAI: ret.LOAI,
                                        MESSAGE: message
                                    }).always(function() {
                                        if(doneTask == allData.length) {
                                            hideSelfLoading(idButton)
                                            loadDSGridVattu();
                                        }
                                    })

                                },
                                error: function() {
                                    list.jqGrid('setCell', allData[i], 'TRANGTHAI', '<span class="text-danger">'+MESSAGEAJAX.ERROR+'<span>');
                                    doneTask++;
                                    $progressBar.css("width", (doneTask/allData.length*100) + "%");
                                    updateTrangthaiDuoc({
                                        ID: ret.ID,
                                        TRANGTHAI: 2,
                                        LOAI: ret.LOAI,
                                        MESSAGE: 'Lỗi chốt dược'
                                    })
                                    if(doneTask == allData.length) {
                                        hideSelfLoading(idButton)
                                        loadDSGridVattu();
                                    }
                                }
                            });

                        }
                    }).fail(function() {
                        hideSelfLoading(idButton)
                        notifiToClient("Red", MESSAGEAJAX.ERROR);
                    })


                }
            })

        }
        if(type == 'toamau') {
            if($("#lanphauthuat_vattu_khothuoc").val() == '') {
                return notifiToClient("Red", "Chưa chọn kho vật tư");
            }
            $("#modalLoadtoamau").modal("show");
            $("#thuocvattuLoadToamau").attr("data-function", "loadToamauVattuPT");
            $("#thuocvattuToamauText").val($("#lanphauthuat_vattu_nghiepvuthuoc option:selected").text() + " - "+$("#lanphauthuat_vattu_khothuoc option:selected").text())
        }
    })
    $("#lanphauthuat_vattu_tenvattu").focusout(function () {
        if(!$(this).parent().find("#lanphauthuat_vattu_mavattu").val()){
            $(this).val("");
            $("#lanphauthuat_vattu_mavattu").val("");
        }
    });
    $("#phauthuatVattuChuyennghiepvuLuu").click(function() {
        var rowData = getThongtinRowSelected("lanphauthuatVattuList");
        if(rowData.NGHIEPVU == $("#phauthuatVattuChuyennghiepvu").val()) {
            return notifiToClient("Red", "Nghiệp vụ chuyển không được trùng với nghiệp vụ hiện tại")
        }
        var idButton = this.id
        confirmToClient(MESSAGEAJAX.CONFIRM, function() {
            showSelfLoading(idButton)
            $.post("cmu_post_CMU_LANPHAUTHUAT_VT_CHUYENNV", {
                url: [singletonObject.dvtt,
                    $("#phauthuatVattuChuyennghiepvu").val(),
                    rowData.LOAI,
                    lanPhauThuatTDTObject.ID_DIEUTRI,
                    rowData.ID,
                    lanPhauThuatObject.ID,
                    thongtinhsba.thongtinbn.SOVAOVIEN,
                    rowData.NGHIEPVU
                ].join("```")
            }).done(function(data) {
                if(data == 1) {
                    notifiToClient("Red", 'Mã thuốc này đã tôn tại trong nghiệp vụ mới');

                } else if(data == 2)  {
                    notifiToClient("Red", 'Thuốc/vật tư đã thanh toán');
                } else {
                    notifiToClient("Green", MESSAGEAJAX.EDIT_SUCCESS);
                }
            }).fail(function() {
                notifiToClient("Red", MESSAGEAJAX.ERROR);
            }).always(function() {
                loadDSGridVattu();
                hideSelfLoading(idButton)
            });
        })
    })
    $.extend({
        loadToamauVattuPT: function() {
            var list = $("#listThuocvattuToamau");
            var rowIds = list.jqGrid('getDataIDs');
            if(rowIds.length == 0) {
                return notifiToClient("Red", "Chưa có dữ liệu để load");
            }
            $("#loadToamauProgressWrap").show()
            $("#loadToamauProgressWrap .progress-bar").css("width", "0%");
            var data = convertDataFormToJson("lanphauthuatVattuForm")
            var doneTask = 0;
            for(var i = 0; i < rowIds.length; i++) {
                var rowData = list.jqGrid('getRowData', rowIds[i]);
                list.jqGrid('setCell', rowIds[i], 'TRANGTHAI', '<span class="spinner-border spinner-border-sm spinner" role="status" aria-hidden="true"></span>');
                $.ajax({
                    url: "cmu_post_CMU_LANPHAUTHUAT_LDTOAMAU_INS",
                    type: "POST",
                    data: {
                        url: [
                            singletonObject.dvtt,
                            lanPhauThuatObject.ID,
                            rowData.MAVATTU,
                            data.NGHIEPVU,
                            data.KHOVATTU,
                            rowData.SO_LUONG,
                            singletonObject.userId
                        ].join("```")
                    },
                    async: false,
                    success: function(data) {
                        if(data == -1) {
                            list.jqGrid('setCell', rowIds[i], 'TRANGTHAI', '<span class="text-danger">Đã tồn tại<span>');
                            doneTask++;
                        }
                        if(data == -2) {
                            list.jqGrid('setCell', rowIds[i], 'TRANGTHAI', '<span class="text-danger">Không còn tồn kho<span>');
                            doneTask++;
                        }
                        if(data == 0 ) {
                            list.jqGrid('setCell', rowIds[i], 'TRANGTHAI', '<span class="text-danger">'+MESSAGEAJAX.ERROR+'<span>');
                            doneTask++;
                        }
                        if(data > 0) {
                            list.jqGrid('setCell', rowIds[i], 'TRANGTHAI', '<i style="font-size: 18px" class="fa fa-check-circle text-primary"></i>');
                            doneTask++;
                        }
                        $("#loadToamauProgressWrap .progress-bar").css("width", (doneTask/rowIds.length*100) + "%");
                        if(doneTask == rowIds.length) {
                            loadDSGridVattu();
                        }
                    },
                    error: function() {
                        list.jqGrid('setCell', rowIds[i], 'TRANGTHAI', '<span class="text-danger">'+MESSAGEAJAX.ERROR+'<span>');
                        doneTask++;
                        $("#loadToamauProgressWrap .progress-bar").css("width", (doneTask/rowIds.length*100) + "%");
                        if(doneTask == rowIds.length) {
                            loadDSGridVattu();
                        }
                    }
                });


            }
        }
    })
    $("#lanphauthuatVattuForm").validate({})

    function initGridVattu() {
        var list = $("#lanphauthuatVattuList");
        if(!list[0].grid) {
            list.jqGrid({
                url: "",
                datatype: "local",
                loadonce: true,
                height: 400,
                width: null,
                shrinkToFit: false,
                colModel: [
                    {label: "ID", name: 'ID', index: 'ID', width: 60, editable: false},
                    {label: "Nghiệp vụ",name: 'MOTA_NGHIEPVU', index: 'MOTA_NGHIEPVU', width: 150, editable: false,
                        cellattr: function (rowId, tv, rawObject, cm, rdata) {
                            return 'style="font-weight: bold;color:' + getColorByNghiepvu(rawObject.NGHIEPVU) + '"';
                        }
                    },
                    {label: "Mã vật tư", name: 'MAVATTU', index: 'MAVATTU', width: 100, editable: false},
                    {label: "Tên vật tư", name: 'TENVATTU', index: 'TENVATTU', width: 200, editable: false,
                        cellattr: function(cellValue, options, rowObject) {
                            if (rowObject && rowObject.LOAI == 'GMHS') {
                                return 'style="color:#28a745; font-weight:bold;white-space: normal;"';
                            }
                            return 'style="white-space: normal;"';
                        }
                    },
                    {label: "DVT", name: 'DVT', index: 'DVT', width: 100, editable: false},
                    {label: "Số lượng", name: 'SOLUONG', index: 'SOLUONG', width: 100, editable: true},
                    {label: "Trạng thái", name: 'TRANGTHAI_HT', index: 'TRANGTHAI_HT', align: 'center', width: 100, editable: false,
                        formatter: function(cellValue, options, rowObject) {
                            if(rowObject && rowObject.TRANGTHAI == 2) {
                                return "<i class='fa fa-exclamation-triangle text-danger'></i> <br/> "+rowObject.GHICHU;
                            }
                            if(rowObject && rowObject.TRANGTHAI == 1) {
                                return "<i class='fa fa-check-square-o text-success'></i> <br/>"+rowObject.GHICHU;
                            }
                            return "<i class='fa fa-exclamation-circle text-warning'></i> <br/> Chưa chốt";
                        },
                    },

                    {label: "Tên loại vật tư",name: 'TENLOAIVATTU', index: 'TENLOAIVATTU', width: 150, editable: false},
                    {label: "Kho",name: 'TENKHOVATTU', index: 'TENKHOVATTU', width: 150, editable: false},
                    {label: "Người tạo",name: 'TEN_NHANVIEN', index: 'TEN_NHANVIEN', width: 150, editable: false},
                    {label: "Ngày tạo ",name: 'NGAYTAO', index: 'NGAYTAO', width: 150, editable: false},
                    {label: "HOATCHAT",name: 'HOATCHAT', index: 'HOATCHAT', width: 150, editable: false, hidden: true},
                    {label: "HAMLUONG",name: 'HAMLUONG', index: 'HAMLUONG', width: 150, editable: false, hidden: true},
                    {label: "CACHSUDUNG",name: 'CACHSUDUNG', index: 'CACHSUDUNG', width: 150, editable: false, hidden: true},
                    {label: "NGHIEPVU",name: 'NGHIEPVU', index: 'NGHIEPVU', width: 150, editable: false, hidden: true},
                    {label: "KHOVATTU",name: 'KHOVATTU', index: 'KHOVATTU', width: 150, editable: false, hidden: true},
                    {label: "DONGIA",name: 'DONGIA', index: 'DONGIA', width: 150, editable: false, hidden: true},
                    {label: "TRANGTHAI",name: 'TRANGTHAI', index: 'TRANGTHAI', width: 150, editable: false, hidden: true},
                    {label: "LOAI",name: 'LOAI', index: 'LOAI', width: 150, editable: false, hidden: true},
                    {label: "GHICHU",name: 'GHICHU', index: 'GHICHU', width: 150, editable: false, hidden: true},
                ],
                rowNum: 1000,
                cellEdit: true,
                cellsubmit: 'clientArray',
                caption: "Danh sách thuốc, dịch truyền/vật tư",
                afterSaveCell: function (rowid, name, val, iRow, iCol) {
                    if(isNaN(val) || val < 0) {
                        notifiToClient("Red", "Số lượng phải là số dương");
                    } else {
                        var rowData = getThongtinRowSelected("lanphauthuatVattuList");
                        showLoaderIntoWrapId("lanphauthuatVattuListWrap")
                        var url = 'cmu_post_CMU_LANPHAUTHUAT_VATTU_UPDATE'
                        if(rowData.LOAI == 'GMHS') {
                            url = 'cmu_post_CMU_LPT_GMHS_CT_THUOC_UPSL'
                        }
                        $.post(url, {
                            url: [singletonObject.dvtt, rowData.ID, val].join("```")
                        }).done(function(data) {
                            loadDSGridVattu()
                            if(data == -1) {
                                return notifiToClient("Red", "Đã chốt số liệu không thể cập nhật");
                            }
                            if(data == 0) {
                                return notifiToClient("Red", "Lỗi cập nhật số lượng");
                            }
                        }).fail(function() {
                            notifiToClient("Red", "Lỗi cập nhật số lượng");
                        }).always(function() {
                            hideLoaderIntoWrapId("lanphauthuatVattuListWrap")
                        });
                    }
                },
                onRightClickRow: function(id) {
                    if (id) {
                        var rowData = getThongtinRowSelected("lanphauthuatVattuList");
                        $.contextMenu('destroy', '#lanphauthuatVattuList tr');
                        var items = {
                            "chuyennghiepvu": {name: '<p><i class="fa fa-exchange text-primary" aria-hidden="true"></i> Chuyển nghiệp vụ</p>'},
                            "mochot": {name: '<p><i class="fa fa-external-link text-primary" aria-hidden="true"></i> Mở  chốt dược</p>'},
                            "xoa": {name: '<p><i class="fa fa-trash-o text-danger" aria-hidden="true"></i> Xóa</p>'}

                        }
                        if(rowData.LOAI == 'GMHS' || rowData.TRANGTHAI == 1) {
                            delete items.xoa;
                        }
                        if(rowData.TRANGTHAI == 0 || rowData.SOLUONG == 0) {
                            delete items.mochot;
                        }
                        if(rowData.NGHIEPVU == 'noitru_toamuangoai') {
                            delete items.chuyennghiepvu;
                        }
                        $.contextMenu({
                            selector: '#lanphauthuatVattuList tr',
                            reposition : false,
                            callback: function (key, options) {
                                if (key == "xoa") {
                                    confirmToClient("Bạn có chắc chắn muốn xóa thuốc/vật tư  này?", function() {
                                        showLoaderIntoWrapId("lanphauthuatVattuListWrap")
                                        $.post("cmu_post_CMU_LANPHAUTHUAT_VATTU_DEL", {
                                            url: [singletonObject.dvtt, rowData.ID].join("```")
                                        }).done(function(data) {
                                            if(data > 0) {
                                                notifiToClient("Green", "Xóa dữ liệu thành công");
                                            } else {
                                                notifiToClient("Red", "Lỗi xóa dữ liệu");
                                            }
                                        }).fail(function() {
                                            notifiToClient("Red", "Lỗi xóa dữ liệu");
                                        }).always(function() {
                                            loadDSGridVattu();
                                            hideLoaderIntoWrapId("lanphauthuatVattuListWrap")
                                        });
                                    })
                                }
                                if (key == 'chuyennghiepvu') {
                                    $("#modalPhauthuatVattuChuyennghiepvu").modal("show")
                                }
                                if (key == 'mochot') {
                                    var idWrap = "lanphauthuatVattuListWrap";
                                    confirmToClient(MESSAGEAJAX.CONFIRM, function() {
                                        showLoaderIntoWrapId(idWrap)
                                        $.get("cmu_list_CMU_LANPHAUTHUAT_SELVT?url="+convertArray([singletonObject.dvtt,
                                            lanPhauThuatTDTObject.ID_DIEUTRI,
                                            rowData.MAVATTU,
                                            rowData.NGHIEPVU,
                                            thongtinhsba.thongtinbn.SOVAOVIEN
                                        ])
                                        ).done(function(data) {
                                            var thanhtoan = 0;
                                            var lanhthuoc = 0;
                                            data.forEach(function(item) {
                                                if(item.TT_THANHTOAN == 1) {
                                                    thanhtoan = 1;
                                                }
                                                if(item.TRANG_THAI > 1) {
                                                    lanhthuoc = 1;
                                                }
                                            })
                                            if(thanhtoan == 1) {
                                                hideLoaderIntoWrapId(idWrap)
                                                return notifiToClient("Red", "Bệnh nhân đã thanh toán");
                                            }
                                            if(lanhthuoc == 1) {
                                                hideLoaderIntoWrapId(idWrap)
                                                return notifiToClient("Red", "Bệnh nhân đã lãnh thuốc");
                                            }
                                            for(var i = 0; i < data.length; i++) {
                                                var url = "noitru_toathuoc_delete";
                                                var ret = data[i];
                                                var arr = [ret.STT_TOATHUOC, ret.MA_TOA_THUOC, singletonObject.dvtt,
                                                    ret.MA_TOA_THUOC,
                                                    thongtinhsba.thongtinbn.STT_BENHAN,
                                                    ret.STT_DOTDIEUTRI,
                                                    lanPhauThuatTDTObject.SOPHIEUTHANHTOAN,
                                                    ret.THANHTIEN_THUOC, ret.MAKHOVATTU, ret.DONGIA_BAN_BH,
                                                    rowData.NGHIEPVU, ret.MAVATTU, ret.TEN_VAT_TU, ret.SO_LUONG];
                                                $.ajax({
                                                    url: url,
                                                    type: "POST",
                                                    data: {
                                                        url: arr.join("```"),
                                                    },
                                                    async: false
                                                })
                                            }
                                            $.post("cmu_post_CMU_LANPHAUTHUAT_VT_MOCHOT", {
                                                url: [singletonObject.dvtt,
                                                    rowData.LOAI,
                                                    rowData.ID
                                                ].join("```")
                                            }).done(function(data) {
                                                notifiToClient("Green", MESSAGEAJAX.EDIT_SUCCESS);
                                            }).fail(function() {
                                                notifiToClient("Red", MESSAGEAJAX.ERROR);
                                            }).always(function() {
                                                loadDSGridVattu();
                                                hideLoaderIntoWrapId(idWrap)
                                            });


                                        }).fail(function() {
                                            notifiToClient("Red", MESSAGEAJAX.ERROR);
                                            hideLoaderIntoWrapId(idWrap)
                                        })

                                    })
                                }
                            },
                            items: items
                        });

                    }
                },
                footerrow: true,
                grouping: true,
                groupingView: {
                    groupField: ["TENLOAIVATTU"],
                    groupColumnShow: [false],
                    groupText: ['<b>{0}</b>'],
                    groupCollapse: false,
                },
                loadComplete: function () {
                    var $self = $(this);
                    var totalRows = $self.jqGrid('getGridParam', 'records');
                    $self.jqGrid("footerData", "set", {DVT: totalRows});
                    $self.jqGrid("footerData", "set", {TENVATTU: "Tổng loại:"});
                }
            });
        }
    }
    function loadDSGridVattu() {
        var url = "cmu_getlist?url="+convertArray([singletonObject.dvtt, lanPhauThuatObject.ID,'CMU_LANPHAUTHUAT_VATTU_SEL']);
        loadDataGridGroupBy($("#lanphauthuatVattuList"), url);
    }

    function updateTrangthaiDuoc(object) {
        return $.post("cmu_post_CMU_LANPHAUTHUAT_THUOCVT_UPD", {
            url: [
                singletonObject.dvtt,
                lanPhauThuatObject.ID,
                object.ID,
                object.LOAI,
                object.TRANGTHAI,
                object.MESSAGE,
                singletonObject.userId
            ].join("```")
        })
    }
})