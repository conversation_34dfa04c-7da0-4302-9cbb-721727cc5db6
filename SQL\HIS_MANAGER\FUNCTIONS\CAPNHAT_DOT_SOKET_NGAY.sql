create or replace function    HIS_MANAGER."CAPNHAT_DOT_SOKET_NGAY"(
    p_dvtt          IN varchar2,
    p_sovaovien     IN number,
    p_ngaytao       IN VARCHAR2,
    p_dotsoket      IN VARCHAR2,
    p_dataBoSung    IN CLOB
)
return number
IS
    v_ngaytao   DATE := TO_DATE(p_ngaytao, 'dd/mm/yyyy hh24:mi');
begin
update vlg_soket15ngay
set ngay_tao = v_ngaytao,
    data_bosung = p_dataBoSung
where sovaovien=p_sovaovien and dvtt=p_dvtt and dot_soket = p_dotsoket;
return 1;
end ;
