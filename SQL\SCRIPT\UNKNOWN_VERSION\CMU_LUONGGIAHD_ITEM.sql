CREATE TABLE "HIS_MANAGER"."CMU_LUONGGIAHD_ITEM"
(	"ID" NUMBER GENERATED ALWAYS AS IDENTITY MINVALUE 1 MAXVALUE 9999999999999999999999999999 INCREMENT BY 1 START WITH 1 CACHE 20 NOORDER  NOCYCLE  NOT NULL ENABLE,
     "DVTT" VARCHAR2(50 BYTE) NOT NULL ENABLE,
     "LOAI" VARCHAR2(255 BYTE),
     "NOIDUNG" VARCHAR2(2000 BYTE),
     "NGUOITAO" VARCHAR2(20 BYTE),
     "KHOA<PERSON><PERSON>" VARCHAR2(20 BYTE),
     "NGAYTAO" DATE,
     CONSTRAINT "PK_LUONGGIAHD_ITEM_ID" PRIMARY KEY ("ID")
)
CREATE INDEX "HIS_MANAGER"."CMU_LUONGGIAHD_ITEMID" ON "HIS_MANAGER"."CMU_LUONGGIAHD_ITEM" ("DVTT");