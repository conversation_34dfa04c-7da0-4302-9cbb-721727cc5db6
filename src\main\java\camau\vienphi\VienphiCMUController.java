/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package camau.vienphi;

//import baocao.Baocaomau15Controller;
import HOSOSUCKHOE.DuocCongdulieuyteDAO;
import VSC.jdbc.JdbcTemplate;
import VSC.jreport.report.util.JasperHelper;
import static com.sun.corba.se.spi.presentation.rmi.StubAdapter.request;

import VSC.jreport.report.util.JasperHelperCMU;
import VSC.ytcs.tiemchung.sms_foder.SendSMS;
import camau.*;
import com.google.gson.JsonObject;
import com.google.gson.JsonParser;
import com.squareup.okhttp.OkHttpClient;
import com.vnpt.signservice.client.api.KySoServiceHelper;
import com.vnpt.signservice.client.api.SignatureService;
import com.vnpt.signservice.client.model.SignAdvanceParameter;
import dangnhap.SessionFilter;
import dangnhap.UserDAO;
import dangnhap.UserDAOImp;
import dmthamsodonvi.thamsodonviDAO;

import java.io.*;
import java.net.URISyntaxException;
import java.net.URL;
import java.net.URLDecoder;
import java.sql.SQLException;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;
import javax.sql.DataSource;
import javax.swing.filechooser.FileSystemView;
import khambenh.KhambenhDAO;
import kiengiang.KGGUtil;
import l2.L2Utils;
import l2.ThamSoManager;
import lichsusudung.LichsusudungDAO;
import lichsusudung.LichsusudungObj;
import logAction.LogActionDAO;
import net.sf.jasperreports.engine.*;
import net.sf.jasperreports.engine.export.ooxml.JRDocxExporter;
import net.sf.jasperreports.engine.util.JRLoader;
import net.sf.jasperreports.engine.util.JRProperties;
import net.sf.jasperreports.export.ExporterInputItem;
import net.sf.jasperreports.export.SimpleExporterInput;
import net.sf.jasperreports.export.SimpleExporterInputItem;
import net.sf.jasperreports.export.SimpleOutputStreamExporterOutput;
import org.apache.http.impl.client.HttpClients;
import org.apache.pdfbox.pdmodel.PDDocument;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.jdbc.datasource.DataSourceUtils;
import org.springframework.messaging.simp.SimpMessageSendingOperations;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.util.LinkedCaseInsensitiveMap;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.servlet.ModelAndView;
import phanquyen.PhanquyenDAO;
import smartca.model.*;
import smartca.model.Account;
import smartca.request.SignRequest;
import smartca.service.SmartCADAO;
import smartca.service.SmartCAService;
import socket.ChatMessage;
import soctrang.BangkekcbtheokhoaDAO;
import soctrang.BaocaochiphiduocDAO;
import soctrang.BaocaothongkecanlamsangDAO;
import soctrang.dmphongban.STG_phongbanDAO;
import soctrang.vienphi.STG_VienphiObj;
import thamsohethong.Thamsohethong;
import tienich.docso;
import tienich.tienich;
import vienphi.VienphiDAO;
import vienphi.xemthutienvienphiDAO;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
//import static vungtau.BaoCaoHDCLSListExcelView_Unicode.dvtt;
import java.util.HashMap;
import java.util.ArrayList;
import java.net.HttpURLConnection;
import java.net.URLConnection;
import java.net.URL;
import java.util.*;
import java.util.Base64;
import java.util.Base64.Decoder;
import java.nio.charset.StandardCharsets;
import java.util.Enumeration;
import java.net.*;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.gson.Gson;
//import com.vnpt.hashsignature.pdf.PdfHashSigner.FontName;
//import com.vnpt.hashsignature.pdf.PdfHashSigner.FontStyle;
//import com.vnpt.hashsignature.pdf.PdfHashSigner.RenderMode;
//import com.vnpt.hashsignature.pdf.PdfSignatureComment;
//import com.vnpt.hashsignature.pdf.PdfSignatureComment.Types;
//import com.vnpt.hashsignature.pdf.PdfSignatureView;
//import com.vnpt.hashsignature.signer.SignatureParameter;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.concurrent.TimeUnit;

import org.json.JSONArray;
import org.json.JSONObject;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.util.EntityUtils;

/**
 *
 * <AUTHOR> Xuân Dũng
 */
@Controller
public class VienphiCMUController {

    @Autowired
    SessionFilter SessionFilter;

    @Autowired
    VienphiDAO vienphiDAO;

    @Autowired
    VienphiCMUDAO VienphiCMUDAO;

    @Autowired
    KhambenhDAO khambenhDAO;

    @Autowired
    BaocaothongkecanlamsangDAO BaocaothongkecanlamsangDAO;

    @Autowired
    BangkekcbtheokhoaDAO BangkekcbtheokhoaDAO;

    @Autowired
    BaocaochiphiduocDAO BaocaochiphiduocDAO;

    @Autowired
    KhambenhDAO KhambenhDAO;

    @Autowired
    xemthutienvienphiDAO xemthutienvienphiDAO;

    @Autowired
    LichsusudungDAO LichsusudungDAO;
    @Autowired
    thamsodonviDAO thamsodonviDAO;

    @Autowired
    @Resource(name = "dataSourceMNG")
    DataSource dataSourceMNG;

    @Autowired
    @Resource(name = "dataSource_baocao")
    DataSource dataSource_baocao;

    //Ân thêm
    @Autowired
    phanquyen.PhanquyenDAO PhanquyenDAO;

    @Autowired
    UserDAO userDAO;

    @Autowired
    soctrang.dmphongban.STG_phongbanDAO STG_phongbanDAO;

    @Autowired
    SmartCADAO smartCADAO;

    @Autowired
    private SimpMessageSendingOperations messagingTemplate;

    @Autowired
    DuocCongdulieuyteDAO duocCongdulieuyteDAO;


    @Autowired
    LogActionDAO logActionDAO;

    @Autowired
    CMUSmartCAService cmuSmartCAService;

    @Autowired
    CMUApiService CMUApiService;


    @RequestMapping(value = "/cmu_vienphicobaohiem")
    public ModelAndView cmu_vienphicobaohiem(ModelMap mm, HttpSession session) {
        if (!SessionFilter.checkSession(session)) {
            return SessionFilter.redirectLogin2();
        }
        mm.put("ngayhientai", tienich.layngayhientai());
        mm.put("apdungquyenbienlai", thamsodonviDAO.laythamso_donvi_motthamso(session.getAttribute("Sess_DVTT").toString(), "86"));
        return new ModelAndView("camau/vienphi/Vienphicobaohiem");
    }

    @RequestMapping(value = "/cmu_vienphikhongbaohiem")
    public ModelAndView cmu_vienphikhongbaohiem(ModelMap mm, HttpSession session) {
        if (!SessionFilter.checkSession(session)) {
            return SessionFilter.redirectLogin2();
        }
        mm.put("ngayhientai", tienich.layngayhientai());
        mm.put("apdungquyenbienlai", thamsodonviDAO.laythamso_donvi_motthamso(session.getAttribute("Sess_DVTT").toString(), "86"));
        return new ModelAndView("/camau/vienphi/Vienphikhongbaohiem");
    }

    @RequestMapping(value = "/cmu_baocaobienlaivienphi")
    public ModelAndView cmu_baocaobienlaivienphi(ModelMap mm, HttpSession session) {
        if (!SessionFilter.checkSession(session)) {
            return SessionFilter.redirectLogin2();
        }
        mm.addAttribute("phongban", BaocaothongkecanlamsangDAO.danhsach_phongban(session.getAttribute("Sess_DVTT").toString()));
        mm.put("ngayhientai", tienich.layngayhientai());
        return new ModelAndView("camau/vienphi/baocaobienlaivienphi");
    }

    @RequestMapping(value = "/cmu_kiemtrasobienlai")
    public @ResponseBody
    List cmu_kiemtrasobienlai(@RequestParam(value = "dvtt") String dvtt, @RequestParam(value = "quyenbienlai") String quyenbienlai,
            @RequestParam(value = "sobienlai") String sobienlai) {
        return VienphiCMUDAO.kiemtrasobienlai(dvtt, quyenbienlai, sobienlai);
    }

    @RequestMapping(value = "/cmu_kiemlockbienlai")
    public @ResponseBody
    List cmu_kiemlockbienlai(@RequestParam(value = "dvtt") String dvtt, @RequestParam(value = "quyenbienlai") String quyenbienlai,
            @RequestParam(value = "sobienlai") String sobienlai, @RequestParam(value = "huylock") String huylock) {
        return VienphiCMUDAO.kiemtralockbienlai(dvtt, quyenbienlai, sobienlai, huylock);
    }

    @RequestMapping(value = "/cmu_themlantt_cobhyt_giamtai")
    public @ResponseBody
    String cmu_themlantt_cobhyt_giamtai(@RequestParam(value = "url") String url, HttpSession session) throws UnsupportedEncodingException {
        Thamsohethong tsht = (Thamsohethong) session.getAttribute("Sess_Thamso");
        url = URLDecoder.decode(url, "UTF-8");
        String arr[] = url.split("```");
        STG_VienphiObj vp = new STG_VienphiObj();
        String makb = arr[0];
        String dvtt = arr[8];
        vp.dvtt = arr[8];
        vp.sophieutt = arr[0];
        vp.ngaygiotao = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new java.util.Date());
        vp.nhanvientao = arr[1];
        vp.sotienthanhtoan = arr[2];
        vp.sotienbntra = arr[3];
        vp.sotienthoilai = arr[4];
        vp.stt_lantt = "1";
        vp.thanhtoandaodong = Boolean.parseBoolean(arr[5]);
        vp.sobienlai = (arr[6].equals("-1") ? null : arr[6]);
        vp.ghichu = arr[7];
        Map map_tt;
        Map map_temp;
        vp.ngay = new SimpleDateFormat("yyyy-MM-dd").format(new java.util.Date());
        String sql = "select MA_KHAM_BENH,ifnull(DOTDIEUTRIDAUTIEN,0) as DOTDIEUTRIDAUTIEN,ifnull(DIEUTRICHUYENKHOA,0) as DIEUTRICHUYENKHOA, NGAY_THANH_TOAN "
                + " from kb_phieuthanhtoan "
                + " where SOPHIEUTHANHTOAN = ? "
                + " and DVTT = ?;";
        if (arr[15].equals("0")) {
            sql = "select MA_KHAM_BENH,SOPHIEUTHANHTOAN,ifnull(DOTDIEUTRIDAUTIEN,0) as DOTDIEUTRIDAUTIEN,ifnull(DIEUTRICHUYENKHOA,0) as DIEUTRICHUYENKHOA, NGAY_THANH_TOAN "
                    + " from kb_phieuthanhtoan "
                    + " where ID_TIEPNHAN = ? "
                    + " and DVTT = ?;";
            map_tt = vienphiDAO.queryForMap(sql, new Object[]{vp.sophieutt, vp.dvtt});
        } else {
            map_tt = vienphiDAO.queryForMap(sql, new Object[]{vp.sophieutt, vp.dvtt});
        }

        if (!map_tt.isEmpty()) {
            vp.makhambenh = map_tt.get("MA_KHAM_BENH").toString();
            vp.idtiepnhan = vp.makhambenh.replaceFirst("kb_", "");
            vp.landieutrichuyenkhoa = map_tt.get("DIEUTRICHUYENKHOA").toString();
            vp.tt_landieutrichuyenkhoa = map_tt.get("DOTDIEUTRIDAUTIEN").toString();
            vp.ngaybangke = map_tt.get("NGAY_THANH_TOAN").toString();
            if (arr[15].equals("0")) {
                vp.sophieutt = map_tt.get("SOPHIEUTHANHTOAN").toString();
            }
        }
        sql = "select TI_LE_MIEN_GIAM,DUNG_TUYEN,SO_THE_BHYT "
                + " from kb_tiep_nhan "
                + " where DVTT= ? "
                + " and ID_TIEPNHAN = ?;";
        map_tt = vienphiDAO.queryForMap(sql, new Object[]{vp.dvtt, vp.idtiepnhan});
        if (!map_tt.isEmpty()) {
            vp.phantrambaohiem = map_tt.get("TI_LE_MIEN_GIAM").toString();
            vp.dungtuyen = Boolean.parseBoolean(map_tt.get("DUNG_TUYEN").toString());
            vp.sothebhyt = map_tt.get("SO_THE_BHYT").toString();
        }
        vp.capdonvi = tsht.tuyenbenhvien;
        /*sql = "select TILEMIENGIAM "
         + " from his_public_list.dm_doi_tuong_bhyt "
         + " where  MA_DOI_TUONG_BHYT=?";
         String madoituong = vp.sothebhyt.substring(0, 3);
         vp.phantramthebhyt = vienphiDAO.queryForString(sql, new Object[]{madoituong});*/
        vp.trangthai = "0";
        vp.tongbhyt = "0";
        vp.cantren = "0";
        vp.kenhinbangke = tsht.kenhinbangke;
        vp.sotienbenhnhanphaitt = arr[9];
        vp.ma_quyen_bienlai = (arr[11].equals("-1") ? null : arr[11]);
        vp.ten_quyen_bienlai = (arr[12].equals("-1") ? null : arr[12]);
        int tt_all = Integer.parseInt(arr[13]);
        String sophieuthanhtoan = "!!!" + arr[14] + "!!!";// Để dễ so sánh trong MYSQL
        LichsusudungObj objls = new LichsusudungObj(vp.dvtt, "Thanh toán viện phí có BHYT cho bệnh nhân có số phiếu " + vp.sophieutt, session.getAttribute("Sess_UserID").toString() + "-" + session.getAttribute("Sess_User"), "Thanh toán");
        LichsusudungDAO.them_lichsusudung(objls);

        return VienphiCMUDAO.insert_lantt_cobhyt_gt(vp, tt_all, sophieuthanhtoan);
    }

    @RequestMapping(value = "/cmu_themlantt_kobhyt_giamtai")
    public @ResponseBody
    String cmu_themlantt_kobhyt_giamtai(@RequestParam(value = "url") String url, HttpSession session) throws UnsupportedEncodingException {
        Thamsohethong tsht = (Thamsohethong) session.getAttribute("Sess_Thamso");
        url = URLDecoder.decode(url, "UTF-8");
        String arr[] = url.split("```");
        STG_VienphiObj vp = new STG_VienphiObj();
        String makb = arr[0];
        String dvtt = arr[8];
        vp.dvtt = arr[8];
        vp.idtiepnhan = makb;
        vp.ngaygiotao = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new java.util.Date());
        vp.nhanvientao = arr[1];
        vp.sotienthanhtoan = arr[2];
        vp.sotienbntra = arr[3];
        vp.sotienthoilai = arr[4];
        vp.stt_lantt = "1";
        vp.thanhtoandaodong = Boolean.parseBoolean(arr[5]);
        vp.sobienlai = (arr[6].equals("-1") ? null : arr[6]);
        vp.ghichu = arr[7];

        vp.ngay = new SimpleDateFormat("yyyy-MM-dd").format(new java.util.Date());

        vp.capdonvi = tsht.tuyenbenhvien;
        /*sql = "select TILEMIENGIAM "
         + " from his_public_list.dm_doi_tuong_bhyt "
         + " where  MA_DOI_TUONG_BHYT=?";
         String madoituong = vp.sothebhyt.substring(0, 3);
         vp.phantramthebhyt = vienphiDAO.queryForString(sql, new Object[]{madoituong});*/

        vp.sotienbenhnhanphaitt = arr[9];
        vp.ma_quyen_bienlai = (arr[11].equals("-1") ? null : arr[11]);
        vp.ten_quyen_bienlai = (arr[12].equals("-1") ? null : arr[12]);
        int tt_all = Integer.parseInt(arr[13]);
        String sophieuthanhtoan = "!!!" + arr[14] + "!!!";// Để dễ so sánh trong MYSQL
        LichsusudungObj objls = new LichsusudungObj(vp.dvtt, "Thanh toán viện phí có BHYT cho bệnh nhân có số phiếu " + vp.sophieutt, session.getAttribute("Sess_UserID").toString() + "-" + session.getAttribute("Sess_User"), "Thanh toán");
        LichsusudungDAO.them_lichsusudung(objls);

        return VienphiCMUDAO.cmu_insert_lantt(vp, tt_all, sophieuthanhtoan);
    }
    // CMU bảng kê tổng hợp thu chi

    @RequestMapping(value = "/cmu_tonghopthuchi")
    public ModelAndView cmu_tonghopthuchi(ModelMap mm, HttpSession session) {
        if (!SessionFilter.checkSession(session)) {
            return SessionFilter.redirectLogin2();
        }
        mm.put("ngayhientai", tienich.layngayhientai());
        return new ModelAndView("camau/tonghopthuchi");
    }

    @RequestMapping(value = "/cmu_intonghopthuchi", produces = "application/json; charset=utf-8")
    public @ResponseBody
    void cmu_intonghopthuchi(@RequestParam(value = "url") String url, HttpSession session, HttpServletResponse response, HttpServletRequest request) throws SQLException {

        thamsohethong.Thamsohethong tsht = (thamsohethong.Thamsohethong) session.getAttribute("Sess_Thamso");
        String tenbenhvien = tsht.tenbenhvien.toUpperCase();
        String arr[] = url.split("```");
        String tungay = arr[0];
        String denngay = arr[1];
        String dvtt = arr[2];
        String noitru = arr[3];
        String manhanvien = arr[4];
        String tennhanvien = arr[5];
        String tieude;
        String tieude2;

        if (noitru.equals("0")) {
            tieude = "BẢNG KÊ TỔNG HỢP THU CHI NGOẠI TRÚ";
        } else if (noitru.equals("1")) {
            tieude = "BẢNG KÊ TỔNG HỢP THU CHI NỘI TRÚ";
        } else {
            tieude = "BẢNG KÊ TỔNG HỢP THU CHI";
        }

        if (arr[0].equals(arr[1])) {
            tieude2 = "NGÀY " + arr[0];
        } else {
            tieude2 = "TỪ NGÀY " + arr[0] + " ĐẾN NGÀY " + arr[1];
        }

        String ngaythangnam = new SimpleDateFormat("dd-MM-yyyy").format(new java.util.Date());
        String ngay[] = ngaythangnam.split("-");
        String ngaylapbieu = "Ngày " + ngay[0] + " tháng " + ngay[1] + " năm " + ngay[2];

        String tongtien = VienphiCMUDAO.cmu_tonghopthuchi_tongtien(tungay, denngay, dvtt, noitru, manhanvien);
        // CMU đọc số âm
        Double sotiendoc;
        String sotienbangchu;
        if (Double.parseDouble(tongtien) < 0) {
            sotiendoc = -Double.parseDouble(tongtien);
            sotienbangchu = "Âm " + new docso().docso(sotiendoc);

        } else {
            sotiendoc = Double.parseDouble(tongtien);
            sotienbangchu = new docso().docso(sotiendoc);
        }
        // CMU đọc số âm
        sotienbangchu = new docso().docso(sotiendoc);
        Map parameters = new HashMap();
        String donviquanlytructiep = "SỞ Y TẾ " + tsht.tinh.toUpperCase();
        if (!tsht.tendonviquanlytructiep.equals("")) {
            donviquanlytructiep = tsht.tendonviquanlytructiep.toUpperCase();
        }
        parameters.put("soyte", donviquanlytructiep);
        parameters.put("tenbenhvien", tenbenhvien);
        parameters.put("tieude", tieude);
        parameters.put("tieude2", tieude2);
        parameters.put("tungay", tungay);
        parameters.put("denngay", denngay);
        parameters.put("dvtt", dvtt);
        parameters.put("noitru", noitru);
        parameters.put("manhanvien", manhanvien);
        parameters.put("tennhanvien", tennhanvien);
        parameters.put("nguoilapbieu", session.getAttribute("Sess_User").toString());
        parameters.put("ngaylapbieu", ngaylapbieu);
        parameters.put("tongtien", tongtien);
        parameters.put("sotienbangchu", sotienbangchu);

        File reportFile;
        Map<String, Object> map_duongdan = khambenhDAO.select_duongdanreport(dvtt, "96013");
        if(map_duongdan != null && !map_duongdan.isEmpty()){
            String duong_dan = map_duongdan.get("DUONG_DAN").toString();
            reportFile = new File(request.getSession().getServletContext().getRealPath("" + duong_dan + ""));
        } else {
            reportFile = new File(request.getSession().getServletContext().getRealPath("/WEB-INF/pages/camau/rp_tonghopthuchi.jasper"));
        }

        JasperHelper.printReport("pdf", reportFile, parameters, this.dataSourceMNG.getConnection(), response);

    }

    @RequestMapping(value = "/cmu_intonghopthuchi_tonghop", produces = "application/json; charset=utf-8")
    public @ResponseBody
    void cmu_intonghopthuchi_tonghop(@RequestParam(value = "url") String url, @RequestParam(value = "type") String type, HttpSession session, HttpServletResponse response, HttpServletRequest request) throws SQLException {
        thamsohethong.Thamsohethong tsht = (thamsohethong.Thamsohethong) session.getAttribute("Sess_Thamso");
        String tenbenhvien = tsht.tenbenhvien.toUpperCase();
        String arr[] = url.split("```");
        String tungay = arr[0];
        String denngay = arr[1];
        String dvtt = arr[2];
        String noitru = arr[3];
        String manhanvien = arr[4];
        String tennhanvien = arr[5];
        String tieude;
        String tieude2;
        if (noitru.equals("0")) {
            tieude = "BẢNG KÊ TỔNG HỢP THU CHI NGOẠI TRÚ";
        } else if (noitru.equals("1")) {
            tieude = "BẢNG KÊ TỔNG HỢP THU CHI NỘI TRÚ";
        } else {
            tieude = "BẢNG KÊ TỔNG HỢP THU CHI";
        }

        if (arr[0].equals(arr[1])) {
            tieude2 = "NGÀY " + arr[0];
        } else {
            tieude2 = "TỪ NGÀY " + arr[0] + " ĐẾN NGÀY " + arr[1];
        }

        String ngaythangnam = new SimpleDateFormat("dd-MM-yyyy").format(new java.util.Date());
        String ngay[] = ngaythangnam.split("-");
        String ngaylapbieu = "Ngày " + ngay[0] + " tháng " + ngay[1] + " năm " + ngay[2];

        String tongtien = VienphiCMUDAO.cmu_tonghopthuchi_tonghop_tongtien(dvtt, tungay, denngay, manhanvien, noitru);
        if (tongtien == null) {
            tongtien = "0";// Xử lý khi null
        }
        String sotienbangchu = new docso().docso(Double.parseDouble(tongtien));
        Map parameters = new HashMap();
        String donviquanlytructiep = "SỞ Y TẾ " + tsht.tinh.toUpperCase();
        if (!tsht.tendonviquanlytructiep.equals("")) {
            donviquanlytructiep = tsht.tendonviquanlytructiep.toUpperCase();
        }
        parameters.put("soyte", donviquanlytructiep);
        parameters.put("tenbenhvien", tenbenhvien);
        parameters.put("tieude", tieude);
        parameters.put("tieude2", tieude2);
        parameters.put("tungay", tungay);
        parameters.put("denngay", denngay);
        parameters.put("dvtt", dvtt);
        parameters.put("noitru", Integer.parseInt(noitru));
        parameters.put("manhanvien", manhanvien);
        parameters.put("tennhanvien", tennhanvien);
        parameters.put("nguoilapbieu", session.getAttribute("Sess_User").toString());
        parameters.put("ngaylapbieu", ngaylapbieu);
        parameters.put("tongtien", Double.parseDouble(tongtien));
        parameters.put("sotienbangchu", sotienbangchu);
        File reportFile = new File(request.getSession().getServletContext().getRealPath("/WEB-INF/pages/camau/rp_vienphi_tonghop_theonhanvien.jasper"));
        if (type.equals("xls")) {
            JasperHelper.printReport("xls", reportFile, parameters, DataSourceUtils.getConnection(dataSourceMNG), response);
        } else {
            JasperHelper.printReport("pdf", reportFile, parameters, DataSourceUtils.getConnection(dataSourceMNG), response);
        }

    }

    @RequestMapping(value = "/cmu_bangkechitietphatsinhtheokhoaphong")
    public ModelAndView bangkechitietphatsinhtheokhoaphong(ModelMap mm, HttpSession session) {
        if (!SessionFilter.checkSession(session)) {
            return SessionFilter.redirectLogin2();
        }
        String dvtt = session.getAttribute("Sess_DVTT").toString();
        String noitru = "-1";
        mm.addAttribute("nhanvien", xemthutienvienphiDAO.dsnhanvien(dvtt, noitru));
        //mm.addAttribute("phongban", khambenhDAO.danhsach_phongban(session.getAttribute("Sess_DVTT").toString()));
        mm.put("ngayhientai", tienich.layngayhientai());
        return new ModelAndView("camau/bangkechitietphatsinhtheokhoaphong");
    }

    @RequestMapping(value = "/xembangkechitietphatsinhtheokhoaphong", method = RequestMethod.GET)
    public @ResponseBody
    void xembangkechitietphatsinhtheokhoaphong(@RequestParam(value = "url") String url, HttpSession session, HttpServletRequest request, HttpServletResponse response) throws Exception {
        thamsohethong.Thamsohethong tsht = (thamsohethong.Thamsohethong) session.getAttribute("Sess_Thamso");
        String[] arr = url.split("```");
        String tenbenhvien = tsht.tenbenhvien.toUpperCase();
        String dvtt = arr[0];
        String tungay = arr[1];
        String denngay = arr[2];
        String hinhthuc = arr[3];
        String loaifile = arr[4];
        String tamung = arr[5];
        int manhanvien = Integer.parseInt(arr[6]);
        String tennhanvien = arr[7];
        String tenhinhthuc = "";
        String tentamung = "";
        if ("0".equals(tamung)) {
            tentamung = "";
        } else if ("1".equals(tamung)) {
            tentamung = "THU TẠM ỨNG";
        } else if ("2".equals(tamung)) {
            tentamung = "THU VIỆN PHÍ";
        }
        if ("1".equals(hinhthuc)) {
            tenhinhthuc = "NGOẠI TRÚ";
        } else if ("2".equals(hinhthuc)) {
            tenhinhthuc = "NỘI TRÚ";
        } else if ("0".equals(hinhthuc)) {
            tenhinhthuc = "";
        }
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");
        Date tungay_ = format.parse(tungay);
        Date denngay_ = format.parse(denngay);
        String ngaythangnam = new SimpleDateFormat("dd/MM/yyyy").format(new java.util.Date());
        String[] arr_ngay = ngaythangnam.split("/");
        Map parameters = new HashMap();
        File reportFile;
        parameters.put("dvtt", dvtt);
        parameters.put("thoigian", "Từ ngày " + new SimpleDateFormat("dd/MM/yyyy").format(tungay_)
                + " đến " + new SimpleDateFormat("dd/MM/yyyy").format(denngay_));
        parameters.put("tungay", tungay);
        parameters.put("denngay", tungay);
        parameters.put("ngay", arr_ngay[0]);
        parameters.put("thang", arr_ngay[1]);
        parameters.put("nam", arr_ngay[2]);
        parameters.put("hinhthuc", Integer.parseInt(hinhthuc));
        parameters.put("tenhinhthuc", tenhinhthuc);
        parameters.put("tamung", Integer.parseInt(tamung));
        parameters.put("tentamung", tentamung);
        parameters.put("manhanvien", manhanvien);
        parameters.put("tennhanvien", tennhanvien);
        parameters.put("nguoilapbieu", session.getAttribute("Sess_User").toString());
        String donviquanlytructiep = "SỞ Y TẾ " + tsht.tinh.toUpperCase();
        if (!tsht.tendonviquanlytructiep.equals("")) {
            donviquanlytructiep = tsht.tendonviquanlytructiep.toUpperCase();
        }
        parameters.put("tensoyte", donviquanlytructiep);
        parameters.put("tenbenhvien", tenbenhvien);

        reportFile = new File(request.getSession().getServletContext().getRealPath("/WEB-INF/pages/camau/rp_bangkechitietphatsinhtheokhoaphong.jasper"));

        if (loaifile.equals("pdf")) {
            JasperHelper.printReport("pdf", reportFile, parameters, DataSourceUtils.getConnection(dataSourceMNG), response);
        } else if (loaifile.equals("xls")) {
            JasperHelper.printReport("xls", reportFile, parameters, DataSourceUtils.getConnection(dataSourceMNG), response);
        }
    }

    @RequestMapping(value = "/cmu_baocaoxntheoluot", method = RequestMethod.GET)
    public ModelAndView baoCaoTheoLuot(ModelMap mm, HttpSession session){
        if (!SessionFilter.checkSession(session)) {
            return SessionFilter.redirectLogin2();
        }
        mm.put("ngayhientai", tienich.layngayhientai());
        return new ModelAndView("camau/xetnghiem/bc_xetnghiem_theoluot");
    }

    @RequestMapping(value = "/cmu_inbaocaoxntheoluot", method = RequestMethod.GET)
    @ResponseBody
    public void inBaoCaoTheoLuot(HttpSession session, HttpServletResponse response, HttpServletRequest request, @RequestParam(value = "url") String url){
        if (!SessionFilter.checkSession(session)) {
            SessionFilter.redirectLogin3(response);
        }

        try{
            String arr[] = url.split("```");
            Map parameters = new HashMap();
            String dvtt = session.getAttribute("Sess_DVTT").toString();
            Thamsohethong tsht = (Thamsohethong) session.getAttribute("Sess_Thamso");
            parameters.put("DVTT", dvtt);
            parameters.put("tuNgay", arr[0]);
            parameters.put("denNgay", arr[1]);
            parameters.put("tenBenhVien", tsht.tenbenhvien);
            parameters.put("soYTe", tsht.tinh);
            parameters.put("hinhthuc", Integer.valueOf(arr[3]));
            parameters.put("loaidv", Integer.valueOf(arr[4]));
            // CMU kiểu báo cáo
            // parameters.put("kieubc", Integer.valueOf(arr[5]));
            // CMU kiểu báo cáo
            long date = new Date().getTime();
            File reportFile;
            if(arr[5].equals("1"))
                reportFile = new File(request.getSession().getServletContext().getRealPath("/WEB-INF/pages/camau/xetnghiem/rp_baocaoxntheoluot.jasper"));
            else
                reportFile = new File(request.getSession().getServletContext().getRealPath("/WEB-INF/pages/camau/xetnghiem/rp_baocaoxntheoluot_khoa.jasper"));

            if (arr[2].equals("1")) {
                JasperHelper.printReport("pdf", reportFile, parameters, this.dataSourceMNG.getConnection(), response);
            } else  {
                JasperHelper.printReport("xls", reportFile, parameters, this.dataSourceMNG.getConnection(), response);
            }

        }catch(NumberFormatException | SQLException  ex){
            System.out.println(ex.getMessage());
        }
    }


    @RequestMapping(value = "/cmu_noitru_capnhat_sogiuong", method = RequestMethod.GET)
    @ResponseBody
    public String cmu_noitru_capnhat_sogiuong(HttpSession session, HttpServletResponse response, HttpServletRequest request, @RequestParam(value = "url") String url){
         String arr[] = url.split("```");
        return VienphiCMUDAO.cmu_up_giuongbuong(arr[0],arr[1],arr[2],arr[3],arr[4],arr[5]);
    }


    @RequestMapping(value = "/cmu_lay_so_giuong_buong", method = RequestMethod.GET)
    @ResponseBody
    public List cmu_lay_so_giuong_buong(HttpSession session, HttpServletResponse response, HttpServletRequest request, @RequestParam(value = "url") String url){
         String arr[] = url.split("```");
        return VienphiCMUDAO.cmu_lay_giuongbuong(session.getAttribute("Sess_DVTT").toString(),
                arr[0],arr[1]);

    }


    @RequestMapping(value = "/cmu_inhoadon_lienluu", method = RequestMethod.GET)
    @ResponseBody
    public void cmu_inhoadon_lienluu(HttpSession session, HttpServletResponse response, HttpServletRequest request, @RequestParam(value = "url") String url){
        if (!SessionFilter.checkSession(session)) {
            SessionFilter.redirectLogin3(response);
        }

        try{
            String arr[] = url.split("```");
            Map parameters = new HashMap();
            String dvtt = session.getAttribute("Sess_DVTT").toString();
            Thamsohethong tsht = (Thamsohethong) session.getAttribute("Sess_Thamso");
            parameters.put("DVTT", dvtt);

            parameters.put("tenbenhvien", tsht.tenbenhvien);
            String sotienbangchu = new docso().docso(Double.parseDouble(arr[1]));
            String ngaythangnam  = "Ngày " + arr[7] + " tháng " + arr[8] + " năm " + arr[9];
            String nguoithutien = arr[6];

            parameters.put("TEN_BENH_NHAN", arr[3]);
            parameters.put("DIA_CHI", arr[2]);
            parameters.put("sotienbangso", arr[1]);
            parameters.put("sotienbangchu", sotienbangchu);
            parameters.put("ngaythangnam", ngaythangnam);
            parameters.put("nguoithutien", nguoithutien);
            parameters.put("lienluu", arr[10]);
            parameters.put("sobienlai", arr[4]);
            parameters.put("maquyenbienlai", arr[5]);
            File reportFile = new File(request.getSession().getServletContext().getRealPath("/WEB-INF/pages/camau/vienphi/rp_phieuthutiennoitru.jasper"));
            JasperHelper.printReport("pdf", reportFile, parameters, this.dataSourceMNG.getConnection(), response);

        }catch(NumberFormatException | SQLException  ex){
            System.out.println(ex.getMessage());
        }
    }

    //CMU 16/06/2017
    @RequestMapping(value = "/cmu_thongkethoigiankham", method = RequestMethod.GET)
    public ModelAndView cmu_thongkethoigiankham(ModelMap mm, HttpSession session){
        if (!SessionFilter.checkSession(session)) {
            return SessionFilter.redirectLogin2();
        }
        String dvtt = session.getAttribute("Sess_DVTT").toString();
        mm.put("ngayhientai", tienich.layngayhientai());
        mm.put("phongban", PhanquyenDAO.ds_phongban(dvtt));
        mm.put("phongbenh", userDAO.layphongbenh(dvtt));
        return new ModelAndView("camau/cmu_thongkethoigiankham");
    }

    @RequestMapping(value = "/cmu_inbaocaotkkb", method = RequestMethod.GET)
    @ResponseBody
    public void cmu_inbaocaotkkb(HttpSession session, HttpServletResponse response,
            HttpServletRequest request, @RequestParam(value = "dvtt") String dvtt,
            @RequestParam(value = "tungay") String tungay,
            @RequestParam(value = "denngay") String denngay,
            @RequestParam(value = "hinhthucin") String hinhthucin,
            @RequestParam(value = "khoa") String khoa,
            @RequestParam(value = "phong") String phong

            ){
        if (!SessionFilter.checkSession(session)) {
            SessionFilter.redirectLogin3(response);
        }

        try{

            Map parameters = new HashMap();

            Thamsohethong tsht = (Thamsohethong) session.getAttribute("Sess_Thamso");
            parameters.put("dvtt", dvtt);

            parameters.put("tenbenhvien", tsht.tenbenhvien);
            String arr1[] = tungay.split("-");
            String arr2[] = denngay.split("-");
            String ngaythangnam  = "Từ Ngày " +arr1[2]+"-"+arr1[1]+"-"+arr1[0]  + " đến " + arr2[2]+"-"+arr2[1]+"-"+arr2[0];
            parameters.put("tenbenhvien", tsht.tenbenhvien);
            parameters.put("tensoyte", tsht.tinh);
            parameters.put("ngaythangnam", ngaythangnam);
            parameters.put("tungay", tungay);
            parameters.put("denngay", denngay);
            parameters.put("khoa", khoa);
            parameters.put("phong", phong);
            File reportFile = new File(request.getSession().getServletContext().getRealPath("/WEB-INF/pages/camau/rp_bctgkhambenh.jasper"));
            if (hinhthucin.equals("1")) {
                JasperHelper.printReport("pdf", reportFile, parameters, this.dataSourceMNG.getConnection(), response);
            } else  {
                JasperHelper.printReport("xls", reportFile, parameters, this.dataSourceMNG.getConnection(), response);
            }

        }catch(NumberFormatException | SQLException  ex){
            System.out.println(ex.getMessage());
        }
    }
    //END CMU 16/06/2017

    // CMU 21/07/2017
    @RequestMapping(value = "/cmu_getlist", method = RequestMethod.GET)
    @ResponseBody
    public List cmu_getlist(
            HttpSession session,
            HttpServletResponse response,
            HttpServletRequest request, @RequestParam(value = "url") String url){
        String arr[] = url.split("```");
        Object[] obj = new Object[arr.length-1];
        String parameter = "";
        String typeofparameter = "c,";
        for(int i = 0; i < arr.length-1;i++) {
            obj[i] = arr[i];
            parameter +="?";
            typeofparameter +="s";
            if(i != arr.length-2) {
                parameter +=",";
                typeofparameter +=",";
            }
        }
        String store = VienphiCMUDAO.cmu_getstring(new Object[]{arr[arr.length - 1]}, "CMU_GETSTORE(?)#s,s");
        if(store == null || store.isEmpty()) {
            store = arr[arr.length - 1] + "(" + parameter + ")#"+typeofparameter;
        }
        return VienphiCMUDAO.cmu_getlist(obj,store);

    }

    @RequestMapping(value = "/cmu_list_{id}", method = RequestMethod.GET)
    @ResponseBody
    public List cmu_getlist(@PathVariable("id") String id,
            HttpSession session,
            HttpServletResponse response,
            HttpServletRequest request, @RequestParam(value = "url") String url){
        String arr[] = url.split("```");
        Object[] obj = new Object[arr.length];
        String parameter = "";
        String typeofparameter = "c,";
        for(int i = 0; i < arr.length ; i++) {
            obj[i] = arr[i];
            parameter +="?";
            typeofparameter +="s";
            if(i != arr.length-1) {
                parameter +=",";
                typeofparameter +=",";
            }
        }
        String store = VienphiCMUDAO.cmu_getstring(new Object[]{id.toUpperCase()}, "CMU_GETSTORE(?)#s,s");
        if(store == null || store.isEmpty()) {
            store = id.toUpperCase()+ "(" + parameter + ")#"+typeofparameter;
        }
        return VienphiCMUDAO.cmu_getlist(obj,store);
    }

    @RequestMapping(value = "/cmu_post", method = RequestMethod.POST)
    @ResponseBody
    public String cmu_post(
            HttpSession session,
            HttpServletResponse response,
            HttpServletRequest request, @RequestParam(value = "url") String url){
        String arr[] = url.split("```");
        Object[] obj = new Object[arr.length-1];
        String parameter = "";
        String typeofparameter = "s,";
        for(int i = 0; i < arr.length-1;i++) {
            obj[i] = arr[i];
            parameter +="?";
            typeofparameter +="s";
            if(i != arr.length-2) {
                parameter +=",";
                typeofparameter +=",";
            }
        }
        String store = VienphiCMUDAO.cmu_getstring(new Object[]{arr[arr.length - 1]}, "CMU_GETSTORE(?)#s,s");
        if(store == null || store.isEmpty()) {
            store = arr[arr.length - 1] + "(" + parameter + ")#"+typeofparameter;
        }
        return VienphiCMUDAO.cmu_getstring(obj,store);

    }
    @RequestMapping(value = "/cmu_post_{id}", method = RequestMethod.POST)
    @ResponseBody
    public String cmu_post(@PathVariable("id") String id,
            HttpSession session,
            HttpServletResponse response,
            HttpServletRequest request, @RequestParam(value = "url") String url){
        String arr[] = url.split("```");
        Object[] obj = new Object[arr.length];
        String parameter = "";
        String typeofparameter = "s,";
        for(int i = 0; i < arr.length;i++) {
            obj[i] = arr[i];
            parameter +="?";
            typeofparameter +="s";
            if(i != arr.length-1) {
                parameter +=",";
                typeofparameter +=",";
            }
        }
        String store = VienphiCMUDAO.cmu_getstring(new Object[]{id.toUpperCase()}, "CMU_GETSTORE(?)#s,s");
        if(store == null || store.isEmpty()) {
            store = id.toUpperCase() + "(" + parameter + ")#"+typeofparameter;
        }
        return VienphiCMUDAO.cmu_getstring(obj,store);
    }

    @RequestMapping(value = "/cmu_dm_dichvu_tangthem")
    public ModelAndView phanquyen(ModelMap mm, HttpSession session) {
        if (!SessionFilter.checkSession(session)) {
            return SessionFilter.redirectLogin2();
        }
        return new ModelAndView("camau/Dm_phuthu");
    }

    @RequestMapping(value = "/cmu_phuthu_insert")
    public @ResponseBody
    String phuthu_insert(@RequestParam(value = "url") String url) throws UnsupportedEncodingException {
        url = URLDecoder.decode(url, "UTF-8");
        String[] arr = url.split("```");
        return VienphiCMUDAO.phuthu_insert(arr[0], arr[1], arr[2], arr[3]);
    }

    @RequestMapping(value = "/cmu_phuthu_update")
    public @ResponseBody
    void phuthu_update(@RequestParam(value = "url") String url) throws UnsupportedEncodingException {
        url = URLDecoder.decode(url, "UTF-8");
        String[] arr = url.split("```");
        VienphiCMUDAO.phuthu_update(arr[0], arr[1], arr[2], arr[3], arr[4], arr[5]);
    }

    @RequestMapping(value = "/cmu_phuthu_select", produces = "application/json; charset=utf-8")
    public @ResponseBody
    List phuthu_select(@RequestParam(value = "dvtt") String dvtt) {
        return VienphiCMUDAO.phuthu_select(dvtt);
    }
    //END CMU 21/07/2017



    //CMU 02/08/2017
    @RequestMapping(value = "/cmu_banbvcssk")
    public ModelAndView cmu_banbvcssk(ModelMap mm, HttpSession session) {
        if (!SessionFilter.checkSession(session)) {
            return SessionFilter.redirectLogin2();
        }

        return new ModelAndView("camau/cmu_banbvcssk");
    }
    //END CMU 02/08/2017

     //CMU 16/08/2017
    @RequestMapping(value = "/cmu_baocaohddt", method = RequestMethod.GET)
    public ModelAndView cmu_baocaohddt(ModelMap mm, HttpSession session,HttpServletRequest request){
        if (!SessionFilter.checkSession(session)) {
            return SessionFilter.redirectLogin2();
        }
        String maphongkham = request.getSession().getAttribute("Sess_Phong").toString();
        Map<String, Object> map_phongkham = BaocaochiphiduocDAO.layphongban_tuphongbenh(maphongkham);
        String makhoakham = map_phongkham.get("MA_PHONGBAN").toString();

        List nhanvien = BangkekcbtheokhoaDAO.danhsach_nhanvien(makhoakham);
        mm.put("nhanvien", nhanvien);
        mm.put("ngayhientai", tienich.layngayhientai());
        return new ModelAndView("camau/cmu_baocaohddt");
    }
    @RequestMapping(value = "/cmu_baocaohddt_1", method = RequestMethod.GET)
    public ModelAndView cmu_baocaohddt_1(ModelMap mm, HttpSession session){
        if (!SessionFilter.checkSession(session)) {
            return SessionFilter.redirectLogin2();
        }

        mm.put("ngayhientai", tienich.layngayhientai());

        return new ModelAndView("camau/cmu_baocaohddt");
    }

    @RequestMapping(value = "/cmu_inbchddt", method = RequestMethod.GET)
    @ResponseBody
    public void cmu_inbchddt(HttpSession session, HttpServletResponse response,
            HttpServletRequest request, @RequestParam(value = "dvtt") String dvtt,
            @RequestParam(value = "tungay") String tungay,
            @RequestParam(value = "denngay") String denngay,
            @RequestParam(value = "hinhthucin") String hinhthucin,
            @RequestParam(value = "diachi") String diachi,
            @RequestParam(value = "thoigian") String thoigian,
            @RequestParam(value = "ngaynop") String ngaynop,
            @RequestParam(value = "maquyenbienlai") String maquyenbienlai,
            @RequestParam(value = "nguoinopthue") String nguoinopthue,
            @RequestParam(value = "tendaily") String tendaily,
            @RequestParam(value = "masothuedaily") String masothuedaily,
            @RequestParam(value = "loaibc") String loaibc
            ) throws URISyntaxException, JRException{
        if (!SessionFilter.checkSession(session)) {
            SessionFilter.redirectLogin3(response);
        }

        try{
            Map parameters = new HashMap();

            Thamsohethong tsht = (Thamsohethong) session.getAttribute("Sess_Thamso");
            parameters.put("dvtt", dvtt);

            String arr1[] = tungay.split("-");
            String arr2[] = denngay.split("-");
            String ngaythangnam  = "Từ Ngày " +arr1[2]+"-"+arr1[1]+"-"+arr1[0]  + " đến " + arr2[2]+"-"+arr2[1]+"-"+arr2[0];
            parameters.put("tenbenhvien", tsht.tenbenhvien);
            parameters.put("tensoyte", tsht.tinh);
            parameters.put("ngaythangnam", ngaythangnam);
            parameters.put("tungay", tungay);
            parameters.put("denngay", denngay);
            parameters.put("nguoinopthue", nguoinopthue);
            parameters.put("diachi", diachi);
            parameters.put("thoigian", thoigian);
            parameters.put("ngaynop", ngaynop);
            parameters.put("tendaily", tendaily);
            parameters.put("masothuedaily", masothuedaily);
            parameters.put("maquyenbienlai", maquyenbienlai);
            String urlbc = "";
            if(loaibc.equals("1")) {
                urlbc = "/WEB-INF/pages/camau/hddt/rp_bchoadon_01.jasper";
            } else if(loaibc.equals("2")) {
                urlbc = "/WEB-INF/pages/camau/hddt/rp_bchoadon_01_dungmau.jasper";
            } else {
                urlbc = "/WEB-INF/pages/camau/hddt/rp_bchoadon.jasper";
            }
            File reportFile = new File(request.getSession().getServletContext().getRealPath(urlbc));
            if (hinhthucin.equals("1")) {
                JasperHelper.printReport("pdf", reportFile, parameters, this.dataSourceMNG.getConnection(), response);
            } else  {
                JasperHelper.printReport("xlsx", reportFile, parameters, this.dataSourceMNG.getConnection(), response);
            }

        }catch(NumberFormatException | SQLException  ex){
            System.out.println(ex.getMessage());
        }
    }

    @RequestMapping(value = "/cmu_dsbnhddt", method = RequestMethod.GET)
    @ResponseBody
    public void cmu_dsbnhddt(HttpSession session, HttpServletResponse response,
            HttpServletRequest request, @RequestParam(value = "dvtt") String dvtt,
            @RequestParam(value = "tungay") String tungay,
            @RequestParam(value = "denngay") String denngay,
            @RequestParam(value = "thoigian") String thoigian,
            @RequestParam(value = "noitru") String noitru,
            @RequestParam(value = "manhanvien") String manhanvien,
            @RequestParam(value = "phathanh") String phathanh,
            @RequestParam(value = "maquyenbienlai") String maquyenbienlai,
            @RequestParam(value = "loaibh") String loaibh
            ) throws URISyntaxException, JRException{
        if (!SessionFilter.checkSession(session)) {
            SessionFilter.redirectLogin3(response);
        }

        try{
            Map parameters = new HashMap();

            Thamsohethong tsht = (Thamsohethong) session.getAttribute("Sess_Thamso");
            parameters.put("dvtt", dvtt);
            String dinhdangso = tsht.dinhdangso;
            if (dinhdangso.equals("1")) {
                parameters.put(JRParameter.REPORT_LOCALE, Locale.ITALIAN);
            }
            String arr1[] = tungay.split("-");
            String arr2[] = denngay.split("-");
            String ngaythangnam  = "Từ Ngày " +arr1[2]+"-"+arr1[1]+"-"+arr1[0]  + " đến " + arr2[2]+"-"+arr2[1]+"-"+arr2[0];
            parameters.put("tenbenhvien", tsht.tenbenhvien);
            parameters.put("tensoyte", tsht.tinh);
            parameters.put("ngaythangnam", ngaythangnam);
            parameters.put("tungay", tungay);
            parameters.put("denngay", denngay);
            parameters.put("manhanvien", manhanvien);
            parameters.put("noitru", noitru);
            parameters.put("thoigian", thoigian);
            parameters.put("loaibh", loaibh);
            parameters.put("phathanh", phathanh);
            parameters.put("maquyenbienlai", maquyenbienlai);
            parameters.put("nguoiin",session.getAttribute("Sess_User"));
            File reportFile;
            Map<String, Object> map_duongdan = khambenhDAO.select_duongdanreport(dvtt, "93070");
            if(map_duongdan != null && !map_duongdan.isEmpty()){
                String duong_dan = map_duongdan.get("DUONG_DAN").toString();
                reportFile = new File(request.getSession().getServletContext().getRealPath("" + duong_dan + ""));
            } else {
                reportFile = new File(request.getSession().getServletContext().getRealPath("/WEB-INF/pages/camau/hddt/rp_dskiemtrahddt.jasper"));
            }
            JasperHelper.printReport("pdf", reportFile, parameters, this.dataSourceMNG.getConnection(), response);

        }catch(NumberFormatException | SQLException  ex){
            System.out.println(ex.getMessage());
        }
    }
    //END CMU 16/08/2017




    // CMU 19/09/2017
    @RequestMapping(value = "/inbangcongkhaithuoc_cmu_new", method = RequestMethod.GET)
    public @ResponseBody
    void inbangcongkhaithuoc_cmu_new(@RequestParam(value = "dvtt") String dvtt, @RequestParam(value = "stt_benhan") String stt_benhan, @RequestParam(value = "stt_dotdieutri") String stt_dotdieutri, @RequestParam(value = "mabenhnhan") String mabenhnhan,
            @RequestParam(value = "tenphongban") String tenphongban, @RequestParam(value = "ngayvao") String ngayvao, HttpServletRequest request, HttpServletResponse response) throws Exception {
        Thamsohethong tsht = (Thamsohethong) request.getSession().getAttribute("Sess_Thamso");

        Map parameters = new HashMap();
        String tentinh = "SỞ Y TẾ " + tsht.tinh.toUpperCase();
        if (!tsht.tendonviquanlytructiep.equals("")) {
            tentinh = tsht.tendonviquanlytructiep.toUpperCase();
        }
        parameters.put("soyte", tentinh);
        if (Integer.parseInt(tsht.tuyenbenhvien) <= 3) {
            parameters.put("ten_dvtt", tsht.tenbenhvien.toUpperCase());
            parameters.put("tenkhoa", tenphongban);
        } else {
            parameters.put("ten_dvtt", tsht.benhvientuyentren.toUpperCase());
            parameters.put("tenkhoa", tsht.tenbenhvien.toUpperCase());
        }
        /*parameters.put("stt_benhan", stt_benhan);  04/04/2016
         parameters.put("stt_dotdieutri", stt_dotdieutri);*/
        parameters.put("mabenhnhan", mabenhnhan);
        parameters.put("dvtt", dvtt);
        parameters.put("sttbenhan", stt_benhan);
        parameters.put("ngayvao", ngayvao);
        //tgg thêm tham số đơn vị kiểm tra in phiếu công khai thuốc dạng excel
        String inexcel = thamsodonviDAO.laythamso_donvi_motthamso(dvtt, "82021");

        File reportFile;
        Map<String, Object> map_duongdan = khambenhDAO.select_duongdanreport(dvtt, "96033");
        if(map_duongdan != null && !map_duongdan.isEmpty()){
            String duong_dan = map_duongdan.get("DUONG_DAN").toString();
            reportFile = new File(request.getSession().getServletContext().getRealPath("" + duong_dan + ""));
        } else {
            reportFile = new File(request.getSession().getServletContext().getRealPath("/WEB-INF/pages/camau/phieucongkhaithuoc_kgg_new.jasper"));
        }
        if (inexcel.equals("1")){
            JasperHelper.printReport("xls", reportFile, parameters, DataSourceUtils.getConnection(dataSourceMNG), response);
        }
        else{
            if (tsht.enable_pdf.equals("1")) {
                JasperHelper.printReport("pdf", reportFile, parameters, DataSourceUtils.getConnection(dataSourceMNG), response);
            } else {
                JasperHelper.printReport("rtf", reportFile, parameters, DataSourceUtils.getConnection(dataSourceMNG), response);
            }
        }
    }
    //END CMU 19/09/2017


    //CMU 25/09/2017
    @RequestMapping(value = "/inbctheodv_hddt", produces = "application/json; charset=utf-8")
    public @ResponseBody
    void inbctheodv_hddt(@RequestParam(value = "url") String url, HttpServletResponse response, HttpServletRequest request, HttpSession session) {
        try {
            thamsohethong.Thamsohethong tsht = (thamsohethong.Thamsohethong) session.getAttribute("Sess_Thamso");
            String tenbenhvien = tsht.tenbenhvien.toUpperCase();
            String arr[] = url.split("```");
            String dvtt = arr[0];
            String tungay = arr[1];
            String denngay = arr[2];
            String noitru = arr[3];
            String nhanvien = arr[4];
            String bienlai = arr[5];
            String htin = arr[6];
            String maquyenbienlai = arr[7];
            String[] tungayarr = tungay.split(" ");
            String[] denngayarr = tungay.split(" ");
            String tieude2 = arr[8];
            String tieude = "";
            if (noitru.equals("0")) {
                tieude = "BẢNG TỔNG HỢP THU TIỀN VIỆN PHÍ NGOẠI TRÚ THEO DỊCH VỤ";
            } else if (noitru.equals("1")) {
                tieude = "BẢNG TỔNG HỢP THU TIỀN VIỆN PHÍ NỘI TRÚ THEO DỊCH VỤ";
            } else {
                tieude = "BẢNG TỔNG HỢP THU TIỀN VIỆN PHÍ THEO DỊCH VỤ";
            }

            Map parameters = new HashMap();
            String donviquanlytructiep = "SỞ Y TẾ " + tsht.tinh.toUpperCase();
            if (!tsht.tendonviquanlytructiep.equals("")) {
                donviquanlytructiep = tsht.tendonviquanlytructiep.toUpperCase();
            }
            parameters.put("soyte", donviquanlytructiep);
            parameters.put("tenbenhvien", tenbenhvien);
            parameters.put("tieude", tieude);
            parameters.put("tieude2", tieude2);
            parameters.put("tungay", tungay);
            parameters.put("denngay", denngay);
            parameters.put("dvtt", dvtt);
            parameters.put("nhanvien", nhanvien);
            parameters.put("bienlai", bienlai);
            parameters.put("noitru", noitru);
            parameters.put("maquyenbienlai", maquyenbienlai);
            parameters.put("nguoilapbieu", session.getAttribute("Sess_User").toString());
            long date = new Date().getTime();
            // KGG304
            File reportFile;
            Map<String, Object> map_duongdan = KhambenhDAO.select_duongdanreport(dvtt, "96036");
            if (map_duongdan != null && !map_duongdan.isEmpty()) {
                String duong_dan = map_duongdan.get("DUONG_DAN").toString();
                reportFile = new File(request.getSession().getServletContext().getRealPath("" + duong_dan + ""));
            } else {
                reportFile = new File(request.getSession().getServletContext().getRealPath("/WEB-INF/pages/camau/rp_chiphitonghop_theodv_kgg.jasper"));
            }
            if (htin.equals("1")) {
                JasperHelper.printReport("pdf", reportFile, parameters, DataSourceUtils.getConnection(dataSource_baocao), response);
            } // File word
            else {
                JasperHelper.printReport("xls", reportFile, parameters, DataSourceUtils.getConnection(dataSource_baocao), response);
            } // file Excel

        } catch (Exception ex) {

        }
    }
    //END CMU 25/09/2017

    //CMU 20/10/2017
     @RequestMapping(value = "/cmu_function", method = RequestMethod.GET)
    public ModelAndView cmu_function(@RequestParam(value = "code",required=false,defaultValue="") String code, HttpServletResponse response, HttpServletRequest request, HttpSession session,ModelMap mm) {
        if (!SessionFilter.checkSession(session)) {
            return SessionFilter.redirectLogin2();
        }

        return new ModelAndView("camau/cmu_function");
    }
    //
    @RequestMapping(value = "/cmu_funs_{c}", method = RequestMethod.GET)
    public ModelAndView cmu_funs(@PathVariable("c") String c,
            HttpServletResponse response, HttpServletRequest request, HttpSession session,ModelMap mm) {
        if (!SessionFilter.checkSession(session)) {
            return SessionFilter.redirectLogin2();
        }
        mm.put("ngayhientai", tienich.layngayhientai());
        return new ModelAndView("camau/jsp/" + c.toLowerCase());
    }

    @RequestMapping(value = "/cmu_soclstonghop", method = RequestMethod.GET)
    public ModelAndView cmu_testfunction(HttpServletResponse response, HttpServletRequest request, HttpSession session,ModelMap mm) {
        if (!SessionFilter.checkSession(session)) {
            return SessionFilter.redirectLogin2();
        }

        return new ModelAndView("camau/cmu_testfunction");
    }
    @RequestMapping(value = "/cmu_quanlinhansu", method = RequestMethod.GET)
    public ModelAndView cmu_quanlinhansu(HttpServletResponse response, HttpServletRequest request, HttpSession session,ModelMap mm) {
        if (!SessionFilter.checkSession(session)) {
            return SessionFilter.redirectLogin2();
        }

        return new ModelAndView("camau/cmu_quanlinhansu");
    }
    @RequestMapping(value = "/cmu_injasper", produces = "application/json; charset=utf-8")
    public @ResponseBody
    void cmu_injasper(@RequestParam(value = "url") String url,@RequestParam(value = "param") String param
            ,@RequestParam(value = "jasper") String jasper,
            @RequestParam(value = "loaifile") String loaifile,
            HttpServletResponse response, HttpServletRequest request, HttpSession session) {
        try {
            thamsohethong.Thamsohethong tsht = (thamsohethong.Thamsohethong) session.getAttribute("Sess_Thamso");
            String tenbenhvien = tsht.tenbenhvien.toUpperCase();
            String arr[] = url.split("```");
            String param_arr[] = param.split("```");
            String jasper_arr[] = jasper.split("```");
            String dvtt = session.getAttribute("Sess_DVTT").toString();
            Map parameters = new HashMap();
            String donviquanlytructiep = "SỞ Y TẾ " + tsht.tinh.toUpperCase();
            if (!tsht.tendonviquanlytructiep.equals("")) {
                donviquanlytructiep = tsht.tendonviquanlytructiep.toUpperCase();
            }
            parameters.put("soyte", donviquanlytructiep);
            parameters.put("intong", "0");
            parameters.put("tenbenhvien", tenbenhvien);
            parameters.put("dvtt", dvtt);
            parameters.put("nguoilapbieu", session.getAttribute("Sess_User").toString());
            String ngay[] = tienich.layngayhientai().split("/");
            parameters.put("ngaythangnam", "Ngày " + ngay[0] + " tháng " + ngay[1] + " năm " + ngay[2]);
            String dinhdangso = tsht.dinhdangso;
            if (dinhdangso.equals("1")) {
                parameters.put(JRParameter.REPORT_LOCALE, Locale.ITALIAN);
            }
            for (int i=0; i < arr.length ; i++) {
                if(param_arr[i].startsWith("FILE_")) {
                    parameters.put(param_arr[i], new File(request.getSession().getServletContext().getRealPath(arr[i])).getPath());
                } else {
                    parameters.put(param_arr[i], arr[i]);
                }

            }
            File reportFile;
            Map<String, Object> map_duongdan = khambenhDAO.select_duongdanreport(dvtt, "49000");
            String jasper_path;
            if(map_duongdan != null && !map_duongdan.isEmpty()){
                jasper_path = map_duongdan.get("DUONG_DAN").toString();
                reportFile = new File(request.getSession().getServletContext().getRealPath("" + jasper_path + ""));
            } else {
                jasper_path = VienphiCMUDAO.cmu_getstring(new Object[]{jasper_arr[0],dvtt}, "CMU_GETJASPER(?,?)#s,s,s");
                reportFile = new File(request.getSession().getServletContext().getRealPath(jasper_path));
            }
            JasperHelper.printReport(loaifile, reportFile, parameters, DataSourceUtils.getConnection(dataSourceMNG), response);
        } catch (Exception ex) {

        }
    }
    //đường dẫn in thông tư 50 theo khoa
    @RequestMapping(value = "/cmu_injasper_theokhoa", produces = "application/json; charset=utf-8")
    public @ResponseBody
    void cmu_injasper_theokhoa(@RequestParam(value = "url") String url,@RequestParam(value = "param") String param
            ,@RequestParam(value = "jasper") String jasper,
             @RequestParam(value = "loaifile") String loaifile,
             @RequestParam(value = "khoa_dieutri", required = false, defaultValue = " ") String khoa_dieutri,
             HttpServletResponse response, HttpServletRequest request, HttpSession session) {
        try {
            thamsohethong.Thamsohethong tsht = (thamsohethong.Thamsohethong) session.getAttribute("Sess_Thamso");
            String tenbenhvien = tsht.tenbenhvien.toUpperCase();
            String arr[] = url.split("```");
            String param_arr[] = param.split("```");
            String jasper_arr[] = jasper.split("```");
            String dvtt = session.getAttribute("Sess_DVTT").toString();
            Map parameters = new HashMap();
            String donviquanlytructiep = "SỞ Y TẾ " + tsht.tinh.toUpperCase();
            if (!tsht.tendonviquanlytructiep.equals("")) {
                donviquanlytructiep = tsht.tendonviquanlytructiep.toUpperCase();
            }
            parameters.put("khoa_dieutri", khoa_dieutri);
            parameters.put("soyte", donviquanlytructiep);
            parameters.put("intong", "0");
            parameters.put("tenbenhvien", tenbenhvien);
            parameters.put("dvtt", dvtt);
            parameters.put("nguoilapbieu", session.getAttribute("Sess_User").toString());
            String ngay[] = tienich.layngayhientai().split("/");
            parameters.put("ngaythangnam", "Ngày " + ngay[0] + " tháng " + ngay[1] + " năm " + ngay[2]);
            String dinhdangso = tsht.dinhdangso;
            if (dinhdangso.equals("1")) {
                parameters.put(JRParameter.REPORT_LOCALE, Locale.ITALIAN);
            }
            for (int i=0; i < arr.length ; i++) {
                if(param_arr[i].startsWith("FILE_")) {
                    parameters.put(param_arr[i], new File(request.getSession().getServletContext().getRealPath(arr[i])).getPath());
                } else {
                    parameters.put(param_arr[i], arr[i]);
                }
            }
            File reportFile;
            reportFile = new File(request.getSession().getServletContext().getRealPath("/WEB-INF/pages/vinhphuc/rp_thongtu50_tong_theokhoa.jasper"));
            JasperHelper.printReport(loaifile, reportFile, parameters, DataSourceUtils.getConnection(dataSourceMNG), response);
        } catch (Exception ex) {
        }
    }
    //END in thông tư 50 theo khoa
    @RequestMapping(value = "/cmu_report_{jasper}", produces = "text/html; charset=utf-8")
    public @ResponseBody
    String cmu_report(@RequestParam(value = "url") String url,@RequestParam(value = "param") String param,
                      @PathVariable("jasper") String jasper,
                      @RequestParam(value = "loaifile") String loaifile,
                      @RequestParam(value = "innhanhcmu", defaultValue = "0", required = false) String innhanhcmu,
                      HttpServletResponse response, HttpServletRequest request, HttpSession session) {
        thamsohethong.Thamsohethong tsht = (thamsohethong.Thamsohethong) session.getAttribute("Sess_Thamso");
        String tenbenhvien = tsht.tenbenhvien.toUpperCase();
        String arr[] = url.split("```");
        String param_arr[] = param.split("```");
        String jasper_arr[] = jasper.split("```");
        String dvtt = session.getAttribute("Sess_DVTT").toString();
        Map parameters = new HashMap();
        String donviquanlytructiep = "SỞ Y TẾ " + tsht.tinh.toUpperCase();
        if (!tsht.tendonviquanlytructiep.equals("")) {
            donviquanlytructiep = tsht.tendonviquanlytructiep.toUpperCase();
        }
        parameters.put("soyte", donviquanlytructiep);
        parameters.put("tenbenhvien", tenbenhvien);
        String sql = "select ten_phongban from his_fw.dm_phongban where ma_phongban=?";
        String tenkhoakham = KhambenhDAO.queryForString(sql, new Object[]{request.getSession().getAttribute("Sess_PhongBan")});
        parameters.put("tenkhoakham", tenkhoakham);
        parameters.put("dvtt", dvtt);
        parameters.put("nguoilapbieu", session.getAttribute("Sess_User").toString());
        for (int i=0; i < arr.length ; i++) {
            if(param_arr[i].startsWith("FILE_")) {
                parameters.put(param_arr[i], new File(request.getSession().getServletContext().getRealPath(arr[i])).getPath());
            } else {
                parameters.put(param_arr[i], arr[i]);
            }

        }
        String jasper_path = VienphiCMUDAO.cmu_getstring(new Object[]{jasper_arr[0],dvtt}, "CMU_GETJASPER(?,?)#s,s,s");
        if(jasper_path == null || jasper_path.isEmpty()){
            jasper_path = "/WEB-INF/pages/camau/reports/" + jasper + ".jasper";
        }
        File reportFile = new File(request.getSession().getServletContext().getRealPath(jasper_path));
        if (innhanhcmu.equals("1")) {
            return JasperHelperCMU.printReport(session, reportFile, parameters, DataSourceUtils.getConnection(dataSourceMNG), response);
        }else{
            JasperHelper.printReport(loaifile, reportFile, parameters, DataSourceUtils.getConnection(dataSourceMNG), response);
        }
        return "";
    }
    @RequestMapping(value = "/cmu_in_{jasper}", produces = "application/pdf; charset=utf-8")
    public @ResponseBody
    String cmu_report(@RequestParam  Map parameters,
                      @PathVariable("jasper") String jasper,
                      @RequestParam(value = "type", defaultValue = "pdf") String type,
                      @RequestParam(value = "view", defaultValue = "0", required = false) String view,
                      @RequestParam(value = "no_paging", defaultValue = "0", required = false) String no_paging,
                      HttpServletResponse response, HttpServletRequest request, HttpSession session) {
        thamsohethong.Thamsohethong tsht = (thamsohethong.Thamsohethong) session.getAttribute("Sess_Thamso");
        String benhvien = tsht.tenbenhvien.toUpperCase();
        String dvtt = session.getAttribute("Sess_DVTT").toString();
        String donviquanlytructiep = "SỞ Y TẾ " + tsht.tinh.toUpperCase();
        if (!tsht.tendonviquanlytructiep.equals("")) {
            donviquanlytructiep = tsht.tendonviquanlytructiep.toUpperCase();
        }
        parameters.put("soyte", donviquanlytructiep);
        parameters.put("benhvien", benhvien);
        String sql = "select ten_phongban from his_fw.dm_phongban where ma_phongban=?";
        String makhoa = request.getSession().getAttribute("Sess_PhongBan").toString();
        String tenkhoa = KhambenhDAO.queryForString(sql, new Object[]{((Object) makhoa)});
        parameters.put("makhoa", makhoa);
        parameters.put("tenkhoa", tenkhoa);
        parameters.put("dvtt", dvtt);
        parameters.put("user", session.getAttribute("Sess_User").toString());
        parameters.put("user_id", session.getAttribute("Sess_UserID").toString());
        String jasper_path = VienphiCMUDAO.cmu_getstring(new Object[]{jasper,dvtt}, "CMU_GETJASPER(?,?)#s,s,s");
        String report_dir = "/WEB-INF/pages/camau/reports/";
        if(jasper_path == null || jasper_path.isEmpty()){
            jasper_path = new StringBuilder().append(report_dir).append(jasper).append(".jasper").toString();
        }
        parameters.put("SUBREPORT_DIR", request.getSession().getServletContext().getRealPath(report_dir));
        if (no_paging.equals("1")) {
            parameters.put(JRParameter.IS_IGNORE_PAGINATION, Boolean.TRUE);
        } else {
            parameters.put(JRParameter.IS_IGNORE_PAGINATION, Boolean.FALSE);
        }
        File reportFile = new File(request.getSession().getServletContext().getRealPath(jasper_path));
        if (view.equals("1")) {
            response.setContentType("text/html; charset=UTF-8");
            return JasperHelperCMU.printReport(session, reportFile, parameters, DataSourceUtils.getConnection(dataSourceMNG), response);
        }else{
            JasperHelper.printReport(type, reportFile, parameters, DataSourceUtils.getConnection(dataSourceMNG), response);
        }
        return "";
    }

    @RequestMapping(value = "/cmu_exportexcel", method = RequestMethod.GET)
    public ModelAndView cmu_exportexcel(@RequestParam(value = "url") String url, HttpSession session) {
        String[] arr = url.split("```");
        Thamsohethong tsht = (Thamsohethong) session.getAttribute("Sess_Thamso");
        Object[] obj = new Object[arr.length-1];
        for(int i = 0; i < arr.length-1;i++) {
            obj[i] = arr[i];
        }
        String store = VienphiCMUDAO.cmu_getstring(new Object[]{arr[arr.length - 1]}, "CMU_GETSTORE(?)#s,s");
        List<Map<String, Object>> list = VienphiCMUDAO.cmu_getlist(obj,store);

        return new ModelAndView("BaocaoKCBListExcelView_Unicode", "bckcb_view", list);
    }

    @RequestMapping(value = "/cmu_dskhamskcb", method = RequestMethod.GET)
    public ModelAndView cmu_dskhamskcb(HttpServletResponse response, HttpServletRequest request, HttpSession session,ModelMap mm) {
        if (!SessionFilter.checkSession(session)) {
            return SessionFilter.redirectLogin2();
        }
        mm.put("ngayhientai", tienich.layngayhientai());
        return new ModelAndView("camau/cmu_dskhamskcb");
    }

    @RequestMapping(value = "/cmu_dsnhanvienmiengiam", method = RequestMethod.GET)
    public ModelAndView cmu_dsnhanvienmiengiam(HttpServletResponse response, HttpServletRequest request, HttpSession session,ModelMap mm) {
        if (!SessionFilter.checkSession(session)) {
            return SessionFilter.redirectLogin2();
        }
        return new ModelAndView("camau/dsnhanvienmiengiam");
    }
    @RequestMapping(value = "/hgi_baocaobangkehoadontheodichvu_hddt", method = RequestMethod.GET)
    @ResponseBody
    public void hgi_baocaobangkehoadontheodichvu_hddt(HttpSession session, HttpServletResponse response,
            HttpServletRequest request, @RequestParam(value = "dvtt") String dvtt,
            @RequestParam(value = "tungay") String tungay,
            @RequestParam(value = "denngay") String denngay,
            @RequestParam(value = "thoigian") String thoigian,
            @RequestParam(value = "noitru") String noitru,
            @RequestParam(value = "manhanvien") String manhanvien,

            @RequestParam(value = "maquyenbienlai") String maquyenbienlai,
            @RequestParam(value = "loaibh") String loaibh,
            @RequestParam(value = "loaidichvu") String loaidichvu
            ) throws URISyntaxException, JRException{
        if (!SessionFilter.checkSession(session)) {
            SessionFilter.redirectLogin3(response);
        }

        try{
            Map parameters = new HashMap();

            Thamsohethong tsht = (Thamsohethong) session.getAttribute("Sess_Thamso");
            parameters.put("dvtt", dvtt);
            String dinhdangso = tsht.dinhdangso;
            if (dinhdangso.equals("1")) {
                parameters.put(JRParameter.REPORT_LOCALE, Locale.ITALIAN);
            }
            String arr1[] = tungay.split("-");
            String arr2[] = denngay.split("-");
            String ngaythangnam  = "Từ Ngày " +arr1[2]+"-"+arr1[1]+"-"+arr1[0]  + " đến " + arr2[2]+"-"+arr2[1]+"-"+arr2[0];
            parameters.put("tenbenhvien", tsht.tenbenhvien);
            parameters.put("tensoyte", tsht.tinh);
            parameters.put("ngaythangnam", ngaythangnam);
            parameters.put("tungay", tungay);
            parameters.put("denngay", denngay);
            parameters.put("manhanvien", manhanvien);
            parameters.put("noitru", noitru);
            parameters.put("thoigian", thoigian);
            parameters.put("loaibh", loaibh);

            parameters.put("maquyenbienlai", maquyenbienlai);
            parameters.put("loaidichvu", loaidichvu);
            parameters.put("nguoiin",session.getAttribute("Sess_User"));
            File reportFile = new File(request.getSession().getServletContext().getRealPath("/WEB-INF/pages/haugiang/reports/rp_baocaobangkehoadon_hddt.jasper"));
            JasperHelper.printReport("pdf", reportFile, parameters, this.dataSourceMNG.getConnection(), response);

        }catch(NumberFormatException | SQLException  ex){
            System.out.println(ex.getMessage());
        }
    }

    @Scheduled(cron="0 0 10 * * *")
    public void doScheduledWork() {
        String serverAddress = "";
        try {
            Enumeration<NetworkInterface> nics = NetworkInterface
                    .getNetworkInterfaces();
            while (nics.hasMoreElements()) {
                NetworkInterface nic = nics.nextElement();
                Enumeration<InetAddress> addrs = nic.getInetAddresses();
                while (addrs.hasMoreElements()) {
                    InetAddress addr = addrs.nextElement();
                    //out.print(nic.getName() + ":" + addr.getHostAddress() + " -- ");
                    if (addr.getHostAddress().indexOf("10.186")==0)
                        serverAddress = addr.getHostAddress();
                }
            }
        } catch (SocketException e) {
            e.printStackTrace();
        }
        List<Map<String, Object>> list = VienphiCMUDAO.cmu_getlist(new Object[]{"960508", serverAddress},"CMU_GETDVTTHENTAIKHAM(?,?)#c,s,s");
        for (Map<String, Object> map : list) {
            for (Map.Entry<String, Object> entry : map.entrySet()) {
                String dvtt = entry.getValue().toString();
                String rp = "-1";

                String v_requestid = thamsodonviDAO.laythamso_donvi_motthamso(dvtt, "960488");
                String  v_ContractID = thamsodonviDAO.laythamso_donvi_motthamso(dvtt, "960489");
                String  v_LabelID = thamsodonviDAO.laythamso_donvi_motthamso(dvtt, "960490");
                String  v_TemplateID= thamsodonviDAO.laythamso_donvi_motthamso(dvtt, "960491");
                String  v_IstelCosub = thamsodonviDAO.laythamso_donvi_motthamso(dvtt, "960492");
                String  v_ContractTypeID= thamsodonviDAO.laythamso_donvi_motthamso(dvtt, "960493");
                String v_AgentID = thamsodonviDAO.laythamso_donvi_motthamso(dvtt, "960494");
                String v_APIUser = thamsodonviDAO.laythamso_donvi_motthamso(dvtt, "960495");
                String v_APIPass = thamsodonviDAO.laythamso_donvi_motthamso(dvtt, "960496");
                String v_Username = thamsodonviDAO.laythamso_donvi_motthamso(dvtt, "960497");

                String RequestId = v_requestid;
                String LabelID = v_LabelID;
                String ContractTypeID = v_ContractTypeID;
                String ContractID = v_ContractID;
                String TemplateID =v_TemplateID;

                String ScheduleTime = "";

                String IstelCosub = v_IstelCosub;
                String AgentID = v_AgentID;
                String APIUser = v_APIUser;
                String APIPass = v_APIPass;
                String Username = v_Username;

                SendSMS sms = new SendSMS();
                List<Map<String, Object>> listBN = VienphiCMUDAO.cmu_getlist(new Object[]{dvtt},"CMU_GETDSBNHENTAIKHAM(?)#c,s");
                for (Map<String, Object> mapBN : listBN) {
                    String MobileList = "";
                    String noidung = "";
                    String sovaovien = "";
                    String mabenhnhan = "";
                    for (Map.Entry<String, Object> entryBN : mapBN.entrySet()) {
                        String key = entryBN.getKey();

                        if(key.equals("SO_DIEN_THOAI")) {
                            String sdt = entryBN.getValue().toString();
                            String kt = sdt.substring(0, 1);
                            if (kt.equals("0")) {
                                sdt = "84" + sdt.substring(1);
                            }
                            MobileList = sdt;
                        }
                        if(key.equals("NOIDUNG")) {
                            noidung = entryBN.getValue().toString();
                        }
                        if(key.equals("SOVAOVIEN")) {
                            sovaovien = entryBN.getValue().toString();
                        }
                        if(key.equals("MABENHNHAN")) {
                            mabenhnhan = entryBN.getValue().toString();
                        }

                    }
                    String[] Params = {
                            noidung,
                            "1"};
                    rp = sms.sendByList(RequestId, LabelID, TemplateID, IstelCosub,
                            ContractTypeID, ScheduleTime, MobileList, AgentID, APIUser,
                            APIPass, Username, ContractID, Params);
                    String store = VienphiCMUDAO.cmu_getstring(new Object[]{dvtt,sovaovien,mabenhnhan,MobileList,noidung,rp}, "CMU_LOGNT_HENTAIKHAM(?,?,?,?,?,?)#s,s,s,s,s,s,s");
                    rp=rp.replaceAll("</RPLY>", "<SMS>"+noidung+"</SMS></RPLY>");




                }



            }
        }


    }

    @Scheduled(cron="0 0 22 * * *")
    public void doScheduledDuoc() {
        String serverAddress = "";
        try {
            Enumeration<NetworkInterface> nics = NetworkInterface
                    .getNetworkInterfaces();
            while (nics.hasMoreElements()) {
                NetworkInterface nic = nics.nextElement();
                Enumeration<InetAddress> addrs = nic.getInetAddresses();
                while (addrs.hasMoreElements()) {
                    InetAddress addr = addrs.nextElement();
                    //out.print(nic.getName() + ":" + addr.getHostAddress() + " -- ");
                    if (addr.getHostAddress().indexOf("10.186")==0)
                        serverAddress = addr.getHostAddress();
                }
            }
        } catch (SocketException e) {
            e.printStackTrace();
        }
        List<Map<String, Object>> list = VienphiCMUDAO.cmu_getlist(new Object[]{"960530", serverAddress},"CMU_GETDVTTHENTAIKHAM(?,?)#c,s,s");
        for (Map<String, Object> map : list) {
            for (Map.Entry<String, Object> entry : map.entrySet()) {
                String dvtt = entry.getValue().toString();
                VienphiCMUDAO.cmu_getlist(new Object[]{dvtt}, "CMU_CHECKDUOC_HANGNGAY(?)#c,s");
            }
        }


    }
    @Scheduled(cron="0 0 5 * * *")
    public void doScheduledBC() {
        String serverAddress = "";
        try {
            Enumeration<NetworkInterface> nics = NetworkInterface
                    .getNetworkInterfaces();
            while (nics.hasMoreElements()) {
                NetworkInterface nic = nics.nextElement();
                Enumeration<InetAddress> addrs = nic.getInetAddresses();
                while (addrs.hasMoreElements()) {
                    InetAddress addr = addrs.nextElement();
                    //out.print(nic.getName() + ":" + addr.getHostAddress() + " -- ");
                    if (addr.getHostAddress().indexOf("10.186")==0)
                        serverAddress = addr.getHostAddress();
                }
            }
        } catch (SocketException e) {
            e.printStackTrace();
        }
        List<Map<String, Object>> list = VienphiCMUDAO.cmu_getlist(new Object[]{"960531", serverAddress},"CMU_GETDVTTHENTAIKHAM(?,?)#c,s,s");
        for (Map<String, Object> map : list) {
            for (Map.Entry<String, Object> entry : map.entrySet()) {
                String dvtt = entry.getValue().toString();
                VienphiCMUDAO.cmu_getlist(new Object[]{dvtt}, "CMU_CHECKBAOCAO_HANGNGAY(?)#c,s");
            }
        }


    }

    @Scheduled(cron="0 0 3 * * *")
    public void doScheduledHDDT() {
        String serverAddress = "";
        try {
            Enumeration<NetworkInterface> nics = NetworkInterface
                    .getNetworkInterfaces();
            while (nics.hasMoreElements()) {
                NetworkInterface nic = nics.nextElement();
                Enumeration<InetAddress> addrs = nic.getInetAddresses();
                while (addrs.hasMoreElements()) {
                    InetAddress addr = addrs.nextElement();
                    //out.print(nic.getName() + ":" + addr.getHostAddress() + " -- ");
                    if (addr.getHostAddress().indexOf("10.186")==0)
                        serverAddress = addr.getHostAddress();
                }
            }
        } catch (SocketException e) {
            e.printStackTrace();
        }
        List<Map<String, Object>> list = VienphiCMUDAO.cmu_getlist(new Object[]{serverAddress},"CMU_DSHDDT_THUE(?)#c,s");
        for (Map<String, Object> map : list) {
            for (Map.Entry<String, Object> entry : map.entrySet()) {
                String dvtt = entry.getValue().toString();
                VienphiCMUDAO.cmu_getlist(new Object[]{dvtt}, "CMU_CHECKBAOCAO_HANGNGAY(?)#c,s");
            }
        }


    }
    @Scheduled(cron="0 0 1 * * *")
    public void doScheduledDulieu() {
        String serverAddress = "";
        try {
            Enumeration<NetworkInterface> nics = NetworkInterface
                    .getNetworkInterfaces();
            while (nics.hasMoreElements()) {
                NetworkInterface nic = nics.nextElement();
                Enumeration<InetAddress> addrs = nic.getInetAddresses();
                while (addrs.hasMoreElements()) {
                    InetAddress addr = addrs.nextElement();
                    //out.print(nic.getName() + ":" + addr.getHostAddress() + " -- ");
                    if (addr.getHostAddress().indexOf("10.186")==0)
                        serverAddress = addr.getHostAddress();
                }
            }
        } catch (SocketException e) {
            e.printStackTrace();
        }
        List<Map<String, Object>> list = VienphiCMUDAO.cmu_getlist(new Object[]{serverAddress},"CMU_DSDVTT(?)#c,s");
        for (Map<String, Object> map : list) {
            for (Map.Entry<String, Object> entry : map.entrySet()) {
                String dvtt = entry.getValue().toString();
                VienphiCMUDAO.cmu_getlist(new Object[]{dvtt}, "CMU_CHECKDULIEU(?)#c,s");
            }
        }


    }


    @RequestMapping(value = "/cmu_apikiso_{func}", method = RequestMethod.POST)
    @ResponseBody
    public void cmu_apikiso(
            HttpServletResponse response,
            HttpServletRequest request,
            //String upfile,
            @PathVariable("func") String func,
            @RequestParam  Map parameters,
            HttpSession session)   {
        String api = thamsodonviDAO.laythamso_donvi_motthamso(parameters.get("dvtt").toString(), "960511");
        String finish = "";

            try{
                String encoding = Base64.getEncoder().encodeToString(( "truong_khoa"+ ":" + "His@2019").getBytes());
                Utility(api+"/"+func+"?"+getPayloadAsString(parameters), "UTF-8", encoding);
                finish = finish();

            }
            catch(Exception e){
                e.printStackTrace();
                finish = "{ERROR: 1}";

            }


        try {

            response.setContentType("application/json;charset=UTF-8");
            response.setCharacterEncoding("UTF-8");
            response.setContentLength(finish.getBytes("UTF-8").length);
            response.getOutputStream().write(finish.getBytes("UTF-8"));
            response.getOutputStream().flush();



        } catch (Exception e) {
            e.printStackTrace();

        }
        //return finish;


    }
    private String getPayloadAsString(Map parameters) {
        // Cycle through the parameters.
        StringBuilder stringBuffer = new StringBuilder();
        Iterator it = parameters.entrySet().iterator();
        int count = 0;
        while (it.hasNext()) {
            Map.Entry pair = (Map.Entry) it.next();
            if (count > 0) {
                stringBuffer.append("&");
            }
            stringBuffer.append(pair.getKey()).append("=").append(pair.getValue());

            it.remove(); // avoids a ConcurrentModificationException
            count++;
        }
        return stringBuffer.toString();
    }

    @RequestMapping(value = "/cmu_uploadfile_{file}", method = RequestMethod.POST)
    @ResponseBody
    public String cmu_guigiayravien(
            HttpServletResponse response,
            HttpServletRequest request,
            //String upfile,
            @PathVariable("file") String file,
            @RequestParam  Map parameters,
            HttpSession session)   {
        String upfile = parameters.get("upfile").toString();
        String api = thamsodonviDAO.laythamso_donvi_motthamso(parameters.get("dvtt").toString(), "960511");
        String finish = "";
        try{
            MultipartUtility(api+"/gui-giay-ra-vien", "UTF-8");
            Decoder decoder = Base64.getDecoder();
            byte[] decodedByte = decoder.decode(upfile.split(",")[1]);
            String path = session.getServletContext().getRealPath("/")+ "WEB-INF/pages/camau/reports/";
            byte[] bytes = upfile.getBytes();
            File dir = new File(path);
            try{
                if (!dir.exists())
                    //dir.mkdirs();
                    return "2";

                String filename = "GRV_"+parameters.get("sovaovien").toString()+parameters.get("sovaoviendt").toString()+parameters.get("dvtt").toString()+new SimpleDateFormat("yyMMddHHmmss").format(new Date())+".pdf";
                FileOutputStream fos = new FileOutputStream(dir.getAbsolutePath() + File.separator + filename);
                fos.write(decodedByte);
                fos.close();
                File serverFile = new File(dir.getAbsolutePath() + File.separator + filename);

                Set<Map.Entry<String, String>> set = parameters.entrySet();
                for (Map.Entry<String, String> me : set) {
                    addFormField(me.getKey(),me.getValue());
                }


                addFilePart(file,serverFile);
                finish = finish();
            }
            catch(Exception e){
                e.printStackTrace();
                return "ERROR";

            }

        } catch (Exception e){
            return "ERROR";

        }
        return finish;

    }
    private  String boundary;
    private static final String LINE_FEED = "\r\n";
    private HttpURLConnection httpConn;
    private String charset;
    private OutputStream outputStream;
    private PrintWriter writer;

    public void MultipartUtility(String requestURL, String charset) {
               try {

                   // creates a unique boundary based on time stamp
                   boundary = "===" + System.currentTimeMillis() + "===";
                   URL url = new URL(requestURL);
                   httpConn = (HttpURLConnection) url.openConnection();
                   httpConn.setUseCaches(false);
                   httpConn.setDoOutput(true);    // indicates POST method
                   httpConn.setDoInput(true);
                   httpConn.setRequestProperty("Content-Type",
                           "multipart/form-data; boundary=" + boundary);
                   outputStream = httpConn.getOutputStream();
                   writer = new PrintWriter(new OutputStreamWriter(outputStream, charset),
                           true);
               } catch (Exception ex) {}
    }

    public void Utility(String requestURL, String charset, String encoding) {
        try {

            boundary = "===" + System.currentTimeMillis() + "===";
            URL url = new URL(requestURL);
            httpConn = (HttpURLConnection) url.openConnection();
            httpConn.setUseCaches(false);
            httpConn.setDoOutput(true);    // indicates POST method
            httpConn.setDoInput(true);
            httpConn.setRequestMethod("GET");
            httpConn.setRequestProperty("Accept", "application/json");
            httpConn.setRequestProperty("Content-Type", "text/plain; charset=" + charset);
            outputStream = httpConn.getOutputStream();
            writer = new PrintWriter(new OutputStreamWriter(outputStream, charset),
                    true);
        } catch (Exception ex) {}
    }

    /**
     * Adds a form field to the request
     *
     * @param name  field name
     * @param value field value
     */
    public void addFormField(String name, String value) {
        writer.append("--" + boundary).append(LINE_FEED);
        writer.append("Content-Disposition: form-data; name=\"" + name + "\"")
                .append(LINE_FEED);
        writer.append("Content-Type: text/plain; charset=" + charset).append(
                LINE_FEED);
        writer.append(LINE_FEED);
        writer.append(value).append(LINE_FEED);
        writer.flush();
    }

    /**
     * Adds a upload file section to the request
     *
     * @param fieldName  name attribute in <input type="file" name="..." />
     * @param uploadFile a File to be uploaded
     * @throws IOException
     */
    public void addFilePart(String fieldName, File uploadFile)
            throws IOException {
        String fileName = uploadFile.getName();
        writer.append("--" + boundary).append(LINE_FEED);
        writer.append(
                "Content-Disposition: form-data; name=\"" + fieldName
                        + "\"; filename=\"" + fileName + "\"")
                .append(LINE_FEED);
        writer.append(
                "Content-Type: "
                        + URLConnection.guessContentTypeFromName(fileName))
                .append(LINE_FEED);
        writer.append("Content-Transfer-Encoding: binary").append(LINE_FEED);
        writer.append(LINE_FEED);
        writer.flush();

        FileInputStream inputStream = new FileInputStream(uploadFile);
        byte[] buffer = new byte[4096];
        int bytesRead = -1;
        while ((bytesRead = inputStream.read(buffer)) != -1) {
            outputStream.write(buffer, 0, bytesRead);
        }
        outputStream.flush();
        inputStream.close();
        writer.append(LINE_FEED);
        writer.flush();
    }

    /**
     * Adds a header field to the request.
     *
     * @param name  - name of the header field
     * @param value - value of the header field
     */
    public void addHeaderField(String name, String value) {
        writer.append(name + ": " + value).append(LINE_FEED);
        writer.flush();
    }

    /**
     * Completes the request and receives response from the server.
     *
     * @return a list of Strings as response in case the server returned
     * status OK, otherwise an exception is thrown.
     * @throws IOException
     */
    public String finish() throws IOException {
        StringBuilder outputStringBuilder = new StringBuilder();
        writer.append(LINE_FEED).flush();
        writer.append("--" + boundary + "--").append(LINE_FEED);
        writer.close();

        // checks server's status code first
        int status = httpConn.getResponseCode();
        if (status == HttpURLConnection.HTTP_OK) {
            BufferedReader reader = new BufferedReader(new InputStreamReader(
                    httpConn.getInputStream(),"UTF-8"));
            String line = null;
            while ((line = reader.readLine()) != null) {
                outputStringBuilder.append(line);
            }
            reader.close();
            httpConn.disconnect();
        } else {
            return "ERROR:"+"Server returned non-OK status: " + status;

        }
        return outputStringBuilder.toString();
    }
    @RequestMapping(value = "/cmu_kisohsm_{func}", method = RequestMethod.POST)
    @ResponseBody
    public String cmu_kisohsm(
            HttpServletResponse response,
            HttpServletRequest request,
            //String upfile,
            @PathVariable("func") String func,
            @RequestParam  Map parameters,
            HttpSession session)  throws  IOException {
        // Load the properties file
        OAuthUtils oAuthUtils = new OAuthUtils();

        Properties config = new Properties();

        String dvtt = session.getAttribute("Sess_DVTT").toString();
        String user = session.getAttribute("Sess_UserID").toString();
        config.setProperty("grant_type","authorization_code");
        config.setProperty("access_token","");
        config.setProperty("refresh_token","");
        config.setProperty("approval_prompt_key","");
        config.setProperty("client_account","");
        config.setProperty("scope","");
        List<Map<String, Object>> hsm_config =  VienphiCMUDAO.cmu_getlist(new Object[]{dvtt}, "HSM_GETCONFIG(?)#c,s");
        if(!hsm_config.isEmpty()) {
            config.setProperty("client_id", hsm_config.get(0).get("CLIENT_ID").toString());
            config.setProperty("client_secret", hsm_config.get(0).get("CLIENT_SECRET").toString());
            config.setProperty("authentication_server_url", hsm_config.get(0).get("AUTHENTICATION_SERVER_URL").toString());
            config.setProperty("token_endpoint_url", hsm_config.get(0).get("TOKEN_ENDPOINT_URL").toString());
            config.setProperty("resource_server_url", hsm_config.get(0).get("RESOURCE_SERVER_URL").toString());
            config.setProperty("resource_server_2fa_url", hsm_config.get(0).get("RESOURCE_SERVER_URL").toString() + "/mobile");
        }
        List<Map<String, Object>> hsm_account =  VienphiCMUDAO.cmu_getlist(new Object[]{dvtt, user}, "HSM_GETACCOUNT(?,?)#c,s,s");
        List<Map<String, Object>> hsm_login =  VienphiCMUDAO.cmu_getlist(new Object[]{dvtt, user}, "HSM_LOGIN_TOKEN(?,?)#c,s,s");
        if(hsm_account.isEmpty()) {
            return "{ERROR:1}";
        }
        OAuth2Details oauthDetails = oAuthUtils.createOAuthDetails(config);
        if(hsm_login.isEmpty() || hsm_login.get(0).get("LOGIN_REFRESH").toString().equals("1")) {
            Map map = oAuthUtils.getAccessTokenOwnerPass(oauthDetails, hsm_account.get(0).get("PASSWORD").toString(),
                    hsm_account.get(0).get("USERNAME").toString());
            oauthDetails.setAccessToken(map.get("access_token").toString());
            VienphiCMUDAO.cmu_getstring(new Object[]{dvtt, map.get("access_token").toString(),
                    map.get("refresh_token").toString(),
                    user,
                    map.get("expires_in").toString()
            }, "HSM_INSERT_LOGIN(?,?,?,?,?)#s,s,s,s,s,s");
        } else if( hsm_login.get(0).get("LOGIN_AGAIN").toString().equals("1")) {
            Map map = oAuthUtils.getAccessTokenByRefreshToken(oauthDetails, hsm_login.get(0).get("REFRESH_TOKEN").toString());
            oauthDetails.setAccessToken(map.get("access_token").toString());
            VienphiCMUDAO.cmu_getstring(new Object[]{dvtt, map.get("access_token").toString(),
                    map.get("refresh_token").toString(),
                    user,
                    map.get("expires_in").toString()
            }, "HSM_INSERT_LOGIN(?,?,?,?,?)#s,s,s,s,s,s");
        } else {
            oauthDetails.setAccessToken(hsm_login.get(0).get("ACCESS_TOKEN").toString());
        }
        String upfile = parameters.get("upfile").toString();
        Decoder decoder = Base64.getDecoder();
        byte[] decodedByte = decoder.decode(upfile.split(",")[1]);
        String path = session.getServletContext().getRealPath("/")+ "WEB-INF/pages/camau/reports/";
        File dir = new File(path);
        try{
            if (!dir.exists())
                //dir.mkdirs();
                return "{ERROR:2}";

            String filename = func+"_"+parameters.get("mabenhnhan").toString()+dvtt+new SimpleDateFormat("yyMMddHHmmss").format(new Date())+".pdf";
            FileOutputStream fos = new FileOutputStream(dir.getAbsolutePath() + File.separator + filename);
            fos.write(decodedByte);
            fos.close();
            byte[] fileContent = Files.readAllBytes(Paths.get(dir.getAbsolutePath() + File.separator + filename));

            String data = Base64.getEncoder().encodeToString(fileContent);
            SignPdfAdvanceParamenter param = new SignPdfAdvanceParamenter();
            param.setVisibleType(parameters.get("visibleType").toString());
            param.setFontSize(parameters.get("fontSize").toString());
            param.setFontName(parameters.get("fontName").toString());
            param.setFontStyle(parameters.get("fontStyle").toString());
            param.setFontColor(parameters.get("fontColor").toString());
            param.setTextAlign(parameters.get("textAlign").toString());
            String[] param_position = parameters.get("position").toString().split(";");
            String enableComment = parameters.get("comment").toString();
            List<Map<String, Object>> hsm_position =  VienphiCMUDAO.cmu_getlist(new Object[]{dvtt, func}, "HSM_GETPOSITION(?,?)#c,s,s");
            JSONArray signatures_arr = new JSONArray();
            JSONArray comment_arr = new JSONArray();
            if (parameters.get("keyword").toString().equals("0")) {
                param.setSignatures(Base64.getEncoder().encodeToString(parameters.get("signatures").toString().getBytes()));
            } else {
                if (hsm_position.isEmpty()) {
                    List<Map<String, Object>> position = PDFTextLocator.getCoordiantes(parameters.get("keyword").toString(), dir.getAbsolutePath() + File.separator + filename);
                    ListIterator<Map<String, Object>>
                            iterator = position.listIterator();


                    int i = 0;
                    String mark_position = "";
                    while (iterator.hasNext()) {
                        JSONObject obj_position = new JSONObject();
                        String[] t_position = param_position[i].split(",");
                        Map<String, Object> dataMap = iterator.next();
                        int x = Integer.valueOf(dataMap.get("x").toString()) + Integer.valueOf(t_position[0]);
                        int y = Integer.valueOf(dataMap.get("y").toString()) + Integer.valueOf(t_position[1]);
                        int x1 = Integer.valueOf(dataMap.get("x").toString()) + Integer.valueOf(t_position[2]);
                        int y1 = Integer.valueOf(dataMap.get("y").toString()) + Integer.valueOf(t_position[3]);
                        mark_position += dataMap.get("x").toString() + "," + dataMap.get("y").toString() + "," + dataMap.get("page").toString() + ";";
                        obj_position.put("rectangle", x + "," + y + "," + x1 + "," + y1);
                        obj_position.put("page", dataMap.get("page").toString());
                        signatures_arr.put(obj_position);
                        if (enableComment.equals("1")) {
                            JSONObject obj_comment = new JSONObject();
                            obj_comment.put("rectangle", x + "," + y + "," + x1 + "," + y1);
                            obj_comment.put("page", dataMap.get("page").toString());
                            obj_comment.put("fontName", parameters.get("comment_fontName").toString());
                            obj_comment.put("fontStyle", parameters.get("comment_fontStyle").toString());
                            obj_comment.put("fontSize", parameters.get("comment_fontSize").toString());
                            obj_comment.put("fontColor", parameters.get("comment_fontColor").toString());
                            comment_arr.put(obj_comment);
                        }
                        i++;
                    }
                    VienphiCMUDAO.cmu_getstring(new Object[]{dvtt, func, mark_position.substring(0, mark_position.length() - 1)
                    }, "HSM_INSERT_POSITION(?,?,?)#s,s,s,s");
                } else {
                    String[] store_position = hsm_position.get(0).get("POSITION").toString().split(";");
                    for (int i = 0; i < store_position.length; i++) {
                        JSONObject obj_position = new JSONObject();
                        String[] t_position = param_position[i].split(",");
                        String[] t_pos = store_position[i].split(",");
                        int x = Integer.valueOf(t_pos[0]) + Integer.valueOf(t_position[0]);
                        int y = Integer.valueOf(t_pos[1]) + Integer.valueOf(t_position[1]);
                        int x1 = Integer.valueOf(t_pos[0]) + Integer.valueOf(t_position[2]);
                        int y1 = Integer.valueOf(t_pos[1]) + Integer.valueOf(t_position[3]);
                        obj_position.put("rectangle", x + "," + y + "," + x1 + "," + y1);
                        obj_position.put("page", t_pos[2]);
                        signatures_arr.put(obj_position);
                        if (enableComment.equals("1")) {
                            JSONObject obj_comment = new JSONObject();
                            obj_comment.put("rectangle", x + "," + y + "," + x1 + "," + y1);
                            obj_comment.put("page", t_pos[4]);
                            obj_comment.put("fontName", parameters.get("comment_fontName").toString());
                            obj_comment.put("fontStyle", parameters.get("comment_fontStyle").toString());
                            obj_comment.put("fontSize", parameters.get("comment_fontSize").toString());
                            obj_comment.put("fontColor", parameters.get("comment_fontColor").toString());
                            comment_arr.put(obj_comment);
                        }
                    }
                }
                param.setSignatures(Base64.getEncoder().encodeToString(signatures_arr.toString().getBytes()));
            }
            if(enableComment.equals("1")) {
                param.setComment(Base64.getEncoder().encodeToString(comment_arr.toString().getBytes()));
            }
            param.setImage(hsm_account.get(0).get("CHUKY").toString());
            param.setSignatureText(hsm_account.get(0).get("HOTENHIENTHI").toString());
            param.setIsDebug(parameters.get("debug").toString());
            String result = signPdfAdvance(oauthDetails, data, param, dvtt, parameters, user, func);
            File fileTemp = new File(dir.getAbsolutePath() + File.separator + filename);
            fileTemp.delete();
            VienphiCMUDAO.cmu_getstring(new Object[]{dvtt, param.toString(),data,user,func,result
            }, "HSM_LUULOG(?,?,?,?,?,?)#s,s,s,s,s,s,s");
            return result;
        }
        catch(Exception e){
            return "{ERROR:"+e.toString()+"}";

        }

    }
    /**
     * Gửi thẳng file lên VNPT-Kyso để ký và nhận về file đã ký
     * @param oauthDetails
     * @param dataBase64
     */
    public  String signPdfAdvance(OAuth2Details oauthDetails, String dataBase64,
                                  SignPdfAdvanceParamenter param, String dvtt, Map parameters, String user, String loaifile) {
        String certId = "";
        List<Certificate> certs = AccountService.getAccountCertificate(oauthDetails);
        if (certs == null || certs.isEmpty()) {
            return "{ERROR:No certificates found for this account}";
        }
        // TODO: Select user certificates
        certId = certs.get(0).getID();

        String groupId = AccountService.isValidPaymentInfo(oauthDetails);
        if (groupId == null) {
            return "{ERROR:No valid payment information found}";
        }

        RequestMessage req = null;
        Gson gson = new Gson();
        String para = null;
        ObjectMapper objectMapper = new ObjectMapper();

        CloseableHttpClient client = OAuthUtils.initSecureClient();

        try {
            HttpPost request = new HttpPost(oauthDetails.getResourceServerUrl());
            request.addHeader("Content-Type", "application/json; charset=UTF-8");

            String oauth = OAuthConstants.BEARER + " " + oauthDetails.getAccessToken();
            request.addHeader(OAuthConstants.AUTHORIZATION, oauth);
            StringEntity params = null;

            param.setCertID(certId);
            param.setServiceGroupID(groupId);
            param.setFileName("file_"+new SimpleDateFormat("yyMMddHHmmss").format(new Date())+".pdf");
            param.setDataBase64(dataBase64);

            req = new RequestMessage(UUID.randomUUID().toString(), ServiceID.SignatureService,
                    FunctionName.SignPdfAdvance, param);

            para = objectMapper.writeValueAsString(req);
            params = new StringEntity(para);
            request.setEntity(params);

            CloseableHttpResponse response = client.execute(request);
            String respContent = EntityUtils.toString(response.getEntity());
            int code = response.getStatusLine().getStatusCode();
            if (OAuthConstants.HTTP_OK != code) {
                System.out.println(respContent);
                return respContent;
            }

            ResponseMessage responseMesg = gson.fromJson(respContent, ResponseMessage.class);

            if (responseMesg.getResponseCode() == 53) {
                TwoFactorResp cont = gson.fromJson(gson.toJson(responseMesg.getContent()), TwoFactorResp.class);
                return "{TWOFACTOR:"+cont.getTranID()+";"+cont.getAuthType()+"}";
            } else if (responseMesg.getResponseCode() != 1) {
                return "{ERROR:"+ responseMesg.getResponesContent()+"}";
            } else {
                // Successful
                SignContent signed = gson.fromJson(gson.toJson(responseMesg.getContent()), SignContent.class);
                System.out.println(signed.getSignedData());
                VienphiCMUDAO.cmu_getstring(new Object[]{dvtt, user,
                        parameters.get("mabenhnhan").toString(),
                        loaifile,
                        signed.getTranID(),
                        signed.getSignedData()
                }, "HSM_LUUFILE_SIGNED(?,?,?,?,?,?)#s,s,s,s,s,s,s");
            }

            return respContent;
        } catch (Exception ex) {
            ex.printStackTrace();
            return "{ERROR:"+ex.toString()+"}";
        } finally {
            try {
                client.close();
            } catch (IOException e) {
                return "{ERROR:"+e.toString()+"}";

            }
        }
    }


    public static void writeToFile(byte[] input, String pathname) {
        FileOutputStream outStream = null;
        try {
            outStream = new FileOutputStream(new File(pathname));
            outStream.write(input);
        } catch (FileNotFoundException e) {
            e.printStackTrace();
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            if (outStream != null) {
                try {
                    outStream.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
    }



    @RequestMapping(
            value = {"/cmu_intoathuoc_nolog"},
            method = {RequestMethod.GET},
            produces = {"text/html; charset=utf-8"}
    )
    @ResponseBody
    public void cmu_intoathuoc_nolog(@RequestParam("makb") String makb, @RequestParam("nghiepvu") String nghiepvu, @RequestParam("dvtt") String dvtt, @RequestParam(value = "duocpham",required = false,defaultValue = "0") String duocpham, @RequestParam(value = "kyso",required = false,defaultValue = "0") String kyso, @RequestParam(value = "type",required = false,defaultValue = "0") String type, @RequestParam(value = "makho",required = false,defaultValue = "") String makho, HttpServletRequest request, HttpServletResponse response) throws Exception {
        Thamsohethong tsht = userDAO.thamsohethong(dvtt);
        String sophieu = this.khambenhDAO.laysophieuthanhtoan(makb, dvtt, "0");
        Map parameters = new HashMap();
        String sql = "select SOVAOVIEN, nvl(DIEUTRICHUYENKHOA,0) as DIEUTRICHUYENKHOA,nvl(DOTDIEUTRIDAUTIEN,0) as DOTDIEUTRIDAUTIEN,to_char(NGAY_THANH_TOAN,'YYYY-MM-DD') as NGAY_THANH_TOAN   from kb_phieuthanhtoan  where SOPHIEUTHANHTOAN = ?  and DVTT = ?";
        Map map_temp = this.khambenhDAO.queryForMap(sql, new Object[]{sophieu, dvtt});
        String dotdieutrichuyenkhoa = map_temp.get("DIEUTRICHUYENKHOA").toString();
        String sovaovien = map_temp.get("SOVAOVIEN").toString();
        String ptt_dieutrichuyenkhoa = map_temp.get("DOTDIEUTRIDAUTIEN").toString();
        String ngaybangke = map_temp.get("NGAY_THANH_TOAN").toString();
        if (nghiepvu.equals("ngoaitru_toamienphi") || nghiepvu.equals("ngoaitru_toaquaybanthuocbv")) {
            try {
                this.khambenhDAO.capnhat_hoantatkham_2348(dvtt, makb.replaceFirst("kb_", ""), ngaybangke);
            } catch (Exception var139) {
            }
        }

        Map<String, Object> map = this.khambenhDAO.hienthibangin_GT(dvtt, sophieu, makb, ptt_dieutrichuyenkhoa, dotdieutrichuyenkhoa, sovaovien, "0", tsht.thamsolaythongtin_lenbaocao_bangb1);
        Map<String, Object> map_ptt = this.khambenhDAO.hienthibangin_ptt_GT(dvtt, sophieu, makb, sovaovien);
        Map<String, Object> map_chandoan = this.khambenhDAO.hienthibangin_chandoan_GT(dvtt, sophieu, makb, ptt_dieutrichuyenkhoa, dotdieutrichuyenkhoa);
        Map<String, Object> map_khambenh = this.khambenhDAO.hienthithongtinkhambenh(makb, dvtt);
        String matoathuoc = makb.replace("kb_", "tt_");
        Map<String, Object> map_toathuoc = this.khambenhDAO.hienthithongtin_toathuoc(matoathuoc, dvtt);
        String maphongkham = "";
        String tenphongkham = "";
        String tenkhoakham = "";
        String Chandoan = map_chandoan.get("CHANDOAN").toString();
        String chandoan_bacsi = map_chandoan.get("CHANDOANBACSI").toString();
        if (!chandoan_bacsi.trim().equals("")) {
            Chandoan = chandoan_bacsi;
        }

        String sophieuthanhtoan = map.get("sophieuthanhtoan") == null ? "" : map.get("sophieuthanhtoan").toString();
        parameters.put("sophieuthanhtoan", sophieuthanhtoan);
        parameters.put("type", type);
        parameters.put("tinhthanhpho", tsht.tinh);
        String ICD = map_chandoan.get("ICD").toString();
        String Sophieuthanhtoan = map_ptt.get("SOPHIEUTHANHTOAN").toString();
        String mabenhnhan = map.get("MA_BENH_NHAN").toString();
        String hotenbenhnhan = map.get("TEN_BENH_NHAN").toString();
        String ngaysinh = map.get("NGAY_SINH").toString();
        String ngaytiepnhan = map.get("THOI_GIAN_TIEP_NHAN").toString();
        String ngaysinh1 = map.get("NGAY_SINH").toString();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        Date ngaysinhDate = sdf.parse(ngaysinh1);
        sdf = new SimpleDateFormat("dd/MM/yyyy");
        ngaysinh1 = sdf.format(ngaysinhDate);
        String sqlCallFuncTuoiHienThi = "select hienthi_tuoi_benhnhan_2(?,?) from dual";
        String tuoiht = this.khambenhDAO.queryForString(sqlCallFuncTuoiHienThi, new Object[]{ngaysinh, ngaytiepnhan});
        String tuoi = map.get("TUOI").toString();
        String tennghenghiep = map.get("TEN_NGHE_NGHIEP").toString();
        parameters.put("tuoiht", tuoiht);
        String lan_hen = map.get("LAN_HEN") == null ? "" : map.get("LAN_HEN").toString();
        parameters.put("lan_hen", lan_hen);
        String tungay_dongy = "             ";
        String denngay_dongy = "            ";

        try {
            tungay_dongy = tienich.chuyenNgayMysql_Str(map_toathuoc.get("TUNGAY").toString());
            parameters.put("tungay_dongy", tungay_dongy);
        } catch (Exception var138) {
            parameters.put("tungay_dongy", " ");
        }

        try {
            denngay_dongy = tienich.chuyenNgayMysql_Str(map_toathuoc.get("DENNGAY").toString());
            parameters.put("denngay_dongy", denngay_dongy);
        } catch (Exception var137) {
            parameters.put("denngay_dongy", " ");
        }

        String thoigiansudungthuoc = "Thuốc sử dụng từ ngày  " + tungay_dongy + "  đến ngày  " + denngay_dongy;
        String nam = Boolean.parseBoolean(map.get("GIOI_TINH").toString()) ? "x" : "";
        String nu = !Boolean.parseBoolean(map.get("GIOI_TINH").toString()) ? "x" : "";
        String gioitinh = Boolean.parseBoolean(map.get("GIOI_TINH").toString()) ? "Nam" : "Nữ";
        String[] arr_ns = ngaysinh.split("-");
        String namsinh = arr_ns[0];
        arr_ns = ngaysinh.split("-");
        String ngayht_ag = tienich.layngayhientai();
        String[] arr_ht_ag = ngayht_ag.split("/");
        int namsinh_ag = Integer.parseInt(arr_ns[0]);
        int namhientai_ag = Integer.parseInt(arr_ht_ag[2]);
        int tuoi_ag = namhientai_ag - namsinh_ag;
        int thangtam_ns_ag = Integer.parseInt(arr_ns[1]);
        int thangtam_ht_ag = Integer.parseInt(arr_ht_ag[1]);
        int thang_ag = (namhientai_ag - namsinh_ag) * 12 + (thangtam_ht_ag - thangtam_ns_ag) + 1;
        String diachi;
        if (map.get("SO_NHA").toString().trim().equals("")) {
            diachi = map.get("DIA_CHI").toString();
        } else {
            diachi = map.get("SO_NHA").toString() + ", " + map.get("DIA_CHI").toString();
        }

        String the = map.get("SO_THE_BHYT").toString();
        String mathe_2kytudau = "";
        String mathe_kythu3 = "";
        String mathekytu_45 = "";
        String mathe_67 = "";
        String mathe_8910 = "";
        String mathe_5kytucuoi = "";
        String noidangky = "";
        String vienphi = "";
        if (!the.trim().equals("") && !nghiepvu.equals("ngoaitru_toadichvu")) {
            mathe_2kytudau = the.substring(0, 2);
            mathe_kythu3 = the.substring(2, 3);
            mathekytu_45 = the.substring(3, 5);
            mathe_67 = the.substring(5, 7);
            mathe_8910 = the.substring(7, 10);
            mathe_5kytucuoi = the.substring(10, 15);
            noidangky = map.get("NOIDANGKY_KCB").toString();
        } else {
            vienphi = "x";
        }

        String doituongbh = "";
        if (the.equals(" ")) {
            doituongbh = "Thu phí";
        } else {
            doituongbh = "BHYT";
        }

        if (dvtt.equals("82196")) {
            parameters.put("tenbenhvien", tsht.benhvientuyentren.toUpperCase());
            parameters.put("tenkhoakham", "Phòng khám ĐK TTYT Thị Xã Cai Lậy");
            parameters.put("tenphongkham", tenphongkham);
        } else if (Integer.parseInt(tsht.tuyenbenhvien) > 3) {
            parameters.put("tenbenhvien", tsht.benhvientuyentren);
            parameters.put("tenkhoakham", tsht.tenbenhvien.toUpperCase());
            parameters.put("tenphongkham", "");
        } else {
            parameters.put("tenbenhvien", tsht.tenbenhvien.toUpperCase());
            parameters.put("tenkhoakham", tenkhoakham);
            parameters.put("tenphongkham", tenphongkham);
        }

        parameters.put("hovaten", hotenbenhnhan);
        parameters.put("diachi", diachi);
        parameters.put("chucvu", tennghenghiep);
        parameters.put("gioitinh_nam", nam);
        parameters.put("gioitinh_nu", nu);
        parameters.put("gioitinh", gioitinh);
        parameters.put("vienphi", vienphi);
        parameters.put("sovaovien", sovaovien);
        parameters.put("thoigiansudungthuoc", thoigiansudungthuoc);
        parameters.put("ngaysinh1", ngaysinh1);
        String tendangky_kcb_bandau = (String) L2Utils.getValue(map, "TENDANGKY_KCB", "");
        parameters.put("tennoidangky", tendangky_kcb_bandau);
        parameters.put("doituongbh", doituongbh);
        parameters.put("makho", makho);
        if (!dvtt.substring(0, 2).equals("89") && !tsht.matinh.equals("24")) {
            if (dvtt.substring(0, 2).equals("94") && thang_ag < 72) {
                parameters.put("namsinh", thang_ag + " tháng");
            } else {
                parameters.put("namsinh", namsinh);
                if (map.get("TUOI").toString().equals("0")) {
                    parameters.put("tuoi", tuoiht);
                } else {
                    parameters.put("tuoi", tuoi);
                }
            }
        } else if (thang_ag < 72) {
            parameters.put("namsinh", String.valueOf(thang_ag));
            parameters.put("tuoithang", "Tháng:");
        } else {
            parameters.put("namsinh", String.valueOf(tuoi_ag));
            parameters.put("tuoithang", "Tuổi:");
        }

        String mach = map_khambenh.get("MACH") == null ? "" : map_khambenh.get("MACH").toString();
        String huyetapcao = map_khambenh.get("HUYETAPCAO") == null ? "" : map_khambenh.get("HUYETAPCAO").toString();
        String huyetapthap = map_khambenh.get("HUYETAPTHAP") == null ? "" : map_khambenh.get("HUYETAPTHAP").toString();
        String thannhiet = map_khambenh.get("NHIETDO") == null ? "" : map_khambenh.get("NHIETDO").toString();
        String cannang = map_khambenh.get("CANNANG") == null ? "" : map_khambenh.get("CANNANG").toString();
        String ngaykham_tk = map.get("NGAY_GIO_KHAM").toString().trim();
        String chieucao = map_khambenh.get("CHIEUCAO") == null ? "" : map_khambenh.get("CHIEUCAO").toString();
        String bmi = map_khambenh.get("BMI") == null ? "" : map_khambenh.get("BMI").toString();
        String[] arr_nk = ngaykham_tk.split("-");
        String ngaylapbangke = arr_nk[2].substring(0, 2);
        String thanglapbangke = arr_nk[1].substring(0, 2);
        String namlapbangke = arr_nk[0].substring(0, 4);
        String ngaythangratoa = "Ngày " + ngaylapbangke + " tháng " + thanglapbangke + " năm " + namlapbangke;
        String sothan = map_toathuoc.get("SOTHAN").toString();
        String ngayhentaikham = map_khambenh.get("NGAY_HEN") == null ? "" : tienich.chuyenNgayMysql_Str(map_khambenh.get("NGAY_HEN").toString());
        if (!ngayhentaikham.equals("")) {
            String[] ngay_hen_arr = ngayhentaikham.split("/");
            ngayhentaikham = tienich.layThu_Ngay(Integer.parseInt(ngay_hen_arr[0]), Integer.parseInt(ngay_hen_arr[1]), Integer.parseInt(ngay_hen_arr[2])) + ", " + ngayhentaikham;
        }

        String cls = "";
        String cls_cdha = "";
        String cls_xn = "";
        String xnkhac = "";
        String Chandoan_nguyennhan = "";
        String khongincls = this.userDAO.lay_thamsohethong_theoma(dvtt, "49");
        String thamso = this.userDAO.lay_thamsohethong_theoma(dvtt, "66");
        if (khongincls.equals("0")) {
            cls_cdha = "";
            if (cls_cdha.equals("")) {
                cls_cdha = "";
            } else {
                cls_cdha = "Chẩn đoán hình ảnh: ";
            }

            List<Map<String, Object>> xn_list = this.khambenhDAO.intoathuoc_xetnghiem(makb, dvtt);
            if (!xn_list.isEmpty()) {
                Iterator var98;
                Map m;
                if (dvtt.equals("94005")) {
                    for(var98 = xn_list.iterator(); var98.hasNext(); cls = cls + m.get("TEN_XETNGHIEM") + ": " + m.get("KET_QUA") + ", ") {
                        m = (Map)var98.next();
                    }

                    cls_xn = cls.substring(0, cls.length() - 2) + "; ";
                } else {
                    for(var98 = xn_list.iterator(); var98.hasNext(); cls = cls + m.get("TEN_XETNGHIEM") + ", ") {
                        m = (Map)var98.next();
                    }

                    cls = cls.substring(0, cls.length() - 2) + "; ";
                }
            }

            List<Map<String, Object>> cdha_list = this.khambenhDAO.intoathuoc_cdha(makb, dvtt);
            if (!cdha_list.isEmpty()) {
                Map m;
                for(Iterator var144 = cdha_list.iterator(); var144.hasNext(); cls = cls + m.get("TEN_CDHA") + ", ") {
                    m = (Map)var144.next();
                }

                cls = cls.substring(0, cls.length() - 2) + "; ";
            }

            List<Map<String, Object>> ttpt_list = this.khambenhDAO.intoathuoc_ttpt(makb, dvtt);
            if (!cdha_list.isEmpty()) {
                Map m;
                for(Iterator var147 = ttpt_list.iterator(); var147.hasNext(); cls = cls + m.get("TEN_DV") + ", ") {
                    m = (Map)var147.next();
                }

                cls = cls.substring(0, cls.length() - 2);
            }

            if (dvtt.equals("94005")) {
                if (cls_xn.equals("")) {
                    cls = "";
                    xnkhac = "";
                } else {
                    cls = "Xét nghiệm: " + cls_xn;
                    xnkhac = "Xét nghiệm khác: ";
                }
            } else {
                cls = "Cận lâm sàng: " + cls;
            }
        }

        String bacsidt = map.get("BACSI_DT").toString();
        Map thongtin_bs = this.khambenhDAO.thongtin_bacsi(bacsidt);
        String tenbacsi = thongtin_bs.get("ten_nhanvien").toString();
        String chuky = thongtin_bs.get("chuky_nhanvien").toString();
        String maso = "";
        String tongtienthuoc = "";
        tongtienthuoc = this.khambenhDAO.khambenh_ngoaitru_chiphi_thuoc_thuong(matoathuoc, dvtt, nghiepvu, "0");
        String tongtienthuoc_chu = (new docso()).docso(Double.parseDouble(tongtienthuoc));
        parameters.put("tongtienthuoc_bangchu", tongtienthuoc_chu);
        parameters.put("tongtienthuoc_bangso", tongtienthuoc);
        parameters.put("ngayhen", "");
        parameters.put("mach", mach);
        parameters.put("huyetap_tren", huyetapcao);
        parameters.put("huyetap_duoi", huyetapthap);
        parameters.put("thannhiet", thannhiet);
        parameters.put("thamso", thamso);
        parameters.put("canlamsang", cls);
        if (ICD.equals("") && Chandoan.equals("")) {
            parameters.put("chandoan", "Chẩn đoán: ");
        } else if (dvtt.equals("14020")) {
            parameters.put("chandoan", "Chẩn đoán: " + Chandoan);
        } else {
            parameters.put("chandoan", "Chẩn đoán: " + ICD + " - " + Chandoan);
        }

        parameters.put("ICD", ICD);
        parameters.put("sophieu", Sophieuthanhtoan);
        parameters.put("ngay", ngaylapbangke);
        parameters.put("thang", thanglapbangke);
        parameters.put("nam", namlapbangke);
        if (tsht.MACDINH_TENTRENBANGKE_TRENTOATHUOC.equals("0")) {
            if (!tsht.inhotenbacsi.equals("1") && !tsht.inhotenbacsi.equals("2") && !tsht.inhotenbacsi.equals("5")) {
                parameters.put("bacsidieutri", "");
            } else {
                parameters.put("bacsidieutri", tenbacsi);
            }
        } else {
            parameters.put("bacsidieutri", tenbacsi);
        }

        parameters.put("masonguoibenh", mabenhnhan);
        parameters.put("mathe_2kytudau", mathe_2kytudau);
        parameters.put("mathe_kythu3", mathe_kythu3);
        parameters.put("the45", mathekytu_45);
        parameters.put("the67", mathe_67);
        parameters.put("the8910", mathe_8910);
        parameters.put("mathe_5kytucuoi", mathe_5kytucuoi);
        parameters.put("matoathuoc", makb.replace("kb_", "tt_"));
        DateFormat dateFormat = new SimpleDateFormat("dd/MM/yyyy HH:mm:ss");
        String anngayinbangke = this.userDAO.lay_thamsohethong_theoma(dvtt, "96069");
        if (!"1".equals(anngayinbangke)) {
            parameters.put("ngayintoa", dateFormat.format(new Date()));
        }

        parameters.put("ngayhentaikham", ngayhentaikham);
        parameters.put("dvtt", dvtt);
        parameters.put("nghiepvu", nghiepvu);
        parameters.put("ngaythangrathuoc", ngaythangratoa);
        parameters.put("maso", maso);
        parameters.put("sothan", sothan);
        parameters.put("sothebhyt", the);
        parameters.put("ngay_batdau", tienich.chuyenNgayMysql_Str(map.get("NGAY_BATDAU").toString()));
        parameters.put("ngay_ketthuc", tienich.chuyenNgayMysql_Str(map.get("NGAY_HETHAN").toString()));
        String path_hinhanh3 = (new File(request.getSession().getServletContext().getRealPath("/WEB-INF/pages/Canlamsang/binhphuoc.jpg"))).getPath();
        parameters.put("hinhanh3", path_hinhanh3);
        String path_hinhanh2 = (new File(request.getSession().getServletContext().getRealPath("/WEB-INF/pages/Canlamsang/logo_pksannhi.jpg"))).getPath();
        parameters.put("hinhanh", path_hinhanh2);
        parameters.put("nguoiin", "");
        String thongbao = "";
        parameters.put("thongbao", thongbao);
        String tieudequanly = tsht.tendonviquanlytructiep;
        if (tieudequanly.equals("")) {
            tieudequanly = "Sở Y Tế " + tsht.tinh;
        }

        parameters.put("soytetg", tieudequanly.toUpperCase());
        String path_img_chuky = "";
        if (!chuky.trim().equals("")) {
            try {
                path_img_chuky = chuky.replaceFirst("data:image/png;base64", "");
            } catch (Exception var136) {
                var136.printStackTrace();
            }
        } else {
            path_img_chuky = request.getSession().getServletContext().getRealPath("/resources/Theme/images/chuky_rong.png");
        }

        parameters.put("chuky", path_img_chuky);
        parameters.put("nguoilienhe", map.get("NGUOI_LIEN_HE"));
        String tentoathuoc;
        String path_hinhanh;
        if (tsht.matinh.equals("74")) {
            tentoathuoc = "";
            List<Map<String, Object>> xn_list = this.khambenhDAO.intoathuoc_dieutri(makb, dvtt);
            if (!xn_list.isEmpty()) {
                Map m;
                for(Iterator var113 = xn_list.iterator(); var113.hasNext(); tentoathuoc = tentoathuoc + m.get("TEN_DV") + ", ") {
                    m = (Map)var113.next();
                }

                tentoathuoc = tentoathuoc.substring(0, tentoathuoc.length() - 2) + "; ";
            }

            parameters.put("dieutri", tentoathuoc);
            parameters.put("cannang", cannang);
            String bdg_thongtintonghop = "";
            if (cls.length() > 14) {
                bdg_thongtintonghop = bdg_thongtintonghop + cls + '\n';
            }

            if (Chandoan.length() > 1) {
                bdg_thongtintonghop = bdg_thongtintonghop + "Chẩn đoán: (" + ICD + ") " + Chandoan + '\n';
            }

            List bdg_thongtinkb = this.khambenhDAO.laythongtinkb(makb, dvtt);
            LinkedCaseInsensitiveMap BDG_TTKB = (LinkedCaseInsensitiveMap)bdg_thongtinkb.get(0);
            path_hinhanh = BDG_TTKB.get("CHANDOAN_YHCT") == null ? "" : (String)BDG_TTKB.get("CHANDOAN_YHCT");
            if (path_hinhanh.length() > 1) {
                bdg_thongtintonghop = bdg_thongtintonghop + "Chẩn đoán YHCT: " + path_hinhanh + '\n';
            }

            if (tentoathuoc.length() > 1) {
                bdg_thongtintonghop = bdg_thongtintonghop + "PTTT: " + tentoathuoc;
            }

            parameters.put("bdg_thongtintonghop", bdg_thongtintonghop);
        }

        if (!dvtt.equals("82204") && !dvtt.equals("82212")) {
            if (nghiepvu.trim().equals("")) {
                parameters.put("tentoathuoc", "ĐƠN THUỐC TỔNG HỢP");
            } else if (nghiepvu.trim().equals("toathuoc_nghien")) {
                parameters.put("tentoathuoc", "ĐƠN THUỐC NGHIỆN");
            } else if (dvtt.substring(0, 2).equals("94")) {
                parameters.put("tentoathuoc", "Đơn Thuốc");
            } else if (!tsht.tentoathuoc_muataiquaybv.equals("") && nghiepvu.equals("ngoaitru_toaquaybanthuocbv")) {
                parameters.put("tentoathuoc", tsht.tentoathuoc_muataiquaybv);
            } else if (dvtt.equals("08205") && nghiepvu.equals("ngoaitru_toaquaybanthuocbv") && the.equals(" ")) {
                parameters.put("tentoathuoc", "ĐƠN THUỐC BỆNH NHÂN THU PHÍ");
            } else {
                tentoathuoc = this.khambenhDAO.laytentoathuoc_theonghiepvu(nghiepvu).toUpperCase();
                if (dvtt.equals("30016") || dvtt.equals("45012")) {
                    tentoathuoc = tentoathuoc.replace("miễn phí", "BVSK");
                }

                parameters.put("tentoathuoc", tentoathuoc.replaceFirst("TOA", "ĐƠN"));
            }
        } else {
            parameters.put("tentoathuoc", "ĐƠN THUỐC");
        }

        tentoathuoc = this.userDAO.lay_thamsohethong_theoma(dvtt, "93039");
        if (dvtt.equals("17051") && !the.trim().equals("") && nghiepvu.equals("ngoaitru_toadichvu")) {
            mathe_2kytudau = the.substring(0, 2);
            mathe_kythu3 = the.substring(2, 3);
            mathekytu_45 = the.substring(3, 5);
            mathe_67 = the.substring(5, 7);
            mathe_8910 = the.substring(7, 10);
            mathe_5kytucuoi = the.substring(10, 15);
            noidangky = map.get("NOIDANGKY_KCB").toString();
            parameters.put("mathe_2kytudau", mathe_2kytudau);
            parameters.put("mathe_kythu3", mathe_kythu3);
            parameters.put("the45", mathekytu_45);
            parameters.put("the67", mathe_67);
            parameters.put("the8910", mathe_8910);
            parameters.put("mathe_5kytucuoi", mathe_5kytucuoi);
        }

        if (!the.trim().equals("") && nghiepvu.equals("ngoaitru_toadichvu") && tentoathuoc.equals("1")) {
            mathe_2kytudau = the.substring(0, 2);
            mathe_kythu3 = the.substring(2, 3);
            mathekytu_45 = the.substring(3, 5);
            mathe_67 = the.substring(5, 7);
            mathe_8910 = the.substring(7, 10);
            mathe_5kytucuoi = the.substring(10, 15);
            noidangky = map.get("NOIDANGKY_KCB").toString();
            parameters.put("mathe_2kytudau", mathe_2kytudau);
            parameters.put("mathe_kythu3", mathe_kythu3);
            parameters.put("the45", mathekytu_45);
            parameters.put("the67", mathe_67);
            parameters.put("the8910", mathe_8910);
            parameters.put("mathe_5kytucuoi", mathe_5kytucuoi);
        }

        String hgi_tieude = this.userDAO.lay_thamsohethong_theoma(dvtt, "93040");
        if (nghiepvu.equals("ngoaitru_toadichvu") && !hgi_tieude.equals("0")) {
            parameters.put("tentoathuoc", hgi_tieude);
        }

        if (dvtt.substring(0, 2).equals("84")) {
            namsinh = tienich.chuyenNgayMysql_Str(ngaysinh);
            parameters.put("namsinh", namsinh);
        }

        File reportFile = new File("");
        String loidantoathuoc = "";
        String loidantoathuoc_ngayhen = "";
        path_hinhanh = request.getSession().getServletContext().getRealPath("/WEB-INF/pages/Khambenh") + tsht.mautoathuoc.replace("images:", "");
        String toathuoc_maumoi = this.thamsodonviDAO.laythamso_donvi_motthamso(dvtt, "31017");
        String sudung_reportmau = this.thamsodonviDAO.laythamso_donvi_motthamso(dvtt, "86219");
        Map map_duongdan_stg;
        String tongsongayuong;
        String duongdan_intt_stg;
        String duong_dan01;
        String path;
        Map map_duongdan01;
        if (tsht.mautoathuoc.equalsIgnoreCase("giayhen")) {
            if (nghiepvu.equals("ngoaitru_toadongy")) {
                loidantoathuoc = map_toathuoc.get("LOI_DAN_TOA_DONGY") == null ? "" : map_toathuoc.get("LOI_DAN_TOA_DONGY").toString();
                loidantoathuoc_ngayhen = map_toathuoc.get("LOI_DAN_TOA_DONGY_H") == null ? "" : map_toathuoc.get("LOI_DAN_TOA_DONGY_H").toString();
                parameters.put("pheptri", map_toathuoc.get("PHEP_TRI_DONG_Y") == null ? "" : map_toathuoc.get("PHEP_TRI_DONG_Y").toString());
                parameters.put("baithuoc", map_toathuoc.get("BAI_THUOC_DONG_Y") == null ? "" : map_toathuoc.get("BAI_THUOC_DONG_Y").toString());
                map_duongdan_stg = this.khambenhDAO.select_duongdanreport(dvtt, "89053");
                if (map_duongdan_stg != null && !map_duongdan_stg.isEmpty()) {
                    tongsongayuong = map_duongdan_stg.get("DUONG_DAN").toString();
                    reportFile = new File(request.getSession().getServletContext().getRealPath("" + tongsongayuong + ""));
                } else {
                    reportFile = new File(request.getSession().getServletContext().getRealPath("/WEB-INF/pages/Khambenh/kb_donthuoc_dongy.jasper"));
                }
            } else if (nghiepvu.equals("ngoaitru_toadichvu")) {
                loidantoathuoc = map_toathuoc.get("LOI_DAN_TOA_DICHVU_NGOAITRU") == null ? "" : map_toathuoc.get("LOI_DAN_TOA_DICHVU_NGOAITRU").toString();
                loidantoathuoc_ngayhen = map_toathuoc.get("LOI_DAN_TOA_DICHVU_NGOAITRU_H") == null ? "" : map_toathuoc.get("LOI_DAN_TOA_DICHVU_NGOAITRU_H").toString();
                if (dvtt.substring(0, 2).equals("89")) {
                    reportFile = new File(request.getSession().getServletContext().getRealPath("/WEB-INF/pages/Khambenh/AGG_kb_toathuocgiayhen.jasper"));
                } else if (dvtt.substring(0, 2).equals("82")) {
                    reportFile = new File(request.getSession().getServletContext().getRealPath("/WEB-INF/pages/Khambenh/kb_toathuocgiayhen_tg.jasper"));
                } else {
                    reportFile = new File(request.getSession().getServletContext().getRealPath("/WEB-INF/pages/Khambenh/kb_toathuocgiayhen.jasper"));
                }
            } else if (!nghiepvu.equals("ngoaitru_toamienphi") && !nghiepvu.equals("ngoaitru_toavattumienphi")) {
                if (nghiepvu.equals("ngoaitru_toaquaybanthuocbv")) {
                    loidantoathuoc = map_toathuoc.get("LOI_DAN_TOA_THUOC_MUATAIQUAYBV") == null ? "" : map_toathuoc.get("LOI_DAN_TOA_THUOC_MUATAIQUAYBV").toString();
                    loidantoathuoc_ngayhen = map_toathuoc.get("LOI_DAN_TOA_THUOC_MUATAIQUAY_H") == null ? "" : map_toathuoc.get("LOI_DAN_TOA_THUOC_MUATAIQUAY_H").toString();
                    if (dvtt.substring(0, 2).equals("89")) {
                        reportFile = new File(request.getSession().getServletContext().getRealPath("/WEB-INF/pages/Khambenh/AGG_kb_toathuocgiayhen.jasper"));
                    } else if (dvtt.substring(0, 2).equals("82")) {
                        reportFile = new File(request.getSession().getServletContext().getRealPath("/WEB-INF/pages/Khambenh/kb_toathuocgiayhen_tg.jasper"));
                    } else {
                        reportFile = new File(request.getSession().getServletContext().getRealPath("/WEB-INF/pages/Khambenh/kb_toathuocgiayhen.jasper"));
                    }
                } else if (nghiepvu.equals("ngoaitru_toavattu")) {
                    loidantoathuoc = map_toathuoc.get("LOI_DAN_TOA_VATTU_BHYT") == null ? "" : map_toathuoc.get("LOI_DAN_TOA_VATTU_BHYT").toString();
                    loidantoathuoc_ngayhen = map_toathuoc.get("LOI_DAN_TOA_VATTU_BHYT_H") == null ? "" : map_toathuoc.get("LOI_DAN_TOA_VATTU_BHYT_H").toString();
                    if (dvtt.substring(0, 2).equals("89")) {
                        reportFile = new File(request.getSession().getServletContext().getRealPath("/WEB-INF/pages/Khambenh/AGG_kb_toathuocgiayhen.jasper"));
                    } else if (dvtt.substring(0, 2).equals("82")) {
                        reportFile = new File(request.getSession().getServletContext().getRealPath("/WEB-INF/pages/Khambenh/kb_toathuocgiayhen_tg.jasper"));
                    } else {
                        reportFile = new File(request.getSession().getServletContext().getRealPath("/WEB-INF/pages/Khambenh/kb_toathuocgiayhen.jasper"));
                    }
                } else if (nghiepvu.equals("ngoaitru_toathuoc")) {
                    loidantoathuoc = map_toathuoc.get("LOI_DAN_TOA_THUOC") == null ? "" : map_toathuoc.get("LOI_DAN_TOA_THUOC").toString();
                    loidantoathuoc_ngayhen = map_toathuoc.get("LOI_DAN_TOA_THUOC_H") == null ? "" : map_toathuoc.get("LOI_DAN_TOA_THUOC_H").toString();
                    if (dvtt.substring(0, 2).equals("89")) {
                        reportFile = new File(request.getSession().getServletContext().getRealPath("/WEB-INF/pages/Khambenh/AGG_kb_toathuocgiayhen.jasper"));
                    } else if (dvtt.substring(0, 2).equals("82")) {
                        reportFile = new File(request.getSession().getServletContext().getRealPath("/WEB-INF/pages/Khambenh/kb_toathuocgiayhen_tg.jasper"));
                    } else if (tsht.matinh.equals("77")) {
                        reportFile = new File(request.getSession().getServletContext().getRealPath("/WEB-INF/pages/vungtau/VTU_kb_toathuocgiayhen.jasper"));
                    } else {
                        reportFile = new File(request.getSession().getServletContext().getRealPath("/WEB-INF/pages/Khambenh/kb_toathuocgiayhen.jasper"));
                    }
                } else if (nghiepvu.equals("")) {
                    loidantoathuoc = map_toathuoc.get("LOI_DAN_TOA_THUOC") == null ? "" : map_toathuoc.get("LOI_DAN_TOA_THUOC").toString();
                    loidantoathuoc_ngayhen = map_toathuoc.get("LOI_DAN_TOA_THUOC_H") == null ? "" : map_toathuoc.get("LOI_DAN_TOA_THUOC_H").toString();
                    if (dvtt.substring(0, 2).equals("89")) {
                        reportFile = new File(request.getSession().getServletContext().getRealPath("/WEB-INF/pages/Khambenh/AGG_kb_toathuoc_tonghop_giayhen.jasper"));
                    } else if (dvtt.substring(0, 2).equals("82")) {
                        reportFile = new File(request.getSession().getServletContext().getRealPath("/WEB-INF/pages/Khambenh/kb_toathuoc_tonghop_giayhen_tg.jasper"));
                    } else {
                        reportFile = new File(request.getSession().getServletContext().getRealPath("/WEB-INF/pages/Khambenh/kb_toathuoc_tonghop_giayhen.jasper"));
                    }
                }
            } else {
                loidantoathuoc = map_toathuoc.get("LOI_DAN_TOA_THUOC_MIENPHI") == null ? "" : map_toathuoc.get("LOI_DAN_TOA_THUOC_MIENPHI").toString();
                loidantoathuoc_ngayhen = map_toathuoc.get("LOI_DAN_TOA_THUOC_MIENPHI_H") == null ? "" : map_toathuoc.get("LOI_DAN_TOA_THUOC_MIENPHI_H").toString();
                if (dvtt.substring(0, 2).equals("89")) {
                    reportFile = new File(request.getSession().getServletContext().getRealPath("/WEB-INF/pages/Khambenh/AGG_kb_toathuocgiayhen.jasper"));
                } else if (dvtt.substring(0, 2).equals("82")) {
                    reportFile = new File(request.getSession().getServletContext().getRealPath("/WEB-INF/pages/Khambenh/kb_toathuocgiayhen_tg.jasper"));
                } else if (dvtt.equals("26011")) {
                    reportFile = new File(request.getSession().getServletContext().getRealPath("/WEB-INF/pages/vinhphuc/kb_toathuoc_mp_vpc.jasper"));
                } else {
                    reportFile = new File(request.getSession().getServletContext().getRealPath("/WEB-INF/pages/Khambenh/kb_toathuocgiayhen.jasper"));
                }
            }
        } else if (tsht.mautoathuoc.startsWith("images:")) {
            if (nghiepvu.equals("ngoaitru_toadongy")) {
                loidantoathuoc = map_toathuoc.get("LOI_DAN_TOA_DONGY") == null ? "" : map_toathuoc.get("LOI_DAN_TOA_DONGY").toString();
                loidantoathuoc_ngayhen = map_toathuoc.get("LOI_DAN_TOA_DONGY_H") == null ? "" : map_toathuoc.get("LOI_DAN_TOA_DONGY_H").toString();
                parameters.put("pheptri", map_toathuoc.get("PHEP_TRI_DONG_Y") == null ? "" : map_toathuoc.get("PHEP_TRI_DONG_Y").toString());
                parameters.put("baithuoc", map_toathuoc.get("BAI_THUOC_DONG_Y") == null ? "" : map_toathuoc.get("BAI_THUOC_DONG_Y").toString());
                parameters.put("hinhanh", path_hinhanh);
                map_duongdan_stg = this.khambenhDAO.select_duongdanreport(dvtt, "89052");
                if (map_duongdan_stg != null && !map_duongdan_stg.isEmpty()) {
                    tongsongayuong = map_duongdan_stg.get("DUONG_DAN").toString();
                    reportFile = new File(request.getSession().getServletContext().getRealPath("" + tongsongayuong + ""));
                } else {
                    reportFile = new File(request.getSession().getServletContext().getRealPath("/WEB-INF/pages/Khambenh/kb_toathuoc_dongy_caibe.jasper"));
                }
            } else if (nghiepvu.equals("ngoaitru_toadichvu")) {
                parameters.put("hinhanh", path_hinhanh);
                loidantoathuoc = map_toathuoc.get("LOI_DAN_TOA_DICHVU_NGOAITRU") == null ? "" : map_toathuoc.get("LOI_DAN_TOA_DICHVU_NGOAITRU").toString();
                loidantoathuoc_ngayhen = map_toathuoc.get("LOI_DAN_TOA_DICHVU_NGOAITRU_H") == null ? "" : map_toathuoc.get("LOI_DAN_TOA_DICHVU_NGOAITRU_H").toString();
                if (dvtt.substring(0, 2).equals("82")) {
                    reportFile = new File(request.getSession().getServletContext().getRealPath("/WEB-INF/pages/Khambenh/kb_toathuoc_caibe_tg.jasper"));
                } else {
                    reportFile = new File(request.getSession().getServletContext().getRealPath("/WEB-INF/pages/Khambenh/kb_toathuoc_caibe.jasper"));
                }
            } else if (!nghiepvu.equals("ngoaitru_toamienphi") && !nghiepvu.equals("ngoaitru_toavattumienphi")) {
                if (nghiepvu.equals("ngoaitru_toaquaybanthuocbv")) {
                    parameters.put("hinhanh", path_hinhanh);
                    loidantoathuoc = map_toathuoc.get("LOI_DAN_TOA_THUOC_MUATAIQUAYBV") == null ? "" : map_toathuoc.get("LOI_DAN_TOA_THUOC_MUATAIQUAYBV").toString();
                    loidantoathuoc_ngayhen = map_toathuoc.get("LOI_DAN_TOA_THUOC_MUATAIQUAY_H") == null ? "" : map_toathuoc.get("LOI_DAN_TOA_THUOC_MUATAIQUAY_H").toString();
                    if (dvtt.substring(0, 2).equals("82")) {
                        reportFile = new File(request.getSession().getServletContext().getRealPath("/WEB-INF/pages/Khambenh/kb_toathuoc_caibe_tg.jasper"));
                    } else {
                        reportFile = new File(request.getSession().getServletContext().getRealPath("/WEB-INF/pages/Khambenh/kb_toathuoc_caibe.jasper"));
                    }
                } else if (nghiepvu.equals("ngoaitru_toavattu")) {
                    parameters.put("hinhanh", path_hinhanh);
                    loidantoathuoc = map_toathuoc.get("LOI_DAN_TOA_VATTU_BHYT") == null ? "" : map_toathuoc.get("LOI_DAN_TOA_VATTU_BHYT").toString();
                    loidantoathuoc_ngayhen = map_toathuoc.get("LOI_DAN_TOA_VATTU_BHYT_H") == null ? "" : map_toathuoc.get("LOI_DAN_TOA_VATTU_BHYT_H").toString();
                    if (dvtt.substring(0, 2).equals("82")) {
                        reportFile = new File(request.getSession().getServletContext().getRealPath("/WEB-INF/pages/Khambenh/kb_toathuoc_caibe_tg.jasper"));
                    } else {
                        reportFile = new File(request.getSession().getServletContext().getRealPath("/WEB-INF/pages/Khambenh/kb_toathuoc_caibe.jasper"));
                    }
                } else if (nghiepvu.equals("ngoaitru_toathuoc")) {
                    parameters.put("hinhanh", path_hinhanh);
                    loidantoathuoc = map_toathuoc.get("LOI_DAN_TOA_THUOC") == null ? "" : map_toathuoc.get("LOI_DAN_TOA_THUOC").toString();
                    loidantoathuoc_ngayhen = map_toathuoc.get("LOI_DAN_TOA_THUOC_H") == null ? "" : map_toathuoc.get("LOI_DAN_TOA_THUOC_H").toString();
                    if (dvtt.substring(0, 2).equals("82")) {
                        reportFile = new File(request.getSession().getServletContext().getRealPath("/WEB-INF/pages/Khambenh/kb_toathuoc_caibe_tg.jasper"));
                    } else {
                        reportFile = new File(request.getSession().getServletContext().getRealPath("/WEB-INF/pages/Khambenh/kb_toathuoc_caibe.jasper"));
                    }
                } else if (nghiepvu.equals("toathuoc_nghien")) {
                    loidantoathuoc = map_toathuoc.get("LOI_DAN_TOA_THUOC") == null ? "" : map_toathuoc.get("LOI_DAN_TOA_THUOC").toString();
                    loidantoathuoc_ngayhen = map_toathuoc.get("LOI_DAN_TOA_THUOC_H") == null ? "" : map_toathuoc.get("LOI_DAN_TOA_THUOC_H").toString();
                    if (dvtt.substring(0, 2).equals("82")) {
                        reportFile = new File(request.getSession().getServletContext().getRealPath("/WEB-INF/pages/Khambenh/kb_toathuoc_caibe_nghien_tg.jasper"));
                    } else {
                        reportFile = new File(request.getSession().getServletContext().getRealPath("/WEB-INF/pages/Khambenh/kb_toathuoc_caibe_nghien.jasper"));
                    }
                } else if (nghiepvu.equals("")) {
                    loidantoathuoc = map_toathuoc.get("LOI_DAN_TOA_THUOC") == null ? "" : map_toathuoc.get("LOI_DAN_TOA_THUOC").toString();
                    loidantoathuoc_ngayhen = map_toathuoc.get("LOI_DAN_TOA_THUOC_H") == null ? "" : map_toathuoc.get("LOI_DAN_TOA_THUOC_H").toString();
                    if (dvtt.substring(0, 2).equals("82")) {
                        path = KGGUtil.findFileReport(this.khambenhDAO, dvtt, "91027", "/WEB-INF/pages/Khambenh/kb_toathuoc_tonghop_tg.jasper");
                        reportFile = new File(request.getSession().getServletContext().getRealPath(path));
                    } else {
                        path = KGGUtil.findFileReport(this.khambenhDAO, dvtt, "91027", "/WEB-INF/pages/Khambenh/kb_toathuoc_tonghop.jasper");
                        reportFile = new File(request.getSession().getServletContext().getRealPath(path));
                    }
                }
            } else {
                parameters.put("hinhanh", path_hinhanh);
                loidantoathuoc = map_toathuoc.get("LOI_DAN_TOA_THUOC_MIENPHI") == null ? "" : map_toathuoc.get("LOI_DAN_TOA_THUOC_MIENPHI").toString();
                loidantoathuoc_ngayhen = map_toathuoc.get("LOI_DAN_TOA_THUOC_MIENPHI_H") == null ? "" : map_toathuoc.get("LOI_DAN_TOA_THUOC_MIENPHI_H").toString();
                if (dvtt.substring(0, 2).equals("82")) {
                    reportFile = new File(request.getSession().getServletContext().getRealPath("/WEB-INF/pages/Khambenh/kb_toathuoc_caibe_tg.jasper"));
                } else {
                    reportFile = new File(request.getSession().getServletContext().getRealPath("/WEB-INF/pages/Khambenh/kb_toathuoc_caibe.jasper"));
                }
            }
        } else if (tsht.mautoathuoc.startsWith("TT05")) {
            if (!tsht.matinh.equals("74")) {
                parameters.put("tenbenhvien", tsht.tenbenhvien.toUpperCase());
            }

            parameters.put("tuoiht", tuoiht);
            int kiemtra_toathuoca5 = this.khambenhDAO.kiemtra_duongdanreport(dvtt, "77002");
            int kiemtra_toathuoca4 = this.khambenhDAO.kiemtra_duongdanreport(dvtt, "77003");
            this.khambenhDAO.kiemtra_duongdanreport(dvtt, "77004");
            this.khambenhDAO.kiemtra_duongdanreport(dvtt, "77005");
            int kiemtra_toanghiena5 = this.khambenhDAO.kiemtra_duongdanreport(dvtt, "77006");
            int kiemtra_toanghiena4 = this.khambenhDAO.kiemtra_duongdanreport(dvtt, "77007");
            if (nghiepvu.equals("ngoaitru_toadongy")) {
                loidantoathuoc = map_toathuoc.get("LOI_DAN_TOA_DONGY") == null ? "" : map_toathuoc.get("LOI_DAN_TOA_DONGY").toString();
                loidantoathuoc_ngayhen = map_toathuoc.get("LOI_DAN_TOA_DONGY_H") == null ? "" : map_toathuoc.get("LOI_DAN_TOA_DONGY_H").toString();
                parameters.put("pheptri", map_toathuoc.get("PHEP_TRI_DONG_Y") == null ? "" : map_toathuoc.get("PHEP_TRI_DONG_Y").toString());
                parameters.put("baithuoc", map_toathuoc.get("BAI_THUOC_DONG_Y") == null ? "" : map_toathuoc.get("BAI_THUOC_DONG_Y").toString());
                map_duongdan01 = this.khambenhDAO.select_duongdanreport(dvtt, "89051");
                if (map_duongdan01 != null && !map_duongdan01.isEmpty()) {
                    parameters.put("ngaythangrathuoc", ngaylapbangke + "/" + thanglapbangke + "/" + namlapbangke);
                    parameters.put("gioitinh", Boolean.parseBoolean(map.get("GIOI_TINH").toString()) ? "Nam" : "Nữ");
                    tongsongayuong = map_duongdan01.get("DUONG_DAN").toString();
                    reportFile = new File(request.getSession().getServletContext().getRealPath("" + tongsongayuong + ""));
                } else {
                    reportFile = new File(request.getSession().getServletContext().getRealPath("/WEB-INF/pages/angiang/AGG_rp_kb_toathuoc_dongy.jasper"));
                }
            } else {
                if (nghiepvu.equals("ngoaitru_toadichvu")) {
                    loidantoathuoc = map_toathuoc.get("LOI_DAN_TOA_DICHVU_NGOAITRU") == null ? "" : map_toathuoc.get("LOI_DAN_TOA_DICHVU_NGOAITRU").toString();
                    loidantoathuoc_ngayhen = map_toathuoc.get("LOI_DAN_TOA_DICHVU_NGOAITRU_H") == null ? "" : map_toathuoc.get("LOI_DAN_TOA_DICHVU_NGOAITRU_H").toString();
                    map_duongdan01 = this.khambenhDAO.select_duongdanreport(dvtt, "940046");
                    if (map_duongdan01 != null && !map_duongdan01.isEmpty()) {
                        tongsongayuong = this.STG_phongbanDAO.laytongsongayuongthuoc(dvtt, makb.replace("kb_", "tt_"), mabenhnhan);
                        Chandoan_nguyennhan = map_chandoan.get("Chandoan_nguyennhan").toString();
                        if (Chandoan_nguyennhan.equals("")) {
                            parameters.put("chandoan_nguyennhan", "");
                        } else {
                            parameters.put("chandoan_nguyennhan", "Chẩn đoán nguyên nhân: " + Chandoan_nguyennhan);
                        }

                        parameters.put("cannang", cannang);
                        parameters.put("chieucao", chieucao);
                        parameters.put("bmi", bmi);
                        parameters.put("canlamsang", cls);
                        parameters.put("xnkhac", xnkhac);
                        parameters.put("cdha", cls_cdha);
                        parameters.put("ngayuong", tongsongayuong);
                        duongdan_intt_stg = map_duongdan01.get("DUONG_DAN").toString();
                        reportFile = new File(request.getSession().getServletContext().getRealPath("" + duongdan_intt_stg + ""));
                    } else {
                        if (kiemtra_toathuoca5 > 0) {
                            map_duongdan01 = this.khambenhDAO.select_duongdanreport(dvtt, "77002");
                            duongdan_intt_stg = map_duongdan01.get("DUONG_DAN").toString();
                            reportFile = new File(request.getSession().getServletContext().getRealPath("" + duongdan_intt_stg + ""));
                        } else if (kiemtra_toathuoca4 > 0) {
                            map_duongdan01 = this.khambenhDAO.select_duongdanreport(dvtt, "77003");
                            duongdan_intt_stg = map_duongdan01.get("DUONG_DAN").toString();
                            reportFile = new File(request.getSession().getServletContext().getRealPath("" + duongdan_intt_stg + ""));
                        } else {
                            reportFile = new File(request.getSession().getServletContext().getRealPath("/WEB-INF/pages/Khambenh/rp_donthuoc_tt05.jasper"));
                        }
                    }
                } else {
                    Map map_duongdan_nan;
                    String duongdan_intt_nan;
                    if (!nghiepvu.equals("ngoaitru_toamienphi") && !nghiepvu.equals("ngoaitru_toavattumienphi")) {
                        if (nghiepvu.equals("ngoaitru_toaquaybanthuocbv")) {
                            loidantoathuoc = map_toathuoc.get("LOI_DAN_TOA_THUOC_MUATAIQUAYBV") == null ? "" : map_toathuoc.get("LOI_DAN_TOA_THUOC_MUATAIQUAYBV").toString();
                            loidantoathuoc_ngayhen = map_toathuoc.get("LOI_DAN_TOA_THUOC_MUATAIQUAY_H") == null ? "" : map_toathuoc.get("LOI_DAN_TOA_THUOC_MUATAIQUAY_H").toString();
                            map_duongdan01 = this.khambenhDAO.select_duongdanreport(dvtt, "940046");
                            tongsongayuong = this.STG_phongbanDAO.laytongsongayuongthuoc(dvtt, makb.replace("kb_", "tt_"), mabenhnhan);
                            if (map_duongdan01 != null && !map_duongdan01.isEmpty()) {
                                Chandoan_nguyennhan = map_chandoan.get("Chandoan_nguyennhan").toString();
                                if (Chandoan_nguyennhan.equals("")) {
                                    parameters.put("chandoan_nguyennhan", "");
                                } else {
                                    parameters.put("chandoan_nguyennhan", "Chẩn đoán nguyên nhân: " + Chandoan_nguyennhan);
                                }

                                parameters.put("cannang", cannang);
                                parameters.put("chieucao", chieucao);
                                parameters.put("bmi", bmi);
                                parameters.put("canlamsang", cls);
                                parameters.put("xnkhac", xnkhac);
                                parameters.put("cdha", cls_cdha);
                                parameters.put("ngayuong", tongsongayuong);
                                duongdan_intt_stg = map_duongdan01.get("DUONG_DAN").toString();
                                reportFile = new File(request.getSession().getServletContext().getRealPath("" + duongdan_intt_stg + ""));
                            } else {
                                reportFile = new File(request.getSession().getServletContext().getRealPath("/WEB-INF/pages/Khambenh/rp_donthuoc_tt05.jasper"));
                            }

                            if (nghiepvu.equals("ngoaitru_toaquaybanthuocbv")) {
                                map_duongdan_nan = this.khambenhDAO.select_duongdanreport(dvtt, "40033");
                                if (map_duongdan_nan != null && !map_duongdan_nan.isEmpty()) {
                                    Chandoan_nguyennhan = map_chandoan.get("Chandoan_nguyennhan").toString();
                                    if (Chandoan_nguyennhan.equals("")) {
                                        parameters.put("chandoan_nguyennhan", "");
                                    } else {
                                        parameters.put("chandoan_nguyennhan", "Chẩn đoán nguyên nhân: " + Chandoan_nguyennhan);
                                    }

                                    parameters.put("cannang", cannang);
                                    parameters.put("chieucao", chieucao);
                                    parameters.put("bmi", bmi);
                                    parameters.put("canlamsang", cls);
                                    parameters.put("xnkhac", xnkhac);
                                    parameters.put("cdha", cls_cdha);
                                    parameters.put("ngayuong", tongsongayuong);
                                    duongdan_intt_nan = map_duongdan_nan.get("DUONG_DAN").toString();
                                    new File(request.getSession().getServletContext().getRealPath("" + duongdan_intt_nan + ""));
                                } else {
                                    new File(request.getSession().getServletContext().getRealPath("/WEB-INF/pages/Khambenh/rp_donthuoc_tt05.jasper"));
                                }

                                if (duocpham.equals("1")) {
                                    reportFile = new File(request.getSession().getServletContext().getRealPath("/WEB-INF/pages/Khambenh/STG_kb_toathuoc_TT18_94057_duocpham.jasper"));
                                } else if (kiemtra_toathuoca5 > 0) {
                                    map_duongdan01 = this.khambenhDAO.select_duongdanreport(dvtt, "77002");
                                    duong_dan01 = map_duongdan01.get("DUONG_DAN").toString();
                                    reportFile = new File(request.getSession().getServletContext().getRealPath("" + duong_dan01 + ""));
                                } else if (kiemtra_toathuoca4 > 0) {
                                    map_duongdan01 = this.khambenhDAO.select_duongdanreport(dvtt, "77003");
                                    duong_dan01 = map_duongdan01.get("DUONG_DAN").toString();
                                    reportFile = new File(request.getSession().getServletContext().getRealPath("" + duong_dan01 + ""));
                                } else {
                                    reportFile = new File(request.getSession().getServletContext().getRealPath("/WEB-INF/pages/Khambenh/rp_donthuoc_tt05.jasper"));
                                }
                            }
                        } else if (nghiepvu.equals("ngoaitru_toavattu")) {
                            loidantoathuoc = map_toathuoc.get("LOI_DAN_TOA_VATTU_BHYT") == null ? "" : map_toathuoc.get("LOI_DAN_TOA_VATTU_BHYT").toString();
                            loidantoathuoc_ngayhen = map_toathuoc.get("LOI_DAN_TOA_VATTU_BHYT_H") == null ? "" : map_toathuoc.get("LOI_DAN_TOA_VATTU_BHYT_H").toString();
                            map_duongdan01 = this.khambenhDAO.select_duongdanreport(dvtt, "940046");
                            tongsongayuong = this.STG_phongbanDAO.laytongsongayuongthuoc(dvtt, makb.replace("kb_", "tt_"), mabenhnhan);
                            if (map_duongdan01 != null && !map_duongdan01.isEmpty()) {
                                Chandoan_nguyennhan = map_chandoan.get("Chandoan_nguyennhan").toString();
                                if (Chandoan_nguyennhan.equals("")) {
                                    parameters.put("chandoan_nguyennhan", "");
                                } else {
                                    parameters.put("chandoan_nguyennhan", "Chẩn đoán nguyên nhân: " + Chandoan_nguyennhan);
                                }

                                parameters.put("cannang", cannang);
                                parameters.put("chieucao", chieucao);
                                parameters.put("bmi", bmi);
                                parameters.put("canlamsang", cls);
                                parameters.put("xnkhac", xnkhac);
                                parameters.put("cdha", cls_cdha);
                                parameters.put("ngayuong", tongsongayuong);
                                duongdan_intt_stg = map_duongdan01.get("DUONG_DAN").toString();
                                reportFile = new File(request.getSession().getServletContext().getRealPath("" + duongdan_intt_stg + ""));
                            } else if (nghiepvu.equals("ngoaitru_toavattu")) {
                                map_duongdan_nan = this.khambenhDAO.select_duongdanreport(dvtt, "40033");
                                if (map_duongdan_nan != null && !map_duongdan_nan.isEmpty()) {
                                    Chandoan_nguyennhan = map_chandoan.get("Chandoan_nguyennhan").toString();
                                    if (Chandoan_nguyennhan.equals("")) {
                                        parameters.put("chandoan_nguyennhan", "");
                                    } else {
                                        parameters.put("chandoan_nguyennhan", "Chẩn đoán nguyên nhân: " + Chandoan_nguyennhan);
                                    }

                                    parameters.put("cannang", cannang);
                                    parameters.put("chieucao", chieucao);
                                    parameters.put("bmi", bmi);
                                    parameters.put("canlamsang", cls);
                                    parameters.put("xnkhac", xnkhac);
                                    parameters.put("cdha", cls_cdha);
                                    parameters.put("ngayuong", tongsongayuong);
                                    duongdan_intt_nan = map_duongdan_nan.get("DUONG_DAN").toString();
                                    reportFile = new File(request.getSession().getServletContext().getRealPath("" + duongdan_intt_nan + ""));
                                } else if (kiemtra_toathuoca5 > 0) {
                                    map_duongdan01 = this.khambenhDAO.select_duongdanreport(dvtt, "77002");
                                    duong_dan01 = map_duongdan01.get("DUONG_DAN").toString();
                                    reportFile = new File(request.getSession().getServletContext().getRealPath("" + duong_dan01 + ""));
                                } else if (kiemtra_toathuoca4 > 0) {
                                    map_duongdan01 = this.khambenhDAO.select_duongdanreport(dvtt, "77003");
                                    duong_dan01 = map_duongdan01.get("DUONG_DAN").toString();
                                    reportFile = new File(request.getSession().getServletContext().getRealPath("" + duong_dan01 + ""));
                                } else {
                                    reportFile = new File(request.getSession().getServletContext().getRealPath("/WEB-INF/pages/Khambenh/rp_donthuoc_tt05.jasper"));
                                }
                            }
                        } else if (nghiepvu.equals("ngoaitru_toathuoc")) {
                            loidantoathuoc = map_toathuoc.get("LOI_DAN_TOA_THUOC") == null ? "" : map_toathuoc.get("LOI_DAN_TOA_THUOC").toString();
                            loidantoathuoc_ngayhen = map_toathuoc.get("LOI_DAN_TOA_THUOC_H") == null ? "" : map_toathuoc.get("LOI_DAN_TOA_THUOC_H").toString();
                            parameters.put("hinhanh", path_hinhanh);
                            map_duongdan01 = this.khambenhDAO.select_duongdanreport(dvtt, "940046");
                            tongsongayuong = this.STG_phongbanDAO.laytongsongayuongthuoc(dvtt, makb.replace("kb_", "tt_"), mabenhnhan);
                            if (map_duongdan01 != null && !map_duongdan01.isEmpty()) {
                                Chandoan_nguyennhan = map_chandoan.get("Chandoan_nguyennhan").toString();
                                if (Chandoan_nguyennhan.equals("")) {
                                    parameters.put("chandoan_nguyennhan", "");
                                } else {
                                    parameters.put("chandoan_nguyennhan", "Chẩn đoán nguyên nhân: " + Chandoan_nguyennhan);
                                }

                                parameters.put("cannang", cannang);
                                parameters.put("chieucao", chieucao);
                                parameters.put("bmi", bmi);
                                parameters.put("canlamsang", cls);
                                parameters.put("xnkhac", xnkhac);
                                parameters.put("cdha", cls_cdha);
                                parameters.put("ngayuong", tongsongayuong);
                                parameters.put("thamsotenbs", tsht.MACDINH_TENTRENBANGKE_TRENTOATHUOC);
                                duongdan_intt_stg = map_duongdan01.get("DUONG_DAN").toString();
                                reportFile = new File(request.getSession().getServletContext().getRealPath("" + duongdan_intt_stg + ""));
                            } else if (nghiepvu.equals("ngoaitru_toathuoc")) {
                                map_duongdan_nan = this.khambenhDAO.select_duongdanreport(dvtt, "40033");
                                if (map_duongdan_nan != null && !map_duongdan_nan.isEmpty()) {
                                    Chandoan_nguyennhan = map_chandoan.get("Chandoan_nguyennhan").toString();
                                    if (Chandoan_nguyennhan.equals("")) {
                                        parameters.put("chandoan_nguyennhan", "");
                                    } else {
                                        parameters.put("chandoan_nguyennhan", "Chẩn đoán nguyên nhân: " + Chandoan_nguyennhan);
                                    }

                                    parameters.put("cannang", cannang);
                                    parameters.put("chieucao", chieucao);
                                    parameters.put("bmi", bmi);
                                    parameters.put("canlamsang", cls);
                                    parameters.put("xnkhac", xnkhac);
                                    parameters.put("cdha", cls_cdha);
                                    parameters.put("ngayuong", tongsongayuong);
                                    duongdan_intt_nan = map_duongdan_nan.get("DUONG_DAN").toString();
                                    new File(request.getSession().getServletContext().getRealPath("" + duongdan_intt_nan + ""));
                                } else {
                                    new File(request.getSession().getServletContext().getRealPath("/WEB-INF/pages/Khambenh/rp_donthuoc_tt05.jasper"));
                                }

                                if (kiemtra_toathuoca5 > 0) {
                                    map_duongdan01 = this.khambenhDAO.select_duongdanreport(dvtt, "77002");
                                    duong_dan01 = map_duongdan01.get("DUONG_DAN").toString();
                                    reportFile = new File(request.getSession().getServletContext().getRealPath("" + duong_dan01 + ""));
                                } else if (kiemtra_toathuoca4 > 0) {
                                    map_duongdan01 = this.khambenhDAO.select_duongdanreport(dvtt, "77003");
                                    duong_dan01 = map_duongdan01.get("DUONG_DAN").toString();
                                    reportFile = new File(request.getSession().getServletContext().getRealPath("" + duong_dan01 + ""));
                                } else {
                                    reportFile = new File(request.getSession().getServletContext().getRealPath("/WEB-INF/pages/Khambenh/rp_donthuoc_tt05.jasper"));
                                }
                            }
                        } else if (nghiepvu.equals("toathuoc_nghien")) {
                            loidantoathuoc = map_toathuoc.get("LOI_DAN_TOA_THUOC") == null ? "" : map_toathuoc.get("LOI_DAN_TOA_THUOC").toString();
                            loidantoathuoc_ngayhen = map_toathuoc.get("LOI_DAN_TOA_THUOC_H") == null ? "" : map_toathuoc.get("LOI_DAN_TOA_THUOC_H").toString();
                            if (kiemtra_toanghiena5 > 0) {
                                map_duongdan01 = this.khambenhDAO.select_duongdanreport(dvtt, "77006");
                                tongsongayuong = map_duongdan01.get("DUONG_DAN").toString();
                                reportFile = new File(request.getSession().getServletContext().getRealPath("" + tongsongayuong + ""));
                            } else if (kiemtra_toanghiena4 > 0) {
                                map_duongdan01 = this.khambenhDAO.select_duongdanreport(dvtt, "77007");
                                tongsongayuong = map_duongdan01.get("DUONG_DAN").toString();
                                reportFile = new File(request.getSession().getServletContext().getRealPath("" + tongsongayuong + ""));
                            } else {
                                reportFile = new File(request.getSession().getServletContext().getRealPath("/WEB-INF/pages/Khambenh/kb_toathuoc_caibe_nghien.jasper"));
                            }
                        } else if (nghiepvu.equals("")) {
                            loidantoathuoc = map_toathuoc.get("LOI_DAN_TOA_THUOC") == null ? "" : map_toathuoc.get("LOI_DAN_TOA_THUOC").toString();
                            loidantoathuoc_ngayhen = map_toathuoc.get("LOI_DAN_TOA_THUOC_H") == null ? "" : map_toathuoc.get("LOI_DAN_TOA_THUOC_H").toString();
                            path = KGGUtil.findFileReport(this.khambenhDAO, dvtt, "91027", "/WEB-INF/pages/Khambenh/kb_toathuoc_tonghop.jasper");
                            reportFile = new File(request.getSession().getServletContext().getRealPath(path));
                        }
                    } else {
                        map_duongdan01 = this.khambenhDAO.select_duongdanreport(dvtt, "940046");
                        tongsongayuong = this.STG_phongbanDAO.laytongsongayuongthuoc(dvtt, makb.replace("kb_", "tt_"), mabenhnhan);
                        if (map_duongdan01 != null && !map_duongdan01.isEmpty()) {
                            Chandoan_nguyennhan = map_chandoan.get("Chandoan_nguyennhan").toString();
                            if (Chandoan_nguyennhan.equals("")) {
                                parameters.put("chandoan_nguyennhan", "");
                            } else {
                                parameters.put("chandoan_nguyennhan", "Chẩn đoán nguyên nhân: " + Chandoan_nguyennhan);
                            }

                            parameters.put("cannang", cannang);
                            parameters.put("chieucao", chieucao);
                            parameters.put("bmi", bmi);
                            parameters.put("canlamsang", cls);
                            parameters.put("xnkhac", xnkhac);
                            parameters.put("cdha", cls_cdha);
                            parameters.put("ngayuong", tongsongayuong);
                            duongdan_intt_stg = map_duongdan01.get("DUONG_DAN").toString();
                            reportFile = new File(request.getSession().getServletContext().getRealPath("" + duongdan_intt_stg + ""));
                        } else if (nghiepvu.equals("ngoaitru_toamienphi") || nghiepvu.equals("ngoaitru_toavattumienphi")) {
                            map_duongdan_nan = this.khambenhDAO.select_duongdanreport(dvtt, "40033");
                            if (map_duongdan_nan != null && !map_duongdan_nan.isEmpty()) {
                                Chandoan_nguyennhan = map_chandoan.get("Chandoan_nguyennhan").toString();
                                if (Chandoan_nguyennhan.equals("")) {
                                    parameters.put("chandoan_nguyennhan", "");
                                } else {
                                    parameters.put("chandoan_nguyennhan", "Chẩn đoán nguyên nhân: " + Chandoan_nguyennhan);
                                }

                                parameters.put("cannang", cannang);
                                parameters.put("chieucao", chieucao);
                                parameters.put("bmi", bmi);
                                parameters.put("canlamsang", cls);
                                parameters.put("xnkhac", xnkhac);
                                parameters.put("cdha", cls_cdha);
                                parameters.put("ngayuong", tongsongayuong);
                                duongdan_intt_nan = map_duongdan_nan.get("DUONG_DAN").toString();
                                reportFile = new File(request.getSession().getServletContext().getRealPath("" + duongdan_intt_nan + ""));
                            } else if (!dvtt.equals("34004") && !dvtt.equals("34317")) {
                                if (kiemtra_toathuoca5 > 0) {
                                    map_duongdan01 = this.khambenhDAO.select_duongdanreport(dvtt, "77002");
                                    duong_dan01 = map_duongdan01.get("DUONG_DAN").toString();
                                    reportFile = new File(request.getSession().getServletContext().getRealPath("" + duong_dan01 + ""));
                                } else if (kiemtra_toathuoca4 > 0) {
                                    map_duongdan01 = this.khambenhDAO.select_duongdanreport(dvtt, "77003");
                                    duong_dan01 = map_duongdan01.get("DUONG_DAN").toString();
                                    reportFile = new File(request.getSession().getServletContext().getRealPath("" + duong_dan01 + ""));
                                } else {
                                    reportFile = new File(request.getSession().getServletContext().getRealPath("/WEB-INF/pages/Khambenh/rp_donthuoc_tt05.jasper"));
                                }
                            } else {
                                reportFile = new File(request.getSession().getServletContext().getRealPath("/WEB-INF/pages/Khambenh/rp_phieucapthuoc_34004_34317.jasper"));
                            }
                        }
                    }
                }
            }
        } else if (!nghiepvu.equals("ngoaitru_toadongy") && !nghiepvu.equals("intoathuocdongy_muataiquay")) {
            if (nghiepvu.equals("ngoaitru_toadichvu")) {
                loidantoathuoc = map_toathuoc.get("LOI_DAN_TOA_DICHVU_NGOAITRU") == null ? "" : map_toathuoc.get("LOI_DAN_TOA_DICHVU_NGOAITRU").toString();
                loidantoathuoc_ngayhen = map_toathuoc.get("LOI_DAN_TOA_DICHVU_NGOAITRU_H") == null ? "" : map_toathuoc.get("LOI_DAN_TOA_DICHVU_NGOAITRU_H").toString();
                map_duongdan_stg = this.khambenhDAO.select_duongdanreport(dvtt, "940046");
                if (map_duongdan_stg != null && !map_duongdan_stg.isEmpty()) {
                    tongsongayuong = this.STG_phongbanDAO.laytongsongayuongthuoc(dvtt, makb.replace("kb_", "tt_"), mabenhnhan);
                    Chandoan_nguyennhan = map_chandoan.get("Chandoan_nguyennhan").toString();
                    if (Chandoan_nguyennhan.equals("")) {
                        parameters.put("chandoan_nguyennhan", "");
                    } else {
                        parameters.put("chandoan_nguyennhan", "Chẩn đoán nguyên nhân: " + Chandoan_nguyennhan);
                    }

                    parameters.put("cannang", cannang);
                    parameters.put("chieucao", chieucao);
                    parameters.put("bmi", bmi);
                    parameters.put("canlamsang", cls);
                    parameters.put("xnkhac", xnkhac);
                    parameters.put("cdha", cls_cdha);
                    parameters.put("ngayuong", tongsongayuong);
                    duongdan_intt_stg = map_duongdan_stg.get("DUONG_DAN").toString();
                    reportFile = new File(request.getSession().getServletContext().getRealPath("" + duongdan_intt_stg + ""));
                } else if (dvtt.substring(0, 2).equals("89")) {
                    reportFile = new File(request.getSession().getServletContext().getRealPath("/WEB-INF/pages/Khambenh/AGG_kb_toathuoc.jasper"));
                } else if (toathuoc_maumoi.equals("1")) {
                    reportFile = new File(request.getSession().getServletContext().getRealPath("/WEB-INF/pages/haiphong/kb_toathuoc.jasper"));
                } else {
                    parameters.put("tuoiht", tuoiht);
                    if (dvtt.substring(0, 2).equals("82")) {
                        if (dvtt.equals("82204")) {
                            reportFile = new File(request.getSession().getServletContext().getRealPath("/WEB-INF/pages/Khambenh/kb_toathuoc_tg_dalieu.jasper"));
                        } else if (dvtt.equals("82999")) {
                            reportFile = new File(request.getSession().getServletContext().getRealPath("/WEB-INF/pages/Khambenh/kb_toathuoc_pksannhi.jasper"));
                        } else {
                            reportFile = new File(request.getSession().getServletContext().getRealPath("/WEB-INF/pages/Khambenh/kb_toathuoc_tg.jasper"));
                        }
                    } else {
                        reportFile = new File(request.getSession().getServletContext().getRealPath("/WEB-INF/pages/Khambenh/kb_toathuoc.jasper"));
                    }
                }
            } else if (!nghiepvu.equals("ngoaitru_toamienphi") && !nghiepvu.equals("ngoaitru_toavattumienphi")) {
                if (nghiepvu.equals("ngoaitru_toaquaybanthuocbv")) {
                    loidantoathuoc = map_toathuoc.get("LOI_DAN_TOA_THUOC_MUATAIQUAYBV") == null ? "" : map_toathuoc.get("LOI_DAN_TOA_THUOC_MUATAIQUAYBV").toString();
                    loidantoathuoc_ngayhen = map_toathuoc.get("LOI_DAN_TOA_THUOC_MUATAIQUAY_H") == null ? "" : map_toathuoc.get("LOI_DAN_TOA_THUOC_MUATAIQUAY_H").toString();
                    map_duongdan_stg = this.khambenhDAO.select_duongdanreport(dvtt, "940046");
                    if (duocpham.equals("1")) {
                        reportFile = new File(request.getSession().getServletContext().getRealPath("/WEB-INF/pages/Khambenh/STG_kb_toathuoc_TT18_94057_duocpham.jasper"));
                    } else if (map_duongdan_stg != null && !map_duongdan_stg.isEmpty()) {
                        tongsongayuong = this.STG_phongbanDAO.laytongsongayuongthuoc(dvtt, makb.replace("kb_", "tt_"), mabenhnhan);
                        Chandoan_nguyennhan = map_chandoan.get("Chandoan_nguyennhan").toString();
                        if (Chandoan_nguyennhan.equals("")) {
                            parameters.put("chandoan_nguyennhan", "");
                        } else {
                            parameters.put("chandoan_nguyennhan", "Chẩn đoán nguyên nhân: " + Chandoan_nguyennhan);
                        }

                        parameters.put("hinhanh", path_hinhanh);
                        parameters.put("cannang", cannang);
                        parameters.put("chieucao", chieucao);
                        parameters.put("bmi", bmi);
                        parameters.put("canlamsang", cls);
                        parameters.put("xnkhac", xnkhac);
                        parameters.put("cdha", cls_cdha);
                        parameters.put("ngayuong", tongsongayuong);
                        duongdan_intt_stg = map_duongdan_stg.get("DUONG_DAN").toString();
                        if (dvtt.equals("26011")) {
                            reportFile = new File(request.getSession().getServletContext().getRealPath("/WEB-INF/pages/vinhphuc/kb_toathuoc_mp_vpc.jasper"));
                        } else {
                            reportFile = new File(request.getSession().getServletContext().getRealPath("" + duongdan_intt_stg + ""));
                        }
                    } else if (!dvtt.equals("14020") && !dvtt.equals("14022") && !dvtt.equals("14005")) {
                        if (dvtt.substring(0, 2).equals("89")) {
                            reportFile = new File(request.getSession().getServletContext().getRealPath("/WEB-INF/pages/Khambenh/AGG_kb_toathuoc.jasper"));
                        } else if (dvtt.substring(0, 2).equals("20")) {
                            parameters.put("tuoiht", tuoiht);
                            reportFile = new File(request.getSession().getServletContext().getRealPath("/WEB-INF/pages/langson/lsn_kb_toathuoc.jasper"));
                        } else {
                            parameters.put("tuoiht", tuoiht);
                            if (dvtt.substring(0, 2).equals("82")) {
                                if (dvtt.equals("82204")) {
                                    reportFile = new File(request.getSession().getServletContext().getRealPath("/WEB-INF/pages/Khambenh/kb_toathuoc_tg_dalieu.jasper"));
                                } else if (dvtt.equals("82999")) {
                                    reportFile = new File(request.getSession().getServletContext().getRealPath("/WEB-INF/pages/Khambenh/kb_toathuoc_pksannhi.jasper"));
                                } else {
                                    reportFile = new File(request.getSession().getServletContext().getRealPath("/WEB-INF/pages/Khambenh/kb_toathuoc_tg.jasper"));
                                }
                            } else if (toathuoc_maumoi.equals("1")) {
                                reportFile = new File(request.getSession().getServletContext().getRealPath("/WEB-INF/pages/haiphong/kb_toathuoc.jasper"));
                            } else {
                                reportFile = new File(request.getSession().getServletContext().getRealPath("/WEB-INF/pages/Khambenh/kb_toathuoc.jasper"));
                            }
                        }
                    } else {
                        reportFile = new File(request.getSession().getServletContext().getRealPath("/WEB-INF/pages/SonLa/rp_donthuoc_taiquay_14005.jasper"));
                    }
                } else if (nghiepvu.equals("ngoaitru_toavattu")) {
                    loidantoathuoc = map_toathuoc.get("LOI_DAN_TOA_VATTU_BHYT") == null ? "" : map_toathuoc.get("LOI_DAN_TOA_VATTU_BHYT").toString();
                    loidantoathuoc_ngayhen = map_toathuoc.get("LOI_DAN_TOA_VATTU_BHYT_H") == null ? "" : map_toathuoc.get("LOI_DAN_TOA_VATTU_BHYT_H").toString();
                    if (dvtt.substring(0, 2).equals("89")) {
                        reportFile = new File(request.getSession().getServletContext().getRealPath("/WEB-INF/pages/Khambenh/AGG_kb_toathuoc.jasper"));
                    } else if (dvtt.substring(0, 2).equals("20")) {
                        parameters.put("tuoiht", tuoiht);
                        reportFile = new File(request.getSession().getServletContext().getRealPath("/WEB-INF/pages/langson/lsn_kb_toathuoc.jasper"));
                    } else if (dvtt.substring(0, 2).equals("62")) {
                        reportFile = new File(request.getSession().getServletContext().getRealPath("/WEB-INF/pages/Khambenh/kb_toathuoc_tachkho.jasper"));
                    } else {
                        parameters.put("tuoiht", tuoiht);
                        if (dvtt.substring(0, 2).equals("82")) {
                            if (dvtt.equals("82204")) {
                                reportFile = new File(request.getSession().getServletContext().getRealPath("/WEB-INF/pages/Khambenh/kb_toathuoc_tg_dalieu.jasper"));
                            } else if (dvtt.equals("82999")) {
                                reportFile = new File(request.getSession().getServletContext().getRealPath("/WEB-INF/pages/Khambenh/kb_toathuoc_pksannhi.jasper"));
                            } else {
                                reportFile = new File(request.getSession().getServletContext().getRealPath("/WEB-INF/pages/Khambenh/kb_toathuoc_tg.jasper"));
                            }
                        } else if (toathuoc_maumoi.equals("1")) {
                            reportFile = new File(request.getSession().getServletContext().getRealPath("/WEB-INF/pages/haiphong/kb_toathuoc.jasper"));
                        } else {
                            reportFile = new File(request.getSession().getServletContext().getRealPath("/WEB-INF/pages/Khambenh/kb_toathuoc.jasper"));
                        }
                    }
                } else if (nghiepvu.equals("ngoaitru_toathuoc")) {
                    loidantoathuoc = map_toathuoc.get("LOI_DAN_TOA_THUOC") == null ? "" : map_toathuoc.get("LOI_DAN_TOA_THUOC").toString();
                    loidantoathuoc_ngayhen = map_toathuoc.get("LOI_DAN_TOA_THUOC_H") == null ? "" : map_toathuoc.get("LOI_DAN_TOA_THUOC_H").toString();
                    map_duongdan_stg = this.khambenhDAO.select_duongdanreport(dvtt, "940046");
                    if (map_duongdan_stg != null && !map_duongdan_stg.isEmpty()) {
                        tongsongayuong = this.STG_phongbanDAO.laytongsongayuongthuoc(dvtt, makb.replace("kb_", "tt_"), mabenhnhan);
                        Chandoan_nguyennhan = map_chandoan.get("Chandoan_nguyennhan").toString();
                        if (Chandoan_nguyennhan.equals("")) {
                            parameters.put("chandoan_nguyennhan", "");
                        } else {
                            parameters.put("chandoan_nguyennhan", "Chẩn đoán nguyên nhân: " + Chandoan_nguyennhan);
                        }

                        parameters.put("cannang", cannang);
                        parameters.put("chieucao", chieucao);
                        parameters.put("bmi", bmi);
                        parameters.put("canlamsang", cls);
                        parameters.put("xnkhac", xnkhac);
                        parameters.put("cdha", cls_cdha);
                        parameters.put("ngayuong", tongsongayuong);
                        parameters.put("hinhanh", path_hinhanh);
                        duongdan_intt_stg = map_duongdan_stg.get("DUONG_DAN").toString();
                        reportFile = new File(request.getSession().getServletContext().getRealPath("" + duongdan_intt_stg + ""));
                    } else if (tsht.matinh.equals("14")) {
                        if (!dvtt.equals("14020") && !dvtt.equals("14005")) {
                            if (!dvtt.equals("14022") && !dvtt.equals("14239") && !dvtt.equals("14019") && !dvtt.equals("14018") && !dvtt.equals("14030")) {
                                if (tsht.tuyenbenhvien.equals("4")) {
                                    reportFile = new File(request.getSession().getServletContext().getRealPath("/WEB-INF/pages/SonLa/rp_donthuoc_bhyt_TramYTE_Xa.jasper"));
                                }
                            } else {
                                reportFile = new File(request.getSession().getServletContext().getRealPath("/WEB-INF/pages/SonLa/rp_donthuoc_bhyt_bvtamthan.jasper"));
                            }
                        } else {
                            reportFile = new File(request.getSession().getServletContext().getRealPath("/WEB-INF/pages/SonLa/rp_donthuoc_bhyt_14005.jasper"));
                        }
                    } else if (dvtt.equals("66008")) {
                        parameters.put("tuoiht", tuoiht);
                        parameters.put("bacsidieutri", tenbacsi);
                        reportFile = new File(request.getSession().getServletContext().getRealPath("/WEB-INF/pages/Khambenh/dlk_kb_toathuoc.jasper"));
                    } else if (tsht.matinh.equals("77")) {
                        if (!dvtt.equals("77008") && !dvtt.equals("77076")) {
                            if (dvtt.equals("77094")) {
                                reportFile = new File(request.getSession().getServletContext().getRealPath("/WEB-INF/pages/vungtau/VTU_kb_toathuoc_BVMAT.jasper"));
                            } else {
                                reportFile = new File(request.getSession().getServletContext().getRealPath("/WEB-INF/pages/vungtau/VTU_kb_toathuoc.jasper"));
                            }
                        } else {
                            reportFile = new File(request.getSession().getServletContext().getRealPath("/WEB-INF/pages/vungtau/VTU_kb_toathuoc_A4.jasper"));
                        }
                    } else if (!tsht.matinh.equals("22") && !tsht.matinh.equals("36")) {
                        if (dvtt.indexOf("52") != 0 && dvtt.indexOf("56") != 0) {
                            if (dvtt.substring(0, 2).equals("89")) {
                                reportFile = new File(request.getSession().getServletContext().getRealPath("/WEB-INF/pages/Khambenh/AGG_kb_toathuoc.jasper"));
                            } else if (dvtt.substring(0, 2).equals("20")) {
                                parameters.put("tuoiht", tuoiht);
                                reportFile = new File(request.getSession().getServletContext().getRealPath("/WEB-INF/pages/langson/lsn_kb_toathuoc.jasper"));
                            } else {
                                parameters.put("tuoiht", tuoiht);
                                if (dvtt.substring(0, 2).equals("82")) {
                                    if (dvtt.equals("82204")) {
                                        reportFile = new File(request.getSession().getServletContext().getRealPath("/WEB-INF/pages/Khambenh/kb_toathuoc_tg_dalieu.jasper"));
                                    } else if (dvtt.equals("82999")) {
                                        reportFile = new File(request.getSession().getServletContext().getRealPath("/WEB-INF/pages/Khambenh/kb_toathuoc_pksannhi.jasper"));
                                    } else {
                                        reportFile = new File(request.getSession().getServletContext().getRealPath("/WEB-INF/pages/Khambenh/kb_toathuoc_tg.jasper"));
                                    }
                                } else if (toathuoc_maumoi.equals("1")) {
                                    reportFile = new File(request.getSession().getServletContext().getRealPath("/WEB-INF/pages/haiphong/kb_toathuoc.jasper"));
                                } else {
                                    reportFile = new File(request.getSession().getServletContext().getRealPath("/WEB-INF/pages/Khambenh/kb_toathuoc.jasper"));
                                }
                            }
                        } else {
                            reportFile = new File(request.getSession().getServletContext().getRealPath("/WEB-INF/pages/binhdinh/kb_toathuoc_BDH.jasper"));
                        }
                    } else {
                        reportFile = new File(request.getSession().getServletContext().getRealPath("/WEB-INF/pages/Khambenh/kb_toathuoc_A4.jasper"));
                    }
                } else if (nghiepvu.equals("")) {
                    loidantoathuoc = map_toathuoc.get("LOI_DAN_TOA_THUOC") == null ? "" : map_toathuoc.get("LOI_DAN_TOA_THUOC").toString();
                    loidantoathuoc_ngayhen = map_toathuoc.get("LOI_DAN_TOA_THUOC_H") == null ? "" : map_toathuoc.get("LOI_DAN_TOA_THUOC_H").toString();
                    if (dvtt.substring(0, 2).equals("82")) {
                        if (dvtt.equals("82204")) {
                            reportFile = new File(request.getSession().getServletContext().getRealPath("/WEB-INF/pages/Khambenh/kb_toathuoc_tonghop_tg_dalieu.jasper"));
                        } else {
                            path = KGGUtil.findFileReport(this.khambenhDAO, dvtt, "91027", "/WEB-INF/pages/Khambenh/kb_toathuoc_tonghop_tg.jasper");
                            reportFile = new File(request.getSession().getServletContext().getRealPath(path));
                        }
                    } else if (tsht.matinh.equals("77")) {
                        reportFile = new File(request.getSession().getServletContext().getRealPath("/WEB-INF/pages/vungtau/VTU_kb_toathuoc_tonghop.jasper"));
                    } else if (toathuoc_maumoi.equals("1")) {
                        reportFile = new File(request.getSession().getServletContext().getRealPath("/WEB-INF/pages/haiphong/kb_toathuoc_tonghop.jasper"));
                    } else {
                        path = KGGUtil.findFileReport(this.khambenhDAO, dvtt, "91027", "/WEB-INF/pages/Khambenh/kb_toathuoc_tonghop.jasper");
                        reportFile = new File(request.getSession().getServletContext().getRealPath(path));
                    }
                } else if (nghiepvu.equals("toathuoc_nghien")) {
                    loidantoathuoc = map_toathuoc.get("LOI_DAN_TOA_THUOC") == null ? "" : map_toathuoc.get("LOI_DAN_TOA_THUOC").toString();
                    loidantoathuoc_ngayhen = map_toathuoc.get("LOI_DAN_TOA_THUOC_H") == null ? "" : map_toathuoc.get("LOI_DAN_TOA_THUOC_H").toString();
                    if (dvtt.substring(0, 2).equals("89")) {
                        reportFile = new File(request.getSession().getServletContext().getRealPath("/WEB-INF/pages/Khambenh/AGG_kb_toathuoc_tonghop.jasper"));
                    } else if (dvtt.substring(0, 2).equals("82")) {
                        reportFile = new File(request.getSession().getServletContext().getRealPath("/WEB-INF/pages/Khambenh/kb_toathuoc_nghien_tg.jasper"));
                    } else if (tsht.matinh.equals("77")) {
                        reportFile = new File(request.getSession().getServletContext().getRealPath("/WEB-INF/pages/vungtau/VTU_kb_toathuoc_nghien.jasper"));
                    } else {
                        reportFile = new File(request.getSession().getServletContext().getRealPath("/WEB-INF/pages/Khambenh/kb_toathuoc_nghien.jasper"));
                    }
                }
            } else {
                loidantoathuoc = map_toathuoc.get("LOI_DAN_TOA_THUOC_MIENPHI") == null ? "" : map_toathuoc.get("LOI_DAN_TOA_THUOC_MIENPHI").toString();
                loidantoathuoc_ngayhen = map_toathuoc.get("LOI_DAN_TOA_THUOC_MIENPHI_H") == null ? "" : map_toathuoc.get("LOI_DAN_TOA_THUOC_MIENPHI_H").toString();
                if (dvtt.substring(0, 2).equals("89")) {
                    reportFile = new File(request.getSession().getServletContext().getRealPath("/WEB-INF/pages/Khambenh/AGG_kb_toathuoc.jasper"));
                } else if (toathuoc_maumoi.equals("1")) {
                    reportFile = new File(request.getSession().getServletContext().getRealPath("/WEB-INF/pages/haiphong/kb_toathuoc.jasper"));
                } else {
                    parameters.put("tuoiht", tuoiht);
                    map_duongdan_stg = this.khambenhDAO.select_duongdanreport(dvtt, "*********");
                    if (map_duongdan_stg != null && !map_duongdan_stg.isEmpty()) {
                        tongsongayuong = map_duongdan_stg.get("DUONG_DAN").toString();
                        reportFile = new File(request.getSession().getServletContext().getRealPath(tongsongayuong));
                    } else if (dvtt.substring(0, 2).equals("82")) {
                        if (dvtt.equals("82204")) {
                            reportFile = new File(request.getSession().getServletContext().getRealPath("/WEB-INF/pages/Khambenh/kb_toathuoc_tg_dalieu.jasper"));
                        } else if (dvtt.equals("82999")) {
                            reportFile = new File(request.getSession().getServletContext().getRealPath("/WEB-INF/pages/Khambenh/kb_toathuoc_pksannhi.jasper"));
                        } else if (dvtt.equals("82208")) {
                            reportFile = new File(request.getSession().getServletContext().getRealPath("/WEB-INF/pages/Khambenh/kb_toathuoc_toacanbo_82208.jasper"));
                        } else {
                            reportFile = new File(request.getSession().getServletContext().getRealPath("/WEB-INF/pages/Khambenh/kb_toathuoc_tachkho.jasper"));
                        }
                    } else if (dvtt.substring(0, 2).equals("62")) {
                        reportFile = new File(request.getSession().getServletContext().getRealPath("/WEB-INF/pages/Khambenh/kb_toathuoc_tachkho.jasper"));
                    } else {
                        reportFile = new File(request.getSession().getServletContext().getRealPath("/WEB-INF/pages/Khambenh/kb_toathuoc.jasper"));
                    }
                }
            }
        } else {
            map_duongdan_stg = null;
            if ((path = KGGUtil.findFileReport(this.khambenhDAO, dvtt, "91005", (String)null)) != null) {
                loidantoathuoc = map_toathuoc.get("LOI_DAN_TOA_DONGY") == null ? "" : map_toathuoc.get("LOI_DAN_TOA_DONGY").toString();
                loidantoathuoc_ngayhen = map_toathuoc.get("LOI_DAN_TOA_DONGY_H") == null ? "" : map_toathuoc.get("LOI_DAN_TOA_DONGY_H").toString();
                parameters.put("tuoiht", tuoiht);
                reportFile = new File(request.getSession().getServletContext().getRealPath(path));
            } else if (!dvtt.equals("91007") && !dvtt.equals("91004") && !dvtt.equals("91008") && !dvtt.equals("91011")) {
                if (!dvtt.equals("82022") && !dvtt.equals("15103")) {
                    if (dvtt.substring(0, 2).equals("89")) {
                        reportFile = new File(request.getSession().getServletContext().getRealPath("/WEB-INF/pages/Khambenh/AGG_kb_toathuoc_dongy.jasper"));
                    } else {
                        loidantoathuoc = map_toathuoc.get("LOI_DAN_TOA_DONGY") == null ? "" : map_toathuoc.get("LOI_DAN_TOA_DONGY").toString();
                        loidantoathuoc_ngayhen = map_toathuoc.get("LOI_DAN_TOA_DONGY_H") == null ? "" : map_toathuoc.get("LOI_DAN_TOA_DONGY_H").toString();
                        parameters.put("pheptri", map_toathuoc.get("PHEP_TRI_DONG_Y") == null ? "" : map_toathuoc.get("PHEP_TRI_DONG_Y").toString());
                        parameters.put("baithuoc", map_toathuoc.get("BAI_THUOC_DONG_Y") == null ? "" : map_toathuoc.get("BAI_THUOC_DONG_Y").toString());
                        parameters.put("bacsidieutri", tenbacsi);
                        if (sudung_reportmau.equals("1")) {
                            parameters.put("hinhanh", path_hinhanh);
                            map_duongdan01 = this.khambenhDAO.select_duongdanreport(dvtt, "86218");
                            duongdan_intt_stg = map_duongdan01.get("DUONG_DAN").toString();
                            reportFile = new File(request.getSession().getServletContext().getRealPath("" + duongdan_intt_stg + ""));
                        } else if (dvtt.equals("66008")) {
                            reportFile = new File(request.getSession().getServletContext().getRealPath("/WEB-INF/pages/Khambenh/dlk_kb_toathuoc_dongy.jasper"));
                        } else if (dvtt.substring(0, 2).equals("82")) {
                            reportFile = new File(request.getSession().getServletContext().getRealPath("/WEB-INF/pages/Khambenh/kb_toathuoc_dongy_tg.jasper"));
                        } else if (toathuoc_maumoi.equals("1")) {
                            reportFile = new File(request.getSession().getServletContext().getRealPath("/WEB-INF/pages/haiphong/kb_toathuoc.jasper"));
                        } else {
                            reportFile = new File(request.getSession().getServletContext().getRealPath("/WEB-INF/pages/Khambenh/kb_donthuoc_dongy.jasper"));
                        }
                    }
                } else {
                    reportFile = new File(request.getSession().getServletContext().getRealPath("/WEB-INF/pages/Khambenh/kb_donthuoc_dongy.jasper"));
                }
            } else {
                loidantoathuoc = map_toathuoc.get("LOI_DAN_TOA_DONGY") == null ? "" : map_toathuoc.get("LOI_DAN_TOA_DONGY").toString();
                loidantoathuoc_ngayhen = map_toathuoc.get("LOI_DAN_TOA_DONGY_H") == null ? "" : map_toathuoc.get("LOI_DAN_TOA_DONGY_H").toString();
                parameters.put("tuoiht", tuoiht);
                reportFile = new File(request.getSession().getServletContext().getRealPath("/WEB-INF/pages/Khambenh/kb_toathuoc.jasper"));
            }
        }

        path = this.thamsodonviDAO.laythamso_donvi_motthamso(dvtt, "89108");
        if (path.equals("1")) {
            if (tsht.matinh.equals("84")) {
                tongsongayuong = "";
                if (loidantoathuoc.equals(" ") || loidantoathuoc_ngayhen.equals("") || loidantoathuoc_ngayhen.trim().equals("") || loidantoathuoc_ngayhen.trim().equals(" ") || loidantoathuoc_ngayhen.equals(" ")) {
                    tongsongayuong = this.thamsodonviDAO.laythamso_donvi_motthamso(dvtt, "89109");
                }

                if (loidantoathuoc.equals(" ")) {
                    loidantoathuoc = tongsongayuong;
                }

                if (loidantoathuoc_ngayhen.equals("") || loidantoathuoc_ngayhen.trim().equals("") || loidantoathuoc_ngayhen.trim().equals(" ") || loidantoathuoc_ngayhen.equals(" ")) {
                    loidantoathuoc_ngayhen = tongsongayuong;
                }
            } else {
                tongsongayuong = "";
                tongsongayuong = this.thamsodonviDAO.laythamso_donvi_motthamso(dvtt, "89109");
                loidantoathuoc = tongsongayuong;
                if (loidantoathuoc_ngayhen.equals("") || loidantoathuoc_ngayhen.trim().equals("") || loidantoathuoc_ngayhen.trim().equals(" ") || loidantoathuoc_ngayhen.equals(" ")) {
                    loidantoathuoc_ngayhen = tongsongayuong;
                }
            }
        }

        parameters.put("loidantoathuoc", loidantoathuoc);
        parameters.put("loidantoathuoc_ngayhen", loidantoathuoc_ngayhen);
        parameters.put("noidangky", noidangky);
        map_duongdan01 = this.khambenhDAO.sochuyentuyen_select(makb.replaceFirst("kb_", ""), dvtt);
        duongdan_intt_stg = "";
        String sochuyentyt;
        String uri;
        String nguoikyso;
        if (!map_duongdan01.isEmpty()) {
            sochuyentyt = map_duongdan01.get("SO_CHUYEN_TUYEN_TYT").toString();
            uri = map_duongdan01.get("SO_CHUYEN_TUYEN_PK").toString();
            nguoikyso = map_duongdan01.get("SO_CHUYEN_TUYEN_TTYT").toString();
            if (!nguoikyso.equals("")) {
                duongdan_intt_stg = nguoikyso;
            } else if (!uri.equals("")) {
                duongdan_intt_stg = uri;
            } else if (!sochuyentyt.equals("")) {
                duongdan_intt_stg = sochuyentyt;
            }
        }

        if (!duongdan_intt_stg.trim().equals("")) {
            duongdan_intt_stg = "Số chuyển tuyến: " + duongdan_intt_stg;
        }

        parameters.put("sochuyentuyen", duongdan_intt_stg);
        sochuyentyt = this.thamsodonviDAO.laythamso_donvi_motthamso(dvtt, "226");
        String filename;
        JasperReport jasperReport;
        JasperPrint print;
        uri = request.getSession().getServletContext().getRealPath("/WEB-INF/store_files/");
        File file = new File(uri + "/gct_" + dvtt + "_" + makb + (new Date()).getTime() + ".pdf");
        path = reportFile.getPath();
        jasperReport = (JasperReport) JRLoader.loadObject(reportFile);
        JRProperties.setProperty("net.sf.jasperreports.query.executer.factory.plsql", "com.jaspersoft.jrx.query.PlSqlQueryExecuterFactory");
        jasperReport.setProperty("net.sf.jasperreports.query.executer.factory.plsql", "com.jaspersoft.jrx.query.PlSqlQueryExecuterFactory");
        print = JasperFillManager.fillReport(path, parameters, DataSourceUtils.getConnection(this.dataSourceMNG));
        response.setContentType("application/pdf");
        response.addHeader("content-disposition", "inline; filename=" + file.getName());
        OutputStream out = response.getOutputStream();
        JasperExportManager.exportReportToPdfStream(print, out);

    }



    @RequestMapping(
            value = {"/cmu_intoathuoc_bacsi_nolog"},
            method = {RequestMethod.GET},
            produces = {"text/html; charset=utf-8"}
    )
    @ResponseBody
    public void cmu_intoathuoc_bacsi_nolog(@RequestParam("makb") String makb, @RequestParam("nghiepvu") String nghiepvu,
                                     @RequestParam("dvtt") String dvtt, @RequestParam("mabacsi") String mabacsi,
                                     @RequestParam(value = "makho",required = false) String makho,
                                     @RequestParam(value = "maphongkham",required = false) String maphongkham,
                                     @RequestParam(value = "tenphongkham",required = false) String tenphongkham,
                                     HttpServletRequest request, HttpServletResponse response, HttpSession session) throws Exception {

        Thamsohethong tsht = userDAO.thamsohethong(dvtt);
        String sophieu = this.khambenhDAO.laysophieuthanhtoan(makb, dvtt, "0");
        Map parameters = new HashMap();
        String sql = "select SOVAOVIEN,nvl(DIEUTRICHUYENKHOA,0) as DIEUTRICHUYENKHOA,nvl(DOTDIEUTRIDAUTIEN,0) as DOTDIEUTRIDAUTIEN,NGAY_THANH_TOAN  from kb_phieuthanhtoan  where SOPHIEUTHANHTOAN = ?  and DVTT = ?";
        Map map_temp = this.khambenhDAO.queryForMap(sql, new Object[]{sophieu, dvtt});
        String sovaovien = map_temp.get("SOVAOVIEN").toString();
        String dotdieutrichuyenkhoa = map_temp.get("DIEUTRICHUYENKHOA").toString();
        String ptt_dieutrichuyenkhoa = map_temp.get("DOTDIEUTRIDAUTIEN").toString();
        String ngaybangke = map_temp.get("NGAY_THANH_TOAN").toString();
        if (nghiepvu.equals("ngoaitru_toamienphi") || nghiepvu.equals("ngoaitru_toaquaybanthuocbv")) {
            try {
                this.khambenhDAO.capnhat_hoantatkham_2348(dvtt, makb.replaceFirst("kb_", ""), ngaybangke);
            } catch (Exception var110) {
            }
        }

        Map<String, Object> map = this.khambenhDAO.hienthibangin_GT(dvtt, sophieu, makb, ptt_dieutrichuyenkhoa, dotdieutrichuyenkhoa, sovaovien, "0", tsht.thamsolaythongtin_lenbaocao_bangb1);
        Map<String, Object> map_ptt = this.khambenhDAO.hienthibangin_ptt_GT(dvtt, sophieu, makb, sovaovien);
        Map<String, Object> map_chandoan = this.khambenhDAO.hienthibangin_chandoan_GT(dvtt, sophieu, makb, ptt_dieutrichuyenkhoa, dotdieutrichuyenkhoa);
        Map<String, Object> map_khambenh = this.khambenhDAO.hienthithongtinkhambenh(makb, dvtt);
        String matoathuoc = makb.replace("kb_", "tt_");
        Map<String, Object> map_toathuoc = this.khambenhDAO.hienthithongtin_toathuoc(matoathuoc, dvtt);

        String tenkhoakham = "";
        String Chandoan = map_chandoan.get("CHANDOAN").toString();
        String chandoan_bacsi = map_chandoan.get("CHANDOANBACSI").toString();
        if (!chandoan_bacsi.equals("")) {
            Chandoan = chandoan_bacsi;
        }

        String ICD = map_chandoan.get("ICD").toString();
        String Sophieuthanhtoan = map_ptt.get("SOPHIEUTHANHTOAN").toString();
        String mabenhnhan = map.get("MA_BENH_NHAN").toString();
        String hotenbenhnhan = map.get("TEN_BENH_NHAN").toString();
        String ngaysinh = map.get("NGAY_SINH").toString();
        String ngaytiepnhan = map.get("THOI_GIAN_TIEP_NHAN").toString();
        String ngaysinh1 = map.get("NGAY_SINH").toString();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        Date ngaysinhDate = sdf.parse(ngaysinh1);
        sdf = new SimpleDateFormat("dd/MM/yyyy");
        ngaysinh1 = sdf.format(ngaysinhDate);
        String sqlCallFuncTuoiHienThi = "select hienthi_tuoi_benhnhan_2(?,?) from dual";
        String tuoiht = this.khambenhDAO.queryForString(sqlCallFuncTuoiHienThi, new Object[]{ngaysinh, ngaytiepnhan});
        parameters.put("tuoiht", tuoiht);
        String lan_hen = map.get("LAN_HEN") == null ? "" : map.get("LAN_HEN").toString();
        parameters.put("lan_hen", lan_hen);
        String tungay_dongy = "             ";
        String denngay_dongy = "            ";

        try {
            tungay_dongy = tienich.chuyenNgayMysql_Str(map_toathuoc.get("TUNGAY").toString());
            parameters.put("tungay_dongy", tungay_dongy);
        } catch (Exception var109) {
            parameters.put("tungay_dongy", " ");
        }

        try {
            denngay_dongy = tienich.chuyenNgayMysql_Str(map_toathuoc.get("DENNGAY").toString());
            parameters.put("denngay_dongy", denngay_dongy);
        } catch (Exception var108) {
            parameters.put("denngay_dongy", " ");
        }

        String thoigiansudungthuoc = "Thuốc sử dụng từ ngày  " + tungay_dongy + "  đến ngày  " + denngay_dongy;
        String sothan = map_toathuoc.get("SOTHAN").toString();
        parameters.put("sothan", sothan);
        parameters.put("makho", makho);
        parameters.put("thoigiansudungthuoc", thoigiansudungthuoc);
        String nam = Boolean.parseBoolean(map.get("GIOI_TINH").toString()) ? "x" : "";
        String nu = !Boolean.parseBoolean(map.get("GIOI_TINH").toString()) ? "x" : "";
        String[] arr_ns = ngaysinh.split("-");
        String namsinh = arr_ns[0];
        arr_ns = ngaysinh.split("-");
        String ngayht_ag = tienich.layngayhientai();
        String[] arr_ht_ag = ngayht_ag.split("/");
        int namsinh_ag = Integer.parseInt(arr_ns[0]);
        int namhientai_ag = Integer.parseInt(arr_ht_ag[2]);
        int tuoi_ag = namhientai_ag - namsinh_ag;
        int thangtam_ns_ag = Integer.parseInt(arr_ns[1]);
        int thangtam_ht_ag = Integer.parseInt(arr_ht_ag[1]);
        int thang_ag = (namhientai_ag - namsinh_ag) * 12 + (thangtam_ht_ag - thangtam_ns_ag) + 1;
        String diachi;
        if (map.get("SO_NHA").toString().trim().equals("")) {
            diachi = map.get("DIA_CHI").toString();
        } else {
            diachi = map.get("SO_NHA").toString() + ", " + map.get("DIA_CHI").toString();
        }

        String the = map.get("SO_THE_BHYT").toString();
        String mathe_2kytudau = "";
        String mathe_kythu3 = "";
        String mathekytu_45 = "";
        String mathe_67 = "";
        String mathe_8910 = "";
        String mathe_5kytucuoi = "";
        String noidangky = "";
        if (!the.trim().equals("") && !nghiepvu.equals("ngoaitru_toadichvu")) {
            mathe_2kytudau = the.substring(0, 2);
            mathe_kythu3 = the.substring(2, 3);
            mathekytu_45 = the.substring(3, 5);
            mathe_67 = the.substring(5, 7);
            mathe_8910 = the.substring(7, 10);
            mathe_5kytucuoi = the.substring(10, 15);
            noidangky = map.get("NOIDANGKY_KCB").toString();
        }

        if (dvtt.equals("82196")) {
            parameters.put("tenbenhvien", tsht.benhvientuyentren.toUpperCase());
            parameters.put("tenkhoakham", "Phòng khám ĐK TTYT Thị Xã Cai Lậy");
            parameters.put("tenphongkham", tenphongkham);
        } else if (Integer.parseInt(tsht.tuyenbenhvien) > 3) {
            parameters.put("tenbenhvien", tsht.benhvientuyentren);
            parameters.put("tenkhoakham", tsht.tenbenhvien.toUpperCase());
            parameters.put("tenphongkham", "");
        } else {
            parameters.put("tenbenhvien", tsht.tenbenhvien.toUpperCase());
            parameters.put("tenkhoakham", tenkhoakham);
            parameters.put("tenphongkham", tenphongkham);
        }

        String tieudequanly = tsht.tendonviquanlytructiep;
        if (tieudequanly.equals("")) {
            tieudequanly = "Sở Y Tế " + tsht.tinh;
        }

        parameters.put("soytetg", tieudequanly.toUpperCase());
        parameters.put("hovaten", hotenbenhnhan);
        parameters.put("sovaovien", sovaovien);
        if (dvtt.substring(0, 2).equals("89")) {
            if (thang_ag < 72) {
                parameters.put("namsinh", String.valueOf(thang_ag));
                parameters.put("tuoithang", "Tháng:");
            } else {
                parameters.put("namsinh", String.valueOf(tuoi_ag));
                parameters.put("tuoithang", "Tuổi:");
            }
        } else {
            parameters.put("namsinh", namsinh);
        }

        String doituongbh = "";
        if (the.equals(" ")) {
            doituongbh = " ";
        } else {
            doituongbh = "BHYT";
        }

        parameters.put("diachi", diachi);
        parameters.put("gioitinh_nam", nam);
        parameters.put("gioitinh_nu", nu);
        String mach = map_khambenh.get("MACH") == null ? "" : map_khambenh.get("MACH").toString();
        String huyetapcao = map_khambenh.get("HUYETAPCAO") == null ? "" : map_khambenh.get("HUYETAPCAO").toString();
        String huyetapthap = map_khambenh.get("HUYETAPTHAP") == null ? "" : map_khambenh.get("HUYETAPTHAP").toString();
        String thannhiet = map_khambenh.get("NHIETDO") == null ? "" : map_khambenh.get("NHIETDO").toString();
        String cannang = map_khambenh.get("CANNANG") == null ? "" : map_khambenh.get("CANNANG").toString();
        String ngaykham_tk = map.get("NGAY_GIO_KHAM").toString().trim();
        String[] arr_nk = ngaykham_tk.split("-");
        String ngaylapbangke = arr_nk[2].substring(0, 2);
        String thanglapbangke = arr_nk[1].substring(0, 2);
        String namlapbangke = arr_nk[0].substring(0, 4);
        String ngayhentaikham = map_khambenh.get("NGAY_HEN") == null ? "" : tienich.chuyenNgayMysql_Str(map_khambenh.get("NGAY_HEN").toString());
        String path_hinhanh = request.getSession().getServletContext().getRealPath("/resources/Theme/images") + tsht.mautoathuoc.replace("images:", "");
        if (!ngayhentaikham.equals("")) {
            String[] ngay_hen_arr = ngayhentaikham.split("/");
            ngayhentaikham = tienich.layThu_Ngay(Integer.parseInt(ngay_hen_arr[0]), Integer.parseInt(ngay_hen_arr[1]), Integer.parseInt(ngay_hen_arr[2])) + ", " + ngayhentaikham;
        }

        String cls = "";
        String khongincls = this.userDAO.lay_thamsohethong_theoma(dvtt, "49");
        String thamso = this.userDAO.lay_thamsohethong_theoma(dvtt, "66");
        if (khongincls.equals("0")) {
            List<Map<String, Object>> xn_list = this.khambenhDAO.intoathuoc_xetnghiem(makb, dvtt);
            if (!xn_list.isEmpty()) {
                Map m;
                for(Iterator var85 = xn_list.iterator(); var85.hasNext(); cls = cls + m.get("TEN_XETNGHIEM") + ", ") {
                    m = (Map)var85.next();
                }

                cls = cls.substring(0, cls.length() - 2) + "; ";
            }

            List<Map<String, Object>> cdha_list = this.khambenhDAO.intoathuoc_cdha(makb, dvtt);
            if (!cdha_list.isEmpty()) {
                Map m;
                for(Iterator var115 = cdha_list.iterator(); var115.hasNext(); cls = cls + m.get("TEN_CDHA") + ", ") {
                    m = (Map)var115.next();
                }

                cls = cls.substring(0, cls.length() - 2);
            }

            List<Map<String, Object>> ttpt_list = this.khambenhDAO.intoathuoc_ttpt(makb, dvtt);
            if (!cdha_list.isEmpty()) {
                Map m;
                for(Iterator var118 = ttpt_list.iterator(); var118.hasNext(); cls = cls + m.get("TEN_DV") + ", ") {
                    m = (Map)var118.next();
                }

                cls = cls.substring(0, cls.length() - 2);
            }

            cls = "Cận lâm sàng: " + cls;
        }

        String bacsidt = map.get("BACSI_DT").toString();
        Map thongtin_bs = this.khambenhDAO.thongtin_bacsi(bacsidt);
        String tenbacsi = thongtin_bs.get("ten_nhanvien").toString();
        String chuky = thongtin_bs.get("chuky_nhanvien").toString();
        parameters.put("mach", mach);
        parameters.put("huyetap_tren", huyetapcao);
        parameters.put("huyetap_duoi", huyetapthap);
        parameters.put("thannhiet", thannhiet);
        parameters.put("thamso", thamso);
        parameters.put("canlamsang", cls);
        if (ICD.trim().equals("") && Chandoan.trim().equals("")) {
            parameters.put("chandoan", "Chẩn đoán: ");
        } else {
            parameters.put("chandoan", "Chẩn đoán: " + ICD + " - " + Chandoan);
        }

        parameters.put("sophieu", Sophieuthanhtoan);
        parameters.put("ngay", ngaylapbangke);
        parameters.put("thang", thanglapbangke);
        parameters.put("nam", namlapbangke);
        if (!tsht.inhotenbacsi.equals("1") && !tsht.inhotenbacsi.equals("2") && !tsht.inhotenbacsi.equals("5")) {
            parameters.put("bacsidieutri", "");
        } else {
            parameters.put("bacsidieutri", tenbacsi);
        }

        parameters.put("masonguoibenh", mabenhnhan);
        parameters.put("mathe_2kytudau", mathe_2kytudau);
        parameters.put("mathe_kythu3", mathe_kythu3);
        parameters.put("the45", mathekytu_45);
        parameters.put("the67", mathe_67);
        parameters.put("the8910", mathe_8910);
        parameters.put("mathe_5kytucuoi", mathe_5kytucuoi);
        parameters.put("matoathuoc", makb.replace("kb_", "tt_"));
        parameters.put("ngaysinh1", ngaysinh1);
        String tendangky_kcb_bandau = (String)L2Utils.getValue(map, "TENDANGKY_KCB", "");
        parameters.put("tennoidangky", tendangky_kcb_bandau);
        parameters.put("doituongbh", doituongbh);
        parameters.put("ngayhentaikham", ngayhentaikham);
        parameters.put("dvtt", dvtt);
        parameters.put("nghiepvu", nghiepvu);
        parameters.put("mabacsi", mabacsi);
        parameters.put("sothebhyt", the);
        DateFormat dateFormat = new SimpleDateFormat("dd/MM/yyyy HH:mm:ss");
        String anngayinbangke = this.userDAO.lay_thamsohethong_theoma(dvtt, "96069");
        if (!"1".equals(anngayinbangke)) {
            parameters.put("ngayintoa", dateFormat.format(new Date()));
        }

        parameters.put("ngay_batdau", tienich.chuyenNgayMysql_Str(map.get("NGAY_BATDAU").toString()));
        parameters.put("ngay_ketthuc", tienich.chuyenNgayMysql_Str(map.get("NGAY_HETHAN").toString()));
        String path_hinhanh3 = (new File(request.getSession().getServletContext().getRealPath("/WEB-INF/pages/Canlamsang/binhphuoc.jpg"))).getPath();
        parameters.put("hinhanh3", path_hinhanh3);
        String path_hinhanh4 = (new File(request.getSession().getServletContext().getRealPath("/WEB-INF/pages/Khambenh/BinhPhuoc_70995/binhphuoc.jpg"))).getPath();
        parameters.put("hinhanh4", path_hinhanh4);
        String path_hinhanh1 = (new File(request.getSession().getServletContext().getRealPath("/WEB-INF/pages/Canlamsang/logo_pksannhi.jpg"))).getPath();
        parameters.put("hinhanh", path_hinhanh1);
        String thongbao = "";
        parameters.put("thongbao", thongbao);
        String path_img_chuky = "";
        if (!chuky.trim().equals("")) {
            try {
                path_img_chuky = chuky.replaceFirst("data:image/png;base64", "");
            } catch (Exception var107) {
                var107.printStackTrace();
            }
        } else {
            path_img_chuky = request.getSession().getServletContext().getRealPath("/resources/Theme/images/chuky_rong.png");
        }

        parameters.put("chuky", path_img_chuky);
        parameters.put("nguoilienhe", map.get("NGUOI_LIEN_HE"));
        String duong_dan01;
        String loidantoathuoc_ngayhen;
        if (tsht.matinh.equals("74")) {
            String dieutri = "";
            List<Map<String, Object>> xn_list = this.khambenhDAO.intoathuoc_dieutri(makb, dvtt);
            if (!xn_list.isEmpty()) {
                Map m;
                for(Iterator var98 = xn_list.iterator(); var98.hasNext(); dieutri = dieutri + m.get("TEN_DV") + ", ") {
                    m = (Map)var98.next();
                }

                dieutri = dieutri.substring(0, dieutri.length() - 2) + "; ";
            }

            parameters.put("dieutri", dieutri);
            loidantoathuoc_ngayhen = "";
            if (cls.length() > 14) {
                loidantoathuoc_ngayhen = loidantoathuoc_ngayhen + cls + '\n';
            }

            if (Chandoan.length() > 1) {
                loidantoathuoc_ngayhen = loidantoathuoc_ngayhen + "Chẩn đoán: (" + ICD + ") " + Chandoan + '\n';
            }

            List bdg_thongtinkb = this.khambenhDAO.laythongtinkb(makb, dvtt);
            LinkedCaseInsensitiveMap BDG_TTKB = (LinkedCaseInsensitiveMap)bdg_thongtinkb.get(0);
            duong_dan01 = BDG_TTKB.get("CHANDOAN_YHCT") == null ? "" : (String)BDG_TTKB.get("CHANDOAN_YHCT");
            if (duong_dan01.length() > 1) {
                loidantoathuoc_ngayhen = loidantoathuoc_ngayhen + "Chẩn đoán YHCT: " + duong_dan01 + '\n';
            }

            if (dieutri.length() > 1) {
                loidantoathuoc_ngayhen = loidantoathuoc_ngayhen + "PTTT: " + dieutri;
            }

            parameters.put("bdg_thongtintonghop", loidantoathuoc_ngayhen);
        }

        if (nghiepvu.trim().equals("")) {
            parameters.put("tentoathuoc", "ĐƠN THUỐC TỔNG HỢP");
        } else {
            parameters.put("tentoathuoc", this.khambenhDAO.laytentoathuoc_theonghiepvu(nghiepvu).toUpperCase().replaceFirst("TOA", "ĐƠN"));
        }

        File reportFile = new File("");
        String loidantoathuoc = "";
        loidantoathuoc_ngayhen = "";
        String toathuoc_maumoi = this.thamsodonviDAO.laythamso_donvi_motthamso(dvtt, "31017");
        parameters.put("cannang", cannang);
        Map map_duongdan01;
        String sochuyenpk;
        String path;
        byte var127;
        if (tsht.mautoathuoc.equalsIgnoreCase("giayhen")) {
            var127 = -1;
            switch(nghiepvu.hashCode()) {
                case -1826687895:
                    if (nghiepvu.equals("ngoaitru_toadichvu")) {
                        var127 = 1;
                    }
                    break;
                case -1393131636:
                    if (nghiepvu.equals("ngoaitru_toamienphi")) {
                        var127 = 2;
                    }
                    break;
                case -1051988714:
                    if (nghiepvu.equals("ngoaitru_toaquaybanthuocbv")) {
                        var127 = 3;
                    }
                    break;
                case -335830795:
                    if (nghiepvu.equals("ngoaitru_toadongy")) {
                        var127 = 0;
                    }
                    break;
                case -321256043:
                    if (nghiepvu.equals("ngoaitru_toathuoc")) {
                        var127 = 5;
                    }
                    break;
                case -319618326:
                    if (nghiepvu.equals("ngoaitru_toavattu")) {
                        var127 = 4;
                    }
                    break;
                case 0:
                    if (nghiepvu.equals("")) {
                        var127 = 6;
                    }
            }

            switch(var127) {
                case 0:
                    parameters.put("tuoiht", tuoiht);
                    loidantoathuoc = map_toathuoc.get("LOI_DAN_TOA_DONGY") == null ? "" : map_toathuoc.get("LOI_DAN_TOA_DONGY").toString();
                    loidantoathuoc_ngayhen = map_toathuoc.get("LOI_DAN_TOA_DONGY_H") == null ? "" : map_toathuoc.get("LOI_DAN_TOA_DONGY_H").toString();
                    parameters.put("pheptri", map_toathuoc.get("PHEP_TRI_DONG_Y") == null ? "" : map_toathuoc.get("PHEP_TRI_DONG_Y").toString());
                    parameters.put("baithuoc", map_toathuoc.get("BAI_THUOC_DONG_Y") == null ? "" : map_toathuoc.get("BAI_THUOC_DONG_Y").toString());
                    map_duongdan01 = this.khambenhDAO.select_duongdanreport(dvtt, "89021");
                    if (map_duongdan01 != null && !map_duongdan01.isEmpty()) {
                        sochuyenpk = map_duongdan01.get("DUONG_DAN").toString();
                        reportFile = new File(request.getSession().getServletContext().getRealPath("" + sochuyenpk + ""));
                    } else {
                        reportFile = new File(request.getSession().getServletContext().getRealPath("/WEB-INF/pages/angiang/AGG_rp_kb_toathuoc_dongy.jasper"));
                    }
                    break;
                case 1:
                    loidantoathuoc = map_toathuoc.get("LOI_DAN_TOA_DICHVU_NGOAITRU") == null ? "" : map_toathuoc.get("LOI_DAN_TOA_DICHVU_NGOAITRU").toString();
                    loidantoathuoc_ngayhen = map_toathuoc.get("LOI_DAN_TOA_DICHVU_NGOAITRU_H") == null ? "" : map_toathuoc.get("LOI_DAN_TOA_DICHVU_NGOAITRU_H").toString();
                    if (dvtt.substring(0, 2).equals("82")) {
                        reportFile = new File(request.getSession().getServletContext().getRealPath("/WEB-INF/pages/Khambenh/kb_toathuocgiayhen_tg.jasper"));
                    } else if (dvtt.substring(0, 2).equals("89")) {
                        reportFile = new File(request.getSession().getServletContext().getRealPath("/WEB-INF/pages/Khambenh/AGG_kb_toathuocgiayhen.jasper"));
                    } else {
                        reportFile = new File(request.getSession().getServletContext().getRealPath("/WEB-INF/pages/Khambenh/kb_toathuocgiayhen.jasper"));
                    }
                    break;
                case 2:
                    loidantoathuoc = map_toathuoc.get("LOI_DAN_TOA_THUOC_MIENPHI") == null ? "" : map_toathuoc.get("LOI_DAN_TOA_THUOC_MIENPHI").toString();
                    loidantoathuoc_ngayhen = map_toathuoc.get("LOI_DAN_TOA_THUOC_MIENPHI_H") == null ? "" : map_toathuoc.get("LOI_DAN_TOA_THUOC_MIENPHI_H").toString();
                    if (dvtt.substring(0, 2).equals("82")) {
                        reportFile = new File(request.getSession().getServletContext().getRealPath("/WEB-INF/pages/Khambenh/kb_toathuocgiayhen_tg.jasper"));
                    } else if (dvtt.substring(0, 2).equals("89")) {
                        reportFile = new File(request.getSession().getServletContext().getRealPath("/WEB-INF/pages/Khambenh/AGG_kb_toathuocgiayhen.jasper"));
                    } else {
                        reportFile = new File(request.getSession().getServletContext().getRealPath("/WEB-INF/pages/Khambenh/kb_toathuocgiayhen.jasper"));
                    }
                    break;
                case 3:
                    loidantoathuoc = map_toathuoc.get("LOI_DAN_TOA_THUOC_MUATAIQUAYBV") == null ? "" : map_toathuoc.get("LOI_DAN_TOA_THUOC_MUATAIQUAYBV").toString();
                    loidantoathuoc_ngayhen = map_toathuoc.get("LOI_DAN_TOA_THUOC_MUATAIQUAY_H") == null ? "" : map_toathuoc.get("LOI_DAN_TOA_THUOC_MUATAIQUAY_H").toString();
                    if (dvtt.substring(0, 2).equals("82")) {
                        reportFile = new File(request.getSession().getServletContext().getRealPath("/WEB-INF/pages/Khambenh/kb_toathuocgiayhen_tg.jasper"));
                    } else if (dvtt.substring(0, 2).equals("89")) {
                        reportFile = new File(request.getSession().getServletContext().getRealPath("/WEB-INF/pages/Khambenh/AGG_kb_toathuocgiayhen.jasper"));
                    } else {
                        reportFile = new File(request.getSession().getServletContext().getRealPath("/WEB-INF/pages/Khambenh/kb_toathuocgiayhen.jasper"));
                    }
                    break;
                case 4:
                    loidantoathuoc = map_toathuoc.get("LOI_DAN_TOA_VATTU_BHYT") == null ? "" : map_toathuoc.get("LOI_DAN_TOA_VATTU_BHYT").toString();
                    loidantoathuoc_ngayhen = map_toathuoc.get("LOI_DAN_TOA_VATTU_BHYT_H") == null ? "" : map_toathuoc.get("LOI_DAN_TOA_VATTU_BHYT_H").toString();
                    if (dvtt.substring(0, 2).equals("82")) {
                        reportFile = new File(request.getSession().getServletContext().getRealPath("/WEB-INF/pages/Khambenh/kb_toathuocgiayhen_tg.jasper"));
                    } else if (dvtt.substring(0, 2).equals("89")) {
                        reportFile = new File(request.getSession().getServletContext().getRealPath("/WEB-INF/pages/Khambenh/AGG_kb_toathuocgiayhen.jasper"));
                    } else {
                        reportFile = new File(request.getSession().getServletContext().getRealPath("/WEB-INF/pages/Khambenh/kb_toathuocgiayhen.jasper"));
                    }
                    break;
                case 5:
                    loidantoathuoc = map_toathuoc.get("LOI_DAN_TOA_THUOC") == null ? "" : map_toathuoc.get("LOI_DAN_TOA_THUOC").toString();
                    loidantoathuoc_ngayhen = map_toathuoc.get("LOI_DAN_TOA_THUOC_H") == null ? "" : map_toathuoc.get("LOI_DAN_TOA_THUOC_H").toString();
                    if (dvtt.substring(0, 2).equals("82")) {
                        reportFile = new File(request.getSession().getServletContext().getRealPath("/WEB-INF/pages/Khambenh/kb_toathuocgiayhen_tg.jasper"));
                    } else if (tsht.matinh.equals("77")) {
                        reportFile = new File(request.getSession().getServletContext().getRealPath("/WEB-INF/pages/vungtau/VTU_kb_toathuocgiayhen.jasper"));
                    } else if (dvtt.substring(0, 2).equals("89")) {
                        reportFile = new File(request.getSession().getServletContext().getRealPath("/WEB-INF/pages/Khambenh/AGG_kb_toathuocgiayhen.jasper"));
                    } else {
                        reportFile = new File(request.getSession().getServletContext().getRealPath("/WEB-INF/pages/Khambenh/kb_toathuocgiayhen.jasper"));
                    }
                    break;
                case 6:
                    loidantoathuoc = map_toathuoc.get("LOI_DAN_TOA_THUOC") == null ? "" : map_toathuoc.get("LOI_DAN_TOA_THUOC").toString();
                    loidantoathuoc_ngayhen = map_toathuoc.get("LOI_DAN_TOA_THUOC_H") == null ? "" : map_toathuoc.get("LOI_DAN_TOA_THUOC_H").toString();
                    if (dvtt.substring(0, 2).equals("82")) {
                        reportFile = new File(request.getSession().getServletContext().getRealPath("/WEB-INF/pages/Khambenh/kb_toathuoc_tonghop_giayhen_tg.jasper"));
                    } else if (dvtt.substring(0, 2).equals("89")) {
                        reportFile = new File(request.getSession().getServletContext().getRealPath("/WEB-INF/pages/Khambenh/AGG_kb_toathuoc_tonghop_giayhen.jasper"));
                    } else {
                        reportFile = new File(request.getSession().getServletContext().getRealPath("/WEB-INF/pages/Khambenh/kb_toathuoc_tonghop_giayhen.jasper"));
                    }
            }
        } else if (!tsht.mautoathuoc.equalsIgnoreCase("thuong") && !tsht.mautoathuoc.trim().equalsIgnoreCase("")) {
            if (tsht.mautoathuoc.startsWith("images:")) {
                var127 = -1;
                switch(nghiepvu.hashCode()) {
                    case -1826687895:
                        if (nghiepvu.equals("ngoaitru_toadichvu")) {
                            var127 = 1;
                        }
                        break;
                    case -1393131636:
                        if (nghiepvu.equals("ngoaitru_toamienphi")) {
                            var127 = 2;
                        }
                        break;
                    case -1051988714:
                        if (nghiepvu.equals("ngoaitru_toaquaybanthuocbv")) {
                            var127 = 3;
                        }
                        break;
                    case -335830795:
                        if (nghiepvu.equals("ngoaitru_toadongy")) {
                            var127 = 0;
                        }
                        break;
                    case -321256043:
                        if (nghiepvu.equals("ngoaitru_toathuoc")) {
                            var127 = 5;
                        }
                        break;
                    case -319618326:
                        if (nghiepvu.equals("ngoaitru_toavattu")) {
                            var127 = 4;
                        }
                        break;
                    case 0:
                        if (nghiepvu.equals("")) {
                            var127 = 6;
                        }
                }

                switch(var127) {
                    case 0:
                        parameters.put("tuoiht", tuoiht);
                        loidantoathuoc = map_toathuoc.get("LOI_DAN_TOA_DONGY") == null ? "" : map_toathuoc.get("LOI_DAN_TOA_DONGY").toString();
                        loidantoathuoc_ngayhen = map_toathuoc.get("LOI_DAN_TOA_DONGY_H") == null ? "" : map_toathuoc.get("LOI_DAN_TOA_DONGY_H").toString();
                        parameters.put("pheptri", map_toathuoc.get("PHEP_TRI_DONG_Y") == null ? "" : map_toathuoc.get("PHEP_TRI_DONG_Y").toString());
                        parameters.put("baithuoc", map_toathuoc.get("BAI_THUOC_DONG_Y") == null ? "" : map_toathuoc.get("BAI_THUOC_DONG_Y").toString());
                        parameters.put("hinhanh", path_hinhanh);
                        map_duongdan01 = this.khambenhDAO.select_duongdanreport(dvtt, "89023");
                        if (map_duongdan01 != null && !map_duongdan01.isEmpty()) {
                            sochuyenpk = map_duongdan01.get("DUONG_DAN").toString();
                            reportFile = new File(request.getSession().getServletContext().getRealPath("" + sochuyenpk + ""));
                        } else {
                            reportFile = new File(request.getSession().getServletContext().getRealPath("/WEB-INF/pages/angiang/AGG_rp_kb_toathuoc_dongy.jasper"));
                        }
                        break;
                    case 1:
                        parameters.put("hinhanh", path_hinhanh);
                        loidantoathuoc = map_toathuoc.get("LOI_DAN_TOA_DICHVU_NGOAITRU") == null ? "" : map_toathuoc.get("LOI_DAN_TOA_DICHVU_NGOAITRU").toString();
                        loidantoathuoc_ngayhen = map_toathuoc.get("LOI_DAN_TOA_DICHVU_NGOAITRU_H") == null ? "" : map_toathuoc.get("LOI_DAN_TOA_DICHVU_NGOAITRU_H").toString();
                        reportFile = new File(request.getSession().getServletContext().getRealPath("/WEB-INF/pages/Khambenh/kb_toathuoc_caibe.jasper"));
                        break;
                    case 2:
                        parameters.put("hinhanh", path_hinhanh);
                        loidantoathuoc = map_toathuoc.get("LOI_DAN_TOA_THUOC_MIENPHI") == null ? "" : map_toathuoc.get("LOI_DAN_TOA_THUOC_MIENPHI").toString();
                        loidantoathuoc_ngayhen = map_toathuoc.get("LOI_DAN_TOA_THUOC_MIENPHI_H") == null ? "" : map_toathuoc.get("LOI_DAN_TOA_THUOC_MIENPHI_H").toString();
                        reportFile = new File(request.getSession().getServletContext().getRealPath("/WEB-INF/pages/Khambenh/kb_toathuoc_caibe.jasper"));
                        break;
                    case 3:
                        parameters.put("hinhanh", path_hinhanh);
                        loidantoathuoc = map_toathuoc.get("LOI_DAN_TOA_THUOC_MUATAIQUAYBV") == null ? "" : map_toathuoc.get("LOI_DAN_TOA_THUOC_MUATAIQUAYBV").toString();
                        loidantoathuoc_ngayhen = map_toathuoc.get("LOI_DAN_TOA_THUOC_MUATAIQUAY_H") == null ? "" : map_toathuoc.get("LOI_DAN_TOA_THUOC_MUATAIQUAY_H").toString();
                        reportFile = new File(request.getSession().getServletContext().getRealPath("/WEB-INF/pages/Khambenh/kb_toathuoc_caibe.jasper"));
                        break;
                    case 4:
                        parameters.put("hinhanh", path_hinhanh);
                        loidantoathuoc = map_toathuoc.get("LOI_DAN_TOA_VATTU_BHYT") == null ? "" : map_toathuoc.get("LOI_DAN_TOA_VATTU_BHYT").toString();
                        loidantoathuoc_ngayhen = map_toathuoc.get("LOI_DAN_TOA_VATTU_BHYT_H") == null ? "" : map_toathuoc.get("LOI_DAN_TOA_VATTU_BHYT_H").toString();
                        reportFile = new File(request.getSession().getServletContext().getRealPath("/WEB-INF/pages/Khambenh/kb_toathuoc_caibe.jasper"));
                        break;
                    case 5:
                        parameters.put("hinhanh", path_hinhanh);
                        loidantoathuoc = map_toathuoc.get("LOI_DAN_TOA_THUOC") == null ? "" : map_toathuoc.get("LOI_DAN_TOA_THUOC").toString();
                        loidantoathuoc_ngayhen = map_toathuoc.get("LOI_DAN_TOA_THUOC_H") == null ? "" : map_toathuoc.get("LOI_DAN_TOA_THUOC_H").toString();
                        reportFile = new File(request.getSession().getServletContext().getRealPath("/WEB-INF/pages/Khambenh/kb_toathuoc_caibe.jasper"));
                        break;
                    case 6:
                        loidantoathuoc = map_toathuoc.get("LOI_DAN_TOA_THUOC") == null ? "" : map_toathuoc.get("LOI_DAN_TOA_THUOC").toString();
                        loidantoathuoc_ngayhen = map_toathuoc.get("LOI_DAN_TOA_THUOC_H") == null ? "" : map_toathuoc.get("LOI_DAN_TOA_THUOC_H").toString();
                        sochuyenpk = KGGUtil.findFileReport(this.khambenhDAO, dvtt, "91027", "/WEB-INF/pages/Khambenh/kb_toathuoc_tonghop.jasper");
                        reportFile = new File(request.getSession().getServletContext().getRealPath(sochuyenpk));
                }
            } else if (tsht.mautoathuoc.startsWith("TT05")) {
                if (!tsht.matinh.equals("74")) {
                    parameters.put("tenbenhvien", tsht.tenbenhvien.toUpperCase());
                }

                parameters.put("tuoiht", tuoiht);
                if (nghiepvu.equals("ngoaitru_toadongy")) {
                    loidantoathuoc = map_toathuoc.get("LOI_DAN_TOA_DONGY") == null ? "" : map_toathuoc.get("LOI_DAN_TOA_DONGY").toString();
                    loidantoathuoc_ngayhen = map_toathuoc.get("LOI_DAN_TOA_DONGY_H") == null ? "" : map_toathuoc.get("LOI_DAN_TOA_DONGY_H").toString();
                    parameters.put("pheptri", map_toathuoc.get("PHEP_TRI_DONG_Y") == null ? "" : map_toathuoc.get("PHEP_TRI_DONG_Y").toString());
                    parameters.put("baithuoc", map_toathuoc.get("BAI_THUOC_DONG_Y") == null ? "" : map_toathuoc.get("BAI_THUOC_DONG_Y").toString());
                    map_duongdan01 = this.khambenhDAO.select_duongdanreport(dvtt, "89019");
                    if (map_duongdan01 != null && !map_duongdan01.isEmpty()) {
                        parameters.put("ngaythangrathuoc", ngaylapbangke + "/" + thanglapbangke + "/" + namlapbangke);
                        parameters.put("gioitinh", Boolean.parseBoolean(map.get("GIOI_TINH").toString()) ? "Nam" : "Nữ");
                        duong_dan01 = map_duongdan01.get("DUONG_DAN").toString();
                        reportFile = new File(request.getSession().getServletContext().getRealPath("" + duong_dan01 + ""));
                    } else {
                        reportFile = new File(request.getSession().getServletContext().getRealPath("/WEB-INF/pages/angiang/AGG_rp_kb_toathuoc_dongy.jasper"));
                    }
                } else if (nghiepvu.equals("ngoaitru_toadichvu")) {
                    loidantoathuoc = map_toathuoc.get("LOI_DAN_TOA_DICHVU_NGOAITRU") == null ? "" : map_toathuoc.get("LOI_DAN_TOA_DICHVU_NGOAITRU").toString();
                    loidantoathuoc_ngayhen = map_toathuoc.get("LOI_DAN_TOA_DICHVU_NGOAITRU_H") == null ? "" : map_toathuoc.get("LOI_DAN_TOA_DICHVU_NGOAITRU_H").toString();
                    map_duongdan01 = this.khambenhDAO.select_duongdanreport(dvtt, "89049");
                    if (map_duongdan01 != null && !map_duongdan01.isEmpty()) {
                        parameters.put("ngaythangrathuoc", ngaylapbangke + "/" + thanglapbangke + "/" + namlapbangke);
                        parameters.put("gioitinh", Boolean.parseBoolean(map.get("GIOI_TINH").toString()) ? "Nam" : "Nữ");
                        duong_dan01 = map_duongdan01.get("DUONG_DAN").toString();
                        reportFile = new File(request.getSession().getServletContext().getRealPath("" + duong_dan01 + ""));
                    } else {
                        reportFile = new File(request.getSession().getServletContext().getRealPath("/WEB-INF/pages/Khambenh/rp_donthuoc_tt05.jasper"));
                    }
                } else if (!nghiepvu.equals("ngoaitru_toamienphi") && !nghiepvu.equals("ngoaitru_toavattumienphi")) {
                    if (nghiepvu.equals("ngoaitru_toaquaybanthuocbv")) {
                        loidantoathuoc = map_toathuoc.get("LOI_DAN_TOA_THUOC_MUATAIQUAYBV") == null ? "" : map_toathuoc.get("LOI_DAN_TOA_THUOC_MUATAIQUAYBV").toString();
                        loidantoathuoc_ngayhen = map_toathuoc.get("LOI_DAN_TOA_THUOC_MUATAIQUAY_H") == null ? "" : map_toathuoc.get("LOI_DAN_TOA_THUOC_MUATAIQUAY_H").toString();
                        map_duongdan01 = this.khambenhDAO.select_duongdanreport(dvtt, "89049");
                        if (map_duongdan01 != null && !map_duongdan01.isEmpty()) {
                            parameters.put("ngaythangrathuoc", ngaylapbangke + "/" + thanglapbangke + "/" + namlapbangke);
                            parameters.put("gioitinh", Boolean.parseBoolean(map.get("GIOI_TINH").toString()) ? "Nam" : "Nữ");
                            duong_dan01 = map_duongdan01.get("DUONG_DAN").toString();
                            reportFile = new File(request.getSession().getServletContext().getRealPath("" + duong_dan01 + ""));
                        } else {
                            reportFile = new File(request.getSession().getServletContext().getRealPath("/WEB-INF/pages/Khambenh/rp_donthuoc_tt05.jasper"));
                        }
                    } else if (nghiepvu.equals("ngoaitru_toavattu")) {
                        loidantoathuoc = map_toathuoc.get("LOI_DAN_TOA_VATTU_BHYT") == null ? "" : map_toathuoc.get("LOI_DAN_TOA_VATTU_BHYT").toString();
                        loidantoathuoc_ngayhen = map_toathuoc.get("LOI_DAN_TOA_VATTU_BHYT_H") == null ? "" : map_toathuoc.get("LOI_DAN_TOA_VATTU_BHYT_H").toString();
                        map_duongdan01 = this.khambenhDAO.select_duongdanreport(dvtt, "89049");
                        if (map_duongdan01 != null && !map_duongdan01.isEmpty()) {
                            parameters.put("ngaythangrathuoc", ngaylapbangke + "/" + thanglapbangke + "/" + namlapbangke);
                            parameters.put("gioitinh", Boolean.parseBoolean(map.get("GIOI_TINH").toString()) ? "Nam" : "Nữ");
                            duong_dan01 = map_duongdan01.get("DUONG_DAN").toString();
                            reportFile = new File(request.getSession().getServletContext().getRealPath("" + duong_dan01 + ""));
                        } else {
                            reportFile = new File(request.getSession().getServletContext().getRealPath("/WEB-INF/pages/Khambenh/rp_donthuoc_tt05.jasper"));
                        }
                    } else if (nghiepvu.equals("ngoaitru_toathuoc")) {
                        loidantoathuoc = map_toathuoc.get("LOI_DAN_TOA_THUOC") == null ? "" : map_toathuoc.get("LOI_DAN_TOA_THUOC").toString();
                        loidantoathuoc_ngayhen = map_toathuoc.get("LOI_DAN_TOA_THUOC_H") == null ? "" : map_toathuoc.get("LOI_DAN_TOA_THUOC_H").toString();
                        map_duongdan01 = this.khambenhDAO.select_duongdanreport(dvtt, "89049");
                        if (map_duongdan01 != null && !map_duongdan01.isEmpty()) {
                            parameters.put("ngaythangrathuoc", ngaylapbangke + "/" + thanglapbangke + "/" + namlapbangke);
                            parameters.put("gioitinh", Boolean.parseBoolean(map.get("GIOI_TINH").toString()) ? "Nam" : "Nữ");
                            duong_dan01 = map_duongdan01.get("DUONG_DAN").toString();
                            reportFile = new File(request.getSession().getServletContext().getRealPath("" + duong_dan01 + ""));
                        } else {
                            reportFile = new File(request.getSession().getServletContext().getRealPath("/WEB-INF/pages/Khambenh/rp_donthuoc_tt05.jasper"));
                        }
                    } else if (nghiepvu.equals("toathuoc_nghien")) {
                        loidantoathuoc = map_toathuoc.get("LOI_DAN_TOA_THUOC") == null ? "" : map_toathuoc.get("LOI_DAN_TOA_THUOC").toString();
                        loidantoathuoc_ngayhen = map_toathuoc.get("LOI_DAN_TOA_THUOC_H") == null ? "" : map_toathuoc.get("LOI_DAN_TOA_THUOC_H").toString();
                        map_duongdan01 = this.khambenhDAO.select_duongdanreport(dvtt, "89050");
                        if (map_duongdan01 != null && !map_duongdan01.isEmpty()) {
                            parameters.put("ngaythangrathuoc", ngaylapbangke + "/" + thanglapbangke + "/" + namlapbangke);
                            parameters.put("gioitinh", Boolean.parseBoolean(map.get("GIOI_TINH").toString()) ? "Nam" : "Nữ");
                            duong_dan01 = map_duongdan01.get("DUONG_DAN").toString();
                            reportFile = new File(request.getSession().getServletContext().getRealPath("" + duong_dan01 + ""));
                        } else {
                            reportFile = new File(request.getSession().getServletContext().getRealPath("/WEB-INF/pages/Khambenh/kb_toathuoc_caibe_nghien.jasper"));
                        }
                    } else if (nghiepvu.equals("")) {
                        loidantoathuoc = map_toathuoc.get("LOI_DAN_TOA_THUOC") == null ? "" : map_toathuoc.get("LOI_DAN_TOA_THUOC").toString();
                        loidantoathuoc_ngayhen = map_toathuoc.get("LOI_DAN_TOA_THUOC_H") == null ? "" : map_toathuoc.get("LOI_DAN_TOA_THUOC_H").toString();
                        path = KGGUtil.findFileReport(this.khambenhDAO, dvtt, "91027", "/WEB-INF/pages/Khambenh/kb_toathuoc_tonghop.jasper");
                        reportFile = new File(request.getSession().getServletContext().getRealPath(path));
                    }
                } else {
                    map_duongdan01 = this.khambenhDAO.select_duongdanreport(dvtt, "89049");
                    if (map_duongdan01 != null && !map_duongdan01.isEmpty()) {
                        parameters.put("ngaythangrathuoc", ngaylapbangke + "/" + thanglapbangke + "/" + namlapbangke);
                        parameters.put("gioitinh", Boolean.parseBoolean(map.get("GIOI_TINH").toString()) ? "Nam" : "Nữ");
                        duong_dan01 = map_duongdan01.get("DUONG_DAN").toString();
                        reportFile = new File(request.getSession().getServletContext().getRealPath("" + duong_dan01 + ""));
                    } else {
                        reportFile = new File(request.getSession().getServletContext().getRealPath("/WEB-INF/pages/Khambenh/rp_donthuoc_tt05.jasper"));
                    }
                }
            }
        } else {
            var127 = -1;
            switch(nghiepvu.hashCode()) {
                case -1826687895:
                    if (nghiepvu.equals("ngoaitru_toadichvu")) {
                        var127 = 1;
                    }
                    break;
                case -1393131636:
                    if (nghiepvu.equals("ngoaitru_toamienphi")) {
                        var127 = 2;
                    }
                    break;
                case -1051988714:
                    if (nghiepvu.equals("ngoaitru_toaquaybanthuocbv")) {
                        var127 = 3;
                    }
                    break;
                case -335830795:
                    if (nghiepvu.equals("ngoaitru_toadongy")) {
                        var127 = 0;
                    }
                    break;
                case -321256043:
                    if (nghiepvu.equals("ngoaitru_toathuoc")) {
                        var127 = 5;
                    }
                    break;
                case -319618326:
                    if (nghiepvu.equals("ngoaitru_toavattu")) {
                        var127 = 4;
                    }
                    break;
                case 0:
                    if (nghiepvu.equals("")) {
                        var127 = 6;
                    }
            }

            Map map_duongdan;
            switch(var127) {
                case 0:
                    parameters.put("tuoiht", tuoiht);
                    loidantoathuoc = map_toathuoc.get("LOI_DAN_TOA_DONGY") == null ? "" : map_toathuoc.get("LOI_DAN_TOA_DONGY").toString();
                    loidantoathuoc_ngayhen = map_toathuoc.get("LOI_DAN_TOA_DONGY_H") == null ? "" : map_toathuoc.get("LOI_DAN_TOA_DONGY_H").toString();
                    parameters.put("pheptri", map_toathuoc.get("PHEP_TRI_DONG_Y") == null ? "" : map_toathuoc.get("PHEP_TRI_DONG_Y").toString());
                    parameters.put("baithuoc", map_toathuoc.get("BAI_THUOC_DONG_Y") == null ? "" : map_toathuoc.get("BAI_THUOC_DONG_Y").toString());
                    map_duongdan01 = this.khambenhDAO.select_duongdanreport(dvtt, "89022");
                    if (map_duongdan01 != null && !map_duongdan01.isEmpty()) {
                        sochuyenpk = map_duongdan01.get("DUONG_DAN").toString();
                        reportFile = new File(request.getSession().getServletContext().getRealPath("" + sochuyenpk + ""));
                    } else if (toathuoc_maumoi.equals("1")) {
                        reportFile = new File(request.getSession().getServletContext().getRealPath("/WEB-INF/pages/haiphong/kb_toathuoc.jasper"));
                    } else {
                        reportFile = new File(request.getSession().getServletContext().getRealPath("/WEB-INF/pages/angiang/AGG_rp_kb_toathuoc_dongy.jasper"));
                    }
                    break;
                case 1:
                    loidantoathuoc = map_toathuoc.get("LOI_DAN_TOA_DICHVU_NGOAITRU") == null ? "" : map_toathuoc.get("LOI_DAN_TOA_DICHVU_NGOAITRU").toString();
                    loidantoathuoc_ngayhen = map_toathuoc.get("LOI_DAN_TOA_DICHVU_NGOAITRU_H") == null ? "" : map_toathuoc.get("LOI_DAN_TOA_DICHVU_NGOAITRU_H").toString();
                    if (dvtt.equals("27002")) {
                        reportFile = new File(request.getSession().getServletContext().getRealPath("/WEB-INF/pages/bacninh/bnh_toathuoc.jasper"));
                    } else if (toathuoc_maumoi.equals("1")) {
                        reportFile = new File(request.getSession().getServletContext().getRealPath("/WEB-INF/pages/haiphong/kb_toathuoc.jasper"));
                    } else {
                        parameters.put("tuoiht", tuoiht);
                        if (dvtt.substring(0, 2).equals("82")) {
                            if (dvtt.equals("82204")) {
                                reportFile = new File(request.getSession().getServletContext().getRealPath("/WEB-INF/pages/Khambenh/kb_toathuoc_tg_dalieu.jasper"));
                            } else if (dvtt.equals("82999")) {
                                reportFile = new File(request.getSession().getServletContext().getRealPath("/WEB-INF/pages/Khambenh/kb_toathuoc_pksannhi.jasper"));
                            } else {
                                reportFile = new File(request.getSession().getServletContext().getRealPath("/WEB-INF/pages/Khambenh/kb_toathuoc_tg.jasper"));
                            }
                        } else if (dvtt.substring(0, 2).equals("89")) {
                            reportFile = new File(request.getSession().getServletContext().getRealPath("/WEB-INF/pages/Khambenh/AGG_kb_toathuoc.jasper"));
                        } else {
                            reportFile = new File(request.getSession().getServletContext().getRealPath("/WEB-INF/pages/Khambenh/kb_toathuoc.jasper"));
                        }
                    }
                    break;
                case 2:
                    loidantoathuoc = map_toathuoc.get("LOI_DAN_TOA_THUOC_MIENPHI") == null ? "" : map_toathuoc.get("LOI_DAN_TOA_THUOC_MIENPHI").toString();
                    loidantoathuoc_ngayhen = map_toathuoc.get("LOI_DAN_TOA_THUOC_MIENPHI_H") == null ? "" : map_toathuoc.get("LOI_DAN_TOA_THUOC_MIENPHI_H").toString();
                    if (dvtt.equals("27002")) {
                        reportFile = new File(request.getSession().getServletContext().getRealPath("/WEB-INF/pages/bacninh/bnh_toathuoc.jasper"));
                    } else if (toathuoc_maumoi.equals("1")) {
                        reportFile = new File(request.getSession().getServletContext().getRealPath("/WEB-INF/pages/haiphong/kb_toathuoc.jasper"));
                    } else {
                        parameters.put("tuoiht", tuoiht);
                        map_duongdan = this.khambenhDAO.select_duongdanreport(dvtt, "*********");
                        if (map_duongdan != null && !map_duongdan.isEmpty()) {
                            path = map_duongdan.get("DUONG_DAN").toString();
                            reportFile = new File(request.getSession().getServletContext().getRealPath(path));
                        } else if (dvtt.substring(0, 2).equals("82")) {
                            if (dvtt.equals("82204")) {
                                reportFile = new File(request.getSession().getServletContext().getRealPath("/WEB-INF/pages/Khambenh/kb_toathuoc_tg_dalieu.jasper"));
                            } else if (dvtt.equals("82999")) {
                                reportFile = new File(request.getSession().getServletContext().getRealPath("/WEB-INF/pages/Khambenh/kb_toathuoc_pksannhi.jasper"));
                            } else {
                                reportFile = new File(request.getSession().getServletContext().getRealPath("/WEB-INF/pages/Khambenh/kb_toathuoc_tg.jasper"));
                            }
                        } else {
                            reportFile = new File(request.getSession().getServletContext().getRealPath("/WEB-INF/pages/Khambenh/kb_toathuoc.jasper"));
                        }
                    }
                    break;
                case 3:
                    loidantoathuoc = map_toathuoc.get("LOI_DAN_TOA_THUOC_MUATAIQUAYBV") == null ? "" : map_toathuoc.get("LOI_DAN_TOA_THUOC_MUATAIQUAYBV").toString();
                    loidantoathuoc_ngayhen = map_toathuoc.get("LOI_DAN_TOA_THUOC_MUATAIQUAY_H") == null ? "" : map_toathuoc.get("LOI_DAN_TOA_THUOC_MUATAIQUAY_H").toString();
                    if (dvtt.equals("27002")) {
                        reportFile = new File(request.getSession().getServletContext().getRealPath("/WEB-INF/pages/bacninh/bnh_toathuoc.jasper"));
                    } else {
                        parameters.put("tuoiht", tuoiht);
                        if (dvtt.substring(0, 2).equals("82")) {
                            if (dvtt.equals("82204")) {
                                reportFile = new File(request.getSession().getServletContext().getRealPath("/WEB-INF/pages/Khambenh/kb_toathuoc_tg_dalieu.jasper"));
                            } else if (dvtt.equals("82999")) {
                                reportFile = new File(request.getSession().getServletContext().getRealPath("/WEB-INF/pages/Khambenh/kb_toathuoc_pksannhi.jasper"));
                            } else {
                                reportFile = new File(request.getSession().getServletContext().getRealPath("/WEB-INF/pages/Khambenh/kb_toathuoc_tg.jasper"));
                            }
                        } else if (toathuoc_maumoi.equals("1")) {
                            reportFile = new File(request.getSession().getServletContext().getRealPath("/WEB-INF/pages/haiphong/kb_toathuoc.jasper"));
                        } else if (dvtt.substring(0, 2).equals("89")) {
                            reportFile = new File(request.getSession().getServletContext().getRealPath("/WEB-INF/pages/Khambenh/AGG_kb_toathuoc.jasper"));
                        } else {
                            reportFile = new File(request.getSession().getServletContext().getRealPath("/WEB-INF/pages/Khambenh/kb_toathuoc.jasper"));
                        }
                    }
                    break;
                case 4:
                    loidantoathuoc = map_toathuoc.get("LOI_DAN_TOA_VATTU_BHYT") == null ? "" : map_toathuoc.get("LOI_DAN_TOA_VATTU_BHYT").toString();
                    loidantoathuoc_ngayhen = map_toathuoc.get("LOI_DAN_TOA_VATTU_BHYT_H") == null ? "" : map_toathuoc.get("LOI_DAN_TOA_VATTU_BHYT_H").toString();
                    if (dvtt.equals("27002")) {
                        reportFile = new File(request.getSession().getServletContext().getRealPath("/WEB-INF/pages/bacninh/bnh_toathuoc.jasper"));
                    } else {
                        parameters.put("tuoiht", tuoiht);
                        if (dvtt.substring(0, 2).equals("82")) {
                            if (dvtt.equals("82204")) {
                                reportFile = new File(request.getSession().getServletContext().getRealPath("/WEB-INF/pages/Khambenh/kb_toathuoc_tg_dalieu.jasper"));
                            } else if (dvtt.equals("82999")) {
                                reportFile = new File(request.getSession().getServletContext().getRealPath("/WEB-INF/pages/Khambenh/kb_toathuoc_pksannhi.jasper"));
                            } else {
                                reportFile = new File(request.getSession().getServletContext().getRealPath("/WEB-INF/pages/Khambenh/kb_toathuoc_tg.jasper"));
                            }
                        } else if (toathuoc_maumoi.equals("1")) {
                            reportFile = new File(request.getSession().getServletContext().getRealPath("/WEB-INF/pages/haiphong/kb_toathuoc.jasper"));
                        } else if (dvtt.substring(0, 2).equals("89")) {
                            reportFile = new File(request.getSession().getServletContext().getRealPath("/WEB-INF/pages/Khambenh/AGG_kb_toathuoc.jasper"));
                        } else {
                            reportFile = new File(request.getSession().getServletContext().getRealPath("/WEB-INF/pages/Khambenh/kb_toathuoc.jasper"));
                        }
                    }
                    break;
                case 5:
                    loidantoathuoc = map_toathuoc.get("LOI_DAN_TOA_THUOC") == null ? "" : map_toathuoc.get("LOI_DAN_TOA_THUOC").toString();
                    loidantoathuoc_ngayhen = map_toathuoc.get("LOI_DAN_TOA_THUOC_H") == null ? "" : map_toathuoc.get("LOI_DAN_TOA_THUOC_H").toString();
                    map_duongdan = this.khambenhDAO.select_duongdanreport(dvtt, "83001");
                    if (map_duongdan != null && !map_duongdan.isEmpty()) {
                        path = map_duongdan.get("DUONG_DAN").toString();
                        reportFile = new File(request.getSession().getServletContext().getRealPath("" + path + ""));
                    } else if (dvtt.equals("27002")) {
                        reportFile = new File(request.getSession().getServletContext().getRealPath("/WEB-INF/pages/bacninh/bnh_toathuoc.jasper"));
                    } else {
                        parameters.put("tuoiht", tuoiht);
                        if (dvtt.substring(0, 2).equals("82")) {
                            if (dvtt.equals("82204")) {
                                reportFile = new File(request.getSession().getServletContext().getRealPath("/WEB-INF/pages/Khambenh/kb_toathuoc_tg_dalieu.jasper"));
                            } else if (dvtt.equals("82999")) {
                                reportFile = new File(request.getSession().getServletContext().getRealPath("/WEB-INF/pages/Khambenh/kb_toathuoc_pksannhi.jasper"));
                            } else {
                                reportFile = new File(request.getSession().getServletContext().getRealPath("/WEB-INF/pages/Khambenh/kb_toathuoc_tg.jasper"));
                            }
                        } else if (toathuoc_maumoi.equals("1")) {
                            reportFile = new File(request.getSession().getServletContext().getRealPath("/WEB-INF/pages/haiphong/kb_toathuoc.jasper"));
                        } else if (tsht.matinh.equals("77")) {
                            reportFile = new File(request.getSession().getServletContext().getRealPath("/WEB-INF/pages/vungtau/VTU_kb_toathuoc.jasper"));
                        } else if (dvtt.substring(0, 2).equals("89")) {
                            reportFile = new File(request.getSession().getServletContext().getRealPath("/WEB-INF/pages/Khambenh/AGG_kb_toathuoc.jasper"));
                        } else {
                            reportFile = new File(request.getSession().getServletContext().getRealPath("/WEB-INF/pages/Khambenh/kb_toathuoc.jasper"));
                        }
                    }
                    break;
                case 6:
                    loidantoathuoc = map_toathuoc.get("LOI_DAN_TOA_THUOC") == null ? "" : map_toathuoc.get("LOI_DAN_TOA_THUOC").toString();
                    loidantoathuoc_ngayhen = map_toathuoc.get("LOI_DAN_TOA_THUOC_H") == null ? "" : map_toathuoc.get("LOI_DAN_TOA_THUOC_H").toString();
                    if (dvtt.substring(0, 2).equals("82")) {
                        if (dvtt.equals("82204")) {
                            reportFile = new File(request.getSession().getServletContext().getRealPath("/WEB-INF/pages/Khambenh/kb_toathuoc_tonghop_tg_dalieu.jasper"));
                        } else {
                            path = KGGUtil.findFileReport(this.khambenhDAO, dvtt, "91027", "/WEB-INF/pages/Khambenh/kb_toathuoc_tonghop_tg.jasper");
                            reportFile = new File(request.getSession().getServletContext().getRealPath(path));
                        }
                    } else if (toathuoc_maumoi.equals("1")) {
                        reportFile = new File(request.getSession().getServletContext().getRealPath("/WEB-INF/pages/haiphong/kb_toathuoc_tonghop.jasper"));
                    } else if (tsht.matinh.equals("77")) {
                        reportFile = new File(request.getSession().getServletContext().getRealPath("/WEB-INF/pages/vungtau/VTU_kb_toathuoc_tonghop.jasper"));
                    } else if (dvtt.substring(0, 2).equals("89")) {
                        reportFile = new File(request.getSession().getServletContext().getRealPath("/WEB-INF/pages/Khambenh/AGG_kb_toathuoc_tonghop.jasper"));
                    } else {
                        path = KGGUtil.findFileReport(this.khambenhDAO, dvtt, "91027", "/WEB-INF/pages/Khambenh/kb_toathuoc_tonghop.jasper");
                        reportFile = new File(request.getSession().getServletContext().getRealPath(path));
                    }
            }
        }

        parameters.put("loidantoathuoc", loidantoathuoc);
        parameters.put("loidantoathuoc_ngayhen", loidantoathuoc_ngayhen);
        parameters.put("noidangky", noidangky);
        map_duongdan01 = this.khambenhDAO.sochuyentuyen_select(makb.replaceFirst("kb_", ""), dvtt);
        duong_dan01 = "";
        if (!map_duongdan01.isEmpty()) {
            String sochuyentyt = map_duongdan01.get("SO_CHUYEN_TUYEN_TYT").toString();
            sochuyenpk = map_duongdan01.get("SO_CHUYEN_TUYEN_PK").toString();
            path = map_duongdan01.get("SO_CHUYEN_TUYEN_TTYT").toString();
            if (!path.equals("")) {
                duong_dan01 = path;
            } else if (!sochuyenpk.equals("")) {
                duong_dan01 = sochuyenpk;
            } else if (!sochuyentyt.equals("")) {
                duong_dan01 = sochuyentyt;
            }
        }

        if (!duong_dan01.trim().equals("")) {
            duong_dan01 = "Số chuyển tuyến: " + duong_dan01;
        }

        parameters.put("sochuyentuyen", duong_dan01);

        JasperReport jasperReport;
        JasperPrint print;
        String uri = request.getSession().getServletContext().getRealPath("/WEB-INF/store_files/");
        File file = new File(uri + "/gct_" + dvtt + "_" + makb + (new Date()).getTime() + ".pdf");
        path = reportFile.getPath();
        jasperReport = (JasperReport) JRLoader.loadObject(reportFile);
        JRProperties.setProperty("net.sf.jasperreports.query.executer.factory.plsql", "com.jaspersoft.jrx.query.PlSqlQueryExecuterFactory");
        jasperReport.setProperty("net.sf.jasperreports.query.executer.factory.plsql", "com.jaspersoft.jrx.query.PlSqlQueryExecuterFactory");
        print = JasperFillManager.fillReport(path, parameters, DataSourceUtils.getConnection(this.dataSourceMNG));
        response.setContentType("application/pdf");
        response.addHeader("content-disposition", "inline; filename=" + file.getName());
        OutputStream out = response.getOutputStream();
        JasperExportManager.exportReportToPdfStream(print, out);
    }



    @RequestMapping(
            method = {RequestMethod.POST},
            produces = {"application/json"},
            path = {"/confirm-smartca"}
    )
    @ResponseBody
    public Map Confirm(@RequestBody String json) {
        Object data = new HashMap();

        try {
            ObjectMapper mapper = new ObjectMapper();
            data = (Map)mapper.readValue(json, Map.class);
        } catch (IOException var28) {
            var28.printStackTrace();
        }

        String tranId = String.valueOf(((Map)data).get("tranId"));
        String refTranId = String.valueOf(((Map)data).get("refTranId"));
        String status = String.valueOf(((Map)data).get("status"));

        String sql = "CALL HIS_MANAGER.SMARTCA_NOTIFY(?,?,?)";
        JdbcTemplate jdbcTemplate = new JdbcTemplate(this.dataSourceMNG);
        jdbcTemplate.update(sql, new Object[]{tranId, refTranId, status});


        Map result = new HashMap();
        result.put("responseCode", "00");
        result.put("description", "Thành công");
        if(status.equals("1")) {
            try {
                ChatMessage chatMessage = new ChatMessage();
                chatMessage.setType(ChatMessage.MessageType.CHAT);
                chatMessage.setSender("VNPT-SMARTCA");
                chatMessage.setContent(json);
                messagingTemplate.convertAndSendToUser("vnptsmartca","/notify", chatMessage);
            } catch (Exception ex) {

            }
            String sqlGrv = "call SMARTCA_GRV(?,?,?)#c,s,s,s";
            JdbcTemplate jdbcTemplate2 = new JdbcTemplate(this.dataSourceMNG);
            Map c = jdbcTemplate2.queryForMap(sqlGrv, new Object[]{tranId, refTranId, status});
            TranInfoRespContent tranInfoRespContent;
            if (!c.isEmpty() && c.get("GRV").toString().equals("1")) {
                String dvtt = c.get("DVTT").toString();
                String userId = c.get("USERID").toString();
                String userIdTruongkhoa = c.get("USERID_TRUONGKHOA").toString();
                int isLogin = this.loginSmartCAIFNOT(dvtt, userId, c.get("PASSWORD").toString());
                this.loginSmartCAIFNOT(dvtt, userIdTruongkhoa, c.get("PASSWORD_TRUONGKHOA").toString());
                if (isLogin == 1) {
                    TranInfoResponse tranInfoRes = this.smartcaGetInformationByTranId(tranId, dvtt, userIdTruongkhoa);

                    TranInfoRespContent respContent = tranInfoRes.getContent();
                    List<Map<String, Object>> hsm_account =  VienphiCMUDAO.cmu_getlist(new Object[]{dvtt, userId}, "HSM_GETACCOUNT(?,?)#c,s,s");
                    String options = c.get("OPTIONS").toString();
                    try {
                        CMUSmartCADetail smartCADetail = this.getSmartDetail(c.get("REFTRANID").toString(), dvtt, userId);
                        List<DocumentResp> documents = respContent.getDocuments();
                        options = options.replaceAll("CHUKY_IMAGE", hsm_account.get(0).get("CHUKY").toString());

                        SmartCACredential smartCACredential;
                        smartCACredential = cmuSmartCAService.getCredential(smartCADetail);
                        String[] content = smartCACredential.getContent();
                        String credentialId = content[0];
                        smartCADetail.setCredentialId(credentialId);
                        CMUFileSign fileSign = new CMUFileSign(options, c.get("REFTRANID").toString(), documents.get(0).getDataSigned());
                        List<CMUFileSign> fileSigns = new ArrayList<CMUFileSign>();
                        fileSigns.add(fileSign);
                        smartCADetail.setSignDatas(fileSigns);
                        this.sign(smartCADetail);

//                        tranInfoRespContent = this.(c.get("NAME_FILE").toString(), documents.get(0).getDataSigned(), smartCADetail, options);
//                        smartCADAO.smartcaStoreLog(dvtt, smartCADetail.getTranId(), tranInfoRespContent.getTranId());
                    } catch (Exception ex) {

                    }
                }

            }
        }

        return result;
    }

    public @ResponseBody
    @RequestMapping(value = "/cmu-smartca-get-information", method = RequestMethod.POST, produces = "application/json; charset=utf-8")
    Map smartcaGetInformation(@RequestBody String jsonParam, HttpSession session, HttpServletResponse httpServletResponse) throws UnknownHostException, UnsupportedEncodingException, InterruptedException {
        Map response = new HashMap();
        JsonParser jsonParser = new JsonParser();
        JsonObject paramObject = new JsonObject();
        paramObject = jsonParser.parse(jsonParam).getAsJsonObject();
        String tranId = paramObject.get("tranId").getAsString();
        String userId = paramObject.get("userId").getAsString();
        String dvtt = L2Utils.getDvtt(session);
        List<Map<String, Object>> smartca_account =  VienphiCMUDAO.cmu_getlist(new Object[]{dvtt, userId}, "SMARTCA_GETACCOUNT(?,?)#c,s,s");
        this.loginSmartCAIFNOT(dvtt, userId, smartca_account.get(0).get("PASSWORD").toString());
        TranInfoResponse tranInfoRes = this.smartcaGetInformationByTranId(tranId, dvtt, userId);
        response.put("DATA", tranInfoRes);
        return response;
    }

    TranInfoResponse smartcaGetInformationByTranId(String tranId, String dvtt, String userId) {
        CMUSmartCADetail smartCADetail = this.getSmartDetail(tranId, dvtt, userId);
        TranInfoResponse tranInfoRes = cmuSmartCAService.getTranInfo(smartCADetail, tranId);

        return tranInfoRes;
    }

    CMUSmartCADetail getSmartDetail(String tranId, String dvtt, String userId) {
        Map smartCAAuth = smartCADAO.smartcaLayThongTinCauHinh(dvtt, userId);
        String username = smartCAAuth.containsKey("USERNAME") ? smartCAAuth.get("USERNAME").toString() : "";
        Map smartcaAccessToken = smartCADAO.smartcaLayThongTinToken(dvtt, username);
        String smartcaToken = smartcaAccessToken.getOrDefault("ACCESS_TOKEN", "").toString();
        String smartcaUrl = smartCAAuth.getOrDefault("URL", "").toString();
        CMUSmartCADetail smartCADetail = new CMUSmartCADetail();
        smartCADetail.setUrl(smartcaUrl);
        smartCADetail.setAccessToken(smartcaToken);
        smartCADetail.setTranId(tranId);

        return smartCADetail;
    }

    int loginSmartCAIFNOT(String dvtt, String userId, String password) {
        Map smartCAAuth = smartCADAO.smartcaLayThongTinCauHinh(dvtt, userId);
        if (smartCAAuth == null) { // Tài khoản chưa cấu hình
            return 0;
        } else {
            String username = smartCAAuth.containsKey("USERNAME") ? smartCAAuth.get("USERNAME").toString() : "";
            Map smartcaAccessToken = smartCADAO.smartcaLayThongTinToken(dvtt, username);
            boolean login = SmartCAService.checkLoginSmartCA(smartcaAccessToken, smartCAAuth);
            if (login) {// Đã đăng nhập
                return 1;
            } else { // Chưa đăng nhập
                String smartcaUrl = smartCAAuth.getOrDefault("URL", "").toString();
                String clientId = smartCAAuth.getOrDefault("CLIENT_ID", "").toString();
                String clientSecret = smartCAAuth.getOrDefault("CLIENT_SECRET", "").toString();
                SmartCADetail smartCADetail = new SmartCADetail(username, password, smartcaUrl, clientId, clientSecret, "password");
                Account account = SmartCAService.authenticate(smartCADetail);
                if (account != null) {
                    String accessToken = account.getAccess_token();
                    long expires_in = account.getExpires_in();
                    String token_type = account.getToken_type();
                    String refresh_token = account.getRefresh_token();
                    String scope = account.getScope();
                    smartCADetail.setAccessToken(account.getAccess_token());
                    smartCADAO.smartcaLuuDangNhap(dvtt, accessToken, expires_in, token_type, refresh_token, scope, username, password);
                } else {
                    return 0; // does not exist authorization SmartCA
                }
            }
        }
        return 1;
    }


    @RequestMapping(value = "/cmu_getposition", method = RequestMethod.POST)
    @ResponseBody
    public String cmu_getposition(
            HttpServletResponse response,
            HttpServletRequest request,
            //String upfile,
            @RequestParam  Map parameters,
            HttpSession session)  throws IOException {
        String dvtt = session.getAttribute("Sess_DVTT").toString();
        String user = session.getAttribute("Sess_UserID").toString();
        String upfile = parameters.get("upfile").toString();
        Decoder decoder = Base64.getDecoder();
        byte[] decodedByte = decoder.decode(upfile.split(",")[1]);
        String path = session.getServletContext().getRealPath("/")+ "WEB-INF/pages/camau/reports/";
        File dir = new File(path);
        try{
            if (!dir.exists())
                //dir.mkdirs();
                return "{ERROR:2}";

            String filename = parameters.get("tenfile").toString()+parameters.get("mabenhnhan").toString()+dvtt+new SimpleDateFormat("yyMMddHHmmss").format(new Date())+".pdf";
            FileOutputStream fos = new FileOutputStream(dir.getAbsolutePath() + File.separator + filename);
            fos.write(decodedByte);
            fos.close();
            List<Map<String, Object>> position = PDFTextLocator.getCoordiantes(parameters.get("keyword").toString(), dir.getAbsolutePath() + File.separator + filename);
            ListIterator<Map<String, Object>>
                    iterator = position.listIterator();


            int i = 0;
            String mark_position = "";
            while (iterator.hasNext()) {
                Map<String, Object> dataMap = iterator.next();
                mark_position += dataMap.get("x").toString() + "," + dataMap.get("y").toString() + "," + dataMap.get("page").toString() + ";";

                i++;
            }
            File fileTemp = new File(dir.getAbsolutePath() + File.separator + filename);
            fileTemp.delete();
            return mark_position;
        }
        catch(Exception e){
            return "{ERROR:"+e.toString()+"}";

        }

    }

    @RequestMapping(value = "/cmu_combogrid_{store}", method = RequestMethod.GET, produces = "application/json; charset=utf-8")
    public @ResponseBody
    Map cmu_combogrid_store(
            @PathVariable("store") String store,
            @RequestParam(value = "url") String url,
            @RequestParam(value = "sidx") String sidx,
            @RequestParam(value = "page") String page,
            @RequestParam(value = "sord") String sord,
            @RequestParam(value = "rows") String rows,
            @RequestParam(value = "searchTerm") String searchTerm,
            @RequestParam(value = "token", defaultValue = "") String token
    ){
        //if(token.length() < 5 && (tienich.getMD5(store + "@CMU")).toUpperCase() != token){
        //    return new HashMap();
        //}
        searchTerm = "%" + searchTerm.toLowerCase() + "%";
        sidx =  sidx.equals("") ? "1" : sidx;
        int page_Int = Integer.parseInt(page);
        int limit = Integer.parseInt(rows);
        int start = limit * page_Int - limit;
        url += "```" + searchTerm + "```" + Integer.toString(start)  + "```" + Integer.toString(limit);
        String arr[] = url.split("```");
        Object[] obj = new Object[arr.length];
        String parameter = "";
        String typeofparameter = "c,";
        for(int i = 0; i < arr.length ; i++) {
            obj[i] = arr[i];
            parameter +="?";
            typeofparameter +="s";
            if(i != arr.length-1) {
                parameter +=",";
                typeofparameter +=",";
            }
        }
        store = store.toUpperCase()+ "(" + parameter + ")#"+typeofparameter;
        List<Map<String, Object>> rows_returns = VienphiCMUDAO.cmu_getlist(obj,store);
        if (!rows_returns.isEmpty()) {
            //Integer.parseInt(rows_returns.get(0)["xxx"]);//soluong all row
            Map<String, Object> first_row = rows_returns.get(0);
            int records_all = Integer.parseInt(first_row.get("TOTALS").toString());
            int total_pages = (records_all / limit) + 1;
            Map m = new HashMap();
            m.put("records", records_all);
            m.put("total", total_pages);
            m.put("rows", rows_returns);
            return m;
        }
        return new HashMap();
    }


    @RequestMapping(value = "/pharmacy_danh_sach_thuoc_ton_kho", method = RequestMethod.GET, produces = "application/json; charset=utf-8")
    public @ResponseBody
    Map pharmacyDanhSachThuocTonKho(@RequestParam(value = "sidx") String sidx,
                                    @RequestParam(value = "page") String page,
                                    @RequestParam(value = "sord") String sord,
                                    @RequestParam(value = "rows") String rows,
                                    @RequestParam(value = "searchTerm") String searchTerm, HttpSession session) {
        String dvtt = session.getAttribute("Sess_DVTT").toString();
        String manhanvien = session.getAttribute("Sess_UserID").toString();
        Map m = new HashMap();

        m.put("code", "0");
        m.put("message", "Từ khóa phải lớn hơn 3 ký tự");
        return m;
    }

    public Sign sign(CMUSmartCADetail smartCADetail) {
        String respContent;
        Gson gson = new Gson();
        CloseableHttpClient client = HttpClients.createDefault();
        String notifyUrl = "https://yte-camau.vnpthis.vn/web_his/confirm-smartca";
        String description = "VNPT-HIS";
        String refTranId = smartCADetail.getTranId();
        CMUSignRequest signRequest = new CMUSignRequest(smartCADetail.getSignDatas(), smartCADetail.getCredentialId(), notifyUrl, description, refTranId);
        String para = gson.toJson(signRequest);
        Sign sign;
        try {
            HttpPost request = new HttpPost(smartCADetail.getUrl() + "/csc/signature/sign");
            String oauth = "Bearer" + " " + smartCADetail.getAccessToken();
            request.addHeader("Authorization", oauth);
            request.setHeader(HttpHeaders.CONTENT_TYPE, "application/json; charset=utf-8");
            request.setHeader(HttpHeaders.ACCEPT_ENCODING, "UTF-8");
            StringEntity params = new StringEntity(para, "UTF-8");
            request.setEntity(params);

            CloseableHttpResponse response = client.execute(request);
            respContent = EntityUtils.toString(response.getEntity());
            System.out.println(respContent);
            int code = response.getStatusLine().getStatusCode();
            if (200 != code) {
                System.out.println(respContent);
                return null;
            }
            sign = gson.fromJson(respContent, Sign.class);
            return sign;
        } catch (Exception ex) {
            ex.printStackTrace();
        } finally {
            try {
                client.close();
            } catch (IOException e) {
                e.printStackTrace();
            }
        }

        return null;
    }

    @RequestMapping(value = "/testtinnhan",
            method = RequestMethod.GET, produces = "application/json; charset=utf-8")
    public @ResponseBody
    String dotestSMS( HttpSession session, @RequestParam(value = "sms") String noidung) {
        String dvtt = session.getAttribute("Sess_DVTT").toString();
        String rp = "-1";

        String v_requestid = thamsodonviDAO.laythamso_donvi_motthamso(dvtt, "960488");
        String  v_ContractID = thamsodonviDAO.laythamso_donvi_motthamso(dvtt, "960489");
        String  v_LabelID = thamsodonviDAO.laythamso_donvi_motthamso(dvtt, "960490");
        String  v_TemplateID= thamsodonviDAO.laythamso_donvi_motthamso(dvtt, "960491");
        String  v_IstelCosub = thamsodonviDAO.laythamso_donvi_motthamso(dvtt, "960492");
        String  v_ContractTypeID= thamsodonviDAO.laythamso_donvi_motthamso(dvtt, "960493");
        String v_AgentID = thamsodonviDAO.laythamso_donvi_motthamso(dvtt, "960494");
        String v_APIUser = thamsodonviDAO.laythamso_donvi_motthamso(dvtt, "960495");
        String v_APIPass = thamsodonviDAO.laythamso_donvi_motthamso(dvtt, "960496");
        String v_Username = thamsodonviDAO.laythamso_donvi_motthamso(dvtt, "960497");

        String RequestId = v_requestid;
        String LabelID = v_LabelID;
        String ContractTypeID = v_ContractTypeID;
        String ContractID = v_ContractID;
        String TemplateID =v_TemplateID;

        String ScheduleTime = "";

        String IstelCosub = v_IstelCosub;
        String AgentID = v_AgentID;
        String APIUser = v_APIUser;
        String APIPass = v_APIPass;
        String Username = v_Username;

        SendSMS sms = new SendSMS();
        String[] Params = {
                noidung,
                "1"};
        rp = sms.sendByList(RequestId, LabelID, TemplateID, IstelCosub,
                ContractTypeID, ScheduleTime, "84888649339", AgentID, APIUser,
                APIPass, Username, ContractID, Params);
        String store = VienphiCMUDAO.cmu_getstring(new Object[]{dvtt,0,0,"84888649339",noidung,rp}, "CMU_LOGNT_HENTAIKHAM(?,?,?,?,?,?)#s,s,s,s,s,s,s");
        rp=rp.replaceAll("</RPLY>", "<SMS>"+noidung+"</SMS></RPLY>");
        return "";



    }

    @RequestMapping(value = "/get-encode-magiayto", method = RequestMethod.POST)
    @ResponseBody
    public String get_encode_magiayto(
            HttpServletResponse response,
            HttpServletRequest request,
            @RequestParam(value = "magiay") String magiay,
            @RequestParam  Map parameters,
            HttpSession session)  throws Exception {
        String enocde = CMUApiService.encodeTo13(magiay);
        return enocde ;
    }
}
