var thongTinPhieuChucNangSongDangChon = {};
var thongTinPhieuChucNangSongTruocChinhSua = {};
var thongTinPhieuChucNangSongSauChinhSua = {};

var keyLogModule = {
    //Thông tin cơ bản
    THONGTINCOBAN: {
        ICD: 'ICD',
        CHAN_DOAN: 'Chẩn đoán',
        CAN_NANG_VAO_VIEN: 'Cân nặng vào viện',
        CHIEU_CAO: 'Chiều cao',
        BMI: 'BMI'
    },
    //Sàng lọc nguy cơ dinh giưỡng
    SANGLOCNGUYCODINHDUONG: {
        SANG_LOC_BMI: 'BMI sàng lọc',
        SANG_LOC_SUT_CAN: 'Sụt cân trong 1 tháng qua',
        SANG_LOC_LUONG_AN: 'Lượng ăn giảm trong tuần qua',
        SANG_LOC_BENH_NANG: 'Bệnh nặng hạn chế đi lại',
    },
    //Đánh giá tình trạng dinh dưỡng
    DANHGIATINHTRANGDINHDUONG: {
        DANH_GIA_BMI: 'Đ<PERSON>h giá BMI',
        DANH_GIA_SUT_CAN: 'Đánh giá sụt cân',
        DANH_GIA_LUONG_AN: 'Đánh giá lượng ăn',
        DANH_GIA_BENH_LY: 'Đánh giá bệnh lý'
    },
    //Kế hoạch can thiệp
    KEHOACHCANTHIEP: {
        TAI_DANH_GIA: 'Tái đánh giá',
        BAC_SI_DIEU_TRI: 'Bác sĩ điều trị'
    }
};
var arrOption = {
    coKhong: [
        {
            key: '0',
            value: 'Không'
        },
        {
            key: '1',
            value: 'Có'
        }
    ],
    BMI: [
        {
            key: '0',
            value: '0 điểm(≥ 20,5 kg/m³)'
        },
        {
            key: '1',
            value: '1 điểm(18.5 - 20.5 kg/m²)'
        },
        {
            key: '2',
            value: '2 điểm(< 18.5 kg/m²)'
        }
    ],
    sutCan: [
        {
            key: '0',
            value: '0 điểm (Không sụt cân)'
        },
        {
            key: '1',
            value: '1 điểm (5% - 9.9% trong 1 tháng qua)'
        },
        {
            key: '2',
            value: '2 điểm (≥ 10% trong 1 tháng qua)'
        }
    ],
    luongAn: [
        {
            key: '0',
            value: '0 điểm (Không giảm hoặc nhẹ)'
        },
        {
            key: '1',
            value: '1 điểm (Giảm ≥ 50% trong tuần qua)'
        },
        {
            key: '2',
            value: '2 điểm (Giảm ≥ 75% trong tuần qua)'
        }
    ],
    benhLy: [
        {
            key: '0',
            value: '0 điểm (Bệnh nhẹ - Trung bình)'
        },
        {
            key: '1',
            value: '1 điểm (Bệnh nặng: ví dụ đại phẫu, tai biến mạch máu não, nhiễm trùng nặng, ung thư)'
        },
        {
            key: '2',
            value: '2 điểm (Bệnh rất nặng: ví dụ chấn thương nặng, đang được chăm sóc tích cực)'
        }
    ],
    taiDanhGia: [
        {
            key: '3',
            value: 'Sau 3 ngày (ở người bệnh suy dinh dưỡng)'
        },
        {
            key: '7',
            value: 'Sau 7 ngày (ở người bệnh không suy dinh dưỡng)'
        }
    ],
    duongNuoiAn: [
        {
            key: 'MIENG',
            value: 'Miệng'
        },
        {
            key: 'DUONG_MIENG',
            value: 'Miệng'
        },
        {
            key: 'ONG_THONG',
            value: 'Ống thông'
        },
        {
            key: 'TINH_MACH',
            value: 'Tĩnh mạch'
        }
    ],
    bmiTruocMangThai: [
        {
            key: '0',
            value: '0 điểm(18.5 - 24.9)'
        },
        {
            key: '1',
            value: '1 điểm(≥25)'
        },
        {
            key: '1',
            value: '1 điểm(<18.5)'
        }
    ],
    chuViVongCanhTay: [
        {
            key: '0',
            value: '0 điểm(≥23 cm)'
        },
        {
            key: '2',
            value: '2 điểm(<23 cm)'
        },
    ],
    tocDoTangCan: [
        {
            key: '0',
            value: '0 điểm(Tăng cân theo khuyến nghị)'
        },
        {
            key: '2',
            value: '1 điểm(Tăng cân trên hoặc dưới mức khuyến nghị)'
        },
    ],
    benhKemTheo: [
        {
            key: '0',
            value: '0 điểm (Không)'
        },
        {
            key: '1',
            value: '1 điểm (Tăng huyết áp, đái tháo đường, nghén nặng, thiếu máu, bệnh lý đường tiêu hoá,...)'
        },
    ],
    tuoiThaiTheo: [
        {
            key: 'KINH_CUOI',
            value: 'Kinh cuối'
        },
        {
            key: '3_THANG_DAU_THAI_KY',
            value: 'Siêu âm 3 tháng đầu thai kỳ'
        }
    ],
    bmiTreEm: [
        {
            key: '0',
            value: '0 điểm(>-1 SD)'
        },
        {
            key: '1',
            value: '1 điểm(-1 đến >-2 SD)'
        },
        {
            key: '2',
            value: '2 điểm(≤-2 SD)'
        }
    ],
    sutCanTreEm: [
        {
            key: '0',
            value: '0 điểm(>-1 SD)'
        },
        {
            key: '1',
            value: '1 điểm(Tăng cân < 50% so với chuẩn trẻ em < 2 tuổi)'
        },
        {
            key: '2',
            value: '2 điểm(Tăng cân < 25% so với chuẩn trẻ em < 2 tuổi)'
        },
        {
            key: '1',
            value: '1 điểm(Sụt cân 7.5% trọng lượng ở trẻ em ≥ 2 tuổi)'
        },
        {
            key: '2',
            value: '2 điểm(Sụt cân 10% trọng lượng ở trẻ em ≥ 2 tuổi)'
        }
    ],
    luongAnTreEm: [
        {
            key: '0',
            value: '0 điểm(Không giảm hoặc giảm nhẹ)'
        },
        {
            key: '1',
            value: '1 điểm(Giảm ≥ 50% trong tuần qua)'
        },
        {
            key: '2',
            value: '2 điểm(Giảm ≥ 70% trong tuần qua)'
        }
    ]
}
var keyLogPDGDDTren18 = {
    ...keyLogModule.THONGTINCOBAN,
    ...keyLogModule.SANGLOCNGUYCODINHDUONG,
    KETQUASANGLOC: 'Kết quả sàng lọc',
    CHIDINH: 'Chỉ đỉnh',
    ...keyLogModule.DANHGIATINHTRANGDINHDUONG,
    KETLUAN: 'Kết luận',
    CHE_DO_AN: 'Chế độ ăn',
    CHI_DINH_DD_KHAC: 'Chỉ định khác',
    ...keyLogModule.KEHOACHCANTHIEP
};
var pdgddt18DuLieuBanDau = {};
var keyLogPDGDDNguoiLon = {
    ...keyLogModule.THONGTINCOBAN,
    CAN_NANG_RA_VIEN: 'Cân nặng ra viện',
    ...keyLogModule.SANGLOCNGUYCODINHDUONG,
    KETQUASANGLOC: 'Kết quả sàng lọc',
    ...keyLogModule.DANHGIATINHTRANGDINHDUONG,
    DANH_GIA_TONG_DIEM: 'Tổng điểm',
    KETLUAN: 'Kết luận',
    CHE_DO_AN: 'Chế độ ăn',
    DUONG_NUOI_AN: 'Đường nuôi ăn',
    MOI_HOI_CHAN_DD: 'Mời hội chẩn dinh dưỡng',
    ...keyLogModule.KEHOACHCANTHIEP
}
var pdgddnlDuLieuBanDau = {};
var keyLogPDGDDSanPhu = {
    TUOI_THAI: 'Tuổi thai',
    TUOI_THAI_THEO: 'Tuổi thai theo',
    ...keyLogModule.THONGTINCOBAN,
    CAN_NANG_RA_VIEN: 'Cân nặng ra viện',
    CHU_VI_VONG_CANH_TAY: 'Chu vi vòng cánh tay',
    DANH_GIA_BMI: 'Đánh giá BMI',
    DANH_GIA_CV_VONG_CANH_TAY: 'Đánh giá chu vi vòng cánh tay đánh giá',
    DANH_GIA_TD_TANG_CAN: 'Đánh giá tốc độ tăng cân ánh giá',
    DANH_GIA_BENH_KEM_THEO: 'Đánh giá bệnh kèm theo',
    KETLUAN: 'Kết luận',
    CHE_DO_AN: 'Chế độ ăn',
    DUONG_NUOI_AN: 'Đường nuôi ăn',
    MOI_HOI_CHAN_DD: 'Mời hội chẩn dinh dưỡng',
    ...keyLogModule.KEHOACHCANTHIEP
}
var pdgddspDuLieuBanDau = {}
var keyLogPDGDDTreEm = {
    ...keyLogModule.THONGTINCOBAN,
    CAN_NANG_RA_VIEN: 'Cân nặng ra viện',
    DANH_GIA_BMI: 'Đánh giá BMI',
    DANH_GIA_SUT_CAN: 'Đánh giá sụt cân',
    DANH_GIA_LUONG_AN: 'Đánh giá lượng ăn',
    KETLUAN: 'Kết luận',
    CHE_DO_AN: 'Chế độ ăn',
    DUONG_NUOI_AN: 'Đường nuôi ăn',
    MOI_HOI_CHAN_DD: 'Mời hội chẩn dinh dưỡng',
    ...keyLogModule.KEHOACHCANTHIEP
}
var pdgddteDuLieuBanDau = {};
let pdgddChung = { newForm: {}, preForm: {} };

function initGridPhieuchucnangsong() {
    var listChucnangsong = $("#ttchamsoc_list_chucnangsong");
    if(!listChucnangsong[0].grid) {
        listChucnangsong.jqGrid({
            datatype: "local",
            loadonce: true,
            height: 400,
            width: null,
            shrinkToFit: false,
            colModel: [
                {label: "ID", name: 'ID', index: 'ID', width: 10, hidden: true},
                {label: "DVTT",name: 'DVTT', index: 'DVTT', width: 10, hidden: true},
                {label: "SOVAOVIEN",name: 'SOVAOVIEN', index: 'SOVAOVIEN', width: 10, hidden: true},
                {label: "Trạng thái", name: 'TRANG_THAI', index: 'TRANG_THAI', width: 100,
                    formatter: function(cellValue, options, rowObject) {
                        if(rowObject.KEYSIGN) {
                            return "<span class='badge badge-success'>Đã ký</span>";
                        } else {
                            return "<span class='badge badge-danger'>Chưa ký</span>";
                        }
                    },
                    cellattr: function(rowId, tv, rawObject, cm, rdata) {
                        return 'style="text-align: center;"';
                    }
                },
                {label: "Khoa", name: 'KHOA', index: 'KHOA', width: 130},
                {label: "STT Điều trị", name: 'STT_DIEUTRI', index: 'STT_DIEUTRI', width: 50, hidden: true},
                {label: "Ngày giờ", name: 'THOIGIANTHEODOI', index: 'THOIGIANTHEODOI', width: 130},
                {label: "Nhịp thở",name: 'NHIPTHO', index: 'NHIPTHO', width: 80},
                {label: "Nhiệt độ",name: 'NHIETDO', index: 'NHIETDO', width: 80},
                {label: "Huyết áp",name: 'HUYET_AP', index: 'HUYET_AP', width: 80},
                {label: "Mạch",name: 'MACH', index: 'MACH', width: 80},
                {label: "Cân nặng",name: 'CANNANG', index: 'CANNANG', width: 80},
                {label: "SpO2",name: 'CHISO4', index: 'CHISO4', width: 80},
                {label: "FiO2",name: 'CHISO5', index: 'CHISO5', width: 80},
                {label: "Khác",name: 'CHISOKHAC', index: 'CHISOKHAC', width: 80},
                {label: "Người theo dõi",name: 'NGUOITHEODOI', index: 'NGUOITHEODOI', width: 130},
                {label: "Người tạo",name: 'NGUOITAO', index: 'NGUOITAO', width: 80, hidden: true},
                {label: "Ngày tạo",name: 'NGAYTAO', index: 'NGAYTAO', width: 130},
                {label: "MA_NHANVIEN",name: 'MA_NHANVIEN', index: 'MA_NHANVIEN', width: 10, hidden: true},
                {label: "HUYETAP_TREN",name: 'HUYETAP_TREN', index: 'HUYETAP_TREN', width: 10, hidden: true},
                {label: "HUYETAP_DUOI",name: 'HUYETAP_DUOI', index: 'HUYETAP_DUOI', width: 10, hidden: true},
                {label: "KEYSIGN",name: 'KEYSIGN', index: 'KEYSIGN', width: 10, hidden: true},
                {label: "TRANG",name: 'TRANG', index: 'TRANG', width: 10, hidden: true}
            ],
            rowNum: 1800000,
            caption: "Danh sách phiếu chức năng sống",
            onRightClickRow: function (id1) {
                if (id1) {
                    $.contextMenu('destroy', '#ttchamsoc_list_chucnangsong tr');
                    listChucnangsong.jqGrid('setSelection', id1);
                    var rowData = getThongtinRowSelected("ttchamsoc_list_chucnangsong");
                    var items = {
                        "copy": {name: '<p><i class="fa fa-bolt text-primary" aria-hidden="true"></i> Copy chỉ số</p>'},
                    };
                    if(rowData.KEYSIGN) {
                        items = {
                            ...items,
                            "huykyso": {name: '<p class="text-danger"><i class="fa fa-key" aria-hidden="true"></i> Huỷ ký số</p>'},
                        }
                    } else {
                        items = {
                            ...items,
                            "kyso": {name: '<p class="text-primary"><i class="fa fa-key" aria-hidden="true"></i> Ký số</p>'},
                            "capnhat": {name: '<p class="text-success"><i class="fa fa-pencil" aria-hidden="true"></i> Chỉnh sửa</p>'},
                            "xoa": {name: '<p class="text-danger"><i class="fa fa-trash-o" aria-hidden="true"></i> Xóa</p>'},
                        }
                    }
                    $.contextMenu({
                        selector: '#ttchamsoc_list_chucnangsong tr',
                        callback: function (key, options) {
                            if (key == "capnhat") {
                                var id = $("#ttchamsoc_list_chucnangsong").jqGrid('getGridParam', 'selrow');
                                var ret = $("#ttchamsoc_list_chucnangsong").jqGrid('getRowData', id);
                                if (singletonObject.userId != ret.MA_NHANVIEN) {
                                    return notifiToClient("Red", "Bạn không có quyền cập nhật chỉ số này");
                                }
                                if (id) {
                                    $("#modalFormPhieuchucnangsong").modal("show");
                                    $("#ttchamsoc_pcns_inputmach").val(ret.MACH);
                                    $("#ttchamsoc_pcns_id").val(ret.ID);
                                    $("#ttchamsoc_pcns_inputnhietdo").val(ret.NHIETDO);
                                    $("#ttchamsoc_pcns_inputhuyetap_tren").val(ret.HUYETAP_TREN);
                                    $("#ttchamsoc_pcns_inputhuyetap_duoi").val(ret.HUYETAP_DUOI);
                                    $("#ttchamsoc_pcns_inputnhiptho").val(ret.NHIPTHO);
                                    $("#ttchamsoc_pcns_inputcannang").val(ret.CANNANG);
                                    $("#ttchamsoc_pcns_inputc4").val(ret.CHISO4);
                                    $("#ttchamsoc_pcns_inputc5").val(ret.CHISO5);
                                    $("#ttchamsoc_pcns_ngaygioplap").val(ret.THOIGIANTHEODOI);
                                    $("#ttchamsoc_pcns_inputkhac").val(ret.CHISOKHAC);
                                    thongTinPhieuChucNangSongTruocChinhSua = ret;
                                }
                            }
                            if (key == "xoa") {
                                var id = $("#ttchamsoc_list_chucnangsong").jqGrid('getGridParam', 'selrow');
                                var ret = $("#ttchamsoc_list_chucnangsong").jqGrid('getRowData', id);
                                if (singletonObject.userId == ret.MA_NHANVIEN) {
                                    var confirmModal = $.confirm({
                                        title: 'Xác nhận!',
                                        type: 'orange',
                                        content: "Bạn có chắc chắn muốn xóa chỉ số này?",
                                        buttons: {
                                            warning: {
                                                btnClass: 'btn-warning',
                                                text: "Tiếp tục",
                                                action: function(){
                                                    confirmModal.close();
                                                    showLoaderIntoWrapId("ttchamsoc_list_chucnangsong_wrap")
                                                    $.post("cmu_post", {
                                                        url: [singletonObject.dvtt, ret.ID, "CMU_HSBA_CNS_DEL"].join("```")
                                                    }).done(function() {
                                                        notifiToClient("Green", "Xóa thành công");
                                                        loadDsPhieuChucNangSong();
                                                        thongTinPhieuChucNangSongDangChon = ret;
                                                        luuLogHSBATheoBN({
                                                            SOVAOVIEN: thongtinhsba.thongtinbn.SOVAOVIEN,
                                                            LOAI: LOGHSBALOAI.PHIEUCHUCNANGSONG.KEY,
                                                            NOIDUNGBANDAU: $.getContentLogPhieuchucnangsong(ret),
                                                            NOIDUNGMOI: "",
                                                            USERID: singletonObject.userId,
                                                            ACTION: LOGHSBAACTION.DELETE.KEY,
                                                        })
                                                    }).fail(function() {
                                                        notifiToClient("Red", "Xóa thất bại");
                                                    }).always(function() {
                                                        hideLoaderIntoWrapId("ttchamsoc_list_chucnangsong_wrap")
                                                    })
                                                }
                                            },
                                            cancel: function () {
                                                hideSelfLoading(idButton)
                                            }
                                        }
                                    });
                                } else {
                                    notifiToClient("Red", "Bạn không có quyền xóa chỉ số này");
                                }
                            }
                            if (key == "copy") {
                                var ret = getThongtinRowSelected("ttchamsoc_list_chucnangsong");
                                $("#modalFormPhieuchucnangsong").modal("show");
                                var hap = ret.HUYET_AP.split("/")
                                $("#ttchamsoc_pcns_inputmach").val(ret.MACH);
                                $("#ttchamsoc_pcns_id").val(0);
                                $("#ttchamsoc_pcns_inputnhietdo").val(ret.NHIETDO);
                                $("#ttchamsoc_pcns_inputhuyetap_tren").val(hap[0]);
                                if(hap.length > 1) {
                                    $("#ttchamsoc_pcns_inputhuyetap_duoi").val(hap[1]);
                                }
                                $("#ttchamsoc_pcns_inputnhiptho").val(ret.NHIPTHO);
                                $("#ttchamsoc_pcns_inputcannang").val(ret.CANNANG);
                                $("#ttchamsoc_pcns_inputc4").val(ret.CHISO4);
                                $("#ttchamsoc_pcns_inputc5").val(ret.CHISO5);
                                $("#ttchamsoc_pcns_ngaygioplap").val(ret.THOIGIANTHEODOI);
                                $("#ttchamsoc_pcns_inputkhac").val(ret.CHISOKHAC);
                            }
                            if (key == "kyso") {
                                var ret = getThongtinRowSelected("ttchamsoc_list_chucnangsong");
                                var allRowData = $("#ttchamsoc_list_chucnangsong").jqGrid('getGridParam', 'data');
                                var retDateTime = moment(ret.THOIGIANTHEODOI, "DD/MM/YYYY HH:mm");
                                var filteredObjects = allRowData.filter(item => {
                                    var itemDateTime = moment(item.THOIGIANTHEODOI, "DD/MM/YYYY HH:mm");
                                    return itemDateTime.isBefore(retDateTime) && !item.KEYSIGN;
                                });
                                if (filteredObjects.length > 0) {
                                    return notifiToClient("Red", "Không thể ký số phiếu này vì đã có phiếu trước ngày " + ret.THOIGIANTHEODOI + " chưa ký");
                                }
                                if (singletonObject.userId != ret.MA_NHANVIEN) {
                                    return notifiToClient("Red", "Bạn không có quyền ký số phiếu này: " + ret.NGUOITHEODOI);
                                }
                                getUrlXemPhieuChucNangSong(ret.TRANG, ret.ID).then(url => {
                                    kySoChung({
                                        dvtt: singletonObject.dvtt,
                                        userId: singletonObject.userId,
                                        url: url,
                                        loaiGiay: "PHIEU_NOITRU_CHUCNANGSONG",
                                        maBenhNhan: thongtinhsba.thongtinbn.MA_BENH_NHAN,
                                        soBenhAn: thongtinhsba.thongtinbn.STT_BENHAN,
                                        soPhieuDichVu: ret.TRANG,
                                        maDichVu: ret.ID,
                                        soVaoVien: thongtinhsba.thongtinbn.SOVAOVIEN,
                                        soVaoVienDT: thongtinhsba.thongtinbn.SOVAOVIEN_DT,
                                        keyword: ";" + ret.ID + ";",
                                        userId: singletonObject.userId,
                                        fileName: "Phiếu chức năng sống: " + thongtinhsba.thongtinbn.TEN_BENH_NHAN + " - Ngày: " + ret.THOIGIANTHEODOI,
                                        visibleType: "100",
                                    }, function(dataKySo) {
                                        luuLogHSBATheoBN({
                                            SOVAOVIEN: thongtinhsba.thongtinbn.SOVAOVIEN,
                                            LOAI: LOGHSBALOAI.PHIEUCHUCNANGSONG.KEY,
                                            NOIDUNGBANDAU: "",
                                            NOIDUNGMOI: "Ký số chức năng sống ngày " + ret.THOIGIANTHEODOI,
                                            USERID: singletonObject.userId,
                                            ACTION: LOGHSBAACTION.EDIT.KEY,
                                        })
                                        $("#ttchamsoc-chucnangsong").click();
                                    });
                                }).catch(error => {
                                    notifiToClient("Red", "Lỗi: " + error);
                                });
                            }
                            if (key == "huykyso") {
                                var ret = getThongtinRowSelected("ttchamsoc_list_chucnangsong");
                                var allRowData = $("#ttchamsoc_list_chucnangsong").jqGrid('getGridParam', 'data');
                                var retDateTime = moment(ret.THOIGIANTHEODOI, "DD/MM/YYYY HH:mm");
                                var filteredObjects = allRowData.filter(item => {
                                    var itemDateTime = moment(item.THOIGIANTHEODOI, "DD/MM/YYYY HH:mm");
                                    return itemDateTime.isAfter(retDateTime) && item.KEYSIGN;
                                });
                                if (filteredObjects.length > 0) {
                                    return notifiToClient("Red", "Không thể huỷ ký số phiếu này vì đã có phiếu sau ngày " + ret.THOIGIANTHEODOI + " đã ký");
                                }
                                if (singletonObject.userId != ret.MA_NHANVIEN) {
                                    return notifiToClient("Red", "Bạn không có quyền huỷ ký số phiếu này: " + ret.NGUOITHEODOI);
                                }
                                confirmToClient("Bạn có chắc sẽ hủy ký số phiếu này?", function() {
                                    huykysoFilesign769("PHIEU_NOITRU_CHUCNANGSONG", ret.TRANG, singletonObject.userId, singletonObject.dvtt,
                                        thongtinhsba.thongtinbn.SOVAOVIEN, thongtinhsba.thongtinbn.SOVAOVIEN_DT, ret.ID, function(data) {
                                            luuLogHSBATheoBN({
                                                SOVAOVIEN: thongtinhsba.thongtinbn.SOVAOVIEN,
                                                LOAI: LOGHSBALOAI.PHIEUCHUCNANGSONG.KEY,
                                                NOIDUNGBANDAU: "",
                                                NOIDUNGMOI: "Huỷ ký số chức năng sống ngày " + ret.THOIGIANTHEODOI,
                                                USERID: singletonObject.userId,
                                                ACTION: LOGHSBAACTION.EDIT.KEY,
                                            })
                                            $("#ttchamsoc-chucnangsong").click();
                                        })
                                }, function () {

                                })
                            }
                        },
                        items: items
                    });
                }
            }
        })
        listChucnangsong.jqGrid('filterToolbar', {stringResult: true, searchOnEnter: false, defaultSearch: "cn"});
    }
}

function getUrlXemPhieuChucNangSong(trang, id_ky = 0) {
    return new Promise(async (resolve, reject) => {
        try {
            var list = thongtinhsba.thongtinbn.phieuchucnangsong;
            var indexPage = list.length < 18?
                list.length - 1: list.length -  (Number(trang) - 1 )*18 - 1;
            var ret = list[indexPage];
            var resData = $.ajax({
                url: "cmu_getlist?url=" + convertArray([
                    singletonObject.dvtt,
                    thongtinhsba.thongtinbn.MA_BENH_NHAN,
                    thongtinhsba.thongtinbn.STT_BENHAN,
                    thongtinhsba.thongtinbn.STT_DOTDIEUTRI,
                    ret.THOIGIANTHEODOI,
                    "NAN_PHIEUCHUCNANGSONG_SP_F"]),
                method: "GET",
                async: false,
            }).responseText;
            var dataArr = JSON.parse(resData);
            if (dataArr.length > 0) {
                var dataMach = [];
                var dataNhietDo = [];
                for (var i = 0; i < dataArr.length; i++) {
                    dataMach.push(dataArr[i].MACH);
                    dataNhietDo.push(dataArr[i].NHIETDO);
                    if (dataArr[i].ID == id_ky) {
                        break;
                    }
                }
                veBieuDoChucNangSong({
                    MACH: dataMach,
                    NHIET_DO: dataNhietDo
                });
                var image = $("#myChartCSC1").get(0).toDataURL("image/png").replace("data:image/png;base64,", "");
                $.ajax({
                    url: "cmu_post_CMU_CHART_INS",
                    method: "POST",
                    data: {
                        url: [singletonObject.dvtt, ret.THOIGIANTHEODOI, 'CHUCNANGSONG', i+1, image].join("```"),
                    },
                    async: false
                }).done(function(count) {
                });

                var params = {
                    dvtt: singletonObject.dvtt,
                    mabenhnhan: thongtinhsba.thongtinbn.MABENHNHAN,
                    stt_benhan: thongtinhsba.thongtinbn.STT_BENHAN,
                    stt_dotdieutri: thongtinhsba.thongtinbn.STT_DOTDIEUTRI,
                    id_ky: id_ky,
                    in_tu_ngay: ret.THOIGIANTHEODOI
                };
                for(var i = 0; i < 18; i++) {
                    var object = {};
                    var item = dataArr[i];
                    object["day_" + i] = item ? item.NGAY_DO : null;
                    object["gio_" + i] = item ? item.GIO_DO : null;
                    object["hap_tren_" + i] = item ? item.HUYETAPTREN : null;
                    object["hap_duoi_" + i] = item ? item.HUYETAPDUOI : null;
                    object["cannang_" + i] = item ? item.CANNANG : null;
                    object["nhiptho_" + i] = item ? item.NHIPTHO : null;
                    object["spo2_" + i] = item ? item.SPO2 : null;
                    object["fio2_" + i] = item ? item.FIO2 : null;
                    object["khac_" + i] = item ? item.KHAC : null;
                    object["nguoilap_" + i] = item ? item.NGUOILAP : null;
                    object["id_" + i] = item ? item.ID : null;
                    object["keysign_" + i] = item ? item.KEYSIGN : null;
                    params = {...params, ...object};
                    if (item && item.ID == id_ky) {
                        break;
                    }
                }
            }
            resolve('cmu_in_rp_theodoichucnangsong_emr?type=pdf&' + $.param(params));
        } catch (e) {
            reject(e);
        }
    });
}

function veBieuDoChucNangSong(objectChart) {
    $("#wrap_canvas").html("<canvas id='myChartCSC1' width='1028' height='584'></canvas>")
    var canvas = document.getElementById('myChartCSC1');
    var ctx = canvas.getContext('2d');
    var canvasWidth = canvas.width;
    var canvasHeight = canvas.height;
    ctx.clearRect(0, 0, canvasWidth, canvasHeight)
    var margin = 10;
    var Hmargin = 20;
    var chartWidth = canvasWidth - 2 * margin;
    var chartHeight = canvasHeight - 2 * Hmargin;
    var barWidth = chartWidth / 18;

    function isNumeric(value) {
        return !isNaN(parseFloat(value)) && isFinite(value);
    }

    function drawChart() {
        ctx.lineWidth = 1;
        ctx.strokeStyle = '#c5c5c5';
        ctx.fillStyle = '#c5c5c5';
        ctx.beginPath();
        for (let i = 0; i <= 7; i++) {
            var yPos = Hmargin + i * (chartHeight / 7);
            ctx.beginPath();
            ctx.moveTo(margin, yPos);
            ctx.lineTo(canvasWidth - margin, yPos);
            ctx.setLineDash([5, 5]);
            ctx.stroke();
        }
        for (let i = 0; i <= 18; i++) {
            var xPos = margin + i * barWidth;
            ctx.beginPath();
            ctx.moveTo(xPos, Hmargin);
            ctx.lineTo(xPos, canvasHeight - Hmargin);
            ctx.setLineDash([5, 5]);
            ctx.stroke();
        }

    }

    function drawMachChart(data) {
        ctx.lineWidth = 2;
        ctx.setLineDash([]);
        ctx.fillStyle = '#ff4136';
        ctx.strokeStyle = '#ff4136';
        var x = 0;
        var y = 0;
        var stepH = (chartHeight / 7);
        for (let i = 0; i < data.length; i++) {
            if(data[i]){
                x = (margin + i * barWidth) + barWidth / 2;
                if (isNumeric(data[i]) && data[i] <= 180) {
                    y = canvasHeight - Hmargin - ((data[i]-40)/20) * stepH;
                    if (y <= canvasHeight - Hmargin && y >= Hmargin) {
                        ctx.beginPath();
                        ctx.arc(x, y, 8, 0, 2 * Math.PI);
                        ctx.fill();
                    }
                    if(data[i] <= 40) {
                        ctx.font = "bold 15px Arial";
                        ctx.fillText("M:"+data[i], x - margin, canvasHeight -stepH );
                    }
                } else {
                    ctx.font = "bold 15px Arial";
                    ctx.textAlign = "center";
                    ctx.fillText("M:"+data[i], x, canvasHeight -stepH );
                }
            }
        }
        for (let i = 0; i < data.length; i++) {
            if (data[i] && data[i] <= 180) {
                x = (margin + i * barWidth) + barWidth / 2;
                y = canvasHeight - Hmargin - ((data[i]-40)/20) * stepH;
                if (i === 0  ) {
                    ctx.moveTo(x, y);
                } else {
                    if (y > canvasHeight - Hmargin) {
                        y = canvasHeight - Hmargin;
                    }
                    ctx.lineTo(x, y);
                }
            }
        }
        ctx.stroke();
    }

    function drawNhietDoChart(data) {
        ctx.lineWidth = 2;
        ctx.setLineDash([]);
        ctx.fillStyle = '#0000ff';
        ctx.strokeStyle = '#0000ff';
        var x = 0;
        var y = 0;
        var stepH = (chartHeight / 7);
        for (let i = 0; i < data.length; i++) {
            if (data[i]){
                x = (margin + i * barWidth) + barWidth / 2;
                if (isNumeric(data[i]) && data[i] <= 42) {
                    y = canvasHeight - Hmargin - (data[i]-35) * stepH;
                    if (y <= canvasHeight - Hmargin && y >= Hmargin) {
                        ctx.beginPath();
                        ctx.fillRect(x-3, y-5, 12, 12);
                    }
                    if(data[i] < 35) {
                        ctx.font = "bold 15px Arial";
                        ctx.fillText("t:"+data[i], x - margin, canvasHeight - stepH - 25);
                    }
                } else {
                    ctx.font = "bold 15px Arial";
                    ctx.textAlign = "center";
                    ctx.fillText("t:"+data[i], x, canvasHeight - stepH - 25);
                }
            }
        }

        for (let i = 0; i < data.length; i++) {
            if (data[i] && data[i] <= 42) {
                x = (margin + i * barWidth) + barWidth / 2;
                y = canvasHeight - Hmargin - (data[i]-35) * stepH;
                if (y > canvasHeight - Hmargin) {
                    y = canvasHeight - Hmargin;
                }
                if (i === 0  ) {
                    ctx.moveTo(x, y);
                } else {
                    ctx.lineTo(x, y);
                }
            }
        }
        ctx.stroke();
    }
    drawChart();
    drawMachChart(objectChart.MACH);
    drawNhietDoChart(objectChart.NHIET_DO);
}

function loadDsPhieuChucNangSong() {
    var url = "cmu_list_cmu_list_phieucns?url="+convertArray([singletonObject.dvtt, thongtinhsba.thongtinbn.SOVAOVIEN]);
    $("#ttchamsoc_list_chucnangsong").jqGrid('setGridParam', {datatype: 'json', url: url}).trigger('reloadGrid');
}

function loadTTPhieuDanhGiaDinhDuong() {
    var dsBacSi = singletonObject.danhsachnhanvien;
    initSelect2IfnotIntance("phieutren18_bsdieutri", dsBacSi, 'MA_NHANVIEN', 'TEN_NHANVIEN', 0, 0, singletonObject.userId);
    initSelect2IfnotIntance("phieunguoilon_bsdieutri", dsBacSi, 'MA_NHANVIEN', 'TEN_NHANVIEN', 0, 0, singletonObject.userId);
    initSelect2IfnotIntance("phieusanphu_bsdieutri", dsBacSi, 'MA_NHANVIEN', 'TEN_NHANVIEN', 0, 0, singletonObject.userId);
    initSelect2IfnotIntance("phieutreem_bsdieutri", dsBacSi, 'MA_NHANVIEN', 'TEN_NHANVIEN', 0, 0, singletonObject.userId);

    var url = 'cmu_getlist?url=' + convertArray([
        thongtinhsba.thongtinbn.STT_BENHAN,
        $("#ttin_dieutri_khoa").val(),
        $("#ttin_dieutri_bs").val(),
        singletonObject.dvtt,
        "HSBA_TODIEUTRI_CMU_SEL"
    ]);

    $.get(url).done(function(data){
        if (data && data.length > 0) {
            $("#latest_id_dieutri").val(data[0].ID_DIEUTRI);
        } else  {
            $("#latest_id_dieutri").val("");
        }
    });
}

function reloadDSDanhGiaDD() {
    var url = "cmu_getlist?url=" + convertArray([singletonObject.dvtt, thongtinhsba.thongtinbn.STT_BENHAN, "CMU_GET_DSPHIEU_DANHGIA_DD"]);
    $("#ttchamsoc_list_phieudanhgiadd").jqGrid('setGridParam', {
        datatype: 'json',
        url: url
    }).trigger('reloadGrid')
}

function resetModalForm(formId, btnId) {
    $(".custom-select").val(null).trigger('change');
    $(".checkbox-group").prop("checked", false);
    $("#" + formId).trigger("reset");
    $("#" + btnId).attr("data-action", "THEM");
    $("#" + btnId).html("<i class=\"fa fa-floppy-o\" aria-hidden=\"true\"></i> Lưu phiếu");
}

function calculateSuyDinhDuong(idBmi, idSutCan, idLuongAn, kqTot, kqXau) {
    var getBmi = $("#" + idBmi).val();
    var getSutCan = $("#" + idSutCan).val();
    var getLuongAn = $("#" + idLuongAn).val();
    var calcDiem = Number(getBmi) + Number(getSutCan) + Number(getLuongAn);
    var ketQua = "";
    if (calcDiem < 2) {
        ketQua = kqTot;
    } else if (calcDiem >= 2) {
        ketQua = kqXau;
    }

    return ketQua;
}

function calculateBMI(idCanNang, idChieuCao , idBMI) {
    var getCanNang = $("#" + idCanNang).val();
    var getChieuCao = $("#" + idChieuCao).val();
    var chieuCaoMet = getChieuCao / 100;

    if (getCanNang != "" && (getCanNang < 1 || getCanNang > 300)) {
        notifiToClient("Red", "Cân nặng không hợp lệ, giá trị từ 1 - 300");
        return false;
    }

    if(getChieuCao != "" && (getChieuCao < 1 || getChieuCao > 300)) {
        notifiToClient("Red", "Chiều cao không hợp lệ, giá trị từ 1 - 300");
        return false;
    }

    var bmi = "";
    if(getCanNang != "" && getChieuCao != "") {
        bmi = (getCanNang / (chieuCaoMet * chieuCaoMet)).toFixed(2);
        console.log(typeof bmi);
    }
    $("#" + idBMI).val(bmi);
}

function ketQuaSangLoc(bmi, sutCan, luongAn, benhNang) {
    if (Number(bmi) == 1 || Number(sutCan) == 1 || Number(luongAn) == 1 || Number(benhNang) == 1) {
        $("#phieutren18_ketluansangloc").html("Có nguy cơ suy dinh dưỡng");
        $("#phieutren18_chidinh").html("Đánh giá tình trạng dinh dưỡng");
    } else {
        $("#phieutren18_ketluansangloc").html("Không có nguy cơ suy dinh dưỡng");
        $("#phieutren18_chidinh").html("Tái sàng lọc sau 1 tuần");
    }
}

/**
 * Phân loại dựa trên điểm BMI
 * */
function danhGiaBMI(bmiResult, idKetQua) {
    if (bmiResult) {
        var bmiValue = Number(bmiResult);
        var bmiRanges = [
            { min: -Infinity, max: 15.9, label: "Gầy cấp III" },
            { min: 16, max: 16.9, label: "Gầy cấp II" },
            { min: 17, max: 18.4, label: "Gầy cấp I" },
            { min: 18.5, max: 24.9, label: "Bình thường" },
            { min: 25, max: 29.9, label: "Thừa cân" },
            { min: 30, max: 34.9, label: "Béo phì cấp I" },
            { min: 35, max: 39.9, label: "Béo phì cấp II" },
            { min: 40, max: Infinity, label: "Béo phì cấp III" }
        ];
        var result = bmiRanges.filter(function(range) {
            return bmiValue >= range.min && bmiValue <= range.max;
        })[0];

        if (result) {
            $("#" + idKetQua).val(result.label);
        }
    }
}

function danhGiaTinhTrangDD(idBmi, idSutCan, idLuongAn, idBenhLy, positiveResults, negativeResults) {
    var danhGiaBMI = $("#" + idBmi).val();
    var danhGiaSutCan = $("#" + idSutCan).val();
    var danhGiaLuongAn = $("#" + idLuongAn).val();
    var danhGiaBenhLy = $("#" + idBenhLy).val();
    var tongDiem = Number(danhGiaBMI) + Number(danhGiaSutCan) + Number(danhGiaLuongAn) + Number(danhGiaBenhLy);
    if (tongDiem >= 2) {
        $("#phieutren18_ketluan").html(negativeResults);
    } else {
        $("#phieutren18_ketluan").html(positiveResults);
    }
}

$(function() {
    var formioMauchung;
    var keyLuuLogPhieuchucnangsong = {
        "THOIGIANTHEODOI": "Ngày giờ lập",
        "MACH": "Mạch",
        "NHIPTHO": "Nhịp nhở",
        "NHIETDO": "Nhiệt độ",
        "HUYETAP_TREN": "Huyết áp tâm trương",
        "HUYETAP_DUOI": "Huyết áp tâm thu",
        "CANNANG": "Cân nặng",
        "CHISO4": "SpO2",
        "CHISO5": "FiO2",
        "CHISOKHAC": "Khác",
    }
    $("#ttchamsoc-chucnangsong").click(function() {
        getFilesign769(
            "PHIEU_NOITRU_CHUCNANGSONG",
            '-1',
            -1,
            singletonObject.dvtt,
            thongtinhsba.thongtinbn.SOVAOVIEN,
            thongtinhsba.thongtinbn.SOVAOVIEN_DT,
            '-1',
            function(dataKySo) {
                if (dataKySo.length > 0) {
                    $("#ttchamsoc-chucnangsong-tab .daky-hidden").hide();
                    $("#ttchamsoc-chucnangsong-tab .chuaky-hidden").show();
                } else {
                    $("#ttchamsoc-chucnangsong-tab .chuaky-hidden").hide();
                    $("#ttchamsoc-chucnangsong-tab .daky-hidden").show();
                }
            },
        );
        loadDsPhieuChucNangSong();
        loadDSPhieuCNS()
    })
    $("#ttchamsoc_pcns_themchiso").click(function() {
        addTextTitleModal("titlePhieuchucnangsong", "Chỉ số chức năng sống")
        $("#modalFormPhieuchucnangsong").modal("show");
        $("#formPhieuchucnangsong input").val("");
        $("#ttchamsoc_pcns_id input").val(0);
        $.get("cmu_list_CMU_HSBA_GETDEF?url="+convertArray([singletonObject.dvtt, thongtinhsba.thongtinbn.SOVAOVIEN, thongtinhsba.thongtinbn.SOVAOVIEN_DT])).done(function(dataDef) {
            if(dataDef.length > 0) {
                $("#ttchamsoc_pcns_inputcannang").val(dataDef[0].CANNANG)
            }
        })
    })
    $("#action_ttchamsoc_luupcns").click(function() {
        var idButton = this.id;
        showSelfLoading(idButton);
        if (!$("#formPhieuchucnangsong").valid()) {
            hideSelfLoading(idButton);
            return false;
        }
        var res = convertDataFormToJson("formPhieuchucnangsong");
        if($("#ttchamsoc_pcns_id").val() > 0) {
            return $.post("cmu_post", {
                url: [singletonObject.dvtt,
                    $("#ttchamsoc_pcns_id").val(),
                    res.ttchamsoc_pcns_inputmach,
                    res.ttchamsoc_pcns_inputnhietdo,
                    res.ttchamsoc_pcns_inputhuyetap_tren,
                    res.ttchamsoc_pcns_inputhuyetap_duoi,
                    res.ttchamsoc_pcns_inputnhiptho,
                    res.ttchamsoc_pcns_inputcannang,
                    res.ttchamsoc_pcns_inputc4,
                    res.ttchamsoc_pcns_inputc5,
                    res.ttchamsoc_pcns_inputkhac,
                    res.ttchamsoc_pcns_ngaygioplap
                    ,"CMU_HSBA_CNS_UPD"].join("```")
            }).done(function(data) {
                $("#modalFormPhieuchucnangsong").modal("hide");
                notifiToClient("Green","Lưu thành công")
                loadDsPhieuChucNangSong();
                var ret = getThongtinRowSelected("ttchamsoc_list_chucnangsong");
                var objectBandau = {};
                Object.keys(keyLuuLogPhieuchucnangsong).forEach(function(key) {
                    objectBandau[key] = ret[key]
                })
                var diffObject = findDifferencesBetweenObjects(objectBandau, res);
                var Logbandau = [];
                var Logmoi = [];
                if(!diffObject['THOIGIANTHEODOI']) {
                    Logbandau.push(keyLuuLogPhieuchucnangsong['THOIGIANTHEODOI'] + ": " + ret['THOIGIANTHEODOI'])
                    Logmoi.push(keyLuuLogPhieuchucnangsong['THOIGIANTHEODOI'] + ": " + ret['THOIGIANTHEODOI'])
                }
                for (let key in diffObject) {
                    if (keyLuuLogPhieuchucnangsong.hasOwnProperty(key)) {
                        Logbandau.push( keyLuuLogPhieuchucnangsong[key] + ": " +ret[key])
                        Logmoi.push(keyLuuLogPhieuchucnangsong[key] + ": " +res[key])
                    }
                }
                if(!_.isEmpty(diffObject)) {
                    luuLogHSBATheoBN({
                        SOVAOVIEN: thongtinhsba.thongtinbn.SOVAOVIEN,
                        LOAI: LOGHSBALOAI.PHIEUCHUCNANGSONG.KEY,
                        NOIDUNGBANDAU: Logbandau.join(";"),
                        NOIDUNGMOI: Logmoi.join(";"),
                        USERID: singletonObject.userId,
                        ACTION: LOGHSBAACTION.EDIT.KEY,
                    })
                }

            }).fail(function() {
                notifiToClient("Red","Lưu không thành công")
            }).always(function(){
                hideSelfLoading(idButton);
            })
        }
        $.post("cmu_post", {
            url: [singletonObject.dvtt,
                thongtinhsba.thongtinbn.SOVAOVIEN,
                res.ttchamsoc_pcns_inputmach,
                res.ttchamsoc_pcns_inputnhietdo,
                res.ttchamsoc_pcns_inputhuyetap_tren,
                res.ttchamsoc_pcns_inputhuyetap_duoi,
                res.ttchamsoc_pcns_inputnhiptho,
                res.ttchamsoc_pcns_inputcannang,
                res.ttchamsoc_pcns_inputc4,
                res.ttchamsoc_pcns_inputc5,
                res.ttchamsoc_pcns_inputkhac,
                res.ttchamsoc_pcns_ngaygioplap,
                singletonObject.userId,
                singletonObject.makhoa
                ,"CMU_HSBA_CNS_INS"].join("```")
        }).done(function(data) {
            if(data == 1) {
                notifiToClient("Red","Trùng thời gian theo dõi")
            } else {
                $("#modalFormPhieuchucnangsong").modal("hide");
                notifiToClient("Green","Lưu thành công")
                loadDsPhieuChucNangSong();
                loadDSPhieuCNS();
                thongTinPhieuChucNangSongSauChinhSua = {
                    DVTT: singletonObject.dvtt,
                    SOVAOVIEN: thongtinhsba.thongtinbn.SOVAOVIEN,
                    NHIPTHO: $("#ttchamsoc_pcns_inputnhiptho").val(),
                    NHIETDO: $("#ttchamsoc_pcns_inputnhietdo").val(),
                    HUYET_AP: $("#ttchamsoc_pcns_inputhuyetap_tren").val() + "/" + $("#ttchamsoc_pcns_inputhuyetap_duoi").val(),
                    MACH: $("#ttchamsoc_pcns_inputmach").val(),
                    CANNANG: $("#ttchamsoc_pcns_inputcannang").val(),
                    CHISO4: $("#ttchamsoc_pcns_inputc4").val(),
                    CHISO5: $("#ttchamsoc_pcns_inputc5").val(),
                    CHISOKHAC: $("#ttchamsoc_pcns_inputkhac").val(),
                    NGAYTAO: $("#ttchamsoc_pcns_ngaygioplap").val(),
                    MA_NHANVIEN: singletonObject.userId
                };
                luuLogHSBATheoBN({
                    SOVAOVIEN: thongtinhsba.thongtinbn.SOVAOVIEN,
                    LOAI: LOGHSBALOAI.PHIEUCHUCNANGSONG.KEY,
                    NOIDUNGBANDAU: "",
                    NOIDUNGMOI: getContentLogPhieuchucnangsong(res),
                    USERID: singletonObject.userId,
                    ACTION: LOGHSBAACTION.INSERT.KEY,
                })
            }
        }).fail(function() {
            notifiToClient("Red","Lưu không thành công")
        }).always(function(){
            hideSelfLoading(idButton);
        })
    })
    $("#ttchamsoc_pcns_inphieu").click(function() {
        getFilesign769(
            "PHIEU_NOITRU_CHUCNANGSONG",
            $("#ttchamsoc_pcns_page").val(),
            -1,
            singletonObject.dvtt,
            thongtinhsba.thongtinbn.SOVAOVIEN,
            thongtinhsba.thongtinbn.SOVAOVIEN_DT,
            '-1',
            function (dataKySo) {
                if (dataKySo.length > 0) {
                    getCMUFileSigned769(dataKySo[0].KEYMINIO, "pdf")
                } else {
                    getUrlXemPhieuChucNangSong($("#ttchamsoc_pcns_page").val()).then(url => {
                        previewPdfDefaultModal(url, "ttchamsoc_preview_pcns")
                    }).catch(error => {
                        notifiToClient("Red", "Lỗi: " + error);
                    });
                }
            },
        );
    });

    $("#ttchamsoc_pcns_kyso").click(function() {
        var idButton = this.id;
        showSelfLoading(idButton);
        var dsPdf = [];
        $('#ttchamsoc_pcns_page option').each(function() {
            var list = thongtinhsba.thongtinbn.phieuchucnangsong;
            var indexPage = list.length < 18?
                list.length - 1: list.length -  (Number($(this).val()) - 1 )*18 - 1;
            var ret = list[indexPage];
            var arr = [singletonObject.dvtt,
                thongtinhsba.thongtinbn.MA_BENH_NHAN
                , thongtinhsba.thongtinbn.STT_BENHAN + ";"+ret.THOIGIANTHEODOI
                , thongtinhsba.thongtinbn.STT_DOTDIEUTRI
                , thongtinhsba.thongtinbn.TEN_BENH_NHAN
                , thongtinhsba.thongtinbn.TUOI_HT
                , thongtinhsba.thongtinbn.GIOI_TINH_HT
                , "", "",
                thongtinhsba.thongtinbn.ICD_HT];
            var url = "nan_phieuTheodoichucnangsong_report?sovaovien=" + thongtinhsba.thongtinbn.SOVAOVIEN +
                "&sovaovien_dt=" + thongtinhsba.thongtinbn.SOVAOVIEN_DT + "&ngaybdin=" +
                ret.THOIGIANTHEODOI + "&url=" + convertArray(arr);
            dsPdf.push({
                "url": [url],
                "name": "Phiếu chức năng sống trang " + $(this).val()
            });
        });
        loadAndCombinePDFs(dsPdf).then(data => {
            kySoChung({
                dvtt: singletonObject.dvtt,
                userId: singletonObject.userId,
                url: data,
                loaiGiay: "PHIEU_NOITRU_CHUCNANGSONG",
                maBenhNhan: String(thongtinhsba.thongtinbn.MA_BENH_NHAN),
                soBenhAn: thongtinhsba.thongtinbn.STT_BENHAN,
                soPhieuDichVu: -1,
                soVaoVien: thongtinhsba.thongtinbn.SOVAOVIEN,
                soVaoVienDT: thongtinhsba.thongtinbn.SOVAOVIEN_DT,
                keyword: "Kí và ghi họ tên",
                userId: singletonObject.userId,
                fileName: "Phiếu chức năng sống: " + thongtinhsba.thongtinbn.TEN_BENH_NHAN + " - " + thongtinhsba.thongtinbn.SOBENHAN,
            }, function(dataKySo) {
                $("#ttchamsoc-chucnangsong").click();
                hideSelfLoading(idButton);
            });
        }).catch(error => {
            notifiToClient("Red", "Có lỗi xảy ra:", error);
            hideSelfLoading(idButton);
        });
    });

    $("#ttchamsoc_pcns_huykyso").click(function() {
        confirmToClient("Bạn có chắc sẽ hủy ký số phiếu này?", function() {
            huykysoFilesign769("PHIEU_NOITRU_CHUCNANGSONG", '-1', singletonObject.userId, singletonObject.dvtt,
                thongtinhsba.thongtinbn.SOVAOVIEN, thongtinhsba.thongtinbn.SOVAOVIEN_DT, -1, function(data) {
                    $("#ttchamsoc-chucnangsong").click();
                })
        }, function () {

        })
    });

    /**
     * Phiếu đánh giá dinh dưỡng
     * */
    $("#phieutren18_chedoan").select2({multiple: true});
    $("#phieutren18_chedoan").val(null).trigger('change');

    $(".close").click(function() {
        resetModalForm("form_tren_18", "phieutren18_luuphieu");
        resetModalForm("form_nguoi_lon", "phieunguoilon_luuphieu");
        resetModalForm("form_san_phu", "phieusanphu_luuphieu");
        resetModalForm("form_tre_em", "phieutreem_luuphieu");
    });

    $("#ttchamsoc-danhgiadinhduong").click(function() {
        reloadDSDanhGiaDD();
        var list = $("#ttchamsoc_list_phieudanhgiadd");
        if(!list[0].grid) {
            $("#ttchamsoc_list_phieudanhgiadd").jqGrid({
                url: "cmu_getlist?url=" + convertArray([singletonObject.dvtt, thongtinhsba.thongtinbn.STT_BENHAN, 'CMU_GET_DSPHIEU_DANHGIA_DD']),
                datatype: "json",
                loadonce: true,
                height: 300,
                width: null,
                shrinkToFit: false,
                ignoreCase: true,
                caption: "Danh sách phiếu đánh giá dinh dưỡng",
                rowNum: 9999,
                colModel: [
                    {
                        name: "KYSO",
                        label: "Ký số",
                        align: 'left',
                        width: 100,
                        formatter: function (cellValue, options, rowData) {
                            if (rowData.KEYSIGN) {
                                return '<span class="cellWithoutBackground" style="font-weight:bold ;color: green">Đã ký</span>';
                            } else {
                                return '<span class="cellWithoutBackground" style="font-weight:bold ;color: red">Chưa ký</span>';
                            }
                        }
                    },
                    {name: "MA_PHIEU", label: "MA_PHIEU", hidden: true},
                    {name: "LOAI_PHIEU", label: "LOAI_PHIEU", hidden: true},
                    {name: "MA_BENH_NHAN", label: "MA_BENH_NHAN", hidden: true},
                    {name: "TEN_BENH_NHAN", label: "TEN_BENH_NHAN", hidden: true},
                    {name: "CAN_NANG", label: "CAN_NANG", hidden: true},
                    {name: "CAN_NANG_VAO_VIEN", label: "CAN_NANG_VAO_VIEN", hidden: true},
                    {name: "CHIEU_CAO", label: "CHIEU_CAO", hidden: true},
                    {name: "CAN_NANG_RA_VIEN", label: "CAN_NANG_RA_VIEN", hidden: true},
                    {name: "SANG_LOC_BMI", label: "SANG_LOC_BMI", hidden: true},
                    {name: "SANG_LOC_SUT_CAN", label: "SANG_LOC_SUT_CAN", hidden: true},
                    {name: "SANG_LOC_LUONG_AN", label: "SANG_LOC_LUONG_AN", hidden: true},
                    {name: "SANG_LOC_BENH_NANG", label: "SANG_LOC_BENH_NANG", hidden: true},
                    {name: "DANH_GIA_BMI", label: "DANH_GIA_BMI", hidden: true},
                    {name: "DANH_GIA_SUT_CAN", label: "DANH_GIA_SUT_CAN", hidden: true},
                    {name: "DANH_GIA_LUONG_AN", label: "DANH_GIA_LUONG_AN", hidden: true},
                    {name: "DANH_GIA_BENH_LY", label: "DANH_GIA_BENH_LY", hidden: true},
                    {name: "DANH_GIA_TONG_DIEM", label: "DANH_GIA_TONG_DIEM", hidden: true},
                    {name: "DUONG_NUOI_AN", label: "DUONG_NUOI_AN", hidden: true},
                    {name: "TAI_DANH_GIA", label: "TAI_DANH_GIA", hidden: true},
                    {name: "MOI_HOI_CHAN_DD", label: "MOI_HOI_CHAN_DD", hidden: true},
                    {name: "BAC_SI_DIEU_TRI", label: "BAC_SI_DIEU_TRI", hidden: true},
                    {name: "TUOI_THAI", label: "TUOI_THAI", hidden: true},
                    {name: "TUOI_THAI_THEO", label: "TUOI_THAI_THEO", hidden: true},
                    {name: "CHU_VI_VONG_CANH_TAY", label: "CHU_VI_VONG_CANH_TAY", hidden: true},
                    {name: "DANH_GIA_CV_VONG_CANH_TAY", label: "DANH_GIA_CV_VONG_CANH_TAY", hidden: true},
                    {name: "DANH_GIA_TD_TANG_CAN", label: "DANH_GIA_TD_TANG_CAN", hidden: true},
                    {name: "DANH_GIA_BENH_KEM_THEO", label: "DANH_GIA_BENH_KEM_THEO", hidden: true},
                    {name: "DANH_GIA_GIA_TRI", label: "DANH_GIA_GIA_TRI", hidden: true},
                    {name: "CHI_DINH_DD_KHAC", label: "CHI_DINH_DD_KHAC", hidden: true},
                    {name: "NGAY_TAO_PHIEU", label: "Ngày tạo phiếu", align: 'center', width: 150},
                    {
                        name: "LOAI_PHIEU_SHOWS",
                        label: "Loại phiếu",
                        align: 'left',
                        width: 150,
                        formatter: function (cellValue, options, rowData) {
                            if (rowData.LOAI_PHIEU == 'NGUOI_LON') {
                                return "Người lớn"
                            } else if(rowData.LOAI_PHIEU == 'TRE_EM') {
                                return "Trẻ em"
                            } else if(rowData.LOAI_PHIEU == 'SAN_PHU') {
                                return "Sản phụ"
                            } else if(rowData.LOAI_PHIEU == 'TREN_18') {
                                return "Trên 18 tuổi"
                            } else if(rowData.LOAI_PHIEU === "DGDD_NB_NOITRU") {
                                return "Sàng lọc và đánh giá dd người bệnh nội trú"
                            } else if(rowData.LOAI_PHIEU === "HD_CHEDO_DD_NB") {
                                return "Hướng dẫn chế độ dd cho người bệnh nội trú"
                            } else if(rowData.LOAI_PHIEU === "SL_DGDD_NHI_SS") {
                                return "Sàng lọc và đánh giá dd bệnh nhi sơ sinh"
                            } else if(rowData.LOAI_PHIEU === "SL_DGDD_NHI") {
                                return "Sàng lọc và đánh giá dd bệnh nhi"
                            } else {
                                return ""
                            }
                        }
                    },
                    {name: "BMI", label: "BMI", align: 'center', width: 100},
                    {name: "CHAN_DOAN", label: "Chẩn đoán", align: 'left', width: 300},
                    {name: "KET_LUAN", label: "Tình trạng dinh dưỡng", align: 'left', width: 200},
                    {name: "CHE_DO_AN", label: "Chế độ ăn", align: 'center', width: 300},
                    {
                        name: "DUONG_NUOI_AN_SHOWS",
                        label: "Đường nuôi ăn",
                        align: 'center',
                        width: 150,
                        formatter: function (cellValue, options, rowData) {
                            if (rowData.DUONG_NUOI_AN == "MIENG") {
                                return "Miệng"
                            } else if(rowData.DUONG_NUOI_AN == "ONG_THONG") {
                                return "Ống thông"
                            } else if(rowData.DUONG_NUOI_AN == "TINH_MACH") {
                                return "Tĩnh mạch"
                            } else {
                                return ""
                            }
                        }
                    },
                    {
                        name: "TAI_DANH_GIA_SHOWS",
                        label: "Tái đánh giá",
                        align: 'center',
                        width: 150,
                        formatter: function (cellValue, options, rowData) {
                            if (rowData.TAI_DANH_GIA == 7) {
                                return "Sau 7 ngày"
                            } else if(rowData.TAI_DANH_GIA == 3) {
                                return "Sau 3 ngày"
                            } else {
                                return ""
                            }
                        }
                    },
                    {name: "TEN_BAC_SI", label: "Bác sĩ điều trị", align: 'center', width: 300},
                    {name: "KET_LUAN_SANG_LOC", label: "Kết luận sàng lọc", align: 'center', width: 300},
                    {name: "KEYSIGN", label: "KEYSIGN", hidden: true},
                    {name: "NGAY_TREN_PHIEU", label: "NGAY_TREN_PHIEU", hidden: true},
                ],
                onRightClickRow: function (id1) {
                    if (id1) {
                        var ret = getThongtinRowSelected("ttchamsoc_list_phieudanhgiadd");
                        let tenPhieu = $('#select_ten_phieu option[value="' + ret.LOAI_PHIEU + '"]').text();
                        var items = {
                        }
                        $.contextMenu('destroy', '#ttchamsoc_list_phieudanhgiadd tr');
                        if (ret.KEYSIGN) {
                            if (["DGDD_NB_NOITRU", "SL_DGDD_NHI_SS", "SL_DGDD_NHI"].includes(ret.LOAI_PHIEU)) {
                                getFilesign769V3({
                                    dvtt: singletonObject.dvtt,
                                    soVaoVien: thongtinhsba.thongtinbn.SOVAOVIEN,
                                    soVaoVien_DT: thongtinhsba.thongtinbn.SOVAOVIEN_DT,
                                    kyHieuPhieu: "PHIEU_NOITRU_DANHGIADINHDUONG_DD",
                                    soPhieu: ret.MA_PHIEU,
                                    userId: "-1",
                                    soBenhAn: "-1",
                                    nghiepVu: ret.LOAI_PHIEU,
                                }, function(dataKySo) {
                                    if(dataKySo.length > 0) {
                                        items = {
                                            "xem": {name: '<p><i class="fa fa-eye text-primary" aria-hidden="true"></i> Xem</p>'},
                                            "huykyso2": {name: '<p><i class="fa fa-key text-danger" aria-hidden="true"></i> Hủy ký số Điều dưỡng</p>'},
                                            ...items
                                        }
                                    }
                                    else {
                                        items = {
                                            "xem": {name: '<p><i class="fa fa-eye text-primary" aria-hidden="true"></i> Xem</p>'},
                                            "kyso2": {name: '<p><i class="fa fa-key text-success" aria-hidden="true"></i> Ký số Điều dưỡng</p>'},
                                            "huykyso": {name: '<p><i class="fa fa-key text-danger" aria-hidden="true"></i> Hủy ký số</p>'},
                                            ...items
                                        }
                                    }
                                });
                            }
                            else {
                                items = {
                                    "xem": {name: '<p><i class="fa fa-eye text-primary" aria-hidden="true"></i> Xem</p>'},
                                    "huykyso": {name: '<p><i class="fa fa-key text-danger" aria-hidden="true"></i> Hủy ký số</p>'},
                                    ...items
                                }
                            }
                        } else {
                            items = {
                                "kyso": {name: '<p><i class="fa fa-key text-success" aria-hidden="true"></i> Ký số</p>'},
                                "xem": {name: '<p><i class="fa fa-eye text-primary" aria-hidden="true"></i> Xem</p>'},
                                "sua": {name: '<p><i class="fa fa-pencil-square-o text-primary" aria-hidden="true"></i> Sửa</p>'},
                                "xoa": {name: '<p><i class="fa fa-trash-o text-danger" aria-hidden="true"></i> Xóa</p>'},
                                ...items
                            }
                        }
                        $.contextMenu({
                            selector: '#ttchamsoc_list_phieudanhgiadd tr',
                            callback: function (key, options) {
                                if (key === "kyso") {
                                    if(singletonObject.userId != ret.BAC_SI_DIEU_TRI) {
                                        return notifiToClient("Red", "Bạn không có quyền ký số cho phiếu này");
                                    }
                                    var today = new Date();
                                    let layNgay = ret.NGAY_TREN_PHIEU ? moment(ret.NGAY_TREN_PHIEU, "DD/MM/YYYY HH:mm").date() : String(today.getDate()).padStart(2, '0'),
                                        layThang = ret.NGAY_TREN_PHIEU ? moment(ret.NGAY_TREN_PHIEU, "DD/MM/YYYY HH:mm").month() + 1 : String(today.getMonth() + 1).padStart(2, '0'),
                                        layNam = ret.NGAY_TREN_PHIEU ? moment(ret.NGAY_TREN_PHIEU, "DD/MM/YYYY HH:mm").year() : today.getFullYear();
                                    var params = {
                                        maphieu: ret.MA_PHIEU,
                                        mabenhnhan: ret.MA_BENH_NHAN,
                                        sovaovien: thongtinhsba.thongtinbn.SOVAOVIEN,
                                        ngay: layNgay,
                                        thang: layThang,
                                        nam: layNam
                                    }
                                    var url = "";
                                    if (ret.LOAI_PHIEU == "NGUOI_LON") {
                                        url = "cmu_in_cmu_phieudanhgiadd_nguoilon?type=pdf&" + $.param(params);
                                    } else if (ret.LOAI_PHIEU == "TRE_EM") {
                                        url ="cmu_in_cmu_phieudanhgiadd_treem?type=pdf&" + $.param(params);
                                    } else if (ret.LOAI_PHIEU == "SAN_PHU") {
                                        url ="cmu_in_cmu_phieudanhgiadd_sanphu?type=pdf&" + $.param(params);
                                    } else if (ret.LOAI_PHIEU == "TREN_18") {
                                        url = "cmu_in_cmu_phieudanhgiadd_tren18?type=pdf&" + $.param(params);
                                    } else if(ret.LOAI_PHIEU === "DGDD_NB_NOITRU") {
                                        url = "cmu_in_rp_phieu_sangloc_dgdd_nb_noitru?type=pdf&" + $.param(params);
                                    } else if(ret.LOAI_PHIEU === "HD_CHEDO_DD_NB") {
                                        url = "cmu_in_rp_phieu_huongdan_chedo_dd_nb_noitru?type=pdf&" + $.param(params);
                                    } else if(ret.LOAI_PHIEU === "SL_DGDD_NHI_SS") {
                                        url = "cmu_in_rp_phieu_sangloc_dgdd_benh_nhi_sosinh?type=pdf&" + $.param(params);
                                    } else if(ret.LOAI_PHIEU === "SL_DGDD_NHI") {
                                        url = "cmu_in_rp_phieu_sangloc_dgdd_benh_nhi?type=pdf&" + $.param(params);
                                    }
                                    kySoChung({
                                        dvtt: singletonObject.dvtt,
                                        userId: singletonObject.userId,
                                        url: url,
                                        loaiGiay: "PHIEU_NOITRU_DANHGIADINHDUONG",
                                        maBenhNhan: ret.MA_BENH_NHAN,
                                        soBenhAn: thongtinhsba.thongtinbn.STT_BENHAN,
                                        soPhieuDichVu: ret.MA_PHIEU,
                                        nghiepVu: ret.LOAI_PHIEU,
                                        soVaoVien: thongtinhsba.thongtinbn.SOVAOVIEN,
                                        soVaoVienDT: thongtinhsba.thongtinbn.SOVAOVIEN_DT,
                                        keyword: "BÁC SĨ ĐÁNH GIÁ",
                                        userId: singletonObject.userId,
                                    }, function(dataKySo) {
                                        reloadDSDanhGiaDD();
                                    });
                                }
                                if (key === "kyso2") {
                                    let today = new Date();
                                    let layNgay = ret.NGAY_TREN_PHIEU ? moment(ret.NGAY_TREN_PHIEU, "DD/MM/YYYY HH:mm").date() : String(today.getDate()).padStart(2, '0'),
                                        layThang = ret.NGAY_TREN_PHIEU ? moment(ret.NGAY_TREN_PHIEU, "DD/MM/YYYY HH:mm").month() + 1 : String(today.getMonth() + 1).padStart(2, '0'),
                                        layNam = ret.NGAY_TREN_PHIEU ? moment(ret.NGAY_TREN_PHIEU, "DD/MM/YYYY HH:mm").year() : today.getFullYear();
                                    let params = {
                                        ID: ret.MA_PHIEU,
                                        dvtt: singletonObject.dvtt,
                                        LOAI_PHIEU: ret.LOAI_PHIEU,
                                        ngay: layNgay,
                                        thang: layThang,
                                        nam: layNam,
                                        SOVAOVIEN: thongtinhsba.thongtinbn.SOVAOVIEN,
                                        SOVAOVIEN_DT: thongtinhsba.thongtinbn.SOVAOVIEN_DT,
                                        USER_ID: "-1"
                                    }
                                    getUrlDanhGiaDinhDuong(params).then(objReturn => {
                                        if (objReturn.isError == 0) {
                                            kySoChung({
                                                dvtt: singletonObject.dvtt,
                                                userId: singletonObject.userId,
                                                url: objReturn.url,
                                                loaiGiay: "PHIEU_NOITRU_DANHGIADINHDUONG_DD",
                                                maBenhNhan: ret.MA_BENH_NHAN,
                                                soBenhAn: thongtinhsba.thongtinbn.STT_BENHAN,
                                                soPhieuDichVu: ret.MA_PHIEU,
                                                nghiepVu: ret.LOAI_PHIEU,
                                                soVaoVien: thongtinhsba.thongtinbn.SOVAOVIEN,
                                                soVaoVienDT: thongtinhsba.thongtinbn.SOVAOVIEN_DT,
                                                keyword: "ĐIỀU DƯỠNG ĐÁNH GIÁ",
                                            }, function(_) {
                                                reloadDSDanhGiaDD();
                                            });
                                        } else {
                                            notifiToClient("Red", objReturn.message);
                                        }
                                    }).catch(error => {
                                        notifiToClient("Red", error.message || "Lỗi không xác định");
                                    });
                                }
                                if (key === "huykyso") {
                                    confirmToClient("Bạn có chắc sẽ hủy ký số phiếu này?", function() {
                                        huykysoFilesign769("PHIEU_NOITRU_DANHGIADINHDUONG", ret.MA_PHIEU, singletonObject.userId, singletonObject.dvtt,
                                            thongtinhsba.thongtinbn.SOVAOVIEN, thongtinhsba.thongtinbn.SOVAOVIEN_DT, -1, function(data) {
                                                reloadDSDanhGiaDD();
                                            })
                                    }, function () {

                                    })
                                }
                                if (key === "huykyso2") {
                                    confirmToClient("Bạn có chắc sẽ hủy ký số phiếu này?", function() {
                                        huykysoFilesign769("PHIEU_NOITRU_DANHGIADINHDUONG_DD", ret.MA_PHIEU, singletonObject.userId, singletonObject.dvtt,
                                            thongtinhsba.thongtinbn.SOVAOVIEN, thongtinhsba.thongtinbn.SOVAOVIEN_DT, -1, function(data) {
                                                reloadDSDanhGiaDD();
                                            })
                                    }, function () {

                                    })
                                }
                                if (key == "xem") {
                                    var today = new Date();
                                    let layNgay = ret.NGAY_TREN_PHIEU ? moment(ret.NGAY_TREN_PHIEU, "DD/MM/YYYY HH:mm").date() : String(today.getDate()).padStart(2, '0'),
                                        layThang = ret.NGAY_TREN_PHIEU ? moment(ret.NGAY_TREN_PHIEU, "DD/MM/YYYY HH:mm").month() + 1 : String(today.getMonth() + 1).padStart(2, '0'),
                                        layNam = ret.NGAY_TREN_PHIEU ? moment(ret.NGAY_TREN_PHIEU, "DD/MM/YYYY HH:mm").year() : today.getFullYear();
                                    var params = {
                                        ID: ret.MA_PHIEU,
                                        dvtt: singletonObject.dvtt,
                                        LOAI_PHIEU: ret.LOAI_PHIEU,
                                        ngay: layNgay,
                                        thang: layThang,
                                        nam: layNam,
                                        SOVAOVIEN: thongtinhsba.thongtinbn.SOVAOVIEN,
                                        SOVAOVIEN_DT: thongtinhsba.thongtinbn.SOVAOVIEN_DT,
                                        USER_ID: "-1"
                                    }
                                    getUrlDanhGiaDinhDuong(params).then(objReturn => {
                                        if (objReturn.isError == 0) {
                                            previewPdfDefaultModal(objReturn.url, 'preview_phieudgdd');
                                        } else {
                                            notifiToClient("Red", objReturn.message);
                                        }
                                    }).catch(error => {
                                        notifiToClient("Red", error.message || "Lỗi không xác định");
                                    });
                                }
                                if (key == "sua") {
                                    if (ret.LOAI_PHIEU == "NGUOI_LON") {
                                        pdgddnlDuLieuBanDau = ret;
                                        $("#modalFormDanhGiaDinhDuongNguoiLon").modal("show");
                                        addTextTitleModal("dgdd_title_nguoilon", "Phiếu đánh giá tình trạng dinh dưỡng người lớn");
                                        $("#phieunguoilon_luuphieu").attr("data-action", "CAP_NHAT");
                                        $("#phieunguoilon_luuphieu").html("<i class=\"fa fa-floppy-o\" aria-hidden=\"true\"></i> Cập nhật phiếu");
                                        $("#phieunguoilon_maphieu").val(ret.MA_PHIEU);
                                        $("#phieunguoilon_mabenhnhan").val(ret.MA_BENH_NHAN);
                                        $("#phieunguoilon_chandoan").val(ret.CHAN_DOAN);
                                        $("#phieunguoilon_cnvaovien").val(ret.CAN_NANG_VAO_VIEN);
                                        $("#phieunguoilon_chieucao").val(ret.CHIEU_CAO);
                                        $("#phieunguoilon_bmi").val(ret.BMI);
                                        $("#phieunguoilon_cnravien").val(ret.CAN_NANG_RA_VIEN);
                                        $("#phieunguoilon_sanlocbmi").val(ret.SANG_LOC_BMI);
                                        $("#phieunguoilon_sanlocsutcan").val(ret.SANG_LOC_SUT_CAN);
                                        $("#phieunguoilon_sanglocluongan").val(ret.SANG_LOC_LUONG_AN);
                                        $("#phieunguoilon_sanglocbenhnang").val(ret.SANG_LOC_BENH_NANG);
                                        if (ret.SANG_LOC_BMI == 1 || ret.SANG_LOC_SUT_CAN == 1 || ret.SANG_LOC_LUONG_AN == 1 || ret.SANG_LOC_BENH_NANG == 1) {
                                            $("#phieunguoilon_ketqua").html("Có - Tiếp tục bước 2, đánh giá");
                                        } else {
                                            $("#phieunguoilon_ketqua").html("Không - Tái sàng lọc sau 1 tuần")
                                        }
                                        $("#phieunguoilon_danhgiabmi").val(ret.DANH_GIA_BMI);
                                        $("#phieunguoilon_danhgiasutcan").val(ret.DANH_GIA_SUT_CAN);
                                        $("#phieunguoilon_danhgialuongan").val(ret.DANH_GIA_LUONG_AN);
                                        $("#phieunguoilon_danhgiabenhly").val(ret.DANH_GIA_BENH_LY);
                                        if(ret.DANH_GIA_TONG_DIEM < 2){
                                            $("#phieunguoilon_tongdiem").val(ret.DANH_GIA_TONG_DIEM);
                                            $("#phieunguoilon_ketluan").html("Không suy dinh dưỡng");
                                        } else if (ret.DANH_GIA_TONG_DIEM >= 2) {
                                            $("#phieunguoilon_tongdiem").val(ret.DANH_GIA_TONG_DIEM);
                                            $("#phieunguoilon_ketluan").html("Suy dinh dưỡng");
                                        }
                                        $("#phieunguoilon_chedoan").val(ret.CHE_DO_AN);
                                        $("#phieunguoilon_duongnuoi").val(ret.DUONG_NUOI_AN);
                                        $("#phieunguoilon_moihoichan").val(ret.MOI_HOI_CHAN_DD);
                                        $("#phieunguoilon_taidanhgia").val(ret.TAI_DANH_GIA);
                                        $("#phieunguoilon_bsdieutri").val(ret.BAC_SI_DIEU_TRI).select2();
                                        ret.NGAY_TREN_PHIEU ?
                                            $("#phieunguoilon_ngaytrenphieu").val(ret.NGAY_TREN_PHIEU) :
                                            $("#phieunguoilon_ngaytrenphieu").datetimepicker({dateFormat:"dd/MM/yyyy"}).datepicker("setDate",new Date());
                                    } else if(ret.LOAI_PHIEU == "TRE_EM") {
                                        pdgddteDuLieuBanDau = ret;
                                        console.log(ret);
                                        $("#modalFormDanhGiaDinhDuongTreEm").modal("show");
                                        addTextTitleModal("dgdd_title_treem", "Phiếu đánh giá tình trạng dinh dưỡng trẻ em");
                                        $('#phieutreem_luuphieu').attr("data-action", "CAP_NHAT");
                                        $('#phieutreem_luuphieu').html("<i class=\"fa fa-floppy-o\" aria-hidden=\"true\"></i> Cập nhật phiếu");
                                        $("#phieuntreem_maphieu").val(ret.MA_PHIEU);
                                        $("#phieuntreem_mabenhnhan").val(ret.MA_BENH_NHAN);
                                        $("#phieutreem_chandoan").val(ret.CHAN_DOAN);
                                        $("#phieutreem_cnvaovien").val(ret.CAN_NANG_VAO_VIEN);
                                        $("#phieutreem_chieucao").val(ret.CHIEU_CAO);
                                        $("#phieutreem_bmi").val(ret.BMI);
                                        $("#phieutreem_cnravien").val(ret.CAN_NANG_RA_VIEN);
                                        $("#phieutreem_danhgiabmi").val(ret.DANH_GIA_BMI);
                                        $("#phieutreem_danhgiasutcan").val(ret.DANH_GIA_SUT_CAN);
                                        $("#phieutreem_danhgialuongan").val(ret.DANH_GIA_LUONG_AN);
                                        $("#phieutreem_kqdanhgia").html(ret.KET_LUAN);
                                        $("#phieutreem_chedoan").val(ret.CHE_DO_AN);
                                        $("#phieutreem_duongnuoi").val(ret.DUONG_NUOI_AN);
                                        $("#phieutreem_moihoichan").val(ret.MOI_HOI_CHAN_DD);
                                        $("#phieutreem_taidanhgia").val(ret.TAI_DANH_GIA);
                                        $("#phieutreem_bsdieutri").val(ret.BAC_SI_DIEU_TRI).select2();
                                        ret.NGAY_TREN_PHIEU ?
                                            $("#phieutreem_ngaytrenphieu").val(ret.NGAY_TREN_PHIEU) :
                                            $("#phieutreem_ngaytrenphieu").datetimepicker({dateFormat:"dd/MM/yyyy"}).datepicker("setDate",new Date());
                                    } else if (ret.LOAI_PHIEU == "SAN_PHU") {
                                        pdgddspDuLieuBanDau = ret;
                                        $("#modalFormDanhGiaDinhDuongSanPhu").modal("show");
                                        addTextTitleModal("dgdd_title_sanphu", "Phiếu đánh giá tình trạng dinh dưỡng phụ nữ mang thai");
                                        $('#phieusanphu_luuphieu').attr("data-action", "CAP_NHAT");
                                        $('#phieusanphu_luuphieu').html("<i class=\"fa fa-floppy-o\" aria-hidden=\"true\"></i> Cập nhật phiếu");
                                        $("#phieusanphu_maphieu").val(ret.MA_PHIEU);
                                        $("#phieusanphu_mabenhnhan").val(ret.MA_BENH_NHAN);
                                        $("#phieusanphu_tuoithai").val(ret.TUOI_THAI);
                                        $("#phieusanphu_tuoithaitheo").val(ret.TUOI_THAI_THEO);
                                        $("#phieusanphu_chandoan").val(ret.CHAN_DOAN);
                                        $("#phieusanphu_cnvaovien").val(ret.CAN_NANG_VAO_VIEN);
                                        $("#phieusanphu_chieucao").val(ret.CHIEU_CAO);
                                        $("#phieusanphu_bmi").val(ret.BMI);
                                        $("#phieusanphu_cnravien").val(ret.CAN_NANG_RA_VIEN);
                                        $("#phieusanphu_cvvongcanhtay").val(ret.CHU_VI_VONG_CANH_TAY);
                                        $("#phieusanphu_danhgiabmi").val(ret.DANH_GIA_BMI);
                                        if (ret.DANH_GIA_BMI == '1' && ret.DANH_GIA_GIA_TRI == 'MUC_1') {
                                            $("#phieusanphu_danhgiabmi option[data-value='MUC_1']").prop("selected", true);
                                        } else if(ret.DANH_GIA_BMI == '1' && ret.DANH_GIA_GIA_TRI == 'MUC_2') {
                                            $("#phieusanphu_danhgiabmi option[data-value='MUC_2']").prop("selected", true);
                                        }
                                        $("#phieusanphu_chuvicanhtay").val(ret.DANH_GIA_CV_VONG_CANH_TAY);
                                        $("#phieusanphu_tdtangcan").val(ret.DANH_GIA_TD_TANG_CAN);
                                        $("#phieusanphu_benhkemtheo").val(ret.DANH_GIA_BENH_KEM_THEO);
                                        $("#phieusanphu_kqdanhgia").html(ret.KET_LUAN);
                                        $("#phieusanphu_chedoan").val(ret.CHE_DO_AN);
                                        $("#phieusanphu_duongnuoi").val(ret.DUONG_NUOI_AN);
                                        $("#phieusanphu_moihoichan").val(ret.MOI_HOI_CHAN_DD);
                                        $("#phieusanphu_taidanhgia").val(ret.TAI_DANH_GIA);
                                        $("#phieusanphu_bsdieutri").val(ret.BAC_SI_DIEU_TRI).select2();
                                        $("#phieusanphu_taidanhgia").val(ret.TAI_DANH_GIA);
                                        ret.NGAY_TREN_PHIEU ?
                                            $("#phieusanphu_ngaytrenphieu").val(ret.NGAY_TREN_PHIEU) :
                                            $("#phieusanphu_ngaytrenphieu").datetimepicker({dateFormat:"dd/MM/yyyy"}).datepicker("setDate",new Date());
                                    } else if (ret.LOAI_PHIEU == "TREN_18") {
                                        pdgddt18DuLieuBanDau = ret;
                                        $("#modalFormDanhGiaDinhDuongTren18").modal("show");
                                        addTextTitleModal("dgdd_title_tren18", "Phiếu đánh giá tình trạng dinh dưỡng > 18 tuổi");
                                        $('#phieutren18_luuphieu').attr("data-action", "CAP_NHAT");
                                        $('#phieutren18_luuphieu').html("<i class=\"fa fa-floppy-o\" aria-hidden=\"true\"></i> Cập nhật phiếu");
                                        $("#phieutren18_maphieu").val(ret.MA_PHIEU);
                                        $("#phieutren18_mabenhnhan").val(ret.MA_BENH_NHAN);
                                        $("#phieutren18_chandoan").val(ret.CHAN_DOAN);
                                        $("#phieutren18_cannang").val(ret.CAN_NANG_VAO_VIEN);
                                        $("#phieutren18_chieucao").val(ret.CHIEU_CAO);
                                        $("#phieutren18_bmi").val(ret.BMI);
                                        danhGiaBMI(ret.BMI, "phieutren18_ketqua");
                                        $("#phieutren18_sanlocbmi").val(ret.SANG_LOC_BMI);
                                        $("#phieutren18_sanlocsutcan").val(ret.SANG_LOC_SUT_CAN);
                                        $("#phieutren18_sanglocluongan").val(ret.SANG_LOC_LUONG_AN);
                                        $("#phieutren18_sanglocbenhnang").val(ret.SANG_LOC_BENH_NANG);
                                        ketQuaSangLoc(ret.SANG_LOC_BMI, ret.SANG_LOC_SUT_CAN, ret.SANG_LOC_LUONG_AN, ret.SANG_LOC_BENH_NANG);
                                        $("#phieutren18_danhgiabmi").val(ret.DANH_GIA_BMI);
                                        $("#phieutren18_danhgiasutcan").val(ret.DANH_GIA_SUT_CAN);
                                        $("#phieutren18_danhgialuongan").val(ret.DANH_GIA_LUONG_AN);
                                        $("#phieutren18_danhgiabenhly").val(ret.DANH_GIA_BENH_LY);
                                        danhGiaTinhTrangDD("phieutren18_danhgiabmi", "phieutren18_danhgiasutcan", "phieutren18_danhgialuongan", "phieutren18_danhgiabenhly", "Bình thường", "Suy dinh dưỡng");
                                        var cheDoAn = ret.CHE_DO_AN;
                                        var cheDoAnArr = cheDoAn.split(',');
                                        $("#phieutren18_chedoan").select2().val(cheDoAnArr).trigger('change');
                                        $("#phieutren18_taidanhgia").val(ret.TAI_DANH_GIA);
                                        $("#phieusanphu_chidinhkhac").val(ret.CHI_DINH_DD_KHAC);
                                        $("#phieutren18_bsdieutri").val(ret.BAC_SI_DIEU_TRI).select2();
                                        ret.NGAY_TREN_PHIEU ?
                                            $("#phieutren18_ngaytrenphieu").val(ret.NGAY_TREN_PHIEU) :
                                            $("#phieutren18_ngaytrenphieu").datetimepicker({dateFormat:"dd/MM/yyyy"}).datepicker("setDate",new Date());
                                    } else if(ret.LOAI_PHIEU === "DGDD_NB_NOITRU") {
                                        layDataPhieuDGDDChung(ret.MA_PHIEU, function (dataPhieu) {
                                            initModalDanhGiaDinhDuong(tenPhieu, "NBNoiTru", "SUA", dataPhieu);
                                        });
                                    } else if(ret.LOAI_PHIEU === "HD_CHEDO_DD_NB") {
                                        layDataPhieuDGDDChung(ret.MA_PHIEU, function (dataPhieu) {
                                            initModalDanhGiaDinhDuong(tenPhieu, "HDanCDoDD", "SUA", dataPhieu);
                                        });
                                    } else if(ret.LOAI_PHIEU === "SL_DGDD_NHI_SS") {
                                        layDataPhieuDGDDChung(ret.MA_PHIEU, function (dataPhieu) {
                                            initModalDanhGiaDinhDuong(tenPhieu, "BenhNhiSS", "SUA", dataPhieu);
                                        });
                                    } else if(ret.LOAI_PHIEU === "SL_DGDD_NHI") {
                                        layDataPhieuDGDDChung(ret.MA_PHIEU, function (dataPhieu) {
                                            initModalDanhGiaDinhDuong(tenPhieu, "BenhNhi", "SUA", dataPhieu);
                                        });
                                    }
                                }
                                if (key == "xoa") {
                                    pdgddt18DuLieuBanDau = ret;
                                    pdgddnlDuLieuBanDau = ret;
                                    pdgddspDuLieuBanDau = ret;
                                    pdgddteDuLieuBanDau = ret;
                                    var maPhieu = ret.MA_PHIEU;
                                    var confirmModal = $.confirm({
                                        title: 'Xác nhận!',
                                        type: 'orange',
                                        content: "Bạn có chắc chắn muốn xóa phiếu này?",
                                        buttons: {
                                            warning: {
                                                btnClass: 'btn-warning',
                                                text: "Tiếp tục",
                                                action: function() {
                                                    var arr = [maPhieu, singletonObject.dvtt]
                                                    var url = "";
                                                    if ([
                                                        "TREN_18", "DGDD_NB_NOITRU", "HD_CHEDO_DD_NB", "SL_DGDD_NHI_SS",
                                                        "SL_DGDD_NHI"
                                                    ].includes(ret.LOAI_PHIEU)) {
                                                        url = 'cmu_post_CMU_PHIEU_DANHGIA_DD_DELETE'
                                                    } else {
                                                        url = 'cmu_post_CMU_PHIEUDGDD_SANNHI_DELETE'
                                                    }
                                                    $.post(url, {
                                                        url: arr.join("```")
                                                    }).done(function (data) {
                                                        if (data === "1") {
                                                            var arr = [
                                                                'Xóa phiếu đánh giá dinh dưỡng ' + ret.MA_PHIEU + ' - ' + ret.DANH_GIA + ' của bệnh nhân '+ ret.TEN_BENH_NHAN + ' - ' + ret.MA_BENH_NHAN,
                                                                "Xóa phiếu đánh giá dinh dưỡng",
                                                                singletonObject.dvtt,
                                                                singletonObject.userId + ' - ' + singletonObject.user,
                                                                'his_logtruycap.ls_sudungchuongtrinh_insert'];
                                                            $.post("cmu_post", {
                                                                url: arr.join("```")
                                                            })
                                                            reloadDSDanhGiaDD();
                                                            if (ret.LOAI_PHIEU == "TREN_18")
                                                                luuLogPDGDD(3, 0);
                                                            else if (ret.LOAI_PHIEU == "NGUOI_LON")
                                                                luuLogPDGDD(3, 1);
                                                            else if (ret.LOAI_PHIEU == "SAN_PHU")
                                                                luuLogPDGDD(3, 2);
                                                            else if (ret.LOAI_PHIEU == "TRE_EM")
                                                                luuLogPDGDD(3, 3);

                                                            notifiToClient("Green", "Xóa phiếu đánh giá thành công");
                                                        } else {
                                                            notifiToClient("Red", "Lỗi xóa phiếu đánh giá")
                                                        }
                                                    })
                                                }
                                            },
                                            cancel: {
                                                text: "Huỷ bỏ",
                                                btnClass: "btn-default",
                                            },
                                        }
                                    });
                                }
                            },
                            items: items
                        })
                    }
                }
            });
        }

    });

    $("#dgdd_tao_phieu").on("click", function () {
        var loaiPhieu = $("#select_ten_phieu").val();
        var tenPhieu = $("#select_ten_phieu").find("option:selected").text();
        var getTenBN = thongtinhsba.thongtinbn.TEN_BENH_NHAN
        var getSoBA = thongtinhsba.thongtinbn.SOBENHAN

        if(loaiPhieu == "DGDD_VLG"){
            $("#modalFormDanhGiaDinhDuong_vlg").modal("show");
            addTextTitleModal("dgdd_title_vlg", tenPhieu);
            loadThongTinHanhChinhDD();
        }else if (loaiPhieu == "DGDD_TREN18") {
            $("#modalFormDanhGiaDinhDuongTren18").modal("show");
            $("#phieutren18_bsdieutri").val(singletonObject.userId).trigger("change");
            addTextTitleModal("dgdd_title_tren18", tenPhieu);
            $("#phieutren18_ngaytrenphieu").datetimepicker({dateFormat:"dd/MM/yyyy"}).datepicker("setDate",new Date());
        } else if (loaiPhieu == "DGDD_NGUOI_LON") {
            $("#phieunguoilon_bsdieutri").val(singletonObject.userId).trigger("change");
            $("#modalFormDanhGiaDinhDuongNguoiLon").modal("show");
            addTextTitleModal("dgdd_title_nguoilon", tenPhieu);
            $("#phieunguoilon_ngaytrenphieu").datetimepicker({dateFormat:"dd/MM/yyyy"}).datepicker("setDate",new Date());
        } else if(loaiPhieu == "DGDD_SAN_PHU") {
            $("#phieusanphu_bsdieutri").val(singletonObject.userId).trigger("change");
            $("#modalFormDanhGiaDinhDuongSanPhu").modal("show");
            addTextTitleModal("dgdd_title_sanphu", tenPhieu);
            $("#phieusanphu_ngaytrenphieu").datetimepicker({dateFormat:"dd/MM/yyyy"}).datepicker("setDate",new Date());
        } else if(loaiPhieu === "DGDD_NB_NOITRU") {
            initModalDanhGiaDinhDuong(tenPhieu, "NBNoiTru", "THEM", false);
        } else if(loaiPhieu === "HD_CHEDO_DD_NB") {
            initModalDanhGiaDinhDuong(tenPhieu, "HDanCDoDD", "THEM", false);
        } else if(loaiPhieu === "SL_DGDD_NHI_SS") {
            initModalDanhGiaDinhDuong(tenPhieu, "BenhNhiSS", "THEM", false);
        } else if(loaiPhieu === "SL_DGDD_NHI") {
            initModalDanhGiaDinhDuong(tenPhieu, "BenhNhi", "THEM", false);
        } else {
            $("#phieutreem_bsdieutri").val(singletonObject.userId).trigger("change");
            $("#modalFormDanhGiaDinhDuongTreEm").modal("show");
            addTextTitleModal("dgdd_title_treem", tenPhieu);
            $("#phieutreem_ngaytrenphieu").datetimepicker({dateFormat:"dd/MM/yyyy"}).datepicker("setDate",new Date());
        }

        var getIdDieuTri = $("#latest_id_dieutri").val();
        if (getIdDieuTri != "") {
            $.get("cmu_list_HSBA_CMU_TDT_GETBYID?url="+convertArray([
                singletonObject.dvtt,
                getIdDieuTri,
                thongtinhsba.thongtinbn.SOVAOVIEN,
                thongtinhsba.thongtinbn.SOVAOVIEN_DT
            ])).done(function (resData) {
                if (resData[0].CANNANG !== null && resData[0].CHIEUCAO !== null){
                    $("#phieutren18_cannang, #phieunguoilon_cnvaovien, #phieutreem_cnvaovien, #phieusanphu_cnvaovien").val(resData[0].CANNANG);
                    $("#phieutren18_chieucao, #phieunguoilon_chieucao, #phieutreem_chieucao, #phieusanphu_chieucao").val(resData[0].CHIEUCAO);
                    var canNang = resData[0].CANNANG;
                    var chieuCao = resData[0].CHIEUCAO;
                    var chieuCaoMet = chieuCao / 100
                    var bmi = (canNang / (chieuCaoMet * chieuCaoMet)).toFixed(2);
                    $("#phieutren18_bmi, #phieunguoilon_bmi, #phieutreem_bmi, #phieusanphu_bmi").val(bmi)
                }
            });
        }
    })
    /**
     * Phiếu đánh giá dinh dưỡng trên 18
     * */

    $("#phieutren18_cannang, #phieutren18_chieucao").on("keyup", function () {
        calculateBMI("phieutren18_cannang", "phieutren18_chieucao", "phieutren18_bmi");
        var bmi = $("#phieutren18_bmi").val();
        danhGiaBMI(bmi, "phieutren18_ketqua");
    });

    $("#phieutren18_sanlocbmi, #phieutren18_sanlocsutcan, #phieutren18_sanglocluongan, #phieutren18_sanglocbenhnang").on("change", function () {
        ketQuaSangLoc($("#phieutren18_sanlocbmi").val(), $("#phieutren18_sanlocsutcan").val(), $("#phieutren18_sanglocluongan").val(), $("#phieutren18_sanglocbenhnang").val());
    });

    $("#phieutren18_danhgiabmi, #phieutren18_danhgiasutcan, #phieutren18_danhgialuongan, #phieutren18_danhgiabenhly").on("change", function () {
        danhGiaTinhTrangDD("phieutren18_danhgiabmi", "phieutren18_danhgiasutcan", "phieutren18_danhgialuongan", "phieutren18_danhgiabenhly", "Bình thường", "Suy dinh dưỡng");
    });

    $("#phieutren18_luuphieu").on("click", function () {
        var btnAction = $('#phieutren18_luuphieu').attr("data-action");
        showSelfLoading("phieutren18_luuphieu");
        var tenBN = thongtinhsba.thongtinbn.TEN_BENH_NHAN;
        var gioiTinh = thongtinhsba.thongtinbn.GIOI_TINH;
        var ngaySinh = thongtinhsba.thongtinbn.NGAY_SINH;
        var namSinh = ngaySinh.slice(-4);
        var chanDoan = $("#phieutren18_chandoan").val();
        var canNang = $("#phieutren18_cannang").val();
        var chieuCao = $("#phieutren18_chieucao").val();
        var bmi = $("#phieutren18_bmi").val();

        var sangLocBmi = $("#phieutren18_sanlocbmi").val();
        var sangLocSutCan = $("#phieutren18_sanlocsutcan").val();
        var sangLocLuongAn = $("#phieutren18_sanglocluongan").val();
        var sangLocBenhNang = $("#phieutren18_sanglocbenhnang").val();
        var ketLuanSangLoc = $("#phieutren18_ketluansangloc").text();
        if (ketLuanSangLoc == "") {
            notifiToClient("Red", "Chưa có kết quả sàng lọc nguy cơ dinh dưỡng");
            hideSelfLoading("phieutren18_luuphieu");
            return false;
        }

        var danhGiaBmi = $("#phieutren18_danhgiabmi").val();
        var danhGiaSutCan = $("#phieutren18_danhgiasutcan").val();
        var danhGiaLuongAn = $("#phieutren18_danhgialuongan").val();
        var danhGiaBenhLy = $("#phieutren18_danhgiabenhly").val();
        var ketLuanDanhGia = $("#phieutren18_ketluan").text();
        if (ketLuanDanhGia == "") {
            notifiToClient("Red", "Chưa có kết luận đánh giá tình trạng dinh dưỡng");
            hideSelfLoading("phieutren18_luuphieu");
            return false;
        }

        var getCheDoAn = $('#phieutren18_chedoan').val();
        if(getCheDoAn) {
            var cheDoAn = getCheDoAn.map(function(item) {
                return item;
            }).join(',');
        }
        var chiDinhKhac = $("#phieusanphu_chidinhkhac").val();
        var taiDanhGia = $("#phieutren18_taidanhgia").val();
        var bacSiDieuTri = $("#phieutren18_bsdieutri").val();

        var ngayTrenPhieu = $("#phieutren18_ngaytrenphieu").val();

        var actionUrl;
        var url;
        if (btnAction == "THEM") {
            actionUrl = "cmu_post_CMU_PHIEU_DGDD_TREN18_INSERT";
            url = [
                singletonObject.dvtt,
                thongtinhsba.thongtinbn.STT_BENHAN,
                thongtinhsba.thongtinbn.MA_BENH_NHAN.toString(),
                tenBN,
                gioiTinh,
                namSinh,
                chanDoan,
                canNang,
                chieuCao,
                bmi,
                Number(sangLocBmi),
                Number(sangLocSutCan),
                Number(sangLocLuongAn),
                Number(sangLocBenhNang),
                ketLuanSangLoc,
                Number(danhGiaBmi),
                Number(danhGiaSutCan),
                Number(danhGiaLuongAn),
                Number(danhGiaBenhLy),
                ketLuanDanhGia,
                cheDoAn,
                Number(taiDanhGia),
                chiDinhKhac,
                Number(singletonObject.userId),
                singletonObject.ngayhientai,
                Number(bacSiDieuTri),
                ngayTrenPhieu,
                "TREN_18"
            ];
        } else {
            var maPhieu = $("#phieutren18_maphieu").val();
            actionUrl = "cmu_post_CMU_PHIEU_DGDD_TREN18_UPDATE";
            url = [
                maPhieu,
                singletonObject.dvtt,
                thongtinhsba.thongtinbn.MA_BENH_NHAN.toString(),
                chanDoan,
                canNang,
                chieuCao,
                bmi,
                Number(sangLocBmi),
                Number(sangLocSutCan),
                Number(sangLocLuongAn),
                Number(sangLocBenhNang),
                ketLuanSangLoc,
                Number(danhGiaBmi),
                Number(danhGiaSutCan),
                Number(danhGiaLuongAn),
                Number(danhGiaBenhLy),
                ketLuanDanhGia,
                cheDoAn,
                Number(taiDanhGia),
                chiDinhKhac,
                Number(singletonObject.userId),
                singletonObject.ngayhientai,
                Number(bacSiDieuTri),
                ngayTrenPhieu,
                "TREN_18"
            ];
        }

        $.post(actionUrl, {
            url: url.join('```')
        }).done(function (data) {
            if (data == "1") {
                luuLogPDGDD(1, 0);
                notifiToClient("Green", "Thêm phiếu thành công");
            } else {
                luuLogPDGDD(2, 0);
                notifiToClient("Green", "Cập nhật phiếu thành công");
            }
        }).always(function () {
            $("#modalFormDanhGiaDinhDuongTren18").modal("hide");
            resetModalForm("form_tren_18", "phieutren18_luuphieu");
            hideSelfLoading("phieutren18_luuphieu");
            reloadDSDanhGiaDD();
        });

    });

    $("#phieutren18_icd, #phieunguoilon_icd, #phieutreem_icd, #phieusanphu_icd").keypress(function(evt) {
        if(evt.keyCode == 13) {
            var icd = $(this).val().toUpperCase();
            getMotabenhly(icd, function(data) {
                var splitIcd = data.split("!!!")
                $("#phieutren18_chandoan").val(splitIcd[1])
                $("#phieunguoilon_chandoan").val(splitIcd[1])
                $("#phieutreem_chandoan").val(splitIcd[1])
                $("#phieusanphu_chandoan").val(splitIcd[1])
            });
        }
    })

    combgridTenICD("phieutren18_chandoan", function(item) {
        $("#phieutren18_icd").val(item.ICD.toUpperCase());
        $("#phieutren18_chandoan").val(item.MO_TA_BENH_LY);
    });

    combgridTenICD("phieunguoilon_chandoan", function(item) {
        $("#phieunguoilon_icd").val(item.ICD.toUpperCase());
        $("#phieunguoilon_chandoan").val(item.MO_TA_BENH_LY);
    });

    combgridTenICD("phieutreem_chandoan", function(item) {
        $("#phieutreem_icd").val(item.ICD.toUpperCase());
        $("#phieutreem_chandoan").val(item.MO_TA_BENH_LY);
    });

    combgridTenICD("phieusanphu_chandoan", function(item) {
        $("#phieusanphu_icd").val(item.ICD.toUpperCase());
        $("#phieusanphu_chandoan").val(item.MO_TA_BENH_LY);
    });

    /**
     * Phiếu đánh giá dinh dưỡng người lớn
     * */
    $("#phieunguoilon_cnvaovien, #phieunguoilon_chieucao").on("keyup", function () {
        calculateBMI("phieunguoilon_cnvaovien", "phieunguoilon_chieucao", "phieunguoilon_bmi");
    });

    $("#phieunguoilon_sanlocbmi, #phieunguoilon_sanlocsutcan, #phieunguoilon_sanglocluongan, #phieunguoilon_sanglocbenhnang").on("change", function () {
        if ($("#phieunguoilon_sanlocbmi").val() == "1" || $("#phieunguoilon_sanlocsutcan").val() == "1" || $("#phieunguoilon_sanglocluongan").val() == "1" || $("#phieunguoilon_sanglocbenhnang").val() == "1") {
            $("#phieunguoilon_ketqua").html("Có - Tiếp tục bước 2, đánh giá");
        } else {
            $("#phieunguoilon_ketqua").html("Không - Tái sàng lọc sau 1 tuần")
        }
    });

    $("#phieunguoilon_danhgiabmi, #phieunguoilon_danhgiasutcan, #phieunguoilon_danhgialuongan, #phieunguoilon_danhgiabenhly").on("change", function () {
        var danhGiaBMI = $("#phieunguoilon_danhgiabmi").val();
        var danhGiaSutCan = $("#phieunguoilon_danhgiasutcan").val();
        var danhGiaLuongAn = $("#phieunguoilon_danhgialuongan").val();
        var danhGiaBenhLy = $("#phieunguoilon_danhgiabenhly").val();
        var tongDiem = Number(danhGiaBMI) + Number(danhGiaSutCan) + Number(danhGiaLuongAn) + Number(danhGiaBenhLy);
        $("#phieunguoilon_tongdiem").val(tongDiem);
        if (tongDiem >= 2) {
            $("#phieunguoilon_ketluan").html("Suy dinh dưỡng")
        } else {
            $("#phieunguoilon_ketluan").html("Không suy dinh dưỡng")
        }
    });

    $("#phieunguoilon_luuphieu").on("click", function () {
        var btnAction = $('#phieunguoilon_luuphieu').attr("data-action");
        showSelfLoading("phieunguoilon_luuphieu");
        var tenBN = thongtinhsba.thongtinbn.TEN_BENH_NHAN;
        var gioiTinh = thongtinhsba.thongtinbn.GIOI_TINH;
        var ngaySinh = thongtinhsba.thongtinbn.NGAY_SINH;
        var namSinh = ngaySinh.slice(-4);
        var chanDoan = $("#phieunguoilon_chandoan").val();
        var canNangVaoVien = $("#phieunguoilon_cnvaovien").val();
        var chieuCao = $("#phieunguoilon_chieucao").val();
        var bmi = $("#phieunguoilon_bmi").val();
        var canNangRaVien = $("#phieunguoilon_cnravien").val();
        var sangLocBMI = $("#phieunguoilon_sanlocbmi").val();
        var sangLocSutCan = $("#phieunguoilon_sanlocsutcan").val();
        var sangLocLuongAn = $("#phieunguoilon_sanglocluongan").val();
        var sangLocBenhNang = $("#phieunguoilon_sanglocbenhnang").val();

        var taiSangLoc = $("#phieunguoilon_taisangloc").val();
        var danhGia = $("#phieunguoilon_danhgia").val();
        var ketLuanSangLoc = $("#phieunguoilon_ketqua").text();
        if(ketLuanSangLoc == "") {
            notifiToClient("Red", "Chưa có kết quả sàng lọc nguy cơ dinh dưỡng");
            hideSelfLoading("phieunguoilon_luuphieu");
            return false;
        }

        var danhGiaBMI = $("#phieunguoilon_danhgiabmi").val();
        var danhGiaSutCan = $("#phieunguoilon_danhgiasutcan").val();
        var danhGiaLuongAn = $("#phieunguoilon_danhgialuongan").val();
        var danhGiaBenhLy = $("#phieunguoilon_danhgiabenhly").val();
        var ketLuan = $("#phieunguoilon_ketluan").text();

        var tongDiem = $("#phieunguoilon_tongdiem").val();
        if(tongDiem == "") {
            notifiToClient("Red", "Chưa có kết quả đánh giá tình trạng dinh dưỡng");
            hideSelfLoading("phieunguoilon_luuphieu");
            return false;
        }

        var cheDoAn = $("#phieunguoilon_chedoan").val();
        var duongNuoi = $("#phieunguoilon_duongnuoi").val();
        var moiHoiChan = $("#phieunguoilon_moihoichan").val();
        var taiDanhGia = $("#phieunguoilon_taidanhgia").val();
        var bacSiDieuTri = $("#phieunguoilon_bsdieutri").val();

        var ngayTrenPhieu = $("#phieunguoilon_ngaytrenphieu").val();

        var actionUrl;
        var url;
        if (btnAction == "THEM") {
            actionUrl = "cmu_post_CMU_PHIEUDGDD_SANNHI_INSERT";
            url = [
                singletonObject.dvtt,
                thongtinhsba.thongtinbn.STT_BENHAN,
                thongtinhsba.thongtinbn.MA_BENH_NHAN.toString(),
                tenBN,
                gioiTinh,
                Number(namSinh),
                chanDoan,
                canNangVaoVien,
                chieuCao,
                bmi,
                canNangRaVien,
                Number(sangLocBMI),
                Number(sangLocSutCan),
                Number(sangLocLuongAn),
                Number(sangLocBenhNang),
                ketLuanSangLoc,
                Number(danhGiaBMI),
                Number(danhGiaSutCan),
                Number(danhGiaLuongAn),
                Number(danhGiaBenhLy),
                Number(tongDiem),
                ketLuan,
                cheDoAn,
                duongNuoi,
                Number(moiHoiChan),
                Number(taiDanhGia),
                Number(bacSiDieuTri),
                Number(singletonObject.userId),
                singletonObject.ngayhientai,
                ngayTrenPhieu,
                "NGUOI_LON"
            ];
        } else {
            var maPhieu = $("#phieunguoilon_maphieu").val();
            actionUrl = "cmu_post_CMU_PHIEUDGDD_SANNHI_UPDATE";
            url = [
                Number(maPhieu),
                singletonObject.dvtt,
                thongtinhsba.thongtinbn.MA_BENH_NHAN.toString(),
                "NGUOI_LON",
                chanDoan,
                canNangVaoVien,
                chieuCao,
                bmi,
                canNangRaVien,
                Number(sangLocBMI),
                Number(sangLocSutCan),
                Number(sangLocLuongAn),
                Number(sangLocBenhNang),
                ketLuanSangLoc,
                Number(danhGiaBMI),
                Number(danhGiaSutCan),
                Number(danhGiaLuongAn),
                Number(danhGiaBenhLy),
                Number(tongDiem),
                ketLuan,
                cheDoAn,
                duongNuoi,
                Number(moiHoiChan),
                Number(taiDanhGia),
                Number(bacSiDieuTri),
                ngayTrenPhieu,
            ];
        }

        $.post(actionUrl, {
            url: url.join('```')
        }).done(function (data) {
            if (data == "1") {
                luuLogPDGDD(1, 1);
                notifiToClient("Green", "Thêm phiếu thành công");
            } else {
                luuLogPDGDD(2, 1);
                notifiToClient("Green", "Cập nhật phiếu thành công");
            }
        }).always(function () {
            $("#modalFormDanhGiaDinhDuongNguoiLon").modal("hide");
            resetModalForm("form_nguoi_lon", "phieunguoilon_luuphieu");
            hideSelfLoading("phieunguoilon_luuphieu");
            reloadDSDanhGiaDD();
        });
    })
    /**
     * Phiếu đánh giá dinh dưỡng trẻ em
     * */
    $("#phieutreem_cnvaovien, #phieutreem_chieucao").on("keyup", function () {
        calculateBMI("phieutreem_cnvaovien", "phieutreem_chieucao", "phieutreem_bmi");
    });

    $("#phieutreem_danhgiabmi, #phieutreem_danhgiasutcan, #phieutreem_danhgialuongan").on("change", function () {
        var ketQua = calculateSuyDinhDuong("phieutreem_danhgiabmi", "phieutreem_danhgiasutcan", "phieutreem_danhgialuongan", "Bình thường", "Bình thường");
        $("#phieutreem_kqdanhgia").html(ketQua);
    })

    $("#phieutreem_luuphieu").on("click", function () {
        var btnAction = $('#phieutreem_luuphieu').attr("data-action");
        showSelfLoading("phieutreem_luuphieu");
        var tenBenhNhi = thongtinhsba.thongtinbn.TEN_BENH_NHAN;
        var gioiTinh = thongtinhsba.thongtinbn.GIOI_TINH;
        var ngaySinh = thongtinhsba.thongtinbn.NGAY_SINH;
        var namSinh = ngaySinh.slice(-4);
        var chanDoan = $("#phieutreem_chandoan").val();
        var canNangVaoVien = $("#phieutreem_cnvaovien").val();
        var chieuCao = $("#phieutreem_chieucao").val();
        var bmi = $("#phieutreem_bmi").val();
        var canNangRaVien = $("#phieutreem_cnravien").val();

        var danhGiaBMI = $("#phieutreem_danhgiabmi").val();
        var danhGiaSutCan = $("#phieutreem_danhgiasutcan").val();
        var danhGiaLuongAn = $("#phieutreem_danhgialuongan").val();
        var danhSutCanGT = $("#phieutreem_danhgiasutcan").find('option:selected').attr("data-value");
        var danhGiaTongDiem = Number(danhGiaBMI) + Number(danhGiaSutCan) + Number(danhGiaLuongAn);
        var ketLuan = $("#phieutreem_kqdanhgia").text();
        if (ketLuan == "") {
            notifiToClient("Red", "Chưa có kết quả sàng lọc nguy cơ dinh dưỡng");
            hideSelfLoading("phieutreem_luuphieu");
            return false;
        }

        var cheDoAn = $("#phieutreem_chedoan").val();
        var duongNuoi = $("#phieutreem_duongnuoi").val();
        var moiHoiChan = $("#phieutreem_moihoichan").val();
        var taiDanhGia = $("#phieutreem_taidanhgia").val();
        var bacSiDieuTri = $("#phieutreem_bsdieutri").val();

        var ngayTrenPhieu = $("#phieutreem_ngaytrenphieu").val();

        var actionUrl;
        var url;
        if (btnAction == "THEM") {
            actionUrl = "cmu_post_CMU_PHIEUDGDD_TREEM_INS";
            url = [
                singletonObject.dvtt,
                thongtinhsba.thongtinbn.STT_BENHAN,
                thongtinhsba.thongtinbn.MA_BENH_NHAN.toString(),
                tenBenhNhi,
                gioiTinh,
                Number(namSinh),
                chanDoan,
                canNangVaoVien,
                chieuCao,
                bmi,
                canNangRaVien,
                Number(danhGiaBMI),
                Number(danhGiaSutCan),
                danhSutCanGT,
                Number(danhGiaLuongAn),
                ketLuan,
                cheDoAn,
                duongNuoi,
                Number(moiHoiChan),
                taiDanhGia,
                Number(bacSiDieuTri),
                Number(singletonObject.userId),
                singletonObject.ngayhientai,
                ngayTrenPhieu,
                "TRE_EM"
            ];
        } else {
            var maPhieu = $("#phieuntreem_maphieu").val();
            actionUrl = "cmu_post_CMU_PHIEUDGDD_TREEM_UPT";
            url = [
                maPhieu,
                singletonObject.dvtt,
                thongtinhsba.thongtinbn.MA_BENH_NHAN.toString(),
                chanDoan,
                canNangVaoVien,
                chieuCao,
                bmi,
                canNangRaVien,
                Number(danhGiaBMI),
                Number(danhGiaSutCan),
                danhSutCanGT,
                Number(danhGiaLuongAn),
                ketLuan,
                cheDoAn,
                duongNuoi,
                Number(moiHoiChan),
                taiDanhGia,
                Number(bacSiDieuTri),
                ngayTrenPhieu,
                "TRE_EM"
            ];
        }

        $.post(actionUrl, {
            url: url.join('```')
        }).done(function (data) {
            if (data == "1") {
                luuLogPDGDD(1, 3);
                notifiToClient("Green", "Thêm phiếu thành công");
            } else {
                luuLogPDGDD(2, 3);
                notifiToClient("Green", "Cập nhật phiếu thành công");
            }
        }).always(function () {
            $("#modalFormDanhGiaDinhDuongTreEm").modal("hide");
            resetModalForm("form_tre_em", "phieutreem_luuphieu");
            hideSelfLoading("phieutreem_luuphieu");
            reloadDSDanhGiaDD();
        });
    });
    /**
     * Phiếu đánh giá dinh dưỡng sản phụ
     * */
    $("#phieusanphu_cnvaovien, #phieusanphu_chieucao").on("keyup", function () {
        calculateBMI("phieusanphu_cnvaovien", "phieusanphu_chieucao", "phieusanphu_bmi");
    });

    $("#phieusanphu_danhgiabmi, #phieusanphu_chuvicanhtay, #phieusanphu_tdtangcan, #phieusanphu_benhkemtheo").on("change", function () {
        var getBmi = $("#phieusanphu_danhgiabmi").val();
        var getCanhTay = $("#phieusanphu_chuvicanhtay").val();
        var getTdTangCan = $("#phieusanphu_tdtangcan").val();
        var getBenhKemTheo = $("#phieusanphu_benhkemtheo").val();
        var calcDiem = Number(getBmi) + Number(getCanhTay) + Number(getTdTangCan) + Number(getBenhKemTheo);
        var ketQua = "";
        if (calcDiem < 2) {
            ketQua = "Bình thường";
        } else if (calcDiem >= 2) {
            ketQua = "Có nguy cơ về dinh dưỡng";
        }
        $("#phieusanphu_kqdanhgia").html(ketQua);
    })

    $("#phieusanphu_luuphieu").on("click", function () {
        var btnAction = $('#phieusanphu_luuphieu').attr("data-action");
        showSelfLoading("phieusanphu_luuphieu");
        var tenBN = thongtinhsba.thongtinbn.TEN_BENH_NHAN;
        var gioiTinh = thongtinhsba.thongtinbn.GIOI_TINH;
        var ngaySinh = thongtinhsba.thongtinbn.NGAY_SINH;
        var namSinh = ngaySinh.slice(-4);
        var tuoiThai = $("#phieusanphu_tuoithai").val();
        var tuoiThaiTheo = $("#phieusanphu_tuoithaitheo").val();
        var chanDoan = $("#phieusanphu_chandoan").val();
        var canNangVaoVien = $("#phieusanphu_cnvaovien").val();
        var chieuCao = $("#phieusanphu_chieucao").val();
        var bmi = $("#phieusanphu_bmi").val();
        var canNangRaVien = $("#phieusanphu_cnravien").val();
        var chuViVongTay = $("#phieusanphu_cvvongcanhtay").val();

        var danhGiaBMI = $("#phieusanphu_danhgiabmi").val();
        var danhGiaBMIGiaTri = $("#phieusanphu_danhgiabmi").find('option:selected').attr("data-value");
        var danhGiaCvVongCanhTay = $("#phieusanphu_chuvicanhtay").val();
        var danhGiaTDTangCan = $("#phieusanphu_tdtangcan").val();
        var danhGiaBenhKemTheo = $("#phieusanphu_benhkemtheo").val();
        var danhGia = Number(danhGiaBMI) + Number(danhGiaCvVongCanhTay) + Number(danhGiaTDTangCan) + Number(danhGiaBenhKemTheo);
        var ketLuan = $("#phieusanphu_kqdanhgia").text();
        if (ketLuan == "") {
            notifiToClient("Red", "Chưa có kết quả sàng lọc nguy cơ dinh dưỡng");
            hideSelfLoading("phieusanphu_luuphieu");
            return false;
        }

        var cheDoAn = $("#phieusanphu_chedoan").val();
        var duongNuoi = $("#phieusanphu_duongnuoi").val();
        var moiHoiChan = $("#phieusanphu_moihoichan").val();
        var taiDanhGia = $("#phieusanphu_taidanhgia").val();
        var bacSiDieuTri = $("#phieusanphu_bsdieutri").val();

        var ngayTrenPhieu = $("#phieusanphu_ngaytrenphieu").val();

        var actionUrl;
        var url;
        if (btnAction == "THEM") {
            actionUrl = "cmu_post_CMU_PHIEUDGDD_SANPHU_INS";
            url = [
                singletonObject.dvtt,
                thongtinhsba.thongtinbn.STT_BENHAN,
                thongtinhsba.thongtinbn.MA_BENH_NHAN.toString(),
                tenBN,
                Number(gioiTinh),
                Number(namSinh),
                Number(tuoiThai),
                tuoiThaiTheo,
                chanDoan,
                canNangVaoVien,
                chieuCao,
                bmi,
                canNangRaVien,
                chuViVongTay,
                Number(danhGiaBMI),
                danhGiaBMIGiaTri,
                Number(danhGiaCvVongCanhTay),
                Number(danhGiaTDTangCan),
                Number(danhGiaBenhKemTheo),
                ketLuan,
                cheDoAn,
                duongNuoi,
                Number(moiHoiChan),
                Number(taiDanhGia),
                Number(bacSiDieuTri),
                Number(singletonObject.userId),
                singletonObject.ngayhientai,
                ngayTrenPhieu,
                "SAN_PHU"
            ];
        } else {
            var maPhieu = $("#phieusanphu_maphieu").val();
            actionUrl = "cmu_post_CMU_PHIEUDGDD_SANPHU_UPT";
            url = [
                maPhieu,
                singletonObject.dvtt,
                thongtinhsba.thongtinbn.MA_BENH_NHAN.toString(),
                Number(tuoiThai),
                tuoiThaiTheo,
                chanDoan,
                canNangVaoVien,
                chieuCao,
                bmi,
                canNangRaVien,
                chuViVongTay,
                Number(danhGiaBMI),
                danhGiaBMIGiaTri,
                Number(danhGiaCvVongCanhTay),
                Number(danhGiaTDTangCan),
                Number(danhGiaBenhKemTheo),
                ketLuan,
                cheDoAn,
                duongNuoi,
                Number(moiHoiChan),
                Number(taiDanhGia),
                Number(bacSiDieuTri),
                ngayTrenPhieu,
                "SAN_PHU"
            ];
        }

        $.post(actionUrl, {
            url: url.join('```')
        }).done(function (data) {
            if (data == "1") {
                luuLogPDGDD(1, 2);
                notifiToClient("Green", "Thêm phiếu thành công");
            } else {
                luuLogPDGDD(2, 2);
                notifiToClient("Green", "Cập nhật phiếu thành công");
            }
        }).always(function () {
            $("#modalFormDanhGiaDinhDuongSanPhu").modal("hide");
            resetModalForm("form_san_phu", "phieusanphu_luuphieu");
            hideSelfLoading("phieusanphu_luuphieu");
            reloadDSDanhGiaDD();
        });

    })

    $("#action_mauphieuphieucs").click(function() {
        var element = $("#mau_danhsachmaujson_wrap");
        element.attr("function-add", "themmauphieuphieucs");
        element.attr("function-chinhsua", "chinhsuamauphieuphieucs");
        element.attr("function-select", "chonmauphieuphieucs");
        element.attr("function-getdata", "getdatamauphieuphieucs");
        element.attr("function-validate", "formioPhieuchamsocValidate");
        element.attr("data-key", "PHIEU_CHAM_SOC");
        $("#modalMauChungJSON").modal("show");
        $.loadDanhSachMauChungJSON("PHIEU_CHAM_SOC")
    })
    $.extend({
        themmauphieuphieucs: function () {
            getJSONFormIOMauPhieuchamsoc({})
        },
        chinhsuamauphieuphieucs: function (rowSelect) {
            var json = JSON.parse(rowSelect.NOIDUNG);
            var dienbien = json[0].value;
            var ylenh = json[1].value;

            getJSONFormIOMauPhieuchamsoc({
                ID: rowSelect.ID,
                TENMAU: rowSelect.TENMAU,
                DIENBIEN: dienbien,
                YLENH: ylenh
            })
        },
        chonmauphieuphieucs: function (rowSelect) {
            var json = JSON.parse(rowSelect.NOIDUNG);
            var dienbien = json[0].value;
            var ylenh = json[1].value;
            $("#phieucs_dienbien").val(dienbien);
            $("#phieucs_ylenh").val(ylenh);
            $("#modalMauChungJSON").modal("hide");
        },
        getdatamauphieuphieucs: function () {
            return {
                ID: formioMauchung.submission.data.ID,
                TENMAU: formioMauchung.submission.data.TENMAU,
                NOIDUNG: JSON.stringify([
                    {
                        "label": "Diễn biến",
                        "value": formioMauchung.submission.data.DIENBIEN,
                    },
                    {
                        "label": "Y lệnh",
                        "value": formioMauchung.submission.data.YLENH,
                    }
                ]),
                KEYMAUCHUNG: "PHIEU_CHAM_SOC"

            };
        },
        formioPhieuchamsocValidate: function() {
            formioMauchung.emit("checkValidity");
            if (!formioMauchung.checkValidity(null, false, null, true)) {
                return false;
            }
            return true;
        },
        getContentLogPhieuchucnangsong: function(data) {
            return getContentLogPhieuchucnangsong(data);
        }
    })

    $("#formPhieuchucnangsong").validate({
        rules: {
            ttchamsoc_pcns_ngaygioplap:{
                validDateTime: true,
                required: true
            },
            ttchamsoc_pcns_inputmach: {
                range: [0,200]
            },
            ttchamsoc_pcns_inputnhietdo: {
                range: [35,43]
            },
            // ttchamsoc_pcns_inputhuyetap_tren: {
            //     range: [0, 300]
            // },
            // ttchamsoc_pcns_inputhuyetap_duoi: {
            //     range: [0, 300]
            // },
            ttchamsoc_pcns_inputcannang: {
                range: [0.1, 300]
            },
            // ttchamsoc_pcns_inputnhiptho: {
            //     range: [0,300]
            // }
        }
    })

    function loadDSPhieuCNS() {
        var url = "cmu_list_cmu_list_phieucns?url="+convertArray([singletonObject.dvtt, thongtinhsba.thongtinbn.SOVAOVIEN]);
        $.get(url).done(function(data) {
            thongtinhsba.thongtinbn.phieuchucnangsong = data;
            $("#ttchamsoc_pcns_page").html("")
            var totalPage = Math.ceil(data.length / 18);
            var page = 1;
            for(var i = 1; i <= totalPage; i++) {
                $("#ttchamsoc_pcns_page").append('<option value="'+i+'">'+page+'</option>')
                page++;
            }
        })
    }

    function getJSONFormIOMauPhieuchamsoc(dataForm) {
        var jsonForm = getJSONObjectForm([
            {
                "label": "ID",
                "key": "ID",
                "type": "textfield",
                others: {
                    hidden: true
                }
            },
            {
                "label": "Tên mẫu",
                "key": "TENMAU",
                "type": "textarea",
                validate: {
                    required: true
                },
                others: {
                    "labelPosition": "left-left",
                    "labelWidth": 10
                }
            },
            {
                "label": "Diễn biến",
                "key": "DIENBIEN",
                "type": "textarea",
                others: {
                    "labelPosition": "left-left",
                    "labelWidth": 10
                }
            },
            {
                "label": "Y lệnh",
                "key": "YLENH",
                "type": "textarea",
                others: {
                    "labelPosition": "left-left",
                    "labelWidth": 10
                }
            }
        ])
        Formio.createForm(document.getElementById('formChiTietMauChungJSON'),
            jsonForm,{}
        ).then(function(form) {
            formioMauchung = form;
            formioMauchung.submission = {
                data: {
                    ...dataForm
                }
            }
        });
    }

    //LƯU LOG PHIẾU ĐÁNH GIÁ DINH DƯỠNG
    //pdgddType: có các giá trị 0, 1, 2, 3 tương đương với phiếu ĐGDD > 18, người lớn, phụ nữ mang thai, trẻ em
    function luuLogPDGDD(type, pdgddType) {
        let formData = {};
        let maPhieu;
        if (pdgddType == 0) {
            formData = getPDGDDData(keyLogPDGDDTren18, 0, pdgddt18DuLieuBanDau, 0);
            maPhieu = `Mã phiếu: ${$('#phieutren18_maphieu').val()}; `
        }
        else if (pdgddType == 1) {
            formData = getPDGDDData(keyLogPDGDDNguoiLon, 0, pdgddnlDuLieuBanDau, 1);
            maPhieu = `Mã phiếu: ${$('#phieunguoilon_maphieu').val()}; `
        }
        else if (pdgddType == 2) {
            formData = getPDGDDData(keyLogPDGDDSanPhu, 0, pdgddspDuLieuBanDau, 2);
            maPhieu = `Mã phiếu: ${$('#phieusanphu_maphieu').val()}; `
        }
        else if (pdgddType == 3) {
            formData = getPDGDDData(keyLogPDGDDTreEm, 0, pdgddteDuLieuBanDau, 3);
            maPhieu = `Mã phiếu: ${$('#phieuntreem_maphieu').val()}; `
        }

        let logContent = {
            SOVAOVIEN: thongtinhsba.thongtinbn.SOVAOVIEN,
            LOAI: '',
            NOIDUNGBANDAU: '',
            NOIDUNGMOI: '',
            USERID: singletonObject.userId,
            ACTION: LOGHSBAACTION.INSERT.KEY,
        }

        if (type == 1) {
            if (pdgddType == 0) {
                logContent.LOAI = LOGHSBALOAI.PHIEUDANHGIATINHTRANGDINHDUONG.TREN18TUOI.KEY;
                logContent.NOIDUNGMOI = getStringPDGDD(formData, keyLogPDGDDTren18);
                luuLogHSBATheoBN(logContent);
            }
            else if (pdgddType == 1) {
                logContent.LOAI = LOGHSBALOAI.PHIEUDANHGIATINHTRANGDINHDUONG.NGUOILON.KEY;
                logContent.NOIDUNGMOI = getStringPDGDD(formData, keyLogPDGDDNguoiLon);
                luuLogHSBATheoBN(logContent);
            }
            else if (pdgddType == 2) {
                logContent.LOAI = LOGHSBALOAI.PHIEUDANHGIATINHTRANGDINHDUONG.SANPHU.KEY;
                logContent.NOIDUNGMOI = getStringPDGDD(formData, keyLogPDGDDSanPhu);
                luuLogHSBATheoBN(logContent);
            }
            else if (pdgddType == 3) {
                logContent.LOAI = LOGHSBALOAI.PHIEUDANHGIATINHTRANGDINHDUONG.TREEM.KEY;
                logContent.NOIDUNGMOI = getStringPDGDD(formData, keyLogPDGDDTreEm);
                luuLogHSBATheoBN(logContent);
            }
        } else if (type == 2) {
            if (pdgddType == 0) {
                if (pdgddt18DuLieuBanDau) {
                    let noiDungBanDauEdit = getPDGDDData(keyLogPDGDDTren18, 1, pdgddt18DuLieuBanDau, 0);
                    if (noiDungBanDauEdit) {
                        let  diff = findDifferencesBetweenObjects(noiDungBanDauEdit, formData)
                        if (Object.keys(diff).length > 0) {
                            let noiDungThayDoi = {};
                            Object.keys(diff).forEach(key => noiDungThayDoi[key] = diff[key][1]);

                            logContent.LOAI = LOGHSBALOAI.PHIEUDANHGIATINHTRANGDINHDUONG.TREN18TUOI.KEY;
                            logContent.NOIDUNGBANDAU = maPhieu + getStringPDGDD(noiDungBanDauEdit, keyLogPDGDDTren18);
                            logContent.NOIDUNGMOI = maPhieu + getStringPDGDD(noiDungThayDoi, keyLogPDGDDTren18);
                            luuLogHSBATheoBN(logContent);
                        }
                    }
                }
            }
            else if (pdgddType == 1) {
                if (pdgddnlDuLieuBanDau) {
                    let noiDungBanDauEdit = getPDGDDData(keyLogPDGDDNguoiLon, 1, pdgddnlDuLieuBanDau, 1);
                    if (noiDungBanDauEdit) {
                        let  diff = findDifferencesBetweenObjects(noiDungBanDauEdit, formData)
                        if (Object.keys(diff).length > 0) {
                            let noiDungThayDoi = {};
                            Object.keys(diff).forEach(key => noiDungThayDoi[key] = diff[key][1]);

                            logContent.LOAI = LOGHSBALOAI.PHIEUDANHGIATINHTRANGDINHDUONG.NGUOILON.KEY;
                            logContent.NOIDUNGBANDAU = maPhieu + getStringPDGDD(noiDungBanDauEdit, keyLogPDGDDNguoiLon);
                            logContent.NOIDUNGMOI = maPhieu + getStringPDGDD(noiDungThayDoi, keyLogPDGDDNguoiLon);
                            luuLogHSBATheoBN(logContent);
                        }
                    }
                }
            }
            else if (pdgddType == 2) {
                if (pdgddspDuLieuBanDau) {
                    let noiDungBanDauEdit = getPDGDDData(keyLogPDGDDSanPhu, 1, pdgddspDuLieuBanDau, 2);
                    if (noiDungBanDauEdit) {
                        let  diff = findDifferencesBetweenObjects(noiDungBanDauEdit, formData)
                        if (Object.keys(diff).length > 0) {
                            let noiDungThayDoi = {};
                            Object.keys(diff).forEach(key => noiDungThayDoi[key] = diff[key][1]);

                            logContent.LOAI = LOGHSBALOAI.PHIEUDANHGIATINHTRANGDINHDUONG.SANPHU.KEY;
                            logContent.NOIDUNGBANDAU = maPhieu + getStringPDGDD(noiDungBanDauEdit, keyLogPDGDDSanPhu);
                            logContent.NOIDUNGMOI = maPhieu + getStringPDGDD(noiDungThayDoi, keyLogPDGDDSanPhu);
                            luuLogHSBATheoBN(logContent);
                        }
                    }
                }
            }
            else if (pdgddType == 3) {
                if (pdgddteDuLieuBanDau) {
                    let noiDungBanDauEdit = getPDGDDData(keyLogPDGDDTreEm, 1, pdgddteDuLieuBanDau, 3);
                    if (noiDungBanDauEdit) {
                        let  diff = findDifferencesBetweenObjects(noiDungBanDauEdit, formData)
                        if (Object.keys(diff).length > 0) {
                            let noiDungThayDoi = {};
                            Object.keys(diff).forEach(key => noiDungThayDoi[key] = diff[key][1]);

                            logContent.LOAI = LOGHSBALOAI.PHIEUDANHGIATINHTRANGDINHDUONG.TREEM.KEY;
                            logContent.NOIDUNGBANDAU = maPhieu + getStringPDGDD(noiDungBanDauEdit, keyLogPDGDDTreEm);
                            logContent.NOIDUNGMOI = maPhieu + getStringPDGDD(noiDungThayDoi, keyLogPDGDDTreEm);
                            luuLogHSBATheoBN(logContent);
                        }
                    }
                }
            }
        } else if (type == 3) {
            if (pdgddType == 0) {
                if (pdgddt18DuLieuBanDau) {
                    let noiDungBanDauDelete = getPDGDDData(keyLogPDGDDTren18, 1, pdgddt18DuLieuBanDau, 0);
                    if (noiDungBanDauDelete) {
                        logContent.LOAI = LOGHSBALOAI.PHIEUDANHGIATINHTRANGDINHDUONG.TREN18TUOI.KEY;
                        logContent.NOIDUNGBANDAU = maPhieu + getStringPDGDD(noiDungBanDauDelete, keyLogPDGDDTren18);
                        logContent.NOIDUNGMOI = '';
                        luuLogHSBATheoBN(logContent);
                    }
                }
            }
            else if (pdgddType == 1) {
                if (pdgddnlDuLieuBanDau) {
                    let noiDungBanDauDelete = getPDGDDData(keyLogPDGDDNguoiLon, 1, pdgddnlDuLieuBanDau, 1);
                    if (noiDungBanDauDelete) {
                        logContent.LOAI = LOGHSBALOAI.PHIEUDANHGIATINHTRANGDINHDUONG.NGUOILON.KEY;
                        logContent.NOIDUNGBANDAU = maPhieu + getStringPDGDD(noiDungBanDauDelete, keyLogPDGDDNguoiLon);
                        logContent.NOIDUNGMOI = '';
                        luuLogHSBATheoBN(logContent);
                    }
                }
            }
            else if (pdgddType == 2) {
                if (pdgddspDuLieuBanDau) {
                    let noiDungBanDauDelete = getPDGDDData(keyLogPDGDDSanPhu, 1, pdgddspDuLieuBanDau, 2);
                    if (noiDungBanDauDelete) {
                        logContent.LOAI = LOGHSBALOAI.PHIEUDANHGIATINHTRANGDINHDUONG.SANPHU.KEY;
                        logContent.NOIDUNGBANDAU = maPhieu + getStringPDGDD(noiDungBanDauDelete, keyLogPDGDDSanPhu);
                        logContent.NOIDUNGMOI = '';
                        luuLogHSBATheoBN(logContent);
                    }
                }
            }
            else if (pdgddType == 3) {
                if (pdgddteDuLieuBanDau) {
                    let noiDungBanDauDelete = getPDGDDData(keyLogPDGDDTreEm, 1, pdgddteDuLieuBanDau, 3);
                    if (noiDungBanDauDelete) {
                        logContent.LOAI = LOGHSBALOAI.PHIEUDANHGIATINHTRANGDINHDUONG.TREEM.KEY;
                        logContent.NOIDUNGBANDAU = maPhieu + getStringPDGDD(noiDungBanDauDelete, keyLogPDGDDTreEm);
                        logContent.NOIDUNGMOI = '';
                        luuLogHSBATheoBN(logContent);
                    }
                }
            }
        }
    }

    $("#phieu_vlg_inphieu").click(function (){
        printPhieuDanhGiaDinhDuong();
    });

    $("#phieu_vlg_luuphieu").click(function (){
        updatePhieuDinhDuong();
    });

    function setValueRadio(name, value){
        $('input:radio[name='+name+'][value='+value+']').prop('checked', true);
    }

    function loadThongTinHanhChinhDD(){
        $.ajax({
            url: 'cmu_list_VLG_GET_DGTT_DINHDUONG?url='+convertArray([singletonObject.dvtt, thongtinhsba.thongtinbn.SOVAOVIEN, thongtinhsba.thongtinbn.SOVAOVIEN_DT , thongtinhsba.thongtinbn.NHAPVIENVAOKHOA]),
            type: 'GET',
            datatype: 'json',
            complete: function (data) {
                $('#HoTenBenhNhanDD').html('').append($('<span style="font-weight: bold; margin-left: 5px" />').text(data.responseJSON.TEN_BENH_NHAN));
                $('#TuoiBenhNhanDD').html('').append($('<span style="margin-left: 5px" />').text(data.responseJSON[0].TUOI));
                $('#GioiTinhDD').html('').append($('<span style="margin-left: 5px" />').text(data.responseJSON[0].GIOI_TINH));
                $('#NgayNhapVienDD').html('').append($('<span style="margin-left: 5px" />').text(data.responseJSON[0].NGAY_NHAP_VIEN));
                $('#ChanDoanDD').html('').append($('<span style="margin-left: 5px" />').text(data.responseJSON[0].CHAN_DOAN));
                $('#CanNangDD').val(data.responseJSON[0].CANNANG);
                $('#ChieuCaoDD').val(data.responseJSON[0].CHIEUCAO);
                $('#ChiSoBmiDD').val(data.responseJSON[0].BMI);
                if(!isNaN(parseFloat($("#ChiSoBmiDD").val()))){
                    let bmi = parseFloat($("#ChiSoBmiDD").val()) > 20.5 ? "1" :
                        (parseFloat($("#ChiSoBmiDD").val()) >= 18.5 && parseFloat($("#ChiSoBmiDD").val()) <= 20.5) ? "2" :
                            "3"
                    ;
                    setValueRadio('DanhGiaBmiDD', bmi);
                    setValueRadio('SutCan3thangDD2', data.responseJSON[0].SUTCAN_3THANG2);
                    setValueRadio('AnKem5ngayDD', data.responseJSON[0].ANKEM_5NGAY);
                    setValueRadio('DanhGiaTongDiemDD', data.responseJSON[0].DANHGIA_TONGDIEM);
                    setValueRadio('SutCan3thangDD3', data.responseJSON[0].SUTCAN_3THANG3);
                    setValueRadio('ThayDoiCheDoAnDD', data.responseJSON[0].THAYDOI_CHEDO_AN);
                    setValueRadio('TrieuChungDinhDuong', data.responseJSON[0].TRIEUCHUNG_DD);
                    setValueRadio('ChucNangCoTheDD', data.responseJSON[0].CHUCNANG_COTHE);
                    setValueRadio('DauHieuThucTheDD', data.responseJSON[0].DAUHIEU_THUCTHE);
                    setValueRadio('TongSoDiemSGA', data.responseJSON[0].TONGDIEM_SGA);
                    setValueRadio('DanhGiaTTDD', data.responseJSON[0].DANHGIA_TTDD);
                    setValueRadio('KetLuaDD', data.responseJSON[0].KETLUAN);
                    setValueRadio('ChiDinhDuongNuoiDD', data.responseJSON[0].CD_DUONG_NDUONG);
                }
            }
        });
    }

    function updatePhieuDinhDuong(){
        $.ajax({
            type: 'POST',
            url: 'cmu_post_VLG_UPDATE_DGTT_DINHDUONG?url=' +  convertArray([
                singletonObject.dvtt,
                thongtinhsba.thongtinbn.SOVAOVIEN,
                thongtinhsba.thongtinbn.SOVAOVIEN_DT,
                $("input[name='SutCan3thangDD2']:checked").val() == undefined ? "0" : $("input[name='SutCan3thangDD2']:checked").val(),
                $("input[name='AnKem5ngayDD']:checked").val() == undefined ? "0" : $("input[name='AnKem5ngayDD']:checked").val(),
                $("input[name='DanhGiaTongDiemDD']:checked").val() == undefined ? "0" : $("input[name='DanhGiaTongDiemDD']:checked").val(),
                $("input[name='SutCan3thangDD3']:checked").val() == undefined ? "0" : $("input[name='SutCan3thangDD3']:checked").val(),
                $("input[name='ThayDoiCheDoAnDD']:checked").val() == undefined ? "0" : $("input[name='ThayDoiCheDoAnDD']:checked").val(),
                $("input[name='TrieuChungDinhDuong']:checked").val() == undefined ? "0" : $("input[name='TrieuChungDinhDuong']:checked").val(),
                $("input[name='ChucNangCoTheDD']:checked").val() == undefined ? "0" : $("input[name='ChucNangCoTheDD']:checked").val(),
                $("input[name='DauHieuThucTheDD']:checked").val() == undefined ? "0" : $("input[name='DauHieuThucTheDD']:checked").val(),
                $("input[name='TongSoDiemSGA']:checked").val() == undefined ? "0" : $("input[name='TongSoDiemSGA']:checked").val(),
                $("input[name='DanhGiaTTDD']:checked").val() == undefined ? "0" : $("input[name='DanhGiaTTDD']:checked").val(),
                $("input[name='KetLuaDD']:checked").val() == undefined ? "0" : $("input[name='KetLuaDD']:checked").val(),
                $("input[name='ChiDinhDuongNuoiDD']:checked").val() == undefined ? "0" : $("input[name='ChiDinhDuongNuoiDD']:checked").val(),
                singletonObject.userId,
                singletonObject.user,
                thongtinhsba.thongtinbn.NHAPVIENVAOKHOA,
                thongtinhsba.thongtinbn.TEN_PHONGBAN
            ]),
            success: function (data) {
                if (data == '1'){
                    notifiToClient("Green","Thêm phiếu thành công");
                }else if (data == '2'){
                    notifiToClient("Green", "Cập nhật phiếu thành công");
                }else if (data == '-1'){
                    notifiToClient("Red","Thêm phiếu thất bại");
                }else {
                    notifiToClient("Red","Cập nhật phiếu thất bại");
                }
            },
            error: function (data) {
                notifiToClient("Red","Cập nhật phiếu thất bại");
            }
        });
    }
    function printPhieuDanhGiaDinhDuong(){ //singletonObject.tenbenhvien ,

        var params = {
            DVTT:singletonObject.dvtt,
            SOVAOVIEN:thongtinhsba.thongtinbn.SOVAOVIEN,
            SOVAOVIEN_DT:thongtinhsba.thongtinbn.SOVAOVIEN_DT,
            MAPHONGBAN:thongtinhsba.thongtinbn.NHAPVIENVAOKHOA
        }
        var url = 'cmu_in_VLG_RP_PHEU_DD?type=pdf&' + $.param(params);
        previewPdfDefaultModal(url, "preview");
    }

    function getPDGDDData(keyLog, type, duLieuBanDau, pdgddType) {
        let rsData = {}
        Object.keys(keyLog).forEach(key => rsData[key] = '');

        if (type == 0) {
            if (pdgddType == 0) {
                rsData = {
                    ICD: $('#phieutren18_icd').val(),
                    CHAN_DOAN: $("#phieutren18_chandoan").val(),
                    CAN_NANG_VAO_VIEN: $("#phieutren18_cannang").val(),
                    CHIEU_CAO: $("#phieutren18_chieucao").val(),
                    BMI: $("#phieutren18_bmi").val(),
                    SANG_LOC_BMI: getOptionValueLog(arrOption.coKhong, $("#phieutren18_sanlocbmi").val()),
                    SANG_LOC_SUT_CAN: getOptionValueLog(arrOption.coKhong, $("#phieutren18_sanlocsutcan").val()),
                    SANG_LOC_LUONG_AN: getOptionValueLog(arrOption.coKhong, $("#phieutren18_sanglocluongan").val()),
                    SANG_LOC_BENH_NANG: getOptionValueLog(arrOption.coKhong, $("#phieutren18_sanglocbenhnang").val()),
                    KETQUASANGLOC: $("#phieutren18_ketluansangloc").text(),
                    CHIDINH: $('#phieutren18_chidinh').text(),
                    DANH_GIA_BMI: getOptionValueLog(arrOption.BMI, $("#phieutren18_danhgiabmi").val()),
                    DANH_GIA_SUT_CAN: getOptionValueLog(arrOption.sutCan, $("#phieutren18_danhgiasutcan").val()),
                    DANH_GIA_LUONG_AN: getOptionValueLog(arrOption.luongAn, $("#phieutren18_danhgialuongan").val()),
                    DANH_GIA_BENH_LY: getOptionValueLog(arrOption.benhLy, $("#phieutren18_danhgiabenhly").val()),
                    KETLUAN: $("#phieutren18_ketluan").text(),
                    CHE_DO_AN: $('#phieutren18_chedoan').val(),
                    CHI_DINH_DD_KHAC: $("#phieusanphu_chidinhkhac").val(),
                    TAI_DANH_GIA: getOptionValueLog(arrOption.taiDanhGia, $("#phieutren18_taidanhgia").val()),
                    BAC_SI_DIEU_TRI: getTenBacSiLog(singletonObject.danhsachnhanvien, $("#phieutren18_bsdieutri").val())
                }
            }
            else if (pdgddType == 1) {
                rsData = {
                    ICD: $('#phieunguoilon_icd').val(),
                    CHAN_DOAN: $("#phieunguoilon_chandoan").val(),
                    CAN_NANG_VAO_VIEN: $("#phieunguoilon_cnvaovien").val(),
                    CHIEU_CAO: $("#phieunguoilon_chieucao").val(),
                    BMI: $("#phieunguoilon_bmi").val(),
                    CAN_NANG_RA_VIEN: $("#phieunguoilon_cnravien").val(),
                    SANG_LOC_BMI: getOptionValueLog(arrOption.coKhong, $("#phieunguoilon_sanlocbmi").val()),
                    SANG_LOC_SUT_CAN: getOptionValueLog(arrOption.coKhong, $("#phieunguoilon_sanlocsutcan").val()),
                    SANG_LOC_LUONG_AN: getOptionValueLog(arrOption.coKhong, $("#phieunguoilon_sanglocluongan").val()),
                    SANG_LOC_BENH_NANG: getOptionValueLog(arrOption.coKhong, $("#phieunguoilon_sanglocbenhnang").val()),
                    KETQUASANGLOC: $("#phieunguoilon_ketqua").text(),
                    DANH_GIA_BMI: getOptionValueLog(arrOption.BMI, $("#phieunguoilon_danhgiabmi").val()),
                    DANH_GIA_SUT_CAN: getOptionValueLog(arrOption.sutCan, $("#phieunguoilon_danhgiasutcan").val()),
                    DANH_GIA_LUONG_AN: getOptionValueLog(arrOption.luongAn, $("#phieunguoilon_danhgialuongan").val()),
                    DANH_GIA_BENH_LY: getOptionValueLog(arrOption.benhLy, $("#phieunguoilon_danhgiabenhly").val()),
                    DANH_GIA_TONG_DIEM: $("#phieunguoilon_tongdiem").val(),
                    KETLUAN: $("#phieunguoilon_ketluan").text(),
                    CHE_DO_AN: $("#phieunguoilon_chedoan").val(),
                    DUONG_NUOI_AN: getOptionValueLog(arrOption.duongNuoiAn, $("#phieunguoilon_duongnuoi").val()),
                    MOI_HOI_CHAN_DD: getOptionValueLog(arrOption.coKhong, $("#phieunguoilon_moihoichan").val()),
                    TAI_DANH_GIA: getOptionValueLog(arrOption.taiDanhGia, $("#phieunguoilon_taidanhgia").val()),
                    BAC_SI_DIEU_TRI: getTenBacSiLog(singletonObject.danhsachnhanvien, $("#phieunguoilon_bsdieutri").val())
                }
            }
            else if (pdgddType == 2) {
                rsData = {
                    TUOI_THAI: $("#phieusanphu_tuoithai").val(),
                    TUOI_THAI_THEO: getOptionValueLog(arrOption.tuoiThaiTheo, $("#phieusanphu_tuoithaitheo").val()),
                    ICD: $('#phieusanphu_icd').val(),
                    CHAN_DOAN: $("#phieusanphu_chandoan").val(),
                    CAN_NANG_VAO_VIEN: $("#phieusanphu_cnvaovien").val(),
                    CHIEU_CAO: $("#phieusanphu_chieucao").val(),
                    BMI: $("#phieusanphu_bmi").val(),
                    CAN_NANG_RA_VIEN: $("#phieusanphu_cnravien").val(),
                    CHU_VI_VONG_CANH_TAY: $("#phieusanphu_cvvongcanhtay").val(),
                    DANH_GIA_BMI: getOptionValueLog(arrOption.bmiTruocMangThai, $("#phieusanphu_danhgiabmi").val()),
                    DANH_GIA_CV_VONG_CANH_TAY: getOptionValueLog(arrOption.chuViVongCanhTay, $("#phieusanphu_chuvicanhtay").val()),
                    DANH_GIA_TD_TANG_CAN: getOptionValueLog(arrOption.tocDoTangCan, $("#phieusanphu_tdtangcan").val()),
                    DANH_GIA_BENH_KEM_THEO: getOptionValueLog(arrOption.benhKemTheo, $("#phieusanphu_benhkemtheo").val()),
                    KETLUAN: $("#phieusanphu_kqdanhgia").text(),
                    CHE_DO_AN: $("#phieusanphu_chedoan").val(),
                    DUONG_NUOI_AN: getOptionValueLog(arrOption.duongNuoiAn, $("#phieusanphu_duongnuoi").val()),
                    MOI_HOI_CHAN_DD: getOptionValueLog(arrOption.coKhong, $("#phieusanphu_moihoichan").val()),
                    TAI_DANH_GIA: getOptionValueLog(arrOption.taiDanhGia, $("#phieusanphu_taidanhgia").val()),
                    BAC_SI_DIEU_TRI: getTenBacSiLog(singletonObject.danhsachnhanvien, $("#phieusanphu_bsdieutri").val())
                }
            }
            else if (pdgddType == 3) {
                rsData = {
                    ICD: $('#phieutreem_icd').val(),
                    CHAN_DOAN: $("#phieutreem_chandoan").val(),
                    CAN_NANG_VAO_VIEN: $("#phieutreem_cnvaovien").val(),
                    CHIEU_CAO: $("#phieutreem_chieucao").val(),
                    BMI: $("#phieutreem_bmi").val(),
                    CAN_NANG_RA_VIEN: $("#phieutreem_cnravien").val(),
                    DANH_GIA_BMI: getOptionValueLog(arrOption.bmiTreEm, $("#phieutreem_danhgiabmi").val()),
                    DANH_GIA_SUT_CAN: getOptionValueLog(arrOption.sutCanTreEm, $("#phieutreem_danhgiasutcan").val()),
                    DANH_GIA_LUONG_AN: getOptionValueLog(arrOption.luongAnTreEm, $("#phieutreem_danhgialuongan").val()),
                    KETLUAN: $("#phieutreem_kqdanhgia").text(),
                    CHE_DO_AN: $("#phieutreem_chedoan").val(),
                    DUONG_NUOI_AN: getOptionValueLog(arrOption.duongNuoiAn, $("#phieutreem_duongnuoi").val()),
                    MOI_HOI_CHAN_DD: getOptionValueLog(arrOption.coKhong, $("#phieutreem_moihoichan").val()),
                    TAI_DANH_GIA: getOptionValueLog(arrOption.taiDanhGia, $("#phieutreem_taidanhgia").val()),
                    BAC_SI_DIEU_TRI: getTenBacSiLog(singletonObject.danhsachnhanvien, $("#phieutreem_bsdieutri").val())
                }
            }
        } else if (type == 1) {
            if (pdgddType == 0) {
                Object.keys(rsData).forEach(key => rsData[key] = pdgddt18DuLieuBanDau[key]);
                rsData.KETQUASANGLOC = getKLSangLocLog(rsData.SANG_LOC_BMI, rsData.SANG_LOC_SUT_CAN, rsData.SANG_LOC_LUONG_AN, rsData.SANG_LOC_BENH_NANG, 0)?.ketQuaSangLoc;
                rsData.CHIDINH = getKLSangLocLog(rsData.SANG_LOC_BMI, rsData.SANG_LOC_SUT_CAN, rsData.SANG_LOC_LUONG_AN, rsData.SANG_LOC_BENH_NANG, 0)?.chiDinh;
                rsData.KETLUAN = getKLDanhGiaDDLog(rsData.DANH_GIA_BMI, rsData.DANH_GIA_SUT_CAN, rsData.DANH_GIA_LUONG_AN, rsData.DANH_GIA_BENH_LY, 0)?.ketLuan;
                rsData.SANG_LOC_BMI = getOptionValueLog(arrOption.coKhong, rsData.SANG_LOC_BMI);
                rsData.SANG_LOC_SUT_CAN = getOptionValueLog(arrOption.coKhong, rsData.SANG_LOC_SUT_CAN);
                rsData.SANG_LOC_LUONG_AN = getOptionValueLog(arrOption.coKhong, rsData.SANG_LOC_LUONG_AN);
                rsData.SANG_LOC_BENH_NANG = getOptionValueLog(arrOption.coKhong, rsData.SANG_LOC_BENH_NANG);
                rsData.DANH_GIA_BMI = getOptionValueLog(arrOption.BMI, rsData.DANH_GIA_BMI);
                rsData.DANH_GIA_SUT_CAN = getOptionValueLog(arrOption.sutCan, rsData.DANH_GIA_SUT_CAN);
                rsData.DANH_GIA_LUONG_AN = getOptionValueLog(arrOption.luongAn, rsData.DANH_GIA_LUONG_AN);
                rsData.DANH_GIA_BENH_LY = getOptionValueLog(arrOption.benhLy, rsData.DANH_GIA_BENH_LY);
                rsData.TAI_DANH_GIA = getOptionValueLog(arrOption.taiDanhGia, rsData.TAI_DANH_GIA);
                rsData.BAC_SI_DIEU_TRI = getTenBacSiLog(singletonObject.danhsachnhanvien, rsData.BAC_SI_DIEU_TRI);
            }
            else if (pdgddType == 1) {
                Object.keys(rsData).forEach(key => rsData[key] = pdgddnlDuLieuBanDau[key]);
                rsData.KETQUASANGLOC = getKLSangLocLog(rsData.SANG_LOC_BMI, rsData.SANG_LOC_SUT_CAN, rsData.SANG_LOC_LUONG_AN, rsData.SANG_LOC_BENH_NANG, 1)?.ketQuaSangLoc;
                rsData.DANH_GIA_TONG_DIEM = getKLDanhGiaDDLog(rsData.DANH_GIA_BMI, rsData.DANH_GIA_SUT_CAN, rsData.DANH_GIA_LUONG_AN, rsData.DANH_GIA_BENH_LY, 1)?.tongDiem;
                rsData.KETLUAN = getKLDanhGiaDDLog(rsData.DANH_GIA_BMI, rsData.DANH_GIA_SUT_CAN, rsData.DANH_GIA_LUONG_AN, rsData.DANH_GIA_BENH_LY, 1)?.ketLuan;
                rsData.SANG_LOC_BMI = getOptionValueLog(arrOption.coKhong, rsData.SANG_LOC_BMI);
                rsData.SANG_LOC_SUT_CAN = getOptionValueLog(arrOption.coKhong, rsData.SANG_LOC_SUT_CAN);
                rsData.SANG_LOC_LUONG_AN = getOptionValueLog(arrOption.coKhong, rsData.SANG_LOC_LUONG_AN);
                rsData.SANG_LOC_BENH_NANG = getOptionValueLog(arrOption.coKhong, rsData.SANG_LOC_BENH_NANG);
                rsData.DANH_GIA_BMI = getOptionValueLog(arrOption.BMI, rsData.DANH_GIA_BMI);
                rsData.DANH_GIA_SUT_CAN = getOptionValueLog(arrOption.sutCan, rsData.DANH_GIA_SUT_CAN);
                rsData.DANH_GIA_LUONG_AN = getOptionValueLog(arrOption.luongAn, rsData.DANH_GIA_LUONG_AN);
                rsData.DANH_GIA_BENH_LY = getOptionValueLog(arrOption.benhLy, rsData.DANH_GIA_BENH_LY);
                rsData.DUONG_NUOI_AN = getOptionValueLog(arrOption.duongNuoiAn, rsData.DUONG_NUOI_AN);
                rsData.MOI_HOI_CHAN_DD = getOptionValueLog(arrOption.coKhong, rsData.MOI_HOI_CHAN_DD),
                rsData.TAI_DANH_GIA = getOptionValueLog(arrOption.taiDanhGia, rsData.TAI_DANH_GIA);
                rsData.BAC_SI_DIEU_TRI = getTenBacSiLog(singletonObject.danhsachnhanvien, rsData.BAC_SI_DIEU_TRI);
            }
            else if (pdgddType == 2) {
                Object.keys(rsData).forEach(key => rsData[key] = pdgddspDuLieuBanDau[key]);
                rsData.KETLUAN = getKLDanhGiaDDLog(rsData.DANH_GIA_BMI, rsData.DANH_GIA_CV_VONG_CANH_TAY, rsData.DANH_GIA_TD_TANG_CAN, rsData.DANH_GIA_BENH_KEM_THEO, 2)?.ketLuan;
                rsData.TUOI_THAI_THEO = getOptionValueLog(arrOption.tuoiThaiTheo, rsData.TUOI_THAI_THEO);
                rsData.DANH_GIA_BMI = getOptionValueLog(arrOption.bmiTruocMangThai, rsData.DANH_GIA_BMI);
                rsData.DANH_GIA_CV_VONG_CANH_TAY = getOptionValueLog(arrOption.chuViVongCanhTay, rsData.DANH_GIA_CV_VONG_CANH_TAY);
                rsData.DANH_GIA_TD_TANG_CAN = getOptionValueLog(arrOption.tocDoTangCan, rsData.DANH_GIA_TD_TANG_CAN);
                rsData.DANH_GIA_BENH_KEM_THEO = getOptionValueLog(arrOption.benhKemTheo, rsData.DANH_GIA_BENH_KEM_THEO);
                rsData.DUONG_NUOI_AN = getOptionValueLog(arrOption.duongNuoiAn, rsData.DUONG_NUOI_AN);
                rsData.MOI_HOI_CHAN_DD = getOptionValueLog(arrOption.coKhong, rsData.MOI_HOI_CHAN_DD);
                rsData.TAI_DANH_GIA = getOptionValueLog(arrOption.taiDanhGia, rsData.TAI_DANH_GIA);
                rsData.BAC_SI_DIEU_TRI = getTenBacSiLog(singletonObject.danhsachnhanvien, rsData.BAC_SI_DIEU_TRI);
            }
            else if (pdgddType == 3) {
                Object.keys(rsData).forEach(key => rsData[key] = pdgddteDuLieuBanDau[key]);
                rsData.KETLUAN = getKLDanhGiaDDLog(rsData.DANH_GIA_BMI, rsData.DANH_GIA_SUT_CAN, rsData.DANH_GIA_LUONG_AN, 0, 3)?.ketLuan;
                rsData.DANH_GIA_BMI = getOptionValueLog(arrOption.bmiTreEm, rsData.DANH_GIA_BMI);
                rsData.DANH_GIA_SUT_CAN = getOptionValueLog(arrOption.sutCanTreEm, rsData.DANH_GIA_SUT_CAN);
                rsData.DANH_GIA_LUONG_AN = getOptionValueLog(arrOption.luongAnTreEm, rsData.DANH_GIA_LUONG_AN);
                rsData.DUONG_NUOI_AN = getOptionValueLog(arrOption.duongNuoiAn, rsData.DUONG_NUOI_AN);
                rsData.MOI_HOI_CHAN_DD = getOptionValueLog(arrOption.coKhong, rsData.MOI_HOI_CHAN_DD);
                rsData.TAI_DANH_GIA = getOptionValueLog(arrOption.taiDanhGia, rsData.TAI_DANH_GIA);
                rsData.BAC_SI_DIEU_TRI = getTenBacSiLog(singletonObject.danhsachnhanvien, rsData.BAC_SI_DIEU_TRI);
            }
        }

        return rsData
    }

    function getKLSangLocLog(bmi, sutCan, luongAn, benhNang, pdgddType) {
        let rsObj = { ketQuaSangLoc: '', chiDinh: '' };
        if (pdgddType == 0) {
            if (Number(bmi) == 1 || Number(sutCan) == 1 || Number(luongAn) == 1 || Number(benhNang) == 1) {
                rsObj.ketQuaSangLoc = "Có nguy cơ suy dinh dưỡng";
                rsObj.chiDinh = "Đánh giá tình trạng dinh dưỡng";
            } else {
                rsObj.ketQuaSangLoc = "Không có nguy cơ suy dinh dưỡng";
                rsObj.chiDinh = "Tái sàng lọc sau 1 tuần";
            }
        }
        else if (pdgddType == 1) {
            if (Number(bmi) == 1 || Number(sutCan) == 1 || Number(luongAn) == 1 || Number(benhNang) == 1) {
                rsObj.ketQuaSangLoc = "Có - Tiếp tục bước 2, đánh giá";
            } else {
                rsObj.ketQuaSangLoc = "Không - Tái sàng lọc sau 1 tuần";
            }
        }
        return rsObj;
    }

    function getKLDanhGiaDDLog(bmi, sutCan, luongAn, benhNang, pdgddType) {
        let rsObj = { tongDiem: '', ketLuan: '' };
        let tongDiem = Number(bmi) + Number(sutCan) + Number(luongAn) + Number(benhNang);
        rsObj.tongDiem = tongDiem;
        if (pdgddType != 2) {
            if (tongDiem >= 2)
                rsObj.ketLuan = 'Suy dinh dưỡng';
            else
                rsObj.ketLuan = 'Bình thường';
        } else {
            if (tongDiem >= 2)
                rsObj.ketLuan = 'Có nguy cơ về dinh dưỡng';
            else
                rsObj.ketLuan = 'Bình thường';
        }
        return rsObj;
    }

    function getOptionValueLog(myArray, key){
        for (let i = 0; i < myArray.length; i++) {
            if (myArray[i].key === key) {
                return myArray[i].value;
            }
        }
    }

    function getStringPDGDD(obj, keyLog) {
        let string = '';
        let objLength = Object.keys(obj).length;
        Object.keys(obj).forEach((key, index) => {
            if (index != objLength - 1)
                string += `${keyLog[key]}: ${obj[key]}; `
            else
                string += `${keyLog[key]}: ${obj[key]}.`
        });
        return string;
    }

    function getContentLogPhieuchucnangsong(data) {
        var content = [];
        for (let key in data) {
            if (keyLuuLogPhieuchucnangsong.hasOwnProperty(key)) {
                content.push(keyLuuLogPhieuchucnangsong[key] + ": " +data[key])
            }
        }
        return content.join(";");
    }

    /**
     * Phiếu đánh giá dinh dưỡng chung
     * */
    function layDataPhieuDGDDChung(maPhieu, callback) {
        let url = 'cmu_getlist?url=' + convertArray([
            maPhieu, singletonObject.dvtt,
            thongtinhsba.thongtinbn.STT_BENHAN,
            'CMU_DGDD_CHUNG_DATA_PHIEU'
        ]);
        $.get(url).done(function(data){
            if (data && data.length > 0) {
                let jsonData = JSON.parse(data[0].DATA_PHIEU);
                jsonData.MA_PHIEU = data[0].MA_PHIEU;
                callback(jsonData)
            } else  {
                notifiToClient('Red', 'TTSC-01: Lỗi lấy dữ liệu phiếu');
            }
        });
    }

    function dinhKemSuKienPhieuDGDDChung(maPhieu, isInit) {
        let eICD = pdgddChung.newForm.getComponent('ICD');
        let eChanDoan = pdgddChung.newForm.getComponent('CHAN_DOAN');

        $('#formDanhGiaDinhDuongChung input[name="data[ICD]"]').on('keypress', function(event) {
            let mabenhICD = $(this).val();
            if (event.keyCode === 13 && mabenhICD !== "") {
                mabenhICD = mabenhICD.toUpperCase();
                getMotabenhly(mabenhICD, function(data) {
                    let splitIcd = data.split("!!!")
                    eChanDoan.setValue(splitIcd[1]);
                    eICD.setValue(mabenhICD);
                })
            }
        });
        combgridTenICD('formDanhGiaDinhDuongChung input[name="data[CHAN_DOAN]"]', function(item) {
            eICD.setValue(item.ICD);
            eChanDoan.setValue(item.MO_TA_BENH_LY);
        });

        $('#formDanhGiaDinhDuongChung [name^="data[TINHDIEM"]').on('change', function () {
            let tongDiem = 0;
            $('#formDanhGiaDinhDuongChung [name^="data[TINHDIEM"]').each(function() {
                let soDiem = this.name.match(/_(\d+)/)[1];
                if (
                    (this.type === 'checkbox' && this.checked) ||
                    (this.type === 'select-one' && $(this).val() === "2")
                ) {
                    tongDiem += Number(soDiem);
                }
            });
            tongDiem = tongDiem === 0 ?
                0 : (thongtinhsba.thongtinbn.TUOI >= 70 && ["NBNoiTru"].includes(maPhieu) ?
                    (tongDiem + 1) : tongDiem);
            let eTongDiem = pdgddChung.newForm.getComponent('TONG_DIEM');
            eTongDiem.setValue(tongDiem);
        });

        let eBMI = pdgddChung.newForm.getComponent('BMI');
        let eCanNang = pdgddChung.newForm.getComponent('CANNANG');
        let eChieuCao = pdgddChung.newForm.getComponent('CHIEUCAO');

        $('#formDanhGiaDinhDuongChung input[name="data[CANNANG]"]').change(function() {
            if(!$(this).val() || !eChieuCao.getValue()) { return; }
            eBMI.setValue((eChieuCao.getValue()/Math.pow($(this).val()/100, 2)).toFixed(2));
        })
        $('#formDanhGiaDinhDuongChung input[name="data[CHIEUCAO]"]').change(function() {
            if(!$(this).val() || !eCanNang.getValue()) { return; }
            eBMI.setValue((eCanNang.getValue()/Math.pow($(this).val()/100, 2)).toFixed(2));
        })

        if (isInit) {
            pdgddChung.newForm.redraw();
            dinhKemSuKienPhieuDGDDChung(maPhieu);
        }
    }

    function loadFormDanhGiaDinhDuong(maPhieu, data, callback) {
        try {
            $.getJSON(
                "resources/camau/js/formioDanhGiaDinhDuong" + maPhieu + ".json?v="+moment().format('YYYYMMDDHHmmss')
            ).done(function(rs){
                Formio.createForm(document.getElementById('formDanhGiaDinhDuongChung'), rs, {
                    disableAlerts: true,
                }).then(function(instance) {
                    pdgddChung.newForm = instance;
                    let bsDanhGia = pdgddChung.newForm.getComponent("BAC_SI_DANH_GIA").component;
                    bsDanhGia.data.json = singletonObject.danhsachtatcanhanvien;
                    let dieuDuong = pdgddChung.newForm.getComponent("DIEU_DUONG");
                    dieuDuong ? (dieuDuong.component.data.json = singletonObject.danhsachtatcanhanvien) : "BYPASS";

                    let initData = { data: {} };
                    if (data) {
                        initData.data = data;
                    }
                    else {
                        initData.data = {}
                    }

                    pdgddChung.newForm.setSubmission(initData).then(function () {
                        dinhKemSuKienPhieuDGDDChung(maPhieu, true);
                    }).catch(function(err) {
                        notifiToClient('Red', 'Lỗi cài đặt dữ liệu'); console.error(err);
                    });

                    callback && callback();
                });
            })
        } catch (err) {
            console.error(err)
        }
    }

    function initModalDanhGiaDinhDuong(tenPhieu, maPhieu, action, dataPhieu) {
        loadFormDanhGiaDinhDuong(maPhieu, dataPhieu, function () {
            if(action === 'THEM') {
                $('#taoDGDDChung').show();
                $('#suaDGDDChung').hide();
            } else {
                pdgddChung.preForm = $.extend(true, {}, dataPhieu);
                $('#taoDGDDChung').hide();
                $('#suaDGDDChung').show();
            }
            addTextTitleModal('dgdd_title_chung', tenPhieu);
            $('#modalFormDanhGiaDinhDuongChung').modal('show');
        });
    }

    $("#taoDGDDChung").on("click", function () {
        pdgddChung.newForm.emit("checkValidity");
        if (!pdgddChung.newForm.checkValidity(null, false, null, true)) {
            return;
        }
        let formData = $.extend(true, {}, pdgddChung.newForm.submission.data);
        delete formData.MA_PHIEU;
        let thisBtn = this.id;
        let idSlect = $("#select_ten_phieu");
        let loaiPhieu = idSlect.val();
        let tenPhieu = idSlect.find("option:selected").text();
        showSelfLoading(thisBtn);
        try {
            $.post("cmu_post_CMU_PHIEU_DGDD_CHUNG_INS", {
                url: [
                    singletonObject.dvtt,
                    thongtinhsba.thongtinbn.STT_BENHAN,
                    thongtinhsba.thongtinbn.MABENHNHAN,
                    singletonObject.userId,
                    loaiPhieu,
                    JSON.stringify(formData)
                ].join("```")
            }).fail(function() {
                notifiToClient('Red', MESSAGEAJAX.ERROR);
            }).done(function(data) {
                if (data > 0) {
                    let subData = { CO: "Có", KHONG: "Không" }
                    luuLogHSBAInsertFormioV4(
                        pdgddChung.newForm, loaiPhieu,
                        "Tạo " + tenPhieu + " - Mã phiếu: " + data + " - ", subData
                    );
                    notifiToClient('Green', MESSAGEAJAX.ADD_SUCCESS);
                    $("#modalFormDanhGiaDinhDuongChung").modal('hide');
                    reloadDSDanhGiaDD();
                }
                else {
                    notifiToClient('Red', MESSAGEAJAX.FAIL);
                }
            }).always(function() {
                hideSelfLoading(thisBtn);
            });
        }
        catch (err) {
            console.error(err)
        }
    });

    $("#suaDGDDChung").on("click", function () {
        pdgddChung.newForm.emit("checkValidity");
        if (!pdgddChung.newForm.checkValidity(null, false, null, true)) {
            return;
        }
        let formData = $.extend(true, {}, pdgddChung.newForm.submission.data);
        delete formData.MA_PHIEU;
        let thisBtn = this.id;
        let idSlect = $("#select_ten_phieu");
        let loaiPhieu = idSlect.val();
        let tenPhieu = idSlect.find("option:selected").text();
        showSelfLoading(thisBtn);
        try {
            $.post("cmu_post_CMU_PHIEU_DGDD_CHUNG_UPD", {
                url: [
                    pdgddChung.newForm.data.MA_PHIEU,
                    singletonObject.dvtt,
                    thongtinhsba.thongtinbn.STT_BENHAN,
                    JSON.stringify(formData)
                ].join("```")
            }).fail(function() {
                notifiToClient('Red', MESSAGEAJAX.ERROR);
            }).done(function(data) {
                if (data > 0) {
                    let subData = { CO: "Có", KHONG: "Không" }
                    luuLogHSBAChinhSuaFormioV4(
                        pdgddChung.preForm, pdgddChung.newForm,
                        loaiPhieu, "Cập nhật " + tenPhieu + " - ID: " + formData.ID + " - ",
                        subData
                    );
                    notifiToClient('Green', MESSAGEAJAX.EDIT_SUCCESS);
                    $("#modalFormDanhGiaDinhDuongChung").modal('hide');
                    reloadDSDanhGiaDD();
                }
                else {
                    notifiToClient('Red', MESSAGEAJAX.FAIL);
                }
            }).always(function() {
                hideSelfLoading(thisBtn);
            });
        }
        catch (err) {
            console.error(err)
        }
    });
});
