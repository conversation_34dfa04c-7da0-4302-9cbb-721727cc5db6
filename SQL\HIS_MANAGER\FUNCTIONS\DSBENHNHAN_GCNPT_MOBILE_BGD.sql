create or replace FUNCTION dsbenhnhan_gcnpt_mobile_bgd (
    p_dvtt          IN              VARCHAR2,
    p_ngaybatdau    IN              VARCHAR2,
    p_ngayketthuc   IN              VARCHAR2,
    p_trangthai     IN              VARCHAR2,
    p_chucvu        IN              VARCHAR2,
    p_manhanvien    IN              VARCHAR2
) RETURN SYS_REFCURSOR IS

    cur             SYS_REFCURSOR;
    v_ngaykethuc    DATE := TO_DATE(p_ngayketthuc || ' 23:59:59', 'DD/MM/YYYY HH24:MI:SS');
    v_ngaybatdau    DATE := TO_DATE(p_ngaybatdau || ' 00:00:00', 'DD/MM/YYYY HH24:MI:SS');
    v_tennhanvien   VARCHAR2(500);
BEGIN
SELECT
    ten_nhanvien_cd
INTO v_tennhanvien
FROM
    his_fw.dm_nhanvien_cd
WHERE
    ma_nhanvien = p_manhanvien;

OPEN cur FOR SELECT
                    a.*
                FROM
                    (
                        SELECT
                            bn.ma_benh_nhan,
                            ba.stt_benhan,
                            ba.sob<PERSON>han,
                            ddt.stt_dotdieutri,
                            bn.ten_benh_<PERSON>han,
                            bn.gioi_tinh,
                            ba.sovaovien,
                            ddt.sovaovien_dt,
                            EXTRACT(YEAR FROM trunc(ba.ngaynhapvien)) - EXTRACT(YEAR FROM trunc(bn.ngay_sinh)) AS tuoi,
                            cmu_hienthi_thangv2(bn.ngay_sinh, ba.ngaynhapvien, p_dvtt) thang,
                            cmu_hienthi_ngayv2(bn.ngay_sinh, ba.ngaynhapvien, p_dvtt) ngay,
                            bn.dia_chi,
                            giuong.stt_buong      ten_phong,
                            giuong.stt_giuong     sogiuong,
                            'PHIEU_NOITRU_GIAYCHUNGNHAN_TRUONGKHOA' ky_hieu_phieu,
                            grv.so_phieu_dichvu   so_phieu_dv,
                            grv.ma_dv             ma_dich_vu,
                            'kyso/get-file-minio' urlgetpdf,
                            'GIÁM ĐỐC' keyword,
                            'GIÁM ĐỐC' keywordcomment,
                            '-10' cx1,
                            '130' cx,
                            '-40' cy1,
                            '-105' cy2,
                            '-10' x1,
                            '130' x,
                            '10' y1,
                            '-55' y2,
                            'PHIEU_NOITRU_GIAYCHUNGNHAN_BGD' kyhieukyso,
                            '-1' id_dieutri,
                            '1' visibletype,
                            v_tennhanvien         tennhanvien,
                            grv.ngay_gio_pttt     ngayravien,
                            'rp_giayravien_mobile' jasper,
                            TO_CHAR(grv.ngay_gio_pttt, 'DD/MM/YYYY HH24:MI') ngayrv,
                            pban.ten_phongban,
                            '13' fontsize,
                            CASE
                                WHEN p_trangthai = 1 THEN
                                    cmu_getminiokey(p_dvtt, grv.sovaovien, 'PHIEU_NOITRU_GIAYCHUNGNHAN_BGD', grv.so_phieu_dichvu
                                    , grv.ma_dv)
                                ELSE
                                    minio.keyminio
                            END keyminio,
                            check_gcnpt_daky(grv.dvtt, grv.sovaovien, grv.sovaovien_dt, grv.so_phieu_dichvu, grv.ma_dv, 'PHIEU_NOITRU_GIAYCHUNGNHAN_BGD'
                            ) truongkhoa,
                            sign.keysign
                        FROM
                             cmu_grvct                               grvct
                            JOIN noitru_cd_dichvu_ct                     grv ON grvct.dvtt = grv.dvtt
                                                            AND grvct.sovaovien = grv.sovaovien
                            JOIN noitru_cd_dichvu                        cd ON grv.dvtt = cd.dvtt
                                                        AND cd.so_phieu_dichvu = grv.so_phieu_dichvu
                                                        AND grv.stt_benhan = cd.stt_benhan
                                                        AND grv.stt_dotdieutri = cd.stt_dotdieutri
                            JOIN noitru_benhan                           ba ON grv.dvtt = ba.dvtt
                                                     AND grv.sovaovien = ba.sovaovien
                            JOIN smartca_signed_kcb                      sign ON grv.dvtt = sign.dvtt
                                                            AND grv.sovaovien = sign.sovaovien
                                                            AND sign.status = 0
                                                            AND grv.sovaovien_dt = sign.sovaovien_dt
                                                            AND sign.ky_hieu_phieu = 'PHIEU_NOITRU_GIAYCHUNGNHAN_TRUONGKHOA'
                                                            AND sign.so_phieu_dv = grv.so_phieu_dichvu
                                                            AND sign.ma_dich_vu = grv.ma_dv
                            JOIN his_manager.smartca_signed_file_minio   minio ON sign.dvtt = minio.dvtt
                                                                                AND sign.keysign = minio.keysign
                            JOIN noitru_dotdieutri                       ddt ON ddt.stt_benhan = ba.stt_benhan
                                                          AND ddt.dvtt = ba.dvtt
                            LEFT JOIN cmu_sobuonggiuong                       giuong ON ddt.dvtt = giuong.dvtt
                                                                  AND ddt.stt_benhan = giuong.stt_benhan
                                                                  AND giuong.stt_dotdieutri = ddt.stt_dotdieutri
                            JOIN his_public_list.dm_benh_nhan            bn ON ba.mabenhnhan = bn.ma_benh_nhan
                            JOIN his_fw.dm_phongban                      pban ON pban.ma_phongban = grvct.makhoa
                                                            AND pban.ma_donvi = p_dvtt
                            JOIN cmu_khoakyso                            khoaks ON khoaks.dvtt = pban.ma_donvi
                                                        AND khoaks.makhoa = pban.ma_phongban
                                                        AND khoaks.manhanvien = p_manhanvien
                                                        AND khoaks.loai = 'BGD'
                        WHERE
                            grvct.dvtt = p_dvtt
                            AND grvct.ngayravien BETWEEN v_ngaybatdau AND v_ngaykethuc
                    ) a
                WHERE
                    ( ( p_trangthai = 1
                        AND a.truongkhoa = 1 )
                      OR ( a.truongkhoa = 0
                           AND p_trangthai = 0 ) )
                ORDER BY
                    ngayravien DESC;

RETURN cur;
END;