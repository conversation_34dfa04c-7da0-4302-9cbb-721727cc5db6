CREATE OR REPLACE PROCEDURE "GRV_INGIAYRAVIEN_MOBILE" (
    p_dvtt             IN                 VARCHAR2,
    p_stt_<PERSON>han       IN                 VARCHAR2,
    p_stt_dotdieutri   IN                 VARCHAR2,
    p_ma<PERSON><PERSON><PERSON>       IN                 NUMBER,
    cur                OUT                SYS_REFCURSOR
) IS

    v_tenbenhnhan         VARCHAR2(500) DEFAULT '';
    v_diachi              VARCHAR2(500) DEFAULT '';
    v_dantoc              VARCHAR2(500) DEFAULT '';
    v_nghenghiep          VARCHAR2(500) DEFAULT '';
    v_sobenhan            VARCHAR2(500) DEFAULT '';
    v_soluutru            VARCHAR2(500) DEFAULT '';
    v_gioitinh            NUMBER(10);
    v_tuoi                NUMBER(10);
    v_ngaysinh            DATE;
    v_ngayvaovien         TIMESTAMP;
    v_socuoinam           VARCHAR2(50) DEFAULT '';
    v_matinh              VARCHAR2(50) DEFAULT '';
    v_mahuyen             VARCHAR2(50) DEFAULT '';
    v_thang               VARCHAR(50);
-- VNPTHIS-2517 29/09/2017
    v_mayte               VARCHAR(100) DEFAULT '';
    v_nguoilienhe         VARCHAR(500) DEFAULT '';
    v_thamsonguoilienhe   NUMBER(2);
    v_xuatvien_tmp        NUMBER(2) DEFAULT 0;
    v_ngayhen             DATE default NULL;
    v_hentaikham          NUMBER(2) DEFAULT 0;
    v_mabv_tt56           VARCHAR2(255) := his_fw.cmu_tsdv(p_dvtt, 96159, '.../.../');
    v_sovaovien           NUMBER := 0;
    v_sovaovien_dt        NUMBER := 0;
    v_kiemtra1            NUMBER := 0;
    v_ccd                 VARCHAR2(255) := '';
    v_ngaycapcccd         VARCHAR2(255) := '';
BEGIN
SELECT
    sovaovien,
    sovaovien_dt
INTO
    v_sovaovien,
    v_sovaovien_dt
FROM
    noitru_dotdieutri
WHERE
    dvtt = p_dvtt
  AND stt_benhan = p_stt_benhan
  AND stt_dotdieutri = p_stt_dotdieutri;

SELECT
    COUNT(dvtt)
INTO v_kiemtra1
FROM
    noitru_xuatvien
WHERE
    sovaovien = v_sovaovien
  AND sovaovien_dt = v_sovaovien_dt
  AND dvtt = p_dvtt;

IF v_kiemtra1 = 1 THEN
        grv_ingiayravien_mobilexv(p_dvtt, p_stt_benhan, p_stt_dotdieutri, p_mabenhnhan, cur);
        return;
END IF;

BEGIN
SELECT
    mota_thamso
INTO v_thamsonguoilienhe
FROM
    his_fw.dm_thamso_donvi
WHERE
    dvtt = p_dvtt
  AND ma_thamso = 94054;

EXCEPTION
        WHEN no_data_found THEN
            v_thamsonguoilienhe := 0;
END;

BEGIN
SELECT
    CASE
        WHEN p_dvtt = '94005' THEN
            '819'
        ELSE
            TO_CHAR(b.ma_tinh_thanh)
        END
        || '/'
        ||
    CASE
        WHEN p_dvtt = '94005' THEN
            '03'
        ELSE
            TO_CHAR(b.ma_huyen_thebhyt)
        END
        || '/'
        || substr(TO_CHAR(SYSDATE, 'YYYY'), - 2, 2)
        || '/'
        || substr(nvl(a.sobenhan, a.soxuatvien_bant), 0, instr(nvl(a.sobenhan, a.soxuatvien_bant), '/', 1) - 1)
INTO v_mayte
FROM
    his_manager.noitru_benhan       a,
    his_public_list.dm_quan_huyen   b,
    his_fw.dm_donvi                 c
WHERE
    a.dvtt = p_dvtt
  AND a.stt_benhan = p_stt_benhan
  AND a.mabenhnhan = p_mabenhnhan
  AND substr(a.dvtt, 0, 2) = b.ma_tinh_thanh
  AND c.ma_donvi = p_dvtt
  AND c.mahuyen = b.ma_quan_huyen;

EXCEPTION
        WHEN no_data_found THEN
            v_mayte := ' ';
END;

SELECT
    nvl(nguoi_lien_he, '')
INTO v_nguoilienhe
FROM
    his_public_list.dm_benh_nhan
WHERE
    ma_benh_nhan = p_mabenhnhan;
-- VNPTHIS-2517 29/09/2017

BEGIN
SELECT
    ten_benh_nhan,
    ngay_sinh,
    gioi_tinh,
    so_nha
        || ' '
        || dia_chi,
    ten_dantoc,
    ten_nghe_nghiep,
    cmt_benhnhan,
    TO_CHAR(ngaycapcmt, 'DD/MM/YYYY')
INTO
    v_tenbenhnhan,
    v_ngaysinh,
    v_gioitinh,
    v_diachi,
    v_dantoc,
    v_nghenghiep,
    v_ccd,
    v_ngaycapcccd
FROM
    his_public_list.dm_benh_nhan    bn,
    his_public_list.dm_dan_toc      dt,
    his_public_list.dm_nghenghiep   nn
WHERE
    ma_benh_nhan = p_mabenhnhan
  AND bn.ma_dantoc = dt.ma_dantoc
  AND bn.ma_nghe_nghiep = nn.ma_nghe_nghiep;

EXCEPTION
        WHEN no_data_found THEN
            v_ngaysinh := '';
            v_gioitinh := 0;
            v_diachi := '';
            v_dantoc := '';
            v_nghenghiep := '';
END;

SELECT
    hienthi_tuoi_benhnhan(v_ngaysinh)
INTO v_thang
FROM
    dual;

v_tuoi := extract(YEAR FROM systimestamp) - extract(YEAR FROM v_ngaysinh);
SELECT
    CASE
        WHEN bant = 0 THEN
            (
                CASE
                    WHEN nvl(sobenhan, ' ') = ' ' THEN
                        sobenhan_tt
                    ELSE
                        sobenhan
                    END
                )
        ELSE
            sobenhan_ngt
        END,
    CASE
        WHEN nvl(soxuatvien_luutru, ' ') = ' ' THEN
            soxuatvien_tt_luutru
        ELSE
            soxuatvien_luutru
        END,
    ngaynhapvien,
    nvl(substr(nam_ravien, 3, 2), ' ')
INTO
    v_sobenhan,
    v_soluutru,
    v_ngayvaovien,
    v_socuoinam
FROM
    his_manager.noitru_benhan
WHERE
    stt_benhan = p_stt_benhan
  AND dvtt = p_dvtt;
-- bo sung select thong tin tinh huyen

SELECT
    mahuyen,
    matinh
INTO
    v_mahuyen,
    v_matinh
FROM
    his_fw.dm_donvi
WHERE
    ma_donvi = p_dvtt;

OPEN cur FOR SELECT
                    v_tenbenhnhan   AS tenbenhnhan,
                    v_ngaysinh      AS ngaysinh,
                    concat('Ngày/tháng/năm sinh: ', TO_CHAR(v_ngaysinh, 'DD/MM/YYYY'))
                    || ' (Tuổi: '
                    || hgi_hienthi_tuoi_benhnhan(v_ngaysinh, ngaynhapvien, p_dvtt)
                    || ')' AS namsinh,
                    v_gioitinh      AS gioitinh,
                    v_diachi        AS diachi,
                    v_dantoc        AS dantoc,
                    v_nghenghiep    AS nghenghiep,
                    'Mã Y Tế: ' || v_mayte AS mayte, -- VNPTHIS-2517 29/09/2017
                    sobaohiemyte,
                    CASE
                        WHEN v_tuoi < 6 THEN
                            v_thang
                        ELSE
                            concat('Tuổi: ', v_tuoi)
END AS tuoi_ht,
                    CASE
                        WHEN v_tuoi < 6 THEN
                            v_thang
                        ELSE
                            concat(v_tuoi, ' tuổi')
END AS tuoi,
                    CASE
                        WHEN nvl(sobaohiemyte, ' ') = ' ' THEN
                            ' '
                        ELSE
                            substr(sobaohiemyte, 1, 2)
END AS kytudau2,
                    CASE
                        WHEN nvl(sobaohiemyte, ' ') = ' ' THEN
                            ' '
                        ELSE
                            substr(sobaohiemyte, 3, 1)
END AS kytu3,
                    CASE
                        WHEN nvl(sobaohiemyte, ' ') = ' ' THEN
                            ' '
                        ELSE
                            substr(sobaohiemyte, 4, 2)
END AS kytu45,
                    CASE
                        WHEN nvl(sobaohiemyte, ' ') = ' ' THEN
                            ' '
                        ELSE
                            substr(sobaohiemyte, 6, 2)
END AS kytu67,
                    CASE
                        WHEN nvl(sobaohiemyte, ' ') = ' ' THEN
                            ' '
                        ELSE
                            substr(sobaohiemyte, 8, 3)
END AS kytu8910,
                    CASE
                        WHEN nvl(sobaohiemyte, ' ') = ' ' THEN
                            ' '
                        ELSE
                            substr(sobaohiemyte, 11, 5)
END AS kytucuoi,
                    CASE
                        WHEN v_gioitinh = 1 THEN
                            'X'
                        ELSE
                            ' '
END AS nam,
                    CASE
                        WHEN v_gioitinh = 0 THEN
                            'X'
                        ELSE
                            ' '
END AS nu,
                    nvl(TO_CHAR(ngaybatdau_thebhyt, 'DD/MM/YYYY'), ' ') AS tungay,
                    nvl(TO_CHAR(ngayhethan_thebhyt, 'DD/MM/YYYY'), ' ') AS denngay,
                    CASE
                        WHEN v_gioitinh = 0 THEN
                            'Nữ'
                        ELSE
                            'Nam'
END AS gioitinhtxt,
    --to_char(v_ngayvaovien,'    %H   gi?  %i  phút,    ngày  DD   tháng   MM   nam  YYYY') as giovao,
                    TO_CHAR(v_ngayvaovien, 'HH24')
                    || ' giờ '
                    || TO_CHAR(v_ngayvaovien, 'MI')
                    || ' phút, '
                    || ' ngày '
                    || TO_CHAR(v_ngayvaovien, 'DD')
                    || ' tháng '
                    || TO_CHAR(v_ngayvaovien, 'MM')
                    || ' năm '
                    || TO_CHAR(v_ngayvaovien, 'YYYY') AS giovao,
    --      to_char(thoigianxuatvien,'    %H   gi?  %i  phút,    ngày  DD   tháng   MM   nam  YYYY') as giora,
                    TO_CHAR(thoigianxuatvien, 'HH24')
                    || ' giờ '
                    || TO_CHAR(thoigianxuatvien, 'MI')
                    || ' phút, '
                    || ' ngày '
                    || TO_CHAR(thoigianxuatvien, 'DD')
                    || ' tháng '
                    || TO_CHAR(thoigianxuatvien, 'MM')
                    || ' năm '
                    || TO_CHAR(thoigianxuatvien, 'YYYY') AS giora,
                    TO_CHAR(thoigianxuatvien, 'DD') AS ngayra,
                    TO_CHAR(thoigianxuatvien, 'MM') AS thangra,
                    TO_CHAR(thoigianxuatvien, 'YYYY') AS namra,
                    ngayvao,
                    thoigianxuatvien,
                    noidangkybandau,
                    CASE
                        WHEN nvl(benhkemtheo, ' ') != ' ' THEN
                            icd_benhchinh
                            || ': '
                            || ten_benhchinh
                            || ' - '
                            || benhkemtheo
                        ELSE
                            icd_benhchinh
                            || ': '
                            || ten_benhchinh
END AS chandoan,
                    CASE
                        WHEN p_dvtt = '82011' THEN
                            ''
                        WHEN p_dvtt = '48012' THEN --[DNG] Sua 048/012/17/sobenhan
                            concat('048/012/17/', lpad(substr(v_soluutru, 1, length(v_soluutru) - 5), 6, '0'))
                        WHEN p_dvtt LIKE '96%' THEN
                            v_mabv_tt56
                            || TO_CHAR(ngay_nhapvien, 'YYYY')
                            || '/'
                            || regexp_substr(sobenhan, '[^\/]+', 1, 1)
                        ELSE
                            v_sobenhan
END AS sobenhan,
    --v_sobenhan  as sobenhan
    -- bo sung so benh an khac
                    CASE
                        WHEN p_dvtt = '49017' THEN
                            '503'
                            || '/'
                            || '29'
                            || '/'
                            || v_socuoinam
                            || '/'
                            || lpad(substr(v_sobenhan, 1, length(v_sobenhan) - 5), 6, '0')
                        ELSE
                            v_matinh
                            || '/'
                            || v_mahuyen
                            || '/'
                            || v_socuoinam
                            || '/' --||
    -- ba.sovaovien
END AS ma_yte,
                    nvl(soluutru, '') AS soluutru,
                    ba.sobenhan     sobenhan_bn,
                    nvl(v_soluutru, ba.soxuatvien_bant) AS soluutru_stg, -- VNPTHIS-2517 29/09/2017
                    CASE
                        WHEN nvl(pp_dieutri, ' ') = ' ' THEN
                            CASE
                                WHEN substr(p_dvtt, 0, 2) = '94' THEN
                                    ' '
                                ELSE
                                    'Nội khoa'
END -- VNPTHIS-2650 4/10/2017
ELSE
                            pp_dieutri
END AS pp_dieutri,
    -- VNPTHIS-2517 29/09/2017
                    CASE
                        WHEN v_thamsonguoilienhe = 1 THEN
                            CASE
                                WHEN v_tuoi <= 6 THEN
                                    CASE
                                        WHEN nvl(loidan_bs, ' ') = ' ' THEN
                                            'Uống thuốc theo toa'
                                            || ' - '
                                            || v_nguoilienhe -- VNPTHIS-2650
                                        ELSE
                                            loidan_bs
                                            || ' - '
                                            || v_nguoilienhe -- VNPTHIS-2650
END
ELSE
                                    CASE
                                        WHEN nvl(loidan_bs, ' ') = ' ' THEN
                                            'Uống thuốc theo toa'
                                        ELSE
                                            loidan_bs
END
END
ELSE
                            CASE
                                WHEN nvl(loidan_bs, ' ') = ' ' THEN
                                    'Uống thuốc theo toa'
                                ELSE
                                    loidan_bs
END
END AS loidan_bs_stg,
-- VNPTHIS-2517 29/09/2017
                    CASE
                        WHEN nvl(loidan_bs, ' ') = ' ' THEN
                            'Uống thuốc theo toa'
                        ELSE
                            loidan_bs
END AS loidan_bs,
                    soluutru        AS soravien,
                    CASE
                        WHEN p_dvtt = '89009' THEN
                            ' '
                        ELSE
                            CASE
                                WHEN xv.hentaikham = 1 THEN
                                    TO_CHAR(nvl(xv.ngay_hentaikham, ''), 'dd/mm/yyyy')
                                ELSE
                                    ' '
END
END AS ngay_hentaikham,
                    xv.ghichu,
                    cmu_keystore_tracuu_get(p_dvtt, dot.sovaovien, 'GRV') keysearch,
                    v_ccd           cccd,
                    v_ngaycapcccd   ngaycap
                FROM
                    his_manager.noitru_dotdieutri             dot,
                    his_manager.noitru_luuthongtin_xuatvien   xv,
                    his_manager.noitru_benhan                 ba
                WHERE
                    dot.stt_dotdieutri = p_stt_dotdieutri
                    AND dot.stt_benhan = p_stt_benhan
                    AND dot.dvtt = p_dvtt
                    AND dot.stt_benhan = xv.stt_benhan
                    AND dot.stt_dotdieutri = xv.stt_dotdieutri
                    AND dot.dvtt = xv.dvtt
                    AND xv.stt_benhan = ba.stt_benhan
                    AND xv.dvtt = ba.dvtt;

END;