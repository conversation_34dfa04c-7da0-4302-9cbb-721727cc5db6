<%--
  Created by IntelliJ IDEA.
  User: haht
  Date: 12/12/2024
  Time: 8:38 AM
  To change this template use File | Settings | File Templates.
--%>
<%@page contentType="text/html" pageEncoding="UTF-8"%>
<%@taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>

<div class="col-md-6" id="showModalPhieuChuanDoanNguyenNhanTuVong">
    <div class="card m-1">
        <div class="card-body p-0">
            <div class="d-flex justify-content-between px-3 py-2 card-header-custom">
                <h7 class="font-weight-bold">Phiếu chẩn đoán nguyên nhân tử vong</h7>
                <i class="fa fa-file-text-o" style="font-size: 18px" aria-hidden="true"></i>
            </div>
            <div class="px-3 pb-3">
                <div class="d-flex justify-content-between my-3 py-1 hr-custom">
                    <p class="mb-0" id="dataPhieuChuanDoanNguyenNhanTuVong"></p>
                    <div class="d-flex align-items-center" id="xuLyIconPhieuChuanDoanNguyenNhanTuVong">
                        <i class="fa fa-eye text-primary px-1 handle-icon" aria-hidden="true"
                           id="iconXemPhieuChuanDoanNguyenNhanTuVong"></i>
                        <i class="fa fa-pencil-square-o text-primary px-1 handle-icon"
                           id="iconSuaPhieuChuanDoanNguyenNhanTuVong" aria-hidden="true"></i>
                        <i class="fa fa-trash-o red-text pl-1 handle-icon" aria-hidden="true"
                           id="iconXoaPhieuChuanDoanNguyenNhanTuVong"></i>
                    </div>
                </div>
                <div class="col-md-12 text-right">
                    <button class="btn btn-success form-control-sm line-height-1 mb-2 mb-sm-0 clear-shadow themPhieuChuanDoanNguyenNhanTuVong"
                            type="button">
                        <i class="fa fa-plus-square-o"></i> Thêm phiếu
                    </button>
                    <button id="xemThemDSPhieuChuanDoanNguyenNhanTuVong"
                            class="btn btn-dark btn-outlined form-control-sm line-height-1 ml-sm-2 btn-more clear-shadow"
                            data-toggle="modal" data-target="#modalDSPhieuChuanDoanNguyenNhanTuVong" type="button">
                        <i class="fa fa-ellipsis-v" aria-hidden="true"></i> Xem thêm
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="modal fade" id="modalDSPhieuChuanDoanNguyenNhanTuVong" role="dialog"
     aria-labelledby="modalDSPhieuChuanDoanNguyenNhanTuVong" aria-hidden="true"
     data-keyboard="false" data-backdrop="static">
    <div class="modal-dialog modal-dialog-centered" style="max-width: 92%; width:90%;" role="document">
        <div class="modal-content">
            <div class="modal-header modal-header-sticky py-2 d-flex justify-content-center align-items-center">
                <h6 class="modal-title text-primary font-weight-bold">
                    Danh sách Phiếu chẩn đoán nguyên nhân tử vong
                </h6>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <table id="phieuChuanDoanNguyenNhanTuVongGrid"></table>
            </div>
            <div class="modal-footer modal-footer-sticky py-2">
                <div class="col-md-6 text-right">
                    <button class="btn btn-success form-control-sm line-height-1 themPhieuChuanDoanNguyenNhanTuVong"
                            type="button">
                        <i class="fa fa-plus-square-o"></i> Thêm phiếu
                    </button>
                    <button class="btn btn-success form-control-sm ml-2 line-height-1 clear-shadow" type="button"
                            onclick="phieuChuanDoanNguyenNhanTuVongJS.reloadDSPhieuChuanDoanNguyenNhanTuVong()">
                        <i class="fa fa-refresh" aria-hidden="true"></i> Làm mới
                    </button>
                    <button class="btn btn-default ml-2 form-control-sm line-height-1 clear-shadow"
                            type="button" data-dismiss="modal"> Đóng
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="modal fade" id="modalPhieuChuanDoanNguyenNhanTuVong" role="dialog"
     aria-labelledby="modalPhieuChuanDoanNguyenNhanTuVong" aria-hidden="true"
     data-keyboard="false" data-backdrop="static">
    <div class="modal-dialog modal-dialog-centered" style="max-width: 92%; width: 92%;" role="document">
        <div class="modal-content">
            <div class="modal-header modal-header-sticky py-2 d-flex justify-content-center align-items-center">
                <h6 class="modal-title text-primary font-weight-bold" id="titlePhieuChuanDoanNguyenNhanTuVong"></h6>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <div class="row ml-0 mr-0 mt-2">
                    <div class="col-md-12">
                        <div class="wrap-field-wrapper" id="phieuChuanDoanNguyenNhanTuVong"></div>
                    </div>
                </div>
            </div>
            <div class="modal-footer modal-footer-sticky py-2">
                <div class="col-md-12 text-right">
                    <button class="btn btn-success btn-loading form-control-sm line-height-1 clear-shadow"
                            type="button" id="mauChuanBiPhieuChuanDoanNguyenNhanTuVong">
                        <i class="fa fa fa-clone"></i>
                        Mẫu chuẩn bị
                    </button>
                    <button class="btn btn-success btn-loading ml-2 form-control-sm line-height-1 clear-shadow"
                            type="button" data-action="THEM" id="taoPhieuChuanDoanNguyenNhanTuVong">
                        <span class="spinner-border spinner-border-sm spinner" role="status" aria-hidden="true"></span>
                        <i class="fa fa-plus-square"></i>
                        Tạo phiếu
                    </button>
                    <button class="btn btn-success btn-loading ml-2 form-control-sm line-height-1 clear-shadow"
                            type="button" data-action="SUA" id="suaPhieuChuanDoanNguyenNhanTuVong">
                        <span class="spinner-border spinner-border-sm spinner" role="status" aria-hidden="true"></span>
                        <i class="fa fa-floppy-o"></i>
                        Lưu phiếu
                    </button>
                    <button class="btn btn-default ml-2 form-control-sm line-height-1 clear-shadow"
                            type="button" data-dismiss="modal">
                        Đóng
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>
