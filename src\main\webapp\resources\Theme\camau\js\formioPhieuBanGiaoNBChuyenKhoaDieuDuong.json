{"display": "form", "components": [{"label": "", "labelPosition": "left-left", "applyMaskOn": "change", "customClass": "mr-2", "tableView": true, "hidden": true, "key": "ID", "type": "textfield", "input": true}, {"label": "Columns", "columns": [{"components": [{"label": "<PERSON><PERSON><PERSON> g<PERSON> chuy<PERSON>n", "customClass": "mr-2", "widget": {"type": "calendar", "altInput": true, "allowInput": true, "clickOpens": true, "enableDate": true, "enableTime": true, "mode": "single", "noCalendar": false, "format": "dd/MM/yyyy HH:mm", "useLocaleSettings": false, "hourIncrement": 1, "minuteIncrement": 1, "time_24hr": true, "saveAs": "text", "displayInTimezone": "viewer", "locale": "en"}, "applyMaskOn": "change", "tableView": true, "validate": {"required": true, "customMessage": "<PERSON><PERSON><PERSON><PERSON> gian chuyển là bắt buộc"}, "validateWhenHidden": false, "errorLabel": "<PERSON><PERSON><PERSON><PERSON> gian chuyển là bắt buộc", "key": "NGAY_GIO_CHUYEN", "type": "textfield", "input": true}], "width": 6, "offset": 0, "push": 0, "pull": 0, "size": "md", "currentWidth": 6}, {"components": [{"label": "<PERSON><PERSON><PERSON> sĩ chỉ định chuyển", "widget": "<PERSON><PERSON><PERSON>", "tableView": true, "dataSrc": "json", "data": {"json": "{}"}, "template": "<span>{{ item.TEN_NHANVIEN }}</span>", "validate": {"required": true, "customMessage": "<PERSON><PERSON><PERSON> sĩ chỉ định là bắt buộc"}, "validateWhenHidden": false, "key": "BS_CHI_DINH_CHUYEN", "type": "select", "input": true}], "width": 6, "offset": 0, "push": 0, "pull": 0, "size": "md", "currentWidth": 6}], "key": "columns1", "type": "columns", "customClass": "ml-0 mr-0"}, {"label": "Columns", "columns": [{"components": [{"label": "<PERSON><PERSON><PERSON><PERSON> từ:", "labelPosition": "left-left", "widget": "<PERSON><PERSON><PERSON>", "customClass": "mr-2", "tableView": true, "dataSrc": "json", "template": "<span>{{ item.TENKHOA }}</span>", "validate": {"required": true, "customMessage": "<PERSON><PERSON><PERSON> chuyển là bắ<PERSON> bu<PERSON>c"}, "validateWhenHidden": false, "key": "CHUYEN_TU", "type": "select", "input": true}], "width": 6, "offset": 0, "push": 0, "pull": 0, "size": "md", "currentWidth": 6}, {"components": [{"label": "Đến:", "labelPosition": "left-left", "widget": "<PERSON><PERSON><PERSON>", "tableView": true, "dataSrc": "json", "template": "<span>{{ item.TENKHOA }}</span>", "validate": {"required": true, "customMessage": "<PERSON><PERSON><PERSON> <PERSON>h<PERSON>n là bắ<PERSON> bu<PERSON>c"}, "validateWhenHidden": false, "key": "CHUYEN_DEN", "type": "select", "input": true}], "width": 6, "offset": 0, "push": 0, "pull": 0, "size": "md", "currentWidth": 6}], "key": "columns", "type": "columns", "customClass": "ml-0 mr-0"}, {"label": "Columns", "columns": [{"components": [{"label": "<PERSON>i<PERSON>u dưỡng chuyển:", "labelPosition": "left-left", "widget": "<PERSON><PERSON><PERSON>", "customClass": "mr-2", "tableView": true, "dataSrc": "json", "template": "<span>{{ item.TENNHANVIEN }}</span>", "validate": {"required": true, "customMessage": "<PERSON>iều dưỡng chuyển là bắt buộc"}, "validateWhenHidden": false, "key": "DIEU_DUONG_CHUYEN", "type": "select", "input": true}], "width": 6, "offset": 0, "push": 0, "pull": 0, "size": "md", "currentWidth": 6}, {"components": [{"label": "<PERSON><PERSON><PERSON>u dưỡng nhận:", "labelPosition": "left-left", "widget": "<PERSON><PERSON><PERSON>", "tableView": true, "dataSrc": "json", "template": "<span>{{ item.TENNHANVIEN }}</span>", "validateWhenHidden": false, "key": "DIEU_DUONG_NHAN", "type": "select", "input": true}], "width": 6, "offset": 0, "push": 0, "pull": 0, "size": "md", "currentWidth": 6}], "key": "columns19", "type": "columns", "input": false, "tableView": false, "customClass": "ml-0 mr-0"}, {"label": "Columns", "columns": [{"components": [{"label": "Lý do chuyển:", "labelPosition": "left-left", "applyMaskOn": "change", "customClass": "mr-2", "tableView": true, "validate": {"required": true, "customMessage": "Lý do chuyển là bắt buộc"}, "validateWhenHidden": false, "key": "LY_DO_CHUYEN", "type": "textfield", "input": true}], "width": 6, "offset": 0, "push": 0, "pull": 0, "size": "md", "currentWidth": 6}, {"components": [{"label": "<PERSON>y<PERSON><PERSON> theo yêu cầu của ngư<PERSON>i bệnh/thân nhân", "tableView": false, "validateWhenHidden": false, "key": "CHUYEN_THEO_YC", "type": "checkbox", "input": true, "defaultValue": false}], "width": 6, "offset": 0, "push": 0, "pull": 0, "size": "md", "currentWidth": 6}], "key": "columns1", "type": "columns", "input": false, "tableView": false, "customClass": "ml-0 mr-0"}, {"label": "<PERSON><PERSON><PERSON> đo<PERSON>:", "applyMaskOn": "change", "autoExpand": false, "tableView": true, "validateWhenHidden": false, "key": "CHUAN_DOAN", "type": "textarea", "input": true}, {"label": "Columns", "columns": [{"components": [{"label": "<PERSON><PERSON><PERSON> trạng ng<PERSON><PERSON><PERSON> bệnh:", "labelPosition": "left-left", "optionsLabelPosition": "right", "widget": "<PERSON><PERSON><PERSON>", "customClass": "mr-2", "labelWidth": 35, "inline": true, "tableView": false, "data": {"values": [{"label": "<PERSON><PERSON><PERSON>", "value": "TOT", "shortcut": ""}, {"label": "Nhẹ", "value": "NHE", "shortcut": ""}, {"label": "Nặng", "value": "NANG", "shortcut": ""}]}, "validateWhenHidden": false, "key": "TT_NB", "type": "select", "input": true}], "width": 5, "offset": 0, "push": 0, "pull": 0, "size": "md", "currentWidth": 5}, {"components": [{"label": "<PERSON><PERSON><PERSON> độ tỉnh táo:", "labelPosition": "left-left", "optionsLabelPosition": "right", "widget": "<PERSON><PERSON><PERSON>", "labelWidth": 30, "inline": true, "tableView": false, "data": {"values": [{"label": "Tỉnh táo", "value": "TINH_TAO", "shortcut": ""}, {"label": "B<PERSON>ồn ngủ/ngủ gà", "value": "BUON_NGU", "shortcut": ""}, {"label": "<PERSON><PERSON><PERSON>", "value": "KICH_DONG", "shortcut": ""}, {"label": "Lú lẫn", "value": "LU_LAN", "shortcut": ""}, {"label": "<PERSON><PERSON><PERSON> mê", "value": "HON_ME", "shortcut": ""}]}, "validateWhenHidden": false, "key": "MUC_DO_TINH_TAO", "type": "select", "input": true}], "width": 7, "offset": 0, "push": 0, "pull": 0, "size": "md", "currentWidth": 7}], "key": "columns20", "type": "columns", "customClass": "ml-0 mr-0"}, {"label": "Columns", "columns": [{"components": [{"label": "Đau:", "labelPosition": "left-left", "optionsLabelPosition": "right", "labelWidth": 15, "widget": "<PERSON><PERSON><PERSON>", "customClass": "mr-2", "inline": true, "tableView": false, "data": {"values": [{"label": "K<PERSON>ô<PERSON>", "value": "KHONG", "shortcut": ""}, {"label": "<PERSON><PERSON>", "value": "CO", "shortcut": ""}]}, "validateWhenHidden": false, "key": "DAU", "type": "select", "input": true}], "width": 2, "offset": 0, "push": 0, "pull": 0, "size": "md", "currentWidth": 2}, {"components": [{"label": "<PERSON>g điểm đau:", "labelPosition": "left-left", "applyMaskOn": "change", "labelWidth": 50, "tableView": true, "validateWhenHidden": false, "key": "THANG_DIEM_DAU", "type": "textfield", "customClass": "mr-2", "input": true}], "width": 3, "offset": 0, "push": 0, "pull": 0, "size": "md", "currentWidth": 3}, {"components": [{"label": "<PERSON><PERSON> cơ té ngã:", "labelPosition": "left-left", "optionsLabelPosition": "right", "widget": "<PERSON><PERSON><PERSON>", "labelWidth": 55, "customClass": "mr-2", "inline": true, "tableView": false, "data": {"values": [{"label": "K<PERSON>ô<PERSON>", "value": "KHONG", "shortcut": ""}, {"label": "<PERSON><PERSON>", "value": "CO", "shortcut": ""}]}, "validateWhenHidden": false, "key": "NGUY_CO_TE_NGA", "type": "select", "input": true}], "width": 3, "offset": 0, "push": 0, "pull": 0, "size": "md", "currentWidth": 3}, {"components": [{"label": "<PERSON>g điểm nguy cơ té ngã:", "labelWidth": 55, "labelPosition": "left-left", "applyMaskOn": "change", "tableView": true, "validateWhenHidden": false, "key": "THANG_DIEM_NGUY_CO_TE_NGA", "type": "textfield", "input": true}], "width": 4, "offset": 0, "push": 0, "pull": 0, "size": "md", "currentWidth": 4}], "key": "columns2", "type": "columns", "input": false, "tableView": false, "customClass": "ml-0 mr-0"}, {"label": "Columns", "columns": [{"components": [{"label": "Nhiệt độ:", "applyMaskOn": "change", "customClass": "mr-2", "tableView": true, "validateWhenHidden": false, "validate": {"min": 35, "max": 42}, "key": "NHIET_DO", "type": "number", "input": true}], "width": 3, "offset": 0, "push": 0, "pull": 0, "size": "md", "currentWidth": 3}, {"components": [{"label": "Mạch:", "applyMaskOn": "change", "customClass": "mr-2", "tableView": true, "validateWhenHidden": false, "key": "MACH", "type": "textfield", "input": true}], "width": 3, "offset": 0, "push": 0, "pull": 0, "size": "md", "currentWidth": 3}, {"components": [{"label": "HA:", "applyMaskOn": "change", "customClass": "mr-2", "tableView": true, "validateWhenHidden": false, "key": "HA", "type": "textfield", "input": true}], "size": "md", "width": 2, "currentWidth": 2}, {"components": [{"label": "Nhịp thở:", "applyMaskOn": "change", "customClass": "mr-2", "tableView": true, "validateWhenHidden": false, "key": "NHIP_THO", "type": "textfield", "input": true}], "size": "md", "width": 2, "currentWidth": 2}, {"components": [{"label": "SpO2:", "applyMaskOn": "change", "tableView": true, "validateWhenHidden": false, "key": "SPO2", "type": "textfield", "input": true}], "size": "md", "width": 2, "currentWidth": 2}], "key": "columns4", "type": "columns", "input": false, "tableView": false, "customClass": "ml-0 mr-0"}, {"label": "Columns", "columns": [{"components": [{"label": "Dị ứng:", "labelPosition": "left-left", "optionsLabelPosition": "right", "labelWidth": 20, "inline": true, "tableView": false, "values": [{"label": "K<PERSON>ô<PERSON>", "value": "KHONG", "shortcut": ""}, {"label": "<PERSON><PERSON>", "value": "CO", "shortcut": ""}], "validateWhenHidden": false, "key": "DI_UNG", "type": "radio", "input": true}], "width": 3, "offset": 0, "push": 0, "pull": 0, "size": "md", "currentWidth": 3}, {"components": [{"label": "<PERSON><PERSON><PERSON><PERSON> nhân dị <PERSON>ng:", "labelPosition": "left-left", "applyMaskOn": "change", "labelWidth": 20, "tableView": true, "validateWhenHidden": false, "key": "NGUYEN_NHAN_DI_UNG", "type": "textfield", "input": true}], "width": 9, "offset": 0, "push": 0, "pull": 0, "size": "md", "currentWidth": 9}], "key": "columns5", "type": "columns", "input": false, "tableView": false, "customClass": "ml-0 mr-0"}, {"label": "Columns", "columns": [{"components": [{"label": "<PERSON><PERSON><PERSON><PERSON> t<PERSON><PERSON><PERSON>n tĩnh mạch ngoại biên", "tableView": false, "validateWhenHidden": false, "key": "DT_TM_NGOAI_BIEN", "type": "checkbox", "input": true, "defaultValue": false}], "width": 4, "offset": 0, "push": 0, "pull": 0, "size": "md", "currentWidth": 4}, {"components": [{"label": "Nơi đặt:", "labelPosition": "left-left", "applyMaskOn": "change", "customClass": "mr-2", "tableView": true, "validateWhenHidden": false, "key": "DT_TM_NGOAI_BIEN_NOI_DAT", "type": "textfield", "input": true}], "width": 4, "offset": 0, "push": 0, "pull": 0, "size": "md", "currentWidth": 4}, {"components": [{"label": "Ngày đặt:", "labelPosition": "left-left", "widget": {"type": "calendar", "altInput": true, "allowInput": true, "clickOpens": true, "enableDate": true, "enableTime": true, "mode": "single", "noCalendar": false, "format": "dd/MM/yyyy HH:mm", "useLocaleSettings": false, "hourIncrement": 1, "minuteIncrement": 1, "time_24hr": true, "saveAs": "text", "displayInTimezone": "viewer", "locale": "en"}, "applyMaskOn": "change", "tableView": true, "validateWhenHidden": false, "key": "DT_TM_NGOAI_BIEN_NGAY_DAT", "type": "textfield", "input": true}], "size": "md", "width": 4, "currentWidth": 4}], "key": "columns6", "type": "columns", "input": false, "tableView": false, "customClass": "ml-0 mr-0"}, {"label": "Columns", "columns": [{"components": [{"label": "<PERSON><PERSON><PERSON><PERSON> tru<PERSON><PERSON>n tĩnh mạch trung tâm", "tableView": false, "defaultValue": false, "validateWhenHidden": false, "key": "DT_TM_TT", "type": "checkbox", "input": true}], "width": 4, "offset": 0, "push": 0, "pull": 0, "size": "md", "currentWidth": 4}, {"components": [], "width": 4, "offset": 0, "push": 0, "pull": 0, "size": "md", "currentWidth": 4}, {"components": [{"label": "Ngày đặt:", "labelPosition": "left-left", "widget": {"type": "calendar", "altInput": true, "allowInput": true, "clickOpens": true, "enableDate": true, "enableTime": true, "mode": "single", "noCalendar": false, "format": "dd/MM/yyyy HH:mm", "useLocaleSettings": false, "hourIncrement": 1, "minuteIncrement": 1, "time_24hr": true, "saveAs": "text", "displayInTimezone": "viewer", "locale": "en"}, "applyMaskOn": "change", "tableView": true, "validateWhenHidden": false, "key": "DT_TM_TT_NGAY_DAT", "type": "textfield", "input": true}], "size": "md", "width": 4, "currentWidth": 4}], "key": "columns7", "type": "columns", "input": false, "tableView": false, "customClass": "ml-0 mr-0"}, {"label": "Columns", "columns": [{"components": [{"label": "<PERSON><PERSON><PERSON><PERSON> truyền động mạch", "tableView": false, "defaultValue": false, "validateWhenHidden": false, "key": "DT_DM", "type": "checkbox", "input": true}], "width": 4, "offset": 0, "push": 0, "pull": 0, "size": "md", "currentWidth": 4}, {"components": [], "width": 4, "offset": 0, "push": 0, "pull": 0, "size": "md", "currentWidth": 4}, {"components": [{"label": "Ngày đặt:", "labelPosition": "left-left", "widget": {"type": "calendar", "altInput": true, "allowInput": true, "clickOpens": true, "enableDate": true, "enableTime": true, "mode": "single", "noCalendar": false, "format": "dd/MM/yyyy HH:mm", "useLocaleSettings": false, "hourIncrement": 1, "minuteIncrement": 1, "time_24hr": true, "saveAs": "text", "displayInTimezone": "viewer", "locale": "en"}, "applyMaskOn": "change", "tableView": true, "validateWhenHidden": false, "key": "DT_DM_NGAY_DAT", "type": "textfield", "input": true}], "size": "md", "width": 4, "currentWidth": 4}], "key": "columns8", "type": "columns", "input": false, "tableView": false, "customClass": "ml-0 mr-0"}, {"label": "Columns", "columns": [{"components": [{"label": "Ống thông tiểu", "tableView": false, "defaultValue": false, "validateWhenHidden": false, "key": "ONG_THONG_TIEU", "type": "checkbox", "input": true}], "width": 4, "offset": 0, "push": 0, "pull": 0, "size": "md", "currentWidth": 4}, {"components": [], "width": 4, "offset": 0, "push": 0, "pull": 0, "size": "md", "currentWidth": 4}, {"components": [{"label": "Ngày đặt:", "labelPosition": "left-left", "widget": {"type": "calendar", "altInput": true, "allowInput": true, "clickOpens": true, "enableDate": true, "enableTime": true, "mode": "single", "noCalendar": false, "format": "dd/MM/yyyy HH:mm", "useLocaleSettings": false, "hourIncrement": 1, "minuteIncrement": 1, "time_24hr": true, "saveAs": "text", "displayInTimezone": "viewer", "locale": "en"}, "applyMaskOn": "change", "tableView": true, "validateWhenHidden": false, "key": "ONG_THONG_TIEU_NGAY_DAT", "type": "textfield", "input": true}], "size": "md", "width": 4, "currentWidth": 4}], "key": "columns9", "type": "columns", "input": false, "tableView": false, "customClass": "ml-0 mr-0"}, {"label": "Columns", "columns": [{"components": [{"label": "Dẫn lưu", "tableView": false, "defaultValue": false, "validateWhenHidden": false, "key": "DAN_LUU", "type": "checkbox", "input": true}], "width": 4, "offset": 0, "push": 0, "pull": 0, "size": "md", "currentWidth": 4}, {"components": [{"label": "Nơi đặt 1:", "labelPosition": "left-left", "applyMaskOn": "change", "customClass": "mr-2", "tableView": true, "validateWhenHidden": false, "key": "ONG_DAN_LUU_NOI_DAT_1", "type": "textfield", "input": true}], "width": 4, "offset": 0, "push": 0, "pull": 0, "size": "md", "currentWidth": 4}, {"components": [{"label": "Nơi đặt 2:", "labelPosition": "left-left", "applyMaskOn": "change", "tableView": true, "validateWhenHidden": false, "key": "ONG_DAN_LUU_NOI_DAT_2", "type": "textfield", "input": true}], "size": "md", "width": 4, "currentWidth": 4}], "key": "columns10", "type": "columns", "input": false, "tableView": false, "customClass": "ml-0 mr-0"}, {"label": "Khác:", "labelPosition": "left-left", "labelWidth": 15, "applyMaskOn": "change", "tableView": true, "validateWhenHidden": false, "key": "DT_KHAC", "type": "textfield", "input": true}, {"label": "Columns", "columns": [{"components": [{"label": "Liều thở Oxy", "tableView": false, "defaultValue": false, "validateWhenHidden": false, "key": "LIEU_THO_OXY", "type": "checkbox", "input": true}], "width": 2, "offset": 0, "push": 0, "pull": 0, "size": "md", "currentWidth": 2}, {"components": [{"label": "Liều thở Oxy", "labelPosition": "left-left", "applyMaskOn": "change", "tableView": true, "validateWhenHidden": false, "key": "LIEU_THO_OXY_SO", "type": "textfield", "input": true}], "width": 10, "offset": 0, "push": 0, "pull": 0, "size": "md", "currentWidth": 10}], "key": "columns11", "type": "columns", "input": false, "tableView": false, "customClass": "ml-0 mr-0"}, {"label": "Columns", "columns": [{"components": [{"html": "<p>Da:</p>", "label": "Content", "refreshOnChange": false, "key": "content", "type": "content", "input": false, "tableView": false}], "width": 2, "offset": 0, "push": 0, "pull": 0, "size": "md", "currentWidth": 2}, {"components": [{"label": "<PERSON><PERSON><PERSON> lo<PERSON>t do tì đè (ghi rõ)", "tableView": false, "validateWhenHidden": false, "key": "LOET", "type": "checkbox", "input": true, "defaultValue": false}], "width": 5, "offset": 0, "push": 0, "pull": 0, "size": "md", "currentWidth": 5}, {"components": [{"label": "<PERSON><PERSON><PERSON> vế<PERSON>:", "labelPosition": "left-left", "applyMaskOn": "change", "tableView": true, "validateWhenHidden": false, "key": "LOET_MIEU_TA", "type": "textfield", "input": true}], "size": "md", "width": 5, "currentWidth": 5}], "key": "columns12", "type": "columns", "input": false, "tableView": false, "customClass": "ml-0 mr-0"}, {"label": "Columns", "columns": [{"components": [], "width": 2, "offset": 0, "push": 0, "pull": 0, "size": "md", "currentWidth": 2}, {"components": [{"label": "<PERSON><PERSON><PERSON> vết thương (g<PERSON> r<PERSON>, ví dụ như băng đơn giản/vừa phải/phức tạp/bỏng...)", "tableView": false, "validateWhenHidden": false, "key": "BANG_VET_THUONG", "type": "checkbox", "input": true, "defaultValue": false}], "width": 5, "offset": 0, "push": 0, "pull": 0, "size": "md", "currentWidth": 5}, {"components": [], "size": "md", "width": 5, "currentWidth": 5}], "key": "columns13", "type": "columns", "input": false, "tableView": false, "customClass": "ml-0 mr-0"}, {"label": "Columns", "columns": [{"components": [], "width": 2, "offset": 0, "push": 0, "pull": 0, "size": "md", "currentWidth": 2}, {"components": [{"label": "<PERSON><PERSON> rõ băng vế<PERSON> th<PERSON>:", "labelPosition": "left-left", "applyMaskOn": "change", "tableView": true, "validateWhenHidden": false, "key": "BANG_VET_THUONG_GHI_RO", "type": "textfield", "input": true}], "width": 10, "offset": 0, "push": 0, "pull": 0, "size": "md", "currentWidth": 10}], "key": "columns15", "type": "columns", "input": false, "tableView": false, "customClass": "ml-0 mr-0"}, {"label": "Columns", "columns": [{"components": [], "width": 2, "offset": 0, "push": 0, "pull": 0, "size": "md", "currentWidth": 2}, {"components": [{"label": "<PERSON><PERSON><PERSON> cắt chỉ:", "labelPosition": "left-left", "widget": {"type": "calendar", "altInput": true, "allowInput": true, "clickOpens": true, "enableDate": true, "enableTime": false, "mode": "single", "noCalendar": false, "format": "dd/MM/yyyy", "dateFormat": "yyyy-MM-ddTHH:mm:ssZ", "useLocaleSettings": false, "hourIncrement": 1, "minuteIncrement": 1, "time_24hr": true, "saveAs": "text", "displayInTimezone": "viewer", "locale": "en"}, "applyMaskOn": "change", "tableView": true, "validateWhenHidden": false, "key": "BANG_VET_THUONG_NGAY_CAT_CHI", "type": "textfield", "input": true}], "width": 10, "offset": 0, "push": 0, "pull": 0, "size": "md", "currentWidth": 10}], "key": "columns14", "type": "columns", "input": false, "tableView": false, "customClass": "ml-0 mr-0"}, {"label": "Columns", "columns": [{"components": [{"label": "Dinh dưỡng:", "labelPosition": "left-left", "optionsLabelPosition": "right", "inline": true, "tableView": false, "values": [{"label": "<PERSON><PERSON><PERSON>n ăn uống", "value": "NHIN_AN", "shortcut": ""}, {"label": "<PERSON>ua <PERSON>ng thông", "value": "QUA_ONG_THONG", "shortcut": ""}, {"label": "<PERSON><PERSON> độ ăn", "value": "CHE_DO_AN", "shortcut": ""}], "validateWhenHidden": false, "key": "DINH_DUONG", "type": "radio", "input": true}], "width": 6, "offset": 0, "push": 0, "pull": 0, "size": "md", "currentWidth": 6}, {"components": [{"label": "<PERSON><PERSON> độ ăn:", "labelPosition": "left-left", "applyMaskOn": "change", "tableView": true, "validateWhenHidden": false, "key": "DINH_DUONG_CHE_DO_AN", "type": "textfield", "input": true}], "width": 6, "offset": 0, "push": 0, "pull": 0, "size": "md", "currentWidth": 6}], "key": "columns16", "type": "columns", "input": false, "tableView": false, "customClass": "ml-0 mr-0"}, {"label": "Columns", "columns": [{"components": [{"label": "<PERSON>ậ<PERSON> động:", "labelPosition": "left-left", "optionsLabelPosition": "right", "widget": "<PERSON><PERSON><PERSON>", "inline": true, "tableView": false, "data": {"values": [{"label": "<PERSON><PERSON><PERSON><PERSON> ph<PERSON> thuộc", "value": "KHONG_PHU_THUOC", "shortcut": ""}, {"label": "<PERSON>e lăn", "value": "XE_LAN", "shortcut": ""}, {"label": "<PERSON><PERSON><PERSON> g<PERSON>", "value": "NGOI_GHE", "shortcut": ""}, {"label": "Nằm tuyệt đối tại giường", "value": "NAM", "shortcut": ""}]}, "validateWhenHidden": false, "key": "VAN_DONG", "type": "select", "input": true, "customClass": "mr-2"}], "width": 6, "offset": 0, "push": 0, "pull": 0, "size": "md", "currentWidth": 6}, {"components": [{"label": "<PERSON><PERSON><PERSON> tiết:", "labelPosition": "left-left", "optionsLabelPosition": "right", "widget": "<PERSON><PERSON><PERSON>", "inline": true, "tableView": false, "data": {"values": [{"label": "<PERSON><PERSON><PERSON><PERSON> có tự chủ", "value": "TIEU_CO_TU_CHU", "shortcut": ""}, {"label": "<PERSON><PERSON><PERSON><PERSON> không tự chủ", "value": "TIEU_KHONG_TU_CHU", "shortcut": ""}, {"label": "Qua lỗ bài tiết nhân tạo", "value": "QUA_LO_BAI_TIET_NHAN_TAO", "shortcut": ""}]}, "validateWhenHidden": false, "key": "BAI_TIET", "type": "select", "input": true}], "width": 6, "offset": 0, "push": 0, "pull": 0, "size": "md", "currentWidth": 6}], "key": "columns21", "type": "columns", "input": false, "tableView": false, "customClass": "ml-0 mr-0"}, {"label": "Columns", "columns": [{"components": [{"label": "<PERSON><PERSON><PERSON><PERSON> điều trị trong ngày:", "labelPosition": "left-left", "optionsLabelPosition": "right", "inline": true, "tableView": false, "values": [{"label": "K<PERSON>ô<PERSON>", "value": "KHONG", "shortcut": ""}, {"label": "<PERSON><PERSON>", "value": "CO", "shortcut": ""}], "validateWhenHidden": false, "key": "THUOC_DT_TRONG_NGAY", "type": "radio", "input": true}], "width": 4, "offset": 0, "push": 0, "pull": 0, "size": "md", "currentWidth": 4}, {"components": [{"label": "<PERSON><PERSON><PERSON>", "labelPosition": "left-left", "applyMaskOn": "change", "tableView": true, "validateWhenHidden": false, "key": "TEN_THUOC_DT", "type": "textfield", "input": true, "conditional": {"show": true, "when": "THUOC_DT_TRONG_NGAY", "eq": "CO"}}], "width": 4, "offset": 0, "push": 0, "pull": 0, "size": "md", "currentWidth": 4}, {"components": [{"label": "Lúc:", "labelPosition": "left-left", "labelWidth": 15, "widget": {"type": "calendar", "altInput": true, "allowInput": true, "clickOpens": true, "enableDate": true, "enableTime": true, "mode": "single", "noCalendar": false, "format": "dd/MM/yyyy HH:mm", "useLocaleSettings": false, "hourIncrement": 1, "minuteIncrement": 1, "time_24hr": true, "saveAs": "text", "displayInTimezone": "viewer", "locale": "en"}, "applyMaskOn": "change", "tableView": true, "validateWhenHidden": false, "key": "THUOC_DT_TRONG_NGAY_LUC", "type": "textfield", "input": true, "conditional": {"show": true, "when": "THUOC_DT_TRONG_NGAY", "eq": "CO"}}], "width": 4, "offset": 0, "push": 0, "pull": 0, "size": "md", "currentWidth": 4}], "key": "columns17", "type": "columns", "input": false, "tableView": false, "customClass": "ml-0 mr-0"}, {"label": "Columns", "columns": [{"components": [{"label": "<PERSON><PERSON><PERSON><PERSON> cần sử dụng tiếp:", "labelPosition": "left-left", "optionsLabelPosition": "right", "inline": true, "tableView": false, "values": [{"label": "K<PERSON>ô<PERSON>", "value": "KHONG", "shortcut": ""}, {"label": "<PERSON><PERSON>", "value": "CO", "shortcut": ""}], "validateWhenHidden": false, "key": "THUOC_CAN_SD", "type": "radio", "input": true}], "width": 4, "offset": 0, "push": 0, "pull": 0, "size": "md", "currentWidth": 4}, {"components": [{"label": "<PERSON><PERSON><PERSON>", "labelPosition": "left-left", "applyMaskOn": "change", "tableView": true, "validateWhenHidden": false, "key": "TEN_THUOC_TIEP", "type": "textfield", "input": true, "conditional": {"show": true, "when": "THUOC_CAN_SD", "eq": "CO"}}], "width": 4, "offset": 0, "push": 0, "pull": 0, "size": "md", "currentWidth": 4}, {"components": [{"label": "Lúc:", "labelPosition": "left-left", "labelWidth": 15, "widget": {"type": "calendar", "altInput": true, "allowInput": true, "clickOpens": true, "enableDate": true, "enableTime": true, "mode": "single", "noCalendar": false, "format": "dd/MM/yyyy HH:mm", "useLocaleSettings": false, "hourIncrement": 1, "minuteIncrement": 1, "time_24hr": true, "saveAs": "text", "displayInTimezone": "viewer", "locale": "en"}, "applyMaskOn": "change", "tableView": true, "validateWhenHidden": false, "key": "THUOC_CAN_SD_LUC", "type": "textfield", "input": true, "conditional": {"show": true, "when": "THUOC_CAN_SD", "eq": "CO"}}], "width": 4, "offset": 0, "push": 0, "pull": 0, "size": "md", "currentWidth": 4}], "key": "columns18", "type": "columns", "input": false, "tableView": false, "customClass": "ml-0 mr-0"}, {"label": "Columns", "columns": [{"components": [{"label": "<PERSON><PERSON><PERSON> li<PERSON>u bàn giao:", "labelPosition": "left-left", "optionsLabelPosition": "right", "inline": true, "tableView": false, "values": [{"label": "<PERSON><PERSON> s<PERSON> b<PERSON><PERSON>n", "value": "HS_BENH_AN", "shortcut": ""}, {"label": "<PERSON><PERSON><PERSON> dụng cá nhân", "value": "VAT_DUNG_CA_NHAN", "shortcut": ""}, {"label": "K<PERSON><PERSON><PERSON>", "value": "KHAC", "shortcut": ""}], "validateWhenHidden": false, "key": "TAI_LIEU_BAN_GIAO", "type": "selectboxes", "input": true, "inputType": "checkbox", "defaultValue": {"HS_BENH_AN": false, "VAT_DUNG_CA_NHAN": false, "KHAC": false}}], "width": 4, "offset": 0, "push": 0, "pull": 0, "size": "md", "currentWidth": 4}, {"components": [{"label": "<PERSON><PERSON><PERSON> l<PERSON>:", "labelPosition": "left-left", "applyMaskOn": "change", "tableView": true, "validateWhenHidden": false, "key": "TAI_LIEU_BAN_GIAO_KHAC", "type": "textfield", "input": true, "conditional": {"show": true, "when": "TAI_LIEU_BAN_GIAO.KHAC", "eq": true}}], "width": 8, "offset": 0, "push": 0, "pull": 0, "size": "md", "currentWidth": 8}], "key": "columns18", "type": "columns", "input": false, "tableView": false, "customClass": "ml-0 mr-0"}]}