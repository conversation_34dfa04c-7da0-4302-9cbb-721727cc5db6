create or replace FUNCTION cmu_ins_bangkeat_kymb (
    p_dvtt            VARCHAR2,
    p_ma<PERSON><PERSON>han      VARCHAR2,
    p_sovaovien       VARCHAR2,
    p_nhanvientruoc   VARCHAR2,
    p_nhanvientrong   VARCHAR2,
    p_nhanviensau     VARCHAR2,
    p_id_phauthuat    VARCHAR2
) RETURN VARCHAR2 IS
    v_exist    NUMBER := 0;
    v_return   VARCHAR2(22) := '';
BEGIN
    v_return := cmu_chokyso_mobile_ins(p_dvtt, p_ma<PERSON><PERSON><PERSON>, p_sovaovien, 'Bảng kiểm an toàn vị trí trước gây mê', 'PHIEU_NOITRU_KIEMTRAANTOANPT_TRUOCGAYME'
    , p_id_phauthuat, p_nhanvientruoc);

    v_return := cmu_chokyso_mobile_ins(p_dvtt, p_ma<PERSON><PERSON><PERSON>, p_sovaovien, '<PERSON><PERSON><PERSON> kiểm an toàn vị trí trước rạch da', 'PHIEU_NOITRU_KIEMTRAANTOANPT_TRUOCPHAUTHUAT'

    , p_id_phauth<PERSON>t, p_nhanvientrong);

    v_return := cmu_chokyso_mobile_ins(p_dvtt, p_mabenhnhan, p_sovaovien, 'Bảng kiểm an toàn vị trí trước khi rời phòng', 'PHIEU_NOITRU_KIEMTRAANTOANPT_TRUOCKHICHUYEN'

    , p_id_phauthuat, p_nhanviensau);

RETURN 0;
END;