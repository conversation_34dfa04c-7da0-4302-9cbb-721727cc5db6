function getUrlVoBenhAnTrang1(params) {
    return new Promise(async (resolve, reject) => {
        try {
            var url = String.format('print-vobenhan-by-id?iD={0}&loaiBenhAn={1}&soVaoVien={2}&soVaoVienDt={3}&smartcafiletype={4}&pageToPrint=1',
                params.ID,
                params.ID_VBA,
                thongtinhsba.thongtinbn.SOVAOVIEN,
                thongtinhsba.thongtinbn.SOVAOVIEN_DT,
                'pdf');
            resolve({
                url: url,
                kySo: 0,
                isError: 0,
                message: 'Thành công'
            });
        } catch (e) {
            console.log("Lỗi get file ký số vỏ bệnh án trang 1: " + e)
            reject({
                url: '',
                kySo: 0,
                isError: 1,
                message: 'Lỗi get file ký số vỏ bệnh án trang 1: ' + e
            });
        }
    });
}

function getUrlVoBenhAnTrang2(params) {
    return new Promise(async (resolve, reject) => {
        try {
            getFilesign769(
                "PHIEU_NOITRU_VBATRANG2",
                params.ID,
                -1,
                singletonObject.dvtt,
                thongtinhsba.thongtinbn.SOVAOVIEN,
                thongtinhsba.thongtinbn.SOVAOVIEN_DT,
                '-1'
                ,function(dataKySo) {
                    if(dataKySo.length > 0) {
                        getCMUFileSigned769GetLink(dataKySo[0].KEYMINIO, 'pdf').then(pdfData => {
                            resolve({
                                url: pdfData,
                                kySo: 1,
                                isError: 0,
                                message: 'Thành công',
                                idKySo: dataKySo[0].ID
                            });
                        }).catch(e => {
                            resolve({
                                url: '',
                                kySo: 0,
                                isError: 1,
                                message: 'Lỗi get file ký số'
                            });
                        });
                    } else {
                        if (params.ID_VBA == "NGOAITRU_CHUNG"){
                            var url = String.format('print-vobenhan-by-id?iD={0}&loaiBenhAn={1}&soVaoVien={2}&soVaoVienDt={3}&smartcafiletype={4}&pageToPrint=1',
                                params.ID,
                                params.ID_VBA,
                                thongtinhsba.thongtinbn.SOVAOVIEN,
                                thongtinhsba.thongtinbn.SOVAOVIEN_DT,
                                'pdf');
                            resolve({
                                url: url,
                                kySo: 0,
                                isError: 0,
                                message: 'Thành công'
                            });
                        } else {
                            var url = String.format('print-vobenhan-by-id?iD={0}&loaiBenhAn={1}&soVaoVien={2}&soVaoVienDt={3}&smartcafiletype={4}&pageToPrint=2',
                                params.ID,
                                params.ID_VBA,
                                thongtinhsba.thongtinbn.SOVAOVIEN,
                                thongtinhsba.thongtinbn.SOVAOVIEN_DT,
                                'pdf');
                            resolve({
                                url: url,
                                kySo: 0,
                                isError: 0,
                                message: 'Thành công'
                            });
                        }

                    }
                });
        } catch (e) {
            console.log("Lỗi get file ký số vỏ bệnh án trang 2: " + e)
            reject({
                url: '',
                kySo: 0,
                isError: 1,
                message: 'Lỗi get file ký số vỏ bệnh án trang 2: ' + e
            });
        }
    });
}

function getUrlVoBenhAnTrang3(params) {
    return new Promise(async (resolve, reject) => {
        try {
            getFilesign769(
                "PHIEU_NOITRU_VBATRANG3",
                params.ID,
                -1,
                singletonObject.dvtt,
                thongtinhsba.thongtinbn.SOVAOVIEN,
                thongtinhsba.thongtinbn.SOVAOVIEN_DT,
                '-1'
                ,function(dataKySo) {
                    if(dataKySo.length > 0) {
                        getCMUFileSigned769GetLink(dataKySo[0].KEYMINIO, 'pdf').then(pdfData => {
                            resolve({
                                url: pdfData,
                                kySo: 1,
                                isError: 0,
                                message: 'Thành công',
                                idKySo: dataKySo[0].ID
                            });
                        }).catch(e => {
                            resolve({
                                url: '',
                                kySo: 0,
                                isError: 1,
                                message: 'Lỗi get file ký số'
                            });
                        });
                    } else {
                        var url = String.format('print-vobenhan-by-id?iD={0}&loaiBenhAn={1}&soVaoVien={2}&soVaoVienDt={3}&smartcafiletype={4}&pageToPrint=3',
                            params.ID,
                            params.ID_VBA,
                            thongtinhsba.thongtinbn.SOVAOVIEN,
                            thongtinhsba.thongtinbn.SOVAOVIEN_DT,
                            'pdf');
                        resolve({
                            url: url,
                            kySo: 0,
                            isError: 0,
                            message: 'Thành công'
                        });
                    }
                });
        } catch (e) {
            console.log("Lỗi get file ký số vỏ bệnh án trang 3: " + e)
            reject({
                url: '',
                kySo: 0,
                isError: 1,
                message: 'Lỗi get file ký số vỏ bệnh án trang 3: ' + e
            });
        }
    });
}

function getUrlHuongDanNoiQuy(params) {
    return new Promise(async (resolve, reject) => {
        try {
            var arrTemp = [];
            getFilesign769("PHIEU_NOITRU_NOIQUYBENHVIEN_HUONGDAN", params.ID, -1, singletonObject.dvtt,
                thongtinhsba.thongtinbn.SOVAOVIEN, thongtinhsba.thongtinbn.SOVAOVIEN_DT, -1, function(dataKySo) {
                    if (dataKySo.length > 0) {
                        arrTemp.push(dataKySo[0])
                    }
                });
            getFilesign769("PHIEU_NOITRU_NOIQUYBENHVIEN_CAMKET", params.ID, -1, singletonObject.dvtt,
                thongtinhsba.thongtinbn.SOVAOVIEN, thongtinhsba.thongtinbn.SOVAOVIEN_DT, -1, function(dataKySo) {
                    if (dataKySo.length > 0) {
                        arrTemp.push(dataKySo[0])
                    }
                });
            var maxCreateDate = null;
            var maxCreateDataObject = null;
            if (arrTemp.length > 0) {
                $.each(arrTemp, function (index, dataObject) {
                    var createDate = moment(dataObject.CREATE_DATE_STRING, "DD/MM/YYYY HH:mm:ss");
                    if (maxCreateDate === null || createDate.isAfter(maxCreateDate)) {
                        maxCreateDate = createDate;
                        maxCreateDataObject = dataObject;
                    }
                });
                getCMUFileSigned769GetLink(maxCreateDataObject.KEYMINIO, 'pdf').then(pdfData => {
                    resolve({
                        url: pdfData,
                        kySo: 1,
                        isError: 0,
                        message: 'Thành công',
                        idKySo: maxCreateDataObject.ID
                    });
                }).catch(e => {
                    resolve({
                        url: '',
                        kySo: 0,
                        isError: 1,
                        message: 'Lỗi get file ký số'
                    });
                });
            } else {
                var paramsReport = {
                    dvtt: singletonObject.dvtt,
                    sovaovien: thongtinhsba.thongtinbn.SOVAOVIEN,
                    mabenhnhan: thongtinhsba.thongtinbn.MABENHNHAN
                }
                var url = 'cmu_in_cmu_hdthnoiquy?type=pdf&' + $.param(paramsReport);
                resolve({
                    url: url,
                    kySo: 0,
                    isError: 0,
                    message: 'Thành công'
                });
            }
        } catch (e) {
            console.log("Lỗi get file ký số phiếu hướng dẫn nội quy: " + error)
            reject({
                url: '',
                kySo: 0,
                isError: 1,
                message: 'Lỗi get file ký số phiếu hướng dẫn nội quy: ' + e
            });
        }
    });
}

function getUrlDuyetPTDV(params) {
    return new Promise(async (resolve, reject) => {
        try {
            var arrTemp = [];
            getFilesign769("PHIEU_NOITRU_PHIEUDUYETPTDVU_GIAMDOC", params.ID, -1, singletonObject.dvtt,
                thongtinhsba.thongtinbn.SOVAOVIEN, thongtinhsba.thongtinbn.SOVAOVIEN_DT, -1, function(dataKySo) {
                    if (dataKySo.length > 0) {
                        arrTemp.push(dataKySo[0])
                    }
                });
            getFilesign769("PHIEU_NOITRU_PHIEUDUYETPTDVU_TRUONGKHOA", params.ID, -1, singletonObject.dvtt,
                thongtinhsba.thongtinbn.SOVAOVIEN, thongtinhsba.thongtinbn.SOVAOVIEN_DT, -1, function(dataKySo) {
                    if (dataKySo.length > 0) {
                        arrTemp.push(dataKySo[0])
                    }
                });
            var maxCreateDate = null;
            var maxCreateDataObject = null;
            if (arrTemp.length > 0) {
                $.each(arrTemp, function (index, dataObject) {
                    var createDate = moment(dataObject.CREATE_DATE_STRING, "DD/MM/YYYY HH:mm:ss");
                    if (maxCreateDate === null || createDate.isAfter(maxCreateDate)) {
                        maxCreateDate = createDate;
                        maxCreateDataObject = dataObject;
                    }
                });
                getCMUFileSigned769GetLink(maxCreateDataObject.KEYMINIO, 'pdf').then(pdfData => {
                    resolve({
                        url: pdfData,
                        kySo: 1,
                        isError: 0,
                        message: 'Thành công',
                        idKySo: maxCreateDataObject.ID
                    });
                }).catch(e => {
                    resolve({
                        url: '',
                        kySo: 0,
                        isError: 1,
                        message: 'Lỗi get file ký số'
                    });
                });
            } else {
                var text_date_part = params.NGAY_TAO_PHIEU.split("/");
                var text_date = text_date_part[0];
                var text_month = text_date_part[1];
                var text_year = text_date_part[2];
                var params2 = {
                    magiay: params.ID,
                    mabenhnhan: thongtinhsba.thongtinbn.MABENHNHAN,
                    diachi: thongtinhsba.thongtinbn.DIA_CHI,
                    sdt: thongtinhsba.thongtinbn.SO_DIEN_THOAI,
                    tenbenhnhan: thongtinhsba.thongtinbn.TEN_BENH_NHAN,
                    sobenhan: thongtinhsba.thongtinbn.SOBENHAN,
                    sovaovien: thongtinhsba.thongtinbn.SOVAOVIEN,
                    gioitinh: thongtinhsba.thongtinbn.GIOI_TINH,
                    tuoi: thongtinhsba.thongtinbn.TUOI,
                    text_date,
                    text_month,
                    text_year
                }
                var url = 'cmu_in_cmu_phieuduyetpt_dvu?type=pdf&' + $.param(params2);
                resolve({
                    url: url,
                    kySo: 0,
                    isError: 0,
                    message: 'Thành công'
                });
            }
        } catch (e) {
            console.log("Lỗi get file ký số phiếu duyệt phẫu thuật dịch vụ: " + e)
            reject({
                url: '',
                kySo: 0,
                isError: 1,
                message: 'Lỗi get file ký số phiếu duyệt phẫu thuật dịch vụ: ' + e
            });
        }
    });
}

function getUrlYCSDKhangSinhUT(params) {
    return new Promise(async (resolve, reject) => {
        try {
            var arrTemp = [];
            getFilesign769("PHIEU_NOITRU_YCSDKHANGSINHUT_BACSI", params.ID, -1, singletonObject.dvtt,
                thongtinhsba.thongtinbn.SOVAOVIEN, thongtinhsba.thongtinbn.SOVAOVIEN_DT, -1, function(dataKySo) {
                    if (dataKySo.length > 0) {
                        arrTemp.push(dataKySo[0])
                    }
                });
            getFilesign769("PHIEU_NOITRU_YCSDKHANGSINHUT_LANHDAO", params.ID, -1, singletonObject.dvtt,
                thongtinhsba.thongtinbn.SOVAOVIEN, thongtinhsba.thongtinbn.SOVAOVIEN_DT, -1, function(dataKySo) {
                    if (dataKySo.length > 0) {
                        arrTemp.push(dataKySo[0])
                    }
                });
            var maxCreateDate = null;
            var maxCreateDataObject = null;
            if (arrTemp.length > 0) {
                $.each(arrTemp, function (index, dataObject) {
                    var createDate = moment(dataObject.CREATE_DATE_STRING, "DD/MM/YYYY HH:mm:ss");
                    if (maxCreateDate === null || createDate.isAfter(maxCreateDate)) {
                        maxCreateDate = createDate;
                        maxCreateDataObject = dataObject;
                    }
                });
                getCMUFileSigned769GetLink(maxCreateDataObject.KEYMINIO, 'pdf').then(pdfData => {
                    resolve({
                        url: pdfData,
                        kySo: 1,
                        isError: 0,
                        message: 'Thành công',
                        idKySo: maxCreateDataObject.ID
                    });
                }).catch(e => {
                    resolve({
                        url: '',
                        kySo: 0,
                        isError: 1,
                        message: 'Lỗi get file ký số'
                    });
                });
            } else {
                var params2 = {
                    magiay: params.ID,
                    mabenhnhan: thongtinhsba.thongtinbn.MABENHNHAN
                }
                var url = 'cmu_in_cmu_phieu_sd_khangsinh_uutien_combine?type=pdf&' + $.param(params2);
                resolve({
                    url: url,
                    kySo: 0,
                    isError: 0,
                    message: 'Thành công'
                });
            }
        } catch (e) {
            console.log("Lỗi get file ký số phiếu yêu cầu sử dụng kháng sinh ưu tiên: " + e)
            reject({
                url: '',
                kySo: 0,
                isError: 1,
                message: 'Lỗi get file ký số phiếu yêu cầu sử dụng kháng sinh ưu tiên: ' + e
            });
        }
    });
}

function getUrlBienBanTuVong(params) {
    return new Promise(async (resolve, reject) => {
        try {
            var arrTemp = [];
            getFilesign769("PHIEU_NOITRU_BIENBANTUVONG_THUKY", params.ID, -1, singletonObject.dvtt,
                thongtinhsba.thongtinbn.SOVAOVIEN, thongtinhsba.thongtinbn.SOVAOVIEN_DT, -1, function(dataKySo) {
                    if (dataKySo.length > 0) {
                        arrTemp.push(dataKySo[0])
                    }
                });
            getFilesign769("PHIEU_NOITRU_BIENBANTUVONG_CHUTOA", params.ID, -1, singletonObject.dvtt,
                thongtinhsba.thongtinbn.SOVAOVIEN, thongtinhsba.thongtinbn.SOVAOVIEN_DT, -1, function(dataKySo) {
                    if (dataKySo.length > 0) {
                        arrTemp.push(dataKySo[0])
                    }
                });
            var maxCreateDate = null;
            var maxCreateDataObject = null;
            if (arrTemp.length > 0) {
                $.each(arrTemp, function (index, dataObject) {
                    var createDate = moment(dataObject.CREATE_DATE_STRING, "DD/MM/YYYY HH:mm:ss");
                    if (maxCreateDate === null || createDate.isAfter(maxCreateDate)) {
                        maxCreateDate = createDate;
                        maxCreateDataObject = dataObject;
                    }
                });
                getCMUFileSigned769GetLink(maxCreateDataObject.KEYMINIO, 'pdf').then(pdfData => {
                    resolve({
                        url: pdfData,
                        kySo: 1,
                        isError: 0,
                        message: 'Thành công',
                        idKySo: maxCreateDataObject.ID
                    });
                }).catch(e => {
                    resolve({
                        url: '',
                        kySo: 0,
                        isError: 1,
                        message: 'Lỗi get file ký số'
                    });
                });
            } else {
                var params2 = {
                    mabenhnhan: thongtinhsba.thongtinbn.MABENHNHAN,
                    sobenhan: thongtinhsba.thongtinbn.SOBENHAN
                }
                var url = 'cmu_in_cmu_bienbantuvong?type=pdf&' + $.param(params2);
                resolve({
                    url: url,
                    kySo: 0,
                    isError: 0,
                    message: 'Thành công'
                });
            }
        } catch (e) {
            console.log("Lỗi get file ký số biên bản tử vong: " + e)
            reject({
                url: '',
                kySo: 0,
                isError: 1,
                message: 'Lỗi get file ký số biên bản tử vong: ' + e
            });
        }
    });
}

function getUrlBienBanKiemThaoTV(params) {
    return new Promise(async (resolve, reject) => {
        try {
            var arrTemp = [];
            getFilesign769("PHIEU_NOITRU_BIENBANKIEMTHAOTV_THUKY", thongtinhsba.thongtinbn.SOVAOVIEN, -1, singletonObject.dvtt,
                thongtinhsba.thongtinbn.SOVAOVIEN, thongtinhsba.thongtinbn.SOVAOVIEN_DT, -1, function(dataKySo) {
                    if (dataKySo.length > 0) {
                        arrTemp.push(dataKySo[0])
                    }
                });
            getFilesign769("PHIEU_NOITRU_BIENBANKIEMTHAOTV_CHUTRI", thongtinhsba.thongtinbn.SOVAOVIEN, -1, singletonObject.dvtt,
                thongtinhsba.thongtinbn.SOVAOVIEN, thongtinhsba.thongtinbn.SOVAOVIEN_DT, -1, function(dataKySo) {
                    if (dataKySo.length > 0) {
                        arrTemp.push(dataKySo[0])
                    }
                });
            var maxCreateDate = null;
            var maxCreateDataObject = null;
            if (arrTemp.length > 0) {
                $.each(arrTemp, function (index, dataObject) {
                    var createDate = moment(dataObject.CREATE_DATE_STRING, "DD/MM/YYYY HH:mm:ss");
                    if (maxCreateDate === null || createDate.isAfter(maxCreateDate)) {
                        maxCreateDate = createDate;
                        maxCreateDataObject = dataObject;
                    }
                });
                getCMUFileSigned769GetLink(maxCreateDataObject.KEYMINIO, 'pdf').then(pdfData => {
                    resolve({
                        url: pdfData,
                        kySo: 1,
                        isError: 0,
                        message: 'Thành công',
                        idKySo: maxCreateDataObject.ID
                    });
                }).catch(e => {
                    resolve({
                        url: '',
                        kySo: 0,
                        isError: 1,
                        message: 'Lỗi get file ký số'
                    });
                });
            } else {
                var params2 = {
                    tenbenhnhan: thongtinhsba.thongtinbn.TEN_BENH_NHAN,
                    mabenhnhan: thongtinhsba.thongtinbn.MABENHNHAN,
                    namsinh: thongtinhsba.thongtinbn.NGAY_SINH.split("/")[2],
                    diachi: thongtinhsba.thongtinbn.DIA_CHI,
                    gioitinh: params.GIOI_TINH,
                    sovaovien: thongtinhsba.thongtinbn.SOVAOVIEN,
                    thoigianvaovien: params.THOIGIANVAOVIEN
                }
                var url = 'cmu_in_cmu_bienbankiemthaotv?type=pdf&' + $.param(params2);
                resolve({
                    url: url,
                    kySo: 0,
                    isError: 0,
                    message: 'Thành công'
                });
            }
        } catch (e) {
            console.log("Lỗi get file ký số biên bản kiểm thảo tử vong: " + e)
            reject({
                url: '',
                kySo: 0,
                isError: 1,
                message: 'Lỗi get file ký số biên bản kiểm thảo tử vong: ' + e
            });
        }
    });
}

function getUrlBienBanHop(params) {
    return new Promise(async (resolve, reject) => {
        try {
            var arrTemp = [];
            getFilesign769("PHIEU_NOITRU_BIENBANHOP_DAIDIEN", params.ID, -1, singletonObject.dvtt,
                thongtinhsba.thongtinbn.SOVAOVIEN, thongtinhsba.thongtinbn.SOVAOVIEN_DT, -1, function(dataKySo) {
                    if (dataKySo.length > 0) {
                        arrTemp.push(dataKySo[0])
                    }
                });
            getFilesign769("PHIEU_NOITRU_BIENBANHOP_THUKY", params.ID, -1, singletonObject.dvtt,
                thongtinhsba.thongtinbn.SOVAOVIEN, thongtinhsba.thongtinbn.SOVAOVIEN_DT, -1, function(dataKySo) {
                    if (dataKySo.length > 0) {
                        arrTemp.push(dataKySo[0])
                    }
                });
            getFilesign769("PHIEU_NOITRU_BIENBANHOP_BENHNHAN", params.ID, -1, singletonObject.dvtt,
                thongtinhsba.thongtinbn.SOVAOVIEN, thongtinhsba.thongtinbn.SOVAOVIEN_DT, -1, function(dataKySo) {
                    if (dataKySo.length > 0) {
                        arrTemp.push(dataKySo[0])
                    }
                });
            var maxCreateDate = null;
            var maxCreateDataObject = null;
            if (arrTemp.length > 0) {
                $.each(arrTemp, function (index, dataObject) {
                    var createDate = moment(dataObject.CREATE_DATE_STRING, "DD/MM/YYYY HH:mm:ss");
                    if (maxCreateDate === null || createDate.isAfter(maxCreateDate)) {
                        maxCreateDate = createDate;
                        maxCreateDataObject = dataObject;
                    }
                });
                getCMUFileSigned769GetLink(maxCreateDataObject.KEYMINIO, 'pdf').then(pdfData => {
                    resolve({
                        url: pdfData,
                        kySo: 1,
                        isError: 0,
                        message: 'Thành công',
                        idKySo: maxCreateDataObject.ID
                    });
                }).catch(e => {
                    resolve({
                        url: '',
                        kySo: 0,
                        isError: 1,
                        message: 'Lỗi get file ký số'
                    });
                });
            } else {
                var text_date_part = params.NGAY_TAO_PHIEU.split("/");
                var text_date = text_date_part[0];
                var text_month = text_date_part[1];
                var text_year = text_date_part[2];
                var params2 = {
                    magiay: params.ID,
                    mabenhnhan: thongtinhsba.thongtinbn.MABENHNHAN,
                    tenbenhnhan: thongtinhsba.thongtinbn.TEN_BENH_NHAN,
                    sovaovien: thongtinhsba.thongtinbn.SOVAOVIEN,
                    namsinhbn: thongtinhsba.thongtinbn.NGAY_SINH.split("/")[2],
                    ngaytao: "Ngày " + text_date + " tháng " + text_month + " năm " + text_year
                }
                var url = 'cmu_in_cmu_bienbanhop?type=pdf&' + $.param(params2);
                resolve({
                    url: url,
                    kySo: 0,
                    isError: 0,
                    message: 'Thành công'
                });
            }
        } catch (e) {
            console.log("Lỗi get file ký số biên bản họp: " + e)
            reject({
                url: '',
                kySo: 0,
                isError: 1,
                message: 'Lỗi get file ký số biên bản họp: ' + e
            });
        }
    });
}

function getUrlBangKiemAnToan(params) {
    return new Promise(async (resolve, reject) => {
        try {
            var arrTemp = [];
            if (singletonObject.thamSo960610 == 1){
                getFilesign769("PHIEU_NOITRU_KIEMTRAANTOANPT_PTV", params.ID_LPT, -1, singletonObject.dvtt,
                    thongtinhsba.thongtinbn.SOVAOVIEN, thongtinhsba.thongtinbn.SOVAOVIEN_DT, -1, function(dataKySo) {
                        if (dataKySo.length > 0) {
                            arrTemp.push(dataKySo[0])
                        }
                    });
                getFilesign769("PHIEU_NOITRU_KIEMTRAANTOANPT_BSGM", params.ID_LPT, -1, singletonObject.dvtt,
                    thongtinhsba.thongtinbn.SOVAOVIEN, thongtinhsba.thongtinbn.SOVAOVIEN_DT, -1, function(dataKySo) {
                        if (dataKySo.length > 0) {
                            arrTemp.push(dataKySo[0])
                        }
                    });
                getFilesign769("PHIEU_NOITRU_KIEMTRAANTOANPT_KTV", params.ID_LPT, -1, singletonObject.dvtt,
                    thongtinhsba.thongtinbn.SOVAOVIEN, thongtinhsba.thongtinbn.SOVAOVIEN_DT, -1, function(dataKySo) {
                        if (dataKySo.length > 0) {
                            arrTemp.push(dataKySo[0])
                        }
                    });
                getFilesign769("PHIEU_NOITRU_KIEMTRAANTOANPT_DD", params.ID_LPT, -1, singletonObject.dvtt,
                    thongtinhsba.thongtinbn.SOVAOVIEN, thongtinhsba.thongtinbn.SOVAOVIEN_DT, -1, function(dataKySo) {
                        if (dataKySo.length > 0) {
                            arrTemp.push(dataKySo[0])
                        }
                    });
            } else {
                getFilesign769("PHIEU_NOITRU_KIEMTRAANTOANPT_TRUOCGAYME", params.ID_LPT, -1, singletonObject.dvtt,
                    thongtinhsba.thongtinbn.SOVAOVIEN, thongtinhsba.thongtinbn.SOVAOVIEN_DT, -1, function(dataKySo) {
                        if (dataKySo.length > 0) {
                            arrTemp.push(dataKySo[0])
                        }
                    });
                getFilesign769("PHIEU_NOITRU_KIEMTRAANTOANPT_TRUOCPHAUTHUAT", params.ID_LPT, -1, singletonObject.dvtt,
                    thongtinhsba.thongtinbn.SOVAOVIEN, thongtinhsba.thongtinbn.SOVAOVIEN_DT, -1, function(dataKySo) {
                        if (dataKySo.length > 0) {
                            arrTemp.push(dataKySo[0])
                        }
                    });
                getFilesign769("PHIEU_NOITRU_KIEMTRAANTOANPT_TRUOCKHICHUYEN", params.ID_LPT, -1, singletonObject.dvtt,
                    thongtinhsba.thongtinbn.SOVAOVIEN, thongtinhsba.thongtinbn.SOVAOVIEN_DT, -1, function(dataKySo) {
                        if (dataKySo.length > 0) {
                            arrTemp.push(dataKySo[0])
                        }
                    });
                getFilesign769("PHIEU_NOITRU_KIEMTRAANTOANPT_BACSIGAYME", params.ID_LPT, -1, singletonObject.dvtt,
                    thongtinhsba.thongtinbn.SOVAOVIEN, thongtinhsba.thongtinbn.SOVAOVIEN_DT, -1, function(dataKySo) {
                        if (dataKySo.length > 0) {
                            arrTemp.push(dataKySo[0])
                        }
                    });
                getFilesign769("PHIEU_NOITRU_KIEMTRAANTOANPT_BACSIPHAUTHUAT", params.ID_LPT, -1, singletonObject.dvtt,
                    thongtinhsba.thongtinbn.SOVAOVIEN, thongtinhsba.thongtinbn.SOVAOVIEN_DT, -1, function(dataKySo) {
                        if (dataKySo.length > 0) {
                            arrTemp.push(dataKySo[0])
                        }
                    });
            }
            var maxCreateDate = null;
            var maxCreateDataObject = null;
            if (arrTemp.length > 0) {
                $.each(arrTemp, function (index, dataObject) {
                    var createDate = moment(dataObject.CREATE_DATE_STRING, "DD/MM/YYYY HH:mm:ss");
                    if (maxCreateDate === null || createDate.isAfter(maxCreateDate)) {
                        maxCreateDate = createDate;
                        maxCreateDataObject = dataObject;
                    }
                });
                getCMUFileSigned769GetLink(maxCreateDataObject.KEYMINIO, 'pdf').then(pdfData => {
                    resolve({
                        url: pdfData,
                        kySo: 1,
                        isError: 0,
                        message: 'Thành công',
                        idKySo: maxCreateDataObject.ID
                    });
                }).catch(e => {
                    resolve({
                        url: '',
                        kySo: 0,
                        isError: 1,
                        message: 'Lỗi get file ký số'
                    });
                });
            } else {
                var url = '';
                if (singletonObject.thamSo960610 == 1){
                    url = 'cmu_in_phieu_kiem_tra_an_toan_phau_thuat_96161?type=pdf&' + $.param({
                        id_phieu_ktat_phau_thuat: params.ID
                    });
                } else {
                    url = 'cmu_in_phieu_kiem_tra_an_toan_phau_thuat?type=pdf&' + $.param({
                        id_phieu_ktat_phau_thuat: params.ID
                    });
                }
                resolve({
                    url: url,
                    kySo: 0,
                    isError: 0,
                    message: 'Thành công'
                });
            }
        } catch (e) {
            console.log("Lỗi get file ký số phiếu bảng kiểm an toàn phẫu thuật: " + e)
            reject({
                url: '',
                kySo: 0,
                isError: 1,
                message: 'Lỗi get file ký số phiếu bảng kiểm an toàn phẫu thuật: ' + e
            });
        }
    });
}

function getUrlKiemTruocPhauThuat(params) {
    return new Promise(async (resolve, reject) => {
        try {
            var arrTemp = [];
            getFilesign769("PHIEU_NOITRU_KIEMTRUOCPT_BACSI", params.ID_LPT, -1, singletonObject.dvtt,
                thongtinhsba.thongtinbn.SOVAOVIEN, thongtinhsba.thongtinbn.SOVAOVIEN_DT, -1, function(dataKySo) {
                    if (dataKySo.length > 0) {
                        arrTemp.push(dataKySo[0])
                    }
                });
            getFilesign769("PHIEU_NOITRU_KIEMTRUOCPT_DIEUDUONG", params.ID_LPT, -1, singletonObject.dvtt,
                thongtinhsba.thongtinbn.SOVAOVIEN, thongtinhsba.thongtinbn.SOVAOVIEN_DT, -1, function(dataKySo) {
                    if (dataKySo.length > 0) {
                        arrTemp.push(dataKySo[0])
                    }
                });
            var maxCreateDate = null;
            var maxCreateDataObject = null;
            if (arrTemp.length > 0) {
                $.each(arrTemp, function (index, dataObject) {
                    var createDate = moment(dataObject.CREATE_DATE_STRING, "DD/MM/YYYY HH:mm:ss");
                    if (maxCreateDate === null || createDate.isAfter(maxCreateDate)) {
                        maxCreateDate = createDate;
                        maxCreateDataObject = dataObject;
                    }
                });
                getCMUFileSigned769GetLink(maxCreateDataObject.KEYMINIO, 'pdf').then(pdfData => {
                    resolve({
                        url: pdfData,
                        kySo: 1,
                        isError: 0,
                        message: 'Thành công',
                        idKySo: maxCreateDataObject.ID
                    });
                }).catch(e => {
                    resolve({
                        url: '',
                        kySo: 0,
                        isError: 1,
                        message: 'Lỗi get file ký số'
                    });
                });
            } else {
                var url = 'cmu_in_cmu_bangkiemtruocphauthuat?type=pdf&magiay='+ params.ID
                    + '&mabenhnhan=' + thongtinhsba.thongtinbn.MABENHNHAN;
                resolve({
                    url: url,
                    kySo: 0,
                    isError: 0,
                    message: 'Thành công'
                });
            }
        } catch (e) {
            console.log("Lỗi get file ký số phiếu kiểm trước phẫu thuật: " + e)
            reject({
                url: '',
                kySo: 0,
                isError: 1,
                message: 'Lỗi get file ký số phiếu kiểm trước phẫu thuật: ' + e
            });
        }
    });
}

function getUrlTheoDoiChayThan(params) {
    return new Promise(async (resolve, reject) => {
        try {
            var arrTemp = [];
            getFilesign769("PHIEU_NOITRU_THEODOICHAYTHAN_BACSI", params.ID, -1, singletonObject.dvtt,
                thongtinhsba.thongtinbn.SOVAOVIEN, thongtinhsba.thongtinbn.SOVAOVIEN_DT, -1, function(dataKySo) {
                    if (dataKySo.length > 0) {
                        arrTemp.push(dataKySo[0])
                    }
                });
            getFilesign769("PHIEU_NOITRU_THEODOICHAYTHAN_DIEUDUONG", params.ID, -1, singletonObject.dvtt,
                thongtinhsba.thongtinbn.SOVAOVIEN, thongtinhsba.thongtinbn.SOVAOVIEN_DT, -1, function(dataKySo) {
                    if (dataKySo.length > 0) {
                        arrTemp.push(dataKySo[0])
                    }
                });
            var maxCreateDate = null;
            var maxCreateDataObject = null;
            if (arrTemp.length > 0) {
                $.each(arrTemp, function (index, dataObject) {
                    var createDate = moment(dataObject.CREATE_DATE_STRING, "DD/MM/YYYY HH:mm:ss");
                    if (maxCreateDate === null || createDate.isAfter(maxCreateDate)) {
                        maxCreateDate = createDate;
                        maxCreateDataObject = dataObject;
                    }
                });
                getCMUFileSigned769GetLink(maxCreateDataObject.KEYMINIO, 'pdf').then(pdfData => {
                    resolve({
                        url: pdfData,
                        kySo: 1,
                        isError: 0,
                        message: 'Thành công',
                        idKySo: maxCreateDataObject.ID
                    });
                }).catch(e => {
                    resolve({
                        url: '',
                        kySo: 0,
                        isError: 1,
                        message: 'Lỗi get file ký số'
                    });
                });
            } else {
                var params2 = {
                    maphieu: params.ID,
                    mabenhnhan: thongtinhsba.thongtinbn.MABENHNHAN,
                    sobenhan: thongtinhsba.thongtinbn.SOBENHAN,
                }
                var url = 'cmu_in_phieu_theo_doi_chay_than?type=pdf&' + $.param(params2);
                resolve({
                    url: url,
                    kySo: 0,
                    isError: 0,
                    message: 'Thành công'
                });
            }
        } catch (e) {
            console.log("Lỗi get file ký số phiếu theo dõi chạy thận: " + e)
            reject({
                url: '',
                kySo: 0,
                isError: 1,
                message: 'Lỗi get file ký số phiếu theo dõi chạy thận: ' + e
            });
        }
    });
}

function getUrlGiayBaoTu(params) {
    return new Promise(async (resolve, reject) => {
        try {
            if(singletonObject.thamSo960601 == 1) {
                $.ajax({
                    url: "cmu_getlist?url="+convertArray([
                        singletonObject.dvtt,
                        params.SOVAOVIEN,
                        params.SOVAOVIEN_DT,
                        params.SOVAOVIEN,
                        'cmu_smart769_gettv']),
                    method: "GET",
                }).done(function (data) {
                    if(data.length > 0) {
                        var url = "smartca-get-signed-file-minio?keyminio=" + data[0].KEYMINIO + "&type=pdf";
                        $.ajax({
                            method: "POST", url: url, contentType: "charset=utf-8"
                        }).done(function (data) {
                            resolve({
                                url: 'data:application/pdf;base64,' + data.FILE,
                                kySo: 1,
                                isError: 0,
                                message: 'Thành công'
                            });
                        }).fail(function() {
                            reject({
                                url: '',
                                kySo: 0,
                                isError: 1,
                                message: 'Lỗi hệ thống: ' + e
                            });
                        });
                    } else {
                        $.ajax({
                            url: "lay_id_nhankhau?mabenhnhan=" + params.MABENHNHAN
                        }).done(function (dt) {
                            var arr = [parseInt(dt), params.MABENHNHAN, 1, "0"];
                            var url = "ingiaybaotu_nk?url=" + convertArray(arr);
                            resolve({
                                url: url,
                                kySo: 0,
                                isError: 0,
                                message: 'Thành công'
                            });
                        }).fail(function() {
                            resolve({
                                url: '',
                                kySo: 0,
                                isError: 1,
                                message: 'Lỗi dữ liệu'
                            });
                        })

                    }
                }).fail(function() {
                    resolve({
                        url: '',
                        kySo: 0,
                        isError: 1,
                        message: 'Lỗi dữ liệu'
                    });
                });
                return false;
            }
            var formData = new FormData();
            formData.append("sovaovien_dt", 0);
            formData.append("sovaovien", thongtinhsba.thongtinbn.SOVAOVIEN);
            formData.append("dvtt", singletonObject.dvtt);
            $.ajax({
                url: "https://apikysohis.vnptcamau.vn/chi-tiet-giay-chung-tu-base64-noauth",
                type: "POST",
                data: formData,
                processData: false,
                contentType: false,
                success: function (response) {
                    resolve({
                        url: 'data:application/pdf;base64,' + response,
                        kySo: 1,
                        isError: 0,
                        message: 'Thành công'
                    });
                },
                error: function (xhr, status, error) {
                    var arr = [thongtinhsba.thongtinbn.IDNHANKHAU, thongtinhsba.thongtinbn.MABENHNHAN, 1, "0"];
                    var url = "ingiaybaotu_nk?url=" + convertArray(arr);
                    resolve({
                        url: url,
                        kySo: 0,
                        isError: 0,
                        message: 'Thành công'
                    });
                }
            });
        } catch (e) {
            console.log("Lỗi hệ thống: " + e)
            reject({
                url: '',
                kySo: 0,
                isError: 1,
                message: 'Lỗi hệ thống: ' + e
            });
        }
    });
}

function getUrlTruyenMau(params) {
    return new Promise(async (resolve, reject) => {
        try {
            var arrTemp = [];
            getFilesign769("PHIEU_NOITRU_PHIEUTRUYENMAU_KHOALAMSANG_DIEUDUONG", params.ID, -1, singletonObject.dvtt,
                thongtinhsba.thongtinbn.SOVAOVIEN, thongtinhsba.thongtinbn.SOVAOVIEN_DT, -1, function(dataKySo) {
                    if (dataKySo.length > 0) {
                        arrTemp.push(dataKySo[0])
                    }
                });
            getFilesign769("PHIEU_NOITRU_PHIEUTRUYENMAU_KHOALAMSANG_BACSI", params.ID, -1, singletonObject.dvtt,
                thongtinhsba.thongtinbn.SOVAOVIEN, thongtinhsba.thongtinbn.SOVAOVIEN_DT, -1, function(dataKySo) {
                    if (dataKySo.length > 0) {
                        arrTemp.push(dataKySo[0])
                    }
                });
            var maxCreateDate = null;
            var maxCreateDataObject = null;
            if (arrTemp.length > 0) {
                $.each(arrTemp, function (index, dataObject) {
                    var createDate = moment(dataObject.CREATE_DATE_STRING, "DD/MM/YYYY HH:mm:ss");
                    if (maxCreateDate === null || createDate.isAfter(maxCreateDate)) {
                        maxCreateDate = createDate;
                        maxCreateDataObject = dataObject;
                    }
                });
                getCMUFileSigned769GetLink(maxCreateDataObject.KEYMINIO, 'pdf').then(pdfData => {
                    resolve({
                        url: pdfData,
                        kySo: 1,
                        isError: 0,
                        message: 'Thành công',
                        idKySo: maxCreateDataObject.ID
                    });
                }).catch(e => {
                    resolve({
                        url: '',
                        kySo: 0,
                        isError: 1,
                        message: 'Lỗi get file ký số'
                    });
                });
            } else {
                var arr = [singletonObject.dvtt, thongtinhsba.thongtinbn.SOVAOVIEN, thongtinhsba.thongtinbn.SOVAOVIEN_DT, params.ID, thongtinhsba.thongtinbn.TEN_BENH_NHAN, thongtinhsba.thongtinbn.GIOI_TINH == 1? "Nam": "Nữ", " "];
                var param2 = ['dvtt', 'sovaovien', 'sovaovien_dt', 'id', 'hoten', 'gioitinh', "giuong"];
                var url = "cmu_injasper?url=" + convertArray(arr) + "&param=" + convertArray(param2) + "&loaifile=pdf&jasper=rp_phieutruyenmau_khoalamsang";
                resolve({
                    url: url,
                    kySo: 0,
                    isError: 0,
                    message: 'Thành công'
                });
            }
        } catch (e) {
            console.log("Lỗi get file ký số phiếu truyền máu: " + e)
            reject({
                url: '',
                kySo: 0,
                isError: 1,
                message: 'Lỗi get file ký số phiếu truyền máu: ' + e
            });
        }
    });
}

function getUrlDanhGiaRoiLoanNuot(params) {
    return new Promise(async (resolve, reject) => {
        try {
            var arrTemp = [];
            getFilesign769("PHIEU_NOITRU_DANHGIAROILOANNUOT_BACSI", params.ID, -1, singletonObject.dvtt,
                thongtinhsba.thongtinbn.SOVAOVIEN, thongtinhsba.thongtinbn.SOVAOVIEN_DT, -1, function(dataKySo) {
                    if (dataKySo.length > 0) {
                        arrTemp.push(dataKySo[0])
                    }
                });
            getFilesign769("PHIEU_NOITRU_DANHGIAROILOANNUOT_DIEUDUONG", params.ID, -1, singletonObject.dvtt,
                thongtinhsba.thongtinbn.SOVAOVIEN, thongtinhsba.thongtinbn.SOVAOVIEN_DT, -1, function(dataKySo) {
                    if (dataKySo.length > 0) {
                        arrTemp.push(dataKySo[0])
                    }
                });
            var maxCreateDate = null;
            var maxCreateDataObject = null;
            if (arrTemp.length > 0) {
                $.each(arrTemp, function (index, dataObject) {
                    var createDate = moment(dataObject.CREATE_DATE_STRING, "DD/MM/YYYY HH:mm:ss");
                    if (maxCreateDate === null || createDate.isAfter(maxCreateDate)) {
                        maxCreateDate = createDate;
                        maxCreateDataObject = dataObject;
                    }
                });
                getCMUFileSigned769GetLink(maxCreateDataObject.KEYMINIO, 'pdf').then(pdfData => {
                    resolve({
                        url: pdfData,
                        kySo: 1,
                        isError: 0,
                        message: 'Thành công',
                        idKySo: maxCreateDataObject.ID
                    });
                }).catch(e => {
                    resolve({
                        url: '',
                        kySo: 0,
                        isError: 1,
                        message: 'Lỗi get file ký số'
                    });
                });
            } else {
                var text_date_part = params.NGAY_TAO_PHIEU.split("/");
                var text_date = text_date_part[0];
                var text_month = text_date_part[1];
                var text_year = text_date_part[2];
                var params2 = {
                    magiay: params.ID,
                    mabenhnhan: thongtinhsba.thongtinbn.MABENHNHAN,
                    text_date,
                    text_month,
                    text_year
                }
                var url = 'cmu_in_cmu_danhgiaroiloannuot?type=pdf&' + $.param(params2);
                resolve({
                    url: url,
                    kySo: 0,
                    isError: 0,
                    message: 'Thành công'
                });
            }
        } catch (e) {
            console.log("Lỗi get file ký số phiếu đánh giá rối loạn nuốt: " + e)
            reject({
                url: '',
                kySo: 0,
                isError: 1,
                message: 'Lỗi get file ký số phiếu đánh giá rối loạn nuốt: ' + e
            });
        }
    });
}

function getUrlThongTu50(params) {
    return new Promise(async (resolve, reject) => {
        try {
            var arrTemp = [];
            getFilesign769("PHIEU_NOITRU_TT50", params.NGAYLAP_TEXT, -1, singletonObject.dvtt,
                params.SOVAOVIEN, params.SOVAOVIEN_DT, -1, function(dataKySo) {
                    if (dataKySo.length > 0) {
                        arrTemp.push(dataKySo[0])
                    }
                });
            getFilesign769("PHIEU_NOITRU_TT50_BENHNHAN", params.NGAYLAP_TEXT, -1, singletonObject.dvtt,
                params.SOVAOVIEN, params.SOVAOVIEN_DT, -1, function(dataKySo) {
                    if (dataKySo.length > 0) {
                        arrTemp.push(dataKySo[0])
                    }
                });
            var maxCreateDate = null;
            var maxCreateDataObject = null;
            if (arrTemp.length > 0) {
                $.each(arrTemp, function (index, dataObject) {
                    var createDate = moment(dataObject.CREATE_DATE_STRING, "DD/MM/YYYY HH:mm:ss");
                    if (maxCreateDate === null || createDate.isAfter(maxCreateDate)) {
                        maxCreateDate = createDate;
                        maxCreateDataObject = dataObject;
                    }
                });
                getCMUFileSigned769GetLink(maxCreateDataObject.KEYMINIO, 'pdf').then(pdfData => {
                    resolve({
                        url: pdfData,
                        kySo: 1,
                        isError: 0,
                        message: 'Thành công',
                        idKySo: maxCreateDataObject.ID
                    });
                }).catch(e => {
                    console.log("Lỗi get file ký số phiếu TT50: " + e)
                    reject({
                        url: '',
                        kySo: 0,
                        isError: 1,
                        message: "Lỗi get file ký số phiếu TT50: " + e
                    });
                });
            } else {
                var params2 = [
                    params.NGAYLAP_TEXT,
                    params.MABENHNHAN,
                    params.SOVAOVIEN,
                    params.SOVAOVIEN_DT,
                    params.STT_DOTDIEUTRI,
                    singletonObject.makhoa,
                    singletonObject.bant?singletonObject.bant : 0,
                ]
                var param3 = ['ngay', 'mabenhnhan', 'sovaovien', 'sovaovien_dt', 'stt_ddt', 'phongban', 'bant'];
                var url = "cmu_injasper?url=" + convertArray(params2)+"&param="+ convertArray(param3)+"&loaifile=pdf&jasper=CMU_TT50";
                resolve({
                    url: url,
                    kySo: 0,
                    isError: 0,
                    message: 'Thành công'
                });
            }
        } catch (e) {
            console.log("Lỗi get file ký số phiếu TT50: " + e)
            reject({
                url: '',
                kySo: 0,
                isError: 1,
                message: 'Lỗi get file ký số phiếu TT50: ' + e
            });
        }
    });
}

function getUrlTomTatDieuTri(params) {
    return new Promise(async (resolve, reject) => {
        try {
            var arrTemp = [];
            getFilesign769("PHIEU_NOITRU_TOMTATDIEUTRI_BENHNHAN", params.ID, -1, singletonObject.dvtt,
                thongtinhsba.thongtinbn.SOVAOVIEN, thongtinhsba.thongtinbn.SOVAOVIEN_DT, -1, function(dataKySo) {
                    if (dataKySo.length > 0) {
                        arrTemp.push(dataKySo[0])
                    }
                });
            getFilesign769("PHIEU_NOITRU_TOMTATDIEUTRI", params.ID, -1, singletonObject.dvtt,
                thongtinhsba.thongtinbn.SOVAOVIEN, thongtinhsba.thongtinbn.SOVAOVIEN_DT, -1, function(dataKySo) {
                    if (dataKySo.length > 0) {
                        arrTemp.push(dataKySo[0])
                    }
                });
            var maxCreateDate = null;
            var maxCreateDataObject = null;
            if (arrTemp.length > 0) {
                $.each(arrTemp, function (index, dataObject) {
                    var createDate = moment(dataObject.CREATE_DATE_STRING, "DD/MM/YYYY HH:mm:ss");
                    if (maxCreateDate === null || createDate.isAfter(maxCreateDate)) {
                        maxCreateDate = createDate;
                        maxCreateDataObject = dataObject;
                    }
                });
                getCMUFileSigned769GetLink(maxCreateDataObject.KEYMINIO, 'pdf').then(pdfData => {
                    resolve({
                        url: pdfData,
                        kySo: 1,
                        isError: 0,
                        message: 'Thành công',
                        idKySo: maxCreateDataObject.ID
                    });
                }).catch(e => {
                    resolve({
                        url: '',
                        kySo: 0,
                        isError: 1,
                        message: 'Lỗi get file ký số'
                    });
                });
            } else {
                var params2 = [singletonObject.dvtt, thongtinhsba.thongtinbn.MABENHNHAN, thongtinhsba.thongtinbn.STT_BENHAN, params.ID,
                    thongtinhsba.thongtinbn.TEN_BENH_NHAN, thongtinhsba.thongtinbn.NGAY_SINH.split("/")[2],
                    thongtinhsba.thongtinbn.GIOI_TINH_HT, thongtinhsba.thongtinbn.DIA_CHI, thongtinhsba.thongtinbn.TYLEBAOHIEM
                ]
                var param3 = ['dvtt', 'mabenhnhan', 'stt_benhan', 'id_tomtat', 'hoten', 'namsinh', 'gioitinh', 'diachi_bn', 'muchuong'];
                var url = "cmu_injasper?url=" + convertArray(params2)+"&param="+ convertArray(param3)+"&loaifile=pdf&jasper=rp_tomtatdieutri";
                resolve({
                    url: url,
                    kySo: 0,
                    isError: 0,
                    message: 'Thành công'
                });
            }
        } catch (e) {
            console.log("Lỗi get file ký số phiếu tóm tắt điều trị: " + e)
            reject({
                url: '',
                kySo: 0,
                isError: 1,
                message: 'Lỗi get file ký số phiếu tóm tắt điều trị: ' + e
            });
        }
    });
}

function getUrlPhanLoaiNB(params) {
    return new Promise(async (resolve, reject) => {
        try {
            var arrTemp = [];
            getFilesign769("PHIEU_NOITRU_PHANLOAINB_NGUOITH1", params.ID, -1, singletonObject.dvtt,
                thongtinhsba.thongtinbn.SOVAOVIEN, thongtinhsba.thongtinbn.SOVAOVIEN_DT, -1, function(dataKySo) {
                    if (dataKySo.length > 0) {
                        arrTemp.push(dataKySo[0])
                    }
                });
            getFilesign769("PHIEU_NOITRU_PHANLOAINB_NGUOITH2", params.ID, -1, singletonObject.dvtt,
                thongtinhsba.thongtinbn.SOVAOVIEN, thongtinhsba.thongtinbn.SOVAOVIEN_DT, -1, function(dataKySo) {
                    if (dataKySo.length > 0) {
                        arrTemp.push(dataKySo[0])
                    }
                });
            var maxCreateDate = null;
            var maxCreateDataObject = null;
            if (arrTemp.length > 0) {
                $.each(arrTemp, function (index, dataObject) {
                    var createDate = moment(dataObject.CREATE_DATE_STRING, "DD/MM/YYYY HH:mm:ss");
                    if (maxCreateDate === null || createDate.isAfter(maxCreateDate)) {
                        maxCreateDate = createDate;
                        maxCreateDataObject = dataObject;
                    }
                });
                getCMUFileSigned769GetLink(maxCreateDataObject.KEYMINIO, 'pdf').then(pdfData => {
                    resolve({
                        url: pdfData,
                        kySo: 1,
                        isError: 0,
                        message: 'Thành công',
                        idKySo: maxCreateDataObject.ID
                    });
                }).catch(e => {
                    resolve({
                        url: '',
                        kySo: 0,
                        isError: 1,
                        message: 'Lỗi get file ký số'
                    });
                });
            } else {
                var params2 = {
                    magiay: params.ID,
                    mabenhnhan: thongtinhsba.thongtinbn.MABENHNHAN,
                    tenbenhnhan: thongtinhsba.thongtinbn.TEN_BENH_NHAN,
                    sovaovien: thongtinhsba.thongtinbn.SOVAOVIEN,
                    sobenhan: thongtinhsba.thongtinbn.SOBENHAN,
                    tuoi: thongtinhsba.thongtinbn.TUOI,
                    GIOI_TINH: thongtinhsba.thongtinbn.GIOI_TINH,
                }
                var url = 'cmu_in_cmu_phieuphanloainguoibenh?type=pdf&' + $.param(params2);
                resolve({
                    url: url,
                    kySo: 0,
                    isError: 0,
                    message: 'Thành công'
                });
            }
        } catch (e) {
            console.log("Lỗi get file ký số phiếu nhận định phân loại người bệnh: " + e)
            reject({
                url: '',
                kySo: 0,
                isError: 1,
                message: 'Lỗi get file ký số phiếu nhận định phân loại người bệnh: ' + e
            });
        }
    });
}

function getUrlBangKiemGac(params) {
    return new Promise(async (resolve, reject) => {
        try {
            var arrTemp = [];
            getFilesign769("PHIEU_NOITRU_BANGKIEMGAC_1", params.ID_LPT, -1, singletonObject.dvtt,
                thongtinhsba.thongtinbn.SOVAOVIEN, thongtinhsba.thongtinbn.SOVAOVIEN_DT, -1, function(dataKySo) {
                    if (dataKySo.length > 0) {
                        arrTemp.push(dataKySo[0])
                    }
                });
            getFilesign769("PHIEU_NOITRU_BANGKIEMGAC_2", params.ID_LPT, -1, singletonObject.dvtt,
                thongtinhsba.thongtinbn.SOVAOVIEN, thongtinhsba.thongtinbn.SOVAOVIEN_DT, -1, function(dataKySo) {
                    if (dataKySo.length > 0) {
                        arrTemp.push(dataKySo[0])
                    }
                });
            var maxCreateDate = null;
            var maxCreateDataObject = null;
            if (arrTemp.length > 0) {
                $.each(arrTemp, function (index, dataObject) {
                    var createDate = moment(dataObject.CREATE_DATE_STRING, "DD/MM/YYYY HH:mm:ss");
                    if (maxCreateDate === null || createDate.isAfter(maxCreateDate)) {
                        maxCreateDate = createDate;
                        maxCreateDataObject = dataObject;
                    }
                });
                getCMUFileSigned769GetLink(maxCreateDataObject.KEYMINIO, 'pdf').then(pdfData => {
                    resolve({
                        url: pdfData,
                        kySo: 1,
                        isError: 0,
                        message: 'Thành công',
                        idKySo: maxCreateDataObject.ID
                    });
                }).catch(e => {
                    resolve({
                        url: '',
                        kySo: 0,
                        isError: 1,
                        message: 'Lỗi get file ký số'
                    });
                });
            } else {
                var url = 'cmu_in_cmu_bangkiemgac_sn?type=pdf&' + $.param({
                    id_lanphauthuat: params.ID_LPT,
                    mabenhnhan: thongtinhsba.thongtinbn.MA_BENH_NHAN,
                    sobenhan: thongtinhsba.thongtinbn.SOBENHAN,
                    tuoi: thongtinhsba.thongtinbn.TUOI == 0? thongtinhsba.thongtinbn.TUOI_HT: thongtinhsba.thongtinbn.TUOI,
                    gioitinh: thongtinhsba.thongtinbn.GIOI_TINH_HT,
                    hoten: thongtinhsba.thongtinbn.TEN_BENH_NHAN
                });
                resolve({
                    url: url,
                    kySo: 0,
                    isError: 0,
                    message: 'Thành công'
                });
            }
        } catch (e) {
            console.log("Lỗi get file ký số bảng kiểm gạc: " + e)
            reject({
                url: '',
                kySo: 0,
                isError: 1,
                message: 'Lỗi get file ký số bảng kiểm gạc: ' + e
            });
        }
    });
}

function getUrlDeXuatPTTT(params) {
    return new Promise(async (resolve, reject) => {
        try {
            var arrTemp = [];
            getFilesign769("PHIEU_NOITRU_DEXUATPTTT_GD", params.ID, -1, singletonObject.dvtt,
                thongtinhsba.thongtinbn.SOVAOVIEN, thongtinhsba.thongtinbn.SOVAOVIEN_DT, -1, function(dataKySo) {
                    if (dataKySo.length > 0) {
                        arrTemp.push(dataKySo[0])
                    }
                });
            getFilesign769("PHIEU_NOITRU_DEXUATPTTT_TK", params.ID, -1, singletonObject.dvtt,
                thongtinhsba.thongtinbn.SOVAOVIEN, thongtinhsba.thongtinbn.SOVAOVIEN_DT, -1, function(dataKySo) {
                    if (dataKySo.length > 0) {
                        arrTemp.push(dataKySo[0])
                    }
                });
            var maxCreateDate = null;
            var maxCreateDataObject = null;
            if (arrTemp.length > 0) {
                $.each(arrTemp, function (index, dataObject) {
                    var createDate = moment(dataObject.CREATE_DATE_STRING, "DD/MM/YYYY HH:mm:ss");
                    if (maxCreateDate === null || createDate.isAfter(maxCreateDate)) {
                        maxCreateDate = createDate;
                        maxCreateDataObject = dataObject;
                    }
                });
                getCMUFileSigned769GetLink(maxCreateDataObject.KEYMINIO, 'pdf').then(pdfData => {
                    resolve({
                        url: pdfData,
                        kySo: 1,
                        isError: 0,
                        message: 'Thành công',
                        idKySo: maxCreateDataObject.ID
                    });
                }).catch(e => {
                    resolve({
                        url: '',
                        kySo: 0,
                        isError: 1,
                        message: 'Lỗi get file ký số'
                    });
                });
            } else {
                var params2 = {
                    magiay: params.ID,
                    mabenhnhan: thongtinhsba.thongtinbn.MABENHNHAN,
                    khoa: thongtinhsba.thongtinbn.TEN_PHONGBAN,
                    diachi: thongtinhsba.thongtinbn.DIA_CHI,
                    sdt: thongtinhsba.thongtinbn.SO_DIEN_THOAI,
                    tenbenhnhan: thongtinhsba.thongtinbn.TEN_BENH_NHAN,
                    sobenhan: thongtinhsba.thongtinbn.SOBENHAN,
                    sovaovien: thongtinhsba.thongtinbn.SOVAOVIEN,
                    chandoan: thongtinhsba.thongtinbn.ICD_HT,
                    gioitinh: thongtinhsba.thongtinbn.GIOI_TINH,
                    namsinh: thongtinhsba.thongtinbn.NGAY_SINH.split("/")[2],
                    tuoi: thongtinhsba.thongtinbn.TUOI,
                    stt_benhan: thongtinhsba.thongtinbn.STT_BENHAN,
                    stt_dotdieutri: thongtinhsba.thongtinbn.STT_DOTDIEUTRI,
                }
                var url = 'cmu_in_cmu_phieudexuatpttt?type=pdf&' + $.param(params2);
                resolve({
                    url: url,
                    kySo: 0,
                    isError: 0,
                    message: 'Thành công'
                });
            }
        } catch (e) {
            console.log("Lỗi get file ký số phiếu đề xuất phẫu thuật, thủ thuật theo yêu cầu: " + e)
            reject({
                url: '',
                kySo: 0,
                isError: 1,
                message: 'Lỗi get file ký số phiếu đề xuất phẫu thuật, thủ thuật theo yêu cầu: ' + e
            });
        }
    });
}

function getUrlTuVanHDNhapVien(params) {
    return new Promise(async (resolve, reject) => {
        try {
            var arrTemp = [];
            getFilesign769("PHIEU_NOITRU_TVHDNHAPVIEN_BENHNHAN", params.ID, -1, singletonObject.dvtt,
                thongtinhsba.thongtinbn.SOVAOVIEN, thongtinhsba.thongtinbn.SOVAOVIEN_DT, -1, function(dataKySo) {
                    if (dataKySo.length > 0) {
                        arrTemp.push(dataKySo[0])
                    }
                });
            getFilesign769("PHIEU_NOITRU_TVHDNHAPVIEN", params.ID, -1, singletonObject.dvtt,
                thongtinhsba.thongtinbn.SOVAOVIEN, thongtinhsba.thongtinbn.SOVAOVIEN_DT, -1, function(dataKySo) {
                    if (dataKySo.length > 0) {
                        arrTemp.push(dataKySo[0])
                    }
                });
            var maxCreateDate = null;
            var maxCreateDataObject = null;
            if (arrTemp.length > 0) {
                $.each(arrTemp, function (index, dataObject) {
                    var createDate = moment(dataObject.CREATE_DATE_STRING, "DD/MM/YYYY HH:mm:ss");
                    if (maxCreateDate === null || createDate.isAfter(maxCreateDate)) {
                        maxCreateDate = createDate;
                        maxCreateDataObject = dataObject;
                    }
                });
                getCMUFileSigned769GetLink(maxCreateDataObject.KEYMINIO, 'pdf').then(pdfData => {
                    resolve({
                        url: pdfData,
                        kySo: 1,
                        isError: 0,
                        message: 'Thành công',
                        idKySo: maxCreateDataObject.ID
                    });
                }).catch(e => {
                    resolve({
                        url: '',
                        kySo: 0,
                        isError: 1,
                        message: 'Lỗi get file ký số'
                    });
                });
            } else {
                var params2 = {
                    magiay: params.ID,
                    mabenhnhan: thongtinhsba.thongtinbn.MABENHNHAN,
                    tennguoibenh: thongtinhsba.thongtinbn.TEN_BENH_NHAN,
                    sobenhan: thongtinhsba.thongtinbn.SOBENHAN,
                    sovaovien: thongtinhsba.thongtinbn.SOVAOVIEN,
                    gioitinh: thongtinhsba.thongtinbn.GIOI_TINH,
                    tuoi: thongtinhsba.thongtinbn.TUOI,
                    khoa: thongtinhsba.thongtinbn.TEN_PHONGBAN,
                    stt_benhan: thongtinhsba.thongtinbn.STT_BENHAN,
                    stt_dotdieutri: thongtinhsba.thongtinbn.STT_DOTDIEUTRI,
                }
                var url = 'cmu_in_cmu_tv_hd_nhapvien?type=pdf&' + $.param(params2);
                resolve({
                    url: url,
                    kySo: 0,
                    isError: 0,
                    message: 'Thành công'
                });
            }
        } catch (e) {
            console.log("Lỗi get file ký số phiếu tư vấn, hướng dẫn khi nhập viện: " + e)
            reject({
                url: '',
                kySo: 0,
                isError: 1,
                message: 'Lỗi get file ký số phiếu tư vấn, hướng dẫn khi nhập viện: ' + e
            });
        }
    });
}

function getUrlTuVanHDVaoVien(params) {
    return new Promise(async (resolve, reject) => {
        try {
            var arrTemp = [];
            getFilesign769("PHIEU_NOITRU_TVHDVAOVIEN_BENHNHAN", params.ID, -1, singletonObject.dvtt,
                thongtinhsba.thongtinbn.SOVAOVIEN, thongtinhsba.thongtinbn.SOVAOVIEN_DT, -1, function(dataKySo) {
                    if (dataKySo.length > 0) {
                        arrTemp.push(dataKySo[0])
                    }
                });
            getFilesign769("PHIEU_NOITRU_TVHDVAOVIEN", params.ID, -1, singletonObject.dvtt,
                thongtinhsba.thongtinbn.SOVAOVIEN, thongtinhsba.thongtinbn.SOVAOVIEN_DT, -1, function(dataKySo) {
                    if (dataKySo.length > 0) {
                        arrTemp.push(dataKySo[0])
                    }
                });
            var maxCreateDate = null;
            var maxCreateDataObject = null;
            if (arrTemp.length > 0) {
                $.each(arrTemp, function (index, dataObject) {
                    var createDate = moment(dataObject.CREATE_DATE_STRING, "DD/MM/YYYY HH:mm:ss");
                    if (maxCreateDate === null || createDate.isAfter(maxCreateDate)) {
                        maxCreateDate = createDate;
                        maxCreateDataObject = dataObject;
                    }
                });
                getCMUFileSigned769GetLink(maxCreateDataObject.KEYMINIO, 'pdf').then(pdfData => {
                    resolve({
                        url: pdfData,
                        kySo: 1,
                        isError: 0,
                        message: 'Thành công',
                        idKySo: maxCreateDataObject.ID
                    });
                }).catch(e => {
                    resolve({
                        url: '',
                        kySo: 0,
                        isError: 1,
                        message: 'Lỗi get file ký số'
                    });
                });
            } else {
                var params2 = {
                    magiay: params.ID,
                    mabenhnhan: thongtinhsba.thongtinbn.MABENHNHAN,
                    tennguoibenh: thongtinhsba.thongtinbn.TEN_BENH_NHAN,
                    sobenhan: thongtinhsba.thongtinbn.SOBENHAN,
                    sovaovien: thongtinhsba.thongtinbn.SOVAOVIEN,
                    gioitinh: thongtinhsba.thongtinbn.GIOI_TINH,
                    tuoi: thongtinhsba.thongtinbn.TUOI,
                    khoa: thongtinhsba.thongtinbn.TEN_PHONGBAN,
                    stt_benhan: thongtinhsba.thongtinbn.STT_BENHAN,
                    stt_dotdieutri: thongtinhsba.thongtinbn.STT_DOTDIEUTRI,
                }
                var url = 'cmu_in_cmu_tv_hd_vaovien?type=pdf&' + $.param(params2);
                resolve({
                    url: url,
                    kySo: 0,
                    isError: 0,
                    message: 'Thành công'
                });
            }
        } catch (e) {
            console.log("Lỗi get file ký số phiếu tư vấn, hướng dẫn khi vào viện: " + e)
            reject({
                url: '',
                kySo: 0,
                isError: 1,
                message: 'Lỗi get file ký số phiếu tư vấn, hướng dẫn khi vào viện: ' + e
            });
        }
    });
}

function getUrlTuVanHDRaVien(params) {
    return new Promise(async (resolve, reject) => {
        try {
            var arrTemp = [];
            getFilesign769("PHIEU_NOITRU_TVHDRAVIEN_BENHNHAN", params.ID, -1, singletonObject.dvtt,
                thongtinhsba.thongtinbn.SOVAOVIEN, thongtinhsba.thongtinbn.SOVAOVIEN_DT, -1, function(dataKySo) {
                    if (dataKySo.length > 0) {
                        arrTemp.push(dataKySo[0])
                    }
                });
            getFilesign769("PHIEU_NOITRU_TVHDRAVIEN", params.ID, -1, singletonObject.dvtt,
                thongtinhsba.thongtinbn.SOVAOVIEN, thongtinhsba.thongtinbn.SOVAOVIEN_DT, -1, function(dataKySo) {
                    if (dataKySo.length > 0) {
                        arrTemp.push(dataKySo[0])
                    }
                });
            var maxCreateDate = null;
            var maxCreateDataObject = null;
            if (arrTemp.length > 0) {
                $.each(arrTemp, function (index, dataObject) {
                    var createDate = moment(dataObject.CREATE_DATE_STRING, "DD/MM/YYYY HH:mm:ss");
                    if (maxCreateDate === null || createDate.isAfter(maxCreateDate)) {
                        maxCreateDate = createDate;
                        maxCreateDataObject = dataObject;
                    }
                });
                getCMUFileSigned769GetLink(maxCreateDataObject.KEYMINIO, 'pdf').then(pdfData => {
                    resolve({
                        url: pdfData,
                        kySo: 1,
                        isError: 0,
                        message: 'Thành công',
                        idKySo: maxCreateDataObject.ID
                    });
                }).catch(e => {
                    resolve({
                        url: '',
                        kySo: 0,
                        isError: 1,
                        message: 'Lỗi get file ký số'
                    });
                });
            } else {
                var params2 = {
                    magiay: params.ID,
                    mabenhnhan: thongtinhsba.thongtinbn.MABENHNHAN,
                    tennguoibenh: thongtinhsba.thongtinbn.TEN_BENH_NHAN,
                    sobenhan: thongtinhsba.thongtinbn.SOBENHAN,
                    sovaovien: thongtinhsba.thongtinbn.SOVAOVIEN,
                    gioitinh: thongtinhsba.thongtinbn.GIOI_TINH,
                    tuoi: thongtinhsba.thongtinbn.TUOI,
                    khoa: thongtinhsba.thongtinbn.TEN_PHONGBAN,
                    stt_benhan: thongtinhsba.thongtinbn.STT_BENHAN,
                    stt_dotdieutri: thongtinhsba.thongtinbn.STT_DOTDIEUTRI,
                }
                var url = 'cmu_in_cmu_tv_hd_ravien?type=pdf&' + $.param(params2);
                resolve({
                    url: url,
                    kySo: 0,
                    isError: 0,
                    message: 'Thành công'
                });
            }
        } catch (e) {
            console.log("Lỗi get file ký số phiếu tư vấn, hướng dẫn trước khi ra viện: " + e)
            reject({
                url: '',
                kySo: 0,
                isError: 1,
                message: 'Lỗi get file ký số phiếu tư vấn, hướng dẫn trước khi ra viện: ' + e
            });
        }
    });
}

function getUrlGiayHen() {
    return new Promise(async (resolve, reject) => {
        try {
            var arrTemp = [];
            getFilesign769("PHIEU_NOITRU_GIAYHEN_BACSI", thongtinhsba.thongtinbn.SOVAOVIEN, -1, singletonObject.dvtt,
                thongtinhsba.thongtinbn.SOVAOVIEN, thongtinhsba.thongtinbn.SOVAOVIEN_DT, -1, function(dataKySo) {
                    if (dataKySo.length > 0) {
                        arrTemp.push(dataKySo[0])
                    }
                });
            getFilesign769("PHIEU_NOITRU_GIAYHEN_DAIDIEN", thongtinhsba.thongtinbn.SOVAOVIEN, -1, singletonObject.dvtt,
                thongtinhsba.thongtinbn.SOVAOVIEN, thongtinhsba.thongtinbn.SOVAOVIEN_DT, -1, function(dataKySo) {
                    if (dataKySo.length > 0) {
                        arrTemp.push(dataKySo[0])
                    }
                });
            var maxCreateDate = null;
            var maxCreateDataObject = null;
            if (arrTemp.length > 0) {
                $.each(arrTemp, function (index, dataObject) {
                    var createDate = moment(dataObject.CREATE_DATE_STRING, "DD/MM/YYYY HH:mm:ss");
                    if (maxCreateDate === null || createDate.isAfter(maxCreateDate)) {
                        maxCreateDate = createDate;
                        maxCreateDataObject = dataObject;
                    }
                });
                getCMUFileSigned769GetLink(maxCreateDataObject.KEYMINIO, 'pdf').then(pdfData => {
                    resolve({
                        url: pdfData,
                        kySo: 1,
                        isError: 0,
                        message: 'Thành công',
                        idKySo: maxCreateDataObject.ID
                    });
                }).catch(e => {
                    resolve({
                        url: '',
                        kySo: 0,
                        isError: 1,
                        message: 'Lỗi get file ký số'
                    });
                });
            } else {
                var url = 'ingiayhen?dvtt=' + singletonObject.dvtt + "&stt_benhan=" + thongtinhsba.thongtinbn.STT_BENHAN
                    + "&stt_dotdieutri=" + thongtinhsba.thongtinbn.STT_DOTDIEUTRI + "&mabenhnhan=" + thongtinhsba.thongtinbn.MA_BENH_NHAN
                    + "&tenphongban=" + thongtinhsba.thongtinbn.TEN_PHONGBAN
                resolve({
                    url: url,
                    kySo: 0,
                    isError: 0,
                    message: 'Thành công'
                });
            }
        } catch (e) {
            console.log("Lỗi get file ký số giấy hẹn khám lại: " + e)
            reject({
                url: '',
                kySo: 0,
                isError: 1,
                message: 'Lỗi get file ký số giấy hẹn khám lại: ' + e
            });
        }
    });
}

function getUrlTheoDoiSDKhangSinhDP(params) {
    return new Promise(async (resolve, reject) => {
        try {
            var arrTemp = [];
            getFilesign769("PHIEU_NOITRU_THEODOISDKSINHDP_DAU", params.ID, -1, singletonObject.dvtt,
                thongtinhsba.thongtinbn.SOVAOVIEN, thongtinhsba.thongtinbn.SOVAOVIEN_DT, -1, function(dataKySo) {
                    if (dataKySo.length > 0) {
                        arrTemp.push(dataKySo[0])
                    }
                });
            getFilesign769("PHIEU_NOITRU_THEODOISDKSINHDP_LAI", params.ID, -1, singletonObject.dvtt,
                thongtinhsba.thongtinbn.SOVAOVIEN, thongtinhsba.thongtinbn.SOVAOVIEN_DT, -1, function(dataKySo) {
                    if (dataKySo.length > 0) {
                        arrTemp.push(dataKySo[0])
                    }
                });
            var maxCreateDate = null;
            var maxCreateDataObject = null;
            if (arrTemp.length > 0) {
                $.each(arrTemp, function (index, dataObject) {
                    var createDate = moment(dataObject.CREATE_DATE_STRING, "DD/MM/YYYY HH:mm:ss");
                    if (maxCreateDate === null || createDate.isAfter(maxCreateDate)) {
                        maxCreateDate = createDate;
                        maxCreateDataObject = dataObject;
                    }
                });
                getCMUFileSigned769GetLink(maxCreateDataObject.KEYMINIO, 'pdf').then(pdfData => {
                    resolve({
                        url: pdfData,
                        kySo: 1,
                        isError: 0,
                        message: 'Thành công',
                        idKySo: maxCreateDataObject.ID
                    });
                }).catch(e => {
                    resolve({
                        url: '',
                        kySo: 0,
                        isError: 1,
                        message: 'Lỗi get file ký số'
                    });
                });
            } else {
                var params2 = {
                    magiay: params.ID,
                    mabenhnhan: thongtinhsba.thongtinbn.MABENHNHAN,
                    khoa: thongtinhsba.thongtinbn.TEN_PHONGBAN,
                    diachi: thongtinhsba.thongtinbn.DIA_CHI,
                    sdt: thongtinhsba.thongtinbn.SO_DIEN_THOAI,
                    tennguoibenh: thongtinhsba.thongtinbn.TEN_BENH_NHAN,
                    sobenhan: thongtinhsba.thongtinbn.SOBENHAN,
                    sovaovien: thongtinhsba.thongtinbn.SOVAOVIEN,
                    chandoan: thongtinhsba.thongtinbn.ICD_HT,
                    gioitinh: thongtinhsba.thongtinbn.GIOI_TINH,
                    namsinh: thongtinhsba.thongtinbn.NGAY_SINH.split("/")[2],
                    tuoi: thongtinhsba.thongtinbn.TUOI,
                    stt_benhan: thongtinhsba.thongtinbn.STT_BENHAN,
                    stt_dotdieutri: thongtinhsba.thongtinbn.STT_DOTDIEUTRI,
                }
                var url = 'cmu_in_cmu_phieutheodoi_ksinh_duphong?type=pdf&' + $.param(params2);
                resolve({
                    url: url,
                    kySo: 0,
                    isError: 0,
                    message: 'Thành công'
                });
            }
        } catch (e) {
            console.log("Lỗi get file ký số phiếu theo dõi sử dụng kháng sinh dự phòng: " + e)
            reject({
                url: '',
                kySo: 0,
                isError: 1,
                message: 'Lỗi get file ký số phiếu theo dõi sử dụng kháng sinh dự phòng: ' + e
            });
        }
    });
}

function getUrlBanGiaoBS(params) {
    return new Promise(async (resolve, reject) => {
        try {
            var arrTemp = [];
            getFilesign769("PHIEU_NOITRU_BANGIAO_BSBANGIAO", params.ID, -1, singletonObject.dvtt,
                thongtinhsba.thongtinbn.SOVAOVIEN, thongtinhsba.thongtinbn.SOVAOVIEN_DT, -1, function(dataKySo) {
                    if (dataKySo.length > 0) {
                        arrTemp.push(dataKySo[0])
                    }
                });
            getFilesign769("PHIEU_NOITRU_BANGIAO_BSNHAN", params.ID, -1, singletonObject.dvtt,
                thongtinhsba.thongtinbn.SOVAOVIEN, thongtinhsba.thongtinbn.SOVAOVIEN_DT, -1, function(dataKySo) {
                    if (dataKySo.length > 0) {
                        arrTemp.push(dataKySo[0])
                    }
                });
            var maxCreateDate = null;
            var maxCreateDataObject = null;
            if (arrTemp.length > 0) {
                $.each(arrTemp, function (index, dataObject) {
                    var createDate = moment(dataObject.CREATE_DATE_STRING, "DD/MM/YYYY HH:mm:ss");
                    if (maxCreateDate === null || createDate.isAfter(maxCreateDate)) {
                        maxCreateDate = createDate;
                        maxCreateDataObject = dataObject;
                    }
                });
                getCMUFileSigned769GetLink(maxCreateDataObject.KEYMINIO, 'pdf').then(pdfData => {
                    resolve({
                        url: pdfData,
                        kySo: 1,
                        isError: 0,
                        message: 'Thành công',
                        idKySo: maxCreateDataObject.ID
                    });
                }).catch(e => {
                    resolve({
                        url: '',
                        kySo: 0,
                        isError: 1,
                        message: 'Lỗi get file ký số'
                    });
                });
            } else {
                var params2 = {
                    magiay: params.ID,
                    mabenhnhan: thongtinhsba.thongtinbn.MABENHNHAN,
                    tennguoibenh: thongtinhsba.thongtinbn.TEN_BENH_NHAN,
                    sobenhan: thongtinhsba.thongtinbn.SOBENHAN,
                    sovaovien: thongtinhsba.thongtinbn.SOVAOVIEN,
                    gioitinh: thongtinhsba.thongtinbn.GIOI_TINH,
                    tuoi: thongtinhsba.thongtinbn.TUOI,
                    khoa: thongtinhsba.thongtinbn.TEN_PHONGBAN,
                    stt_benhan: thongtinhsba.thongtinbn.STT_BENHAN,
                    stt_dotdieutri: thongtinhsba.thongtinbn.STT_DOTDIEUTRI,
                }
                var url = 'cmu_in_cmu_phieubangiao_bacsi?type=pdf&' + $.param(params2);
                resolve({
                    url: url,
                    kySo: 0,
                    isError: 0,
                    message: 'Thành công'
                });
            }
        } catch (e) {
            console.log("Lỗi get file ký số phiếu bàn giao người bệnh chuyển khoa (dành cho bác sĩ): " + e)
            reject({
                url: '',
                kySo: 0,
                isError: 1,
                message: 'Lỗi get file ký số phiếu bàn giao người bệnh chuyển khoa (dành cho bác sĩ): ' + e
            });
        }
    });
}

function getUrlChuanDoanNguyenNhanTV(params) {
    return new Promise(async (resolve, reject) => {
        try {
            var arrTemp = [];
            getFilesign769("PHIEU_CD_NGUYEN_NHAN_TU_VONG_NGUOI_LAP_PHIEU", params.ID, -1, singletonObject.dvtt,
                thongtinhsba.thongtinbn.SOVAOVIEN, thongtinhsba.thongtinbn.SOVAOVIEN_DT, -1, function(dataKySo) {
                    if (dataKySo.length > 0) {
                        arrTemp.push(dataKySo[0])
                    }
                });
            getFilesign769("PHIEU_CD_NGUYEN_NHAN_TU_VONG_THU_TRUONG_BAO_TU", params.ID, -1, singletonObject.dvtt,
                thongtinhsba.thongtinbn.SOVAOVIEN, thongtinhsba.thongtinbn.SOVAOVIEN_DT, -1, function(dataKySo) {
                    if (dataKySo.length > 0) {
                        arrTemp.push(dataKySo[0])
                    }
                });
            var maxCreateDate = null;
            var maxCreateDataObject = null;
            if (arrTemp.length > 0) {
                $.each(arrTemp, function (index, dataObject) {
                    var createDate = moment(dataObject.CREATE_DATE_STRING, "DD/MM/YYYY HH:mm:ss");
                    if (maxCreateDate === null || createDate.isAfter(maxCreateDate)) {
                        maxCreateDate = createDate;
                        maxCreateDataObject = dataObject;
                    }
                });
                getCMUFileSigned769GetLink(maxCreateDataObject.KEYMINIO, 'pdf').then(pdfData => {
                    resolve({
                        url: pdfData,
                        kySo: 1,
                        isError: 0,
                        message: 'Thành công',
                        idKySo: maxCreateDataObject.ID
                    });
                }).catch(e => {
                    console.log("Lỗi get file ký số phiếu chẩn đoán nguyên nhân tử vong: " + e)
                    reject({
                        url: '',
                        kySo: 0,
                        isError: 1,
                        message: "Lỗi get file ký số phiếu chẩn đoán nguyên nhân tử vong: " + e
                    });
                });
            } else {
                var params2 = {
                    ID_PHIEU: params.ID,
                }
                var url = 'cmu_in_rp_phieu_chuan_doan_nguyen_nhan_tu_vong?type=pdf&' + $.param(params2);
                resolve({
                    url: url,
                    kySo: 0,
                    isError: 0,
                    message: 'Thành công'
                });
            }
        } catch (e) {
            console.log("Lỗi get file ký số phiếu chẩn đoán nguyên nhân tử vong: " + e)
            reject({
                url: '',
                kySo: 0,
                isError: 1,
                message: 'Lỗi get file ký số phiếu chẩn đoán nguyên nhân tử vong: ' + e
            });
        }
    });
}

function getUrlKyThuatPHCN(params) {
    return new Promise(async (resolve, reject) => {
        try {
            var arrTemp = [];
            getFilesign769("PHIEU_NOITRU_KYTHUATPHCN", params.ID, -1, singletonObject.dvtt,
                thongtinhsba.thongtinbn.SOVAOVIEN, thongtinhsba.thongtinbn.SOVAOVIEN_DT, -1, function(dataKySo) {
                    if (dataKySo.length > 0) {
                        arrTemp.push(dataKySo[0])
                    }
                });
            getFilesign769("PHIEU_NOITRU_KYTHUATPHCN_NGUOITH", params.ID, -1, singletonObject.dvtt,
                thongtinhsba.thongtinbn.SOVAOVIEN, thongtinhsba.thongtinbn.SOVAOVIEN_DT, -1, function(dataKySo) {
                    if (dataKySo.length > 0) {
                        arrTemp.push(dataKySo[0])
                    }
                });
            getFilesign769("PHIEU_NOITRU_KYTHUATPHCN_BENHNHAN", params.ID, -1, singletonObject.dvtt,
                thongtinhsba.thongtinbn.SOVAOVIEN, thongtinhsba.thongtinbn.SOVAOVIEN_DT, -1, function(dataKySo) {
                    if (dataKySo.length > 0) {
                        arrTemp.push(dataKySo[0])
                    }
                });
            var maxCreateDate = null;
            var maxCreateDataObject = null;
            if (arrTemp.length > 0) {
                $.each(arrTemp, function (index, dataObject) {
                    var createDate = moment(dataObject.CREATE_DATE_STRING, "DD/MM/YYYY HH:mm:ss");
                    if (maxCreateDate === null || createDate.isAfter(maxCreateDate)) {
                        maxCreateDate = createDate;
                        maxCreateDataObject = dataObject;
                    }
                });
                getCMUFileSigned769GetLink(maxCreateDataObject.KEYMINIO, 'pdf').then(pdfData => {
                    resolve({
                        url: pdfData,
                        kySo: 1,
                        isError: 0,
                        message: 'Thành công',
                        idKySo: maxCreateDataObject.ID
                    });
                }).catch(e => {
                    resolve({
                        url: '',
                        kySo: 0,
                        isError: 1,
                        message: 'Lỗi get file ký số'
                    });
                });
            } else {
                var params2 = {
                    magiay: params.ID,
                    mabenhnhan: thongtinhsba.thongtinbn.MABENHNHAN,
                    tennguoibenh: thongtinhsba.thongtinbn.TEN_BENH_NHAN,
                    chandoan: thongtinhsba.thongtinbn.TENBENHCHINH_NHAPVIEN,
                    sobenhan: thongtinhsba.thongtinbn.SOBENHAN,
                    sovaovien: thongtinhsba.thongtinbn.SOVAOVIEN,
                    gioitinh: thongtinhsba.thongtinbn.GIOI_TINH,
                    tuoi: thongtinhsba.thongtinbn.TUOI,
                    khoa: thongtinhsba.thongtinbn.TEN_PHONGBAN,
                    stt_benhan: thongtinhsba.thongtinbn.STT_BENHAN,
                    stt_dotdieutri: thongtinhsba.thongtinbn.STT_DOTDIEUTRI,
                }
                var url = 'cmu_in_cmu_kithuatphcn?type=pdf&' + $.param(params2);
                resolve({
                    url: url,
                    kySo: 0,
                    isError: 0,
                    message: 'Thành công'
                });
            }
        } catch (e) {
            console.log("Lỗi get file ký số phiếu thực hiện kỹ thuật phục hồi chức năng: " + e)
            reject({
                url: '',
                kySo: 0,
                isError: 1,
                message: 'Lỗi get file ký số phiếu thực hiện kỹ thuật phục hồi chức năng: ' + e
            });
        }
    });
}

function getUrlCamKetRaVienKBS(params) {
    return new Promise(async (resolve, reject) => {
        try {
            var arrTemp = [];
            getFilesign769("PHIEU_NOITRU_CAMKETRAVIENKHONGBS", params.ID, -1, singletonObject.dvtt,
                thongtinhsba.thongtinbn.SOVAOVIEN, thongtinhsba.thongtinbn.SOVAOVIEN_DT, -1, function(dataKySo) {
                    if (dataKySo.length > 0) {
                        arrTemp.push(dataKySo[0])
                    }
                });
            getFilesign769("PHIEU_NOITRU_CAMKETRAVIENKHONGBS_BENHNHAN", params.ID, -1, singletonObject.dvtt,
                thongtinhsba.thongtinbn.SOVAOVIEN, thongtinhsba.thongtinbn.SOVAOVIEN_DT, -1, function(dataKySo) {
                    if (dataKySo.length > 0) {
                        arrTemp.push(dataKySo[0])
                    }
                });
            var maxCreateDate = null;
            var maxCreateDataObject = null;
            if (arrTemp.length > 0) {
                $.each(arrTemp, function (index, dataObject) {
                    var createDate = moment(dataObject.CREATE_DATE_STRING, "DD/MM/YYYY HH:mm:ss");
                    if (maxCreateDate === null || createDate.isAfter(maxCreateDate)) {
                        maxCreateDate = createDate;
                        maxCreateDataObject = dataObject;
                    }
                });
                getCMUFileSigned769GetLink(maxCreateDataObject.KEYMINIO, 'pdf').then(pdfData => {
                    resolve({
                        url: pdfData,
                        kySo: 1,
                        isError: 0,
                        message: 'Thành công',
                        idKySo: maxCreateDataObject.ID
                    });
                }).catch(e => {
                    resolve({
                        url: '',
                        kySo: 0,
                        isError: 1,
                        message: 'Lỗi get file ký số'
                    });
                });
            } else {
                var params2 = {
                    magiay: params.ID,
                    mabenhnhan: thongtinhsba.thongtinbn.MABENHNHAN,
                    khoa: thongtinhsba.thongtinbn.TEN_PHONGBAN,
                    diachi: thongtinhsba.thongtinbn.DIA_CHI,
                    sdt: thongtinhsba.thongtinbn.SO_DIEN_THOAI,
                    tenbenhnhan: thongtinhsba.thongtinbn.TEN_BENH_NHAN,
                    sobenhan: thongtinhsba.thongtinbn.SOBENHAN,
                    sovaovien: thongtinhsba.thongtinbn.SOVAOVIEN,
                    gioitinh: thongtinhsba.thongtinbn.GIOI_TINH,
                    namsinh: thongtinhsba.thongtinbn.NGAY_SINH.split("/")[2],
                    tuoi: thongtinhsba.thongtinbn.TUOI,
                    stt_benhan: thongtinhsba.thongtinbn.STT_BENHAN,
                    stt_dotdieutri: thongtinhsba.thongtinbn.STT_DOTDIEUTRI,
                }
                var url = 'cmu_in_cmu_giayckravien_chuaktbs?type=pdf&' + $.param(params2);
                resolve({
                    url: url,
                    kySo: 0,
                    isError: 0,
                    message: 'Thành công'
                });
            }
        } catch (e) {
            console.log("Lỗi get file ký số phiếu cam kết ra viện không theo chỉ định bác sĩ: " + e)
            reject({
                url: '',
                kySo: 0,
                isError: 1,
                message: 'Lỗi get file ký số phiếu cam kết ra viện không theo chỉ định bác sĩ: ' + e
            });
        }
    });
}

function getUrlCamKetPhauThuatGMHS(params) {
    return new Promise(async (resolve, reject) => {
        try {
            var arrTemp = [];
            getFilesign769("PHIEU_NOITRU_CAMKETPHAUTHUATGMHS_BSPT", params.ID, -1, singletonObject.dvtt,
                thongtinhsba.thongtinbn.SOVAOVIEN, thongtinhsba.thongtinbn.SOVAOVIEN_DT, -1, function(dataKySo) {
                    if (dataKySo.length > 0) {
                        arrTemp.push(dataKySo[0])
                    }
                });
            getFilesign769("PHIEU_NOITRU_CAMKETPHAUTHUATGMHS_BSGM", params.ID, -1, singletonObject.dvtt,
                thongtinhsba.thongtinbn.SOVAOVIEN, thongtinhsba.thongtinbn.SOVAOVIEN_DT, -1, function(dataKySo) {
                    if (dataKySo.length > 0) {
                        arrTemp.push(dataKySo[0])
                    }
                });
            var maxCreateDate = null;
            var maxCreateDataObject = null;
            if (arrTemp.length > 0) {
                $.each(arrTemp, function (index, dataObject) {
                    var createDate = moment(dataObject.CREATE_DATE_STRING, "DD/MM/YYYY HH:mm:ss");
                    if (maxCreateDate === null || createDate.isAfter(maxCreateDate)) {
                        maxCreateDate = createDate;
                        maxCreateDataObject = dataObject;
                    }
                });
                getCMUFileSigned769GetLink(maxCreateDataObject.KEYMINIO, 'pdf').then(pdfData => {
                    resolve({
                        url: pdfData,
                        kySo: 1,
                        isError: 0,
                        message: 'Thành công',
                        idKySo: maxCreateDataObject.ID
                    });
                }).catch(e => {
                    resolve({
                        url: '',
                        kySo: 0,
                        isError: 1,
                        message: 'Lỗi get file ký số'
                    });
                });
            } else {
                var params2 = {
                    magiay: params.ID,
                    mabenhnhan: thongtinhsba.thongtinbn.MABENHNHAN,
                    khoa: thongtinhsba.thongtinbn.TEN_PHONGBAN,
                    diachi: thongtinhsba.thongtinbn.DIA_CHI,
                    sdt: thongtinhsba.thongtinbn.SO_DIEN_THOAI,
                    tenbenhnhan: thongtinhsba.thongtinbn.TEN_BENH_NHAN,
                    sobenhan: thongtinhsba.thongtinbn.SOBENHAN,
                    sovaovien: thongtinhsba.thongtinbn.SOVAOVIEN,
                    gioitinh: thongtinhsba.thongtinbn.GIOI_TINH,
                    namsinh: thongtinhsba.thongtinbn.NGAY_SINH.split("/")[2],
                    tuoi: thongtinhsba.thongtinbn.TUOI,
                    stt_benhan: thongtinhsba.thongtinbn.STT_BENHAN,
                    stt_dotdieutri: thongtinhsba.thongtinbn.STT_DOTDIEUTRI,
                }
                var url = 'cmu_in_cmu_giaycdphauthuatgayme?type=pdf&' + $.param(params2);
                resolve({
                    url: url,
                    kySo: 0,
                    isError: 0,
                    message: 'Thành công'
                });
            }
        } catch (e) {
            console.log("Lỗi get file ký số phiếu cam kết chấp thuận phẫu thuật, thủ thuật và gây mê hồi sức: " + e)
            reject({
                url: '',
                kySo: 0,
                isError: 1,
                message: 'Lỗi get file ký số phiếu cam kết chấp thuận phẫu thuật, thủ thuật và gây mê hồi sức: ' + e
            });
        }
    });
}

function getUrlCamKetDTXaTri(params) {
    return new Promise(async (resolve, reject) => {
        try {
            getFilesign769(
                "PHIEU_NOITRU_CAMKETDTXATRI",
                params.ID,
                -1,
                singletonObject.dvtt,
                thongtinhsba.thongtinbn.SOVAOVIEN,
                thongtinhsba.thongtinbn.SOVAOVIEN_DT,
                -1,
                function(dataKySo) {
                    console.log(dataKySo)
                    if(dataKySo.length > 0) {
                        getCMUFileSigned769GetLink(dataKySo[0].KEYMINIO, 'pdf').then(pdfData => {
                            resolve({
                                url: pdfData,
                                kySo: 1,
                                isError: 0,
                                message: 'Thành công',
                                idKySo: dataKySo[0].ID
                            });
                        }).catch(e => {
                            resolve({
                                url: '',
                                kySo: 0,
                                isError: 1,
                                message: 'Lỗi get file ký số'
                            });
                        });
                    } else {
                        var params2 = {
                            magiay: params.ID,
                            mabenhnhan: thongtinhsba.thongtinbn.MABENHNHAN,
                            khoa: thongtinhsba.thongtinbn.TEN_PHONGBAN,
                            diachi: thongtinhsba.thongtinbn.DIA_CHI,
                            sdt: thongtinhsba.thongtinbn.SO_DIEN_THOAI,
                            tenbenhnhan: thongtinhsba.thongtinbn.TEN_BENH_NHAN,
                            sobenhan: thongtinhsba.thongtinbn.SOBENHAN,
                            sovaovien: thongtinhsba.thongtinbn.SOVAOVIEN,
                            gioitinh: thongtinhsba.thongtinbn.GIOI_TINH,
                            namsinh: thongtinhsba.thongtinbn.NGAY_SINH.split("/")[2],
                            tuoi: thongtinhsba.thongtinbn.TUOI,
                            stt_benhan: thongtinhsba.thongtinbn.STT_BENHAN,
                            stt_dotdieutri: thongtinhsba.thongtinbn.STT_DOTDIEUTRI,
                        }
                        var url = 'cmu_in_cmu_giayckctdtxatri?type=pdf&' + $.param(params2);
                        resolve({
                            url: url,
                            kySo: 0,
                            isError: 0,
                            message: 'Thành công'
                        });
                    }
                }
            )
        } catch (e) {
            console.log("Lỗi get file ký số phiếu cam kết chấp thuận điều trị bằng xạ trị: " + e)
            reject({
                url: '',
                kySo: 0,
                isError: 1,
                message: 'Lỗi get file ký số phiếu cam kết chấp thuận điều trị bằng xạ trị: ' + e
            });
        }
    });
}

function getUrlCamKetDTHoaXaTri(params) {
    return new Promise(async (resolve, reject) => {
        try {
            getFilesign769(
                "PHIEU_NOITRU_CAMKETDTHOAXATRI",
                params.ID,
                -1,
                singletonObject.dvtt,
                thongtinhsba.thongtinbn.SOVAOVIEN,
                thongtinhsba.thongtinbn.SOVAOVIEN_DT,
                -1,
                function(dataKySo) {
                    console.log(dataKySo)
                    if(dataKySo.length > 0) {
                        getCMUFileSigned769GetLink(dataKySo[0].KEYMINIO, 'pdf').then(pdfData => {
                            resolve({
                                url: pdfData,
                                kySo: 1,
                                isError: 0,
                                message: 'Thành công',
                                idKySo: dataKySo[0].ID
                            });
                        }).catch(e => {
                            resolve({
                                url: '',
                                kySo: 0,
                                isError: 1,
                                message: 'Lỗi get file ký số'
                            });
                        });
                    } else {
                        var params2 = {
                            magiay: params.ID,
                            mabenhnhan: thongtinhsba.thongtinbn.MABENHNHAN,
                            khoa: thongtinhsba.thongtinbn.TEN_PHONGBAN,
                            diachi: thongtinhsba.thongtinbn.DIA_CHI,
                            sdt: thongtinhsba.thongtinbn.SO_DIEN_THOAI,
                            tenbenhnhan: thongtinhsba.thongtinbn.TEN_BENH_NHAN,
                            sobenhan: thongtinhsba.thongtinbn.SOBENHAN,
                            sovaovien: thongtinhsba.thongtinbn.SOVAOVIEN,
                            gioitinh: thongtinhsba.thongtinbn.GIOI_TINH,
                            namsinh: thongtinhsba.thongtinbn.NGAY_SINH.split("/")[2],
                            tuoi: thongtinhsba.thongtinbn.TUOI,
                            stt_benhan: thongtinhsba.thongtinbn.STT_BENHAN,
                            stt_dotdieutri: thongtinhsba.thongtinbn.STT_DOTDIEUTRI,
                        }
                        var url = 'cmu_in_cmu_giayckctdthoaxatri?type=pdf&' + $.param(params2);
                        resolve({
                            url: url,
                            kySo: 0,
                            isError: 0,
                            message: 'Thành công'
                        });
                    }
                }
            )
        } catch (e) {
            console.log("Lỗi get file ký số phiếu cam kết chấp thuận điều trị bằng hóa trị - xạ trị: " + e)
            reject({
                url: '',
                kySo: 0,
                isError: 1,
                message: 'Lỗi get file ký số phiếu cam kết chấp thuận điều trị bằng hóa trị - xạ trị: ' + e
            });
        }
    });
}

function getUrlThuPUThuoc(params) {
    return new Promise(async (resolve, reject) => {
        try {
            getFilesign769(
                "PHIEU_NOITRU_PHIEUTHUPUTHUOC",
                1,
                -1,
                singletonObject.dvtt,
                thongtinhsba.thongtinbn.SOVAOVIEN,
                thongtinhsba.thongtinbn.SOVAOVIEN_DT,
                -1,
                function(dataKySo) {
                    if(dataKySo.length > 0) {
                        getCMUFileSigned769GetLink(dataKySo[0].KEYMINIO, 'pdf').then(pdfData => {
                            resolve({
                                url: pdfData,
                                kySo: 1,
                                isError: 0,
                                message: 'Thành công',
                                idKySo: dataKySo[0].ID
                            });
                        }).catch(e => {
                            resolve({
                                url: '',
                                kySo: 0,
                                isError: 1,
                                message: 'Lỗi get file ký số'
                            });
                        });
                    } else {
                        var params2 = {
                            tenbenhnhan: thongtinhsba.thongtinbn.TEN_BENH_NHAN,
                            sobenhan: thongtinhsba.thongtinbn.SOBENHAN,
                            sovaovien: thongtinhsba.thongtinbn.SOVAOVIEN,
                            gioitinh: thongtinhsba.thongtinbn.GIOI_TINH,
                            tuoi: thongtinhsba.thongtinbn.TUOI,
                            khoa: thongtinhsba.thongtinbn.TEN_PHONGBAN,
                            stt_benhan: thongtinhsba.thongtinbn.STT_BENHAN,
                            stt_dotdieutri: thongtinhsba.thongtinbn.STT_DOTDIEUTRI,
                            chandoan: thongtinhsba.thongtinbn.TENBENHCHINH_NHAPVIEN
                        }
                        var url = 'cmu_in_cmu_phieuthuputhuoc?type=pdf&' + $.param(params2);
                        resolve({
                            url: url,
                            kySo: 0,
                            isError: 0,
                            message: 'Thành công'
                        });
                    }
                }
            )
        } catch (e) {
            console.log("Lỗi get file ký số phiếu thử phản ứng thuốc: " + e)
            reject({
                url: '',
                kySo: 0,
                isError: 1,
                message: 'Lỗi get file ký số phiếu thử phản ứng thuốc: ' + e
            });
        }
    });
}

function getUrlCDCNChayThanNT(params) {
    return new Promise(async (resolve, reject) => {
        try {
            getFilesign769(
                "PHIEU_NOITRU_GIAYCDCTNTCAPCUU",
                params.ID,
                -1,
                singletonObject.dvtt,
                thongtinhsba.thongtinbn.SOVAOVIEN,
                thongtinhsba.thongtinbn.SOVAOVIEN_DT,
                -1,
                function(dataKySo) {
                    if(dataKySo.length > 0) {
                        getCMUFileSigned769GetLink(dataKySo[0].KEYMINIO, 'pdf').then(pdfData => {
                            resolve({
                                url: pdfData,
                                kySo: 1,
                                isError: 0,
                                message: 'Thành công',
                                idKySo: dataKySo[0].ID
                            });
                        }).catch(e => {
                            resolve({
                                url: '',
                                kySo: 0,
                                isError: 1,
                                message: 'Lỗi get file ký số'
                            });
                        });
                    } else {
                        var text_date_part = params.NGAY_TAO_PHIEU.split("/");
                        var text_date = text_date_part[0];
                        var text_month = text_date_part[1];
                        var text_year = text_date_part[2];
                        var params2 = {
                            magiay: params.ID,
                            sobenhan: thongtinhsba.thongtinbn.SOBENHAN,
                            sovaovien: thongtinhsba.thongtinbn.SOVAOVIEN,
                            mabenhnhan: thongtinhsba.thongtinbn.MABENHNHAN,
                            text_date,
                            text_month,
                            text_year
                        }
                        var url = 'cmu_in_cmu_giaycdctnt_capcuu?type=pdf&' + $.param(params2);
                        resolve({
                            url: url,
                            kySo: 0,
                            isError: 0,
                            message: 'Thành công'
                        });
                    }
                }
            )
        } catch (e) {
            console.log("Lỗi get file ký số phiếu cam đoan chấp nhận chạy thận nhân tạo cấp cứu: " + e)
            reject({
                url: '',
                kySo: 0,
                isError: 1,
                message: 'Lỗi get file ký số phiếu cam đoan chấp nhận chạy thận nhân tạo cấp cứu: ' + e
            });
        }
    });
}

function getUrlCKChuyenCSKham(params) {
    return new Promise(async (resolve, reject) => {
        try {
            getFilesign769(
                "PHIEU_NOITRU_CAMKETCHUYENCSKB",
                params.ID,
                -1,
                singletonObject.dvtt,
                thongtinhsba.thongtinbn.SOVAOVIEN,
                thongtinhsba.thongtinbn.SOVAOVIEN_DT,
                -1,
                function(dataKySo) {
                    if(dataKySo.length > 0) {
                        getCMUFileSigned769GetLink(dataKySo[0].KEYMINIO, 'pdf').then(pdfData => {
                            resolve({
                                url: pdfData,
                                kySo: 1,
                                isError: 0,
                                message: 'Thành công',
                                idKySo: dataKySo[0].ID
                            });
                        }).catch(e => {
                            resolve({
                                url: '',
                                kySo: 0,
                                isError: 1,
                                message: 'Lỗi get file ký số'
                            });
                        });
                    } else {
                        var params2 = {
                            magiay: params.ID,
                            mabenhnhan: thongtinhsba.thongtinbn.MABENHNHAN,
                            khoa: thongtinhsba.thongtinbn.TEN_PHONGBAN,
                            diachi: thongtinhsba.thongtinbn.DIA_CHI,
                            sdt: thongtinhsba.thongtinbn.SO_DIEN_THOAI,
                            tenbenhnhan: thongtinhsba.thongtinbn.TEN_BENH_NHAN,
                            sobenhan: thongtinhsba.thongtinbn.SOBENHAN,
                            sovaovien: thongtinhsba.thongtinbn.SOVAOVIEN,
                            gioitinh: thongtinhsba.thongtinbn.GIOI_TINH,
                            namsinh: thongtinhsba.thongtinbn.NGAY_SINH.split("/")[2],
                            tuoi: thongtinhsba.thongtinbn.TUOI,
                            stt_benhan: thongtinhsba.thongtinbn.STT_BENHAN,
                            stt_dotdieutri: thongtinhsba.thongtinbn.STT_DOTDIEUTRI,
                        }
                        var url = 'cmu_in_cmu_giayckchuyencosokb?type=pdf&' + $.param(params2);
                        resolve({
                            url: url,
                            kySo: 0,
                            isError: 0,
                            message: 'Thành công'
                        });
                    }
                }
            )
        } catch (e) {
            console.log("Lỗi get file ký số phiếu cam kết chuyển cơ sở khám bệnh, chữa bệnh: " + e)
            reject({
                url: '',
                kySo: 0,
                isError: 1,
                message: 'Lỗi get file ký số phiếu cam kết chuyển cơ sở khám bệnh, chữa bệnh: ' + e
            });
        }
    });
}

function getUrlCungCapTTNB(params) {
    return new Promise(async (resolve, reject) => {
        try {
            getFilesign769(
                "PHIEU_NOITRU_CCTTNGUOIBENH",
                params.ID,
                -1,
                singletonObject.dvtt,
                thongtinhsba.thongtinbn.SOVAOVIEN,
                thongtinhsba.thongtinbn.SOVAOVIEN_DT,
                -1,
                function(dataKySo) {
                    if(dataKySo.length > 0) {
                        getCMUFileSigned769GetLink(dataKySo[0].KEYMINIO, 'pdf').then(pdfData => {
                            resolve({
                                url: pdfData,
                                kySo: 1,
                                isError: 0,
                                message: 'Thành công',
                                idKySo: dataKySo[0].ID
                            });
                        }).catch(e => {
                            resolve({
                                url: '',
                                kySo: 0,
                                isError: 1,
                                message: 'Lỗi get file ký số'
                            });
                        });
                    } else {
                        var params2 = {
                            magiay: params.ID,
                            mabenhnhan: thongtinhsba.thongtinbn.MABENHNHAN,
                            khoa: thongtinhsba.thongtinbn.TEN_PHONGBAN,
                            diachi: thongtinhsba.thongtinbn.DIA_CHI,
                            sdt: thongtinhsba.thongtinbn.SO_DIEN_THOAI,
                            tenbenhnhan: thongtinhsba.thongtinbn.TEN_BENH_NHAN,
                            sobenhan: thongtinhsba.thongtinbn.SOBENHAN,
                            sovaovien: thongtinhsba.thongtinbn.SOVAOVIEN,
                            gioitinh: thongtinhsba.thongtinbn.GIOI_TINH,
                            namsinh: thongtinhsba.thongtinbn.NGAY_SINH.split("/")[2],
                            tuoi: thongtinhsba.thongtinbn.TUOI,
                            stt_benhan: thongtinhsba.thongtinbn.STT_BENHAN,
                            stt_dotdieutri: thongtinhsba.thongtinbn.STT_DOTDIEUTRI,
                        }
                        var url = 'cmu_in_cmu_giayccttnguoibenh?type=pdf&' + $.param(params2);
                        resolve({
                            url: url,
                            kySo: 0,
                            isError: 0,
                            message: 'Thành công'
                        });
                    }
                }
            )
        } catch (e) {
            console.log("Lỗi get file ký số phiếu cung cấp thông tin về người bệnh (tại khoa hồi sức tích cực): " + e)
            reject({
                url: '',
                kySo: 0,
                isError: 1,
                message: 'Lỗi get file ký số phiếu cung cấp thông tin về người bệnh (tại khoa hồi sức tích cực): ' + e
            });
        }
    });
}

function getUrlKhamBenhTheoYC(params) {
    return new Promise(async (resolve, reject) => {
        try {
            getFilesign769(
                "PHIEU_NOITRU_KHAMBENHTHEOYC",
                params.ID,
                -1,
                singletonObject.dvtt,
                thongtinhsba.thongtinbn.SOVAOVIEN,
                thongtinhsba.thongtinbn.SOVAOVIEN_DT,
                -1,
                function(dataKySo) {
                    if(dataKySo.length > 0) {
                        getCMUFileSigned769GetLink(dataKySo[0].KEYMINIO, 'pdf').then(pdfData => {
                            resolve({
                                url: pdfData,
                                kySo: 1,
                                isError: 0,
                                message: 'Thành công',
                                idKySo: dataKySo[0].ID
                            });
                        }).catch(e => {
                            resolve({
                                url: '',
                                kySo: 0,
                                isError: 1,
                                message: 'Lỗi get file ký số'
                            });
                        });
                    } else {
                        var params2 = {
                            magiay: params.ID,
                            mabenhnhan: thongtinhsba.thongtinbn.MABENHNHAN,
                            khoa: thongtinhsba.thongtinbn.TEN_PHONGBAN,
                            diachi: thongtinhsba.thongtinbn.DIA_CHI,
                            sdt: thongtinhsba.thongtinbn.SO_DIEN_THOAI,
                            tenbenhnhan: thongtinhsba.thongtinbn.TEN_BENH_NHAN,
                            sobenhan: thongtinhsba.thongtinbn.SOBENHAN,
                            sovaovien: thongtinhsba.thongtinbn.SOVAOVIEN,
                            gioitinh: thongtinhsba.thongtinbn.GIOI_TINH,
                            namsinh: thongtinhsba.thongtinbn.NGAY_SINH.split("/")[2],
                            tuoi: thongtinhsba.thongtinbn.TUOI,
                            stt_benhan: thongtinhsba.thongtinbn.STT_BENHAN,
                            stt_dotdieutri: thongtinhsba.thongtinbn.STT_DOTDIEUTRI,
                        }
                        var url = 'cmu_in_cmu_giaykbtheoyc?type=pdf&' + $.param(params2);
                        resolve({
                            url: url,
                            kySo: 0,
                            isError: 0,
                            message: 'Thành công'
                        });
                    }
                }
            )
        } catch (e) {
            console.log("Lỗi get file ký số phiếu khám chữa bệnh theo yêu cầu: " + e)
            reject({
                url: '',
                kySo: 0,
                isError: 1,
                message: 'Lỗi get file ký số phiếu khám chữa bệnh theo yêu cầu: ' + e
            });
        }
    });
}

function getUrlPhieuCSC1Khac(params) {
    return new Promise(async (resolve, reject) => {
        try {
            getFilesign769(
                "PHIEU_NOITRU_PHIEUCHAMSOCCAP1",
                params.ID,
                -1,
                singletonObject.dvtt,
                thongtinhsba.thongtinbn.SOVAOVIEN,
                thongtinhsba.thongtinbn.SOVAOVIEN_DT,
                -1,
                function(dataKySo) {
                    if(dataKySo.length > 0) {
                        getCMUFileSigned769GetLink(dataKySo[0].KEYMINIO, 'pdf').then(pdfData => {
                            resolve({
                                url: pdfData,
                                kySo: 1,
                                isError: 0,
                                message: 'Thành công',
                                idKySo: dataKySo[0].ID
                            });
                        }).catch(e => {
                            resolve({
                                url: '',
                                kySo: 0,
                                isError: 1,
                                message: 'Lỗi get file ký số'
                            });
                        });
                    } else {
                        var params2 = {
                            magiay: params.ID,
                            mabenhnhan: thongtinhsba.thongtinbn.MA_BENH_NHAN,
                            khoa: thongtinhsba.thongtinbn.TEN_PHONGBAN,
                            diachi: thongtinhsba.thongtinbn.DIA_CHI,
                            sdt: thongtinhsba.thongtinbn.SO_DIEN_THOAI,
                            tenbenhnhan: thongtinhsba.thongtinbn.TEN_BENH_NHAN,
                            sobenhan: thongtinhsba.thongtinbn.SOBENHAN,
                            sovaovien: thongtinhsba.thongtinbn.SOVAOVIEN,
                            gioitinh: thongtinhsba.thongtinbn.GIOI_TINH,
                            namsinh: thongtinhsba.thongtinbn.NGAY_SINH.split("/")[2],
                            tuoi: thongtinhsba.thongtinbn.TUOI,
                            stt_benhan: thongtinhsba.thongtinbn.STT_BENHAN,
                            stt_dotdieutri: thongtinhsba.thongtinbn.STT_DOTDIEUTRI,
                        }
                        var url = 'cmu_in_cmu_phieuchamsoccap1?type=pdf&' + $.param(params2);
                        resolve({
                            url: url,
                            kySo: 0,
                            isError: 0,
                            message: 'Thành công'
                        });
                    }
                }
            )
        } catch (e) {
            console.log("Lỗi get file ký số phiếu chăm sóc cấp 1: " + e)
            reject({
                url: '',
                kySo: 0,
                isError: 1,
                message: 'Lỗi get file ký số phiếu chăm sóc cấp 1: ' + e
            });
        }
    });
}

function getUrlCamKetTuChoiSDDV(params) {
    return new Promise(async (resolve, reject) => {
        try {
            getFilesign769(
                "PHIEU_NOITRU_CAMKETTUCHOISDDV",
                params.ID,
                -1,
                singletonObject.dvtt,
                thongtinhsba.thongtinbn.SOVAOVIEN,
                thongtinhsba.thongtinbn.SOVAOVIEN_DT,
                -1,
                function(dataKySo) {
                    if(dataKySo.length > 0) {
                        getCMUFileSigned769GetLink(dataKySo[0].KEYMINIO, 'pdf').then(pdfData => {
                            resolve({
                                url: pdfData,
                                kySo: 1,
                                isError: 0,
                                message: 'Thành công',
                                idKySo: dataKySo[0].ID
                            });
                        }).catch(e => {
                            resolve({
                                url: '',
                                kySo: 0,
                                isError: 1,
                                message: 'Lỗi get file ký số'
                            });
                        });
                    } else {
                        var params2 = {
                            magiay: params.ID,
                            mabenhnhan: thongtinhsba.thongtinbn.MABENHNHAN,
                            khoa: thongtinhsba.thongtinbn.TEN_PHONGBAN,
                            diachi: thongtinhsba.thongtinbn.DIA_CHI,
                            sdt: thongtinhsba.thongtinbn.SO_DIEN_THOAI,
                            tenbenhnhan: thongtinhsba.thongtinbn.TEN_BENH_NHAN,
                            sobenhan: thongtinhsba.thongtinbn.SOBENHAN,
                            sovaovien: thongtinhsba.thongtinbn.SOVAOVIEN,
                            gioitinh: thongtinhsba.thongtinbn.GIOI_TINH,
                            namsinh: thongtinhsba.thongtinbn.NGAY_SINH.split("/")[2],
                            tuoi: thongtinhsba.thongtinbn.TUOI,
                            stt_benhan: thongtinhsba.thongtinbn.STT_BENHAN,
                            stt_dotdieutri: thongtinhsba.thongtinbn.STT_DOTDIEUTRI,
                        }
                        var url = 'cmu_in_cmu_giaycktuchoidvkb?type=pdf&' + $.param(params2);
                        resolve({
                            url: url,
                            kySo: 0,
                            isError: 0,
                            message: 'Thành công'
                        });
                    }
                }
            )
        } catch (e) {
            console.log("Lỗi get file ký số phiếu cam kết từ chối sử dụng dịch vụ khám bệnh, chữa bệnh: " + e)
            reject({
                url: '',
                kySo: 0,
                isError: 1,
                message: 'Lỗi get file ký số phiếu cam kết từ chối sử dụng dịch vụ khám bệnh, chữa bệnh: ' + e
            });
        }
    });
}

function getUrlTomTatHSBA(params) {
    return new Promise(async (resolve, reject) => {
        try {
            getFilesign769(
                "PHIEU_NOITRU_TOMTATHSBA",
                params.ID,
                -1,
                singletonObject.dvtt,
                thongtinhsba.thongtinbn.SOVAOVIEN,
                thongtinhsba.thongtinbn.SOVAOVIEN_DT,
                -1,
                function(dataKySo) {
                    if(dataKySo.length > 0) {
                        getCMUFileSigned769GetLink(dataKySo[0].KEYMINIO, 'pdf').then(pdfData => {
                            resolve({
                                url: pdfData,
                                kySo: 1,
                                isError: 0,
                                message: 'Thành công',
                                idKySo: dataKySo[0].ID
                            });
                        }).catch(e => {
                            resolve({
                                url: '',
                                kySo: 0,
                                isError: 1,
                                message: 'Lỗi get file ký số'
                            });
                        });
                    } else {
                        var params2 = {
                            magiay: params.ID,
                            mabenhnhan: thongtinhsba.thongtinbn.MABENHNHAN,
                            khoa: thongtinhsba.thongtinbn.TEN_PHONGBAN,
                            diachi: thongtinhsba.thongtinbn.DIA_CHI,
                            huyen: thongtinhsba.thongtinbn.QUANHUYEN_TEXT,
                            phuong: thongtinhsba.thongtinbn.XAPHUONG_TEXT,
                            tinh: thongtinhsba.thongtinbn.TINHTHANH_TEXT,
                            sdt: thongtinhsba.thongtinbn.SO_DIEN_THOAI,
                            tenbenhnhan: thongtinhsba.thongtinbn.TEN_BENH_NHAN,
                            sobenhan: thongtinhsba.thongtinbn.SOBENHAN,
                            sovaovien: thongtinhsba.thongtinbn.SOVAOVIEN,
                            ngayvaovien: thongtinhsba.thongtinbn.NGAY_VAO_VIEN,
                            ngayravien: thongtinhsba.thongtinbn.NGAY_RA_VIEN,
                            sogiayto: thongtinhsba.thongtinbn.CMT_BENHNHAN,
                            sobhyt: thongtinhsba.thongtinbn.SOTHEBHYT,
                            gioitinh: thongtinhsba.thongtinbn.GIOI_TINH,
                            namsinh: thongtinhsba.thongtinbn.NGAY_SINH.split("/")[2],
                            ngaysinh: thongtinhsba.thongtinbn.NGAY_SINH,
                            tenbenhchinh: thongtinhsba.thongtinbn.CHANDOAN_NGUYENNHAN,
                            tuoi: thongtinhsba.thongtinbn.TUOI,
                            lydovaovien: thongtinhsba.thongtinbn.LYDO_TRANGTHAI_BN_NHAPVIEN,
                            stt_benhan: thongtinhsba.thongtinbn.STT_BENHAN,
                            stt_dotdieutri: thongtinhsba.thongtinbn.STT_DOTDIEUTRI,
                        }
                        var url = 'cmu_in_cmu_bantomtathsba?type=pdf&' + $.param(params2);
                        resolve({
                            url: url,
                            kySo: 0,
                            isError: 0,
                            message: 'Thành công'
                        });
                    }
                }
            )
        } catch (e) {
            console.log("Lỗi get file ký số phiếu tóm tắt hồ sơ bệnh án: " + e)
            reject({
                url: '',
                kySo: 0,
                isError: 1,
                message: 'Lỗi get file ký số phiếu tóm tắt hồ sơ bệnh án: ' + e
            });
        }
    });
}

function getUrlLuongGiaHDCN(params) {
    return new Promise(async (resolve, reject) => {
        try {
            getFilesign769(
                "PHIEU_NOITRU_LUONGGIAHDCN",
                params.ID,
                -1,
                singletonObject.dvtt,
                thongtinhsba.thongtinbn.SOVAOVIEN,
                thongtinhsba.thongtinbn.SOVAOVIEN_DT,
                -1,
                function(dataKySo) {
                    if(dataKySo.length > 0) {
                        getCMUFileSigned769GetLink(dataKySo[0].KEYMINIO, 'pdf').then(pdfData => {
                            resolve({
                                url: pdfData,
                                kySo: 1,
                                isError: 0,
                                message: 'Thành công',
                                idKySo: dataKySo[0].ID
                            });
                        }).catch(e => {
                            resolve({
                                url: '',
                                kySo: 0,
                                isError: 1,
                                message: 'Lỗi get file ký số'
                            });
                        });
                    } else {
                        var params2 = {
                            magiay: params.ID,
                            mabenhnhan: thongtinhsba.thongtinbn.MABENHNHAN,
                            tennguoibenh: thongtinhsba.thongtinbn.TEN_BENH_NHAN,
                            sobenhan: thongtinhsba.thongtinbn.SOBENHAN,
                            sovaovien: thongtinhsba.thongtinbn.SOVAOVIEN,
                            gioitinh: thongtinhsba.thongtinbn.GIOI_TINH,
                            tuoi: thongtinhsba.thongtinbn.TUOI,
                            khoa: thongtinhsba.thongtinbn.TEN_PHONGBAN,
                            stt_benhan: thongtinhsba.thongtinbn.STT_BENHAN,
                            stt_dotdieutri: thongtinhsba.thongtinbn.STT_DOTDIEUTRI,
                        }
                        var url = 'cmu_in_cmu_phieuluonggiahdchucnang?type=pdf&' + $.param(params2);
                        resolve({
                            url: url,
                            kySo: 0,
                            isError: 0,
                            message: 'Thành công'
                        });
                    }
                }
            )
        } catch (e) {
            console.log("Lỗi get file ký số phiếu lượng giá hoạt động chức năng và sự tham gia: " + e)
            reject({
                url: '',
                kySo: 0,
                isError: 1,
                message: 'Lỗi get file ký số phiếu lượng giá hoạt động chức năng và sự tham gia: ' + e
            });
        }
    });
}

function getUrlKhamChiDinhPHCN(params) {
    return new Promise(async (resolve, reject) => {
        try {
            getFilesign769(
                "PHIEU_NOITRU_KHAMCHIDINHPHCN",
                params.ID,
                -1,
                singletonObject.dvtt,
                thongtinhsba.thongtinbn.SOVAOVIEN,
                thongtinhsba.thongtinbn.SOVAOVIEN_DT,
                -1,
                function(dataKySo) {
                    if(dataKySo.length > 0) {
                        getCMUFileSigned769GetLink(dataKySo[0].KEYMINIO, 'pdf').then(pdfData => {
                            resolve({
                                url: pdfData,
                                kySo: 1,
                                isError: 0,
                                message: 'Thành công',
                                idKySo: dataKySo[0].ID
                            });
                        }).catch(e => {
                            resolve({
                                url: '',
                                kySo: 0,
                                isError: 1,
                                message: 'Lỗi get file ký số'
                            });
                        });
                    } else {
                        var params2 = {
                            magiay: params.ID,
                            mabenhnhan: thongtinhsba.thongtinbn.MABENHNHAN,
                            tennguoibenh: thongtinhsba.thongtinbn.TEN_BENH_NHAN,
                            sobenhan: thongtinhsba.thongtinbn.SOBENHAN,
                            sovaovien: thongtinhsba.thongtinbn.SOVAOVIEN,
                            gioitinh: thongtinhsba.thongtinbn.GIOI_TINH,
                            tuoi: thongtinhsba.thongtinbn.TUOI,
                            khoa: thongtinhsba.thongtinbn.TEN_PHONGBAN,
                            stt_benhan: thongtinhsba.thongtinbn.STT_BENHAN,
                            stt_dotdieutri: thongtinhsba.thongtinbn.STT_DOTDIEUTRI,
                        }
                        var url = 'cmu_in_cmu_khamchidinhphcn?type=pdf&' + $.param(params2);
                        resolve({
                            url: url,
                            kySo: 0,
                            isError: 0,
                            message: 'Thành công'
                        });
                    }
                }
            )
        } catch (e) {
            console.log("Lỗi get file ký số phiếu khám và chỉ định phục hồi chức năng: " + e)
            reject({
                url: '',
                kySo: 0,
                isError: 1,
                message: 'Lỗi get file ký số phiếu khám và chỉ định phục hồi chức năng: ' + e
            });
        }
    });
}

function getUrlNhanDinhBanDau(params) {
    return new Promise(async (resolve, reject) => {
        try {
            getFilesign769(
                "PHIEU_NOITRU_NHANDINHBANDAU",
                params.ID,
                -1,
                singletonObject.dvtt,
                thongtinhsba.thongtinbn.SOVAOVIEN,
                thongtinhsba.thongtinbn.SOVAOVIEN_DT,
                -1,
                function(dataKySo) {
                    if(dataKySo.length > 0) {
                        getCMUFileSigned769GetLink(dataKySo[0].KEYMINIO, 'pdf').then(pdfData => {
                            resolve({
                                url: pdfData,
                                kySo: 1,
                                isError: 0,
                                message: 'Thành công',
                                idKySo: dataKySo[0].ID
                            });
                        }).catch(e => {
                            resolve({
                                url: '',
                                kySo: 0,
                                isError: 1,
                                message: 'Lỗi get file ký số'
                            });
                        });
                    } else {
                        var params2 = {
                            magiay: params.ID,
                            mabenhnhan: thongtinhsba.thongtinbn.MABENHNHAN,
                            khoa: thongtinhsba.thongtinbn.TEN_PHONGBAN,
                            diachi: thongtinhsba.thongtinbn.DIA_CHI,
                            sdt: thongtinhsba.thongtinbn.SO_DIEN_THOAI,
                            tenbenhnhan: thongtinhsba.thongtinbn.TEN_BENH_NHAN,
                            chandoan: thongtinhsba.thongtinbn.TENBENHCHINH_NHAPVIEN,
                            sobenhan: thongtinhsba.thongtinbn.SOBENHAN,
                            sovaovien: thongtinhsba.thongtinbn.SOVAOVIEN,
                            gioitinh: thongtinhsba.thongtinbn.GIOI_TINH,
                            namsinh: thongtinhsba.thongtinbn.NGAY_SINH.split("/")[2],
                            tuoi: thongtinhsba.thongtinbn.TUOI,
                            stt_benhan: thongtinhsba.thongtinbn.STT_BENHAN,
                            stt_dotdieutri: thongtinhsba.thongtinbn.STT_DOTDIEUTRI,
                        }
                        var url = 'cmu_in_cmu_nhandinhbandau?type=pdf&' + $.param(params2);
                        resolve({
                            url: url,
                            kySo: 0,
                            isError: 0,
                            message: 'Thành công'
                        });
                    }
                }
            )
        } catch (e) {
            console.log("Lỗi get file ký số phiếu nhận định ban đầu vào viện tại khoa nội trú: " + e)
            reject({
                url: '',
                kySo: 0,
                isError: 1,
                message: 'Lỗi get file ký số phiếu nhận định ban đầu vào viện tại khoa nội trú: ' + e
            });
        }
    });
}

function getUrlPhieuPCA(params) {
    return new Promise(async (resolve, reject) => {
        try {
            getFilesign769(
                "PHIEU_NOITRU_PHIEUPCA",
                params.ID,
                -1,
                singletonObject.dvtt,
                thongtinhsba.thongtinbn.SOVAOVIEN,
                thongtinhsba.thongtinbn.SOVAOVIEN_DT,
                -1,
                function(dataKySo) {
                    if(dataKySo.length > 0) {
                        getCMUFileSigned769GetLink(dataKySo[0].KEYMINIO, 'pdf').then(pdfData => {
                            resolve({
                                url: pdfData,
                                kySo: 1,
                                isError: 0,
                                message: 'Thành công',
                                idKySo: dataKySo[0].ID
                            });
                        }).catch(e => {
                            resolve({
                                url: '',
                                kySo: 0,
                                isError: 1,
                                message: 'Lỗi get file ký số'
                            });
                        });
                    } else {
                        var text_date_part = params.NGAY_TAO_PHIEU.split("/");
                        var text_date = text_date_part[0];
                        var text_month = text_date_part[1];
                        var text_year = text_date_part[2];
                        var params2 = {
                            magiay: params.ID,
                            mabenhnhan: thongtinhsba.thongtinbn.MABENHNHAN,
                            tenbenhnhan: thongtinhsba.thongtinbn.TEN_BENH_NHAN,
                            namsinhbn: thongtinhsba.thongtinbn.NGAY_SINH.split("/")[2],
                            ngaytao: "Ngày " + text_date + " tháng " + text_month + " năm " + text_year
                        }
                        var url = 'cmu_in_cmu_phieu_PCA?type=pdf&' + $.param(params2);
                        resolve({
                            url: url,
                            kySo: 0,
                            isError: 0,
                            message: 'Thành công'
                        });
                    }
                }
            )
        } catch (e) {
            console.log("Lỗi get file ký số phiếu PCA: " + e)
            reject({
                url: '',
                kySo: 0,
                isError: 1,
                message: 'Lỗi get file ký số phiếu PCA: ' + e
            });
        }
    });
}

function getUrlKhamChuyenKhoa(params) {
    return new Promise(async (resolve, reject) => {
        try {
            getFilesign769(
                "PHIEU_NOITRU_KHAMCHUYENKHOA",
                params.ID,
                -1,
                singletonObject.dvtt,
                thongtinhsba.thongtinbn.SOVAOVIEN,
                thongtinhsba.thongtinbn.SOVAOVIEN_DT,
                -1,
                function(dataKySo) {
                    if(dataKySo.length > 0) {
                        getCMUFileSigned769GetLink(dataKySo[0].KEYMINIO, 'pdf').then(pdfData => {
                            resolve({
                                url: pdfData,
                                kySo: 1,
                                isError: 0,
                                message: 'Thành công',
                                idKySo: dataKySo[0].ID
                            });
                        }).catch(e => {
                            resolve({
                                url: '',
                                kySo: 0,
                                isError: 1,
                                message: 'Lỗi get file ký số'
                            });
                        });
                    } else {
                        var params2 = {
                            sobenhan: thongtinhsba.thongtinbn.SOBENHAN,
                            maphieu: params.ID,
                            mabenhnhan: thongtinhsba.thongtinbn.MABENHNHAN
                        }
                        var url = 'cmu_in_phieu_kham_chuyen_khoa?type=pdf&' + $.param(params2);
                        resolve({
                            url: url,
                            kySo: 0,
                            isError: 0,
                            message: 'Thành công'
                        });
                    }
                }
            )
        } catch (e) {
            console.log("Lỗi get file ký số phiếu khám chuyên khoa: " + e)
            reject({
                url: '',
                kySo: 0,
                isError: 1,
                message: 'Lỗi get file ký số phiếu khám chuyên khoa: ' + e
            });
        }
    });
}

function getUrlGiayCDSuDung(params) {
    return new Promise(async (resolve, reject) => {
        try {
            var arrTemp = [];
            getFilesign769("PHIEU_NOITRU_GIAYCAMDOANSUDUNG_BENHNHAN", params.ID, -1, singletonObject.dvtt,
                thongtinhsba.thongtinbn.SOVAOVIEN, thongtinhsba.thongtinbn.SOVAOVIEN_DT, -1, function(dataKySo) {
                    if (dataKySo.length > 0) {
                        arrTemp.push(dataKySo[0])
                    }
                });
            getFilesign769("PHIEU_NOITRU_GIAYCAMDOANSUDUNG", params.ID, -1, singletonObject.dvtt,
                thongtinhsba.thongtinbn.SOVAOVIEN, thongtinhsba.thongtinbn.SOVAOVIEN_DT, -1, function(dataKySo) {
                    if (dataKySo.length > 0) {
                        arrTemp.push(dataKySo[0])
                    }
                });
            var maxCreateDate = null;
            var maxCreateDataObject = null;
            if (arrTemp.length > 0) {
                $.each(arrTemp, function (index, dataObject) {
                    var createDate = moment(dataObject.CREATE_DATE_STRING, "DD/MM/YYYY HH:mm:ss");
                    if (maxCreateDate === null || createDate.isAfter(maxCreateDate)) {
                        maxCreateDate = createDate;
                        maxCreateDataObject = dataObject;
                    }
                });
                getCMUFileSigned769GetLink(maxCreateDataObject.KEYMINIO, 'pdf').then(pdfData => {
                    resolve({
                        url: pdfData,
                        kySo: 1,
                        isError: 0,
                        message: 'Thành công',
                        idKySo: maxCreateDataObject.ID
                    });
                }).catch(e => {
                    console.log("Lỗi get file ký số phiếu cam đoan (Sử dụng cho bệnh nhân dưới 06 tuổi miễn phí, viện phí và người bệnh có thẻ BHYT): " + e)
                    reject({
                        url: '',
                        kySo: 0,
                        isError: 1,
                        message: "Lỗi get file ký số phiếu cam đoan (Sử dụng cho bệnh nhân dưới 06 tuổi miễn phí, viện phí và người bệnh có thẻ BHYT): " + e
                    });
                });
            } else {
                var params2 = {
                    magiay: params.ID,
                    mabenhnhan: thongtinhsba.thongtinbn.MABENHNHAN,
                    tenbenhnhan: thongtinhsba.thongtinbn.TEN_BENH_NHAN,
                    sobenhan: thongtinhsba.thongtinbn.SOBENHAN,
                    sovaovien: thongtinhsba.thongtinbn.SOVAOVIEN,
                    gioitinh: thongtinhsba.thongtinbn.GIOI_TINH,
                    diachi: thongtinhsba.thongtinbn.DIA_CHI,
                    mabhyt: thongtinhsba.thongtinbn.SOBAOHIEMYTE,
                    tuoi: thongtinhsba.thongtinbn.TUOI,
                    khoa: thongtinhsba.thongtinbn.TEN_PHONGBAN,
                    giaychungsinh: thongtinhsba.thongtinbn.TE_GIAYCHUNGSINH ?? "",
                    giaykhaisinh: thongtinhsba.thongtinbn.TE_GIAYKHAISINH ?? "",
                    stt_benhan: thongtinhsba.thongtinbn.STT_BENHAN,
                    stt_dotdieutri: thongtinhsba.thongtinbn.STT_DOTDIEUTRI,
                }
                var url = 'cmu_in_cmu_giaycamdoan_sudung?type=pdf&' + $.param(params2);
                resolve({
                    url: url,
                    kySo: 0,
                    isError: 0,
                    message: 'Thành công'
                });
            }
        } catch (e) {
            console.log("Lỗi get file ký số phiếu cam đoan (Sử dụng cho bệnh nhân dưới 06 tuổi miễn phí, viện phí và người bệnh có thẻ BHYT): " + e)
            reject({
                url: '',
                kySo: 0,
                isError: 1,
                message: 'Lỗi get file ký số phiếu cam đoan (Sử dụng cho bệnh nhân dưới 06 tuổi miễn phí, viện phí và người bệnh có thẻ BHYT): ' + e
            });
        }
    });
}

function getUrlTienSuDiUng(params) {
    return new Promise(async (resolve, reject) => {
        try {
            getFilesign769(
                "PHIEU_NOITRU_TIENSUDIUNG",
                params.ID,
                -1,
                singletonObject.dvtt,
                thongtinhsba.thongtinbn.SOVAOVIEN,
                thongtinhsba.thongtinbn.SOVAOVIEN_DT,
                -1,
                function(dataKySo) {
                    if(dataKySo.length > 0) {
                        getCMUFileSigned769GetLink(dataKySo[0].KEYMINIO, 'pdf').then(pdfData => {
                            resolve({
                                url: pdfData,
                                kySo: 1,
                                isError: 0,
                                message: 'Thành công',
                                idKySo: dataKySo[0].ID
                            });
                        }).catch(e => {
                            resolve({
                                url: '',
                                kySo: 0,
                                isError: 1,
                                message: 'Lỗi get file ký số'
                            });
                        });
                    } else {
                        var text_date_part = params.NGAY_TAO_PHIEU.split("/");
                        var text_date = text_date_part[0];
                        var text_month = text_date_part[1];
                        var text_year = text_date_part[2];
                        var tenkhoa = "";
                        singletonObject.danhsachphongban.forEach(function(obj) {
                            if(obj.MAKHOA == singletonObject.makhoa) {
                                tenkhoa = obj.TENKHOA;
                            }
                        })
                        var arr = [
                            params.ID,
                            singletonObject.dvtt,
                            tenkhoa,
                            params.CHANDOAN,
                            params.NGAYVAOVIEN,
                            thongtinhsba.thongtinbn.SOVAOVIEN,
                            thongtinhsba.thongtinbn.STT_BENHAN,
                            text_date,
                            text_month,
                            text_year
                        ];
                        var param2 = ['magiay', 'dvtt', 'khoa', 'chan_doan', 'ngaygio_vaovien', 'sovaovien', 'stt_benhan', 'text_date', 'text_month', 'text_year'];
                        var url = "cmu_injasper?url=" + convertArray(arr)+"&param="+ convertArray(param2)+"&loaifile=pdf&jasper=cmu_tiensudiung";
                        resolve({
                            url: url,
                            kySo: 0,
                            isError: 0,
                            message: 'Thành công'
                        });
                    }
                }
            )
        } catch (e) {
            console.log("Lỗi get file ký số phiếu khai thác tiền sử dị ứng: " + e)
            reject({
                url: '',
                kySo: 0,
                isError: 1,
                message: 'Lỗi get file ký số phiếu khai thác tiền sử dị ứng: ' + e
            });
        }
    });
}

function getUrlTDTienSanGiat(params) {
    return new Promise(async (resolve, reject) => {
        try {
            getFilesign769(
                "PHIEU_NOITRU_THEODOITIENSANGIAT",
                params.ID,
                -1,
                singletonObject.dvtt,
                thongtinhsba.thongtinbn.SOVAOVIEN,
                thongtinhsba.thongtinbn.SOVAOVIEN_DT,
                -1,
                function(dataKySo) {
                    if(dataKySo.length > 0) {
                        getCMUFileSigned769GetLink(dataKySo[0].KEYMINIO, 'pdf').then(pdfData => {
                            resolve({
                                url: pdfData,
                                kySo: 1,
                                isError: 0,
                                message: 'Thành công',
                                idKySo: dataKySo[0].ID
                            });
                        }).catch(e => {
                            resolve({
                                url: '',
                                kySo: 0,
                                isError: 1,
                                message: 'Lỗi get file ký số'
                            });
                        });
                    } else {
                        var tenkhoa = "";
                        singletonObject.danhsachphongban.forEach(function(obj) {
                            if(obj.MAKHOA == singletonObject.makhoa) {
                                tenkhoa = obj.TENKHOA;
                            }
                        })
                        var arr = [
                            thongtinhsba.thongtinbn.TEN_BENH_NHAN,
                            singletonObject.dvtt,
                            tenkhoa,
                            "",
                            thongtinhsba.thongtinbn.SOVAOVIEN,
                            thongtinhsba.thongtinbn.SOBENHAN,
                            params.ID
                        ];

                        var param = ['hovaten',  'dvtt', 'ten_phongban', 'para', 'sovaovien', 'sobenhan', 'id'];
                        var url = "cmu_injasper?url=" + convertArray(arr)+"&param="+ convertArray(param)+"&loaifile=pdf&jasper=cmu_bangtheodoitiensangiat";
                        resolve({
                            url: url,
                            kySo: 0,
                            isError: 0,
                            message: 'Thành công'
                        });
                    }
                }
            )
        } catch (e) {
            console.log("Lỗi get file ký số phiếu tiền sản giật: " + e)
            reject({
                url: '',
                kySo: 0,
                isError: 1,
                message: 'Lỗi get file ký số phiếu tiền sản giật: ' + e
            });
        }
    });
}

function getUrlTD6hSauDe(params) {
    return new Promise(async (resolve, reject) => {
        try {
            getFilesign769(
                "PHIEU_NOITRU_THEODOI6HSAUDE",
                params.ID,
                -1,
                singletonObject.dvtt,
                thongtinhsba.thongtinbn.SOVAOVIEN,
                thongtinhsba.thongtinbn.SOVAOVIEN_DT,
                -1,
                function(dataKySo) {
                    if(dataKySo.length > 0) {
                        getCMUFileSigned769GetLink(dataKySo[0].KEYMINIO, 'pdf').then(pdfData => {
                            resolve({
                                url: pdfData,
                                kySo: 1,
                                isError: 0,
                                message: 'Thành công',
                                idKySo: dataKySo[0].ID
                            });
                        }).catch(e => {
                            resolve({
                                url: '',
                                kySo: 0,
                                isError: 1,
                                message: 'Lỗi get file ký số'
                            });
                        });
                    } else {
                        var tenkhoa = "";
                        singletonObject.danhsachphongban.forEach(function(obj) {
                            if(obj.MAKHOA == singletonObject.makhoa) {
                                tenkhoa = obj.TENKHOA;
                            }
                        })
                        var arr = [
                            thongtinhsba.thongtinbn.TEN_BENH_NHAN,
                            singletonObject.dvtt,
                            tenkhoa,
                            thongtinhsba.thongtinbn.SOVAOVIEN,
                            thongtinhsba.thongtinbn.SOBENHAN,
                            thongtinhsba.thongtinbn.ICD_HT,
                            params.ID,
                        ];
                        var param = ['hovaten',  'dvtt', 'khoa', 'sovaovien', 'sobenhan', 'chandoan', 'id'];
                        var url = "cmu_injasper?url=" + convertArray(arr)+"&param="+ convertArray(param)+"&loaifile=pdf&jasper=cmu_bangtheodoi6hsaude";
                        resolve({
                            url: url,
                            kySo: 0,
                            isError: 0,
                            message: 'Thành công'
                        });
                    }
                }
            )
        } catch (e) {
            console.log("Lỗi get file ký số phiếu theo dõi mẹ 6 giờ sau đẻ: " + e)
            reject({
                url: '',
                kySo: 0,
                isError: 1,
                message: 'Lỗi get file ký số phiếu theo dõi mẹ 6 giờ sau đẻ: ' + e
            });
        }
    });
}

function getUrlTheoDoiGiucSanh(params) {
    return new Promise(async (resolve, reject) => {
        try {
            getFilesign769(
                "PHIEU_NOITRU_THEODOIGIUCSANH",
                params.ID,
                -1,
                singletonObject.dvtt,
                thongtinhsba.thongtinbn.SOVAOVIEN,
                thongtinhsba.thongtinbn.SOVAOVIEN_DT,
                -1,
                function(dataKySo) {
                    if(dataKySo.length > 0) {
                        getCMUFileSigned769GetLink(dataKySo[0].KEYMINIO, 'pdf').then(pdfData => {
                            resolve({
                                url: pdfData,
                                kySo: 1,
                                isError: 0,
                                message: 'Thành công',
                                idKySo: dataKySo[0].ID
                            });
                        }).catch(e => {
                            resolve({
                                url: '',
                                kySo: 0,
                                isError: 1,
                                message: 'Lỗi get file ký số'
                            });
                        });
                    } else {
                        var tenkhoa = "";
                        singletonObject.danhsachphongban.forEach(function(obj) {
                            if(obj.MAKHOA == singletonObject.makhoa) {
                                tenkhoa = obj.TENKHOA;
                            }
                        })
                        var arr = [
                            thongtinhsba.thongtinbn.TEN_BENH_NHAN,
                            singletonObject.dvtt,
                            tenkhoa,
                            "",
                            thongtinhsba.thongtinbn.SOVAOVIEN,
                            thongtinhsba.thongtinbn.SOBENHAN,
                            params.ID
                        ];

                        var param = ['hovaten',  'dvtt', 'ten_phongban', 'paraa', 'sovaovien', 'sobenhan','id'];
                        var url = "cmu_injasper?url=" + convertArray(arr)+"&param="+ convertArray(param)+"&loaifile=pdf&jasper=cmu_bangtheodoigiucsanh";
                        resolve({
                            url: url,
                            kySo: 0,
                            isError: 0,
                            message: 'Thành công'
                        });
                    }
                }
            )
        } catch (e) {
            console.log("Lỗi get file ký số phiếu theo dõi giục sanh: " + e)
            reject({
                url: '',
                kySo: 0,
                isError: 1,
                message: 'Lỗi get file ký số phiếu theo dõi giục sanh: ' + e
            });
        }
    });
}

function getUrlDongYXNHIVGhiTen(params) {
    return new Promise(async (resolve, reject) => {
        try {
            var arrTemp = [];
            getFilesign769("PHIEU_NOITRU_DONGYXETNGHIEMHIV_BENHNHAN", params.ID, -1, singletonObject.dvtt,
                thongtinhsba.thongtinbn.SOVAOVIEN, thongtinhsba.thongtinbn.SOVAOVIEN_DT, -1, function(dataKySo) {
                    if (dataKySo.length > 0) {
                        arrTemp.push(dataKySo[0])
                    }
                });
            getFilesign769("PHIEU_NOITRU_DONGYXETNGHIEMHIV", params.ID, -1, singletonObject.dvtt,
                thongtinhsba.thongtinbn.SOVAOVIEN, thongtinhsba.thongtinbn.SOVAOVIEN_DT, -1, function(dataKySo) {
                    if (dataKySo.length > 0) {
                        arrTemp.push(dataKySo[0])
                    }
                });
            var maxCreateDate = null;
            var maxCreateDataObject = null;
            if (arrTemp.length > 0) {
                $.each(arrTemp, function (index, dataObject) {
                    var createDate = moment(dataObject.CREATE_DATE_STRING, "DD/MM/YYYY HH:mm:ss");
                    if (maxCreateDate === null || createDate.isAfter(maxCreateDate)) {
                        maxCreateDate = createDate;
                        maxCreateDataObject = dataObject;
                    }
                });
                getCMUFileSigned769GetLink(maxCreateDataObject.KEYMINIO, 'pdf').then(pdfData => {
                    resolve({
                        url: pdfData,
                        kySo: 1,
                        isError: 0,
                        message: 'Thành công',
                        idKySo: maxCreateDataObject.ID
                    });
                }).catch(e => {
                    console.log("Lỗi get file ký số phiếu đồng ý xét nghiệm HIV ghi tên: " + e)
                    reject({
                        url: '',
                        kySo: 0,
                        isError: 1,
                        message: "Lỗi get file ký số phiếu đồng ý xét nghiệm HIV ghi tên: " + e
                    });
                });
            } else {
                var params2 = {
                    magiay: params.ID,
                    mabenhnhan: thongtinhsba.thongtinbn.MABENHNHAN,
                    khoa: thongtinhsba.thongtinbn.TEN_PHONGBAN,
                    diachi: thongtinhsba.thongtinbn.DIA_CHI,
                    sdt: thongtinhsba.thongtinbn.SO_DIEN_THOAI,
                    tenbenhnhan: thongtinhsba.thongtinbn.TEN_BENH_NHAN,
                    sobenhan: thongtinhsba.thongtinbn.SOBENHAN,
                    sovaovien: thongtinhsba.thongtinbn.SOVAOVIEN,
                    gioitinh: thongtinhsba.thongtinbn.GIOI_TINH_HT,
                    namsinh: thongtinhsba.thongtinbn.NGAY_SINH.split("/")[2],
                    tuoi: thongtinhsba.thongtinbn.TUOI,
                    stt_benhan: thongtinhsba.thongtinbn.STT_BENHAN,
                    stt_dotdieutri: thongtinhsba.thongtinbn.STT_DOTDIEUTRI,
                }
                var url = 'cmu_in_cmu_phieudongy_xetnghiemhiv?type=pdf&' + $.param(params2);
                resolve({
                    url: url,
                    kySo: 0,
                    isError: 0,
                    message: 'Thành công'
                });
            }
        } catch (e) {
            console.log("Lỗi get file ký số phiếu đồng ý xét nghiệm HIV ghi tên: " + e)
            reject({
                url: '',
                kySo: 0,
                isError: 1,
                message: 'Lỗi get file ký số phiếu đồng ý xét nghiệm HIV ghi tên: ' + e
            });
        }
    });
}

function getUrlDangKyKBTheoYC(params) {
    return new Promise(async (resolve, reject) => {
        try {
            getFilesign769(
                "PHIEU_NOITRU_DANGKYKBTHEOYC",
                params.ID,
                -1,
                singletonObject.dvtt,
                thongtinhsba.thongtinbn.SOVAOVIEN,
                thongtinhsba.thongtinbn.SOVAOVIEN_DT,
                -1,
                function(dataKySo) {
                    if(dataKySo.length > 0) {
                        getCMUFileSigned769GetLink(dataKySo[0].KEYMINIO, 'pdf').then(pdfData => {
                            resolve({
                                url: pdfData,
                                kySo: 1,
                                isError: 0,
                                message: 'Thành công',
                                idKySo: dataKySo[0].ID
                            });
                        }).catch(e => {
                            resolve({
                                url: '',
                                kySo: 0,
                                isError: 1,
                                message: 'Lỗi get file ký số'
                            });
                        });
                    } else {
                        var params2 = {
                            magiay: params.ID,
                            mabenhnhan: thongtinhsba.thongtinbn.MABENHNHAN,
                            tenbenhnhan: thongtinhsba.thongtinbn.TEN_BENH_NHAN,
                            sobenhan: thongtinhsba.thongtinbn.SOBENHAN,
                            sovaovien: thongtinhsba.thongtinbn.SOVAOVIEN,
                            gioitinh: thongtinhsba.thongtinbn.GIOI_TINH,
                            diachi: thongtinhsba.thongtinbn.DIA_CHI,
                            mabhyt: thongtinhsba.thongtinbn.SOBAOHIEMYTE,
                            tuoi: thongtinhsba.thongtinbn.TUOI,
                            khoa: thongtinhsba.thongtinbn.TEN_PHONGBAN ?? "...",
                            giaychungsinh: thongtinhsba.thongtinbn.TE_GIAYCHUNGSINH ?? "...",
                            giaykhaisinh: thongtinhsba.thongtinbn.TE_GIAYKHAISINH ?? "...",
                            stt_benhan: thongtinhsba.thongtinbn.STT_BENHAN,
                            stt_dotdieutri: thongtinhsba.thongtinbn.STT_DOTDIEUTRI,
                        }
                        var url = 'cmu_in_cmu_phieudangky_khambenhtheoyc?type=pdf&' + $.param(params2);
                        resolve({
                            url: url,
                            kySo: 0,
                            isError: 0,
                            message: 'Thành công'
                        });
                    }
                }
            )
        } catch (e) {
            console.log("Lỗi get file ký số phiếu đăng ký khám chữa bệnh theo yêu cầu: " + e)
            reject({
                url: '',
                kySo: 0,
                isError: 1,
                message: 'Lỗi get file ký số phiếu đăng ký khám chữa bệnh theo yêu cầu: ' + e
            });
        }
    });
}

function getUrlKeHoachChamSoc(params) {
    return new Promise(async (resolve, reject) => {
        try {
            getFilesign769(
                "PHIEU_NOITRU_KEHOACHCHAMSOC",
                params.ID,
                -1,
                singletonObject.dvtt,
                thongtinhsba.thongtinbn.SOVAOVIEN,
                thongtinhsba.thongtinbn.SOVAOVIEN_DT,
                -1,
                function(dataKySo) {
                    if(dataKySo.length > 0) {
                        getCMUFileSigned769GetLink(dataKySo[0].KEYMINIO, 'pdf').then(pdfData => {
                            resolve({
                                url: pdfData,
                                kySo: 1,
                                isError: 0,
                                message: 'Thành công',
                                idKySo: dataKySo[0].ID
                            });
                        }).catch(e => {
                            resolve({
                                url: '',
                                kySo: 0,
                                isError: 1,
                                message: 'Lỗi get file ký số'
                            });
                        });
                    } else {
                        var url = 'cmu_in_cmu_kehoachchamsoc?type=pdf&' + $.param({
                            dvtt: singletonObject.dvtt,
                            id: params.ID,
                            sovaovien: thongtinhsba.thongtinbn.SOVAOVIEN,
                        });
                        resolve({
                            url: url,
                            kySo: 0,
                            isError: 0,
                            message: 'Thành công'
                        });
                    }
                }
            )
        } catch (e) {
            console.log("Lỗi get file ký số phiếu đăng ký khám chữa bệnh theo yêu cầu: " + e)
            reject({
                url: '',
                kySo: 0,
                isError: 1,
                message: 'Lỗi get file ký số phiếu đăng ký khám chữa bệnh theo yêu cầu: ' + e
            });
        }
    });
}

function getUrlTruyenDich(params) {
    return new Promise(async (resolve, reject) => {
        try {
            getFilesign769(
                "PHIEU_NOITRU_TRUYENDICH",
                '-1',
                -1,
                singletonObject.dvtt,
                thongtinhsba.thongtinbn.SOVAOVIEN,
                thongtinhsba.thongtinbn.SOVAOVIEN_DT,
                -1,
                function(dataKySo) {
                    if(dataKySo.length > 0) {
                        getCMUFileSigned769GetLink(dataKySo[0].KEYMINIO, 'pdf').then(pdfData => {
                            resolve({
                                url: pdfData,
                                kySo: 1,
                                isError: 0,
                                message: 'Thành công',
                                idKySo: dataKySo[0].ID
                            });
                        }).catch(e => {
                            resolve({
                                url: '',
                                kySo: 0,
                                isError: 1,
                                message: 'Lỗi get file ký số'
                            });
                        });
                    } else {
                        var arr = [singletonObject.dvtt, thongtinhsba.thongtinbn.SOVAOVIEN, thongtinhsba.thongtinbn.STT_BENHAN, '-1', '-1', params.loaingay, thongtinhsba.thongtinbn.SOBENHAN];
                        var param2  = ["dvtt", "sovaovien", "stt_benhan", "id_truyendich", "id_dottruyen", "loaingay", "sobenhan"];
                        var url = "cmu_report_rp_phieutruyendich_emr?url=" + convertArray(arr)+"&param="+ convertArray(param2)+"&loaifile=pdf";
                        resolve({
                            url: url,
                            kySo: 0,
                            isError: 0,
                            message: 'Thành công'
                        });
                    }
                }
            )
        } catch (e) {
            console.log("Lỗi get file ký số phiếu truyền dịch: " + e)
            reject({
                url: '',
                kySo: 0,
                isError: 1,
                message: 'Lỗi get file ký số phiếu truyền dịch: ' + e
            });
        }
    });
}

function getUrlChamSoc(params) {
    return new Promise(async (resolve, reject) => {
        try {
            getFilesign769(
                "PHIEU_NOITRU_PHIEUCHAMSOC",
                params.ID,
                -1,
                singletonObject.dvtt,
                thongtinhsba.thongtinbn.SOVAOVIEN,
                thongtinhsba.thongtinbn.SOVAOVIEN_DT,
                -1,
                function(dataKySo) {
                    if(dataKySo.length > 0) {
                        getCMUFileSigned769GetLink(dataKySo[0].KEYMINIO, 'pdf').then(pdfData => {
                            resolve({
                                url: pdfData,
                                kySo: 1,
                                isError: 0,
                                message: 'Thành công',
                                idKySo: dataKySo[0].ID
                            });
                        }).catch(e => {
                            resolve({
                                url: '',
                                kySo: 0,
                                isError: 1,
                                message: 'Lỗi get file ký số'
                            });
                        });
                    } else {
                        var arr = [
                            thongtinhsba.thongtinbn.TEN_BENH_NHAN,
                            params.TUOI == 0 ? params.THANG == 0 ? params.NGAY + " ngày" : params.THANG + " tháng" : params.TUOI,
                            singletonObject.dvtt,
                            params.TEN_PHONGBAN,
                            params.CHAN_DOAN,
                            params.GIOI_TINH_HT,
                            thongtinhsba.thongtinbn.SOVAOVIEN,
                            thongtinhsba.thongtinbn.SOBENHAN,
                            params.ID
                        ];
                        var param = ['hovaten', 'tuoi', 'dvtt', 'ten_phongban', 'chandoan', 'gioitinh', 'sovaovien', 'sobenhan', 'idpcs'];
                        var url = "cmu_injasper?url=" + convertArray(arr) + "&param=" + convertArray(param) + "&loaifile=pdf&jasper=rp_phieuchamsoc_npcs_v2";
                        resolve({
                            url: url,
                            kySo: 0,
                            isError: 0,
                            message: 'Thành công'
                        });
                    }
                }
            )
        } catch (e) {
            console.log("Lỗi get file ký số phiếu chăm sóc: " + e)
            reject({
                url: '',
                kySo: 0,
                isError: 1,
                message: 'Lỗi get file ký số phiếu chăm sóc: ' + e
            });
        }
    });
}

function getUrlXTVungVu(params) {
    return new Promise(async (resolve, reject) => {
        try {
            getFilesign769(
                "PHIEU_NOITRU_XATRI_3DVUNGVU",
                params.ID,
                -1,
                singletonObject.dvtt,
                thongtinhsba.thongtinbn.SOVAOVIEN,
                thongtinhsba.thongtinbn.SOVAOVIEN_DT,
                -1,
                function(dataKySo) {
                    if(dataKySo.length > 0) {
                        getCMUFileSigned769GetLink(dataKySo[0].KEYMINIO, 'pdf').then(pdfData => {
                            resolve({
                                url: pdfData,
                                kySo: 1,
                                isError: 0,
                                message: 'Thành công',
                                idKySo: dataKySo[0].ID
                            });
                        }).catch(e => {
                            resolve({
                                url: '',
                                kySo: 0,
                                isError: 1,
                                message: 'Lỗi get file ký số'
                            });
                        });
                    } else {
                        var params2 = {
                            dvtt: singletonObject.dvtt,
                            idxatri: params.ID,
                            sobenhan: thongtinhsba.thongtinbn.SOBENHAN
                        }
                        var url = 'cmu_in_emr_rp_xatri_3dvungvu?type=pdf&' + $.param(params2);
                        resolve({
                            url: url,
                            kySo: 0,
                            isError: 0,
                            message: 'Thành công'
                        });
                    }
                }
            )
        } catch (e) {
            console.log("Lỗi get file ký số phiếu xạ trị 3d vùng vú: " + e)
            reject({
                url: '',
                kySo: 0,
                isError: 1,
                message: 'Lỗi get file ký số phiếu xạ trị 3d vùng vú: ' + e
            });
        }
    });
}

function getUrlXTVungChau(params) {
    return new Promise(async (resolve, reject) => {
        try {
            getFilesign769(
                "PHIEU_NOITRU_XATRI_VUNGCHAU",
                params.ID,
                -1,
                singletonObject.dvtt,
                thongtinhsba.thongtinbn.SOVAOVIEN,
                thongtinhsba.thongtinbn.SOVAOVIEN_DT,
                -1,
                function(dataKySo) {
                    if(dataKySo.length > 0) {
                        getCMUFileSigned769GetLink(dataKySo[0].KEYMINIO, 'pdf').then(pdfData => {
                            resolve({
                                url: pdfData,
                                kySo: 1,
                                isError: 0,
                                message: 'Thành công',
                                idKySo: dataKySo[0].ID
                            });
                        }).catch(e => {
                            resolve({
                                url: '',
                                kySo: 0,
                                isError: 1,
                                message: 'Lỗi get file ký số'
                            });
                        });
                    } else {
                        var params2 = {
                            dvtt: singletonObject.dvtt,
                            idxatri: params.ID,
                            sobenhan: thongtinhsba.thongtinbn.SOBENHAN
                        }
                        var url = 'cmu_in_emr_rp_xatri_vungchau?type=pdf&' + $.param(params2);
                        resolve({
                            url: url,
                            kySo: 0,
                            isError: 0,
                            message: 'Thành công'
                        });
                    }
                }
            )
        } catch (e) {
            console.log("Lỗi get file ký số phiếu xạ trị vùng chậu: " + e)
            reject({
                url: '',
                kySo: 0,
                isError: 1,
                message: 'Lỗi get file ký số phiếu xạ trị vùng chậu: ' + e
            });
        }
    });
}

function getUrlXTVungDauCo(params) {
    return new Promise(async (resolve, reject) => {
        try {
            getFilesign769(
                "PHIEU_NOITRU_XATRI_VUNGDAUCO",
                params.ID,
                -1,
                singletonObject.dvtt,
                thongtinhsba.thongtinbn.SOVAOVIEN,
                thongtinhsba.thongtinbn.SOVAOVIEN_DT,
                -1,
                function(dataKySo) {
                    if(dataKySo.length > 0) {
                        getCMUFileSigned769GetLink(dataKySo[0].KEYMINIO, 'pdf').then(pdfData => {
                            resolve({
                                url: pdfData,
                                kySo: 1,
                                isError: 0,
                                message: 'Thành công',
                                idKySo: dataKySo[0].ID
                            });
                        }).catch(e => {
                            resolve({
                                url: '',
                                kySo: 0,
                                isError: 1,
                                message: 'Lỗi get file ký số'
                            });
                        });
                    } else {
                        var params2 = {
                            dvtt: singletonObject.dvtt,
                            idxatri: params.ID,
                            sobenhan: thongtinhsba.thongtinbn.SOBENHAN
                        }
                        var url = 'cmu_in_emr_rp_xatri_vungdauco?type=pdf&' + $.param(params2);
                        resolve({
                            url: url,
                            kySo: 0,
                            isError: 0,
                            message: 'Thành công'
                        });
                    }
                }
            )
        } catch (e) {
            console.log("Lỗi get file ký số phiếu xạ trị vùng đầu cổ: " + e)
            reject({
                url: '',
                kySo: 0,
                isError: 1,
                message: 'Lỗi get file ký số phiếu xạ trị vùng đầu cổ: ' + e
            });
        }
    });
}

function getUrlXTTheoDoiDT(params) {
    return new Promise(async (resolve, reject) => {
        try {
            getFilesign769(
                "PHIEU_NOITRU_XATRI_THEODOIDIENTIEN",
                params.ID,
                -1,
                singletonObject.dvtt,
                thongtinhsba.thongtinbn.SOVAOVIEN,
                thongtinhsba.thongtinbn.SOVAOVIEN_DT,
                -1,
                function(dataKySo) {
                    if(dataKySo.length > 0) {
                        getCMUFileSigned769GetLink(dataKySo[0].KEYMINIO, 'pdf').then(pdfData => {
                            resolve({
                                url: pdfData,
                                kySo: 1,
                                isError: 0,
                                message: 'Thành công',
                                idKySo: dataKySo[0].ID
                            });
                        }).catch(e => {
                            resolve({
                                url: '',
                                kySo: 0,
                                isError: 1,
                                message: 'Lỗi get file ký số'
                            });
                        });
                    } else {
                        var params2 = {
                            dvtt: singletonObject.dvtt,
                            idxatri: params.ID,
                            sobenhan: thongtinhsba.thongtinbn.SOBENHAN
                        }
                        var url = 'cmu_in_emr_rp_xatri_phieutheodoidientien?type=pdf&' + $.param(params2);
                        resolve({
                            url: url,
                            kySo: 0,
                            isError: 0,
                            message: 'Thành công'
                        });
                    }
                }
            )
        } catch (e) {
            console.log("Lỗi get file ký số phiếu xạ trị theo dõi diễn tiến: " + e)
            reject({
                url: '',
                kySo: 0,
                isError: 1,
                message: 'Lỗi get file ký số phiếu xạ trị theo dõi diễn tiến: ' + e
            });
        }
    });
}

function getUrlXaTriTong(params) {
    return new Promise(async (resolve, reject) => {
        try {
            getFilesign769(
                "PHIEU_NOITRU_XATRI_TONG",
                params.ID,
                -1,
                singletonObject.dvtt,
                thongtinhsba.thongtinbn.SOVAOVIEN,
                thongtinhsba.thongtinbn.SOVAOVIEN_DT,
                -1,
                function(dataKySo) {
                    if(dataKySo.length > 0) {
                        getCMUFileSigned769GetLink(dataKySo[0].KEYMINIO, 'pdf').then(pdfData => {
                            resolve({
                                url: pdfData,
                                kySo: 1,
                                isError: 0,
                                message: 'Thành công',
                                idKySo: dataKySo[0].ID
                            });
                        }).catch(e => {
                            resolve({
                                url: '',
                                kySo: 0,
                                isError: 1,
                                message: 'Lỗi get file ký số'
                            });
                        });
                    } else {
                        var arrErrors = [
                            "- Chưa cập nhật dữ liệu thông tin xạ trị, Vui lòng cập nhật thông tin xạ trị",
                            "- Chưa cập nhật dữ liệu hình mô phỏng",
                            "- Chưa cập nhật dữ liệu thông số kỹ thuật, Vui lòng cập nhật thông số kỹ thuật",
                            "- Chưa cập nhật dữ liệu theo dõi diễn tiến, vui lòng cập nhật theo dõi diễn tiến",
                            "- Chưa cập nhật dữ liệu tổng kết, vui lòng cập nhật tổng kết",
                            // "Còn dữ liệu bảng kiểm gạc, Vui lòng xóa dữ liệu bảng kiểm gạc",
                            // "Còn dữ liệu phiếu tiền mê, Vui lòng xóa dữ liệu phiếu tiền mê",
                            // "Còn dữ liệu vật tư, Vui lòng xóa dữ liệu vật tư",
                            // "Còn dữ liệu dịch vụ kỹ thuật, Vui lòng xóa dữ liệu dịch vụ kỹ thuật",
                            // "Còn dữ liệu ekip, Vui lòng xóa dữ liệu ekip",
                        ];
                        var arrRes = params.res.split(","),
                            arrMsg = [],
                            breakPagePhieuXaTri = 0,
                            breakPageHinhMoPhong = 0,
                            breakPageThongSoKyThuat = 0,
                            breakPageTheoDoiDienTien = 0;
                        arrRes.forEach(function(item, index, array) {
                            if(item == "0") {
                                if (index != 1){
                                    arrMsg.push(arrErrors[index]);
                                }
                            }
                            if (item == '1') {
                                for(var i = index + 1; i < arrRes.length; i++) {
                                    if(arrRes[i] == '1') {
                                        if (index == '0') breakPagePhieuXaTri = 1;
                                        if (index == '1') breakPageHinhMoPhong = 1;
                                        if (index == '2') breakPageThongSoKyThuat = 1;
                                        if (index == '3') breakPageTheoDoiDienTien = 1;
                                    }
                                }
                            }
                        });
                        // if (arrMsg.length > 0) {
                        //     if(idButton) {
                        //         hideSelfLoading(idButton);
                        //     }
                        //     return notifiToClient("Red", arrMsg.join("</br>"));
                        // }
                        // Dữ liệu thông số kỹ thuật
                        var res = $.ajax({
                            url:"cmu_getlist?url=" + convertArray([params.ID, "CMU_LXT_THONGSOKYTHUAT_SEL"]),
                            type:"GET",
                            async: false
                        }).responseText;
                        var dataDef = JSON.parse(res),
                            dataTheTichDieuTri = {},
                            dataTruongChieu = {},
                            dataNangLuong = {},
                            dataTuTheBenhNhan = {},
                            dataMatNa = {},
                            dataGoiDau = {},
                            dataVankeLung = {},
                            dataBac = {},
                            dataMpNghieng = {},
                            dataGiaDoTay = {},
                            dataKhac = {},
                            dataKyThuatSadSsd = {},
                            dataDoSau = {},
                            dataX1 = {},
                            dataX2 = {},
                            dataY1 = {},
                            dataY2 = {},
                            dataGantry = {},
                            dataColimator = {},
                            dataGocQuayBan = {},
                            dataX = {},
                            dataY = {},
                            dataZ = {},
                            dataWedge = {},
                            dataBolus = {},
                            dataMLC = {},
                            dataChiCaNhan = {},
                            dataTyLe = {},
                            dataSoMu = {};
                        if(dataDef.length > 0) {
                            for (var i = 0; i < dataDef.length; i++) {
                                dataTheTichDieuTri['ttdt_' + (i+1)] = dataDef[i].THE_TICH_DIEU_TRI;
                                dataTruongChieu['tc_' + (i+1)] = dataDef[i].TRUONG_CHIEU;
                                dataNangLuong['nl_' + (i+1)] = dataDef[i].NANG_LUONG;
                                dataTuTheBenhNhan['ttbn_' + (i+1)] = dataDef[i].TU_THE_BENH_NHAN;
                                dataMatNa['mn_' + (i+1)] = dataDef[i].MAT_NA;
                                dataGoiDau['gd_' + (i+1)] = dataDef[i].GOI_DAU;
                                dataVankeLung['vkl_' + (i+1)] = dataDef[i].VAN_KE_LUNG;
                                dataBac['bac_' + (i+1)] = dataDef[i].BAC;
                                dataMpNghieng['mpn_' + (i+1)] = dataDef[i].MP_NGHIENG;
                                dataGiaDoTay['gdt_' + (i+1)] = dataDef[i].GIA_DO_TAY;
                                dataKhac['khac_' + (i+1)] = dataDef[i].KHAC;
                                dataKyThuatSadSsd['ktssd_' + (i+1)] = dataDef[i].KY_THUAT_SAD_SSD;
                                dataDoSau['ds_' + (i+1)] = dataDef[i].DO_SAU;
                                dataX1['x1_' + (i+1)] = dataDef[i].KICH_THUOC_TRUONG_CHIEU_X1;
                                dataX2['x2_' + (i+1)] = dataDef[i].KICH_THUOC_TRUONG_CHIEU_X2;
                                dataY1['y1_' + (i+1)] = dataDef[i].KICH_THUOC_TRUONG_CHIEU_Y1;
                                dataY2['y2_' + (i+1)] = dataDef[i].KICH_THUOC_TRUONG_CHIEU_Y2;
                                dataGantry['gantry_' + (i+1)] = dataDef[i].GOC_QUAY_GANTRY;
                                dataColimator['colimator_' + (i+1)] = dataDef[i].GOC_QUAY_COLIMATOR;
                                dataGocQuayBan['gqb_' + (i+1)] = dataDef[i].GOC_QUAY_BAN;
                                dataX['x_' + (i+1)] = dataDef[i].TOA_DO_TAM_TRUONG_CHIEU_X;
                                dataY['y_' + (i+1)] = dataDef[i].TOA_DO_TAM_TRUONG_CHIEU_Y;
                                dataZ['z_' + (i+1)] = dataDef[i].TOA_DO_TAM_TRUONG_CHIEU_Z;
                                dataWedge['wedge_' + (i+1)] = dataDef[i].TAM_LOC_WEDGE;
                                dataBolus['bolus_' + (i+1)] = dataDef[i].BOLUS;
                                dataMLC['mlc_' + (i+1)] = dataDef[i].CHE_CHAN_MLC;
                                dataChiCaNhan['ccn_' + (i+1)] = dataDef[i].CHE_CHAN_CHI_CA_NHAN;
                                dataTyLe['tl_' + (i+1)] = dataDef[i].TY_LE;
                                dataSoMu['sm_' + (i+1)] = dataDef[i].SO_MU;
                            }
                        }
                        // END Dữ liệu thông số kỹ thuật
                        var params2 = {
                            dvtt: singletonObject.dvtt,
                            idxatri: params.ID,
                            sobenhan: thongtinhsba.thongtinbn.SOBENHAN,
                            ...dataTheTichDieuTri,
                            ...dataTruongChieu,
                            ...dataNangLuong,
                            ...dataTuTheBenhNhan,
                            ...dataMatNa,
                            ...dataGoiDau,
                            ...dataVankeLung,
                            ...dataBac,
                            ...dataMpNghieng,
                            ...dataGiaDoTay,
                            ...dataKhac,
                            ...dataKyThuatSadSsd,
                            ...dataDoSau,
                            ...dataX1,
                            ...dataX2,
                            ...dataY1,
                            ...dataY2,
                            ...dataGantry,
                            ...dataColimator,
                            ...dataGocQuayBan,
                            ...dataX,
                            ...dataY,
                            ...dataZ,
                            ...dataWedge,
                            ...dataBolus,
                            ...dataMLC,
                            ...dataChiCaNhan,
                            ...dataTyLe,
                            ...dataSoMu,
                            break1: breakPagePhieuXaTri,
                            break2: breakPageHinhMoPhong,
                            break3: breakPageThongSoKyThuat,
                            break4: breakPageTheoDoiDienTien
                        };
                        var url = 'cmu_in_emr_rp_xatri?type=pdf&' + $.param(params2);
                        resolve({
                            url: url,
                            kySo: 0,
                            isError: 0,
                            message: 'Thành công'
                        });
                    }
                }
            )
        } catch (e) {
            console.log("Lỗi get file ký số phiếu xạ trị: " + e)
            reject({
                url: '',
                kySo: 0,
                isError: 1,
                message: 'Lỗi get file ký số phiếu xạ trị: ' + e
            });
        }
    });
}

function getUrlYCSDKhangSinh(params) {
    return new Promise(async (resolve, reject) => {
        try {
            getFilesign769(
                "PHIEU_NOITRU_SUDUNGKHANGSINH",
                params.ID,
                -1,
                singletonObject.dvtt,
                thongtinhsba.thongtinbn.SOVAOVIEN,
                thongtinhsba.thongtinbn.SOVAOVIEN_DT,
                -1,
                function(dataKySo) {
                    if(dataKySo.length > 0) {
                        getCMUFileSigned769GetLink(dataKySo[0].KEYMINIO, 'pdf').then(pdfData => {
                            resolve({
                                url: pdfData,
                                kySo: 1,
                                isError: 0,
                                message: 'Thành công',
                                idKySo: dataKySo[0].ID
                            });
                        }).catch(e => {
                            resolve({
                                url: '',
                                kySo: 0,
                                isError: 1,
                                message: 'Lỗi get file ký số'
                            });
                        });
                    } else {
                        var params2 = {
                            magiay: params.ID,
                            mabenhnhan: thongtinhsba.thongtinbn.MABENHNHAN,
                            CANNANG: thongtinhsba.thongtinbn.CANNANG
                        }
                        var url = 'cmu_in_cmu_phieu_sd_khangsinh?type=pdf&' + $.param(params2);
                        resolve({
                            url: url,
                            kySo: 0,
                            isError: 0,
                            message: 'Thành công'
                        });
                    }
                }
            )
        } catch (e) {
            console.log("Lỗi get file ký số phiếu yêu cầu sử dụng kháng sinh: " + e)
            reject({
                url: '',
                kySo: 0,
                isError: 1,
                message: 'Lỗi get file ký số phiếu yêu cầu sử dụng kháng sinh: ' + e
            });
        }
    });
}

function getUrlPCSC2(params) {
    return new Promise(async (resolve, reject) => {
        try {
            getFilesign769(
                "PHIEU_NOITRU_CHAMSOCCAP2",
                params.ID_CHAM_SOC_CAP_2,
                -1,//singletonObject.userId,
                singletonObject.dvtt,
                thongtinhsba.thongtinbn.SOVAOVIEN,
                thongtinhsba.thongtinbn.SOVAOVIEN_DT,
                -1,
                function(data) {
                    if(data.length > 0) {
                        getCMUFileSigned769GetLink(data[0].KEYMINIO, 'pdf').then(pdfData => {
                            resolve({
                                url: pdfData,
                                kySo: 1,
                                isError: 0,
                                message: 'Thành công',
                            });
                        });
                    } else {
                        $.get("cmu_getlist?url=" + convertArray([
                            3,
                            params.ID_CHAM_SOC_CAP_2,
                            "CMU_LCSC2_CHITIET_SELXROW"
                        ])).done(function(data) {
                            if (data.length > 0) {
                                var dsPdf = [];
                                for(var p = 0; p < Math.ceil(data.length/3); p++){
                                    var objectNgay = {};
                                    var objectGio = {};
                                    var objectPhanCap = {};
                                    var objectNhanDinh = {};
                                    var objectMach = {};
                                    var objectNhietDo = {};
                                    var objectHuyetAp = {};
                                    var objectNhipTho = {};
                                    var objectSPO2 = {};
                                    var objectCanNang = {};
                                    var objectBMI = {};
                                    var objectDieuDuong = {};
                                    var objectIdChiTiet = {};
                                    var objectKeySign = {};
                                    for(var i = 0; i < 3; i++) {
                                        var item = data[i + p * 3];
                                        if (item) {
                                            objectNgay['ngay_' + (i+1)] = item.ONLY_NGAY;
                                            objectGio['gio_' + (i+1)] = item.ONLY_GIO;
                                            objectPhanCap['phancapcs_' + (i+1)] = item.PHAN_CAP_CS;
                                            objectNhanDinh['nhandinhtd_' + (i+1)] = item.NHAN_DINH_TD;
                                            objectDieuDuong['dieuduong_' + (i+1)] = item.TEN_NGUOI_TAO;
                                            objectMach['mach_' + (i+1)] = item.MACH;
                                            objectNhietDo['nhietdo_' + (i+1)] = item.NHIET_DO;
                                            objectHuyetAp['huyetap_' + (i + 1)] = item.HUYET_AP;
                                            objectNhipTho['nhiptho_' + (i+1)] = item.NHIP_THO;
                                            objectSPO2['spo2_' + (i+1)] = item.SPO2;
                                            objectCanNang['cannang_' + (i+1)] = item.CAN_NANG;
                                            objectBMI['bmi_' + (i+1)] = item.BMI;
                                            objectIdChiTiet['id_chi_tiet_' + (i+1)] = item.ID_CHI_TIET;
                                            objectKeySign['keysign_' + (i+1)] = params.ID_CHI_TIET && params.ID_CHI_TIET == item.ID_CHI_TIET ? "1" : item.KEYSIGN;
                                        }
                                    }
                                    var params2 = {
                                        dvtt: singletonObject.dvtt,
                                        stt_benhan: thongtinhsba.thongtinbn.STT_BENHAN,
                                        sobenhan: thongtinhsba.thongtinbn.SOBENHAN,
                                        mabenhnhan: thongtinhsba.thongtinbn.MABENHNHAN,
                                        ngayvaovien: thongtinhsba.thongtinbn.NGAYGIO_NHAPVIEN,
                                        ngaydautien: data[0].ONLY_NGAY,
                                        id_phieu: params.ID_CHAM_SOC_CAP_2,
                                        maxrow: singletonObject.maxRowCSC2,
                                        page: p,
                                        ...objectNgay,
                                        ...objectGio,
                                        ...objectPhanCap,
                                        ...objectNhanDinh,
                                        ...objectDieuDuong,
                                        ...objectMach,
                                        ...objectNhietDo,
                                        ...objectHuyetAp,
                                        ...objectNhipTho,
                                        ...objectSPO2,
                                        ...objectCanNang,
                                        ...objectBMI,
                                        ...objectIdChiTiet,
                                        ...objectKeySign
                                    };
                                    resolve({
                                        url: 'cmu_in_CMU_CHAMSOCCAP2?type=pdf&' + $.param(params2),
                                        kySo: 1,
                                        isError: 0,
                                        message: 'Thành công',
                                    });
                                }
                            }
                        });
                    }
                }
            );
            // getFilesign769(
            //     "PHIEU_NOITRU_CHAMSOCCAP2",
            //     params.ID,
            //     -1,
            //     singletonObject.dvtt,
            //     thongtinhsba.thongtinbn.SOVAOVIEN,
            //     thongtinhsba.thongtinbn.SOVAOVIEN_DT,
            //     -1,
            //     function(dataKySo) {
            //         if(dataKySo.length > 0) {
            //             getCMUFileSigned769GetLink(dataKySo[0].KEYMINIO, 'pdf').then(pdfData => {
            //                 resolve({
            //                     url: pdfData,
            //                     kySo: 1,
            //                     isError: 0,
            //                     message: 'Thành công',
            //                     idKySo: dataKySo[0].ID
            //                 });
            //             }).catch(e => {
            //                 resolve({
            //                     url: '',
            //                     kySo: 0,
            //                     isError: 1,
            //                     message: 'Lỗi get file ký số'
            //                 });
            //             });
            //         } else {
            //             var dsPdf = [];
            //             for(var p = 0; p < Math.ceil(params.data.length/3); p++){
            //                 var params2 = {
            //                     dvtt: singletonObject.dvtt,
            //                     mabenhnhan: thongtinhsba.thongtinbn.MABENHNHAN,
            //                     ngayvaovien: thongtinhsba.thongtinbn.NGAYGIO_NHAPVIEN,
            //                     ngaydautien: params.data[0].ONLY_NGAY,
            //                     id_phieu: params.ID,
            //                     page: p,
            //                 };
            //                 for(var i = 0; i < 3; i++) {
            //                     var item2 = params.data[i + p * 3];
            //                     if (item2) {
            //                         var object = {};
            //                         $.each(item2, function(key, value) {
            //                             object[key.toLowerCase() + "_" + (i+1)] = value;
            //                         });
            //                         params2 = {
            //                             ...params2,
            //                             ...object
            //                         };
            //                     }
            //                 }
            //                 console.log("params", params2)
            //                 var url = 'cmu_in_rp_chamsoccap2?type=pdf&' + $.param(params2);
            //                 dsPdf.push({
            //                     "url": [url],
            //                     "name": "Phiếu chăm sóc cấp 2" + " trang " + (p+1)
            //                 });
            //             }
            //             loadAndCombinePDFs(dsPdf).then(data => {
            //                 resolve({
            //                     url: url,
            //                     kySo: 0,
            //                     isError: 0,
            //                     message: 'Thành công'
            //                 });
            //             }).catch(error => {
            //                 notifiToClient("Red", "Có lỗi xảy ra:", error);
            //             });
            //         }
            //     }
            // )
        } catch (e) {
            console.log("Lỗi get file ký số phiếu chăm sóc cấp 2: " + e)
            reject({
                url: '',
                kySo: 0,
                isError: 1,
                message: 'Lỗi get file ký số phiếu chăm sóc cấp 2: ' + e
            });
        }
    });
}

function getUrlPCSC1(params) {
    return new Promise(async (resolve, reject) => {
        try {
            getFilesign769(
                "PHIEU_NOITRU_CHAMSOCCAP1",
                params.ID_CHAM_SOC_CAP_1,
                -1,//singletonObject.userId,
                singletonObject.dvtt,
                thongtinhsba.thongtinbn.SOVAOVIEN,
                thongtinhsba.thongtinbn.SOVAOVIEN_DT,
                -1,
                function(data) {
                    if(data.length > 0) {
                        getCMUFileSigned769GetLink(data[0].KEYMINIO, 'pdf').then(pdfData => {
                            resolve({
                                url: pdfData,
                                kySo: 1,
                                isError: 0,
                                message: 'Thành công',
                            });
                        });
                    } else {
                        $.get("cmu_getlist?url=" + convertArray([
                            params.ID_CHAM_SOC_CAP_1,
                            "CMU_LCSC1_CHITIET_SEL24ROW"
                        ])).done(function(data) {
                            if (data.length > 0) {
                                var dsPdf = [];
                                for(var p = 0; p < Math.ceil(data.length/24); p++){
                                    var dataMach = [];
                                    var dataHuyetApTren = [];
                                    var dataHuyetApDuoi = [];
                                    var dataSPO2 = [];
                                    var dataNhietDo = [];
                                    var objectGio = {};
                                    var objectXoayTro = {};
                                    var objectDieuDuong = {};
                                    var objectMach = {};
                                    var objectHuyetAp = {};
                                    var objectSPO2 = {};
                                    var objectNhietDo = {};
                                    var objectIdChiTiet = {};
                                    var objectKeySign = {};
                                    for(var i = 0; i < 24; i++) {
                                        var item = data[i + p * 24];
                                        if (item) {
                                            var xoaytro = [];
                                            item.XOAY_TRO ? xoaytro.push(item.XOAY_TRO) : '';
                                            item.CHAM_SOC_DIEU_DUONG ? xoaytro.push(item.CHAM_SOC_DIEU_DUONG) : '';
                                            dataMach.push(item.MACH);
                                            dataHuyetApDuoi.push(item.HUYET_AP_DUOI);
                                            dataHuyetApTren.push(item.HUYET_AP_TREN);
                                            dataSPO2.push(item.SPO2);
                                            dataNhietDo.push(item.NHIET_DO);
                                            objectGio['ngay_' + (i+1)] = item.ONLY_NGAY ? item.ONLY_NGAY.split('/').slice(0, 2).join('/') : "";
                                            objectGio['gio_' + (i+1)] = item.ONLY_GIO;
                                            objectXoayTro['xoaytro_' + (i+1)] = xoaytro.join(" - ");
                                            objectDieuDuong['dieuduong_' + (i+1)] = item.TEN_NGUOI_TAO;
                                            objectMach['mach_' + (i+1)] = item.MACH;
                                            objectHuyetAp['huyetap_' + (i + 1)] = (item.HUYET_AP_TREN || '') + '/' + (item.HUYET_AP_DUOI || '');
                                            objectSPO2['spo2_' + (i+1)] = item.SPO2;
                                            objectNhietDo['nhietdo_' + (i+1)] = item.NHIET_DO;
                                            objectIdChiTiet['id_chi_tiet_' + (i+1)] = item.ID_CHI_TIET;
                                            objectKeySign['keysign_' + (i+1)] = item.KEYSIGN;
                                        }
                                    }
                                    var params2 = {
                                        dvtt: singletonObject.dvtt,
                                        mabenhnhan: thongtinhsba.thongtinbn.MABENHNHAN,
                                        ngayvaovien: thongtinhsba.thongtinbn.NGAYGIO_NHAPVIEN,
                                        ngaydautien: data[0].ONLY_NGAY,
                                        id_phieu: params.ID_CHAM_SOC_CAP_1,
                                        id_dang_ky: params.ID_DANG_KY || 0,
                                        maxrow: singletonObject.thamSo960605 == 0 ? 40 : 32,
                                        page: p,
                                        ...objectGio,
                                        ...objectXoayTro,
                                        ...objectDieuDuong,
                                        ...objectMach,
                                        ...objectHuyetAp,
                                        ...objectSPO2,
                                        ...objectNhietDo,
                                        ...objectIdChiTiet,
                                        ...objectKeySign
                                    };
                                    if (singletonObject.thamSo960605 == 0) {
                                        resolve({
                                            url: 'cmu_in_CMU_CHAMSOCCAP1_KHONGHINH?type=pdf&' + $.param(params2),
                                            kySo: 1,
                                            isError: 0,
                                            message: 'Thành công',
                                        });
                                    } else {
                                        $("#wrap_canvas").html("<canvas id='myChartCSC1' width='1028' height='584'></canvas>")
                                        veBieuDoChamSocCap1({
                                            MACH: dataMach,
                                            HUYET_AP_TREN: dataHuyetApTren,
                                            HUYET_AP_DUOI: dataHuyetApDuoi,
                                            SPO2: dataSPO2,
                                            NHIET_DO: dataNhietDo
                                        });
                                        var image = $("#myChartCSC1").get(0).toDataURL("image/png").replace("data:image/png;base64,", "");
                                        $.ajax({
                                            url: "cmu_post_CMU_CHART_INS",
                                            type: "POST",
                                            data: {
                                                url: [singletonObject.dvtt, params.ID_CHAM_SOC_CAP_1, 'CHAMSOCCAP1', p, image].join("```"),
                                            },
                                            async: false
                                        }).done(function(count) {
                                            if(count > 0) {
                                                resolve({
                                                    url: 'cmu_in_CMU_CHAMSOCCAP1_COHINH?type=pdf&' + $.param(params2),
                                                    kySo: 1,
                                                    isError: 0,
                                                    message: 'Thành công',
                                                });
                                            }
                                        });
                                    }
                                }
                            }
                        });
                    }
                }
            );
        } catch (e) {
            console.log("Lỗi get file ký số phiếu chăm sóc cấp 1: " + e)
            reject({
                url: '',
                kySo: 0,
                isError: 1,
                message: 'Lỗi get file ký số phiếu chăm sóc cấp 1: ' + e
            });
        }
    });
}

function getUrlChuanBiTienPhau(params) {
    return new Promise(async (resolve, reject) => {
        try {
            getFilesign769(
                "PHIEU_NOITRU_CHUANBITIENPHAU",
                params.ID,
                -1,
                singletonObject.dvtt,
                thongtinhsba.thongtinbn.SOVAOVIEN,
                thongtinhsba.thongtinbn.SOVAOVIEN_DT,
                -1,
                function(dataKySo) {
                    if(dataKySo.length > 0) {
                        getCMUFileSigned769GetLink(dataKySo[0].KEYMINIO, 'pdf').then(pdfData => {
                            resolve({
                                url: pdfData,
                                kySo: 1,
                                isError: 0,
                                message: 'Thành công',
                                idKySo: dataKySo[0].ID
                            });
                        }).catch(e => {
                            resolve({
                                url: '',
                                kySo: 0,
                                isError: 1,
                                message: 'Lỗi get file ký số'
                            });
                        });
                    } else {
                        var text_date_part = params.NGAY_TAO_PHIEU.split("/");
                        var params2 = {
                            magiay: params.ID,
                            mabenhnhan: thongtinhsba.thongtinbn.MABENHNHAN,
                            text_date: text_date_part[0],
                            text_month: text_date_part[1],
                            text_year: text_date_part[2]
                        }
                        var url = 'cmu_in_cmu_phieuchuanbitienphau?type=pdf&' + $.param(params2);
                        resolve({
                            url: url,
                            kySo: 0,
                            isError: 0,
                            message: 'Thành công'
                        });
                    }
                }
            )
        } catch (e) {
            console.log("Lỗi get file ký số phiếu chuẩn bị tiền phẫu: " + e)
            reject({
                url: '',
                kySo: 0,
                isError: 1,
                message: 'Lỗi get file ký số phiếu chuẩn bị tiền phẫu: ' + e
            });
        }
    });
}

function getUrlCamDoanChapNhanPTTT(params) {
    return new Promise(async (resolve, reject) => {
        try {
            getFilesign769(
                "PHIEU_NOITRU_CAMDOANPHAUTHUAT",
                params.ID,
                -1,
                singletonObject.dvtt,
                thongtinhsba.thongtinbn.SOVAOVIEN,
                thongtinhsba.thongtinbn.SOVAOVIEN_DT,
                -1,
                function(dataKySo) {
                    if(dataKySo.length > 0) {
                        getCMUFileSigned769GetLink(dataKySo[0].KEYMINIO, 'pdf').then(pdfData => {
                            resolve({
                                url: pdfData,
                                kySo: 1,
                                isError: 0,
                                message: 'Thành công',
                                idKySo: dataKySo[0].ID
                            });
                        }).catch(e => {
                            resolve({
                                url: '',
                                kySo: 0,
                                isError: 1,
                                message: 'Lỗi get file ký số'
                            });
                        });
                    } else {
                        var params2 = {
                            magiay: params.ID,
                            mabenhnhan: thongtinhsba.thongtinbn.MABENHNHAN
                        }
                        var url = 'cmu_in_cmu_giaycamdoanphauthuat?type=pdf&' + $.param(params2);
                        resolve({
                            url: url,
                            kySo: 0,
                            isError: 0,
                            message: 'Thành công'
                        });
                    }
                }
            )
        } catch (e) {
            console.log("Lỗi get file ký số giấy cam đoan chấp nhận phẫu thuật, thủ thuật: " + e)
            reject({
                url: '',
                kySo: 0,
                isError: 1,
                message: 'Lỗi get file ký số giấy cam đoan chấp nhận phẫu thuật, thủ thuật: ' + e
            });
        }
    });
}

function getUrlCamDoanChayThanNT(params) {
    return new Promise(async (resolve, reject) => {
        try {
            getFilesign769(
                "PHIEU_NOITRU_CHAYTHANNHANTAO",
                params.ID,
                -1,
                singletonObject.dvtt,
                thongtinhsba.thongtinbn.SOVAOVIEN,
                thongtinhsba.thongtinbn.SOVAOVIEN_DT,
                -1,
                function(dataKySo) {
                    if(dataKySo.length > 0) {
                        getCMUFileSigned769GetLink(dataKySo[0].KEYMINIO, 'pdf').then(pdfData => {
                            resolve({
                                url: pdfData,
                                kySo: 1,
                                isError: 0,
                                message: 'Thành công',
                                idKySo: dataKySo[0].ID
                            });
                        }).catch(e => {
                            resolve({
                                url: '',
                                kySo: 0,
                                isError: 1,
                                message: 'Lỗi get file ký số'
                            });
                        });
                    } else {
                        var text_date_part = params.NGAY_TAO_PHIEU.split("/");
                        var text_date = text_date_part[0];
                        var text_month = text_date_part[1];
                        var text_year = text_date_part[2];
                        var tenkhoa = "";
                        singletonObject.danhsachphongban.forEach(function(obj) {
                            if(obj.MAKHOA == singletonObject.makhoa) {
                                tenkhoa = obj.TENKHOA;
                            }
                        })
                        var params2 = {
                            magiay: params.ID,
                            mabenhnhan: thongtinhsba.thongtinbn.MABENHNHAN,
                            text_date,
                            text_month,
                            text_year,
                            tenkhoa
                        }
                        var url = 'cmu_in_cmu_giaycamdoan?type=pdf&' + $.param(params2);
                        resolve({
                            url: url,
                            kySo: 0,
                            isError: 0,
                            message: 'Thành công'
                        });
                    }
                }
            )
        } catch (e) {
            console.log("Lỗi get file ký số giấy cam đoan chấp nhận chạy thận: " + e)
            reject({
                url: '',
                kySo: 0,
                isError: 1,
                message: 'Lỗi get file ký số giấy cam đoan chấp nhận chạy thận: ' + e
            });
        }
    });
}

function getUrlCamDoanGayMe(params) {
    return new Promise(async (resolve, reject) => {
        try {
            getFilesign769(
                "PHIEU_NOITRU_CAMDOANGAYME",
                params.ID,
                -1,
                singletonObject.dvtt,
                thongtinhsba.thongtinbn.SOVAOVIEN,
                thongtinhsba.thongtinbn.SOVAOVIEN_DT,
                -1,
                function(dataKySo) {
                    if(dataKySo.length > 0) {
                        getCMUFileSigned769GetLink(dataKySo[0].KEYMINIO, 'pdf').then(pdfData => {
                            resolve({
                                url: pdfData,
                                kySo: 1,
                                isError: 0,
                                message: 'Thành công',
                                idKySo: dataKySo[0].ID
                            });
                        }).catch(e => {
                            resolve({
                                url: '',
                                kySo: 0,
                                isError: 1,
                                message: 'Lỗi get file ký số'
                            });
                        });
                    } else {
                        var text_date_part = params.NGAY_TAO_PHIEU.split("/");
                        var text_date = text_date_part[0];
                        var text_month = text_date_part[1];
                        var text_year = text_date_part[2];
                        var params2 = {
                            magiay: params.ID,
                            mabenhnhan: thongtinhsba.thongtinbn.MABENHNHAN,
                            text_date,
                            text_month,
                            text_year
                        }
                        var url = 'cmu_in_cmu_giaycamdoangayme?type=pdf&' + $.param(params2);
                        resolve({
                            url: url,
                            kySo: 0,
                            isError: 0,
                            message: 'Thành công'
                        });
                    }
                }
            )
        } catch (e) {
            console.log("Lỗi get file ký số giấy cam đoan chấp nhận gây mê: " + e)
            reject({
                url: '',
                kySo: 0,
                isError: 1,
                message: 'Lỗi get file ký số giấy cam đoan chấp nhận gây mê: ' + e
            });
        }
    });
}

function getUrlLoetTiDe(params) {
    return new Promise(async (resolve, reject) => {
        try {
            getFilesign769(
                "PHIEU_NOITRU_LOETTIDE",
                params.ID,
                -1,
                singletonObject.dvtt,
                thongtinhsba.thongtinbn.SOVAOVIEN,
                thongtinhsba.thongtinbn.SOVAOVIEN_DT,
                -1,
                function(dataKySo) {
                    if(dataKySo.length > 0) {
                        getCMUFileSigned769GetLink(dataKySo[0].KEYMINIO, 'pdf').then(pdfData => {
                            resolve({
                                url: pdfData,
                                kySo: 1,
                                isError: 0,
                                message: 'Thành công',
                                idKySo: dataKySo[0].ID
                            });
                        }).catch(e => {
                            resolve({
                                url: '',
                                kySo: 0,
                                isError: 1,
                                message: 'Lỗi get file ký số'
                            });
                        });
                    } else {
                        var params2 = {
                            dvtt: singletonObject.dvtt,
                            id: params.ID
                        }
                        var url = 'cmu_in_rp_phieuloettide?type=pdf&' + $.param(params2);
                        resolve({
                            url: url,
                            kySo: 0,
                            isError: 0,
                            message: 'Thành công'
                        });
                    }
                }
            )
        } catch (e) {
            console.log("Lỗi get file ký số phiếu loét tì đè: " + e)
            reject({
                url: '',
                kySo: 0,
                isError: 1,
                message: 'Lỗi get file ký số phiếu loét tì đè: ' + e
            });
        }
    });
}

function getUrlHoiChan(params) {
    return new Promise(async (resolve, reject) => {
        try {
            $.ajax({
                url: "cmu_getlist?url="+convertArray([
                    singletonObject.dvtt,
                    thongtinhsba.thongtinbn.SOVAOVIEN,
                    thongtinhsba.thongtinbn.SOVAOVIEN_DT,
                    "",
                    params.ID,
                    'CMU_SMART769_HCGET']),
                method: "GET",
            }).done(function (dataKySo) {
                    if(dataKySo.length > 0) {
                        getCMUFileSigned769GetLink(dataKySo[0].KEYMINIO, 'pdf').then(pdfData => {
                            resolve({
                                url: pdfData,
                                kySo: 1,
                                isError: 0,
                                message: 'Thành công',
                                idKySo: dataKySo[0].ID
                            });
                        }).catch(e => {
                            resolve({
                                url: '',
                                kySo: 0,
                                isError: 1,
                                message: 'Lỗi get file ký số'
                            });
                        });
                    } else {
                        var arr = [singletonObject.dvtt, thongtinhsba.thongtinbn.STT_BENHAN, thongtinhsba.thongtinbn.STT_DOTDIEUTRI, params.STT_HOICHAN, singletonObject.makhoa, 0];
                        var url = "noitru_inphieuhoichan?url=" + convertArray(arr);
                        resolve({
                            url: url,
                            kySo: 0,
                            isError: 0,
                            message: 'Thành công'
                        });
                    }
                }
            )
        } catch (e) {
            console.log("Lỗi get file ký số phiếu hội chẩn: " + e)
            reject({
                url: '',
                kySo: 0,
                isError: 1,
                message: 'Lỗi get file ký số phiếu hội chẩn: ' + e
            });
        }
    });
}

function getUrlKhamTienMe(params) {
    return new Promise(async (resolve, reject) => {
        try {
            getFilesign769(
                "PHIEU_NOITRU_KHAMTIENME",
                params.ID,
                -1,
                singletonObject.dvtt,
                thongtinhsba.thongtinbn.SOVAOVIEN,
                thongtinhsba.thongtinbn.SOVAOVIEN_DT,
                -1,
                function(dataKySo) {
                    if(dataKySo.length > 0) {
                        getCMUFileSigned769GetLink(dataKySo[0].KEYMINIO, 'pdf').then(pdfData => {
                            resolve({
                                url: pdfData,
                                kySo: 1,
                                isError: 0,
                                message: 'Thành công',
                                idKySo: dataKySo[0].ID
                            });
                        }).catch(e => {
                            resolve({
                                url: '',
                                kySo: 0,
                                isError: 1,
                                message: 'Lỗi get file ký số'
                            });
                        });
                    } else {
                        var params2 = {
                            maphieu: params.ID
                        }
                        var url = 'cmu_in_phieu_kham_tien_me?type=pdf&' + $.param(params2);
                        resolve({
                            url: url,
                            kySo: 0,
                            isError: 0,
                            message: 'Thành công'
                        });
                    }
                }
            )
        } catch (e) {
            console.log("Lỗi get file ký số phiếu khám tiền mê: " + e)
            reject({
                url: '',
                kySo: 0,
                isError: 1,
                message: 'Lỗi get file ký số phiếu khám tiền mê: ' + e
            });
        }
    });
}

function getUrlTheoDoiNLGlasgow(params) {
    return new Promise(async (resolve, reject) => {
        try {
            getFilesign769(
                "PHIEU_NOITRU_TDNGUOILONGLASGOW",
                1,
                -1,
                singletonObject.dvtt,
                thongtinhsba.thongtinbn.SOVAOVIEN,
                thongtinhsba.thongtinbn.SOVAOVIEN_DT,
                -1,
                function(dataKySo) {
                    if(dataKySo.length > 0) {
                        getCMUFileSigned769GetLink(dataKySo[0].KEYMINIO, 'pdf').then(pdfData => {
                            resolve({
                                url: pdfData,
                                kySo: 1,
                                isError: 0,
                                message: 'Thành công',
                                idKySo: dataKySo[0].ID
                            });
                        }).catch(e => {
                            resolve({
                                url: '',
                                kySo: 0,
                                isError: 1,
                                message: 'Lỗi get file ký số'
                            });
                        });
                    } else {
                        var arr = [singletonObject.dvtt, thongtinhsba.thongtinbn.SOVAOVIEN, ];
                        var url = "cmu_bangtheodoinguoilonglasgow_report?url=" + convertArray(arr)+'&viewPDF=1';
                        resolve({
                            url: url,
                            kySo: 0,
                            isError: 0,
                            message: 'Thành công'
                        });
                    }
                }
            )
        } catch (e) {
            console.log("Lỗi get file ký số phiếu theo dõi người lớn Glasgow: " + e)
            reject({
                url: '',
                kySo: 0,
                isError: 1,
                message: 'Lỗi get file ký số phiếu theo dõi người lớn Glasgow: ' + e
            });
        }
    });
}

function getUrlPhieuPhauThuat(params) {
    return new Promise(async (resolve, reject) => {
        try {
            var tenpt, chuoirp;
            if (params.LOAI == 'phauthuatmong'){
                tenpt = "_MONG"
                chuoirp = "emr_rp_phieuphauthuat_mong_cmu"
            } else if (params.LOAI == 'phauthuatsupmi'){
                tenpt = "_SUPMI"
                chuoirp = "emr_rp_phieuphauthuat_supmi_cmu"
            } else if (params.LOAI == 'phauthuattuile'){
                tenpt = "_TUILE"
                chuoirp = "emr_rp_phieuphauthuat_tuile_cmu"
            } else if (params.LOAI == 'phauthuatsapejko'){
                tenpt = "_SAPEJKO"
                chuoirp = "emr_rp_phieuphauthuat_sapejko_cmu"
            } else if (params.LOAI == 'phauthuatlac'){
                tenpt = "_LAC"
                chuoirp = "emr_rp_phieuphauthuat_lac_cmu"
            } else if (params.LOAI == 'phauthuatglocom'){
                tenpt = "_GLOCOM"
                chuoirp = "emr_rp_phieuphauthuat_glocom_cmu"
            } else if (params.LOAI == 'phauthuatbematnhancau'){
                tenpt = "_BEMATNHANCAU"
                chuoirp = "emr_rp_phieuphauthuat_nhancau_cmu"
            } else if (params.LOAI == 'phauthuatghepgiacmac'){
                tenpt = "_GHEPGIACMAC"
                chuoirp = "emr_rp_phieuphauthuat_ghepgiacmac_cmu"
            } else if (params.LOAI == 'thethuytinh'){
                tenpt = "_THETHUYTINH"
                chuoirp = "emr_rp_phieuphauthuat_thuthuat_cmu"
            }
            getFilesign769(
                "PHIEU_NOITRU_TRUONGTRINHPT" + tenpt,
                params.SO_PHIEU_DICHVU,
                -1,
                singletonObject.dvtt,
                thongtinhsba.thongtinbn.SOVAOVIEN,
                thongtinhsba.thongtinbn.SOVAOVIEN_DT,
                -1,
                function(dataKySo) {
                    if(dataKySo.length > 0) {
                        getCMUFileSigned769GetLink(dataKySo[0].KEYMINIO, 'pdf').then(pdfData => {
                            resolve({
                                url: pdfData,
                                kySo: 1,
                                isError: 0,
                                message: 'Thành công',
                                idKySo: dataKySo[0].ID
                            });
                        }).catch(e => {
                            resolve({
                                url: '',
                                kySo: 0,
                                isError: 1,
                                message: 'Lỗi get file ký số'
                            });
                        });
                    } else {
                        var arr, url, param;
                        if (params.LOAI == 'phieuphauthuat'){
                            arr = [
                                params.MA_DV,
                                params.SO_PHIEU_DICHVU,
                                '',
                                singletonObject.dvtt,
                                1,
                                thongtinhsba.thongtinbn.STT_BENHAN,
                                thongtinhsba.thongtinbn.STT_DOTDIEUTRI,
                                params.STT_DIEUTRI,
                                thongtinhsba.thongtinbn.SOVAOVIEN,
                                thongtinhsba.thongtinbn.TEN_BENH_NHAN,
                                params.TUOI,
                                params.GIOI_TINH_HT,
                                "0"];
                            url = "inphieuthuthuatphauthuat_hinhmau?url=" + convertArray(arr)+"&&typefile=pdf";
                        } else {
                            arr = [params.SO_PHIEU_DICHVU, singletonObject.dvtt, params.MA_DV, 1, '',
                                thongtinhsba.thongtinbn.STT_BENHAN, thongtinhsba.thongtinbn.STT_DOTDIEUTRI, params.STT_DIEUTRI];
                            param  = ["sophieudichvu", "dvtt", "madv", "noitru", "makhambenh", "sttbenhan", "sttdotdieutri", "sttdieutri"];
                            url = "cmu_report_" + chuoirp + "?url=" + convertArray(arr)+"&param="+ convertArray(param)+"&loaifile=pdf";
                        }
                        resolve({
                            url: url,
                            kySo: 0,
                            isError: 0,
                            message: 'Thành công'
                        });
                    }
                }
            )
        } catch (e) {
            console.log("Lỗi get file ký số phiếu " + params.TENPT + ": " + e)
            reject({
                url: '',
                kySo: 0,
                isError: 1,
                message: 'Lỗi get file ký số phiếu phẫu thuật ' + params.TENPT + ': ' + e
            });
        }
    });
}

function getUrlDanhGiaDinhDuong(params) {
    return new Promise(async (resolve, reject) => {
        try {
            var url = "", loaiGiay;
            var params2 = {
                maphieu: params.ID,
                mabenhnhan: thongtinhsba.thongtinbn.MABENHNHAN,
                sovaovien: thongtinhsba.thongtinbn.SOVAOVIEN,
                stt_dotdieutri: thongtinhsba.thongtinbn.STT_DOTDIEUTRI,
                ngay: params.ngay,
                thang: params.thang,
                nam: params.nam,
                namsinh: thongtinhsba.thongtinbn.NGAY_SINH.split("/")[2],
            }
            if (params.LOAI_PHIEU == "NGUOI_LON") {
                url = "cmu_in_cmu_phieudanhgiadd_nguoilon?type=pdf&" + $.param(params2);
                loaiGiay = "PHIEU_NOITRU_DANHGIADINHDUONG_NGUOILON";
            } else if (params.LOAI_PHIEU == "TRE_EM") {
                url = "cmu_in_cmu_phieudanhgiadd_treem?type=pdf&" + $.param(params2);
                loaiGiay = "PHIEU_NOITRU_DANHGIADINHDUONG_TREEM";
            } else if (params.LOAI_PHIEU == "SAN_PHU") {
                url = "cmu_in_cmu_phieudanhgiadd_sanphu?type=pdf&" + $.param(params2);
                loaiGiay = "PHIEU_NOITRU_DANHGIADINHDUONG_SANPHU";
            } else if (params.LOAI_PHIEU == "TREN_18") {
                url = "cmu_in_cmu_phieudanhgiadd_tren18?type=pdf&" + $.param(params2);
                loaiGiay = "PHIEU_NOITRU_DANHGIADINHDUONG_TREN18";
            } else if(params.LOAI_PHIEU === "DGDD_NB_NOITRU") {
                url = "cmu_in_rp_phieu_sangloc_dgdd_nb_noitru?type=pdf&" + $.param(params2);
            } else if(params.LOAI_PHIEU === "HD_CHEDO_DD_NB") {
                url = "cmu_in_rp_phieu_huongdan_chedo_dd_nb_noitru?type=pdf&" + $.param(params2);
            } else if(params.LOAI_PHIEU === "SL_DGDD_NHI_SS") {
                url = "cmu_in_rp_phieu_sangloc_dgdd_benh_nhi_sosinh?type=pdf&" + $.param(params2);
            } else if(params.LOAI_PHIEU === "SL_DGDD_NHI") {
                url = "cmu_in_rp_phieu_sangloc_dgdd_benh_nhi?type=pdf&" + $.param(params2);
            }
            getFilesign769V3({
                dvtt: singletonObject.dvtt,
                soVaoVien: thongtinhsba.thongtinbn.SOVAOVIEN,
                soVaoVien_DT: thongtinhsba.thongtinbn.SOVAOVIEN_DT,
                kyHieuPhieu: "PHIEU_NOITRU_DANHGIADINHDUONG,PHIEU_NOITRU_DANHGIADINHDUONG_DD",
                soPhieu: params.ID,
                userId: params.USER_ID,
                soBenhAn: "-1",
                nghiepVu: params.LOAI_PHIEU,
            }, function(dataKySo) {
                    if(dataKySo.length > 0) {
                        getCMUFileSigned769GetLink(dataKySo[0].KEYMINIO, 'pdf').then(pdfData => {
                            resolve({
                                url: pdfData,
                                kySo: 1,
                                isError: 0,
                                message: 'Thành công',
                                idKySo: dataKySo[0].ID
                            });
                        }).catch(e => {
                            resolve({
                                url: '',
                                kySo: 0,
                                isError: 1,
                                message: 'Lỗi get file ký số'
                            });
                        });
                    } else {
                        resolve({
                            url: url,
                            kySo: 0,
                            isError: 0,
                            message: 'Thành công'
                        });
                    }
                }
            );
        } catch (e) {
            console.log("Lỗi get file ký số phiếu đánh giá dinh dưỡng: " + e)
            reject({
                url: '',
                kySo: 0,
                isError: 1,
                message: 'Lỗi get file ký số phiếu đánh giá dinh dưỡng: ' + e
            });
        }
    });
}

function getUrlChanDoanNNTuVong(params) {
    return new Promise(async (resolve, reject) => {
        try {
            var arrTemp = [];
            getFilesign769("PHIEU_NOITRU_CHANDOANNNTUVONG_BACSI", params.ID, -1, singletonObject.dvtt,
                thongtinhsba.thongtinbn.SOVAOVIEN, thongtinhsba.thongtinbn.SOVAOVIEN_DT, -1, function(dataKySo) {
                    if (dataKySo.length > 0) {
                        arrTemp.push(dataKySo[0])
                    }
                });
            getFilesign769("PHIEU_NOITRU_CHANDOANNNTUVONG_TRUONGKHOA", params.ID, -1, singletonObject.dvtt,
                thongtinhsba.thongtinbn.SOVAOVIEN, thongtinhsba.thongtinbn.SOVAOVIEN_DT, -1, function(dataKySo) {
                    if (dataKySo.length > 0) {
                        arrTemp.push(dataKySo[0])
                    }
                });
            var maxCreateDate = null;
            var maxCreateDataObject = null;
            if (arrTemp.length > 0) {
                $.each(arrTemp, function (index, dataObject) {
                    var createDate = moment(dataObject.CREATE_DATE_STRING, "DD/MM/YYYY HH:mm:ss");
                    if (maxCreateDate === null || createDate.isAfter(maxCreateDate)) {
                        maxCreateDate = createDate;
                        maxCreateDataObject = dataObject;
                    }
                });
                getCMUFileSigned769GetLink(maxCreateDataObject.KEYMINIO, 'pdf').then(pdfData => {
                    resolve({
                        url: pdfData,
                        kySo: 1,
                        isError: 0,
                        message: 'Thành công',
                        idKySo: maxCreateDataObject.ID
                    });
                }).catch(e => {
                    resolve({
                        url: '',
                        kySo: 0,
                        isError: 1,
                        message: 'Lỗi get file ký số'
                    });
                });
            } else {
                var params2 = {
                    magiay: params.ID,
                    mabenhnhan: thongtinhsba.thongtinbn.MABENHNHAN,
                    tenbenhnhan: thongtinhsba.thongtinbn.TEN_BENH_NHAN,
                    sobenhan: thongtinhsba.thongtinbn.SOBENHAN,
                    sovaovien: thongtinhsba.thongtinbn.SOVAOVIEN,
                    gioitinh: thongtinhsba.thongtinbn.GIOI_TINH_NUM || thongtinhsba.thongtinbn.GIOI_TINH,
                    diachi: thongtinhsba.thongtinbn.DIA_CHI,
                    mabhyt: thongtinhsba.thongtinbn.SOBAOHIEMYTE,
                    tuoi: thongtinhsba.thongtinbn.TUOI,
                    ngayvaovien: thongtinhsba.thongtinbn.NGAY_VAO_VIEN || thongtinhsba.thongtinbn.NGAYNHAPVIEN_DATE,
                    khoa: thongtinhsba.thongtinbn.TEN_PHONGBAN ?? "...",
                    ngayvaokhoa: thongtinhsba.thongtinbn.NGAYVAOKHOA ?? "...",
                    ngaysinh: thongtinhsba.thongtinbn.NGAY_SINH,
                    sogiayto: thongtinhsba.thongtinbn.CMT_BENHNHAN,
                    stt_benhan: thongtinhsba.thongtinbn.STT_BENHAN,
                    stt_dotdieutri: thongtinhsba.thongtinbn.STT_DOTDIEUTRI,
                }
                var url = 'cmu_in_cmu_chandoannntuvong?type=pdf&' + $.param(params2);
                resolve({
                    url: url,
                    kySo: 0,
                    isError: 0,
                    message: 'Thành công'
                });
            }
        } catch (e) {
            console.log("Lỗi get file ký số phiếu tóm tắt thông tin người bệnh nặng xin về: " + e)
            reject({
                url: '',
                kySo: 0,
                isError: 1,
                message: 'Lỗi get file ký số phiếu tóm tắt thông tin người bệnh nặng xin về: ' + e
            });
        }
    });
}

function getUrlTTTTBenhNangXinVe(params) {
    return new Promise(async (resolve, reject) => {
        try {
            var arrTemp = [];
            getFilesign769("PHIEU_NOITRU_TTTTBENHNANGXINVE_BACSI", params.ID, -1, singletonObject.dvtt,
                thongtinhsba.thongtinbn.SOVAOVIEN, thongtinhsba.thongtinbn.SOVAOVIEN_DT, -1, function(dataKySo) {
                    if (dataKySo.length > 0) {
                        arrTemp.push(dataKySo[0])
                    }
                });
            getFilesign769("PHIEU_NOITRU_TTTTBENHNANGXINVE_TRUONGKHOA", params.ID, -1, singletonObject.dvtt,
                thongtinhsba.thongtinbn.SOVAOVIEN, thongtinhsba.thongtinbn.SOVAOVIEN_DT, -1, function(dataKySo) {
                    if (dataKySo.length > 0) {
                        arrTemp.push(dataKySo[0])
                    }
                });
            var maxCreateDate = null;
            var maxCreateDataObject = null;
            if (arrTemp.length > 0) {
                $.each(arrTemp, function (index, dataObject) {
                    var createDate = moment(dataObject.CREATE_DATE_STRING, "DD/MM/YYYY HH:mm:ss");
                    if (maxCreateDate === null || createDate.isAfter(maxCreateDate)) {
                        maxCreateDate = createDate;
                        maxCreateDataObject = dataObject;
                    }
                });
                getCMUFileSigned769GetLink(maxCreateDataObject.KEYMINIO, 'pdf').then(pdfData => {
                    resolve({
                        url: pdfData,
                        kySo: 1,
                        isError: 0,
                        message: 'Thành công',
                        idKySo: maxCreateDataObject.ID
                    });
                }).catch(e => {
                    resolve({
                        url: '',
                        kySo: 0,
                        isError: 1,
                        message: 'Lỗi get file ký số'
                    });
                });
            } else {
                var params2 = {
                    magiay: params.ID,
                    mabenhnhan: thongtinhsba.thongtinbn.MABENHNHAN,
                    tenbenhnhan: thongtinhsba.thongtinbn.TEN_BENH_NHAN,
                    sobenhan: thongtinhsba.thongtinbn.SOBENHAN,
                    sovaovien: thongtinhsba.thongtinbn.SOVAOVIEN,
                    gioitinh: thongtinhsba.thongtinbn.GIOI_TINH_NUM || thongtinhsba.thongtinbn.GIOI_TINH,
                    diachi: thongtinhsba.thongtinbn.DIA_CHI,
                    mabhyt: thongtinhsba.thongtinbn.SOBAOHIEMYTE,
                    tuoi: thongtinhsba.thongtinbn.TUOI,
                    ngayvaovien: thongtinhsba.thongtinbn.NGAY_VAO_VIEN || thongtinhsba.thongtinbn.NGAYNHAPVIEN_DATE,
                    khoa: thongtinhsba.thongtinbn.TEN_PHONGBAN ?? "...",
                    ngayvaokhoa: thongtinhsba.thongtinbn.NGAYVAOKHOA ?? "...",
                    ngaysinh: thongtinhsba.thongtinbn.NGAY_SINH,
                    sogiayto: thongtinhsba.thongtinbn.CMT_BENHNHAN,
                    stt_benhan: thongtinhsba.thongtinbn.STT_BENHAN,
                    stt_dotdieutri: thongtinhsba.thongtinbn.STT_DOTDIEUTRI,
                }
                var url = 'cmu_in_cmu_ttttbenhnangxinve?type=pdf&' + $.param(params2);
                resolve({
                    url: url,
                    kySo: 0,
                    isError: 0,
                    message: 'Thành công'
                });
            }
        } catch (e) {
            console.log("Lỗi get file ký số phiếu tóm tắt thông tin người bệnh nặng xin về: " + e)
            reject({
                url: '',
                kySo: 0,
                isError: 1,
                message: 'Lỗi get file ký số phiếu tóm tắt thông tin người bệnh nặng xin về: ' + e
            });
        }
    });
}

// function getUrlTTTTNguoiBenhXinVe(params) {
//     return new Promise(async (resolve, reject) => {
//         try {
//             getFilesign769(
//                 "PHIEU_NOITRU_TTTTNGBENHXINVE_NGUOILAP",
//                 params.ID,
//                 -1,
//                 singletonObject.dvtt,
//                 thongtinhsba.thongtinbn.SOVAOVIEN,
//                 thongtinhsba.thongtinbn.SOVAOVIEN_DT,
//                 -1,
//                 function(dataKySo) {
//                     if(dataKySo.length > 0) {
//                         getCMUFileSigned769GetLink(dataKySo[0].KEYMINIO, 'pdf').then(pdfData => {
//                             resolve({
//                                 url: pdfData,
//                                 kySo: 1,
//                                 isError: 0,
//                                 message: 'Thành công',
//                                 idKySo: dataKySo[0].ID
//                             });
//                         }).catch(e => {
//                             resolve({
//                                 url: '',
//                                 kySo: 0,
//                                 isError: 1,
//                                 message: 'Lỗi get file ký số'
//                             });
//                         });
//                     } else {
//                         var params2 = {
//                             magiay: params.ID,
//                             mabenhnhan: thongtinhsba.thongtinbn.MABENHNHAN,
//                             tenbenhnhan: thongtinhsba.thongtinbn.TEN_BENH_NHAN,
//                             sobenhan: thongtinhsba.thongtinbn.SOBENHAN,
//                             sovaovien: thongtinhsba.thongtinbn.SOVAOVIEN,
//                             gioitinh: thongtinhsba.thongtinbn.GIOI_TINH_NUM || thongtinhsba.thongtinbn.GIOI_TINH,
//                             diachi: thongtinhsba.thongtinbn.DIA_CHI,
//                             mabhyt: thongtinhsba.thongtinbn.SOBAOHIEMYTE,
//                             tuoi: thongtinhsba.thongtinbn.TUOI,
//                             ngayvaovien: thongtinhsba.thongtinbn.NGAY_VAO_VIEN || thongtinhsba.thongtinbn.NGAYNHAPVIEN_DATE,
//                             khoa: thongtinhsba.thongtinbn.TEN_PHONGBAN ?? "...",
//                             ngaysinh: thongtinhsba.thongtinbn.NGAY_SINH,
//                             sogiayto: thongtinhsba.thongtinbn.CMT_BENHNHAN,
//                             stt_benhan: thongtinhsba.thongtinbn.STT_BENHAN,
//                             stt_dotdieutri: thongtinhsba.thongtinbn.STT_DOTDIEUTRI,
//                         }
//                         var url = 'cmu_in_cmu_ttttbenhnangxinve?type=pdf&' + $.param(params2);
//                         resolve({
//                             url: url,
//                             kySo: 0,
//                             isError: 0,
//                             message: 'Thành công'
//                         });
//                     }
//                 }
//             )
//         } catch (e) {
//             console.log("Lỗi get file ký số phiếu tóm tắt thông tin người bệnh nặng xin về: " + e)
//             reject({
//                 url: '',
//                 kySo: 0,
//                 isError: 1,
//                 message: 'Lỗi get file ký số phiếu tóm tắt thông tin người bệnh nặng xin về: ' + e
//             });
//         }
//     });
// }

function getUrlToDieuTri(params) {
    return new Promise(async (resolve, reject) => {
        try {
            getFilesign769(
                "TODIEUTRI_NOITRU",
                params.ID_DIEUTRI,
                -1,
                singletonObject.dvtt,
                thongtinhsba.thongtinbn.SOVAOVIEN,
                thongtinhsba.thongtinbn.SOVAOVIEN_DT,
                -1,
                function(dataKySo) {
                    if(dataKySo.length > 0) {
                        getCMUFileSigned769GetLink(dataKySo[0].KEYMINIO, 'pdf').then(pdfData => {
                            resolve({
                                url: pdfData,
                                kySo: 1,
                                isError: 0,
                                message: 'Thành công',
                                idKySo: dataKySo[0].ID
                            });
                        }).catch(e => {
                            resolve({
                                url: '',
                                kySo: 0,
                                isError: 1,
                                message: 'Lỗi get file ký số'
                            });
                        });
                    } else {
                        var arr = [
                            singletonObject.dvtt,
                            thongtinhsba.thongtinbn.TEN_BENH_NHAN,
                            params.ID_DIEUTRI,
                            thongtinhsba.thongtinbn.SOVAOVIEN,
                            thongtinhsba.thongtinbn.SOVAOVIEN_DT,
                            params.tenKhoa,
                            params.tuoi,
                            params.GIOI_TINH_HT,
                            thongtinhsba.thongtinbn.SOBENHAN,
                            "/WEB-INF/pages/camau/reports/rp_todieutri_tong_v2_sub.jasper"
                        ];
                        var param = ['dvtt', 'hovaten', 'id_dieutri', 'sovaovien', 'sovaovien_dt', "ten_phongban",
                            "tuoi", "gioitinh", "sobenhan", "FILE_1"];
                        var url = "cmu_injasper?url=" + convertArray(arr) + "&param=" + convertArray(param)
                            + "&loaifile=pdf&jasper=rp_todieutri_v2";
                        resolve({
                            url: url,
                            kySo: 0,
                            isError: 0,
                            message: 'Thành công'
                        });
                    }
                }
            )
        } catch (e) {
            console.log("Lỗi get file ký số tờ điều trị: " + e)
            reject({
                url: '',
                kySo: 0,
                isError: 1,
                message: 'Lỗi get file ký số tờ điều trị: ' + e
            });
        }
    });
}

function getUrlToDieuTriTong(params) {
    return new Promise(async (resolve, reject) => {
        try {
            var arr = [singletonObject.dvtt,
                thongtinhsba.thongtinbn.TEN_BENH_NHAN,
                0,
                thongtinhsba.thongtinbn.SOVAOVIEN,
                thongtinhsba.thongtinbn.SOVAOVIEN_DT,
                params.tenKhoa,
                params.tuoi,
                params.GIOI_TINH_HT,
                thongtinhsba.thongtinbn.SOBENHAN,
                params.BAC_SI_TAO,
                params.KHOA_TAO,
                thongtinhsba.thongtinbn.STT_BENHAN,
                "/WEB-INF/pages/camau/reports/rp_todieutri_tong_v2_sub.jasper"
            ];
            var param = ['dvtt', 'hovaten', 'id_dieutri', 'sovaovien', 'sovaovien_dt',
                "ten_phongban", "tuoi", "gioitinh", "sobenhan", "userid", "makhoa", "stt_benhan", "FILE_1"];
            var url = "cmu_injasper?url=" + convertArray(arr) + "&param="
                + convertArray(param) + "&loaifile=pdf&jasper=rp_todieutri_tong_v2";
            resolve({
                url: url,
                kySo: 0,
                isError: 0,
                message: 'Thành công'
            });
        } catch (e) {
            console.log("Lỗi get file ký số tờ điều trị tổng: " + e)
            reject({
                url: '',
                kySo: 0,
                isError: 1,
                message: 'Lỗi get file ký số tờ điều trị tổng: ' + e
            });
        }
    });
}

function getUrlGiayRaVien(params) {
    return new Promise(async (resolve, reject) => {
        try {
            if(singletonObject.thamSo960601 == 1) {
                $.ajax({
                    url: "cmu_getlist?url="+convertArray([
                        singletonObject.dvtt,
                        params.SOVAOVIEN,
                        params.SOVAOVIEN_DT,
                        params.SOVAOVIEN,
                        'cmu_smart769_getgrv']),
                    method: "GET",
                }).done(function (data) {
                    var rest = data[0]
                    if(rest['KEYMINIO']) {
                        getCMUFileSigned769GetLink(data[0].KEYMINIO, 'pdf').then(pdfData => {
                            resolve({
                                url: pdfData,
                                kySo: 1,
                                isError: 0,
                                message: 'Thành công',
                                idKySo: data[0].ID,
                            });
                        }).catch(error => {
                            reject({
                                url: '',
                                kySo: 0,
                                isError: 1,
                                message: 'Lỗi hệ thống: lấy file ký số từ API'
                            });
                        });
                    } else {
                        reject({
                            url: '',
                            kySo: 0,
                            isError: 2,
                            message: 'Lỗi hệ thống: Chưa có ký số'
                        });
                    }
                })
                return false;
            }
            var formData = new FormData();
            formData.append("sovaovien_dt", params.SOVAOVIEN_DT);
            formData.append("sovaovien", params.SOVAOVIEN);
            formData.append("dvtt", singletonObject.dvtt);
            var y = new XMLHttpRequest();
            y.onload = function(data) {
                if (y.readyState === y.DONE) {
                    if (y.status === 200 && data.srcElement.responseText.includes("ERROR") == false) {
                        console.log("load done")
                        resolve({
                            url:  data.srcElement.responseText,
                            kySo: 1,
                            isError: 0,
                            message: 'Thành công'
                        });
                    } else {
                        console.log("load faile")
                        resolve({
                            url:  "",
                            kySo: 1,
                            isError: 0,
                            message: 'Thành công'
                        });
                    }

                }

            };
            y.open('POST', 'cmu_apikiso_chi-tiet-giay-ra-vien-base64-noauth');
            y.send(formData);

        } catch (e) {
            reject({
                url: '',
                kySo: 0,
                isError: 1,
                message: 'Lỗi hệ thống: ' + e
            });
        }
    });
}

function getUrlGiayChuyenTuyen(params) {
    return new Promise(async (resolve, reject) => {
        try {
            $.ajax({
                url: "cmu_getlist?url="+convertArray([
                    singletonObject.dvtt,
                    params.SOVAOVIEN,
                    params.SOVAOVIEN_DT,
                    params.SOVAOVIEN,
                    'cmu_smart769_getchuyentuyen']),
                method: "GET",
            }).done(function (data) {
                if(data.length > 0) {
                    var url = "smartca-get-signed-file-minio?keyminio=" + data[0].KEYMINIO + "&type=pdf";
                    $.ajax({
                        method: "POST", url: url, contentType: "charset=utf-8"
                    }).done(function (data) {
                        resolve({
                            url: 'data:application/pdf;base64,' + data.FILE,
                            kySo: 1,
                            isError: 0,
                            message: 'Thành công'
                        });
                    }).fail(function() {
                        reject({
                            url: '',
                            kySo: 0,
                            isError: 1,
                            message: 'Lỗi hệ thống: ' + e
                        });
                    });
                } else {
                    var url = 'noitru_ingiaychuyentuyen?dvtt=' + singletonObject.dvtt + "&stt_benhan=" + thongtinhsba.thongtinbn.STT_BENHAN
                        + "&stt_dotdieutri=" + thongtinhsba.thongtinbn.STT_DOTDIEUTRI + "&mabenhnhan=" + thongtinhsba.thongtinbn.MABENHNHAN
                    resolve({
                        url: url,
                        kySo: 0,
                        isError: 0,
                        message: 'Thành công'
                    });
                }
            }).fail(function() {
                reject({
                    url: '',
                    kySo: 0,
                    isError: 1,
                    message: 'Lỗi hệ thống: ' + e
                });
            })

        } catch (e) {
            console.log("Lỗi hệ thống: " + e)
            reject({
                url: '',
                kySo: 0,
                isError: 1,
                message: 'Lỗi hệ thống: ' + e
            });
        }
    });
}

function getUrlGiayChungSinh(params) {
    return new Promise(async (resolve, reject) => {
        try {
            $.ajax({
                url: "cmu_getlist?url="+convertArray([
                    singletonObject.dvtt,
                    params.SOVAOVIEN,
                    params.SOVAOVIEN_DT,
                    params.ID_CHUNGSINH,
                    'cmu_smart769_getchungsinh']),
                method: "GET",
            }).done(function (data) {
                if(data[0].KEYMINIO) {
                    var url = "smartca-get-signed-file-minio?keyminio=" + data[0].KEYMINIO + "&type=pdf";
                    $.ajax({
                        method: "POST", url: url, contentType: "charset=utf-8"
                    }).done(function (data) {
                        resolve({
                            url: 'data:application/pdf;base64,' + data.FILE,
                            kySo: 1,
                            isError: 0,
                            message: 'Thành công'
                        });
                    }).fail(function() {
                        reject({
                            url: '',
                            kySo: 0,
                            isError: 1,
                            message: 'Lỗi hệ thống: ' + e
                        });
                    });
                } else {
                    var url = "ingiaychungsinhVSC?url=" + convertArray([params.ID_CHUNGSINH, 0, "0"])
                    resolve({
                        url: url,
                        kySo: 0,
                        isError: 0,
                        message: 'Thành công'
                    });
                }
            }).fail(function() {
                reject({
                    url: '',
                    kySo: 0,
                    isError: 1,
                    message: 'Lỗi hệ thống: ' + e
                });
            })

        } catch (e) {
            console.log("Lỗi hệ thống: " + e)
            reject({
                url: '',
                kySo: 0,
                isError: 1,
                message: 'Lỗi hệ thống: ' + e
            });
        }
    });
}