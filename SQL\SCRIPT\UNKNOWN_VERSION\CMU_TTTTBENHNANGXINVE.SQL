
CREATE TABLE "HIS_MANAGER"."CMU_TTTT<PERSON><PERSON><PERSON><PERSON>NGXINVE"
(	"ID" NUMBER GENERATED ALWAYS AS IDENTITY MINVALUE 1 MAXVALUE 9999999999999999999999999999 INCREMENT BY 1 START WITH 1 CACHE 20 NOORDER  NOCYCLE  NOT NULL ENABLE,
     "DVTT" VARCHAR2(50 BYTE) NOT NULL ENABLE,
     "SOVAOVIEN" NUMBER(18,0),
     "MA_BENH_NHAN" NUMBER(18,0),
     "THONGTINBENHJSON" VARCHAR2(4000 BYTE),
     "NNTUVONGJSON" VARCHAR2(4000 BYTE),
     "B<PERSON>HKHACJSON" VARCHAR2(4000 BYTE),
     "PHAUTHUAT4TUANJSON" VARCHAR2(4000 BYTE),
     "HINHTHUCTV" VARCHAR2(255 BYTE),
     "NNBENNGOAIJSON" VARCHAR2(4000 BYTE),
     "TUVONG<PERSON><PERSON><PERSON><PERSON><PERSON>JSON" VARCHAR2(4000 BYTE),
     "PHUN<PERSON><PERSON>SO<PERSON>" VARCHAR2(4000 BYTE),
     "KETLUANJSON" VARCHAR2(4000 BYTE),
     "BSDIEUTRI" VARCHAR2(20 BYTE),
     "TRUONGKHOA" VARCHAR2(20 BYTE),
     "THUTRUONG" VARCHAR2(20 BYTE),
     "NGAY_TAO_PHIEU" DATE,
     "NGUOI_TAO" VARCHAR2(20 BYTE),
     "MAKHOA" VARCHAR2(20 BYTE),
     CONSTRAINT "PK_TTTTBENHNANGXINVE_ID" PRIMARY KEY ("ID")
)
CREATE INDEX "HIS_MANAGER"."CMU_TTTTBENHNANGXINVEIN" ON "HIS_MANAGER"."CMU_TTTTBENHNANGXINVE" ("DVTT", "SOVAOVIEN")