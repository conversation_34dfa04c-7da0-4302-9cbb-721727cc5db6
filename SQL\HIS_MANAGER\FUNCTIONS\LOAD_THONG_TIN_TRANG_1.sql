create or replace FUNCTION             LOAD_THONG_TIN_TRANG_1 (
    P_DVTT           IN   VARCHAR2,
    P_SOVAOVIEN      IN   VARCHAR2,
    P_SOVAOVIEN_DT   IN   VARCHAR2,
    P_ID             IN   NUMBER
) RETURN SYS_REFCURSOR IS
    CUR                   SYS_REFCURSOR;
    M_ICD_BENHRAVIEN      VARCHAR2(10) DEFAULT ' ';
    M_TENICD_BENHRAVIEN   VARCHAR2(500) DEFAULT ' ';
    M_BENHKEMTHEO         VARCHAR2(1000) DEFAULT ' ';
    v_nv_lambenhan varchar2(100);
    v_ngay_lambenhan varchar2(50);
    V_TUYEN_BV NUMBER(1) DEFAULT 0;
    V_DEMGIUONG_KE NUMBER(18) DEFAULT  0;
    V_TONGSONGAYDIEUTRI NUMBER(5) := HIS_MANAGER.VBA_TINH_SONGAYDIEUTRI(P_DVTT, P_SOVAOVIEN, P_SOVAOVIEN_DT);
    V_TS_TTSO_VBA VARCHAR2(100):= HIS_MANAGER.FUNC_THAM_SO_VBA(P_DVTT, 5);
    v_id_VBAME number := 0;
    v_loaiBAME varchar2(250);
    v_PARA varchar2(100);
    p_sql  VARCHAR2(4000);
    v_stt_benhan varchar2(250) := '';
    v_stt_dotdieutri number(1) := 1;
    p_solanphauthuat number;
    thamso960606 varchar2(50) := his_manager.cmu_tsdv(p_DVTT, 960606, '0');
    v_thamso960616 number(10) := cmu_tsdv(P_DVTT, 960616, 0);
    M_ICD_BPRAVIENYHCT      VARCHAR2(500) DEFAULT ' ';
    M_TENICD_BPRAVIENYHCT   VARCHAR2(2000) DEFAULT ' ';
BEGIN
    BEGIN
        select dv.cap_donvi into V_TUYEN_BV from HIS_FW.DM_DONVI dv where dv.ma_donvi = P_DVTT;
      EXCEPTION
        WHEN NO_DATA_FOUND THEN
          V_TUYEN_BV:= 0;
    END;
    BEGIN
        SELECT COUNT(1) INTO V_DEMGIUONG_KE
        FROM NOITRU_LOGGIUONGBENH
        WHERE SOVAOVIEN = P_SOVAOVIEN
          AND SOVAOVIEN_DT = P_SOVAOVIEN_DT
          AND DVTT = P_DVTT
          AND NGAYRA IS NOT NULL;
    END;

    begin
        select CONCAT(CONCAT(CD.MOTA_CHUCDANH, '. '), NV.TEN_NHANVIEN),
               to_char(CHA.TIME_CREATE, '"Ngày" dd "tháng" mm "năm" yyyy')
        into v_nv_lambenhan, v_ngay_lambenhan
        from HIS_MANAGER.NOITRU_VOBENHAN CHA
                 JOIN HIS_FW.DM_NHANVIEN NV ON NV.MA_NHANVIEN = CHA.MANV_LAMBENHAN
                 JOIN HIS_FW.DM_CHUCDANH_NHANVIEN CD ON CD.MA_CHUCDANH = NV.CHUCDANH_NHANVIEN
        where CHA.ID = P_ID;
        EXCEPTION
                WHEN NO_DATA_FOUND THEN
                    v_nv_lambenhan := ' ';
                    v_ngay_lambenhan := ' ';
    end;
    BEGIN
        SELECT
            CASE
                WHEN DDT.TT_DOTDIEUTRI = 3
                    OR DDT.TT_DOTDIEUTRI = 6 THEN
                    NVL(XV.ICD_BENHCHINH, ' ') -- Xuất viện
                WHEN DDT.TT_DOTDIEUTRI = 4 THEN
                    NVL(CV.ICD_BENHCHINH, ' ') -- chuyển viện
                WHEN DDT.TT_DOTDIEUTRI = 5 THEN
                    NVL(TV.ICD_BENHCHINH, ' ') -- tử vong
                WHEN DDT.TT_DOTDIEUTRI = 7 THEN
                    NVL(BA.ICD_NHAPVIEN, ' ')  -- kết thúc đợt điều trị
                ELSE
                    ' '
                END,
            CASE
                WHEN DDT.TT_DOTDIEUTRI = 3
                    OR DDT.TT_DOTDIEUTRI = 6 THEN
                    NVL(XV.TEN_BENHCHINH, ' ') -- Xuất viện
                WHEN DDT.TT_DOTDIEUTRI = 4 THEN
                    NVL(CV.TEN_BENHCHINH, ' ') -- chuyển viện
                WHEN DDT.TT_DOTDIEUTRI = 5 THEN
                    NVL(TV.TEN_BENHCHINH, ' ') -- tử vong
                WHEN DDT.TT_DOTDIEUTRI = 7 THEN
                    NVL(BA.TENBENHCHINH_NHAPVIEN, ' ')-- kết thúc đợt điều trị
                ELSE
                    ' '
                END,
            CASE
                WHEN DDT.TT_DOTDIEUTRI = 3
                    OR DDT.TT_DOTDIEUTRI = 6 THEN
                    NVL(XV.BENHKEMTHEO, ' ')  -- Xuất viện
                WHEN DDT.TT_DOTDIEUTRI = 4 THEN
                    NVL(CV.BENHKEMTHEO, ' ')  -- chuyển viện
                WHEN DDT.TT_DOTDIEUTRI = 5 THEN
                    NVL(TV.BENHKEMTHEO, ' ')  -- tử vong
                WHEN DDT.TT_DOTDIEUTRI = 7 THEN
                    NVL(BA.TENBENHPHU_NHAPVIEN, ' ') -- kết thúc đợt điều trị
                ELSE
                    ' '
                END,
                nvl(vbame.ID, 0),
                vbame.ID_VBA,
                ddt.STT_BENHAN,
                ddt.STT_DOTDIEUTRI
        INTO
            M_ICD_BENHRAVIEN,
            M_TENICD_BENHRAVIEN,
            M_BENHKEMTHEO,
            v_id_VBAME,
            v_loaiBAME,
            v_stt_benhan,
            v_stt_dotdieutri
        FROM
            HIS_MANAGER.NOITRU_DOTDIEUTRI            DDT
                JOIN HIS_MANAGER.NOITRU_BENHAN                BA ON BA.DVTT = DDT.DVTT
                AND BA.SOVAOVIEN = DDT.SOVAOVIEN AND BA.STT_BENHAN = DDT.STT_BENHAN
                LEFT JOIN HIS_MANAGER.NOITRU_XUATVIEN              XV ON XV.SOVAOVIEN_DT = DDT.SOVAOVIEN_DT
                AND XV.DVTT = DDT.DVTT
                LEFT JOIN HIS_MANAGER.NOITRU_CHUYENTUYENBENHNHAN   CV ON CV.SOVAOVIEN_DT = DDT.SOVAOVIEN_DT
                AND CV.DVTT = DDT.DVTT
                LEFT JOIN HIS_MANAGER.NOITRU_TUVONG                TV ON TV.SOVAOVIEN_DT = DDT.SOVAOVIEN_DT
                AND TV.DVTT = DDT.DVTT
                left join his_public_list.dm_benh_nhan bn on ddt.mabenhnhan = bn.ma_benh_nhan
                left join his_manager.cmu_ma_gcsguibhxh gcs on gcs.id_giaychungsinh = bn.id_giaychungsinh
                left join HIS_MANAGER.NOITRU_VOBENHAN vbame on vbame.mabenhan = bn.sobenhan_hosome and vbame.dvtt = gcs.dvtt and vbame.sovaovien = gcs.sovaovien and vbame.DELETED = 0
        WHERE
                DDT.DVTT = P_DVTT
          AND DDT.SOVAOVIEN = P_SOVAOVIEN
          AND DDT.SOVAOVIEN_DT = P_SOVAOVIEN_DT;

        EXCEPTION
                WHEN NO_DATA_FOUND THEN
                    M_ICD_BENHRAVIEN := ' ';
                    M_TENICD_BENHRAVIEN := ' ';
                    M_BENHKEMTHEO := ' ';
                    v_id_VBAME := 0;
                    v_loaiBAME := null;
                    v_stt_benhan := null;
                    v_stt_dotdieutri := null;
    END;
    SELECT
    LISTAGG(ICD, ';') within group (ORDER BY ngaytao asc) ICD,
    LISTAGG( '('|| ICD || ') ' || tenicd, ';') within group (ORDER BY ngaytao asc) tenICD
    INTO M_ICD_BPRAVIENYHCT, M_TENICD_BPRAVIENYHCT
    FROM NOITRU_XUATVIEN_ICDYHCT
    WHERE DVTT = P_DVTT AND SOVAOVIEN = P_SOVAOVIEN;
    if v_id_vbame <> 0 then
        BEGIN
          p_sql := 'SELECT BA.PARA' ||
                   ' FROM HIS_MANAGER.VBA_' || v_loaiBAME || ' BA' ||
                   ' WHERE BA.ID = :P_ID';
          EXECUTE IMMEDIATE p_sql INTO v_PARA USING v_id_vbame;
        EXCEPTION
          WHEN OTHERS THEN
            v_PARA := null;
        END;
    end if;

    begin
    SELECT
        count(*)
        INTO p_solanphauthuat
    FROM
        noitru_cd_dichvu_ct ct,
        dm_dich_vu_kham               dm
    WHERE
        ct.dvtt = p_dvtt
        AND ct.stt_benhan = v_stt_benhan
        AND ct.stt_dotdieutri = v_stt_dotdieutri
        and dm.dvtt = ct.dvtt
        AND ct.ma_dv = dm.ma_dv
        AND dm.loai_dv = 'PT'
        ;
    EXCEPTION
                    WHEN NO_DATA_FOUND THEN
                        p_solanphauthuat := 0;
    end;

    OPEN CUR FOR SELECT
                    VBA.ID_VBA,
                     TO_CHAR(BA.SOVAOVIEN) AS SOVAOVIEN,
                     DDT.SOVAOVIEN_DT,
                     BA.STT_BENHAN,
                     BA.MABENHNHAN,
                     BA.SOBENHAN,
                     BA.SOBENHAN_TT,
                     BA.BANT,
                     DDT.STT_DOTDIEUTRI,
                     UPPER(BN.TEN_BENH_NHAN) AS TEN_BENH_NHAN,
                     TO_CHAR(BN.NGAY_SINH, 'DD/MM/YYYY') AS NGAYSINH,
                     SUBSTR(TO_CHAR(BN.NGAY_SINH, 'DD'), 1, 1) AS NGAYSINH_1,
                     SUBSTR(TO_CHAR(BN.NGAY_SINH, 'DD'), 2, 1) AS NGAYSINH_2,
                     SUBSTR(TO_CHAR(BN.NGAY_SINH, 'MM'), 1, 1) AS THANGSINH_1,
                     SUBSTR(TO_CHAR(BN.NGAY_SINH, 'MM'), 2, 1) AS THANGSINH_2,
                     SUBSTR(TO_CHAR(BN.NGAY_SINH, 'YYYY'), 1, 1) AS NAMSINH_1,
                     SUBSTR(TO_CHAR(BN.NGAY_SINH, 'YYYY'), 2, 1) AS NAMSINH_2,
                     SUBSTR(TO_CHAR(BN.NGAY_SINH, 'YYYY'), 3, 1) AS NAMSINH_3,
                     SUBSTR(TO_CHAR(BN.NGAY_SINH, 'YYYY'), 4, 1) AS NAMSINH_4,
                     HIENTHI_TUOI_BENHNHAN(BN.NGAY_SINH, BA.NGAYNHAPVIEN) AS TUOI,
                     TINHTUOI_VOBENHAN(BN.NGAY_SINH, BA.NGAYNHAPVIEN) AS TUOI_TEXT,
                     REGEXP_SUBSTR(TINHTUOI_VOBENHAN(BN.NGAY_SINH, BA.NGAYNHAPVIEN),'[^(!!!)]+',1,1) AS TUOI_NUM,
                     DECODE(LENGTH(REGEXP_SUBSTR(TINHTUOI_VOBENHAN(BN.NGAY_SINH, BA.NGAYNHAPVIEN),'[^(!!!)]+',1,1)),
                            1,'0',
                            2, SUBSTR(REGEXP_SUBSTR(TINHTUOI_VOBENHAN(BN.NGAY_SINH, BA.NGAYNHAPVIEN),'[^(!!!)]+',1,1), 1, 1),
                            3, SUBSTR(REGEXP_SUBSTR(TINHTUOI_VOBENHAN(BN.NGAY_SINH, BA.NGAYNHAPVIEN),'[^(!!!)]+',1,1), 1, 2)) AS TUOI_1,

                     DECODE(LENGTH(REGEXP_SUBSTR(TINHTUOI_VOBENHAN(BN.NGAY_SINH, BA.NGAYNHAPVIEN),'[^(!!!)]+',1,1)),
                            1,SUBSTR(REGEXP_SUBSTR(TINHTUOI_VOBENHAN(BN.NGAY_SINH, BA.NGAYNHAPVIEN),'[^(!!!)]+',1,1), 1, 1),
                            2, SUBSTR(REGEXP_SUBSTR(TINHTUOI_VOBENHAN(BN.NGAY_SINH, BA.NGAYNHAPVIEN),'[^(!!!)]+',1,1), 2, 1),
                            3,SUBSTR(REGEXP_SUBSTR(TINHTUOI_VOBENHAN(BN.NGAY_SINH, BA.NGAYNHAPVIEN),'[^(!!!)]+',1,1), 3, 1)) AS TUOI_2,
                     decode(nvl(REGEXP_SUBSTR(TINHTUOI_VOBENHAN(BN.NGAY_SINH, BA.NGAYNHAPVIEN),'[^(!!!)]+',1,2),' '), 'THANG', 'Tháng', 'NGAY', 'Ngày', 'TUOI', 'Tuổi') AS TUOI_TEXT_RP,

                     BN.GIOI_TINH             AS GIOI_TINH_NUM,
                     to_char(BN.GIOI_TINH)             AS GIOI_TINH_CHAR,
                     decode(to_char(BN.GIOI_TINH), '1', 'Nam', 'Nữ')             AS GIOI_TINH_TEXT,

                     NN.TEN_NGHE_NGHIEP       AS TEN_NGHE_NGHIEP,
                     NN.MA_NGHE_NGHIEP        AS MA_NGHE_NGHIEP,
                     DECODE(TO_CHAR(LENGTH(NN.MA_NGHE_NGHIEP)), '1', '0', SUBSTR(NN.MA_NGHE_NGHIEP, 1, 1)) AS MA_NGHE_NGHIEP_1,
                     DECODE(TO_CHAR(LENGTH(NN.MA_NGHE_NGHIEP)), '1', TO_CHAR(NN.MA_NGHE_NGHIEP), SUBSTR(NN.MA_NGHE_NGHIEP, 2, 1)) AS
                         MA_NGHE_NGHIEP_2,
                     NN.MA_NGHE_NGHIEP_4069   AS MA_NGHE_NGHIEP_4069,
                     DECODE(TO_CHAR(LENGTH(NN.MA_NGHE_NGHIEP_4069)), '1', '0', SUBSTR(NN.MA_NGHE_NGHIEP_4069, 1, 1)) AS MA_NGHE_NGHIEP_4069_1
                         ,
                     DECODE(TO_CHAR(LENGTH(NN.MA_NGHE_NGHIEP_4069)), '1', TO_CHAR(NN.MA_NGHE_NGHIEP_4069), SUBSTR(NN.MA_NGHE_NGHIEP_4069
                         , 2, 1)) AS MA_NGHE_NGHIEP_4069_2,
                     DT.TEN_DANTOC            AS TEN_DANTOC,
                     DT.MA_DANTOC             AS MA_DANTOC,
                     DECODE(TO_CHAR(LENGTH(DT.MA_DANTOC)), '1', '0', SUBSTR(DT.MA_DANTOC, 1, 1)) AS MA_DANTOC_1,
                     DECODE(TO_CHAR(LENGTH(DT.MA_DANTOC)), '1', TO_CHAR(DT.MA_DANTOC), SUBSTR(DT.MA_DANTOC, 2, 1)) AS MA_DANTOC_2,
                     DT.MA_DANTOC_4069,
                     DECODE(TO_CHAR(LENGTH(DT.MA_DANTOC_4069)), '1', '0', SUBSTR(DT.MA_DANTOC_4069, 1, 1)) AS MA_DANTOC_4069_1,
                     DECODE(TO_CHAR(LENGTH(DT.MA_DANTOC_4069)), '1', TO_CHAR(DT.MA_DANTOC_4069), SUBSTR(DT.MA_DANTOC_4069, 2, 1)) AS
                         MA_DANTOC_4069_2,
--                      DECODE(NVL(TTHC.NGOAIKIEU, TO_CHAR(BN.NGOAIKIEU)), '00', 'Việt nam', 'Khác') AS NGOAI_KIEU,
--                      DECODE(NVL(TTHC.NGOAIKIEU, TO_CHAR(BN.NGOAIKIEU)), '00', '00', '99') AS NGOAI_KIEU_INT,
--                     DECODE (TTHC.NGOAIKIEU, NULL, DECODE(TO_CHAR(BN.NGOAIKIEU), '1', 'Khác', 'Việt Nam'), DMQG.TEN_VIETNAM) AS NGOAI_KIEU,
                     DECODE (TO_CHAR(TTHC.NGOAIKIEU),'00', 'Việt Nam', NULL , DECODE(TO_CHAR(BN.NGOAIKIEU), '1', 'Khác', 'Việt Nam'), DMQG.TEN_VIETNAM) AS NGOAI_KIEU,
                     DECODE (TTHC.NGOAIKIEU, NULL, DECODE(TO_CHAR(BN.NGOAIKIEU), '1', '99', '00'), TTHC.NGOAIKIEU) AS NGOAI_KIEU_INT,
                     SUBSTR(TTHC.NGOAIKIEU, 1, 1) as NGOAI_KIEU_1,
                     SUBSTR(TTHC.NGOAIKIEU, 2, 1) as NGOAI_KIEU_2,
                     BN.DIA_CHI AS DIA_CHI_FULL,
                     NVL(TTHC.SONHA, ' ') AS SONHA,
                     NVL(TTHC.THONPHO, NVL(REGEXP_SUBSTR(BN.DIA_CHI, '[^,]+', 1, 1), ' ')) AS THONPHO,

                    NVL(TO_CHAR(TT.TEN_TINH_THANH), TO_CHAR(TTHC.TINHTHANH_TEXT)) AS TEN_TINH_THANH,
                    NVL(TO_CHAR(TT.MA_TINH_THANH), TO_CHAR(TTHC.TINHTHANH)) AS MA_TINH_THANH,
                    CASE
                        WHEN LENGTH(NVL(TO_CHAR(TT.MA_TINH_THANH), TO_CHAR(TTHC.TINHTHANH))) <= 1 THEN
                            '0'
                        ELSE
                            SUBSTR(NVL(TO_CHAR(TT.MA_TINH_THANH), TO_CHAR(TTHC.TINHTHANH)), 1, 1)
                        END AS MA_TINH_THANH_1,
                    CASE
                        WHEN LENGTH(NVL(TO_CHAR(TT.MA_TINH_THANH), TO_CHAR(TTHC.TINHTHANH))) > 1 THEN
                            SUBSTR(NVL(TO_CHAR(TT.MA_TINH_THANH), TO_CHAR(TTHC.TINHTHANH)), 2)
                        ELSE
                            NVL(TO_CHAR(TT.MA_TINH_THANH), TO_CHAR(TTHC.TINHTHANH))
                        END AS MA_TINH_THANH_2,

                    NVL(TO_CHAR(QH.TEN_QUAN_HUYEN), TO_CHAR(TTHC.QUANHUYEN_TEXT)) AS TEN_QUAN_HUYEN,
                    NVL(TO_CHAR(QH.MA_QUAN_HUYEN), TO_CHAR(TTHC.QUANHUYEN)) AS MA_QUAN_HUYEN,
                    CASE
                        WHEN LENGTH(NVL(TO_CHAR(QH.MA_QUAN_HUYEN), TO_CHAR(TTHC.QUANHUYEN))) <= 3 THEN
                            SUBSTR(LPAD(NVL(TO_CHAR(QH.MA_QUAN_HUYEN), TO_CHAR(TTHC.QUANHUYEN)), 3, '0'), 1, 1)
                        ELSE
                            SUBSTR(NVL(TO_CHAR(QH.MA_QUAN_HUYEN), TO_CHAR(TTHC.QUANHUYEN)), 1, 2)
                    END AS MA_QUAN_HUYEN_1,
                    CASE
                        WHEN LENGTH(NVL(TO_CHAR(QH.MA_QUAN_HUYEN), TO_CHAR(TTHC.QUANHUYEN))) <= 3 THEN
                            SUBSTR(LPAD(NVL(TO_CHAR(QH.MA_QUAN_HUYEN), TO_CHAR(TTHC.QUANHUYEN)), 3, '0'), 2, 1)

                        WHEN LENGTH(NVL(TO_CHAR(QH.MA_QUAN_HUYEN), TO_CHAR(TTHC.QUANHUYEN))) = 4 THEN
                            SUBSTR(NVL(TO_CHAR(QH.MA_QUAN_HUYEN), TO_CHAR(TTHC.QUANHUYEN)), 3, 1)
                        ELSE
                            SUBSTR(NVL(TO_CHAR(QH.MA_QUAN_HUYEN), TO_CHAR(TTHC.QUANHUYEN)), 3, 2)
                    END AS MA_QUAN_HUYEN_2,
                    CASE
                        WHEN LENGTH(NVL(TO_CHAR(QH.MA_QUAN_HUYEN), TO_CHAR(TTHC.QUANHUYEN))) <= 3 THEN
                            SUBSTR(LPAD(NVL(TO_CHAR(QH.MA_QUAN_HUYEN), TO_CHAR(TTHC.QUANHUYEN)), 3, '0'), 3, 1)

                        WHEN LENGTH(NVL(TO_CHAR(QH.MA_QUAN_HUYEN), TO_CHAR(TTHC.QUANHUYEN))) = 4 THEN
                            SUBSTR(NVL(TO_CHAR(QH.MA_QUAN_HUYEN), TO_CHAR(TTHC.QUANHUYEN)), 4, 1)
                        ELSE
                            SUBSTR(NVL(TO_CHAR(QH.MA_QUAN_HUYEN), TO_CHAR(TTHC.QUANHUYEN)), 4, 2)
                    END AS MA_QUAN_HUYEN_3,
                     NVL(TTHC.XAPHUONG_TEXT, NVL(PX.TEN_PHUONG_XA, DC.TEN_XA)) AS TEN_PHUONG_XA,
                     NVL(TTHC.XAPHUONG, TO_CHAR(PX.MA_PHUONG_XA)) AS MA_PHUONG_XA,
                     nvl(TTHC.NOILAMVIEC, BN.NOILAMVIEC) AS NOILAMVIEC,
                     DECODE(TO_CHAR(DDT.COBHYT), '1', '1', '0', '2') AS COBHYT,
                     DECODE(TO_CHAR(DDT.COBHYT), '1', '1', '0', '2') AS COBHYT_RP,
                     NVL(DDT.SOBAOHIEMYTE, ' ') AS SOBAOHIEMYTE,
                     NVL(SUBSTR(DDT.SOBAOHIEMYTE, 1, 2), ' ') AS BHYT_MA1,
                     NVL(SUBSTR(DDT.SOBAOHIEMYTE, 3, 3), ' ') AS BHYT_MA2,
                     NVL(SUBSTR(DDT.SOBAOHIEMYTE, 6, 2), ' ') AS BHYT_MA3,
                     NVL(SUBSTR(DDT.SOBAOHIEMYTE, 8, 3), ' ') AS BHYT_MA4,
                     NVL(SUBSTR(DDT.SOBAOHIEMYTE, 11, 5), ' ') AS BHYT_MA5,
                     NVL(TO_CHAR(DDT.NGAYHETHAN_THEBHYT, 'DD/MM/YYYY'), ' ') AS HAN_THE_BHYT,
                     NVL(TO_CHAR(DDT.NGAYHETHAN_THEBHYT, '"ngày" DD "tháng" MM "năm" YYYY'), ' ') AS HAN_THE_BHYT_RP,
                     NVL(TO_CHAR(DDT.NGAYHETHAN_THEBHYT, 'DD'), ' ') AS NGAY_HH_BHYT,
                     NVL(TO_CHAR(DDT.NGAYHETHAN_THEBHYT, 'MM'), ' ') AS THANG_HH_BHYT,
                     NVL(TO_CHAR(DDT.NGAYHETHAN_THEBHYT, 'YYYY'), ' ') AS NAM_HH_BHYT,

                     NVL(TO_CHAR(DDT.NGAYBATDAU_THEBHYT, 'DD/MM/YYYY'), ' ') AS NGAYBD_THE_BHYT,
                     NVL(TO_CHAR(DDT.NGAYBATDAU_THEBHYT, '"ngày" DD "tháng" MM "năm" YYYY'), ' ') AS NGAYBD_THE_BHYT_RP,
                     NVL(TO_CHAR(DDT.NGAYBATDAU_THEBHYT, 'DD'), ' ') AS NGAY_BD_BHYT,
                     NVL(TO_CHAR(DDT.NGAYBATDAU_THEBHYT, 'MM'), ' ') AS THANG_BD_BHYT,
                     NVL(TO_CHAR(DDT.NGAYBATDAU_THEBHYT, 'YYYY'), ' ') AS NAM_BD_BHYT,
                     nvl(DECODE(TO_CHAR(BA.NHAPVIENTUPHANHE_NGOAITRU), '1',
                         NVL(trim(NV_NT.NGUOI_LIEN_HE), NVL(trim(TTHC.NGUOILIENHE), BN.NGUOI_LIEN_HE)),
                         NVL(trim(NV_TT.NGUOI_LIEN_HE), NVL(trim(TTHC.NGUOILIENHE), BN.NGUOI_LIEN_HE))), bnme.TEN_BENH_NHAN) AS NGUOI_LIEN_HE,
                     DECODE(TO_CHAR(BA.NHAPVIENTUPHANHE_NGOAITRU), '1',
                          NVL(trim(NV_NT.SO_DIEN_THOAI), nvl(trim(TTHC.SDT_NGUOILIENHE), BN.SO_DIEN_THOAI)),
                          nvl(trim(NV_TT.SO_DIEN_THOAI), nvl(trim(TTHC.SDT_NGUOILIENHE), BN.SO_DIEN_THOAI))) AS SO_DIEN_THOAI,
                     NVL(TO_CHAR(QLNB.THOIGIAN_BONG, 'HH24'), '') AS GIO_BONG,
                     NVL(TO_CHAR(QLNB.THOIGIAN_BONG, 'DD/MM/YYYY'), '') AS NGAYGIO_BONG_FORMAT,
                     NVL(TO_CHAR(QLNB.THOIGIAN_BONG, '"Ngày" DD "tháng" MM "năm" YYYY'), '') AS NGAYGIO_BONG_TEXT,
                     --NVL(TO_CHAR(RAVIEN.NGAY_TRANG1, 'DD/MM/YYYY'), ' ') as NGAY_TRANG1_FORMAT,
                     --NVL(TO_CHAR(RAVIEN.NGAY_TRANG1, '"Ngày" DD "tháng" MM "năm" YYYY'), 'Ngày ...... tháng ......năm') as NGAY_TRANG1_REPORT,
                     CASE
                         WHEN DDT.TT_DOTDIEUTRI = 3
                             OR DDT.TT_DOTDIEUTRI = 6 THEN
                             TO_CHAR(XV.THOIGIANXUATVIEN, 'DD/MM/YYYY')
                         WHEN DDT.TT_DOTDIEUTRI = 4 THEN
                             TO_CHAR(CV.THOIGIANCHUYENVIEN, 'DD/MM/YYYY')
                         WHEN DDT.TT_DOTDIEUTRI = 5 THEN
                             TO_CHAR(TV.THOIGIANTUVONG, 'DD/MM/YYYY')
                         WHEN DDT.TT_DOTDIEUTRI = 7 THEN
                             TO_CHAR(DDT.NGAYRA, 'DD/MM/YYYY')
                         ELSE
                             ' '
                     END AS NGAY_TRANG1_FORMAT,
                     CASE
                         WHEN DDT.TT_DOTDIEUTRI = 3
                             OR DDT.TT_DOTDIEUTRI = 6 THEN
                             TO_CHAR(XV.THOIGIANXUATVIEN, '"Ngày" DD "tháng" MM "năm" YYYY')
                         WHEN DDT.TT_DOTDIEUTRI = 4 THEN
                             TO_CHAR(CV.THOIGIANCHUYENVIEN, '"Ngày" DD "tháng" MM "năm" YYYY')
                         WHEN DDT.TT_DOTDIEUTRI = 5 THEN
                             TO_CHAR(TV.THOIGIANTUVONG, '"Ngày" DD "tháng" MM "năm" YYYY')
                         WHEN DDT.TT_DOTDIEUTRI = 7 THEN
                             TO_CHAR(DDT.NGAYRA, '"Ngày" DD "tháng" MM "năm" YYYY')
                         ELSE
                             'Ngày ...... tháng ......năm'
                     END AS NGAY_TRANG1_REPORT,
                     RAVIEN.GIAMDOC,
                     nvl(TRUONGKHOAXV.TEN_NHANVIEN, nvl(TRUONGKHOACV.TEN_NHANVIEN, nvl(TRUONGKHOATV.TEN_NHANVIEN, RAVIEN.TRUONGKHOA))) TRUONGKHOA,
                     nvl(TRUONGKHOAXV.MA_NHANVIEN, nvl(TRUONGKHOACV.MA_NHANVIEN, nvl(TRUONGKHOATV.MA_NHANVIEN, '-1'))) MATRUONGKHOA,
                     TO_CHAR(decode(to_char(BA.NHAPVIENTUPHANHE_NGOAITRU), '1',
                        tiepnhan.thoi_gian_tiep_nhan,
                        BA.NGAYNHAPVIEN
                     ), 'dd/mm/yyyy hh24:mi:ss') AS NGAYNHAPVIEN,
                     TO_CHAR(decode(to_char(BA.NHAPVIENTUPHANHE_NGOAITRU), '1',
                        tiepnhan.thoi_gian_tiep_nhan,
                        BA.NGAYNHAPVIEN
                     ), 'dd/mm/yyyy') AS NGAYNHAPVIEN_DATE,
                     TO_CHAR(decode(to_char(BA.NHAPVIENTUPHANHE_NGOAITRU), '1',
                        tiepnhan.thoi_gian_tiep_nhan,
                        BA.NGAYNHAPVIEN
                     ), 'HH24 "giờ" MI "ph, ngày" DD "/" MM "/" YYYY') AS NGAYNHAPVIEN_VARCHAR,
                     case when BA.HINHTHUCNHAPVIEN = 1 then 2
                          when BA.HINHTHUCNHAPVIEN = 3 and ddt.capcuu=1 then 1
                          when BA.HINHTHUCNHAPVIEN = 3 then 3
                          else 0 end AS HINHTHUCNHAPVIEN,
                     to_char(case when BA.HINHTHUCNHAPVIEN = 1 then 2
                          when BA.HINHTHUCNHAPVIEN = 3 and ddt.capcuu=1 then 1
                          when BA.HINHTHUCNHAPVIEN = 3 then 3
                          else 0 end) AS HINHTHUCNHAPVIEN_RP,
                     BA.NOIGIOITHIEU                   AS NOIGIOITHIEU,
                     to_char(BA.NOIGIOITHIEU)          AS NOIGIOITHIEU_RP,
                     NVL(NULLIF(DECODE(TO_CHAR(QLNB.LANVAOVIEN), '', TO_CHAR(BA.VAOVIENLANTHU), TO_CHAR(QLNB.LANVAOVIEN)), '0'), '1') AS VAOVIENLANTHU,
                     CASE
                         WHEN DDT.TT_DOTDIEUTRI = 3
                             OR DDT.TT_DOTDIEUTRI = 6 THEN
                             TO_CHAR(XV.THOIGIANXUATVIEN, 'HH24 "giờ" MI ", ngày" DD "/" MM "/" YYYY')
                         WHEN DDT.TT_DOTDIEUTRI = 4 THEN
                             TO_CHAR(CV.THOIGIANCHUYENVIEN, 'HH24 "giờ" MI ", ngày" DD "/" MM "/" YYYY')
                         WHEN DDT.TT_DOTDIEUTRI = 5 THEN
                             TO_CHAR(TV.THOIGIANTUVONG, 'HH24 "giờ" MI ", ngày" DD "/" MM "/" YYYY')
                         WHEN DDT.TT_DOTDIEUTRI = 7 THEN
                             TO_CHAR(DDT.NGAYRA, 'HH24 "giờ" MI "phút, " DD "/" MM "/" YYYY')
                         ELSE
                             ' '
                     END AS THOIGIAN_RAVIEN_VAR,
                     CASE
                         WHEN DDT.TT_DOTDIEUTRI = 3
                             OR DDT.TT_DOTDIEUTRI = 6 THEN
                             TO_CHAR(XV.THOIGIANXUATVIEN, 'DD/MM/YYYY HH24:MI:SS')
                         WHEN DDT.TT_DOTDIEUTRI = 4 THEN
                             TO_CHAR(CV.THOIGIANCHUYENVIEN, 'DD/MM/YYYY HH24:MI:SS')
                         WHEN DDT.TT_DOTDIEUTRI = 5 THEN
                             TO_CHAR(TV.THOIGIANTUVONG, 'DD/MM/YYYY HH24:MI:SS')
                         WHEN DDT.TT_DOTDIEUTRI = 7 THEN
                             TO_CHAR(DDT.NGAYRA, 'DD/MM/YYYY HH24:MI:SS')
                         ELSE
                             ' '
                     END AS THOIGIAN_RAVIEN,
                     DECODE(CV.TUYENBENHVIENCHUYENDI, 1, 1, 2, 1, 4, 2, 0) AS CHUYENVIEN,
                     to_char(DECODE(CV.TUYENBENHVIENCHUYENDI, 1, 1, 2, 1, 4, 2, 0)) AS CHUYENVIEN_RP,
                     CASE
                         WHEN DDT.TT_DOTDIEUTRI = 4 THEN
                             CV.TENBENHVIEN_CHUYENDI
                         ELSE
                             ' '
                     END AS TEN_BENHVIEN_CHUYENTUYEN,

--                     CASE WHEN DDT.TT_DOTDIEUTRI > 2 THEN TO_NUMBER(--TINHNGAY_DIEUTRI(DDT.NGAYVAO, DDT.NGAYRA)
--                         NVL(TRUNC(DDT.NGAYRA) - TRUNC(DDT.NGAYVAO),0)
--                     )
--                         ELSE 0 END
                    nvl(V_TONGSONGAYDIEUTRI, 0) AS SO_NGAY_DIEU_TRI,
--                     to_char(
--                              CASE WHEN DDT.TT_DOTDIEUTRI > 2 THEN TO_NUMBER(--TINHNGAY_DIEUTRI(DDT.NGAYVAO, DDT.NGAYRA)
--                              NVL(TRUNC(DDT.NGAYRA) - TRUNC(DDT.NGAYVAO),0)
--                              )
--                                   ELSE 0 END
--                          )
                    CASE
                      WHEN nvl(QLNB.TONGSONGAYDIEUTRI, 0) > 0 THEN
                        to_char(nvl(QLNB.TONGSONGAYDIEUTRI, 0))
                      WHEN V_TONGSONGAYDIEUTRI > 0 THEN
                        to_char(V_TONGSONGAYDIEUTRI)
                      ELSE
                        to_char(VBA_TINH_SONGAYDIEUTRI_V2(DDT.TT_DOTDIEUTRI, XV.THOIGIANXUATVIEN, CV.THOIGIANCHUYENVIEN, TV.THOIGIANTUVONG, DDT.NGAYRA,
                                             DECODE(TO_CHAR(BA.NHAPVIENTUPHANHE_NGOAITRU), '1', tiepnhan.thoi_gian_tiep_nhan, BA.NGAYNHAPVIEN), V_TONGSONGAYDIEUTRI))
                    END AS SO_NGAY_DIEU_TRI_RP,
                    --SUBSTR(LPAD(nvl(QLNB.TONGSONGAYDIEUTRI, V_TONGSONGAYDIEUTRI), '3', '0'), 1, 1) AS SO_NGAY_DIEU_TRI_RP_1,
                    --SUBSTR(LPAD(nvl(QLNB.TONGSONGAYDIEUTRI, V_TONGSONGAYDIEUTRI), '3', '0'), 2, 1) AS SO_NGAY_DIEU_TRI_RP_2,
                    --SUBSTR(LPAD(nvl(QLNB.TONGSONGAYDIEUTRI, V_TONGSONGAYDIEUTRI), '3', '0'), 3, 1) AS SO_NGAY_DIEU_TRI_RP_3,

                    --Tính số ngày điều trị V2
                    CASE
                      WHEN nvl(QLNB.TONGSONGAYDIEUTRI, 0) > 0 THEN
                        SUBSTR(LPAD(QLNB.TONGSONGAYDIEUTRI, '3', '0'), 1, 1)
                      WHEN V_TONGSONGAYDIEUTRI > 0 THEN
                        SUBSTR(LPAD(V_TONGSONGAYDIEUTRI, '3', '0'), 1, 1)
                      ELSE
                        SUBSTR(LPAD(VBA_TINH_SONGAYDIEUTRI_V2(DDT.TT_DOTDIEUTRI, XV.THOIGIANXUATVIEN, CV.THOIGIANCHUYENVIEN, TV.THOIGIANTUVONG, DDT.NGAYRA,
                                             DECODE(TO_CHAR(BA.NHAPVIENTUPHANHE_NGOAITRU), '1', tiepnhan.thoi_gian_tiep_nhan, BA.NGAYNHAPVIEN), V_TONGSONGAYDIEUTRI), '3', '0'), 1, 1)
                    END AS SO_NGAY_DIEU_TRI_RP_1,
                    CASE
                      WHEN nvl(QLNB.TONGSONGAYDIEUTRI, 0) > 0 THEN
                        SUBSTR(LPAD(QLNB.TONGSONGAYDIEUTRI, '3', '0'), 2, 1)
                      WHEN V_TONGSONGAYDIEUTRI > 0 THEN
                        SUBSTR(LPAD(V_TONGSONGAYDIEUTRI, '3', '0'), 2, 1)
                      ELSE
                        SUBSTR(LPAD(VBA_TINH_SONGAYDIEUTRI_V2(DDT.TT_DOTDIEUTRI, XV.THOIGIANXUATVIEN, CV.THOIGIANCHUYENVIEN, TV.THOIGIANTUVONG, DDT.NGAYRA,
                                             DECODE(TO_CHAR(BA.NHAPVIENTUPHANHE_NGOAITRU), '1', tiepnhan.thoi_gian_tiep_nhan, BA.NGAYNHAPVIEN), V_TONGSONGAYDIEUTRI), '3', '0'), 2, 1)
                    END AS SO_NGAY_DIEU_TRI_RP_2,
                    CASE
                      WHEN nvl(QLNB.TONGSONGAYDIEUTRI, 0) > 0 THEN
                        SUBSTR(LPAD(QLNB.TONGSONGAYDIEUTRI, '3', '0'), 3, 1)
                      WHEN V_TONGSONGAYDIEUTRI > 0 THEN
                        SUBSTR(LPAD(V_TONGSONGAYDIEUTRI, '3', '0'), 3, 1)
                      ELSE
                        SUBSTR(LPAD(VBA_TINH_SONGAYDIEUTRI_V2(DDT.TT_DOTDIEUTRI, XV.THOIGIANXUATVIEN, CV.THOIGIANCHUYENVIEN, TV.THOIGIANTUVONG, DDT.NGAYRA,
                                             DECODE(TO_CHAR(BA.NHAPVIENTUPHANHE_NGOAITRU), '1', tiepnhan.thoi_gian_tiep_nhan, BA.NGAYNHAPVIEN), V_TONGSONGAYDIEUTRI), '3', '0'), 3, 1)
                    END AS SO_NGAY_DIEU_TRI_RP_3,
                    --END Tính số ngày điều trị V2

                     CASE  NVL(XV.TINHTRANG_RV, 0)
                       WHEN 1 THEN 1 --1. Ra viện
                       WHEN 3 THEN 3 --3. Bỏ về
                       WHEN 4 THEN 2 --2. Xin về
                      ELSE NVL(XV.TINHTRANG_RV, 0)
                     END AS HINHTHUCRAVIEN,
                     CASE  NVL(XV.TINHTRANG_RV, 0)
                       WHEN 1 THEN '1' --1. Ra viện
                       WHEN 3 THEN '3' --3. Bỏ về
                       WHEN 4 THEN '2' --2. Xin về
                      ELSE to_char(NVL(XV.TINHTRANG_RV, 0))
                     END AS HINHTHUCRAVIEN_RP,

                     /*Chẩn đoán nơi chuyển đến*/
                     NVL(NVL(trim(CHANDOAN.ICD_NOICHUYENDEN_TEN), BA.CHANDOAN_NOIGIOITHIEU), ' ') AS TEN_ICD_NHAPVIEN,
                     NVL(trim(CHANDOAN.ICD_NOICHUYENDEN), NVL(BA.ICD_NOI_CHUYEN, ' ')) AS ICD_NHAPVIEN,
                     VBA_SUBSTR_ICD(4, NVL(trim(CHANDOAN.ICD_NOICHUYENDEN), BA.ICD_NOI_CHUYEN), 1) ICD_NHAPVIEN_1,
                     VBA_SUBSTR_ICD(4, NVL(trim(CHANDOAN.ICD_NOICHUYENDEN), BA.ICD_NOI_CHUYEN), 2) ICD_NHAPVIEN_2,
                     VBA_SUBSTR_ICD(4, NVL(trim(CHANDOAN.ICD_NOICHUYENDEN), BA.ICD_NOI_CHUYEN), 3) ICD_NHAPVIEN_3,
                     VBA_SUBSTR_ICD(4, NVL(trim(CHANDOAN.ICD_NOICHUYENDEN), BA.ICD_NOI_CHUYEN), 4) ICD_NHAPVIEN_4,

                     /*Chẩn đoán KKB, Cấp cứu*/
                     NVL(trim(CHANDOAN.ICD_KKB_TEN), DECODE(BA.NHAPVIENTUPHANHE_NGOAITRU, 1, substr(KB.CHANDOANBENH, instr(KB.CHANDOANBENH, '-') + 1), BA.TENBENHCHINH_NHAPVIEN)) AS TENICD_KHOAKB_CAPCUU,
                     NVL(trim(CHANDOAN.ICD_KKB),DECODE(BA.NHAPVIENTUPHANHE_NGOAITRU, 1, KB.ICD, BA.ICD_NHAPVIEN)) AS MAICD_KHOAKB_CAPCUU,
                     VBA_SUBSTR_ICD(4, NVL(trim(CHANDOAN.ICD_KKB),DECODE(BA.NHAPVIENTUPHANHE_NGOAITRU, 1, KB.ICD, BA.ICD_NHAPVIEN)), 1) MAICD_KHOAKB_CAPCUU_1,
                     VBA_SUBSTR_ICD(4, NVL(trim(CHANDOAN.ICD_KKB),DECODE(BA.NHAPVIENTUPHANHE_NGOAITRU, 1, KB.ICD, BA.ICD_NHAPVIEN)), 2) MAICD_KHOAKB_CAPCUU_2,
                     VBA_SUBSTR_ICD(4, NVL(trim(CHANDOAN.ICD_KKB),DECODE(BA.NHAPVIENTUPHANHE_NGOAITRU, 1, KB.ICD, BA.ICD_NHAPVIEN)), 3) MAICD_KHOAKB_CAPCUU_3,
                     VBA_SUBSTR_ICD(4, NVL(trim(CHANDOAN.ICD_KKB),DECODE(BA.NHAPVIENTUPHANHE_NGOAITRU, 1, KB.ICD, BA.ICD_NHAPVIEN)), 4) MAICD_KHOAKB_CAPCUU_4,


                     --nvl(NVL(CHANDOAN.ICD_KHOADT_TEN, LOGPB.TENICD_KHOADIEUTRI), ' ') AS TENICD_KHOADIEUTRI,
                     nvl(NVL(TRIM(CHANDOAN.ICD_KHOADT_TEN), LOGPB.TENICD_KHOADIEUTRI),
                         NVL(NVL(CHANDOAN.ICD_BENHCHINH_RV_TEN, M_TENICD_BENHRAVIEN), ' ')) AS TENICD_KHOADIEUTRI,
                     nvl(NVL(TRIM(CHANDOAN.ICD_KHOADT_TEN), LOGPB.TENICD_KHOADIEUTRI),
                         NVL(NVL(CHANDOAN.ICD_BENHCHINH_RV_TEN, M_TENICD_BENHRAVIEN), ' ')) AS TENICD_KHOADIEUTRIV2,
                     --nvl(NVL(CHANDOAN.ICD_KHOADT, LOGPB.ICD_KHOADIEUTRI), ' ') AS ICD_KHOADIEUTRI,
                     nvl(NVL(TRIM(CHANDOAN.ICD_KHOADT), LOGPB.ICD_KHOADIEUTRI),
                         NVL(nvl(CHANDOAN.ICD_BENHCHINH_RV, M_ICD_BENHRAVIEN), ' ')) AS ICD_KHOADIEUTRI,
                     nvl(NVL(TRIM(CHANDOAN.ICD_KHOADT), LOGPB.ICD_KHOADIEUTRI),
                         NVL(nvl(CHANDOAN.ICD_BENHCHINH_RV, M_ICD_BENHRAVIEN), ' ')) AS ICD_KHOADIEUTRIV2,
                     /*NVL(SUBSTR(nvl(CHANDOAN.ICD_KHOADT,LOGPB.ICD_KHOADIEUTRI), 1, 1), ' ') AS ICD_KHOADIEUTRI_1,
                     NVL(SUBSTR(nvl(CHANDOAN.ICD_KHOADT,LOGPB.ICD_KHOADIEUTRI), 2, 1), ' ') AS ICD_KHOADIEUTRI_2,
                     NVL(DECODE(TO_CHAR(LENGTH(nvl(CHANDOAN.ICD_KHOADT,LOGPB.ICD_KHOADIEUTRI))), '3', SUBSTR(nvl(CHANDOAN.ICD_KHOADT,LOGPB.ICD_KHOADIEUTRI), 3, 1), '5',
                                SUBSTR(nvl(CHANDOAN.ICD_KHOADT,LOGPB.ICD_KHOADIEUTRI), 3, 2), '4', SUBSTR(nvl(CHANDOAN.ICD_KHOADT,LOGPB.ICD_KHOADIEUTRI), 3, 1), ' '), ' ') AS ICD_KHOADIEUTRI_3,
                     NVL(DECODE(TO_CHAR(LENGTH(nvl(CHANDOAN.ICD_KHOADT,LOGPB.ICD_KHOADIEUTRI))), '3', ' ', '5', SUBSTR(nvl(CHANDOAN.ICD_KHOADT,LOGPB.ICD_KHOADIEUTRI), 5, 1), '4', SUBSTR
                         (nvl(CHANDOAN.ICD_KHOADT,LOGPB.ICD_KHOADIEUTRI), 4, 1), ' '), ' ') AS ICD_KHOADIEUTRI_4,*/

                     /* Lay khi Khoa điều trị V2 */
                     NVL(SUBSTR(nvl(TRIM(CHANDOAN.ICD_KHOADT), LOGPB.ICD_KHOADIEUTRI), 1, 1),
                         NVL(SUBSTR(nvl(CHANDOAN.ICD_BENHCHINH_RV, M_ICD_BENHRAVIEN), 1, 1), ' ')) AS ICD_KHOADIEUTRI_1,
                     NVL(SUBSTR(nvl(TRIM(CHANDOAN.ICD_KHOADT), LOGPB.ICD_KHOADIEUTRI), 2, 1),
                         NVL(SUBSTR(nvl(CHANDOAN.ICD_BENHCHINH_RV, M_ICD_BENHRAVIEN), 2, 1), ' ')) AS ICD_KHOADIEUTRI_2,
                     NVL(TRIM(DECODE(TO_CHAR(LENGTH(nvl(CHANDOAN.ICD_KHOADT,LOGPB.ICD_KHOADIEUTRI))), '3',
                         SUBSTR(nvl(CHANDOAN.ICD_KHOADT, LOGPB.ICD_KHOADIEUTRI), 3, 1), '5',
                         SUBSTR(nvl(CHANDOAN.ICD_KHOADT, LOGPB.ICD_KHOADIEUTRI), 3, 2), '4',
                         SUBSTR(nvl(CHANDOAN.ICD_KHOADT, LOGPB.ICD_KHOADIEUTRI), 3, 1), ' ')),
                            NVL(DECODE(TO_CHAR(LENGTH(nvl(CHANDOAN.ICD_BENHCHINH_RV, M_ICD_BENHRAVIEN))), '3',
                                SUBSTR(nvl(CHANDOAN.ICD_BENHCHINH_RV, M_ICD_BENHRAVIEN), 3, 1), '5',
                                SUBSTR(nvl(CHANDOAN.ICD_BENHCHINH_RV, M_ICD_BENHRAVIEN), 3, 2), '4',
                                SUBSTR(nvl(CHANDOAN.ICD_BENHCHINH_RV, M_ICD_BENHRAVIEN), 3, 1), ' '), ' ')) AS ICD_KHOADIEUTRI_3,
                     NVL(TRIM(DECODE(TO_CHAR(LENGTH(nvl(CHANDOAN.ICD_KHOADT,LOGPB.ICD_KHOADIEUTRI))), '3', ' ', '5',
                         SUBSTR(nvl(CHANDOAN.ICD_KHOADT,LOGPB.ICD_KHOADIEUTRI), 5, 1), '4',
                         SUBSTR(nvl(CHANDOAN.ICD_KHOADT,LOGPB.ICD_KHOADIEUTRI), 4, 1), ' ')),
                            NVL(DECODE(TO_CHAR(LENGTH(nvl(CHANDOAN.ICD_BENHCHINH_RV, M_ICD_BENHRAVIEN))), '3', ' ',
                                '5', SUBSTR(nvl(CHANDOAN.ICD_BENHCHINH_RV, M_ICD_BENHRAVIEN), 5, 1),
                                '4', SUBSTR(nvl(CHANDOAN.ICD_BENHCHINH_RV, M_ICD_BENHRAVIEN), 4, 1), ' '), ' ')) AS ICD_KHOADIEUTRI_4,

                     NVL(CHANDOAN.ICD_BENHCHINH_RV_TEN, M_TENICD_BENHRAVIEN) AS TENICD_BENHRAVIEN,
                     NVL(CHANDOAN.ICD_BENHCHINH_RV_TEN, M_TENICD_BENHRAVIEN) AS TENICD_BENHRAVIENV2,
                     nvl(CHANDOAN.ICD_BENHCHINH_RV, M_ICD_BENHRAVIEN) AS ICD_BENHRAVIEN,
                     nvl(CHANDOAN.ICD_BENHCHINH_RV, M_ICD_BENHRAVIEN) AS ICD_BENHRAVIENV2,
                     NVL(SUBSTR(nvl(CHANDOAN.ICD_BENHCHINH_RV, M_ICD_BENHRAVIEN), 1, 1), ' ') AS ICD_BENHRAVIEN_1,
                     NVL(SUBSTR(nvl(CHANDOAN.ICD_BENHCHINH_RV, M_ICD_BENHRAVIEN), 2, 1), ' ') AS ICD_BENHRAVIEN_2,
                     NVL(DECODE(TO_CHAR(LENGTH(nvl(CHANDOAN.ICD_BENHCHINH_RV, M_ICD_BENHRAVIEN))), '3',
                         SUBSTR(nvl(CHANDOAN.ICD_BENHCHINH_RV, M_ICD_BENHRAVIEN), 3, 1), '5',
                         SUBSTR(nvl(CHANDOAN.ICD_BENHCHINH_RV, M_ICD_BENHRAVIEN), 3, 2), '4',
                         SUBSTR(nvl(CHANDOAN.ICD_BENHCHINH_RV, M_ICD_BENHRAVIEN), 3, 1), ' '), ' ') AS ICD_BENHRAVIEN_3,
                     NVL(DECODE(TO_CHAR(LENGTH(nvl(CHANDOAN.ICD_BENHCHINH_RV, M_ICD_BENHRAVIEN))), '3', ' ',
                         '5', SUBSTR(nvl(CHANDOAN.ICD_BENHCHINH_RV, M_ICD_BENHRAVIEN), 5, 1),
                         '4', SUBSTR(nvl(CHANDOAN.ICD_BENHCHINH_RV, M_ICD_BENHRAVIEN), 4, 1), ' '), ' ') AS ICD_BENHRAVIEN_4,
                     NVL(CHANDOAN.ICD_BENHPHU_RV_TEN, nvl(M_BENHKEMTHEO, ' ')) AS BENHKEMTHEO_RAVIEN,
                     /*Mã bệnh phụ ra viện Report*/
                     /*NVL(SUBSTR(CHANDOAN.ICD_BENHPHU_RV, 1, 1), ' ') AS ICD_BENHPHU_RAVIEN_1,
                     NVL(SUBSTR(CHANDOAN.ICD_BENHPHU_RV, 2, 1), ' ') AS ICD_BENHPHU_RAVIEN_2,
                     NVL(DECODE(TO_CHAR(LENGTH(CHANDOAN.ICD_BENHPHU_RV)), '3',
                                       SUBSTR(CHANDOAN.ICD_BENHPHU_RV, 3, 1), '5',
                                       SUBSTR(CHANDOAN.ICD_BENHPHU_RV, 3, 2), '4',
                                       SUBSTR(CHANDOAN.ICD_BENHPHU_RV, 3, 1), ' '), ' ') AS ICD_BENHPHU_RAVIEN_3,
                     NVL(DECODE(TO_CHAR(LENGTH(CHANDOAN.ICD_BENHPHU_RV)), '3', ' ',
                                       '5', SUBSTR(CHANDOAN.ICD_BENHPHU_RV, 5, 1),
                                       '4', SUBSTR(CHANDOAN.ICD_BENHPHU_RV, 4, 1), ' '), ' ') AS ICD_BENHPHU_RAVIEN_4,*/
                     /*****/
                     /*Mã bệnh phụ ra viện Report V2*/
                     NVL(SUBSTR(NVL(REGEXP_SUBSTR(CHANDOAN.ICD_BENHPHU_RV, '^[^;]+'),
                                NVL(REGEXP_SUBSTR(CHANDOAN.ICD_BENHPHU_RV_TEN, '\(([^)]+)\)', 1, 1, NULL, 1), ' ')), 1, 1), ' ') AS ICD_BENHPHU_RAVIEN_1,
                     NVL(SUBSTR(NVL(REGEXP_SUBSTR(CHANDOAN.ICD_BENHPHU_RV, '^[^;]+'),
                                NVL(REGEXP_SUBSTR(CHANDOAN.ICD_BENHPHU_RV_TEN, '\(([^)]+)\)', 1, 1, NULL, 1), ' ')), 2, 1), ' ') AS ICD_BENHPHU_RAVIEN_2,
                     NVL(DECODE(TO_CHAR(LENGTH(NVL(REGEXP_SUBSTR(CHANDOAN.ICD_BENHPHU_RV, '^[^;]+'),
                                NVL(REGEXP_SUBSTR(CHANDOAN.ICD_BENHPHU_RV_TEN, '\(([^)]+)\)', 1, 1, NULL, 1), ' ')))),
                                    '3', SUBSTR(NVL(REGEXP_SUBSTR(CHANDOAN.ICD_BENHPHU_RV, '^[^;]+'),
                                                NVL(REGEXP_SUBSTR(CHANDOAN.ICD_BENHPHU_RV_TEN, '\(([^)]+)\)', 1, 1, NULL, 1), ' ')), 3, 1),
                                    '5', SUBSTR(NVL(REGEXP_SUBSTR(CHANDOAN.ICD_BENHPHU_RV, '^[^;]+'),
                                                NVL(REGEXP_SUBSTR(CHANDOAN.ICD_BENHPHU_RV_TEN, '\(([^)]+)\)', 1, 1, NULL, 1), ' ')), 3, 2),
                                    '4', SUBSTR(NVL(REGEXP_SUBSTR(CHANDOAN.ICD_BENHPHU_RV, '^[^;]+'),
                                                NVL(REGEXP_SUBSTR(CHANDOAN.ICD_BENHPHU_RV_TEN, '\(([^)]+)\)', 1, 1, NULL, 1), ' ')), 3, 1), ' '), ' ') AS ICD_BENHPHU_RAVIEN_3,
                     NVL(DECODE(TO_CHAR(LENGTH(NVL(REGEXP_SUBSTR(CHANDOAN.ICD_BENHPHU_RV, '^[^;]+'),
                                NVL(REGEXP_SUBSTR(CHANDOAN.ICD_BENHPHU_RV_TEN, '\(([^)]+)\)', 1, 1, NULL, 1), ' ')))), '3', ' ',
                                    '5', SUBSTR(NVL(REGEXP_SUBSTR(CHANDOAN.ICD_BENHPHU_RV, '^[^;]+'),
                                                NVL(REGEXP_SUBSTR(CHANDOAN.ICD_BENHPHU_RV_TEN, '\(([^)]+)\)', 1, 1, NULL, 1), ' ')), 5, 1),
                                    '4', SUBSTR(NVL(REGEXP_SUBSTR(CHANDOAN.ICD_BENHPHU_RV, '^[^;]+'),
                                                NVL(REGEXP_SUBSTR(CHANDOAN.ICD_BENHPHU_RV_TEN, '\(([^)]+)\)', 1, 1, NULL, 1), ' ')), 4, 1), ' '), ' ') AS ICD_BENHPHU_RAVIEN_4,
                     /*****/
                     NVL(CHANDOAN.THUTHUAT_PHAUTHUAT, 0) AS THUTHUAT_PHAUTHUAT,
                     TO_CHAR(NVL(CHANDOAN.THUTHUAT_PHAUTHUAT, 0)) AS THUTHUAT_PHAUTHUAT_RP,
                     NVL(CHANDOAN.TAIBIEN_BIENCHUNG, 0) AS TAIBIEN_BIENCHUNG,
                     TO_CHAR(NVL(CHANDOAN.TAIBIEN_BIENCHUNG, 0)) AS TAIBIEN_BIENCHUNG_RP,
                     CASE DECODE(TO_CHAR(DDT.TT_DOTDIEUTRI), '3', XV.KETQUADIEUTRI, '4', CV.KETQUA_DIEUTRI,
                                 '7', BA.KETQUADIEUTRI, '0')
                         WHEN 1   THEN
                             'Khỏi'
                         WHEN 2   THEN
                             'Đỡ'
                         WHEN 3   THEN
                             'Không thay đổi'
                         WHEN 4   THEN
                             'Nặng hơn'
                         WHEN 5   THEN
                             'Tử vong'
                         WHEN 6   THEN
                             'Tiên lượng tử vong'
                         ELSE
                             ' '
                     END AS KETQUA_DIEUTRI,
--                      DECODE(TO_CHAR(DDT.TT_DOTDIEUTRI), '3', XV.KETQUADIEUTRI, '4', CV.KETQUA_DIEUTRI,
--                             '7', BA.KETQUADIEUTRI, '0')
                     CASE WHEN nvl(ravien.ketquadieutri, 0) > 0 THEN
                          nvl(ravien.ketquadieutri, 0)
                       ELSE
                         DECODE(TO_CHAR(DDT.TT_DOTDIEUTRI), '3', XV.KETQUADIEUTRI, '4', CV.KETQUA_DIEUTRI, '7', BA.KETQUADIEUTRI, '0')
                     END AS KETQUA_DIEUTRI_INT,
                     CASE WHEN nvl(ravien.ketquadieutri, 0) > 0 THEN
                          to_char(nvl(ravien.ketquadieutri,0))
                       ELSE
                         TO_CHAR(DECODE(TO_CHAR(DDT.TT_DOTDIEUTRI), '3', XV.KETQUADIEUTRI, '4', CV.KETQUA_DIEUTRI, '7', BA.KETQUADIEUTRI, '0'))
                     END AS KETQUADIEUTRI_RP,
--                      NVL(
--                              CASE
--                                  WHEN DDT.TT_DOTDIEUTRI = 5 THEN
--                                      TO_CHAR(NVL(RAVIEN.NGAYGIO_TV, TV.THOIGIANTUVONG), 'HH24 "giờ" MI "phút, ngày" DD "tháng" MM "năm" YYYY')
--                                  END, ' ') AS THOI_GIAN_TU_VONG_TEXT,
--                      NVL(
--                              CASE
--                                  WHEN DDT.TT_DOTDIEUTRI = 5 THEN
--                                      TO_CHAR(NVL(RAVIEN.NGAYGIO_TV, TV.THOIGIANTUVONG), 'DD/MM/YYYY HH24:MI:SS')
--                                  END, ' ') AS THOI_GIAN_TU_VONG,
                     NVL(TO_CHAR(NVL(RAVIEN.NGAYGIO_TV, TV.THOIGIANTUVONG), 'HH24 "giờ" MI "phút, ngày" DD "tháng" MM "năm" YYYY'), ' ') AS THOI_GIAN_TU_VONG_TEXT,
                     NVL(TO_CHAR(NVL(RAVIEN.NGAYGIO_TV, TV.THOIGIANTUVONG), 'DD/MM/YYYY HH24:MI:SS'), ' ') AS THOI_GIAN_TU_VONG,
                     NVL(TV.NGUYENNHANTUVONG, ' ') AS NGUYENNHANTUVONG,
                     nvl(RAVIEN.GIAIPHAUBENH, 0) AS GIAIPHAUBENH,
                     TO_CHAR(nvl(RAVIEN.GIAIPHAUBENH, 0)) AS GIAIPHAUBENH_RP,
                     nvl(RAVIEN.NN_TUVONG, 0) AS NN_TUVONG,
                     TO_CHAR (nvl(RAVIEN.NN_TUVONG, 0)) AS NN_TUVONG_RP,

                     TO_CHAR(nvl(RAVIEN.KHOANGTG_TUVONG, 0)) AS KHOANGTG_TUVONG,
                     NVL(RAVIEN.ICD_TUVONG, ' ') AS ICD_TUVONG,
                     NVL(ICD_TUVONG.MO_TA_BENH_LY, ' ') AS TEN_ICD_TUVONG,
                     NVL(SUBSTR(RAVIEN.ICD_TUVONG, 1, 1), ' ') AS ICD_TUVONG_1,
                     NVL(SUBSTR(RAVIEN.ICD_TUVONG, 2, 1), ' ') AS ICD_TUVONG_2,
                     NVL(DECODE(TO_CHAR(LENGTH(RAVIEN.ICD_TUVONG)),
                                '3', SUBSTR(RAVIEN.ICD_TUVONG, 3, 1),
                                '5', SUBSTR(RAVIEN.ICD_TUVONG, 3, 2),
                                '4', SUBSTR(RAVIEN.ICD_TUVONG, 3, 1), ' '), ' ') AS ICD_TUVONG_3,
                     NVL(DECODE(TO_CHAR(LENGTH(RAVIEN.ICD_TUVONG)), '3', ' ',
                                '5', SUBSTR(RAVIEN.ICD_TUVONG, 5, 1),
                                '4', SUBSTR(RAVIEN.ICD_TUVONG, 4, 1), ' '), ' ') AS ICD_TUVONG_4,
                     nvl(RAVIEN.KHAMNGHIEM, 0) AS KHAMNGHIEM,
                        TO_CHAR (nvl(RAVIEN.KHAMNGHIEM, 0)) AS KHAMNGHIEM_RP,
                     NVL(RAVIEN.ICD_GIAIPHAU, ' ') AS ICD_GIAIPHAU,
                     NVL(ICD_GIAIPHAU.MO_TA_BENH_LY, ' ') AS TEN_ICD_GIAIPHAU,
                     NVL(SUBSTR(RAVIEN.ICD_GIAIPHAU, 1, 1), ' ') AS ICD_GIAIPHAU_1,
                     NVL(SUBSTR(RAVIEN.ICD_GIAIPHAU, 2, 1), ' ') AS ICD_GIAIPHAU_2,
                     NVL(DECODE(TO_CHAR(LENGTH(RAVIEN.ICD_GIAIPHAU)),
                                '3', SUBSTR(RAVIEN.ICD_GIAIPHAU, 3, 1),
                                '5', SUBSTR(RAVIEN.ICD_GIAIPHAU, 3, 2),
                                '4', SUBSTR(RAVIEN.ICD_GIAIPHAU, 3, 1), ' '), ' ') AS ICD_GIAIPHAU_3,
                     NVL(DECODE(TO_CHAR(LENGTH(RAVIEN.ICD_GIAIPHAU)), '3', ' ',
                                '5', SUBSTR(RAVIEN.ICD_GIAIPHAU, 5, 1),
                                '4', SUBSTR(RAVIEN.ICD_GIAIPHAU, 4, 1), ' '), ' ') AS ICD_GIAIPHAU_4


                     ,NVL(KHOAVAO.TEN_PHONGBAN, '') AS TENKHOA0,
                     NVL(TO_CHAR(QLNB.NGAYGIOVAOKHOA, 'HH24 "giờ" MI "phút," DD "/" MM "/" YYYY'), '') AS CHUYENKHOATHOIGIANTEXT0,
                     NVL(TO_CHAR(QLNB.NGAYGIOVAOKHOA, 'HH24 "giờ" MI "phút," DD "/" MM "/" YYYY'), '') AS CHUYENKHOATHOIGIANTEXT0V2,
                     NVL(TO_CHAR(QLNB.NGAYGIOVAOKHOA, 'DD/MM/YYYY HH24:MI:SS'), '') AS CHUYENKHOATHOIGIAN0,
                     DECODE(NVL(TO_CHAR(QLNB.SONGAY_DIEUTRI), '0'), '0','',TO_CHAR(QLNB.SONGAY_DIEUTRI)) AS CHUYENKHOASONGAY0,
                     DECODE(LENGTH(TO_CHAR(QLNB.SONGAY_DIEUTRI)),
                            1, ' ',
                            2, ' ',
                            3, SUBSTR(TO_CHAR(QLNB.SONGAY_DIEUTRI), 1, 1)
                         ) AS CHUYENKHOASONGAY0_0,
                     SUBSTR(LPAD(QLNB.SONGAY_DIEUTRI, '2', '0'), 1, 1)  AS CHUYENKHOASONGAY0_1,
                     SUBSTR(LPAD(QLNB.SONGAY_DIEUTRI, '2', '0'), 2, 1)  AS CHUYENKHOASONGAY0_2,

                     NVL(QLNB.MAKHOA_CHUYEN01, '') AS MAKHOA1,
                     NVL(CK1.TEN_PHONGBAN, '') AS TENKHOA1,
                     NVL(TO_CHAR(QLNB.NGAYGIOCHUYENKHOA01, 'HH24 "giờ" MI "phút," DD "/" MM "/" YYYY'), '') AS CHUYENKHOATHOIGIANTEXT1,
                     NVL(TO_CHAR(QLNB.NGAYGIOCHUYENKHOA01, 'DD/MM/YYYY HH24:MI:SS'), '') AS CHUYENKHOATHOIGIAN1,
                     DECODE(NVL(TO_CHAR(QLNB.SN_DIEUTRI_CK01), '0'), '0','',TO_CHAR(QLNB.SN_DIEUTRI_CK01)) AS CHUYENKHOASONGAY1,
                     DECODE(LENGTH(TO_CHAR(QLNB.SN_DIEUTRI_CK01)),
                            1, ' ',
                            2, ' ',
                            3, SUBSTR(TO_CHAR(QLNB.SN_DIEUTRI_CK01), 1, 1)
                         ) AS CHUYENKHOASONGAY1_0,
                     SUBSTR(LPAD(QLNB.SN_DIEUTRI_CK01, '2', '0'), 1, 1) AS CHUYENKHOASONGAY1_1,
                     SUBSTR(LPAD(QLNB.SN_DIEUTRI_CK01, '2', '0'), 2, 1) AS CHUYENKHOASONGAY1_2,

                     NVL(QLNB.MAKHOA_CHUYEN02, '') AS MAKHOA2,
                     NVL(CK2.TEN_PHONGBAN, '') AS TENKHOA2,
                     NVL(TO_CHAR(QLNB.NGAYGIOCHUYENKHOA02, 'HH24 "giờ" MI "phút," DD "/" MM "/" YYYY'), '') AS CHUYENKHOATHOIGIANTEXT2,
                     NVL(TO_CHAR(QLNB.NGAYGIOCHUYENKHOA02, 'DD/MM/YYYY HH24:MI:SS'), '') AS CHUYENKHOATHOIGIAN2,
                     DECODE(NVL(TO_CHAR(QLNB.SN_DIEUTRI_CK02), '0'), '0','',TO_CHAR(QLNB.SN_DIEUTRI_CK02)) AS CHUYENKHOASONGAY2,
                     DECODE(LENGTH(TO_CHAR(QLNB.SN_DIEUTRI_CK02)),
                            1, ' ',
                            2, ' ',
                            3, SUBSTR(TO_CHAR(QLNB.SN_DIEUTRI_CK02), 1, 1)
                         ) AS CHUYENKHOASONGAY2_0,
                     SUBSTR(LPAD(QLNB.SN_DIEUTRI_CK02, '2', '0'), 1, 1) AS CHUYENKHOASONGAY2_1,
                     SUBSTR(LPAD(QLNB.SN_DIEUTRI_CK02, '2', '0'), 2, 1) AS CHUYENKHOASONGAY2_2,


                     NVL(QLNB.MAKHOA_CHUYEN03, '') AS MAKHOA3,
                     NVL(CK3.TEN_PHONGBAN, '') AS TENKHOA3,
                     NVL(TO_CHAR(QLNB.NGAYGIOCHUYENKHOA03, 'HH24 "giờ" MI "phút," DD "/" MM "/" YYYY'), '') AS CHUYENKHOATHOIGIANTEXT3,
                     NVL(TO_CHAR(QLNB.NGAYGIOCHUYENKHOA03, 'DD/MM/YYYY HH24:MI:SS'), '') AS CHUYENKHOATHOIGIAN3,
                     DECODE(NVL(TO_CHAR(QLNB.SN_DIEUTRI_CK03), '0'), '0','',TO_CHAR(QLNB.SN_DIEUTRI_CK03)) AS CHUYENKHOASONGAY3,
                     DECODE(LENGTH(TO_CHAR(QLNB.SN_DIEUTRI_CK03)),
                            1, ' ',
                            2, ' ',
                            3, SUBSTR(TO_CHAR(QLNB.SN_DIEUTRI_CK03), 1, 1)
                         ) AS CHUYENKHOASONGAY3_0,
                     SUBSTR(LPAD(QLNB.SN_DIEUTRI_CK03, '2', '0'), 1, 1) AS CHUYENKHOASONGAY3_1,
                     SUBSTR(LPAD(QLNB.SN_DIEUTRI_CK03, '2', '0'), 2, 1) AS CHUYENKHOASONGAY3_2,
                        to_char(nvl(CHANDOAN.SONGAY_SAUPHAUTHUAT, 0)) as SONGAY_SAUPHAUTHUAT,
                        CASE WHEN thamso960606 = 1 THEN
                            TO_CHAR(p_solanphauthuat)
                        else
                            to_char(nvl(CHANDOAN.SOLAN_PHAUTHUAT, 0))
                        end as SOLAN_PHAUTHUAT,

                     decode(CHANDOAN.SONGAY_SAUPHAUTHUAT, null, '',
                            decode(to_char(length(nvl(CHANDOAN.SONGAY_SAUPHAUTHUAT, 0))), '1', '0', SUBSTR(TO_CHAR(CHANDOAN.SONGAY_SAUPHAUTHUAT), 1, 1))) as SONGAY_SAUPHAUTHUAT_1,
                     decode(CHANDOAN.SONGAY_SAUPHAUTHUAT, null, '',
                            decode(to_char(length(nvl(CHANDOAN.SONGAY_SAUPHAUTHUAT, 0))), '1', to_char(CHANDOAN.SONGAY_SAUPHAUTHUAT), SUBSTR(TO_CHAR(CHANDOAN.SONGAY_SAUPHAUTHUAT), 2, 1))) as SONGAY_SAUPHAUTHUAT_2,
                     DECODE(LENGTH(TO_CHAR(CHANDOAN.SONGAY_SAUPHAUTHUAT)),
                            1,
                            '',
                            2,
                            '',
                            3,
                            SUBSTR(TO_CHAR(CHANDOAN.SONGAY_SAUPHAUTHUAT), 1, 1)) AS SONGAY_SAUPHAUTHUAT_MTE_1,
                     DECODE(LENGTH(TO_CHAR(CHANDOAN.SONGAY_SAUPHAUTHUAT)),
                            1,
                            '',
                            2,
                            SUBSTR(TO_CHAR(CHANDOAN.SONGAY_SAUPHAUTHUAT), 1, 1),
                            3,
                            SUBSTR(TO_CHAR(CHANDOAN.SONGAY_SAUPHAUTHUAT), 2, 1)) AS SONGAY_SAUPHAUTHUAT_MTE_2,
                     DECODE(LENGTH(TO_CHAR(CHANDOAN.SONGAY_SAUPHAUTHUAT)),
                            1,
                            TO_CHAR(CHANDOAN.SONGAY_SAUPHAUTHUAT),
                            2,
                            SUBSTR(TO_CHAR(CHANDOAN.SONGAY_SAUPHAUTHUAT), 2, 1),
                            3,
                            SUBSTR(TO_CHAR(CHANDOAN.SONGAY_SAUPHAUTHUAT), 3, 1)) AS SONGAY_SAUPHAUTHUAT_MTE_3,
                     decode(CHANDOAN.SOLAN_PHAUTHUAT, null, '',
                            decode(to_char(length(CHANDOAN.SOLAN_PHAUTHUAT)), '1', '0', SUBSTR(TO_CHAR(CHANDOAN.SOLAN_PHAUTHUAT), 1, 1))) as SOLAN_PHAUTHUAT_1,
                     decode(CHANDOAN.SOLAN_PHAUTHUAT, null, '',
                            decode(to_char(length(CHANDOAN.SOLAN_PHAUTHUAT)), '1', to_char(CHANDOAN.SOLAN_PHAUTHUAT), SUBSTR(TO_CHAR(CHANDOAN.SOLAN_PHAUTHUAT), 2, 1))) as SOLAN_PHAUTHUAT_2,

                     nvl(CHANDOAN.ICD_NGUYENNHAN, ' ') as ICD_NGUYENNHAN_BONG,

                     NVL(SUBSTR(nvl(CHANDOAN.ICD_NGUYENNHAN, ' '), 1, 1), ' ') AS ICD_NGUYENNHAN_BONG_1,
                     NVL(SUBSTR(nvl(CHANDOAN.ICD_NGUYENNHAN, ' '), 2, 1), ' ') AS ICD_NGUYENNHAN_BONG_2,
                     NVL(DECODE(TO_CHAR(LENGTH(nvl(CHANDOAN.ICD_NGUYENNHAN, ' '))), '3',
                                SUBSTR(nvl(CHANDOAN.ICD_NGUYENNHAN, ' '), 3, 1), '5',
                                SUBSTR(nvl(CHANDOAN.ICD_NGUYENNHAN, ' '), 3, 2), '4',
                                SUBSTR(nvl(CHANDOAN.ICD_NGUYENNHAN, ' '), 3, 1), ' '), ' ') AS ICD_NGUYENNHAN_BONG_3,
                     NVL(DECODE(TO_CHAR(LENGTH(nvl(CHANDOAN.ICD_NGUYENNHAN, ' '))), '3', ' ',
                                '5', SUBSTR(nvl(CHANDOAN.ICD_NGUYENNHAN, ' '), 5, 1),
                                '4', SUBSTR(nvl(CHANDOAN.ICD_NGUYENNHAN, ' '), 4, 1), ' '), ' ') AS ICD_NGUYENNHAN_BONG_4,
                     nvl(CHANDOAN.TENICD_NGUYENNHAN, ' ') as TENICD_NGUYENNHAN_BONG,

                     nvl(CHANDOAN.ICD_TRUOC_PHAUTHUAT, ' ') as ICD_TRUOC_PHAUTHUAT,
                     NVL(SUBSTR(nvl(CHANDOAN.ICD_TRUOC_PHAUTHUAT, ' '), 1, 1), ' ') AS ICD_TRUOC_PHAUTHUAT_1,
                     NVL(SUBSTR(nvl(CHANDOAN.ICD_TRUOC_PHAUTHUAT, ' '), 2, 1), ' ') AS ICD_TRUOC_PHAUTHUAT_2,
                     NVL(DECODE(TO_CHAR(LENGTH(nvl(CHANDOAN.ICD_TRUOC_PHAUTHUAT, ' '))), '3',
                                SUBSTR(nvl(CHANDOAN.ICD_TRUOC_PHAUTHUAT, ' '), 3, 1), '5',
                                SUBSTR(nvl(CHANDOAN.ICD_TRUOC_PHAUTHUAT, ' '), 3, 2), '4',
                                SUBSTR(nvl(CHANDOAN.ICD_TRUOC_PHAUTHUAT, ' '), 3, 1), ' '), ' ') AS ICD_TRUOC_PHAUTHUAT_3,
                     NVL(DECODE(TO_CHAR(LENGTH(nvl(CHANDOAN.ICD_TRUOC_PHAUTHUAT, ' '))), '3', ' ',
                                '5', SUBSTR(nvl(CHANDOAN.ICD_TRUOC_PHAUTHUAT, ' '), 5, 1),
                                '4', SUBSTR(nvl(CHANDOAN.ICD_TRUOC_PHAUTHUAT, ' '), 4, 1), ' '), ' ') AS ICD_TRUOC_PHAUTHUAT_4,
                     nvl(CHANDOAN.TENICD_TRUOC_PHAUTHUAT, ' ') as TENICD_TRUOC_PHAUTHUAT,


                     nvl(CHANDOAN.ICD_SAU_PHAUTHUAT, ' ') as ICD_SAU_PHAUTHUAT,
                     NVL(SUBSTR(nvl(CHANDOAN.ICD_SAU_PHAUTHUAT, ' '), 1, 1), ' ') AS ICD_SAU_PHAUTHUAT_1,
                     NVL(SUBSTR(nvl(CHANDOAN.ICD_SAU_PHAUTHUAT, ' '), 2, 1), ' ') AS ICD_SAU_PHAUTHUAT_2,
                     NVL(DECODE(TO_CHAR(LENGTH(nvl(CHANDOAN.ICD_SAU_PHAUTHUAT, ' '))), '3',
                                SUBSTR(nvl(CHANDOAN.ICD_SAU_PHAUTHUAT, ' '), 3, 1), '5',
                                SUBSTR(nvl(CHANDOAN.ICD_SAU_PHAUTHUAT, ' '), 3, 2), '4',
                                SUBSTR(nvl(CHANDOAN.ICD_SAU_PHAUTHUAT, ' '), 3, 1), ' '), ' ') AS ICD_SAU_PHAUTHUAT_3,
                     NVL(DECODE(TO_CHAR(LENGTH(nvl(CHANDOAN.ICD_SAU_PHAUTHUAT, ' '))), '3', ' ',
                                '5', SUBSTR(nvl(CHANDOAN.ICD_SAU_PHAUTHUAT, ' '), 5, 1),
                                '4', SUBSTR(nvl(CHANDOAN.ICD_SAU_PHAUTHUAT, ' '), 4, 1), ' '), ' ') AS ICD_SAU_PHAUTHUAT_4,
                     nvl(CHANDOAN.TENICD_SAU_PHAUTHUAT, ' ') as TENICD_SAU_PHAUTHUAT,
                     nvl(CHANDOAN.NN_TAIBIEN_BIENCHUNG, 0) NN_TAIBIEN_BIENCHUNG,
                     /*COLUMN NAME DEFAULT OF TABLE*/
                     nvl(CHANDOAN.ICD_NGUYENNHAN, ' ') as ICD_NGUYENNHAN,
                     nvl(CHANDOAN.TENICD_NGUYENNHAN, ' ') as TENICD_NGUYENNHAN,
                     nvl(CHANDOAN.ICD_BENHPHU_RV, ' ') as ICD_BENHPHU_RV,
--                     nvl(CHANDOAN.ICD_BENHPHU_RV_TEN, ' ') as ICD_BENHPHU_RV_TEN,
                     NVL(CHANDOAN.ICD_BENHPHU_RV_TEN, nvl(M_BENHKEMTHEO, ' ')) AS ICD_BENHPHU_RV_TEN,
                     /*END COLUMN NAME DEFAULT OF TABLE*/
                     nvl(gcs.HO_TEN_BO, nvl(TTHC.HOTEN_BO, ' ')) as HOTEN_BO,
                     nvl(TTHC.TRINHDO_BO, ' ') as TRINHDO_BO,
                     nvl(bnme.TEN_BENH_NHAN, nvl(TTHC.HOTEN_ME, ' ')) as HOTEN_ME,
                     nvl(TTHC.TRINHDO_ME, ' ') as TRINHDO_ME,
                        nvl(TTHC.TRINHDOVANHOA, ' ') as TRINHDOVANHOA,
                     --VLG THÊM CỘT
                     nvl(CHANDOAN.ICDYHCT_NOICHUYENDEN, ' ') as ICDYHCT_NOICHUYENDEN,
                     nvl(CHANDOAN.ICDYHCT_NOICHUYENDEN_TEN, ' ') as ICDYHCT_NOICHUYENDEN_TEN,
                     nvl(CHANDOAN.ICDYHCT_KKB, ' ') as ICDYHCT_KKB,
                     nvl(CHANDOAN.ICDYHCT_KKB_TEN, ' ') as ICDYHCT_KKB_TEN,
                     nvl(CHANDOAN.ICDYHCT_KHOADT, ' ') as ICDYHCT_KHOADT,
                     nvl(CHANDOAN.ICDYHCT_KHOADT_TEN, ' ') as ICDYHCT_KHOADT_TEN,
                     TO_CHAR(nvl(CHANDOAN.YHCT_THUTHUAT_PHAUTHUAT, 0)) as YHCT_THUTHUAT_PHAUTHUAT,
                     nvl(CHANDOAN.ICDYHCT_BENHCHINH_RV, ' ') as ICDYHCT_BENHCHINH_RV,
                     nvl(CHANDOAN.ICDYHCT_BENHCHINH_RV_TEN, ' ') as ICDYHCT_BENHCHINH_RV_TEN,
                     nvl(CHANDOAN.ICDYHCT_BENHPHU_RV, NVL(M_ICD_BPRAVIENYHCT,' ')) as ICDYHCT_BENHPHU_RV,
                     nvl(CHANDOAN.ICDYHCT_BENHPHU_RV_TEN, NVL(M_TENICD_BPRAVIENYHCT,' ')) as ICDYHCT_BENHPHU_RV_TEN,
                     TO_CHAR(nvl(CHANDOAN.YHCT_TAIBIEN_BIENCHUNG, 0)) as YHCT_TAIBIEN_BIENCHUNG,
                     nvl(CHANDOAN.ICDPHU1_KHOADT, ' ') as ICDPHU1_KHOADT,
                     nvl(CHANDOAN.ICDPHU1_KHOADT_TEN, ' ') as ICDPHU1_KHOADT_TEN,
                     nvl(CHANDOAN.ICDPHU2_KHOADT, ' ') as ICDPHU2_KHOADT,
                     nvl(CHANDOAN.ICDPHU2_KHOADT_TEN, ' ') as ICDPHU2_KHOADT_TEN,
                     nvl(CHANDOAN.ICDYHCTPHU1_KHOADT, ' ') as ICDYHCTPHU1_KHOADT,
                     nvl(CHANDOAN.ICDYHCTPHU1_KHOADT_TEN, ' ') as ICDYHCTPHU1_KHOADT_TEN,
                     nvl(CHANDOAN.ICDYHCTPHU2_KHOADT, ' ') as ICDYHCTPHU2_KHOADT,
                     nvl(CHANDOAN.ICDYHCTPHU2_KHOADT_TEN, ' ') as ICDYHCTPHU2_KHOADT_TEN,
                     nvl(REGEXP_SUBSTR(his_manager.CAT_MA_ICD(CHANDOAN.ICD_BENHPHU_RV), '[^;]+', 1, 1),
                         nvl(REGEXP_SUBSTR(CHANDOAN.ICD_BENHPHU_RV_TEN, '\(([^)]+)\)', 1, 1, NULL, 1), ' ')) AS ICDBENHKEMTHEO_RAVIEN,
                     nvl(SUBSTR(his_manager.CAT_MA_ICD(CHANDOAN.ICD_BENHPHU_RV_TEN), Instr(his_manager.CAT_MA_ICD(CHANDOAN.ICD_BENHPHU_RV_TEN), ';', -1, 1) +1),' ') AS ICDBENHKEMTHEO_RAVIEN_R,
                     nvl(REGEXP_SUBSTR(his_manager.CAT_MA_ICD(CHANDOAN.ICDYHCT_BENHPHU_RV_TEN), '[^;]+', 1, 1),' ') AS ICDYHCTBENHKEMTHEO_RAVIEN,
                     nvl(trim(CHANDOAN.ICD_KKB), nvl(KB.ICD, ' ')) as ICD_KKB,
                     nvl(trim(CHANDOAN.ICD_KKB_TEN), KB.TEN_BENH_THEOBS) as ICD_KKB_TEN,
                     nvl(CHANDOAN.ICD_KHOADT, nvl(LOGPB.ICD_KHOADIEUTRI, ' ')) as ICD_KHOADT,
                     --nvl(CHANDOAN.ICD_KHOADT_TEN, nvl(LOGPB.TENICD_KHOADIEUTRI, ' ')) as ICD_KHOADT_TEN,
                     nvl(TRIM(CHANDOAN.ICD_KHOADT_TEN),
                         nvl(LOGPB.TENICD_KHOADIEUTRI,
                             NVL(NVL(CHANDOAN.ICD_BENHCHINH_RV_TEN, M_TENICD_BENHRAVIEN), ' '))) as ICD_KHOADT_TEN,
                     nvl(CHANDOAN.BENHDANH, ' ') as BENHDANH,
                     nvl(CHANDOAN.BATCUONG, ' ') as BATCUONG,
                     nvl(CHANDOAN.TANGPHU, ' ') as TANGPHU,
                     nvl(CHANDOAN.KINHMACH, ' ') as KINHMACH,
                     nvl(CHANDOAN.NGUYENNHAN, ' ') as NGUYENNHAN,
                     --END VLG
                     /*begin sanKhoa*/
                     NVL(trim(CHANDOAN.ICD_LUCVAODE),BA.ICD_NHAPVIEN) ICD_LUCVAODE,
                     NVL(trim(CHANDOAN.TEN_ICD_LUCVAODE), BA.TENBENHCHINH_NHAPVIEN ) TEN_ICD_LUCVAODE,
                     CHANDOAN.NGAY_VAODE ,
                     CHANDOAN.NGOI_THAI,
                     CHANDOAN.CACHTHUC_DE,
                     CHANDOAN.KIEMSOAT_TUCUNG,
                     CHANDOAN.TINHHINH_PHAUTHUAT,
                     CHANDOAN.PHUONGPHAP_PHAUTHUAT,
                     CHANDOAN.DONTHAI_DATHAI,
                     CHANDOAN.TRESOSINH_GIOITINH,
                     CHANDOAN.TRESOSINH_TINHTRANG,
                     JSON_VALUE(CHANDOAN.DI_TAT,  '$.thongTinDiTat' RETURNING VARCHAR2) DI_TAT,
                     JSON_VALUE(CHANDOAN.DI_TAT,  '$.isDiTat' RETURNING VARCHAR2) IS_DITAT,
                     CHANDOAN.CAN_NANG
                     /*end sanKhoa*/
                      ,NVL(TO_CHAR(TTHC.CHUNGNHAN_KHUYETTAT), '-1') AS CHUNGNHAN_KHUYETTAT
                     ,NVL(TTHC.DANG_KHUYETTAT, ' ') AS DANG_KHUYETTAT
                     ,NVL(TTHC.MUCDO_KHUYETTAT, ' ') AS MUCDO_KHUYETTAT
                     , NVL(CHANDOAN.CHANDOAN_T, ' ') as CHANDOAN_T
                     , NVL(CHANDOAN.CHANDOAN_N, ' ') as CHANDOAN_N
                     , NVL(CHANDOAN.CHANDOAN_M, ' ') as CHANDOAN_M
                     , NVL(CHANDOAN.CHANDOAN_GIAIDOAN, ' ') as CHANDOAN_GIAIDOAN
                     , NVL(CHANDOAN.CHANDOAN_RAVIEN_T, ' ') as CHANDOAN_RAVIEN_T
                     , NVL(CHANDOAN.CHANDOAN_RAVIEN_N, ' ') as CHANDOAN_RAVIEN_N
                     , NVL(CHANDOAN.CHANDOAN_RAVIEN_M, ' ') as CHANDOAN_RAVIEN_M
                     , NVL(CHANDOAN.CHANDOAN_RAVIEN_GIAIDOAN, ' ') as CHANDOAN_RAVIEN_GIAIDOAN,
                     /*begin BA Sơ sinh*/
--                     nvl(bnme.TEN_BENH_NHAN, JSON_VALUE(TTHC.TTHC_ME,  '$.hoTenMe' RETURNING VARCHAR2)) HOTENME,
                     --nvl(bnme.TEN_BENH_NHAN, nvl(BN.HO_TEN_ME, JSON_VALUE(TTHC.TTHC_ME,  '$.hoTenMe' RETURNING VARCHAR2))) HOTENME,
                     nvl(NV_NT.HOTEN_ME,
                     nvl(bnme.TEN_BENH_NHAN, nvl(BN.HO_TEN_ME, JSON_VALUE(TTHC.TTHC_ME,  '$.hoTenMe' RETURNING VARCHAR2)))
                     ) HOTENME,
                     nvl(TO_CHAR(bnme.NGAY_SINH, 'DD/MM/YYYY'), JSON_VALUE(TTHC.TTHC_ME,  '$.ngaySinhCuaMe' RETURNING VARCHAR2)) NGAYSINHCUAME,
                     --nvl(nnme.ten_nghe_nghiep, JSON_VALUE(TTHC.TTHC_ME,  '$.ngheNghiepCuaMe' RETURNING VARCHAR2)) NGHENGHIEPCUAME,
                     nvl(nnmenv.ten_nghe_nghiep,
                        nvl(nnme.ten_nghe_nghiep, JSON_VALUE(TTHC.TTHC_ME,  '$.ngheNghiepCuaMe' RETURNING VARCHAR2))
                     ) NGHENGHIEPCUAME,
                     TTHC.TTHC_ME NGHENGHENGHE,
                     nnmenv.ten_nghe_nghiep NGHENGHENGHE2,
                     nnme.ten_nghe_nghiep NGHENGHENGHE3,
                     nvl(TO_CHAR(bnme.ma_nghe_nghiep), JSON_VALUE(TTHC.TTHC_ME,  '$.maNgheNghiepCuaMe' RETURNING VARCHAR2)) MA_NGHENGHIEP_CUAME,
                     JSON_VALUE(TTHC.TTHC_ME,  '$.maTrinhDoCuaMe' RETURNING VARCHAR2) MA_TRINHDO_CUAME,
                     nvl(bnme.ma_nghe_nghiep, JSON_VALUE(TTHC.TTHC_ME,  '$.maNgheNghiepCuaMe' RETURNING VARCHAR2)) MA_NGHENGHIEP_CUAME_FORMIO,
                     nvl(TO_CHAR(gcs.SO_LAN_SINH), JSON_VALUE(TTHC.TTHC_ME,  '$.soLanDe' RETURNING VARCHAR2)) SOLANDE,
                     NVL(bnme.NHOMMAU, JSON_VALUE(TTHC.TTHC_ME,  '$.nhomMauMe' RETURNING VARCHAR2)) NHOMMAU_CUAME,
                     nvl(v_PARA, JSON_VALUE(TTHC.TTHC_ME,  '$.tienThaiPara' RETURNING VARCHAR2)) TIENTHAI_PARA,
                     nvl(regexp_substr(v_PARA, '[^ ]+', 1, 1),
                        NVL(REGEXP_SUBSTR(JSON_VALUE(TTHC.TTHC_ME,  '$.tienThaiPara' RETURNING VARCHAR2), '[^-]+', 1, 1), ' ')
                        ) as TIEN_THAI_PARA_1,
                     nvl(regexp_substr(v_PARA, '[^ ]+', 1, 2),
                        NVL(REGEXP_SUBSTR(JSON_VALUE(TTHC.TTHC_ME,  '$.tienThaiPara' RETURNING VARCHAR2), '[^-]+', 1, 2), ' ')
                        ) as TIEN_THAI_PARA_2,
                     nvl(regexp_substr(v_PARA, '[^ ]+', 1, 3),
                        NVL(REGEXP_SUBSTR(JSON_VALUE(TTHC.TTHC_ME,  '$.tienThaiPara' RETURNING VARCHAR2), '[^-]+', 1, 3), ' ')
                        ) as TIEN_THAI_PARA_3,
                     nvl(regexp_substr(v_PARA, '[^ ]+', 1, 4),
                        NVL(REGEXP_SUBSTR(JSON_VALUE(TTHC.TTHC_ME,  '$.tienThaiPara' RETURNING VARCHAR2), '[^-]+', 1, 4), ' ')
                        ) as TIEN_THAI_PARA_4,

--                     nvl(gcs.HO_TEN_BO, JSON_VALUE(TTHC.TTHC_CHA,  '$.hoTenCha' RETURNING VARCHAR2)) HOTENCHA,
                     --nvl(gcs.HO_TEN_BO, nvl(BN.HO_TEN_CHA, JSON_VALUE(TTHC.TTHC_CHA,  '$.hoTenCha' RETURNING VARCHAR2))) HOTENCHA,
                     nvl(NV_NT.HOTEN_BO,
                     nvl(gcs.HO_TEN_BO, nvl(BN.HO_TEN_CHA, JSON_VALUE(TTHC.TTHC_CHA,  '$.hoTenCha' RETURNING VARCHAR2)))
                     ) HOTENCHA,
                     JSON_VALUE(TTHC.TTHC_CHA,  '$.ngaySinhCuaCha' RETURNING VARCHAR2) NGAYSINH_CUACHA,
                     --JSON_VALUE(TTHC.TTHC_CHA,  '$.ngheNghiepCuaCha' RETURNING VARCHAR2) NGHENGHIEP_CUACHA,
                     nvl(nnchanv.ten_nghe_nghiep,
                     JSON_VALUE(TTHC.TTHC_CHA,  '$.ngheNghiepCuaCha' RETURNING VARCHAR2)
                     ) NGHENGHIEP_CUACHA,
                     JSON_VALUE(TTHC.TTHC_CHA,  '$.maNgheNghiepCuaCha' RETURNING VARCHAR2) MA_NGHENGHIEP_CUACHA,
                     JSON_VALUE(TTHC.TTHC_CHA,  '$.thongTinNguoiLienHe' RETURNING VARCHAR2) THONGTIN_NGUOILIENHE,
                     JSON_VALUE(TTHC.TTHC_CHA,  '$.maTrinhDoCuaCha' RETURNING VARCHAR2) MA_TRINHDO_CUABO,
                     TTHC.TTHC_CHA TTHC_CHA,
                     TTHC.TTHC_ME TTHC_ME,
                     nvl(BN.SO_DIEN_THOAI, JSON_VALUE(TTHC.TTHC_CHA,  '$.soDienThoaiNguoiLienHe' RETURNING VARCHAR2)) SODIENTHOAI_NGUOILIENHE
                     /*end BA Sơ Sinh*/
                     /*NGOAIYHCT 1941*/
                     ,NVL(ICDPHU3_KHOADT, ' ') as ICDPHU3_KHOADT
                     ,NVL(ICDPHU3_KHOADT_TEN, ' ') as ICDPHU3_KHOADT_TEN
                     ,NVL(ICDYHCTPHU3_KHOADT, ' ') as ICDYHCTPHU3_KHOADT
                     ,NVL(ICDYHCTPHU3_KHOADT_TEN, ' ') as ICDYHCTPHU3_KHOADT_TEN
                     ,NVL(ICDPHU4_KHOADT, ' ') as ICDPHU4_KHOADT
                     ,NVL(ICDPHU4_KHOADT_TEN, ' ') as ICDPHU4_KHOADT_TEN
                     ,NVL(ICDYHCTPHU4_KHOADT, ' ') as ICDYHCTPHU4_KHOADT
                     ,NVL(ICDYHCTPHU4_KHOADT_TEN, ' ') as ICDYHCTPHU4_KHOADT_TEN
                     ,NVL(ICDPHU5_KHOADT, ' ') as ICDPHU5_KHOADT
                     ,NVL(ICDPHU5_KHOADT_TEN, ' ') as ICDPHU5_KHOADT_TEN
                     ,NVL(ICDYHCTPHU5_KHOADT, ' ') as ICDYHCTPHU5_KHOADT
                     ,NVL(ICDYHCTPHU5_KHOADT_TEN, ' ') as ICDYHCTPHU5_KHOADT_TEN
                     /*END NGOAIYHCT 1941*/
                     ,NVL(TTHC.CHANDOAN_NOIGIOITHIEU, ' ') as CHANDOAN_NOIGIOITHIEU
                     ,to_char (TTHC.PHCN_PHANHE) as  PHCN_PHANHE
                     ,nvl(TTHC.HOTEN_NGUOIGIAMHO, ' ') as HOTEN_NGUOIGIAMHO
                     ,nvl(TTHC.NGHENGHIEP_NGUOIGIAMHO, ' ') as NGHENGHIEP_NGUOIGIAMHO
                     ,v_nv_lambenhan as NV_LAMBENHAN
                     ,v_ngay_lambenhan as NGAY_LAMBENHAN
                     ,TTHC.NGHENGHIEP_BO
                     ,TTHC.NGHENGHIEP_ME
                     ,decode(to_char(ddt.TT_DOTDIEUTRI), '4',cv.SOCHUYENVIEN_LUUTRU,'3', ba.SOXUATVIEN_LUUTRU, '5', ba.SOXUATVIEN_LUUTRU, ' ') AS SOLUUTRU_XUATVIEN
                     ,NVL(TO_CHAR(CHANDOAN.CHECK_KIEMSOATTUCUNG), '0') CHECK_KIEMSOATTUCUNG
                     ,CASE
                        WHEN V_TS_TTSO_VBA = '1' THEN
                             NVL(decode(to_char(ddt.TT_DOTDIEUTRI), '4',cv.SOCHUYENVIEN_LUUTRU,'3', ba.SOXUATVIEN_LUUTRU, '5', ba.SOXUATVIEN_LUUTRU, ' '), ' ')
                        ELSE BA.SOBENHAN
                      END AS SOLUUTRU_VBA
                     ,DECODE(V_TS_TTSO_VBA, '1', BA.SOBENHAN, BA.SOVAOVIEN) SOVAOVIEN_VBA,
                     TTHC.MAYTE_TITLE,
                     /*BA Methadone*/
                     JSON_VALUE(QLNB.METHADONE_YEUTO_LIENQUAN,  '$.tiensuqualieu' RETURNING VARCHAR2) TS_QUA_LIEU,
                     JSON_VALUE(QLNB.METHADONE_YEUTO_LIENQUAN,  '$.chitietqualieu' RETURNING VARCHAR2) CT_QUA_LIEU,
                     JSON_VALUE(QLNB.METHADONE_YEUTO_LIENQUAN,  '$.dunchungbomkimtiem' RETURNING VARCHAR2) DUNG_CHUNG_KIM_TIEM,
                     JSON_VALUE(QLNB.METHADONE_YEUTO_LIENQUAN,  '$.chitietdunchungbomkimtiem' RETURNING VARCHAR2) CHI_TIET_DUNG_CHUNG_KT,
                     JSON_VALUE(QLNB.METHADONE_TINHDUC,  '$.relsWithLove' RETURNING VARCHAR2) QH_NHIEU_BANTINH,
                     JSON_VALUE(QLNB.METHADONE_TINHDUC,  '$.relsWithLoveBCS' RETURNING VARCHAR2) QHBT_KHONG_BCS,
                     JSON_VALUE(QLNB.METHADONE_TINHDUC,  '$.relsWithSalePerson' RETURNING VARCHAR2) QH_NGUOI_BAN,
                     JSON_VALUE(QLNB.METHADONE_TINHDUC,  '$.relsWithSalePersonBCS' RETURNING VARCHAR2) QHNB_KHONG_BCS,
                     JSON_VALUE(QLNB.METHADONE_TINHDUC,  '$.relsWithHomosexual' RETURNING VARCHAR2) QH_DONG_GIOI,
                     JSON_VALUE(QLNB.METHADONE_TINHDUC,  '$.relsWithHomosexualBCS' RETURNING VARCHAR2) QHDG_KHONG_BCS,
                     TTHC.SO_DIEN_THOAI_BN,                                 TTHC.TT_HON_NHAN,
                     TTHC.KHA_NANG_TAI_CHINH,                               TTHC.NOI_GIOI_THIEU,
                     TTHC.CMND_CCCD_BN,                                     TTHC.NOI_CAP,
                     to_char(TTHC.Ngay_Cap, 'YYYY-MM-DD') NGAY_CAP,         TTHC.PROFILE_IMG,
                     QLNB.METHADONE_LYDOKHAM,                               QLNB.METHADONE_CHAT_GAYNGHIEN,
                     QLNB.METHADONE_TINHDUC,                                QLNB.METHADONE_SOLAN_CAINGHIEN,
                     QLNB.METHADONE_YEUTO_LIENQUAN,                         QLNB.METHADONE_TS_CAINGHIEN,
                     nvl(QLNB.DS_CHUYEN_KHOA, '[]') DS_CHUYEN_KHOA,
                     TTHC.SDT_NGUOILIENHE,
                     CASE
                       WHEN V_TUYEN_BV = 2 THEN
                         nvl(RAVIEN.CHUCDANH_BENHVIEN, 'GIÁM ĐỐC BỆNH VIỆN')
                       WHEN P_DVTT IN ('96011') THEN
                         'GIÁM ĐỐC'
                       ELSE
                         nvl(RAVIEN.CHUCDANH_BENHVIEN, 'GIÁM ĐỐC')
                     END AS CHUCDANH_BENHVIEN,
                     nvl(RAVIEN.CHUC_DANH_KHOA, 'TRƯỞNG KHOA') CHUC_DANH_KHOA,
                     nvl(CHANDOAN.DS_TRE_SO_SINH, '[]') DS_TRE_SO_SINH,
                     VBA.ID ID_HSBA,
                     v_thamso960616 ANCHUKY
                 FROM
                     (
                         SELECT
                             SOVAOVIEN, STT_BENHAN, MABENHNHAN, ICD_NHAPVIEN, TENBENHCHINH_NHAPVIEN, TENBENHPHU_NHAPVIEN, TRANG_THAI,
                             NGAYNHAPVIEN, NGAYRAVIEN, BANT, NVL(DECODE(BANT, 0, SOBENHAN, SOBENHAN_NGT), ' ') AS SOBENHAN,
                             NVL(SOBENHAN_TT, ' ') AS SOBENHAN_TT, LYDO_TRANGTHAI_BN_NHAPVIEN, HINHTHUCNHAPVIEN, TENKHOA_NHAPVIENVAOKHOA,
                             NHAPVIENTUPHANHE_NGOAITRU, SOXUATVIEN_LUUTRU, KETQUADIEUTRI, TINHTRANGRAVIEN,
                             NVL(MAKHAMBENHNGOAITRU_NHAPVIEN, ' ') AS MAKHAMBENHNGOAITRU_NHAPVIEN,NOIGIOITHIEU,VAOVIENLANTHU, DVTT,
                             ICD_NOI_CHUYEN, CHANDOAN_NOIGIOITHIEU
                         FROM NOITRU_BENHAN
                         WHERE SOVAOVIEN = P_SOVAOVIEN AND DVTT = P_DVTT
                     ) BA
                         JOIN HIS_PUBLIC_LIST.DM_BENH_NHAN                  BN
                            ON BN.MA_BENH_NHAN = BA.MABENHNHAN
                         JOIN NOITRU_DOTDIEUTRI                             DDT
                            ON BA.SOVAOVIEN = DDT.SOVAOVIEN
                                AND DDT.SOVAOVIEN_DT = P_SOVAOVIEN_DT
                         JOIN HIS_PUBLIC_LIST.DM_DAN_TOC                    DT
                            ON DT.MA_DANTOC = BN.MA_DANTOC
                         JOIN HIS_PUBLIC_LIST.DM_NGHENGHIEP                 NN
                            ON NN.MA_NGHE_NGHIEP = BN.MA_NGHE_NGHIEP
                         LEFT JOIN HIS_PUBLIC_LIST.DM_DOI_TUONG_BHYT        DTBHYT
                            ON DTBHYT.MA_DOI_TUONG_BHYT = DDT.MADOITUONG
                         LEFT JOIN HIS_MANAGER.NOITRU_XUATVIEN              XV
                            ON XV.SOVAOVIEN_DT = DDT.SOVAOVIEN_DT
                                AND XV.DVTT = DDT.DVTT
                         LEFT JOIN HIS_MANAGER.NOITRU_CHUYENTUYENBENHNHAN   CV
                            ON CV.SOVAOVIEN_DT = DDT.SOVAOVIEN_DT
                                AND CV.DVTT = DDT.DVTT
                         LEFT JOIN HIS_MANAGER.NOITRU_TUVONG                TV
                            ON TV.SOVAOVIEN_DT = DDT.SOVAOVIEN_DT
                                AND TV.DVTT = DDT.DVTT
--                         LEFT JOIN HIS_FW.DM_PHONGBAN                       PB
--                            ON PB.MA_PHONGBAN = DECODE(DDT.TT_DOTDIEUTRI, 3, XV.KHOA_XUATVIEN, 4, CV.KHOA_CHUYENTUYEN, 5, TV.KHOA_TUVONG, 7, DDT.KHOA_KETTHUCDOTDIEUTRI, '-1')
                         LEFT JOIN HIS_MANAGER.NOITRU_PHIEUTHANHTOAN        PTT
                            ON PTT.DVTT = DDT.DVTT
                                AND PTT.SOVAOVIEN = DDT.SOVAOVIEN
                                AND PTT.SOVAOVIEN_DT = DDT.SOVAOVIEN_DT
                         LEFT JOIN HIS_MANAGER.KB_KHAM_BENH                 KB
                            ON KB.DVTT = BA.DVTT
                                AND KB.MA_KHAM_BENH = BA.MAKHAMBENHNGOAITRU_NHAPVIEN
                         LEFT JOIN HIS_MANAGER.NOITRU_LOGKHOAPHONG          LOGPB
                            ON LOGPB.DVTT = DDT.DVTT
                                AND LOGPB.SOVAOVIEN = DDT.SOVAOVIEN
                                AND LOGPB.SOVAOVIEN_DT = DDT.SOVAOVIEN_DT
                                AND LOGPB.TRANG_THAI NOT IN (8)
                         LEFT JOIN HIS_PUBLIC_LIST.DM_PHUONG_XA             PX
                            ON PX.MA_PHUONG_XA = BN.MAXA_CU_TRU
                         LEFT JOIN HIS_PUBLIC_LIST.DM_TINH_THANH            TT
                            ON TT.MA_TINH_THANH = BN.MATINH_CU_TRU
                         LEFT JOIN HIS_PUBLIC_LIST.DM_QUAN_HUYEN            QH
                            ON QH.MA_QUAN_HUYEN = BN.MAHUYEN_CU_TRU
                         LEFT JOIN HIS_MANAGER.NOITRU_VOBENHAN              VBA
                            ON DDT.DVTT = VBA.DVTT
                                AND DDT.SOVAOVIEN = VBA.SOVAOVIEN
                                AND DDT.SOVAOVIEN_DT = VBA.SOVAOVIEN_DT
                                AND VBA.ID = P_ID
                         LEFT JOIN HIS_MANAGER.VOBENHAN_HANHCHINH    TTHC ON TTHC.ID_HSBA = VBA.ID
                         LEFT JOIN HIS_MANAGER.VOBENHAN_QLNGUOIBENH  QLNB ON QLNB.ID_HSBA = VBA.ID
                         LEFT JOIN HIS_MANAGER.VOBENHAN_CHANDOAN CHANDOAN ON CHANDOAN.ID_HSBA = VBA.ID
                         LEFT JOIN HIS_MANAGER.VOBENHAN_RAVIEN     RAVIEN ON RAVIEN.ID_HSBA = VBA.ID
                         LEFT JOIN HIS_PUBLIC_LIST.DM_BENH_LY      ICD_NCD ON ICD_NCD.ICD = CHANDOAN.ICD_NOICHUYENDEN
                         LEFT JOIN HIS_PUBLIC_LIST.DM_BENH_LY      ICD_KKB ON ICD_KKB.ICD = CHANDOAN.ICD_KKB
                         LEFT JOIN HIS_PUBLIC_LIST.DM_BENH_LY      ICD_KDT ON ICD_KDT.ICD = CHANDOAN.ICD_KHOADT
                         LEFT JOIN HIS_PUBLIC_LIST.DM_BENH_LY   ICD_RAVIEN ON ICD_RAVIEN.ICD = CHANDOAN.ICD_BENHCHINH_RV
                         LEFT JOIN HIS_PUBLIC_LIST.DM_BENH_LY   ICD_TUVONG ON ICD_TUVONG.ICD = RAVIEN.ICD_TUVONG
                         LEFT JOIN HIS_PUBLIC_LIST.DM_BENH_LY ICD_GIAIPHAU ON ICD_GIAIPHAU.ICD = RAVIEN.ICD_GIAIPHAU
                         LEFT JOIN HIS_MANAGER.NOITRU_PHIEUKHAMBENHVAOVIEN  NV_TT ON
                             NV_TT.DVTT =DDT.DVTT
                                    AND NV_TT.STT_BENHAN = DDT.STT_BENHAN
                                 AND NV_TT.STT_DOTDIEUTRI = DDT.STT_DOTDIEUTRI
                         LEFT JOIN HIS_MANAGER.KB_PHIEUNHAPVIENNOITRU NV_NT ON
                             NV_NT.DVTT = BA.DVTT AND NV_NT.MA_KHAM_BENH = BA.MAKHAMBENHNGOAITRU_NHAPVIEN
                         LEFT JOIN HIS_FW.DM_PHONGBAN                  KHOAVAO ON KHOAVAO.MA_PHONGBAN = QLNB.MAKHOA AND KHOAVAO.MA_DONVI = QLNB.DVTT
                         LEFT JOIN HIS_FW.DM_PHONGBAN                  CK1 ON CK1.MA_PHONGBAN = QLNB.MAKHOA_CHUYEN01 AND CK1.MA_DONVI = QLNB.DVTT
                         LEFT JOIN HIS_FW.DM_PHONGBAN                  CK2 ON CK2.MA_PHONGBAN = QLNB.MAKHOA_CHUYEN02 AND CK2.MA_DONVI = QLNB.DVTT
                         LEFT JOIN HIS_FW.DM_PHONGBAN                  CK3 ON CK3.MA_PHONGBAN = QLNB.MAKHOA_CHUYEN03 AND CK2.MA_DONVI = QLNB.DVTT
                         LEFT JOIN HIS_PUBLIC_LIST.DM_BENH_LY      ICD_NCD ON ICD_NCD.ICD = CHANDOAN.ICD_NOICHUYENDEN AND ICD_NCD.hoatong = 1
                         LEFT JOIN HIS_PUBLIC_LIST.DM_BENH_LY      ICD_KKB ON ICD_KKB.ICD = CHANDOAN.ICD_KKB AND ICD_KKB.hoatong = 1
                         LEFT JOIN HIS_PUBLIC_LIST.DM_BENH_LY      ICD_KDT ON ICD_KDT.ICD = CHANDOAN.ICD_KHOADT AND ICD_KDT.hoatong = 1
                         LEFT JOIN HIS_PUBLIC_LIST.DM_BENH_LY   ICD_RAVIEN ON ICD_RAVIEN.ICD = CHANDOAN.ICD_BENHCHINH_RV AND ICD_RAVIEN.hoatong = 1
                         LEFT JOIN HIS_PUBLIC_LIST.DM_BENH_LY   ICD_TUVONG ON ICD_TUVONG.ICD = RAVIEN.ICD_TUVONG AND ICD_TUVONG.hoatong = 1
                         LEFT JOIN HIS_PUBLIC_LIST.DM_BENH_LY ICD_GIAIPHAU ON ICD_GIAIPHAU.ICD = RAVIEN.ICD_GIAIPHAU AND ICD_GIAIPHAU.hoatong = 1
                         LEFT JOIN HIS_MANAGER.DM_QUOCGIA DMQG ON TTHC.NGOAIKIEU = DMQG.KYHIEU
                         left join his_manager.kb_tiep_nhan tiepnhan on tiepnhan.dvtt = ba.dvtt  and 'kb_'||tiepnhan.id_tiepnhan = ba.makhambenhngoaitru_nhapvien
                         left join his_ytcs.ds_chung_sinh gcs on bn.id_giaychungsinh = gcs.id_chung_sinh
                         left join his_public_list.dm_benh_nhan bnme on gcs.mabn_me = bnme.ma_benh_nhan
                         left join his_public_list.dm_nghenghiep nnme on bnme.ma_nghe_nghiep = nnme.ma_nghe_nghiep
                         left join his_public_list.dm_nghenghiep nnmenv on NV_NT.NGHENGHIEP_ME = nnmenv.ma_nghe_nghiep
                         left join his_public_list.dm_nghenghiep nnchanv on NV_NT.NGHENGHIEP_BO = nnchanv.ma_nghe_nghiep
                         LEFT JOIN HIS_FW.DM_NHANVIEN TRUONGKHOAXV on TRUONGKHOAXV.MA_NHANVIEN = XV.MA_BAC_SI_TRUONGKHOA
                         LEFT JOIN HIS_FW.DM_NHANVIEN TRUONGKHOATV on TRUONGKHOATV.MA_NHANVIEN = TV.MA_BAC_SI_TRUONGKHOA
                         LEFT JOIN HIS_FW.DM_NHANVIEN TRUONGKHOACV on TRUONGKHOACV.MA_NHANVIEN = CV.MA_BAC_SI_TRUONGKHOA
                         LEFT JOIN HIS_CATEGORY.DIA_CHI DC
                            ON DC.MA_XA = BN.MAXA_CU_TRU AND DC.MA_HUYEN = BN.MAHUYEN_CU_TRU AND DC.MA_TINH = BN.MATINH_CU_TRU
                        WHERE rownum <= 1;

RETURN CUR;
END;