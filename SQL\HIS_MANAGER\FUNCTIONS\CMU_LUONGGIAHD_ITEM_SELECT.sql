create or replace FUNCTION    HIS_MANAGER."CMU_LUONGGIAHD_ITEM_SELECT" (
p_dvtt varchar2,
p_loai varchar2
)return SYS_REFCURSOR
IS
  cur SYS_REFCURSOR;
BEGIN
open cur for select id,loai,noidung, nguoitao, nv.ten_nhanvien_cd TEN_NGUOI_TAO
  from cmu_luonggiahd_item phieu
        left join his_fw.dm_nhanvien_cd nv on phieu.nguoitao = nv.ma_nhanvien
  where dvtt=p_dvtt and (p_loai = '-1' or loai=p_loai)
  ORDER BY loai, id;
return cur;
END;

