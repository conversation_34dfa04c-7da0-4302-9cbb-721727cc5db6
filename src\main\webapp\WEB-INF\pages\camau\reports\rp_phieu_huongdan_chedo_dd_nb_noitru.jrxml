<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="rp_phieu_huongdan_chedo_dd_nb_noitru" language="groovy" pageWidth="595" pageHeight="842" whenNoDataType="AllSectionsNoDetail" columnWidth="515" leftMargin="40" rightMargin="40" topMargin="20" bottomMargin="20" whenResourceMissingType="Empty" uuid="0c77309d-bb14-4cd7-8eb5-4a8b75b128de">
	<property name="ireport.zoom" value="1.0"/>
	<property name="ireport.x" value="0"/>
	<property name="ireport.y" value="0"/>
	<style name="s_center" hAlign="Center"/>
	<style name="style1">
		<box>
			<leftPen lineWidth="1.0" lineColor="#000000"/>
		</box>
	</style>
	<parameter name="tenkhoa" class="java.lang.String"/>
	<parameter name="soyte" class="java.lang.String"/>
	<parameter name="benhvien" class="java.lang.String"/>
	<parameter name="dvtt" class="java.lang.String"/>
	<parameter name="maphieu" class="java.lang.String">
		<defaultValueExpression><![CDATA[]]></defaultValueExpression>
	</parameter>
	<parameter name="sovaovien" class="java.lang.String"/>
	<parameter name="stt_dotdieutri" class="java.lang.String"/>
	<parameter name="mabenhnhan" class="java.lang.String"/>
	<queryString language="plsql">
		<![CDATA[{call HIS_MANAGER.CMU_PHIEU_DGDD_CHUNG_P(
$P{dvtt}, 
$P{maphieu}, 
$P{stt_dotdieutri}, 
$P{sovaovien},
$P{ORACLE_REF_CURSOR})}]]>
	</queryString>
	<field name="DATA_PHIEU" class="java.lang.String"/>
	<field name="TEN_BENH_NHAN" class="java.lang.String"/>
	<field name="TUOI" class="java.lang.String"/>
	<field name="GIOI_TINH" class="java.lang.String"/>
	<field name="TENKHOA_NHAPVIENVAOKHOA" class="java.lang.String"/>
	<field name="CHAN_DOAN" class="java.lang.String"/>
	<field name="STT_BUONG" class="java.lang.String"/>
	<field name="STT_GIUONG" class="java.lang.String"/>
	<field name="ANCHUKY" class="java.lang.String"/>
	<variable name="JSON_DATA" class="java.lang.Object" resetType="None" calculation="System">
		<variableExpression><![CDATA[(new groovy.json.JsonSlurper().parseText($F{DATA_PHIEU}))]]></variableExpression>
	</variable>
	<group name="rp_phieu_huongdan_chedo_dd_nb_noitru">
		<groupExpression><![CDATA[$P{maphieu}]]></groupExpression>
		<groupHeader>
			<band height="560" splitType="Immediate">
				<staticText>
					<reportElement x="150" y="0" width="260" height="70" uuid="8a27bdd0-1bad-45e7-b96a-b1929eb04695"/>
					<textElement textAlignment="Center">
						<font fontName="Times New Roman" size="16" isBold="true"/>
					</textElement>
					<text><![CDATA[PHIẾU HƯỚNG DẪN CHẾ ĐỘ 
DINH DƯỠNG CHO 
NGƯỜI BỆNH NỘI TRÚ]]></text>
				</staticText>
				<textField>
					<reportElement x="0" y="0" width="150" height="70" uuid="253acd09-14ad-4a62-bde5-d29519abbe92"/>
					<box leftPadding="0"/>
					<textElement verticalAlignment="Top">
						<font fontName="Times New Roman" size="12" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA["Cơ sở KB, CB " + (
$P{benhvien} == null ? 
"" : 
$P{benhvien}
)]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="410" y="0" width="105" height="70" uuid="680f4e69-c2fe-44d8-ae45-184a9a7b8b6e"/>
					<box leftPadding="0"/>
					<textElement verticalAlignment="Top" markup="styled">
						<font fontName="Times New Roman" size="12" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA["MS: DD-04\nSố vào viện " + (
$P{sovaovien} == null ? 
"" : 
$P{sovaovien}
) + "\nMã người bệnh " + (
$P{mabenhnhan} == null ? 
"" : 
$P{mabenhnhan}
)]]></textFieldExpression>
				</textField>
				<frame>
					<reportElement x="0" y="70" width="515" height="45" uuid="aa08d61c-b703-4133-99dd-14710471a4cd"/>
					<textField>
						<reportElement x="0" y="0" width="355" height="15" uuid="4598d20c-0de0-4878-92da-feac7808e9ee"/>
						<box leftPadding="0"/>
						<textElement verticalAlignment="Middle">
							<font fontName="Times New Roman" size="12" isBold="false"/>
						</textElement>
						<textFieldExpression><![CDATA["Họ và tên người bệnh: " + (
$F{TEN_BENH_NHAN} == null ? 
"" : 
$F{TEN_BENH_NHAN}
)]]></textFieldExpression>
					</textField>
					<textField>
						<reportElement x="0" y="15" width="355" height="15" uuid="53c67b54-9c38-4a8b-bf73-7898e9d3630c"/>
						<box leftPadding="0"/>
						<textElement verticalAlignment="Middle">
							<font fontName="Times New Roman" size="12" isBold="false"/>
						</textElement>
						<textFieldExpression><![CDATA["Khoa: " + (
$F{TENKHOA_NHAPVIENVAOKHOA} == null ? 
"" : 
$F{TENKHOA_NHAPVIENVAOKHOA}
)]]></textFieldExpression>
					</textField>
					<textField>
						<reportElement positionType="Float" x="0" y="30" width="515" height="15" uuid="10badb0e-e431-4974-b2e5-c806acb9b037"/>
						<box leftPadding="0"/>
						<textElement verticalAlignment="Middle">
							<font fontName="Times New Roman" size="12" isBold="false"/>
						</textElement>
						<textFieldExpression><![CDATA["Chẩn đoán: " + (
$V{JSON_DATA}.ICD == null ? 
"" : 
$V{JSON_DATA}.ICD
) + " - " + (
$V{JSON_DATA}.CHAN_DOAN == null ? 
"" : 
$V{JSON_DATA}.CHAN_DOAN
)]]></textFieldExpression>
					</textField>
					<textField>
						<reportElement x="355" y="0" width="80" height="15" uuid="8a9af0cf-7848-42f0-a6f1-88b9981ea098"/>
						<textElement verticalAlignment="Middle">
							<font fontName="Times New Roman" size="12" isBold="false"/>
						</textElement>
						<textFieldExpression><![CDATA["Tuổi: " + (
$F{TUOI} == null ? 
"" : 
$F{TUOI}
)]]></textFieldExpression>
					</textField>
					<textField isStretchWithOverflow="true">
						<reportElement x="355" y="15" width="160" height="15" uuid="27b4d06f-641f-4275-b16a-d432434cca15"/>
						<textElement verticalAlignment="Middle">
							<font fontName="Times New Roman" size="12" isBold="false"/>
						</textElement>
						<textFieldExpression><![CDATA["Phòng: " + (
$F{STT_BUONG} == null ? 
"   " : 
$F{STT_BUONG}
) + " Giường: " + (
$F{STT_GIUONG} == null ? 
"   " : 
$F{STT_GIUONG}
)]]></textFieldExpression>
					</textField>
					<textField>
						<reportElement x="450" y="0" width="30" height="15" uuid="285ef18d-f19a-4221-89d0-1565a33d7b34"/>
						<box leftPadding="0"/>
						<textElement verticalAlignment="Middle">
							<font fontName="Times New Roman" size="11" isBold="false"/>
						</textElement>
						<textFieldExpression><![CDATA["Nam"]]></textFieldExpression>
					</textField>
					<textField>
						<reportElement x="437" y="2" width="11" height="11" uuid="476a16e8-321f-447c-8581-b7db05033f82"/>
						<box leftPadding="0">
							<topPen lineWidth="0.75"/>
							<leftPen lineWidth="0.75"/>
							<bottomPen lineWidth="0.75"/>
							<rightPen lineWidth="0.75"/>
						</box>
						<textElement textAlignment="Center" verticalAlignment="Middle">
							<font fontName="Times New Roman" size="9" isBold="false"/>
						</textElement>
						<textFieldExpression><![CDATA[(
$F{GIOI_TINH}.equals( "1" ) ? 
"X" : 
""
)]]></textFieldExpression>
					</textField>
					<textField>
						<reportElement x="482" y="2" width="11" height="11" uuid="88eccd12-8dfa-4392-9e1d-27cf1fe5189b"/>
						<box leftPadding="0">
							<topPen lineWidth="0.75"/>
							<leftPen lineWidth="0.75"/>
							<bottomPen lineWidth="0.75"/>
							<rightPen lineWidth="0.75"/>
						</box>
						<textElement textAlignment="Center" verticalAlignment="Middle">
							<font fontName="Times New Roman" size="9" isBold="false"/>
						</textElement>
						<textFieldExpression><![CDATA[(
$F{GIOI_TINH}.equals( "0" ) ? 
"X" : 
""
)]]></textFieldExpression>
					</textField>
					<textField>
						<reportElement x="495" y="0" width="20" height="15" uuid="b25513a5-f941-4de6-824d-1fe48a98e0ef"/>
						<box leftPadding="0"/>
						<textElement verticalAlignment="Middle">
							<font fontName="Times New Roman" size="11" isBold="false"/>
						</textElement>
						<textFieldExpression><![CDATA["Nữ"]]></textFieldExpression>
					</textField>
				</frame>
				<frame>
					<reportElement positionType="Float" x="0" y="120" width="515" height="335" uuid="bd99dabd-7600-4045-83a4-b75e785d7e7c"/>
					<box>
						<topPen lineWidth="1.0"/>
						<leftPen lineWidth="1.0"/>
						<bottomPen lineWidth="1.0"/>
						<rightPen lineWidth="1.0"/>
					</box>
					<frame>
						<reportElement x="0" y="0" width="515" height="30" uuid="8bf6ffd9-2083-4743-8bd6-e479e688f398"/>
						<textField>
							<reportElement x="0" y="0" width="60" height="30" uuid="fb4fcf13-5b87-4dc6-b4bc-a596944ff499"/>
							<box leftPadding="0">
								<rightPen lineWidth="0.75"/>
							</box>
							<textElement textAlignment="Center" verticalAlignment="Middle">
								<font fontName="Times New Roman" size="12" isBold="true"/>
							</textElement>
							<textFieldExpression><![CDATA["Ngày, giờ"]]></textFieldExpression>
						</textField>
						<textField>
							<reportElement x="60" y="0" width="225" height="30" uuid="d22274ff-3ac2-40b7-8ff2-28265fa91328"/>
							<box leftPadding="0">
								<leftPen lineWidth="0.75"/>
								<rightPen lineWidth="0.75"/>
							</box>
							<textElement textAlignment="Center" verticalAlignment="Middle">
								<font fontName="Times New Roman" size="12" isBold="true"/>
							</textElement>
							<textFieldExpression><![CDATA["Mức độ"]]></textFieldExpression>
						</textField>
						<textField>
							<reportElement x="285" y="0" width="230" height="30" uuid="54fe1d69-f32c-4ba6-9316-90c9721b8bec"/>
							<box leftPadding="0">
								<leftPen lineWidth="0.75"/>
								<rightPen lineWidth="0.0"/>
							</box>
							<textElement textAlignment="Center" verticalAlignment="Middle">
								<font fontName="Times New Roman" size="12" isBold="true"/>
							</textElement>
							<textFieldExpression><![CDATA["Ảnh hưởng của bệnh lý"]]></textFieldExpression>
						</textField>
					</frame>
					<frame>
						<reportElement x="0" y="30" width="60" height="305" uuid="9876502d-c3e4-4e43-8ed7-69b7dba8f99b"/>
						<box>
							<topPen lineWidth="1.0"/>
							<rightPen lineWidth="0.75"/>
						</box>
						<textField>
							<reportElement x="0" y="0" width="60" height="305" uuid="ad77ac6d-3736-4a9e-8f4c-09a0090d8df3"/>
							<box leftPadding="0">
								<rightPen lineWidth="0.0"/>
							</box>
							<textElement textAlignment="Center" verticalAlignment="Middle">
								<font fontName="Times New Roman" size="12" isBold="true"/>
							</textElement>
							<textFieldExpression><![CDATA[$V{JSON_DATA}.NGAY_GIO == null ||
$V{JSON_DATA}.NGAY_GIO.isEmpty() ?
"Ngày khám      /     /     Giờ khám     giờ     phút" :
(
    java.time.OffsetDateTime.parse(
        $V{JSON_DATA}.NGAY_GIO
    ).format(
        java.time.format.DateTimeFormatter.ofPattern("dd'/'MM'/'yyyy HH':'mm")
    )
)]]></textFieldExpression>
						</textField>
					</frame>
					<frame>
						<reportElement x="60" y="30" width="225" height="105" uuid="958e581d-6044-482b-9844-cdd726ea8671"/>
						<box>
							<topPen lineWidth="1.0"/>
							<leftPen lineWidth="0.75"/>
							<rightPen lineWidth="0.75"/>
						</box>
						<frame>
							<reportElement x="0" y="0" width="70" height="105" uuid="d9670950-17fc-4fe8-9135-6dc4a4cd4b4d"/>
							<textField>
								<reportElement x="5" y="0" width="65" height="30" uuid="b2750976-f9b9-4c14-9cd2-6cce560d6a05"/>
								<box leftPadding="0"/>
								<textElement verticalAlignment="Top" markup="styled">
									<font fontName="Times New Roman" size="12" isBold="false"/>
								</textElement>
								<textFieldExpression><![CDATA["Cân nặng:\n" + (
$V{JSON_DATA}.CANNANG == null ? 
"" : 
$V{JSON_DATA}.CANNANG
) + " (kg);"]]></textFieldExpression>
							</textField>
							<staticText>
								<reportElement x="5" y="30" width="65" height="30" uuid="f986a882-b250-4e2a-899b-2467b70d09bf"/>
								<textElement verticalAlignment="Middle" markup="html">
									<font fontName="Times New Roman" size="12" isBold="false" isItalic="false"/>
								</textElement>
								<text><![CDATA[Nguy cơ SDD:]]></text>
							</staticText>
							<staticText>
								<reportElement x="20" y="65" width="50" height="15" uuid="8ce1a7b7-e6dc-4ba8-a49f-839022f83e46"/>
								<textElement verticalAlignment="Middle">
									<font fontName="Times New Roman" size="12"/>
								</textElement>
								<text><![CDATA[Không]]></text>
							</staticText>
							<staticText>
								<reportElement x="20" y="85" width="50" height="15" uuid="3e632c32-6eba-4795-a74a-b703a1c7c6f4"/>
								<textElement verticalAlignment="Middle">
									<font fontName="Times New Roman" size="12"/>
								</textElement>
								<text><![CDATA[Có]]></text>
							</staticText>
							<textField>
								<reportElement x="5" y="67" width="11" height="11" uuid="6990567c-d7f8-488b-9a05-f2a8a70eea2f"/>
								<box leftPadding="0">
									<topPen lineWidth="0.75"/>
									<leftPen lineWidth="0.75"/>
									<bottomPen lineWidth="0.75"/>
									<rightPen lineWidth="0.75"/>
								</box>
								<textElement textAlignment="Center" verticalAlignment="Middle">
									<font fontName="Times New Roman" size="9" isBold="false"/>
								</textElement>
								<textFieldExpression><![CDATA[(
$V{JSON_DATA}.NGUYCO_SDD.toString().equals( "1" ) ? 
"X" : 
""
)]]></textFieldExpression>
							</textField>
							<textField>
								<reportElement x="5" y="87" width="11" height="11" uuid="16007a6f-1540-4381-8c04-1408e28025a4"/>
								<box leftPadding="0">
									<topPen lineWidth="0.75"/>
									<leftPen lineWidth="0.75"/>
									<bottomPen lineWidth="0.75"/>
									<rightPen lineWidth="0.75"/>
								</box>
								<textElement textAlignment="Center" verticalAlignment="Middle">
									<font fontName="Times New Roman" size="9" isBold="false"/>
								</textElement>
								<textFieldExpression><![CDATA[(
$V{JSON_DATA}.NGUYCO_SDD.toString().equals( "2" ) ? 
"X" : 
""
)]]></textFieldExpression>
							</textField>
						</frame>
						<textField>
							<reportElement x="70" y="0" width="155" height="105" uuid="73833590-5ef4-4012-bd7a-5024f6a364c7"/>
							<box leftPadding="6" rightPadding="0">
								<leftPen lineWidth="0.75"/>
							</box>
							<textElement verticalAlignment="Top">
								<font fontName="Times New Roman" size="12" isBold="false"/>
							</textElement>
							<textFieldExpression><![CDATA["Toàn trạng/Cơ " + (
$V{JSON_DATA}.TOAN_TRANG_CO == null ? 
"" : 
$V{JSON_DATA}.TOAN_TRANG_CO
)]]></textFieldExpression>
						</textField>
					</frame>
					<frame>
						<reportElement x="60" y="135" width="225" height="200" uuid="74179d80-7efc-465c-a4b7-b12e632de374"/>
						<box>
							<topPen lineWidth="1.0"/>
							<leftPen lineWidth="0.75"/>
							<rightPen lineWidth="0.75"/>
						</box>
						<frame>
							<reportElement stretchType="RelativeToTallestObject" x="0" y="0" width="70" height="200" uuid="bf9aee0c-51e8-41fd-a2b0-89b7cad2d2dc"/>
							<box>
								<rightPen lineWidth="0.75"/>
							</box>
							<staticText>
								<reportElement x="5" y="0" width="65" height="200" uuid="8d37b58b-c0f7-4cf3-854b-e1bdf1f04686"/>
								<textElement verticalAlignment="Top" markup="html">
									<font fontName="Times New Roman" size="12" isBold="false" isItalic="false"/>
								</textElement>
								<text><![CDATA[Khả năng dung nạp/ thu nạp thức ăn]]></text>
							</staticText>
						</frame>
						<frame>
							<reportElement x="70" y="0" width="75" height="200" uuid="7e080bbb-6562-497b-a48a-c4e8cf6406f6"/>
							<box>
								<leftPen lineWidth="0.75"/>
								<rightPen lineWidth="0.75"/>
							</box>
							<staticText>
								<reportElement x="20" y="5" width="55" height="30" uuid="c772e8d2-ed4a-4c3e-8660-eaa122649b63"/>
								<textElement verticalAlignment="Top">
									<font fontName="Times New Roman" size="12"/>
								</textElement>
								<text><![CDATA[Ăn hết suất ăn 
]]></text>
							</staticText>
							<textField>
								<reportElement x="20" y="40" width="55" height="60" uuid="87ecca46-a930-4eb2-b73a-2983b73944f8"/>
								<box leftPadding="0"/>
								<textElement verticalAlignment="Top">
									<font fontName="Times New Roman" size="12" isBold="false"/>
								</textElement>
								<textFieldExpression><![CDATA["Hạn chế/ kém; đạt " + (
$V{JSON_DATA}.DAT == null ? 
"" : 
$V{JSON_DATA}.DAT
) + " % suất ăn, là do:"]]></textFieldExpression>
							</textField>
							<textField>
								<reportElement x="4" y="7" width="11" height="11" uuid="228b22ac-a4d7-4624-8310-476a90d9511d"/>
								<box leftPadding="0">
									<topPen lineWidth="0.75"/>
									<leftPen lineWidth="0.75"/>
									<bottomPen lineWidth="0.75"/>
									<rightPen lineWidth="0.75"/>
								</box>
								<textElement textAlignment="Center" verticalAlignment="Middle">
									<font fontName="Times New Roman" size="9" isBold="false"/>
								</textElement>
								<textFieldExpression><![CDATA[(
$V{JSON_DATA}.KHANANG.toString().equals( "1" ) ? 
"X" : 
""
)]]></textFieldExpression>
							</textField>
							<textField>
								<reportElement x="4" y="42" width="11" height="11" uuid="686f8365-731e-4a78-985d-6eea41659800"/>
								<box leftPadding="0">
									<topPen lineWidth="0.75"/>
									<leftPen lineWidth="0.75"/>
									<bottomPen lineWidth="0.75"/>
									<rightPen lineWidth="0.75"/>
								</box>
								<textElement textAlignment="Center" verticalAlignment="Middle">
									<font fontName="Times New Roman" size="9" isBold="false"/>
								</textElement>
								<textFieldExpression><![CDATA[(
$V{JSON_DATA}.KHANANG.toString().equals( "2" ) ? 
"X" : 
""
)]]></textFieldExpression>
							</textField>
						</frame>
						<frame>
							<reportElement x="145" y="0" width="80" height="200" uuid="d7a7f704-c419-4765-84b2-cf75d9478185"/>
							<box>
								<leftPen lineWidth="0.75"/>
							</box>
							<staticText>
								<reportElement x="20" y="5" width="60" height="15" uuid="78d80349-40ac-4cd9-8f13-20ad6ce46f4c"/>
								<textElement verticalAlignment="Top">
									<font fontName="Times New Roman" size="12"/>
								</textElement>
								<text><![CDATA[Chán ăn]]></text>
							</staticText>
							<staticText>
								<reportElement x="20" y="25" width="60" height="30" uuid="5be475fb-86ce-4dd0-9b37-32176b8f8137"/>
								<textElement verticalAlignment="Top">
									<font fontName="Times New Roman" size="12"/>
								</textElement>
								<text><![CDATA[Buồn nôn/nôn]]></text>
							</staticText>
							<staticText>
								<reportElement x="20" y="60" width="60" height="30" uuid="f25271be-262c-4b7f-83c1-f9ca6ce98da1"/>
								<textElement verticalAlignment="Top">
									<font fontName="Times New Roman" size="12"/>
								</textElement>
								<text><![CDATA[Đau/chướng bụng]]></text>
							</staticText>
							<staticText>
								<reportElement x="20" y="95" width="60" height="15" uuid="d6314c31-d9ab-49eb-8c51-d0ded4fc3512"/>
								<textElement verticalAlignment="Top">
									<font fontName="Times New Roman" size="12"/>
								</textElement>
								<text><![CDATA[Tiêu chảy]]></text>
							</staticText>
							<textField>
								<reportElement x="20" y="115" width="60" height="70" uuid="a4831727-0d2f-44dd-bee6-a22e5dc1e4d7"/>
								<box leftPadding="0"/>
								<textElement verticalAlignment="Top">
									<font fontName="Times New Roman" size="12" isBold="false"/>
								</textElement>
								<textFieldExpression><![CDATA["Dịch tồn lưu dạ dày (DD qua ống thông) " + (
$V{JSON_DATA}.DD_QUA_ONGTHONG == null ? 
"" : 
$V{JSON_DATA}.DD_QUA_ONGTHONG
) + " mL"]]></textFieldExpression>
							</textField>
							<textField isStretchWithOverflow="true">
								<reportElement x="5" y="185" width="75" height="15" uuid="36389bac-e5b4-4a88-a52f-6e67c4cd405d"/>
								<box leftPadding="0"/>
								<textElement verticalAlignment="Top">
									<font fontName="Times New Roman" size="12" isBold="false"/>
								</textElement>
								<textFieldExpression><![CDATA["Khác: " + (
$V{JSON_DATA}.KHAC == null ? 
"" : 
$V{JSON_DATA}.KHAC
)]]></textFieldExpression>
							</textField>
							<textField>
								<reportElement x="4" y="62" width="11" height="11" uuid="8ecbc1f5-0b21-4344-81ca-7399673b7ee9"/>
								<box leftPadding="0">
									<topPen lineWidth="0.75"/>
									<leftPen lineWidth="0.75"/>
									<bottomPen lineWidth="0.75"/>
									<rightPen lineWidth="0.75"/>
								</box>
								<textElement textAlignment="Center" verticalAlignment="Middle">
									<font fontName="Times New Roman" size="9" isBold="false"/>
								</textElement>
								<textFieldExpression><![CDATA[(
$V{JSON_DATA}.DO.toString().equals( "3" ) ? 
"X" : 
""
)]]></textFieldExpression>
							</textField>
							<textField>
								<reportElement x="4" y="7" width="11" height="11" uuid="59e775c2-7d5f-46aa-b439-ccef08359e5e"/>
								<box leftPadding="0">
									<topPen lineWidth="0.75"/>
									<leftPen lineWidth="0.75"/>
									<bottomPen lineWidth="0.75"/>
									<rightPen lineWidth="0.75"/>
								</box>
								<textElement textAlignment="Center" verticalAlignment="Middle">
									<font fontName="Times New Roman" size="9" isBold="false"/>
								</textElement>
								<textFieldExpression><![CDATA[(
$V{JSON_DATA}.DO.toString().equals( "1" ) ? 
"X" : 
""
)]]></textFieldExpression>
							</textField>
							<textField>
								<reportElement x="4" y="97" width="11" height="11" uuid="490c755d-10d2-4af0-a315-b02247a80605"/>
								<box leftPadding="0">
									<topPen lineWidth="0.75"/>
									<leftPen lineWidth="0.75"/>
									<bottomPen lineWidth="0.75"/>
									<rightPen lineWidth="0.75"/>
								</box>
								<textElement textAlignment="Center" verticalAlignment="Middle">
									<font fontName="Times New Roman" size="9" isBold="false"/>
								</textElement>
								<textFieldExpression><![CDATA[(
$V{JSON_DATA}.DO.toString().equals( "4" ) ? 
"X" : 
""
)]]></textFieldExpression>
							</textField>
							<textField>
								<reportElement x="4" y="117" width="11" height="11" uuid="8b0a90b8-fc8f-4a0c-99d3-1aeaaf88e404"/>
								<box leftPadding="0">
									<topPen lineWidth="0.75"/>
									<leftPen lineWidth="0.75"/>
									<bottomPen lineWidth="0.75"/>
									<rightPen lineWidth="0.75"/>
								</box>
								<textElement textAlignment="Center" verticalAlignment="Middle">
									<font fontName="Times New Roman" size="9" isBold="false"/>
								</textElement>
								<textFieldExpression><![CDATA[(
$V{JSON_DATA}.DO.toString().equals( "5" ) ? 
"X" : 
""
)]]></textFieldExpression>
							</textField>
							<textField>
								<reportElement x="4" y="27" width="11" height="11" uuid="59441f3f-1fc2-4596-9c44-5393f40204f1"/>
								<box leftPadding="0">
									<topPen lineWidth="0.75"/>
									<leftPen lineWidth="0.75"/>
									<bottomPen lineWidth="0.75"/>
									<rightPen lineWidth="0.75"/>
								</box>
								<textElement textAlignment="Center" verticalAlignment="Middle">
									<font fontName="Times New Roman" size="9" isBold="false"/>
								</textElement>
								<textFieldExpression><![CDATA[(
$V{JSON_DATA}.DO.toString().equals( "2" ) ? 
"X" : 
""
)]]></textFieldExpression>
							</textField>
						</frame>
					</frame>
					<frame>
						<reportElement x="285" y="30" width="230" height="305" uuid="7cf0fe39-995c-4043-a25b-20e23677ef1a"/>
						<box>
							<topPen lineWidth="1.0"/>
							<leftPen lineWidth="0.75"/>
							<rightPen lineWidth="0.0"/>
						</box>
						<textField isStretchWithOverflow="true" isBlankWhenNull="true">
							<reportElement positionType="Float" x="0" y="15" width="230" height="45" uuid="2a24e0e6-0c6a-45c1-bed5-a597d6fa174d"/>
							<box leftPadding="6" rightPadding="0">
								<leftPen lineWidth="0.0"/>
							</box>
							<textElement verticalAlignment="Top">
								<font fontName="Times New Roman" size="12" isBold="false"/>
							</textElement>
							<textFieldExpression><![CDATA[(
$V{JSON_DATA}.CDDD_TIEUHOA == null ? 
"" : 
$V{JSON_DATA}.CDDD_TIEUHOA
)]]></textFieldExpression>
						</textField>
						<textField isStretchWithOverflow="true" isBlankWhenNull="true">
							<reportElement positionType="Float" x="0" y="75" width="230" height="230" uuid="dfc4aeb8-b2a7-4831-8e90-fe7620d4bc53"/>
							<box leftPadding="6" rightPadding="0">
								<leftPen lineWidth="0.0"/>
							</box>
							<textElement verticalAlignment="Top">
								<font fontName="Times New Roman" size="12" isBold="false"/>
							</textElement>
							<textFieldExpression><![CDATA[(
$V{JSON_DATA}.TH_DT_DD == null ? 
"" : 
$V{JSON_DATA}.TH_DT_DD
)]]></textFieldExpression>
						</textField>
						<staticText>
							<reportElement x="0" y="0" width="230" height="15" uuid="e6c5fac5-aada-4eae-b5b9-fe4675b4b39f"/>
							<box leftPadding="6"/>
							<textElement verticalAlignment="Middle">
								<font fontName="Times New Roman" size="12"/>
							</textElement>
							<text><![CDATA[- Chế độ dinh dưỡng qua tiêu hóa:]]></text>
						</staticText>
						<staticText>
							<reportElement positionType="Float" x="0" y="60" width="230" height="15" uuid="7780d9f5-6133-4a99-b7f2-0524ab405ead"/>
							<box leftPadding="6"/>
							<textElement verticalAlignment="Middle">
								<font fontName="Times New Roman" size="12"/>
							</textElement>
							<text><![CDATA[- Thuốc/Dịch truyền dinh dưỡng]]></text>
						</staticText>
					</frame>
				</frame>
				<frame>
					<reportElement positionType="Float" x="0" y="460" width="515" height="100" uuid="d1993f0c-8695-4153-b7ac-8f68648ed8ba"/>
					<box>
						<topPen lineWidth="0.0"/>
						<leftPen lineWidth="0.0"/>
						<bottomPen lineWidth="0.0"/>
						<rightPen lineWidth="0.0"/>
					</box>
					<frame>
						<reportElement x="0" y="0" width="240" height="100" uuid="ece8d9ac-ccaf-4cda-94ff-ddb3af006988"/>
						<box>
							<rightPen lineWidth="0.0"/>
						</box>
					</frame>
					<frame>
						<reportElement x="240" y="0" width="275" height="100" uuid="651072f6-7eef-4a71-a346-18aaca78ea51"/>
						<textField isBlankWhenNull="true">
							<reportElement x="0" y="85" width="275" height="15" uuid="fa1396ea-caf0-48d1-9a75-ff4c3d47b604">
								<printWhenExpression><![CDATA[$F{ANCHUKY}.equals("0")]]></printWhenExpression>
							</reportElement>
							<textElement textAlignment="Center" verticalAlignment="Middle">
								<font fontName="Times New Roman" size="12"/>
							</textElement>
							<textFieldExpression><![CDATA[(
$V{JSON_DATA}.BAC_SI_DANH_GIA.tennhanvien == null ?
"" :
$V{JSON_DATA}.BAC_SI_DANH_GIA.tennhanvien
)]]></textFieldExpression>
						</textField>
						<staticText>
							<reportElement x="0" y="15" width="275" height="15" uuid="447932e2-030a-4a1d-9093-d6d2aefb998b"/>
							<textElement textAlignment="Center" verticalAlignment="Middle" markup="html">
								<font fontName="Times New Roman" size="12" isBold="true" isItalic="false"/>
							</textElement>
							<text><![CDATA[Người thực hiện]]></text>
						</staticText>
						<textField>
							<reportElement x="0" y="0" width="275" height="15" uuid="8a349de5-8ee1-4446-b9e8-1da307a7ef1f"/>
							<box leftPadding="0"/>
							<textElement textAlignment="Center" verticalAlignment="Middle">
								<font fontName="Times New Roman" size="12" isBold="false" isItalic="true"/>
							</textElement>
							<textFieldExpression><![CDATA[$V{JSON_DATA}.NGAY_KY_BS == null ||
$V{JSON_DATA}.NGAY_KY_BS.isEmpty() ?
"Ngày      tháng     năm 20" :
(
    java.time.OffsetDateTime.parse(
        $V{JSON_DATA}.NGAY_KY_BS
    ).format(
        java.time.format.DateTimeFormatter.ofPattern("'Ngày' dd 'tháng' MM 'năm' yyyy")
    )
)]]></textFieldExpression>
						</textField>
						<staticText>
							<reportElement x="0" y="30" width="275" height="15" uuid="235c7065-1f74-4ce9-a047-4a71b49c6875"/>
							<textElement textAlignment="Center" verticalAlignment="Middle" markup="html">
								<font fontName="Times New Roman" size="12" isBold="false" isItalic="true"/>
							</textElement>
							<text><![CDATA[(Ký, ghi rõ họ tên)]]></text>
						</staticText>
						<staticText>
							<reportElement x="95" y="45" width="60" height="9" forecolor="#FFFFFF" uuid="a4cb9de4-48d8-4f37-8d37-fad22cbcbe10"/>
							<textElement>
								<font fontName="Times New Roman" size="6" isBold="false"/>
							</textElement>
							<text><![CDATA[BÁC SĨ ĐÁNH GIÁ]]></text>
						</staticText>
					</frame>
				</frame>
			</band>
		</groupHeader>
	</group>
</jasperReport>
