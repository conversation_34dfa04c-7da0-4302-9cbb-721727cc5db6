create or replace procedure HIS_MANAGER.VLG_IN_DOTSOKET(
 p_dvtt in varchar2,
 p_stt_benhan in varchar2,
 p_sovaovien in varchar2,
 p_dot_soket in varchar2, cur OUT SYS_REFCURSOR
)
IS
    v_thamso960616 number(10) := cmu_tsdv(p_dvtt, 960616, 0);
begin


  OPEN cur FOR
   select t.dien_bien        as dien_bien,
       t.xn_can<PERSON><PERSON>    as ten_xet_nghiem,
       t.quatrinh_dieutri as y_lenh,
       t.danhgia_ketqua   as danhgia_ketqua,
       t.huong_dieutri    as huong_dieutri,
       a.stt_buong        as sobuong,
       TO_CHAR(t.ngay_tao, 'HH24 "giờ" MI "p, Ngày" DD "tháng" MM "năm" YYYY') ngay_tao,
       a.stt_giuong       as sogiuong,
       c.sobenhan         as sobenhan,
       nvl(a.stt_buong, ' ')        as buong,
       nvl(a.stt_giuong, ' ')       as giu<PERSON>,
       t.data_bosung,
       v_thamso960616 anchuky
  from noitru_benhan c
  left join his_manager.vlg_soket15ngay t
    on c.dvtt = t.dvtt
   and c.stt_benhan = t.stt_benhan
   and c.sovaovien = t.sovaovien
  left join cmu_sobuonggiuong a
    on a.dvtt = t.dvtt
   and a.stt_benhan = t.dvtt
 where t.dvtt = p_dvtt
   and t.sovaovien = p_sovaovien
   and t.stt_benhan = p_stt_benhan
   and t.dot_soket = p_dot_soket;
end ;

/
