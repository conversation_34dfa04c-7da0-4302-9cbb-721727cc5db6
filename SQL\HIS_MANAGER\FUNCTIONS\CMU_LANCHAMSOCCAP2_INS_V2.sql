create or replace FUNCTION HIS_MANAGER."CMU_LANCHAMSOCCAP2_INS_V2" (
    p_dvtt           IN               VARCHAR2,
    p_userid         IN               VARCHAR2,
    p_makhoa         IN               VARCHAR2,
    p_sovaovien      IN               VARCHAR2,
    p_sovaovien_dt   IN               VARCHAR2,
    p_ma<PERSON><PERSON><PERSON>     IN               VARCHAR2,
    p_stt_benhan     IN               VARCHAR2,
    p_icd            IN               VARCHAR2,
    p_tenicd         IN               VARCHAR2,
    p_ngay           IN               VARCHAR2
) RETURN VARCHAR2 IS

    v_id     NUMBER := 0;
    v_ngay   DATE := TO_DATE(p_ngay, 'dd/mm/yyyy hh24:mi');
    v_toso   NUMBER := 0;
BEGIN
SELECT
    nvl(MAX(to_so), 0)
INTO v_toso
FROM
    cmu_lanchamsoccap2
WHERE
        dvtt = p_dvtt
  AND sovaovien = p_sovaovien
  AND sovaovien_dt = p_sovaovien_dt;

INSERT INTO cmu_lanchamsoccap2 (
    dvtt,
    to_so,
    stt_benhan,
    sovaovien,
    sovaovien_dt,
    ma<PERSON><PERSON><PERSON>,
    ma_khoa_tao_phieu,
    ma_nguoi_tao_phieu,
    ngay_tao_phieu,
    icd_chandoan,
    tenicd_chandoan
) VALUES (
             p_dvtt,
             v_toso + 1,
             p_stt_benhan,
             p_sovaovien,
             p_sovaovien_dt,
             p_mabenhnhan,
             p_makhoa,
             p_userid,
             v_ngay,
             p_icd,
             p_tenicd
         ) RETURNING id_cham_soc_cap_2 INTO v_id;

RETURN v_id;
END;