var objectBenhan = {
    // Chỉ L3
    NHI: getBANHIJSON(),
    PHUKHOA: getBAPHUKHOAJSON(),
    SANKHOA: getBASANKHOAJSON(),
    SOSINH: getBASOSINHJSON(),
    MATLAC: getBAMATLACJSON(),
    MATTREEM: getBAMATTREEMJSON(),
    BONG: getBABONGJSON(),
    NGOAITRURHM: getBANGOAITRURHMJSON(),
    TAYCHANMIENG: getBATAYCHANMIENGJSON(),
    PHCN_NHI: getBAPHCHUCNANGNHIJSON(),
    PHCN: getBAPHCHUCNANGJSON(),
    PHCN_BANT: getBAPHCHUCNANGNGOAITRUJSON(),
    MATCHANTHUONG: getBAMATCHANTHUONGJSON(),
    NGOAITRUYHCT1941: getBANGOAITRUYHCTJSON(),
    PHATHAI: getBAP<PERSON>THAIJSO<PERSON>(),

    // Chỉ L2
    NGOAITRU_CHUNG: getBANGOAITRUCHUNGJSON(),

    // Chung
    NOI: getBAN<PERSON>IJSON(),
    TAIMUIHONG: getBATT<PERSON><PERSON><PERSON>H<PERSON>GJSO<PERSON>(),
    DALIEU: getBADALIEUJSON(),
    NGOAIKHOA: getBANGOAIKHOAJSON(),
    TAMTHAN: getBATAMTHANJSON(),
    NHIEM: getBANHIEMJSON(),
    UNGBUOU: getBANOIJSON(),
    DAYMAT: getBADAYMATJSON(),
    RANGHAMMAT: getBARANGHAMMATJSON(),
    NOITRUYHCT: getBANOIYHCTJSON(),
    MATGLOCOM: getBAMATGLOCOMJSON(),
    BANPHANTRUOC: getBAMATBANPHANTRUOCJSON(),

};
var oldDataTrang1 = {};
var oldDataTrang2 = {};
var oldDataTrang3 = {};

var dataOld = {};
var dataNew = {};

function getThongtinPage1Benhan(idBA, callbackDone, callbackFail) {
    $.get("cmu_list_CMU_LOAD_THONGTIN_TRANG1?url="+convertArray([singletonObject.dvtt, idBA])).fail(function() {
        callbackFail();
    }).done(function(response) {
        var data = response[0]
        // $("#hsba_tthc_qlnbngayvaokhoa").html(data.NGAYGIOVAOKHOA);
        $("#hsba_tthc_qlnbtenicd_full").html(data.ICD_KHOADT ? data.ICD_KHOADT + " - " + data.ICD_KHOADT_TEN : "");
        $("#hsba_tthc_qlnbsongay").html(data.SONGAY_DIEUTRI);
        $("#hsba_tthc_vaovienlanthu").html(thongtinhsba.thongtinbn.VOBENHAN[0].TRANG1.INFO.VAOVIENLANTHU);
        // $(".title-vba").html(`<b>${data.TITLE_REPORT}</b>`);
        var tenphongphong = '';
        singletonObject.danhsachphongban.forEach(function(item) {
            if(item.MAKHOA == data.MAKHOA) {
                tenphongphong = item.TENKHOA;
            }
        })
        $("#hsba_tthc_qlnbkhoavv").html(tenphongphong);
        callbackDone(data);
    })
}
function getThongtinBenhan(idBA, loaiBA, callbackDone, callbackFail) {
    $.get("load-thong-tin-chi-tiet-page2?iD="+idBA+"&loaiBa="+loaiBA).fail(function() {
        callbackFail();
    }).done(function(data) {
        assignNonNullValuesBA(oldDataTrang2,data);
        callbackDone(data);
    })
}

function getThongtinTongket(idBA, loaiBA, callbackDone, callbackFail) {
    $.get("load-thong-tin-chi-tiet-page3?iD="+idBA+"&loaiBa="+loaiBA).fail(function() {
        callbackFail();
    }).done(function(data) {
        assignNonNullValuesBA(oldDataTrang3,data);
        callbackDone(data);
    })
}

function updateNgaylamVaBSHSBA(dataSubmit, callbackDone, callbackFail) {
    $.post("cmu_post_CMU_HSBA_UPD_PAGE2", {
        url: [
            singletonObject.dvtt,
            dataSubmit.ID,
            dataSubmit.NGAYBA,
            dataSubmit.MAKHOA,
            dataSubmit.MABACSILAMBENHAN,
            thongtinhsba.thongtinbn.VOBENHAN[0].ID_VBA
        ].join("```")
    }).done(function(data) {
        callbackDone(data);
    }).fail(function() {
        callbackFail();
    });
}

function updateThongtinPage3(dataSubmit, callbackDone, callbackFail) {
    $.post("cmu_post", {
        url: [
            singletonObject.dvtt,
            dataSubmit.MABACSIDIEUTRI,
            dataSubmit.MAKHOA_KETHUC,
            dataSubmit.id,
            dataSubmit.quaTrinhBenhLy,
            dataSubmit.tomTatKetQuaXNCLS,
            dataSubmit.phuongPhapDieuTri,
            dataSubmit.tinhTrangNguoiBenhRaVien,
            thongtinhsba.thongtinbn.STT_BENHAN,
            "CMU_HSBA_UPD_PAGE3"
        ].join("```")
    }).done(function(data) {
        callbackDone(data);
    }).fail(function() {
        callbackFail();
    });
}

function updateQuanlynbvaChandoan(obj, callbackDone, callbackFail) {
    var data = getAllRowDataJqgrid("hsba_tthc_listchuyenkhoa");
    var dataTreSoSinh = getAllRowDataJqgrid("TABLETRESOSINHT1");
    data.forEach(function(item) {
        item.SONGAY_DT = item.SONGAY_DT.toString();
    });
    data.sort((a, b) =>
        moment(a.THOIGIANCHUYENDI, 'DD/MM/YYYY HH:mm').diff(
            moment(b.THOIGIANCHUYENDI, 'DD/MM/YYYY HH:mm')
        )
    );
    $.post("cmu_post_CMU_HSBA_TTHC_QLNB_UPD_V2", {
        url: [
            singletonObject.dvtt,
            thongtinhsba.thongtinbn.VOBENHAN[0].ID,
            obj.hsba_tthc_qlnbngayvaokhoa,
            obj.hsba_tthc_qlnbsongay,
            obj.hsba_tthc_qlnbicd.toUpperCase(),
            obj.hsba_tthc_qlnbtenicd,
            JSON.stringify(data),
            JSON.stringify(dataTreSoSinh),
        ].join("```"),
        async: false,
    }).done(function(data) {
        callbackDone(data);
    }).fail(function() {
        callbackFail();
    })
}

function loadttNV(sttBenhAn) {
    var params = {
        stt_benhan: sttBenhAn,
    };
    $.ajax({
        url: 'LoadThongTinNhapVienVBA',
        contentType: 'application/json; charset=utf-8',
        data: params,
        type: 'GET',
        success: function (data) {
            if (data){
                thongtinhsba.phieunhapvien = data;
            }
        },
        fail: function () {
            notifiToClient("Red", "Lỗi load thông tin nhập viện");
        }
    });
}

function getThongTinPhieuDieuTriDauTien() {
    var data = {
        sovaovien: thongtinhsba.thongtinbn.SOVAOVIEN,
        soVaoVienDT: thongtinhsba.thongtinbn.SOVAOVIEN_DT,
        sttBenhAn: thongtinhsba.thongtinbn.STT_BENHAN,
    };

    $.ajax({
        type: 'GET',
        contentType: 'application/json',
        dataType: 'json',
        url: 'get-pdt-dau-tien?' + $.param(data),
        complete: function (response) {
            var data = response.responseJSON;
            if (data){
                thongtinhsba.phieudieutridautien = data;
            }
        },
        error: function (exp) {
            notifiToClient("Red", "Lỗi load thông tin phiếu điều trị đầu tiên!");
        }
    });
}

function getCanLamSangDaThucHien(){
    $.ajax({
        url: "cmu_list_VBA_BENHAN_DS_CANLAMSANG?url="+convertArray([
            singletonObject.dvtt,
            thongtinhsba.thongtinbn.STT_BENHAN,
            thongtinhsba.thongtinbn.SOVAOVIEN,
            thongtinhsba.thongtinbn.SOVAOVIEN_DT,
        ]),
        type:"GET",
        async: false
    }).done(function(response) {
        thongtinhsba.clsDaThucHien = response[0];
    }).fail(function() {
        callbackFail();
    })
}

function reloadFormVBAPage1(edit, hidden, idButton) {
    showLoaderIntoWrapId("hsba_tthc_chitietbenhan_wrap");
    loadThongTinVBATrang1();
    var formioBenhan = objectBenhan[thongtinhsba.thongtinbn.VOBENHAN[0].ID_VBA];
    formioBenhan.loadThongtinPage1();
    $("#title-vba-text").html(thongtinhsba.thongtinbn.VOBENHAN[0].TRANG1.VBA_INF.TITLE_REPORT);
    if (typeof formioBenhan.initObjectFormPage1 == "function" && thongtinhsba.thongtinbn.VOBENHAN[0].TRANG1.INFO) {
        Formio.createForm(document.getElementById('hsbaFormTrang1'),
            formioBenhan.initObjectFormPage1(edit, hidden),
            formioBenhan.script,
        ).then(function(form) {
            hideLoaderIntoWrapId("hsba_tthc_chitietbenhan_wrap");
            if(formioBenhan.callbackAfterLoadPage1) {
                formioBenhan.callbackAfterLoadPage1(form, function (data) {
                    if (data && data.data){
                        dataOld = JSON.parse(JSON.stringify(data.data));
                    }
                });
                if(edit) {
                    $("#hsba_tthc_chitietbenhan").hide();
                    $("#hsba_tthc_themchuyenkhoa").show();
                    $("#hsba_tthc_luuchitietbenhan").show();
                    $("#hsba_tthc_huychitietbenhan").show();
                } else {
                    $("#hsba_tthc_chitietbenhan").show();
                    $("#hsba_tthc_themchuyenkhoa").hide();
                    $("#hsba_tthc_luuchitietbenhan").hide();
                    $("#hsba_tthc_huychitietbenhan").hide();
                }
            }
        });
    } else {
        $("#hsbaFormTrang1").html("");
        $("#hsba_tthc_chitietbenhan").hide();
        $("#hsba_tthc_themchuyenkhoa").hide();
    }
    hideLoaderIntoWrapId("hsba_tthc_chitietbenhan_wrap");
    hideSelfLoading(idButton);
}

function reloadVBATrang2(){
    $("#hsba_vba_trang2").click();
}
function kySoVBATrang2(data, idButton) {
    var url = "";
    if (thongtinhsba.thongtinbn.VOBENHAN[0].ID_VBA === "NGOAITRU_CHUNG" ||
        thongtinhsba.thongtinbn.VOBENHAN[0].ID_VBA === "PHCN_BANT" ||
        thongtinhsba.thongtinbn.VOBENHAN[0].ID_VBA === "NGOAITRURHM" ||
        thongtinhsba.thongtinbn.VOBENHAN[0].ID_VBA === "PHATHAI"
    ){
        url = String.format('print-vobenhan-by-id?iD={0}&loaiBenhAn={1}&soVaoVien={2}&soVaoVienDt={3}&smartcafiletype={4}&pageToPrint=1',
            thongtinhsba.thongtinbn.VOBENHAN[0].ID, thongtinhsba.thongtinbn.VOBENHAN[0].ID_VBA,
            thongtinhsba.thongtinbn.SOVAOVIEN,
            thongtinhsba.thongtinbn.SOVAOVIEN_DT, 'pdf');
    } else {
        url = String.format('print-vobenhan-by-id?iD={0}&loaiBenhAn={1}&soVaoVien={2}&soVaoVienDt={3}&smartcafiletype={4}&pageToPrint=2',
            thongtinhsba.thongtinbn.VOBENHAN[0].ID, thongtinhsba.thongtinbn.VOBENHAN[0].ID_VBA,
            thongtinhsba.thongtinbn.SOVAOVIEN,
            thongtinhsba.thongtinbn.SOVAOVIEN_DT, 'pdf');
    }
    // var url = String.format('print-vobenhan-by-id?iD={0}&loaiBenhAn={1}&soVaoVien={2}&soVaoVienDt={3}&smartcafiletype={4}&pageToPrint=2',
    //     thongtinhsba.thongtinbn.VOBENHAN[0].ID, thongtinhsba.thongtinbn.VOBENHAN[0].ID_VBA,
    //     thongtinhsba.thongtinbn.SOVAOVIEN,
    //     thongtinhsba.thongtinbn.SOVAOVIEN_DT, 'pdf');
    kySoChung({
        dvtt: singletonObject.dvtt,
        userId: singletonObject.userId,
        url: url,
        loaiGiay: "PHIEU_NOITRU_VBATRANG2",
        maBenhNhan: String(thongtinhsba.thongtinbn.MA_BENH_NHAN),
        soBenhAn: thongtinhsba.thongtinbn.STT_BENHAN,
        soPhieuDichVu: String(thongtinhsba.thongtinbn.VOBENHAN[0].ID),
        nghiepVu: thongtinhsba.thongtinbn.VOBENHAN[0].ID_VBA,
        soVaoVien: thongtinhsba.thongtinbn.SOVAOVIEN,
        soVaoVienDT: thongtinhsba.thongtinbn.SOVAOVIEN_DT,
        keyword: data.keyword,
        userId: singletonObject.userId,
        fileName: "TRANG 2 " + thongtinhsba.thongtinbn.VOBENHAN[0].TRANG1.VBA_INF.TITLE_REPORT.toUpperCase() + " - " + thongtinhsba.thongtinbn.TEN_BENH_NHAN.toUpperCase(),
    }, function(dataKySo) {
        reloadVBATrang2();
        hideSelfLoading(idButton);
    });
    // getXMLHSBA({
    //     fileName: thongtinhsba.thongtinbn.VOBENHAN[0].ID_VBA,
    //     method: "sendXMLHoSoBenhAnEmr",
    //     maBenhNhan: thongtinhsba.thongtinbn.MA_BENH_NHAN,
    //     soVaoVien: thongtinhsba.thongtinbn.SOVAOVIEN,
    //     soBenhAn: thongtinhsba.thongtinbn.SOBENHAN,
    //     bant: singletonObject.bant == ""? 0: singletonObject.bant,
    //     sttDotDieuTri: thongtinhsba.thongtinbn.STT_DOTDIEUTRI,
    //     soVaoVienDT: thongtinhsba.thongtinbn.SOVAOVIEN_DT,
    //     loaiBenhAn: thongtinhsba.thongtinbn.VOBENHAN[0].ID_VBA,
    //     idVoBenhAn: thongtinhsba.thongtinbn.VOBENHAN[0].ID
    // }, function(response) {
    //     kySoChung({
    //         dvtt: singletonObject.dvtt,
    //         userId: singletonObject.userId,
    //         url: url,
    //         loaiGiay: "PHIEU_NOITRU_VBATRANG2",
    //         maBenhNhan: String(thongtinhsba.thongtinbn.MA_BENH_NHAN),
    //         soBenhAn: thongtinhsba.thongtinbn.STT_BENHAN,
    //         soPhieuDichVu: String(thongtinhsba.thongtinbn.VOBENHAN[0].ID),
    //         nghiepVu: thongtinhsba.thongtinbn.VOBENHAN[0].ID_VBA,
    //         soVaoVien: thongtinhsba.thongtinbn.SOVAOVIEN,
    //         soVaoVienDT: thongtinhsba.thongtinbn.SOVAOVIEN_DT,
    //         keyword: data.keyword,
    //         userId: singletonObject.userId,
    //         fileName: "TRANG 2 " + thongtinhsba.thongtinbn.VOBENHAN[0].TRANG1.VBA_INF.TITLE_REPORT.toUpperCase() + " - " + thongtinhsba.thongtinbn.TEN_BENH_NHAN.toUpperCase(),
    //         signXML: response.contentFile.buffer
    //     }, function(dataKySo) {
    //         reloadVBATrang2();
    //         hideSelfLoading(idButton);
    //     });
    // }, function() {
    //     notifiToClient("Red", "Lỗi ký số");
    //     hideSelfLoading(idButton);
    // })
}

function reloadVBATrang3(){
    $("#hsba_vba_trang3").click();
}
function kySoVBATrang3(data, idButton) {
    var url = String.format('print-vobenhan-by-id?iD={0}&loaiBenhAn={1}&soVaoVien={2}&soVaoVienDt={3}&smartcafiletype={4}&pageToPrint=3',
        thongtinhsba.thongtinbn.VOBENHAN[0].ID, thongtinhsba.thongtinbn.VOBENHAN[0].ID_VBA,
        thongtinhsba.thongtinbn.SOVAOVIEN,
        thongtinhsba.thongtinbn.SOVAOVIEN_DT, 'pdf');
    kySoChung({
        dvtt: singletonObject.dvtt,
        userId: singletonObject.userId,
        url: url,
        loaiGiay: "PHIEU_NOITRU_VBATRANG3",
        maBenhNhan: String(thongtinhsba.thongtinbn.MA_BENH_NHAN),
        soBenhAn: thongtinhsba.thongtinbn.STT_BENHAN,
        soPhieuDichVu: String(thongtinhsba.thongtinbn.VOBENHAN[0].ID),
        nghiepVu: thongtinhsba.thongtinbn.VOBENHAN[0].ID_VBA,
        soVaoVien: thongtinhsba.thongtinbn.SOVAOVIEN,
        soVaoVienDT: thongtinhsba.thongtinbn.SOVAOVIEN_DT,
        keyword: data.keyword,
        userId: singletonObject.userId,
        fileName: "TRANG TỔNG KẾT " + thongtinhsba.thongtinbn.VOBENHAN[0].TRANG1.VBA_INF.TITLE_REPORT.toUpperCase() + " - " + thongtinhsba.thongtinbn.TEN_BENH_NHAN.toUpperCase(),
    }, function(dataKySo) {
        reloadVBATrang3();
        hideSelfLoading(idButton);
    });
}

function getLabelValueBATrang2(key, data) {
    var value = "";
    /*switch (key) {
        case "TEN_BAC_SI_TRUONGKHOA":
            value = $("#tthc_xv_truongkhoa").find(':selected').text()
            break;
        default:
            value = data[key]
            break;
    }*/
    value = data[key];
    return keyLuuLogTrang2[key] + ": " +value;
}
function getLabelValueBATrang3(key, data) {
    var value = "";

    value = data[key];
    return keyLuuLogTrang3[key] + ": " +value;
}

function getLabelValueBATrang1(key, data) {
    var value = "";

    value = data[key];
    return keyLuuLogTrang1[key] + ": " +value;
}

function assignNonNullValuesBA(target, source) {
    for (const key in source) {
        if (source.hasOwnProperty(key) && source[key] !== null && source[key] !== undefined) {
            if (key == "NGAYBSLAMBENHAN"){
                target[key] = moment(source[key]).format("DD/MM/YYYY")
            }else{
                target[key] = source[key];
            }
        } else {
            target[key] = "";
        }
    }
}
function assignNonNullValuesTrang1(target, source) {
    for (const key in source) {
        if (source.hasOwnProperty(key) && source[key] !== null && source[key] !== undefined) {
            if(key == "CAN_LAM_SANG"){
                var value1 = source[key].CDHA;
                var value2 = source[key].KQ_CDHA;
                var value3 = source[key].KQ_TTPT;
                var value4 = source[key].KQ_XETNGHIEM;
                var value5 = source[key].XET_NGHIEM;
                target[key] = (value1 + value2 + value3 + value4 + value5).replace(/\n/g, '');
            } else if (key == "CHUYENKHOATHOIGIAN0"){
                var dinhDangChuoiThoiGian = /^(\d{2})\/(\d{2})\/(\d{4}) (\d{2}):(\d{2}):(\d{2})$/;
                if (dinhDangChuoiThoiGian.test(source[key])) {
                    // Định dạng "DD/MM/YYYY HH:mm:ss"
                    target[key] = source[key];
                } else {
                    var momentThoiGian = moment(source[key]);
                    target[key] = momentThoiGian.format('DD/MM/YYYY HH:mm:ss');
                }
            } else if (key == "THOI_GIAN_TU_VONG_TEXT"){
                target[key] = source[key].trim();
            }
            else{
                target[key] = source[key];
            }
        } else {
            target[key] = "";
        }
    }
}

$(function() {
    var formHSBA;
    var formHSBATongket;
    $("#tomtatketCLSDieutriTabs").tabs();

    // Start Handle event VBA trang 2
    $("#hsba_vba_trang2").click(function() {
        var formioBenhan = objectBenhan[thongtinhsba.thongtinbn.VOBENHAN[0].ID_VBA];
        initSelect2IfnotIntance("ttba_khoabacsilambenhan",singletonObject.danhsachphongban, "MAKHOA", "TENKHOA", false, false);
        loadttNV(thongtinhsba.thongtinbn.STT_BENHAN);
        getThongTinPhieuDieuTriDauTien();
        getCanLamSangDaThucHien();
        Formio.createForm(document.getElementById('hsbaFormTrang2'),
            formioBenhan.initObjectFormPage2(),
            formioBenhan.script,
        ).then(function(form) {
            getFilesign769(
                "PHIEU_NOITRU_VBATRANG2",
                thongtinhsba.thongtinbn.VOBENHAN[0].ID,
                -1,
                singletonObject.dvtt,
                thongtinhsba.thongtinbn.SOVAOVIEN,
                thongtinhsba.thongtinbn.SOVAOVIEN_DT,
                '-1',
                function(dataKySo) {
                    if (dataKySo.length > 0) {
                        thongtinhsba.thongtinbn.VOBENHAN[0].KEYSIGN_T2 = dataKySo[0].KEYSIGN;
                        form.components.forEach(component => {
                            component.disabled = true;
                        });
                        form.redraw();
                        $("#ttba_btn_luuthongtin").hide();
                        $("#ttba_btn_kysopage2").hide();
                        $("#ttba_btn_huykysopage2").show();
                    } else {
                        thongtinhsba.thongtinbn.VOBENHAN[0].KEYSIGN_T2 = null;
                        $("#ttba_btn_luuthongtin").show();
                        $("#ttba_btn_kysopage2").show();
                        $("#ttba_btn_huykysopage2").hide();
                    }
                },
            );
            formHSBA = form;
            if(formioBenhan.callbackAfterLoad) {
                formioBenhan.callbackAfterLoad(form, function (data) {
                    if (data && data.data){
                        dataOld = JSON.parse(JSON.stringify(data.data));
                    }
                });
            }
        });
    })
    $("#ttba_btn_luuthongtin").click(function() {
        var idButton = this.id;
        showSelfLoading(idButton);
        var formioBenhan = objectBenhan[thongtinhsba.thongtinbn.VOBENHAN[0].ID_VBA];
        formioBenhan.save(this, function(data){
            $("#hsba_vba_trang2").click();
            notifiToClient("Green", "Lưu thành công");
            hideSelfLoading(idButton);
        }, function(data) {
            dataNew = JSON.parse(JSON.stringify(data));
            var diffLog = luuLogHSBAChinhSuaFormio(dataOld, dataNew, thongtinhsba.thongtinbn.VOBENHAN[0].ID_VBA, keyLuuLogTrang2);
        });
    });
    $("#ttba_btn_kysopage2").click(function() {
        var idButton = this.id;
        showSelfLoading(idButton);
        var formioBenhan = objectBenhan[thongtinhsba.thongtinbn.VOBENHAN[0].ID_VBA];
        formioBenhan.save(this, function(data){
            kySoVBATrang2(data, idButton);
        });
    });
    $(".ttba_btn_xempage2").click(function() {
        getFilesign769(
            "PHIEU_NOITRU_VBATRANG2",
            thongtinhsba.thongtinbn.VOBENHAN[0].ID,
            -1,
            singletonObject.dvtt,
            thongtinhsba.thongtinbn.SOVAOVIEN,
            thongtinhsba.thongtinbn.SOVAOVIEN_DT,
            '-1'
            ,function(dataKySo) {
                if(dataKySo.length > 0) {
                    getCMUFileSigned769(dataKySo[0].KEYMINIO,"pdf")
                } else {
                    if (thongtinhsba.thongtinbn.VOBENHAN[0].ID_VBA == 'NGOAITRU_CHUNG' ||
                        thongtinhsba.thongtinbn.VOBENHAN[0].ID_VBA == 'NGOAITRURHM' ||
                        thongtinhsba.thongtinbn.VOBENHAN[0].ID_VBA == 'PHCN_BANT' ||
                        thongtinhsba.thongtinbn.VOBENHAN[0].ID_VBA == 'PHATHAI'){
                        var url = String.format('print-vobenhan-by-id?iD={0}&loaiBenhAn={1}&soVaoVien={2}&soVaoVienDt={3}&smartcafiletype={4}&pageToPrint=1',
                            thongtinhsba.thongtinbn.VOBENHAN[0].ID, thongtinhsba.thongtinbn.VOBENHAN[0].ID_VBA,
                            thongtinhsba.thongtinbn.SOVAOVIEN,
                            thongtinhsba.thongtinbn.SOVAOVIEN_DT, 'pdf');
                    } else {
                        var url = String.format('print-vobenhan-by-id?iD={0}&loaiBenhAn={1}&soVaoVien={2}&soVaoVienDt={3}&smartcafiletype={4}&pageToPrint=2',
                            thongtinhsba.thongtinbn.VOBENHAN[0].ID, thongtinhsba.thongtinbn.VOBENHAN[0].ID_VBA,
                            thongtinhsba.thongtinbn.SOVAOVIEN,
                            thongtinhsba.thongtinbn.SOVAOVIEN_DT, 'pdf');
                    }
                    previewPdfDefaultModal(url, 'previewPage2')
                }
            });
    });
    $("#ttba_btn_huykysopage2").click(function() {
        confirmToClient("Bạn có chắc sẽ hủy ký số phiếu này?", function() {
            huykysoFilesign769("PHIEU_NOITRU_VBATRANG2", thongtinhsba.thongtinbn.VOBENHAN[0].ID, singletonObject.userId, singletonObject.dvtt,
                thongtinhsba.thongtinbn.SOVAOVIEN, thongtinhsba.thongtinbn.SOVAOVIEN_DT, -1, function(data) {
                    reloadVBATrang2()
                })
        }, function () {

        })
    });
    // End Handle event VBA trang 2

    // Start Handle event VBA trang 3
    $("#hsba_vba_trang3").click(function() {
        var formioBenhan = objectBenhan[thongtinhsba.thongtinbn.VOBENHAN[0].ID_VBA];
        loadttNV(thongtinhsba.thongtinbn.STT_BENHAN);
        Formio.createForm(document.getElementById('hsbaFormTrang3'),
            formioBenhan.initObjectFormPage3()
            , formioBenhan.scriptTongket
        ).then(function(form) {
            getFilesign769(
                "PHIEU_NOITRU_VBATRANG3",
                thongtinhsba.thongtinbn.VOBENHAN[0].ID,
                -1,
                singletonObject.dvtt,
                thongtinhsba.thongtinbn.SOVAOVIEN,
                thongtinhsba.thongtinbn.SOVAOVIEN_DT,
                '-1',
                function(dataKySo) {
                    if (dataKySo.length > 0) {
                        thongtinhsba.thongtinbn.VOBENHAN[0].KEYSIGN_T3 = dataKySo[0].KEYSIGN;
                        form.components.forEach(component => {
                            component.disabled = true;
                        });
                        form.redraw();
                        $("#ttba_btn_luutongketba").hide();
                        $("#ttba_btn_kysopage3").hide();
                        $("#ttba_btn_huykysopage3").show();
                    } else {
                        thongtinhsba.thongtinbn.VOBENHAN[0].KEYSIGN_T3 = null;
                        $("#ttba_btn_luutongketba").show();
                        $("#ttba_btn_kysopage3").show();
                        $("#ttba_btn_huykysopage3").hide();
                    }
                },
            );
            formHSBATongket = form;
            if(formioBenhan.callbackAfterLoadTongket) {
                formioBenhan.callbackAfterLoadTongket(form, function (data) {
                    if (data && data.data){
                        dataOld = JSON.parse(JSON.stringify(data.data));
                        dataOld.NGAY_TONGKET = moment(dataOld.NGAY_TONGKET).format("DD/MM/YYYY HH:mm");
                    }
                });
            }
        });
    });
    $("#ttba_btn_luutongketba").click(function() {
        var idButton = this.id;
        showSelfLoading(idButton);
        var formioBenhan = objectBenhan[thongtinhsba.thongtinbn.VOBENHAN[0].ID_VBA];
        formioBenhan.saveTongket(this, function(){
            $("#hsba_vba_trang3").click();
            notifiToClient("Green", "Lưu thành công");
            hideSelfLoading(idButton);
        }, function(data) {
            dataNew = JSON.parse(JSON.stringify(data));
            var diffLog = luuLogHSBAChinhSuaFormio(dataOld, dataNew, thongtinhsba.thongtinbn.VOBENHAN[0].ID_VBA, keyLuuLogTrang3);
        });
    });
    $("#ttba_btn_kysopage3").click(function() {
        var idButton = this.id;
        showSelfLoading(idButton);
        var formioBenhan = objectBenhan[thongtinhsba.thongtinbn.VOBENHAN[0].ID_VBA];
        formioBenhan.saveTongket(this, function(data){
            kySoVBATrang3(data, idButton);
        });
    });
    $(".ttba_btn_xemtongketba").click(function() {
        getFilesign769(
            "PHIEU_NOITRU_VBATRANG3",
            thongtinhsba.thongtinbn.VOBENHAN[0].ID,
            -1,
            singletonObject.dvtt,
            thongtinhsba.thongtinbn.SOVAOVIEN,
            thongtinhsba.thongtinbn.SOVAOVIEN_DT,
            '-1'
            ,function(dataKySo) {
                if(dataKySo.length > 0) {
                    getCMUFileSigned769(dataKySo[0].KEYMINIO,"pdf")
                } else {
                    var url = String.format('print-vobenhan-by-id?iD={0}&loaiBenhAn={1}&soVaoVien={2}&soVaoVienDt={3}&smartcafiletype={4}&pageToPrint=3',
                        thongtinhsba.thongtinbn.VOBENHAN[0].ID, thongtinhsba.thongtinbn.VOBENHAN[0].ID_VBA,
                        thongtinhsba.thongtinbn.SOVAOVIEN,
                        thongtinhsba.thongtinbn.SOVAOVIEN_DT, 'pdf');
                    previewPdfDefaultModal(url, 'previewPage3')
                }
            });
    })
    $("#ttba_btn_huykysopage3").click(function() {
        confirmToClient("Bạn có chắc sẽ hủy ký số phiếu này?", function() {
            huykysoFilesign769("PHIEU_NOITRU_VBATRANG3", thongtinhsba.thongtinbn.VOBENHAN[0].ID, singletonObject.userId, singletonObject.dvtt,
                thongtinhsba.thongtinbn.SOVAOVIEN, thongtinhsba.thongtinbn.SOVAOVIEN_DT, -1, function(data) {
                    reloadVBATrang3()
                })
        }, function () {

        })
    });
    // End Handle event VBA trang 3

    $("#hsba_vba_trang1").click(function() {
        hideLoaderIntoWrapId("hsba_tthc_chitietbenhan_wrap");
        initGridChuyenkhoa()
        getListChuyenKhoaDaKhoiTaoVBA();
        reloadFormVBAPage1(0, 0, "hsba_vba_trang1");
    });

    $("#hsbaThayDoiBenhAn").click(function() {
        var dataVBA = thongtinhsba.thongtinbn.VOBENHAN[0];
        var arrTemp = [];
        if (dataVBA.KEYSIGN_T2) {
            arrTemp.push("trang 2");
        }
        if (dataVBA.KEYSIGN_T3) {
            arrTemp.push("trang 3");
        }
        if (dataVBA.KEYSIGN_T1) {
            arrTemp.push("trang 1");
        }
        if (arrTemp.length > 0) {
            notifiToClient("Red", "Phiếu đã ký số " + arrTemp.join(", ") + ". Không thể thay đổi thông tin");
            return;
        }
        confirmToClient("Bạn có chắc chắn muốn đổi bệnh án, Tất cả dữ liệu sẽ bị xoá?", function() {
            $.ajax({
                type: "POST",
                contentType: 'application/json; charset=utf-8',
                dataType: 'json',
                url: String.format("xoa-vo-benh-an?{0}", $.param({
                    iD: dataVBA.ID,
                    idVba: dataVBA.ID_VBA,
                    lyDo: "Đổi loại bệnh án",
                })),
                async: false,
                success: function (data) {
                    if(data.STATUS == "1"){
                        hsbaKhoitaobenhan();
                    }
                }
            })
        }, function () {

        })
    });

    $("#hsba_tthc_themchuyenkhoa").click(function() {
        initSelect2IfnotIntance("hsba_ttck_khoachuyen",singletonObject.danhsachphongban, "MAKHOA", "TENKHOA", false, false);
        $("#hsba_ttck_songaydt").val("")
        $("#hsba_tthc_themmoichuyenkhoa").show();
        $("#hsba_tthc_capnhatchuyenkhoa").hide();
        $("#modalFormhsbathongtinchuyenkhoa").modal("show");
    })
    $("#hsba_tthc_themmoichuyenkhoa").click(function() {
        var idButton = this.id;
        showSelfLoading(idButton)
        if(!$("#formHsbathongtinchuyenkhoa").valid()) {
            hideSelfLoading(idButton)
            return;
        }
        var data = getAllRowDataJqgrid("hsba_tthc_listchuyenkhoa");
        var objectKhoa = getObjectChuyenkhoa()
        data.push(objectKhoa)
        reloadGridChuyenKhoa(data);
        hideSelfLoading(idButton);
        $("#modalFormhsbathongtinchuyenkhoa").modal("hide");
    })
    $("#hsba_tthc_capnhatchuyenkhoa").click(function() {
        var idButton = this.id;
        showSelfLoading(idButton)
        if(!$("#formHsbathongtinchuyenkhoa").valid()) {
            hideSelfLoading(idButton)
            return;
        }
        var data = getAllRowDataJqgrid("hsba_tthc_listchuyenkhoa");
        var objectKhoa = getObjectChuyenkhoa()
        data.map(function (item, index) {
            if(item.MAKHOA == objectKhoa.MAKHOA) {
                return objectKhoa;
            }
            return item;
        })
        reloadGridChuyenKhoa(data);
        hideSelfLoading(idButton)
        $("#modalFormhsbathongtinchuyenkhoa").modal("hide");
    })

    $("[data-id=ttba_btn_xempage1]").click(function(){
        var url = String.format('print-vobenhan-by-id?iD={0}&loaiBenhAn={1}&soVaoVien={2}&soVaoVienDt={3}&smartcafiletype={4}&pageToPrint=1',
            thongtinhsba.thongtinbn.VOBENHAN[0].ID, thongtinhsba.thongtinbn.VOBENHAN[0].ID_VBA,
            thongtinhsba.thongtinbn.SOVAOVIEN,
            thongtinhsba.thongtinbn.SOVAOVIEN_DT, 'pdf');
        previewPdfDefaultModal(url, 'previewPage1')
    })

    $("#hsba_benhan_copycls").click(function() {
        var selRowIds = $("#hsba_list_cls_chidinh").jqGrid("getGridParam", "selarrrow");
        if( selRowIds.length == 0) {
            return notifiToClient("Red", "Vui lòng chọn ít nhất 1 chỉ định")
        }
        var string = "";
        for (var i = 0; i < selRowIds.length; i++) {
            var rowData = $("#hsba_list_cls_chidinh").jqGrid("getLocalRow", selRowIds[i]);
            string +=  rowData.TEN_XETNGHIEM + ";";
        }
        objectBenhan[thongtinhsba.thongtinbn.VOBENHAN[0].ID_VBA].copyChidinhCLS(string.substring(0, string.length - 1));
        $("#modalFormhsbacopyylenhcls").modal("hide");
    })

    $(document).on("reloadDSCLSChiDinh", {}, function(e, param) {
        initGridDSCLSChidinh();
        reloadDSCLSChiDinh()
    });

    $("#hsba_tthc_chitietbenhan").click(function() {
        var idButton = this.id;
        showSelfLoading(idButton);
        reloadFormVBAPage1(1, 0, idButton);
        assignNonNullValuesTrang1(oldDataTrang1,thongtinhsba.thongtinbn.VOBENHAN[0].TRANG1.INFO);
        // dataOld = JSON.parse(JSON.stringify(thongtinhsba.thongtinbn.VOBENHAN[0].TRANG1.INFO));
    });

    $("#hsba_tthc_huychitietbenhan").click(function() {
        var idButton = this.id;
        showSelfLoading(idButton);
        confirmToClient("Bạn có chắc chắn muốn hủy chỉnh sửa thông tin bệnh án?", function() {
            reloadFormVBAPage1(0, 0, idButton);
        }, function () {
            hideSelfLoading(idButton);
        })

    });

    $("#hsba_tthc_luuchitietbenhan").click(function() {
        var idButton = this.id;
        showSelfLoading(idButton);
        objectBenhan[thongtinhsba.thongtinbn.VOBENHAN[0].ID_VBA].savePage1(this, function(data){
            dataNew = JSON.parse(JSON.stringify(data));
            var diffLog = luuLogHSBAChinhSuaFormio(dataOld, dataNew, thongtinhsba.thongtinbn.VOBENHAN[0].ID_VBA, keyLuuLogTrang1);
        });
    });

    $("#ttba_btn_mauhsbapage3").click(function() {
        var formioBenhan = objectBenhan[thongtinhsba.thongtinbn.VOBENHAN[0].ID_VBA];
        if(typeof formioBenhan.getInfoMauHSBATongket == 'function') {
            var objectMau = formioBenhan.getInfoMauHSBATongket();
            var element = $("#mau_danhsachmaujson_wrap");
            element.attr("function-add", objectMau.insertMau);
            element.attr("function-chinhsua", objectMau.editMau);
            element.attr("function-select", objectMau.selectMau);
            element.attr("function-getdata", objectMau.getdataMau);
            element.attr("function-validate", objectMau.formioValidate);
            element.attr("data-key", objectMau.keyMauHSBA);
            $("#modalMauChungJSON").modal("show");
            $.loadDanhSachMauChungJSON(objectMau.keyMauHSBA)
        }
    })

    $("#ttba_btn_clonemaubenhan").click(function() {
        var formioBenhan = objectBenhan[thongtinhsba.thongtinbn.VOBENHAN[0].ID_VBA];
        if(typeof formioBenhan.getInfoMauHSBA == 'function') {
            var objectMau = formioBenhan.getInfoMauHSBA();
            var element = $("#mau_danhsachmaujson_wrap");
            element.attr("function-add", objectMau.insertMau);
            element.attr("function-chinhsua", objectMau.editMau);
            element.attr("function-select", objectMau.selectMau);
            element.attr("function-getdata", objectMau.getdataMau);
            element.attr("function-validate", objectMau.formioValidate);
            element.attr("data-key", objectMau.keyMauHSBA);
            $("#modalMauChungJSON").modal("show");
            $.loadDanhSachMauChungJSON(objectMau.keyMauHSBA)
        }
    })
    $(document).on("reloadDSTomtatCLS", {}, function(e, param) {
        initGridDSTomtatCLS();
        reloadDSTomtatCLS()
    });
    $("#formHsbatthcqlnb").validate({
        rules: {
            hsba_tthc_qlnbngayvaokhoa: {
                validDateTime: true
            },
            hsba_tthc_qlnbsongay: {
                min: 0
            },
            hsba_tthc_vaovienlanthu: {
                min: 0
            }
        }
    })
    $("#formHsbathongtinchuyenkhoa").validate({
        rules: {
            hsba_ttck_songaydt: {
                min: 0
            },
            hsba_ttck_ngaychuyen: {
                validDateTime: true
            },
            hsba_ttck_khoachuyen: {
                phaichonkhoa: true,
                required: true
            }
        }
    })
    combgridTenICD("hsba_tthc_qlnbtenicd", function(item) {
        $("#hsba_tthc_qlnbicd").val(item.ICD.toUpperCase());
        $("#hsba_tthc_qlnbtenicd").val(item.MO_TA_BENH_LY);
    })
    $.extend({
        copyDulieuphauthuatNgoaikhoa: function () {
            var rowData = getThongtinRowSelected("hsba_list_phauthuat");
            if(!rowData) {
                return notifiToClient(Red, "Chưa chọn dữ liệu cần copy");
            }
            var arrICDTruoc = rowData.CHAN_DOAN.split(" - ");
            var arrICDSau = rowData.CHANDOANSAUPT.match(/\((.*?)\)/)
            formPage1.getComponent('TENICD_TRUOC_PHAUTHUAT').setValue(rowData.CHAN_DOAN);
            formPage1.getComponent('ICD_TRUOC_PHAUTHUAT').setValue(arrICDTruoc[0]);
            formPage1.getComponent('ICD_SAU_PHAUTHUAT').setValue(arrICDSau && arrICDSau.length > 1? arrICDSau[1] : "");
            formPage1.getComponent('TENICD_SAU_PHAUTHUAT').setValue(rowData.CHANDOANSAUPT);
            $("#modalFormThongtinPhauthuat").modal("hide");
        },
        copyTomtatKetquaCLSPage3: function(dataSelected) {
            var tomTatKetQuaXNCLS = formHSBATongket.submission.data.tomTatKetQuaXNCLS? formHSBATongket.submission.data.tomTatKetQuaXNCLS: "";
            if(dataSelected.LOAI == 'XN') {
                tomTatKetQuaXNCLS += dataSelected.TEN_XETNGHIEM + ': ' + dataSelected.KET_QUA + '; ';
            }
            if(dataSelected.LOAI == 'CDHA') {
                tomTatKetQuaXNCLS += dataSelected.TEN_CDHA + ': ' + dataSelected.MO_TA + '; ';
            }
            if(dataSelected.LOAI == 'TTPT') {
                tomTatKetQuaXNCLS += dataSelected.TEN_DV + ': ' + dataSelected.TRINHTU_PTTT_XML5 + '; ';
            }
            formHSBATongket.submission = {
                data: {
                    ...formHSBATongket.submission.data,
                    tomTatKetQuaXNCLS
                }
            }
        },
        luuICDieutriPage1: function(icd, tenicd) {
            $.post("cmu_post", {
                url: [singletonObject.dvtt, thongtinhsba.thongtinbn.VOBENHAN[0].ID, icd, tenicd, 'CMU_HSBA_LUUICD_KHOADT'].join("```")
            })
        },
        luuSolanphauthuatPage1: function(solanphauthuat) {
            $.post("cmu_post", {
                url: [singletonObject.dvtt, thongtinhsba.thongtinbn.VOBENHAN[0].ID, solanphauthuat, 'CMU_HSBA_LUU_SOLANPT'].join("```")
            })
        },
        luuSongaysauphauthuatPage1: function(ngayravien) {
            return $.post("cmu_post", {
                url: [singletonObject.dvtt, thongtinhsba.thongtinbn.VOBENHAN[0].ID,
                    thongtinhsba.thongtinbn.SOVAOVIEN,
                    thongtinhsba.thongtinbn.SOVAOVIEN_DT,
                    ngayravien, 'CMU_HSBA_LUU_SONGAYSAUPT'].join("```")
            })
        },
        copyDulieuphauthuatPage3: function(formTongket, data) {
            var cloneData = {...formTongket.submission.data}

            var objectPhauthuat = {
                GIONGAY: moment(data.BATDAUMO, ['DD/MM/YYYY HH:mm']).toISOString(),
                PHUONGPHAP: data.PHUONG_PHAP_PHAU_THUAT +  "/"+ data.PHUONG_PHAP_VO_CAM,
                BACSIPHAUTHUAT: data.BSPHAUTHUAT,
                BACSIGAYME: data.BSGAYME,
            }
            if(cloneData.chiTietThuThuatPhauThuat) {
                cloneData.chiTietThuThuatPhauThuat.push(objectPhauthuat)
            } else {
                cloneData.chiTietThuThuatPhauThuat = [objectPhauthuat]
            }
            formTongket.submission =  {
                data: {...cloneData}
            }
        }
    })
    function initGridChuyenkhoa() {
        var list = $("#hsba_tthc_listchuyenkhoa");
        if(!list[0].grid) {
            list.jqGrid({
                url: '',
                datatype: "local",
                loadonce: true,
                height: 150,
                width: null,
                shrinkToFit: false,
                colModel: [
                    {label: "STT_LOGKHOAPHONG", name: 'STT_LOGKHOAPHONG', index: 'STT_LOGKHOAPHONG', align: "center", width: 10, hidden:true},
                    {label: "MAPHONGBAN", name: 'MAPHONGBAN', index: 'MAPHONGBAN', align: "center", width: 10, hidden:true},
                    {label: "Tên khoa", name: 'TEN_PHONGBAN', index: 'TEN_PHONGBAN', width: 200, align: "left"},
                    {label: "THOI_GIAN_CD", name: 'THOI_GIAN_CD', index: 'THOI_GIAN_CD', width: 10, align: "center", hidden: true},
                    {label: "Thời gian chuyển", name: 'THOI_GIAN_CD_TEXT', index: 'THOI_GIAN_CD_TEXT', width: 300, align: "center"},
                    {label: "THOIGIANCHUYENDI", name: 'THOIGIANCHUYENDI', index: 'THOIGIANCHUYENDI', width: 20, align: "center", hidden: true},
                    {label: "Số ngày ĐT", name: 'SONGAY_DT', index: 'SONGAY_DT', width: 100, align: "center"},
                ],
                viewrecords: true,
                footerrow: true,
                rowNum: 50,
                rownumbers: true,
                caption: "Danh sách các lần chuyển khoa",
                onSelectRow: function(id) {
                },
                onRightClickRow: function(id) {
                    if (id) {
                        $.contextMenu({
                            selector: '#hsba_tthc_listchuyenkhoa tr',
                            reposition : false,
                            callback: function (key, options) {
                                var rowSelected = getThongtinRowSelected("hsba_tthc_listchuyenkhoa")
                                if(key == 'xoa') {
                                    confirmToClient("Bạn có chắc chắn muốn xóa lần chuyển khoa này không?",
                                        function() {
                                            $("#hsba_tthc_listchuyenkhoa").jqGrid('delRowData', $("#hsba_tthc_listchuyenkhoa").jqGrid('getGridParam', 'selrow'));
                                        })
                                }
                                if(key == 'capnhat') {
                                    initSelect2IfnotIntance("hsba_ttck_khoachuyen",singletonObject.danhsachphongban, "MAKHOA", "TENKHOA", false, false);
                                    $("#hsba_ttck_songaydt").val(rowSelected.SONGAY_DT)
                                    $("#hsba_ttck_khoachuyen").val(rowSelected.MAKHOA).trigger("change");
                                    $("#hsba_ttck_ngaychuyen").val(rowSelected.THOIGIANCHUYENDI)
                                    $("#hsba_tthc_themmoichuyenkhoa").hide();
                                    $("#hsba_tthc_capnhatchuyenkhoa").show();
                                    $("#modalFormhsbathongtinchuyenkhoa").modal("show");
                                }
                            },
                            items: {
                                "capnhat": {name: '<p><i class="fa fa-pencil-square-o text-primary" aria-hidden="true"></i> Cập nhật</p>'},
                                "xoa": {name: '<p><i class="fa fa-trash-o text-danger" aria-hidden="true"></i> Xóa</p>'}
                            }
                        });

                    }
                },
            });
        }
    }

    function getListChuyenKhoaDaKhoiTaoVBA(idVoBenhAn = 0) {
        $("#hsba_tthc_listchuyenkhoa").jqGrid('clearGridData');
        $("#hsba_tthc_listchuyenkhoa").jqGrid('setGridParam',{
            datatype: 'json',
            url: 'list-lan-chuyen-khoa?' + $.param({idVoBenhAn: thongtinhsba.thongtinbn.VOBENHAN[0].ID})
        }).trigger('reloadGrid');
    }

    function reloadGridChuyenKhoa(data) {
        var list = $("#hsba_tthc_listchuyenkhoa");
        list[0].grid.beginReq();
        list.jqGrid("clearGridData");
        list.jqGrid('setGridParam', { data: data});
        list[0].grid.endReq();
        list.trigger('reloadGrid');
    }

    function getObjectChuyenkhoa() {
        var temp = $("#hsba_ttck_ngaychuyen").val().split(" ");
        var tGio = temp[1].split(":");
        var tNgay = temp[0].split("/");
        var currentRow = {
            STT_LOGKHOAPHONG: 0,
            MAPHONGBAN: $("#hsba_ttck_khoachuyen").val(),
            TEN_PHONGBAN: $("#hsba_ttck_khoachuyen option:selected").text(),
            THOIGIANCHUYENDI: $("#hsba_ttck_ngaychuyen").val(),
            THOI_GIAN_CD_TEXT: tGio[0] + " giờ " + tGio[1] + " phút, ngày " + tNgay[0] +" tháng " + tNgay[1] + " năm " + tNgay[2],
            SONGAY_DT: $("#hsba_ttck_songaydt").val(),
        }
        return currentRow;
    }

    function initGridDSCLSChidinh() {
        var list = $("#hsba_list_cls_chidinh")
        if(!list[0].grid) {
            list.jqGrid({
                url: '',
                datatype: "local",
                loadonce: true,
                height: 340,
                width: null,
                shrinkToFit: false,
                colModel: [
                    {label: "Mã DV", name: 'MA_XETNGHIEM', index: 'MA_XETNGHIEM', width: 100},
                    {label: "Tên cận lâm sàng", name: 'TEN_XETNGHIEM', index: 'TEN_XETNGHIEM', width: 400 },
                ],
                rowNum: 1000000,
                caption: "Danh sách phiếu chỉ định",
                multiselect: true
            });
        }
    }

    function reloadDSCLSChiDinh() {
        var url = 'cmu_getlist?url=' + convertArray([singletonObject.dvtt,
            thongtinhsba.thongtinbn.STT_BENHAN,
            "CMU_DSCLS_CHIDINH"]);
        $("#hsba_list_cls_chidinh").jqGrid('setGridParam', {
            datatype: 'json',
            url: url
        }).trigger('reloadGrid');
    }

    function initGridDSTomtatCLS() {
        var list = $("#list_tomtatket_xn")
        if(!list[0].grid) {
            $("#list_tomtatket_xn").jqGrid({
                datatype: "local",
                loadonce: false,
                height: 515,
                width: null,
                shrinkToFit: false,
                colModel: [
                    {label: "NGAYGIO_PHIEU",name: 'NGAYGIO_PHIEU', index: 'NGAYGIO_PHIEU', width: 10, hidden: true},
                    {label: "KET_QUA",name: 'KET_QUA', index: 'KET_QUA', width: 10, hidden: true},
                    {label: "DVTT",name: 'DVTT', index: 'DVTT', width: 10, hidden: true},
                    {label: "CANHBAO",name: 'CANHBAO', index: 'CANHBAO', width: 10, hidden: true},
                    {label: "ID_XNCHA",name: 'ID_XNCHA', index: 'ID_XNCHA', width: 10, hidden: true},
                    {label: "MA_XETNGHIEM",name: 'MA_XETNGHIEM', index: 'MA_XETNGHIEM', width: 10, hidden: true},
                    {label: "CO_DULIEUCON",name: 'CO_DULIEUCON', index: 'CO_DULIEUCON', width: 10, hidden: true},
                    {label: "Xét nghiệm",name: 'TEN_XETNGHIEM', index: 'TEN_XETNGHIEM', width: 300 ,cellattr: function (rowId, tv, rawObject, cm, rdata) {return 'style="white-space: normal;padding: 2px;"';}},
                    {label: "Kết quả", name: 'KET_QUA_TEXT', index: 'KET_QUA_TEXT', width: 150 ,
                        cellattr: function (rowId, tv, rawObject, cm, rdata) {return 'style="white-space: normal;padding: 2px;"';},
                        formatter: function (cellvalue, options, rowObject) {
                            var color;
                            if (rowObject.CANHBAO == '1') {
                                return cellvalue;
                            } else {
                                return '<span class="cellWithoutBackground" style="font-weight:bold">' + cellvalue + '</span>';
                            }
                        }
                    }
                ],
                grouping: true,
                groupingView: {
                    groupField: ["NGAYGIO_PHIEU"],
                    groupColumnShow: [false],
                    groupText: ['<b>{0}</b>'],
                    groupCollapse: false
                },
                multiselect: true,
                rowNum: 1000000,
                caption: "Danh sách xét nghiệm",
                onRightClickRow: function(id) {
                    if (id) {
                        $.contextMenu({
                            selector: '#list_tomtatket_xn tr',
                            reposition : false,
                            callback: function (key, options) {
                                var rowSelected = getThongtinRowSelected("list_tomtatket_xn")
                                if(key == 'copy') {
                                    $[$("#tomtatketCLSDieutriTabs").attr("data-function-copy")]({
                                        ...rowSelected,
                                        "LOAI": "XN"
                                    })
                                }
                            },
                            items: {
                                "copy": {name: '<p><i class="fa fa-clone text-primary" aria-hidden="true"></i> Copy</p>'},
                            }
                        });

                    }
                },
            });
        }

        var listCdha = $("#list_tomtatket_cdha")
        if(!listCdha[0].grid) {
            listCdha.jqGrid({
                datatype: "local",
                loadonce: false,
                height: 515,
                width: null,
                shrinkToFit: false,
                colModel: [
                    {label: "TEN_LOAI_CDHA",name: 'TEN_LOAI_CDHA', index: 'TEN_LOAI_CDHA', width: 10, hidden: true},
                    {label: "Tên",name: 'TEN_CDHA', index: 'TEN_CDHA', width: 300 ,cellattr: function (rowId, tv, rawObject, cm, rdata) {return 'style="white-space: normal;padding: 2px;"';}},
                    {label: "Kết quả", name: 'MO_TA', index: 'MO_TA', width: 400 ,cellattr: function (rowId, tv, rawObject, cm, rdata) {return 'style="white-space: normal;padding: 2px;"';}},
                ],
                grouping: true,
                groupingView: {
                    groupField: ["TEN_LOAI_CDHA"],
                    groupColumnShow: [false],
                    groupText: ['<b>{0}</b>'],
                    groupCollapse: false
                },
                multiselect: true,
                rowNum: 1000000,
                caption: "Danh sách CĐHA",
                onRightClickRow: function(id) {
                    if (id) {
                        $.contextMenu({
                            selector: '#list_tomtatket_cdha tr',
                            reposition : false,
                            callback: function (key, options) {
                                var rowSelected = getThongtinRowSelected("list_tomtatket_cdha")
                                if(key == 'copy') {
                                    $[$("#tomtatketCLSDieutriTabs").attr("data-function-copy")]({
                                        ...rowSelected,
                                        "LOAI": "CDHA"
                                    })
                                }
                            },
                            items: {
                                "copy": {name: '<p><i class="fa fa-clone text-primary" aria-hidden="true"></i> Copy</p>'},
                            }
                        });

                    }
                }
            });
        }

        var listTTPT = $("#list_tomtatket_ttpt")
        if(!listTTPT[0].grid) {
            listTTPT.jqGrid({
                datatype: "local",
                loadonce: false,
                height: 515,
                width: null,
                shrinkToFit: false,
                colModel: [
                    {label: "STT_DOTDIEUTRI",name: 'STT_DOTDIEUTRI', index: 'STT_DOTDIEUTRI', width: 10, hidden: true},
                    {label: "NGAY_CHI_DINH",name: 'NGAY_CHI_DINH', index: 'NGAY_CHI_DINH', width: 10, hidden: true},
                    {label: "STT_DIEUTRI",name: 'STT_DIEUTRI', index: 'STT_DIEUTRI', width: 10, hidden: true},
                    {label: "Tên thủ thuât/phẫu thuật", name: 'TEN_DV', index: 'TEN_DV', width: 350},
                    {label: "Tường trình",name: 'TRINHTU_PTTT_XML5', index: 'TRINHTU_PTTT_XML5', width: 400},
                    {label: "Bác sĩ chỉ định",name: 'BSDIEUTRI', index: 'BSDIEUTRI', width: 150}
                ],
                multiselect: true,
                rowNum: 1000000,
                caption: "Danh sách thủ thuật/phẫu thuật",
                onRightClickRow: function(id) {
                    if (id) {
                        $.contextMenu({
                            selector: '#list_tomtatket_ttpt tr',
                            reposition : false,
                            callback: function (key, options) {
                                var rowSelected = getThongtinRowSelected("list_tomtatket_ttpt")
                                if(key == 'copy') {
                                    $[$("#tomtatketCLSDieutriTabs").attr("data-function-copy")]({
                                        ...rowSelected,
                                        "LOAI": "TTPT"
                                    })
                                }
                            },
                            items: {
                                "copy": {name: '<p><i class="fa fa-clone text-primary" aria-hidden="true"></i> Copy</p>'},
                            }
                        });

                    }
                }
            });
        }

    }

    function reloadDSTomtatCLS() {
        var url = 'cmu_list_CMU_NOI_XETNGHIEMDATHUCHIEN?url='
            + convertArray([thongtinhsba.thongtinbn.STT_BENHAN, thongtinhsba.thongtinbn.SOVAOVIEN, singletonObject.dvtt]);
        loadDataGridGroupBy($("#list_tomtatket_xn"), url);

        var urlCDHA = 'cmu_list_CMU_NOI_CDHADATHUCHIEN?url='
            + convertArray([thongtinhsba.thongtinbn.STT_BENHAN, thongtinhsba.thongtinbn.SOVAOVIEN, singletonObject.dvtt]);
        loadDataGridGroupBy($("#list_tomtatket_cdha"), urlCDHA);

        var urlTTPT = 'cmu_list_CMU_NOI_TTPTDATHUCHIEN?url='
            + convertArray([thongtinhsba.thongtinbn.STT_BENHAN, thongtinhsba.thongtinbn.SOVAOVIEN, singletonObject.dvtt]);
        loadDataGridGroupBy($("#list_tomtatket_ttpt"), urlTTPT);
    }

    $("#tomtatketCLSCopySel").click(function () {
        let copyXN = "", copyCDHA = "", copyTTPT = "";
        $("#list_tomtatket_xn").jqGrid("getGridParam", "selarrrow").forEach(function(rowId) {
            let rowXN = $("#list_tomtatket_xn").jqGrid("getRowData", rowId);
            copyXN += rowXN.TEN_XETNGHIEM + ': ' + rowXN.KET_QUA + '; ';
        });
        $("#list_tomtatket_cdha").jqGrid("getGridParam", "selarrrow").forEach(function(rowId) {
            let rowCDHA = $("#list_tomtatket_cdha").jqGrid("getRowData", rowId);
            copyCDHA += rowCDHA.TEN_CDHA + ': ' + rowCDHA.MO_TA + '; ';
        });
        $("#list_tomtatket_ttpt").jqGrid("getGridParam", "selarrrow").forEach(function(rowId) {
            let rowTTPT = $("#list_tomtatket_ttpt").jqGrid("getRowData", rowId);
            copyTTPT += rowTTPT.TEN_DV + ': ' + rowTTPT.TRINHTU_PTTT_XML5 + '; ';
        });
        let tomTatKetQuaXNCLS = formHSBATongket.submission.data.tomTatKetQuaXNCLS ?
                                        formHSBATongket.submission.data.tomTatKetQuaXNCLS : "";
        tomTatKetQuaXNCLS += (copyXN + copyCDHA + copyTTPT);
        formHSBATongket.submission = {
            data: {
                ...formHSBATongket.submission.data,
                tomTatKetQuaXNCLS
            }
        }
        notifiToClient("Green", "Đã Copy xong");
    });
});