$(function() {
    var formBienBanKiemThaoTV
    var thongTinBienBanKTTVTruocChinhSua = {};
    var keyLogBBTV = {
        NGAY_TU_VONG: 'Tử vong lúc',
        MAKHOA: 'Tại khoa',
        NGAY_KIEM_DIEM_TU_VONG: 'kiểm thảo tử vong',
        CHU_TOA: 'Chủ toạ',
        THU_KY: 'Thư ký',
        THANH_VIEN: 'Thành viên tham gia',
        TOM_TAT: 'Tóm tắt',
        KET_LUAN: 'Kết luận'
    }
    var oldData = {};
    var bbtvDuLieuBanDau = {}

    $("#tthc_xv_bienbankiemthaotv, #hsba_bbkiemthaotv").click(function () {
        showBienBanKiemThaoTV()
    });
    $("#bienbankiemthaotv_kysosl").change(function () {
        var nguoiKy = $(this).val();
        var arrTemp = [];
        var maxCreateDate = null;
        var maxCreateDataObject = null;

        getFilesign769("PHIEU_NOITRU_BIENBANKIEMTHAOTV_THUKY", thongtinhsba.thongtinbn.SOVAOVIEN, -1, singletonObject.dvtt,
            thongtinhsba.thongtinbn.SOVAOVIEN, thongtinhsba.thongtinbn.SOVAOVIEN_DT, -1, function(dataKySo) {
                if (dataKySo.length > 0) {
                    arrTemp.push(dataKySo[0])
                }
            });
        getFilesign769("PHIEU_NOITRU_BIENBANKIEMTHAOTV_CHUTRI", thongtinhsba.thongtinbn.SOVAOVIEN, -1, singletonObject.dvtt,
            thongtinhsba.thongtinbn.SOVAOVIEN, thongtinhsba.thongtinbn.SOVAOVIEN_DT, -1, function(dataKySo) {
                if (dataKySo.length > 0) {
                    arrTemp.push(dataKySo[0])
                }
            });
        if(arrTemp.length > 0) {
            $.each(arrTemp, function (index, dataObject) {
                var createDate = moment(dataObject.CREATE_DATE_STRING, "DD/MM/YYYY HH:mm:ss");
                if (maxCreateDate === null || createDate.isAfter(maxCreateDate)) {
                    maxCreateDate = createDate;
                    maxCreateDataObject = dataObject;
                }
            });
        }

        if (nguoiKy == "KYSOTHUKY") {
            getFilesign769("PHIEU_NOITRU_BIENBANKIEMTHAOTV_THUKY", thongtinhsba.thongtinbn.SOVAOVIEN, -1, singletonObject.dvtt,
                thongtinhsba.thongtinbn.SOVAOVIEN, thongtinhsba.thongtinbn.SOVAOVIEN_DT, -1, function (dataKySo) {
                    if (dataKySo.length > 0) {
                        if (maxCreateDataObject.KY_HIEU_PHIEU == "PHIEU_NOITRU_BIENBANKIEMTHAOTV_THUKY") {
                            $("#bienbankiemthaotv_huykyso").show();
                            $("#bienbankiemthaotv_kyso").hide();
                        } else {
                            $("#bienbankiemthaotv_huykyso").hide();
                            $("#bienbankiemthaotv_kyso").hide();
                        }
                    } else {
                        $("#bienbankiemthaotv_huykyso").hide();
                        $("#bienbankiemthaotv_kyso").show();
                    }
                });
        } else if (nguoiKy == "KYSOCHUTRI") {
            getFilesign769("PHIEU_NOITRU_BIENBANKIEMTHAOTV_CHUTRI", thongtinhsba.thongtinbn.SOVAOVIEN, -1, singletonObject.dvtt,
                thongtinhsba.thongtinbn.SOVAOVIEN, thongtinhsba.thongtinbn.SOVAOVIEN_DT, -1, function (dataKySo) {
                    if (dataKySo.length > 0) {
                        if (maxCreateDataObject.KY_HIEU_PHIEU == "PHIEU_NOITRU_BIENBANKIEMTHAOTV_CHUTRI") {
                            $("#bienbankiemthaotv_huykyso").show();
                            $("#bienbankiemthaotv_kyso").hide();
                        } else {
                            $("#bienbankiemthaotv_huykyso").hide();
                            $("#bienbankiemthaotv_kyso").hide();
                        }
                    } else {
                        $("#bienbankiemthaotv_huykyso").hide();
                        $("#bienbankiemthaotv_kyso").show();
                    }
                });
        }
    });

    $('#bienbankiemthaotv_in').click(function () {
        var params = {
            THOIGIANVAOVIEN: thongtinhsba.thongtinbn.NGAYGIO_NHAPVIEN ?? thongtinhsba.thongtinbn.NGAYNHAPVIEN,
            GIOI_TINH: thongtinhsba.thongtinbn.GIOI_TINH_NUM ? thongtinhsba.thongtinbn.GIOI_TINH_NUM : thongtinhsba.thongtinbn.GIOI_TINH,
        }
        getUrlBienBanKiemThaoTV(params).then(objReturn => {
            if (objReturn.isError == 0) {
                previewPdfDefaultModal(objReturn.url, 'preview_bienbankiemthaotuvong');
            } else {
                notifiToClient("Red", objReturn.message);
            }
        }).catch(error => {
            notifiToClient("Red", error.message || "Lỗi không xác định");
        });
    });

    $("#bienbankiemthaotv_luu").on("click", function () {
        var btnAction = $('#bienbankiemthaotv_luu').attr("data-action");
        if (btnAction == "THEM"){
            themBienBanKTTV();
        }else{
            updateBienBanKTTV();
        }
    });

    function themBienBanKTTV(){
        showSelfLoading("bienbankiemthaotv_luu");
        formBienBanKiemThaoTV.emit("checkValidity");
        if (!formBienBanKiemThaoTV.checkValidity(null, false, null, true)) {
            hideSelfLoading(bienbankiemthaotv_luu);
            return;
        }

        var actionUrl;
        var url;
        var dataSubmit = formBienBanKiemThaoTV.submission.data;

        actionUrl = "cmu_post";
        url = [
            singletonObject.dvtt,
            thongtinhsba.thongtinbn.SOVAOVIEN,
            thongtinhsba.thongtinbn.MA_BENH_NHAN,
            moment(dataSubmit.THOIGIANHOP).format("DD/MM/YYYY HH:mm"),
            dataSubmit.DIADIEMHOP,
            dataSubmit.CHUTRI,
            dataSubmit.THUKY,
            dataSubmit.THANHVIEN,
            dataSubmit.QUOCTICH,
            dataSubmit.CCCD,
            moment(dataSubmit.NGAYCAPCCCD).format("DD/MM/YYYY"),
            dataSubmit.NOICAPCCCD,
            moment(dataSubmit.THOIGIANTUVONG).format("DD/MM/YYYY HH:mm"),
            dataSubmit.KHOATUVONG,
            dataSubmit.NGUYENNHANTUVONG,
            dataSubmit.TOMTATBENHSU,
            dataSubmit.TINHTRANGVAOVIEN,
            dataSubmit.CHANDOAN,
            JSON.stringify(dataSubmit.CHITIETDIENBIENBENH),
            dataSubmit.DIENBIENKHAC,
            dataSubmit.NGUYENNHANKHACHQUAN,
            dataSubmit.KETLUAN,
            singletonObject.userId,
            "CMU_BIENBANKIEMTHAOTV_INSERT"
        ];

        $.post(actionUrl, {
            url: url.join('```')
        }).done(function (data) {
            if (data > 0) {
                notifiToClient("Green", MESSAGEAJAX.SUCCESS);
                var noidung = ["Số phiếu:"+ data]
                dataSubmit.CHITIETDIENBIENBENH = JSON.stringify(dataSubmit.CHITIETDIENBIENBENH)
                for (const key in dataSubmit) {
                    noidung.push(formBienBanKiemThaoTV.getComponent(key).label.trim(":") + ": " + getValueOfFormIO(formBienBanKiemThaoTV.getComponent(key)));
                }
                var dataLog = {
                    SOVAOVIEN: thongtinhsba.thongtinbn.SOVAOVIEN,
                    LOAI: LOGHSBALOAI.BIENBANKIEMTHAOTV.KEY,
                    NOIDUNGBANDAU: "",
                    NOIDUNGMOI: noidung.join("; "),
                    USERID: singletonObject.userId,
                    ACTION: LOGHSBAACTION.INSERT.KEY,
                }
                luuLogHSBATheoBN(dataLog);
                $("#tthc_xv_bienbankiemthaotv, #hsba_bbkiemthaotv").click();
            }
        }).fail(function() {
            notifiToClient("Red", MESSAGEAJAX.ERROR);
        }).always(function () {
            hideSelfLoading("bienbankiemthaotv_luu");
        });
    }

    function updateBienBanKTTV(){
        showSelfLoading("bienbankiemthaotv_luu");
        formBienBanKiemThaoTV.emit("checkValidity");
        if (!formBienBanKiemThaoTV.checkValidity(null, false, null, true)) {
            hideSelfLoading(bienbankiemthaotv_luu);
            return;
        }

        var actionUrl;
        var url;
        var dataSubmit = formBienBanKiemThaoTV.submission.data;

        actionUrl = "cmu_post";
        url = [
            dataSubmit.ID,
            singletonObject.dvtt,
            thongtinhsba.thongtinbn.MA_BENH_NHAN,
            moment(dataSubmit.THOIGIANHOP).format("DD/MM/YYYY HH:mm"),
            dataSubmit.DIADIEMHOP,
            dataSubmit.CHUTRI,
            dataSubmit.THUKY,
            dataSubmit.THANHVIEN,
            dataSubmit.QUOCTICH,
            dataSubmit.CCCD,
            moment(dataSubmit.NGAYCAPCCCD).format("DD/MM/YYYY"),
            dataSubmit.NOICAPCCCD,
            moment(dataSubmit.THOIGIANTUVONG).format("DD/MM/YYYY HH:mm"),
            dataSubmit.KHOATUVONG,
            dataSubmit.NGUYENNHANTUVONG,
            dataSubmit.TOMTATBENHSU,
            dataSubmit.TINHTRANGVAOVIEN,
            dataSubmit.CHANDOAN,
            JSON.stringify(dataSubmit.CHITIETDIENBIENBENH),
            dataSubmit.DIENBIENKHAC,
            dataSubmit.NGUYENNHANKHACHQUAN,
            dataSubmit.KETLUAN,
            "CMU_BIENBANKIEMTHAOTV_UPDATE"
        ];

        $.post(actionUrl, {
            url: url.join('```')
        }).done(function (data) {
            if (data > 0) {
                notifiToClient("Green", MESSAGEAJAX.SUCCESS);
                var noidungold = []
                var noidungnew = []
                var luutru = ""
                dataSubmit.CHITIETDIENBIENBENH = JSON.stringify(dataSubmit.CHITIETDIENBIENBENH)
                dataSubmit.THOIGIANHOP = moment(dataSubmit.THOIGIANHOP).format("DD/MM/YYYY HH:mm")
                dataSubmit.NGAYCAPCCCD = moment(dataSubmit.NGAYCAPCCCD).format("DD/MM/YYYY")
                dataSubmit.THOIGIANTUVONG = moment(dataSubmit.THOIGIANTUVONG).format("DD/MM/YYYY HH:mm")
                var diffObject = findDifferencesBetweenObjects(thongTinBienBanKTTVTruocChinhSua, dataSubmit);
                for (const key in diffObject) {
                    try {
                        luutru = formBienBanKiemThaoTV.getComponent(key).label
                        if (luutru) {
                            noidungold.push(luutru.trim(":") + ": " + thongTinBienBanKTTVTruocChinhSua[key]);
                            noidungnew.push(luutru.trim(":") + ": " + getValueOfFormIO(formBienBanKiemThaoTV.getComponent(key)));                        }
                    } catch (error) {
                        // console.log("Error: ", key);
                    }
                }
                var dataLog = {
                    SOVAOVIEN: thongtinhsba.thongtinbn.SOVAOVIEN,
                    LOAI: LOGHSBALOAI.BIENBANKIEMTHAOTV.KEY,
                    NOIDUNGBANDAU: noidungold.join("; "),
                    NOIDUNGMOI: noidungnew.join("; "),
                    USERID: singletonObject.userId,
                    ACTION: LOGHSBAACTION.EDIT.KEY,
                }
                luuLogHSBATheoBN(dataLog);
                $("#tthc_xv_bienbankiemthaotv, #hsba_bbkiemthaotv").click();
            }
        }).fail(function() {
            notifiToClient("Red", MESSAGEAJAX.ERROR);
        }).always(function () {
            hideSelfLoading("bienbankiemthaotv_luu");
        });
    }

    $("#bienbankiemthaotv_xoa").click(function () {
        confirmToClient("Bạn có chắc chắn muốn xóa biên bản kiểm thảo tử vong này?", function() {
            var arr = [singletonObject.dvtt, thongtinhsba.thongtinbn.SOVAOVIEN];
            var url = "cmu_post_CMU_BIENBANKIEMTHAOTV_DELETE";
            $.post(url, {
                url: arr.join("```")
            }).done(function (data) {
                if (data === "1") {
                    // luuLogBBTV(3);
                    notifiToClient("Green", MESSAGEAJAX.SUCCESS);
                    var formData = { ...formBienBanKiemThaoTV.submission.data}
                    var noidung = ["Số phiếu:"+ thongTinBienBanKTTVTruocChinhSua.ID]
                    for (const key in formData) {
                        try {
                            var label = formBienBanKiemThaoTV.getComponent(key).label;
                            if (label) {
                                noidung.push(formBienBanKiemThaoTV.getComponent(key).label + ": " + getValueOfFormIO(formBienBanKiemThaoTV.getComponent(key)));
                            }
                        } catch (error) {
                            // console.log("Error: ", error);
                        }
                    }
                    var dataLog = {
                        SOVAOVIEN: thongtinhsba.thongtinbn.SOVAOVIEN,
                        LOAI: LOGHSBALOAI.BIENBANKIEMTHAOTV.KEY,
                        NOIDUNGBANDAU: noidung.join("; "),
                        NOIDUNGMOI: "",
                        USERID: singletonObject.userId,
                        ACTION: LOGHSBAACTION.DELETE.KEY,
                    }
                    luuLogHSBATheoBN(dataLog);
                    $("#tthc_xv_bienbankiemthaotv, #hsba_bbkiemthaotv").click();
                } else {
                    notifiToClient("Red", MESSAGEAJAX.ERROR);
                }
            }).fail(function() {
                notifiToClient("Red", MESSAGEAJAX.ERROR);
            })
        }, function () {

        })
    })

    function showBienBanKiemThaoTV() {
        $("#modalFormBienBanKiemThaoTV").modal("show");
        addTextTitleModal("titleFormBienBanKiemThaoTV", " Biên bản kiểm thảo tử vong");
        singletonObject.danhsachphongbanFormio = singletonObject.danhsachphongban.map(function(object) {
            return {
                label: object.TENKHOA,
                value: object.MAKHOA,
            }
        })
        var jsonForm = getJSONObjectForm([
            {
                "label": "Người nhà",
                "columns": [
                    {
                        "components": [
                            {
                                "label": "Thời gian họp",
                                "key": "THOIGIANHOP",
                                "type": "datetime",
                                format: "dd/MM/yyyy HH:mm",
                                "customClass": "pr-2",
                                enableTime: true,
                            },
                        ],
                        "width": 6,
                    },
                    {
                        "components": [
                            {
                                "label": "Địa điểm",
                                "key": "DIADIEMHOP",
                                "type": "textfield",
                            },
                        ],
                        "width": 6,
                    },
                ],
                "customClass": "ml-0 mr-0",
                "key": "THOIGIANTUVAN",
                "type": "columns",
            },
            {
                "label": "",
                "columns": [
                    {
                        "components": [
                            {
                                "label": "Chủ trì",
                                others: {
                                    "data": {
                                        "values": singletonObject.danhsachnhanvienFormio
                                    },
                                    defaultValue: singletonObject.userId,
                                },
                                "customClass": "pr-2",
                                "key": "CHUTRI",
                                "type": "select",
                                "validate": {
                                    required: true,
                                }
                            },
                        ],
                        "width": 4,
                        "size": "md",
                    },
                    {
                        "components": [
                            {
                                "label": "Thư ký",
                                others: {
                                    "data": {
                                        "values": singletonObject.danhsachnhanvienFormio
                                    },
                                    defaultValue: singletonObject.userId,
                                },
                                "customClass": "pr-2",
                                "key": "THUKY",
                                "type": "select",
                                "validate": {
                                    required: true,
                                }
                            },
                        ],
                        "width": 4,
                        "size": "md",
                    },
                    {
                        "components": [
                            {
                                "label": "Thành viên",
                                "key": "THANHVIEN",
                                "type": "textfield",
                            },
                        ],
                        "width": 4,
                        "size": "md",
                    },
                ],
                "key": "columns_nhin",
                "type": "columns",
                "customClass": "ml-0 mr-0 pb-2",
            },
            {
                "label": "",
                "columns": [
                    {
                        "components": [
                            {
                                "label": "Quốc tịch BN",
                                "key": "QUOCTICH",
                                "type": "textfield",
                                "customClass": "pr-2",
                                others: {
                                    defaultValue: thongtinhsba.thongtinbn.NGOAIKIEU ? "" : "Việt Nam",
                                },
                            },
                        ],
                        "width": 3,
                        "size": "md",
                    },
                    {
                        "components": [
                            {
                                "label": "Căn cước/ Hộ chiếu",
                                "key": "CCCD",
                                "type": "textfield",
                                "customClass": "pr-2",
                                others: {
                                    defaultValue: thongtinhsba.thongtinbn.CMT_BENHNHAN,
                                },
                            },
                        ],
                        "width": 3,
                        "size": "md",
                    },
                    {
                        "components": [
                            {
                                "label": "Ngày cấp",
                                "key": "NGAYCAPCCCD",
                                "type": "datetime",
                                format: "dd/MM/yyyy",
                                "customClass": "pr-2",
                                enableTime: false,
                            },
                        ],
                        "width": 3,
                        "size": "md",
                    },
                    {
                        "components": [
                            {
                                "label": "Nơi cấp",
                                "key": "NOICAPCCCD",
                                "type": "textfield",
                            },
                        ],
                        "width": 3,
                        "size": "md",
                    },
                ],
                "key": "columns_nhin",
                "type": "columns",
                "customClass": "ml-0 mr-0 pb-2",
            },
            {
                "label": "",
                "columns": [
                    {
                        "components": [
                            {
                                "label": "Thời gian tử vong",
                                "key": "THOIGIANTUVONG",
                                "type": "datetime",
                                format: "dd/MM/yyyy HH:mm",
                                "customClass": "pr-2",
                                enableTime: true,
                            },
                        ],
                        "width": 6,
                        "size": "md",
                    },
                    {
                        "components": [
                            {
                                "label": "Tại khoa",
                                "key": "KHOATUVONG",
                                "type": "select",
                                "customClass": "pr-2",
                                others: {
                                    "data": {
                                        "values": singletonObject.danhsachphongbanFormio
                                    },
                                    defaultValue: singletonObject.makhoa,
                                },
                            },
                        ],
                        "width": 6,
                        "size": "md",
                    },
                ],
                "key": "columns_nhin",
                "type": "columns",
                "customClass": "ml-0 mr-0 pb-2",
            },
            {
                "label": "Nguyên nhân chính tử vong",
                "key": "NGUYENNHANTUVONG",
                "type": "textarea",
            },
            {
                "label": "Tóm tắt bệnh sử",
                "key": "TOMTATBENHSU",
                "type": "textarea",
            },
            {
                "label": "Tình trạng lúc vào viện",
                "key": "TINHTRANGVAOVIEN",
                "type": "textarea",
                others: {
                    "tooltip": "(toàn thân, các bộ phận, các xét nghiệm...)",
                },
            },
            {
                label: "",
                key: "wrap_benhphu",
                columns: [
                    {
                        "components": [
                            {
                                "tag": "label",
                                "attrs": [
                                    {
                                        "attr": "",
                                        "value": ""
                                    }
                                ],
                                "content": "Chẩn đoán",
                                others: {
                                    "tooltip": "(của cơ sở, khoa khám bệnh, khoa điều trị, khi tử vong, giải phẫu bệnh nếu có)",
                                },
                                "key": "htmllabel_benhphu",
                                "type": "htmlelement",
                            },
                        ],
                        "width": 12,
                        "size": "md",
                    },
                    {
                        "components": [
                            {
                                "label": "",
                                "key": "ICD_CHANDOAN",
                                "type": "textfield",
                                customClass: "pr-2",
                                others: {
                                    "placeholder": "ICD",
                                }
                            },
                        ],
                        "width": 2,
                        "size": "md",
                    },
                    {
                        "components": [
                            {
                                "label": "",
                                "key": "TENICD_CHANDOAN",
                                "type": "textfield",
                                others: {
                                    "placeholder": "Tên bệnh",
                                }
                            },
                        ],
                        "width": 10,
                        "size": "md",
                    },
                    {
                        "components": [
                            {
                                "label": "",
                                "key": "CHANDOAN",
                                "type": "textarea",
                                "rows": 2,
                                "input": true
                            },
                        ],
                        "width": 12,
                        "size": "md",
                    },
                ],
                "customClass": "ml-0 mr-0",
                "type": "columns",
            },
            {
                "label": "Danh sách diễn biến bệnh",
                others: {
                    "tooltip": "Tóm tắt diễn biến bệnh, quá trình điều trị, chăm sóc, kết quả",
                },
                "tableView": false,
                "rowDrafts": false,
                "key": "CHITIETDIENBIENBENH",
                "type": "editgrid",
                "displayAsTable": false,
                "input": true,
                "components": [
                    {
                        "label": "Columns",
                        "columns": [
                            {
                                "components": [
                                    {
                                        "label": "Diễn biến, quá trình điều trị, kết quả",
                                        "key": "DIENBIENCT",
                                        "type": "textfield",
                                        customClass: "pr-2",
                                    }
                                ],
                                "width": 6,
                                "size": "md"
                            },
                            {
                                "components": [
                                    {
                                        "label": "Khoa điều trị",
                                        "key": "KHOADIEUTRICT",
                                        "type": "select",
                                        "customClass": "pr-2",
                                        others: {
                                            "data": {
                                                "values": singletonObject.danhsachphongbanFormio
                                            },
                                            defaultValue: singletonObject.makhoa,
                                        },
                                    },
                                ],
                                "size": "md",
                                "width": 3
                            },
                            {
                                "components": [
                                    {
                                        "label": "Thời gian họp",
                                        "key": "THOIGIANHOPCT",
                                        "type": "datetime",
                                        format: "dd/MM/yyyy HH:mm",
                                        "customClass": "pr-2",
                                        enableTime: true,
                                    },
                                ],
                                "size": "md",
                                "width": 2
                            },
                        ],
                        "key": "columns",
                        "type": "columns",
                    }
                ],
                others: {
                    "templates": {
                        "header": "<div class=\"row\">\n" +
                            "  {% util.eachComponent(components, function(component) { %}\n" +
                            "    {% if (component.key == 'DIENBIENCT') { %}\n" +
                            "      <div class=\"col-sm-6\">{{ t(component.label) }}</div>\n" +
                            "    {% } %}\n" +
                            "    {% if (displayValue(component) && component.key == 'KHOADIEUTRICT') { %}\n" +
                            "      <div class=\"col-sm-3\">{{ t(component.label) }}</div>\n" +
                            "    {% } %}\n" +
                            "    {% if (component.key == 'THOIGIANHOPCT') { %}\n" +
                            "      <div class=\"col-sm-2\">{{ t(component.label) }}</div>\n" +
                            "    {% } %}\n" +
                            "  {% }) %}\n" +
                            "</div>",
                        "row": "<div class=\"row\">\n" +
                            "  {% util.eachComponent(components, function(component) { %}\n" +
                            "    {% if (component.key == 'DIENBIENCT') { %}\n" +
                            "      <div class=\"col-sm-6\">\n" +
                            "        {{ isVisibleInRow(component) ? getView(component, row[component.key]) : ''}}\n" +
                            "      </div>\n" +
                            "    {% } %}\n" +
                            "    {% if (displayValue(component) && component.key == 'KHOADIEUTRICT') { %}\n" +
                            "      <div class=\"col-sm-3\">\n" +
                            "        {{ isVisibleInRow(component) ? getView(component, row[component.key]) : ''}}\n" +
                            "      </div>\n" +
                            "    {% } %}\n" +
                            "    {% if (component.key == 'THOIGIANHOPCT') { %}\n" +
                            "      <div class=\"col-sm-2\">\n" +
                            "        {{ isVisibleInRow(component) ? getView(component, row[component.key]) : ''}}\n" +
                            "      </div>\n" +
                            "    {% } %}\n" +
                            "  {% }) %}\n" +
                            "  {% if (!instance.options.readOnly && !instance.disabled) { %}\n" +
                            "    <div class=\"col-sm-1\">\n" +
                            "      <div class=\"btn-group pull-right\">\n" +
                            "        <button class=\"btn btn-default btn-primary btn-sm editRow\"><i class=\"fa fa-pencil-square-o\"></i></button>\n" +
                            "        {% if (!instance.hasRemoveButtons || instance.hasRemoveButtons()) { %}\n" +
                            "          <button class=\"btn btn-danger btn-sm removeRow\"><i class=\"fa fa-trash-o\"></i></button>\n" +
                            "        {% } %}\n" +
                            "      </div>\n" +
                            "    </div>\n" +
                            "  {% } %}\n" +
                            "</div>"
                    },
                    "addAnother": "Thêm mới",
                    "saveRow": "Lưu",
                    "removeRow": "Hủy",
                    // "customConditional": "show = data.thuThuatPhauThuat == 1 || data.thuThuatPhauThuat == 2;",
                }
            },
            {
                "label": "Khác",
                "key": "DIENBIENKHAC",
                "type": "textarea",
            },
            {
                "label": "Nguyên nhân khách quan",
                "key": "NGUYENNHANKHACHQUAN",
                "type": "textarea",
                others: {
                    "tooltip": "Nguyên nhân khách quan, chủ quan, bài học kinh nghiệm trong quá trình điều trị, chăm sóc",
                },
            },
            {
                "label": "Kết luận",
                "key": "KETLUAN",
                "type": "textarea",
            },



        ])
        Formio.createForm(document.getElementById('formNhapBienBanKiemThaoTV'),
            jsonForm,{}
        ).then(function(form) {

            formBienBanKiemThaoTV = form;
            var tenBenhphuElement = form.getComponent('TENICD_CHANDOAN');
            var icdBenhphuElement = form.getComponent('ICD_CHANDOAN');
            var textBenhphuElement = form.getComponent('CHANDOAN');
            var chitietDienBienBenhComponent = form.getComponent('CHITIETDIENBIENBENH');

            if (chitietDienBienBenhComponent) {
                var originalAddRow = chitietDienBienBenhComponent.addRow;
                chitietDienBienBenhComponent.addRow = function() {
                    var currentRows = this.dataValue || [];
                    if (currentRows.length >= 3) {
                        notifiToClient("Red", "Chỉ được phép thêm tối đa 3 dòng dữ liệu!");
                        return Promise.resolve();
                    }
                    return originalAddRow.call(this);
                };
            }

            $("#"+getIdElmentFormio(form,'ICD_CHANDOAN')).on('keypress', function(event) {
                var mabenhICD = $(this).val();
                if(event.keyCode == 13 && mabenhICD != "") {
                    mabenhICD = mabenhICD.toUpperCase();
                    getMotabenhly(mabenhICD, function(data) {
                        var splitIcd = data.split("!!!")
                        tenBenhphuElement.setValue(splitIcd[1]);
                        tenBenhphuElement.focus()
                        icdBenhphuElement.setValue(mabenhICD)
                    })
                }
            })
            $("#"+getIdElmentFormio(form,'TENICD_CHANDOAN')).on('keypress', function(event) {
                if(event.keyCode == 13) {
                    var stringIcd = textBenhphuElement.getValue();
                    var mabenhICD = icdBenhphuElement.getValue()
                    if(!stringIcd.includes(mabenhICD)) {
                        textBenhphuElement.setValue( stringIcd + "; (" +mabenhICD.toUpperCase() + ") " + tenBenhphuElement.getValue());
                    }
                    icdBenhphuElement.setValue("")
                    tenBenhphuElement.setValue("")
                    icdBenhphuElement.focus()
                }
            })
            combgridTenICD(getIdElmentFormio(form,'TENICD_CHANDOAN'), function(item) {
                icdBenhphuElement.setValue(item.ICD);
                tenBenhphuElement.setValue(item.MO_TA_BENH_LY);
            });

            showLoaderIntoWrapId("wrapFormBienBanKiemThaoTV")
            $.get('cmu_getlist?url='+convertArray([
                singletonObject.dvtt,
                thongtinhsba.thongtinbn.SOVAOVIEN,
                'CMU_GET_BIENBANKIEMTHAOTV'
            ])).done(function(data) {
                if(data.length > 0) {
                    showOrHideByClass('bienbankiemthaotv-btn-wrap', "add", 'edit');
                    $('#bienbankiemthaotv_luu').attr("data-action", "CAP_NHAT");
                    formBienBanKiemThaoTV.submission =  {
                        data: {
                            ...data[0],
                            ID: data[0].ID,
                            CHITIETDIENBIENBENH: data[0].CHITIETDIENBIENBENH ? JSON.parse(data[0].CHITIETDIENBIENBENH) : [],
                            THOIGIANHOP:  (data[0].THOIGIANHOP? moment(data[0].THOIGIANHOP, ['DD/MM/YYYY HH:mm']): moment()).toISOString(),
                            NGAYCAPCCCD:  (data[0].NGAYCAPCCCD? moment(data[0].NGAYCAPCCCD, ['DD/MM/YYYY']): moment()).toISOString(),
                            THOIGIANTUVONG:  (data[0].THOIGIANTUVONG? moment(data[0].THOIGIANTUVONG, ['DD/MM/YYYY HH:mm']): moment()).toISOString(),
                        }
                    };
                    thongTinBienBanKTTVTruocChinhSua = data[0]
                    $("#bienbankiemthaotv_huykyso").hide();
                    $("#bienbankiemthaotv_kyso").hide();
                    if (data[0].KEYSIGN_THUKY || data[0].KEYSIGN_CHUTRI){
                        $("#bienbankiemthaotv_luu").hide();
                        $("#bienbankiemthaotv_xoa").hide();
                    }else{
                        $("#bienbankiemthaotv_luu").show();
                        $("#bienbankiemthaotv_xoa").show();
                    }
                    checkKysobbkttv();
                } else {
                    $("#bienbankiemthaotv_luu").show();
                    $('#bienbankiemthaotv_luu').attr("data-action", "THEM");
                    formBienBanKiemThaoTV.submission =  {
                        data: {}
                    };
                    showOrHideByClass('bienbankiemthaotv-btn-wrap', "edit", 'add');
                }
            }).fail(function() {
                notifiToClient("Red", MESSAGEAJAX.ERROR);
            }).always(function() {
                hideLoaderIntoWrapId("wrapFormBienBanKiemThaoTV")
            })
        });
    }

    function resetModalFormBBTV(formId) {
        $(".custom-select").val(null).trigger('change');
        $("#" + formId).trigger("reset");
    }

    $("#bienbankiemthaotv_kysosl").on("change", function () {
        var nguoiKy = $(this).val();
        var arrTemp = [];
        var maxCreateDate = null;
        var maxCreateDataObject = null;

        getFilesign769("PHIEU_NOITRU_BIENBANKIEMTHAOTV_THUKY", thongtinhsba.thongtinbn.SOVAOVIEN, -1, singletonObject.dvtt,
            thongtinhsba.thongtinbn.SOVAOVIEN, thongtinhsba.thongtinbn.SOVAOVIEN_DT, -1, function(dataKySo) {
                if (dataKySo.length > 0) {
                    arrTemp.push(dataKySo[0])
                }
            });
        getFilesign769("PHIEU_NOITRU_BIENBANKIEMTHAOTV_CHUTRI", thongtinhsba.thongtinbn.SOVAOVIEN, -1, singletonObject.dvtt,
            thongtinhsba.thongtinbn.SOVAOVIEN, thongtinhsba.thongtinbn.SOVAOVIEN_DT, -1, function(dataKySo) {
                if (dataKySo.length > 0) {
                    arrTemp.push(dataKySo[0])
                }
            });
        if(arrTemp.length > 0) {
            $.each(arrTemp, function (index, dataObject) {
                var createDate = moment(dataObject.CREATE_DATE_STRING, "DD/MM/YYYY HH:mm:ss");
                if (maxCreateDate === null || createDate.isAfter(maxCreateDate)) {
                    maxCreateDate = createDate;
                    maxCreateDataObject = dataObject;
                }
            });
        }

        if (nguoiKy == "THƯ KÝ") {
            getFilesign769("PHIEU_NOITRU_BIENBANKIEMTHAOTV_THUKY", thongtinhsba.thongtinbn.SOVAOVIEN, -1, singletonObject.dvtt,
                thongtinhsba.thongtinbn.SOVAOVIEN, thongtinhsba.thongtinbn.SOVAOVIEN_DT, -1, function (dataKySo) {
                    if (dataKySo.length > 0) {
                        if (maxCreateDataObject.KY_HIEU_PHIEU == "PHIEU_NOITRU_BIENBANKIEMTHAOTV_THUKY") {
                            $("#bienbankiemthaotv_huykyso").show();
                            $("#bienbankiemthaotv_kyso").hide();
                        } else {
                            $("#bienbankiemthaotv_huykyso").hide();
                            $("#bienbankiemthaotv_kyso").hide();
                        }
                    } else {
                        $("#bienbankiemthaotv_huykyso").hide();
                        $("#bienbankiemthaotv_kyso").show();
                    }
                });
        } else if (nguoiKy == "CHỦ TỌA") {
            getFilesign769("PHIEU_NOITRU_BIENBANKIEMTHAOTV_CHUTRI", thongtinhsba.thongtinbn.SOVAOVIEN, -1, singletonObject.dvtt,
                thongtinhsba.thongtinbn.SOVAOVIEN, thongtinhsba.thongtinbn.SOVAOVIEN_DT, -1, function (dataKySo) {
                    if (dataKySo.length > 0) {
                        if (maxCreateDataObject.KY_HIEU_PHIEU == "PHIEU_NOITRU_BIENBANKIEMTHAOTV_CHUTRI") {
                            $("#bienbankiemthaotv_huykyso").show();
                            $("#bienbankiemthaotv_kyso").hide();
                        } else {
                            $("#bienbankiemthaotv_huykyso").hide();
                            $("#bienbankiemthaotv_kyso").hide();
                        }
                    } else {
                        $("#bienbankiemthaotv_huykyso").hide();
                        $("#bienbankiemthaotv_kyso").show();
                    }
                });
        }
    })

    $("#bienbankiemthaotv_kyso").click(function() {
        var nguoiKy = $("#bienbankiemthaotv_kysosl").val();
        var loaiGiay;
        if (nguoiKy == "KYSOTHUKY"){
            loaiGiay = "PHIEU_NOITRU_BIENBANKIEMTHAOTV_THUKY";
        } else if (nguoiKy == "KYSOCHUTRI") {
            loaiGiay = "PHIEU_NOITRU_BIENBANKIEMTHAOTV_CHUTRI"
        }

        var params = {
            THOIGIANVAOVIEN: thongtinhsba.thongtinbn.NGAYGIO_NHAPVIEN ?? thongtinhsba.thongtinbn.NGAYNHAPVIEN,
            GIOI_TINH: thongtinhsba.thongtinbn.GIOI_TINH_NUM ? thongtinhsba.thongtinbn.GIOI_TINH_NUM : thongtinhsba.thongtinbn.GIOI_TINH,
        }
        getUrlBienBanKiemThaoTV(params).then(objReturn => {
            if (objReturn.isError == 0) {
                previewAndSignPdfDefaultModal({
                    url: objReturn.url,
                    idButton: 'bienbankiemthaotv_kyso_action',
                }, function(){
                    $("#bienbankiemthaotv_kyso_action").click(function() {
                        kySoChung({
                            dvtt: singletonObject.dvtt,
                            userId: singletonObject.userId,
                            url: objReturn.url,
                            loaiGiay: loaiGiay,
                            maBenhNhan: thongtinhsba.thongtinbn.MA_BENH_NHAN,
                            soBenhAn: thongtinhsba.thongtinbn.STT_BENHAN,
                            soPhieuDichVu: thongtinhsba.thongtinbn.SOVAOVIEN,
                            soVaoVien: thongtinhsba.thongtinbn.SOVAOVIEN,
                            soVaoVienDT: thongtinhsba.thongtinbn.SOVAOVIEN_DT,
                            keyword: nguoiKy,
                            fileName: "Biên bản kiểm thảo tử vong: " + thongtinhsba.thongtinbn.TEN_BENH_NHAN,
                        }, function(dataKySo) {
                            // luuLogHSBATheoBN({
                            //     SOVAOVIEN: thongtinhsba.thongtinbn.SOVAOVIEN,
                            //     LOAI: LOGHSBALOAI.BIENBANTUVONG.KEY,
                            //     NOIDUNGBANDAU: "",
                            //     NOIDUNGMOI: singletonObject.user+" ký số biên bản kiểm thảo tử vong "+singletonObject.ngayhientai,
                            //     USERID: singletonObject.userId,
                            //     ACTION: LOGHSBAACTION.INSERT.KEY
                            // });
                            $("#modalPreviewAndSignPDF").modal("hide");
                            $("#tthc_xv_bienbankiemthaotv, #hsba_bbkiemthaotv").click();
                        });
                        // });

                    });
                });
            } else {
                notifiToClient("Red", objReturn.message);
            }
        }).catch(error => {
            notifiToClient("Red", error.message || "Lỗi không xác định");
        });
    })
    $("#bienbankiemthaotv_huykyso").click(function() {
        var nguoiKy = $("#bienbankiemthaotv_kysosl").val();
        var loaiGiay;
        if (nguoiKy == "KYSOTHUKY"){
            loaiGiay = "PHIEU_NOITRU_BIENBANKIEMTHAOTV_THUKY"
        } else if (nguoiKy == "KYSOCHUTRI") {
            loaiGiay = "PHIEU_NOITRU_BIENBANKIEMTHAOTV_CHUTRI"
        }

        confirmToClient("Bạn có chắc sẽ hủy ký số phiếu này?", function() {
            huykysoFilesign769(loaiGiay, thongtinhsba.thongtinbn.SOVAOVIEN, singletonObject.userId, singletonObject.dvtt,
                thongtinhsba.thongtinbn.SOVAOVIEN, thongtinhsba.thongtinbn.SOVAOVIEN_DT, -1, function(data) {
                    $("#tthc_xv_bienbankiemthaotv, #hsba_bbkiemthaotv").click();
                })
        }, function () {

        })
    })
    function checkKysobbkttv(){
        var nguoiKy = $("#bienbankiemthaotv_kysosl").val();
        getFilesign769("PHIEU_NOITRU_BIENBANKIEMTHAOTV_THUKY", thongtinhsba.thongtinbn.SOVAOVIEN, -1, singletonObject.dvtt,
            thongtinhsba.thongtinbn.SOVAOVIEN, thongtinhsba.thongtinbn.SOVAOVIEN_DT, -1, function(dataKySo) {
                if (dataKySo.length > 0) {
                    $("#bienbankiemthaotv_kysosl option[value='KYSOTHUKY']").text("Thư ký (Đã ký)");
                    if (nguoiKy == "KYSOTHUKY"){
                        $("#bienbankiemthaotv_huykyso").show();
                        $("#bienbankiemthaotv_kyso").hide();
                    }
                }else{
                    $("#bienbankiemthaotv_kysosl option[value='KYSOTHUKY']").text("Thư ký");
                    if (nguoiKy == "KYSOTHUKY"){
                        $("#bienbankiemthaotv_kyso").show();
                        $("#bienbankiemthaotv_huykyso").hide();
                    }
                }
            });
        getFilesign769("PHIEU_NOITRU_BIENBANKIEMTHAOTV_CHUTRI", thongtinhsba.thongtinbn.SOVAOVIEN, -1, singletonObject.dvtt,
            thongtinhsba.thongtinbn.SOVAOVIEN, thongtinhsba.thongtinbn.SOVAOVIEN_DT, -1, function(dataKySo) {
                if (dataKySo.length > 0) {
                    $("#bienbankiemthaotv_kysosl option[value='KYSOCHUTRI']").text("Chủ tọa (Đã ký)");
                    if (nguoiKy == "KYSOCHUTRI"){
                        $("#bienbankiemthaotv_huykyso").show();
                        $("#bienbankiemthaotv_kyso").hide();
                    }
                } else {
                    $("#bienbankiemthaotv_kysosl option[value='KYSOCHUTRI']").text("Chủ tọa");
                    if (nguoiKy == "KYSOCHUTRI"){
                        $("#bienbankiemthaotv_kyso").show();
                        $("#bienbankiemthaotv_huykyso").hide();
                    }
                }
            });

    }

    function getLabelValueBBTV(key, data) {
        var value = "";
        switch (key) {
            case "DVTT":
                value = singletonObject.dvtt
                break;
            case "SOVAOVIEN":
                value = thongtinhsba.thongtinbn.SOVAOVIEN
                break;
            case "MA_BENH_NHAN":
                value = thongtinhsba.thongtinbn.MA_BENH_NHAN
                break;
            case "MA_NHAN_VIEN":
                value = singletonObject.userId
                break;
            case "TENKHOA":
                value = $("#tthc_xvbb_khoatuvong").find(':selected').text()
                break;
            case "TEN_CHU_TOA":
                value = $("#tthc_xvbb_chutoa").find(':selected').text()
                break;
            case "TEN_THU_KY":
                value = $("#tthc_xvbb_thuky").find(':selected').text()
                break;
            default:
                value = data[key]
                break;
        }
        return keyLuuLogBBTV[key] + ": " +value;
    }

    function luuthongtinBBTV(){
        var data = {
            ID: $("#tthc_tv_idbienbantv").val(),
            NGAY_TU_VONG: $("#tthc_xvbb_ngaygiotuvong").val(),
            MAKHOA: $("#tthc_xvbb_khoatuvong").val(),
            TENKHOA: $("#tthc_xvbb_khoatuvong").find(':selected').text(),
            NGAY_KIEM_DIEM_TU_VONG: $("#tthc_xvbb_kiemdiemtuvong").val(),
            CHU_TOA: $("#tthc_xvbb_chutoa").val(),
            TEN_CHU_TOA: $("#tthc_xvbb_chutoa").find(':selected').text(),
            THU_KY: $("#tthc_xvbb_thuky").val(),
            TEN_THU_KY: $("#tthc_xvbb_thuky").find(':selected').text(),
            THANH_VIEN: $("#tthc_xvbb_thanhvienthamgia").val(),
            TOM_TAT: $("#tthc_xvbb_tomtatquatrinh").val(),
            KET_LUAN: $("#tthc_xvbb_ketluan").val()
        };
        return data;
    }

    function luulogformBBTV(loai){
        thongtinhsba.thongTinBBTVMoi = luuthongtinBBTV();
        thongtinhsba.thongTinCu = thongtinhsba.thongTinBBTVCu;
        thongtinhsba.thongTinMoi = thongtinhsba.thongTinBBTVMoi;
        if (thongtinhsba.thongTinCu !== undefined && thongtinhsba.thongTinMoi !== undefined) {
            luuLogHSBAChinhSua(thongtinhsba.thongTinCu, thongtinhsba.thongTinMoi, loai);
        }
    }

    function luuLogBBTV(type) {
        let formBBTVData = getBBTVData(0);

        if (type == 1) {
            luuLogHSBATheoBN({
                SOVAOVIEN: thongtinhsba.thongtinbn.SOVAOVIEN,
                LOAI: LOGHSBALOAI.BIENBANTUVONG.KEY,
                NOIDUNGBANDAU: '',
                NOIDUNGMOI: getStringBBTV(formBBTVData),
                USERID: singletonObject.userId,
                ACTION: LOGHSBAACTION.INSERT.KEY,
            })
        } else if (type == 2) {
            if (bbtvDuLieuBanDau) {
                let noiDungBanDauEdit = getBBTVData(1);
                if (noiDungBanDauEdit) {
                    let  diff = findDifferencesBetweenObjects(noiDungBanDauEdit, formBBTVData)
                    if (Object.keys(diff).length > 0) {
                        let noiDungThayDoi = {};
                        Object.keys(diff).forEach(key => noiDungThayDoi[key] = diff[key][1]);

                        luuLogHSBATheoBN({
                            SOVAOVIEN: thongtinhsba.thongtinbn.SOVAOVIEN,
                            LOAI: LOGHSBALOAI.BIENBANTUVONG.KEY,
                            NOIDUNGBANDAU: getStringBBTV(noiDungBanDauEdit),
                            NOIDUNGMOI: getStringBBTV(noiDungThayDoi),
                            USERID: singletonObject.userId,
                            ACTION: LOGHSBAACTION.EDIT.KEY,
                        })
                    }
                }
            }
        } else if (type == 3) {
            if (bbtvDuLieuBanDau) {
                let noiDungBanDauDelete = getBBTVData(1);
                if (noiDungBanDauDelete) {
                    luuLogHSBATheoBN({
                        SOVAOVIEN: thongtinhsba.thongtinbn.SOVAOVIEN,
                        LOAI: LOGHSBALOAI.BIENBANTUVONG.KEY,
                        NOIDUNGBANDAU: getStringBBTV(noiDungBanDauDelete),
                        NOIDUNGMOI: '',
                        USERID: singletonObject.userId,
                        ACTION: LOGHSBAACTION.DELETE.KEY,
                    })
                }
            }
        }
    }

    function getBBTVData (type) {
        let rsData = {}
        Object.keys(keyLogBBTV).forEach(key => rsData[key] = '');

        let dsBacSi = singletonObject.danhsachnhanvien;
        let dsChuyenkhoa = singletonObject.danhsachphongban;
        if (type == 0) {
            rsData = {
                NGAY_TU_VONG: $("#tthc_xvbb_ngaygiotuvong").val(),
                MAKHOA: getTenKhoaLog(dsChuyenkhoa, $("#tthc_xvbb_khoatuvong").val()),
                NGAY_KIEM_DIEM_TU_VONG: $("#tthc_xvbb_kiemdiemtuvong").val(),
                CHU_TOA: getTenBacSiLog(dsBacSi, $("#tthc_xvbb_chutoa").val()),
                THU_KY: getTenBacSiLog(dsBacSi, $("#tthc_xvbb_thuky").val()),
                THANH_VIEN: $("#tthc_xvbb_thanhvienthamgia").val(),
                TOM_TAT: $("#tthc_xvbb_tomtatquatrinh").val(),
                KET_LUAN: $("#tthc_xvbb_ketluan").val()
            }
        } else if (type == 1) {
            Object.keys(rsData).forEach(key => rsData[key] = bbtvDuLieuBanDau[key]);
            rsData['NGAY_TU_VONG'] = moment(rsData['NGAY_TU_VONG'], 'DD/MM/YYYY HH:mm:ss').format('DD/MM/YYYY HH:mm');
            rsData['NGAY_KIEM_DIEM_TU_VONG'] = moment(rsData['NGAY_KIEM_DIEM_TU_VONG'], 'DD/MM/YYYY HH:mm:ss').format('DD/MM/YYYY HH:mm');
            rsData['MAKHOA'] = getTenKhoaLog(dsChuyenkhoa, rsData['MAKHOA']);
            rsData['CHU_TOA'] = getTenBacSiLog(dsBacSi, rsData['CHU_TOA']);
            rsData['THU_KY'] = getTenBacSiLog(dsBacSi, rsData['THU_KY']);
        }

        return rsData
    }

    function getStringBBTV(obj) {
        let string = '';
        let objLength = Object.keys(obj).length;
        Object.keys(obj).forEach((key, index) => {
            if (index != objLength - 1)
                string += `${keyLogBBTV[key]}: ${obj[key]}; `
            else
                string += `${keyLogBBTV[key]}: ${obj[key]}.`
        });
        return string;
    }
});









