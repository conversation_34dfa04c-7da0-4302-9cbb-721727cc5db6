create or replace FUNCTION     HIS_MANAGER."CMU_LUONGGIAHD_ITEM_INS" (
    p_dvtt       IN           VARCHAR2,
    p_loai        IN           VARCHAR2,
    p_noidung        IN           VARCHAR2,
    p_khoatao    IN           VARCHAR2,
    p_nguoitao   IN           VARCHAR2
) RETURN VARCHAR2 IS
    v_checkexist   NUMBER := 0;
    v_result       VARCHAR2(10) := '-1';
BEGIN
SELECT
    COUNT(*)
INTO v_checkexist
FROM
    cmu_luonggiahd_item
WHERE
        dvtt = p_dvtt
  AND loai = p_loai AND noidung = p_noidung;

IF v_checkexist > 0 THEN
        RETURN '-2';
END IF;
INSERT INTO cmu_luonggiahd_item (
    dvtt,
    loai,
    noidung,
    khoatao,
    nguoitao,
    ngaytao
) VALUES (
             p_dvtt,
             p_loai,
             p_noidung,
             p_khoatao,
             p_nguoitao,
             sysdate
         ) RETURNING id INTO v_result;

RETURN v_result;
END;