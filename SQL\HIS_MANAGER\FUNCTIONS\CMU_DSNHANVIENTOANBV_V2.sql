create or replace FUNCTION HIS_MANAGER.CMU_DSNHANVIENTOANBV_V2(
    p_dvtt varchar2
) RETURN SYS_REFCURSOR
IS
    cur  SYS_REFCURSOR;
BEGIN
open cur for
select
    *
from(
        select DISTINCT
            --rownum rn,
            cd.MOTA_CHUCDANH ||'. '|| TEN_NHANVIEN TEN_NHANVIEN,
            MA_NHANVIEN,
            pb.TEN_PHONGBAN,
            pb.MA_PHONGBAN,
            SOCMT_NHANVIEN
        from his_fw.dm_phongban pb
                 join his_fw.dm_nhanvien nv on nv.ma_phongban=pb.ma_phongban
                 join his_fw.dm_chucdanh_nhanvien cd on nv.chucdanh_nhanvien=cd.ma_chucdanh
        where pb.ma_donvi=p_dvtt and nv.enabled = 1

    );
--where rn <= 15;

RETURN cur;
END;
