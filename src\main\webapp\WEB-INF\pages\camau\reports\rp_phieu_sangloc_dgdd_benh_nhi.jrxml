<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="rp_phieu_sangloc_dgdd_benh_nhi" language="groovy" pageWidth="595" pageHeight="842" whenNoDataType="AllSectionsNoDetail" columnWidth="515" leftMargin="40" rightMargin="40" topMargin="20" bottomMargin="20" whenResourceMissingType="Empty" uuid="0c77309d-bb14-4cd7-8eb5-4a8b75b128de">
	<property name="ireport.zoom" value="1.0"/>
	<property name="ireport.x" value="0"/>
	<property name="ireport.y" value="0"/>
	<style name="s_center" hAlign="Center"/>
	<style name="style1">
		<box>
			<leftPen lineWidth="1.0" lineColor="#000000"/>
		</box>
	</style>
	<parameter name="tenkhoa" class="java.lang.String"/>
	<parameter name="soyte" class="java.lang.String"/>
	<parameter name="benhvien" class="java.lang.String"/>
	<parameter name="dvtt" class="java.lang.String"/>
	<parameter name="mabenhnhan" class="java.lang.String"/>
	<parameter name="stt_dotdieutri" class="java.lang.String"/>
	<parameter name="sovaovien" class="java.lang.String"/>
	<parameter name="maphieu" class="java.lang.String">
		<defaultValueExpression><![CDATA[]]></defaultValueExpression>
	</parameter>
	<queryString language="plsql">
		<![CDATA[{call HIS_MANAGER.CMU_PHIEU_DGDD_CHUNG_P(
$P{dvtt}, 
$P{maphieu}, 
$P{stt_dotdieutri}, 
$P{sovaovien},
$P{ORACLE_REF_CURSOR})}]]>
	</queryString>
	<field name="DATA_PHIEU" class="java.lang.String"/>
	<field name="TEN_BENH_NHAN" class="java.lang.String"/>
	<field name="TUOI" class="java.lang.String"/>
	<field name="GIOI_TINH" class="java.lang.String"/>
	<field name="TENKHOA_NHAPVIENVAOKHOA" class="java.lang.String"/>
	<field name="STT_BUONG" class="java.lang.String"/>
	<field name="CHAN_DOAN" class="java.lang.String"/>
	<field name="STT_GIUONG" class="java.lang.String"/>
	<field name="ANCHUKY" class="java.lang.String"/>
	<variable name="JSON_DATA" class="java.lang.Object" resetType="None" calculation="System">
		<variableExpression><![CDATA[(new groovy.json.JsonSlurper().parseText($F{DATA_PHIEU}))]]></variableExpression>
	</variable>
	<group name="rp_phieu_sangloc_dgdd_benh_nhi">
		<groupExpression><![CDATA[$P{maphieu}]]></groupExpression>
		<groupHeader>
			<band height="700" splitType="Immediate">
				<staticText>
					<reportElement x="150" y="0" width="260" height="70" uuid="8a27bdd0-1bad-45e7-b96a-b1929eb04695"/>
					<textElement textAlignment="Center">
						<font fontName="Times New Roman" size="16" isBold="true"/>
					</textElement>
					<text><![CDATA[PHIẾU SÀNG LỌC VÀ ĐÁNH GIÁ
DINH DƯỠNG BỆNH NHI]]></text>
				</staticText>
				<textField>
					<reportElement x="0" y="0" width="150" height="70" uuid="1c57d0db-415b-4d56-9308-13895fd2e7c0"/>
					<box leftPadding="0"/>
					<textElement verticalAlignment="Top">
						<font fontName="Times New Roman" size="12" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA["Cơ sở KB, CB " + (
$P{benhvien} == null ? 
"" : 
$P{benhvien}
)]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="410" y="0" width="105" height="70" uuid="18482678-240f-4ba7-81d1-7f8374bdca9a"/>
					<box leftPadding="0"/>
					<textElement verticalAlignment="Top" markup="styled">
						<font fontName="Times New Roman" size="12" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA["MS: DD-05\nSố vào viện " + (
$P{sovaovien} == null ? 
"" : 
$P{sovaovien}
) + "\nMã người bệnh " + (
$P{mabenhnhan} == null ? 
"" : 
$P{mabenhnhan}
)]]></textFieldExpression>
				</textField>
				<frame>
					<reportElement positionType="Float" x="0" y="115" width="515" height="15" uuid="520e354a-118d-4126-8334-15866a715b76"/>
					<textField>
						<reportElement x="0" y="0" width="175" height="15" uuid="58841c42-5b5e-4396-b55e-e4e763e8de73"/>
						<box leftPadding="0"/>
						<textElement verticalAlignment="Middle">
							<font fontName="Times New Roman" size="12" isBold="false"/>
						</textElement>
						<textFieldExpression><![CDATA["Cân nặng: " + (
$V{JSON_DATA}.CANNANG == null ? 
"         " : 
$V{JSON_DATA}.CANNANG
) + " (kg)"]]></textFieldExpression>
					</textField>
					<textField>
						<reportElement x="175" y="0" width="175" height="15" uuid="d262310d-2553-42fc-a98d-0cdc85bcd5ab"/>
						<textElement verticalAlignment="Middle">
							<font fontName="Times New Roman" size="12" isBold="false"/>
						</textElement>
						<textFieldExpression><![CDATA["Chiều dài: " + (
$V{JSON_DATA}.CHIEUCAO == null ? 
"         " : 
$V{JSON_DATA}.CHIEUCAO
) + " (cm)"]]></textFieldExpression>
					</textField>
					<textField>
						<reportElement x="350" y="0" width="165" height="15" uuid="9b79bc04-5ba1-47c6-be8e-0d209f64555c"/>
						<textElement verticalAlignment="Middle">
							<font fontName="Times New Roman" size="12" isBold="false"/>
						</textElement>
						<textFieldExpression><![CDATA["BMI: " + (
$V{JSON_DATA}.BMI == null ? 
"" : 
$V{JSON_DATA}.BMI
)]]></textFieldExpression>
					</textField>
				</frame>
				<frame>
					<reportElement positionType="Float" x="0" y="130" width="515" height="15" uuid="fe700a43-fad3-4362-a66f-09efb44f9f68"/>
					<textField>
						<reportElement x="0" y="0" width="255" height="15" uuid="d913b347-3c6f-4600-8c0f-2f66fcd3eaf8"/>
						<box leftPadding="0"/>
						<textElement verticalAlignment="Middle" markup="styled">
							<font fontName="Times New Roman" size="12" isBold="false"/>
						</textElement>
						<textFieldExpression><![CDATA["Cân nặng chuẩn so với tuổi: " + (
$V{JSON_DATA}.CN_CHUAN_SOVS_TUOI == null ? 
"   " : 
$V{JSON_DATA}.CN_CHUAN_SOVS_TUOI
) + " SD <i>(Theo hướng dẫn của Tổ chức Y thế giới)</i>"]]></textFieldExpression>
					</textField>
					<textField>
						<reportElement x="260" y="0" width="255" height="15" uuid="8f4b3084-f2d1-4ea9-9736-d8190e81c50f"/>
						<textElement verticalAlignment="Middle" markup="styled">
							<font fontName="Times New Roman" size="12" isBold="false"/>
						</textElement>
						<textFieldExpression><![CDATA["Chiều cao chuẩn so với tuổi: " + (
$V{JSON_DATA}.CC_CHUAN_SOVS_TUOI == null ? 
"   " : 
$V{JSON_DATA}.CC_CHUAN_SOVS_TUOI
) + " SD <i>(Theo hướng dẫn của Tổ chức Y thế giới)</i>"]]></textFieldExpression>
					</textField>
				</frame>
				<staticText>
					<reportElement positionType="Float" x="0" y="145" width="515" height="30" uuid="00ebc63a-0ce5-401b-ab1f-8501f1a88013"/>
					<textElement verticalAlignment="Middle">
						<font fontName="Times New Roman" size="12" isBold="true"/>
					</textElement>
					<text><![CDATA[Phần I: Sàng lọc nguy cơ suy dinh dưỡng (SDD) (Điều dưỡng thực hiện)]]></text>
				</staticText>
				<frame>
					<reportElement positionType="Float" x="0" y="175" width="515" height="15" uuid="e05cad3e-84b7-47a1-a500-53b1ff611342"/>
					<staticText>
						<reportElement x="0" y="0" width="375" height="15" uuid="f9f0c0f8-2640-443a-b21e-4b5ee3a41f01"/>
						<textElement verticalAlignment="Middle">
							<font fontName="Times New Roman" size="12" isBold="true"/>
						</textElement>
						<text><![CDATA[Yếu tố nguy cơ]]></text>
					</staticText>
					<staticText>
						<reportElement x="375" y="0" width="140" height="15" uuid="87957318-a31a-4aca-9ac3-b00ebd002f9d"/>
						<box leftPadding="3"/>
						<textElement verticalAlignment="Middle">
							<font fontName="Times New Roman" size="12" isBold="true"/>
						</textElement>
						<text><![CDATA[Điểm]]></text>
					</staticText>
				</frame>
				<frame>
					<reportElement positionType="Float" x="0" y="190" width="515" height="15" uuid="6783c6e6-30e1-4790-9f08-d608e268a08e"/>
					<staticText>
						<reportElement x="0" y="0" width="375" height="15" uuid="742ec0cc-644d-4bd0-9a95-cfecea1fe40a"/>
						<textElement verticalAlignment="Middle">
							<font fontName="Times New Roman" size="12" isBold="false"/>
						</textElement>
						<text><![CDATA[Người bệnh (NB) có bệnh lý nền gây nguy cơ SDD hoặc dự kiến phẫu thuật?]]></text>
					</staticText>
					<frame>
						<reportElement x="375" y="0" width="140" height="15" uuid="c9ace41f-a00c-4e75-a2d9-14ddd951c263"/>
						<staticText>
							<reportElement x="20" y="0" width="40" height="15" uuid="8f31319b-2e8c-4200-bd4c-68cd3f081210"/>
							<textElement verticalAlignment="Middle">
								<font fontName="Times New Roman" size="12"/>
							</textElement>
							<text><![CDATA[Không]]></text>
						</staticText>
						<staticText>
							<reportElement x="75" y="0" width="65" height="15" uuid="364c7c60-e1bb-4f74-8c75-2d58c9be3dca"/>
							<textElement verticalAlignment="Middle">
								<font fontName="Times New Roman" size="12"/>
							</textElement>
							<text><![CDATA[Có (2 điểm)]]></text>
						</staticText>
						<textField>
							<reportElement x="5" y="2" width="11" height="11" uuid="231d66ab-cd0b-4a5e-b03b-bcbd69171e5f"/>
							<box leftPadding="0">
								<topPen lineWidth="0.75"/>
								<leftPen lineWidth="0.75"/>
								<bottomPen lineWidth="0.75"/>
								<rightPen lineWidth="0.75"/>
							</box>
							<textElement textAlignment="Center" verticalAlignment="Middle">
								<font fontName="Times New Roman" size="9" isBold="false"/>
							</textElement>
							<textFieldExpression><![CDATA[(
$V{JSON_DATA}.TINHDIEM1_2.toString().equals( "1" ) ? 
"X" : 
""
)]]></textFieldExpression>
						</textField>
						<textField>
							<reportElement x="60" y="2" width="11" height="11" uuid="f6489094-1b19-4880-9253-1726126b92bb"/>
							<box leftPadding="0">
								<topPen lineWidth="0.75"/>
								<leftPen lineWidth="0.75"/>
								<bottomPen lineWidth="0.75"/>
								<rightPen lineWidth="0.75"/>
							</box>
							<textElement textAlignment="Center" verticalAlignment="Middle">
								<font fontName="Times New Roman" size="9" isBold="false"/>
							</textElement>
							<textFieldExpression><![CDATA[(
$V{JSON_DATA}.TINHDIEM1_2.toString().equals( "2" ) ? 
"X" : 
""
)]]></textFieldExpression>
						</textField>
					</frame>
				</frame>
				<frame>
					<reportElement positionType="Float" x="0" y="205" width="515" height="15" uuid="5562b562-e57e-42eb-81d9-dea61aab0901"/>
					<staticText>
						<reportElement x="0" y="0" width="375" height="15" uuid="6d56688e-**************-4b13adb990e8"/>
						<textElement verticalAlignment="Middle">
							<font fontName="Times New Roman" size="12" isBold="false"/>
						</textElement>
						<text><![CDATA[Khám lâm sàng có mất lớp mỡ dưới da và/hoặc teo cơ…]]></text>
					</staticText>
					<frame>
						<reportElement x="375" y="0" width="140" height="15" uuid="71fae1a2-b055-43f5-8068-eed922fdaaad"/>
						<staticText>
							<reportElement x="20" y="0" width="40" height="15" uuid="a69b3d4a-b3be-4976-8d31-1ffef5489b30"/>
							<textElement verticalAlignment="Middle">
								<font fontName="Times New Roman" size="12"/>
							</textElement>
							<text><![CDATA[Không]]></text>
						</staticText>
						<staticText>
							<reportElement x="75" y="0" width="65" height="15" uuid="12611da2-bde3-43ec-bd12-b5661a227f86"/>
							<textElement verticalAlignment="Middle">
								<font fontName="Times New Roman" size="12"/>
							</textElement>
							<text><![CDATA[Có (1 điểm)]]></text>
						</staticText>
						<textField>
							<reportElement x="5" y="2" width="11" height="11" uuid="d3ecf3c6-0ea1-4b25-acc7-35bb3c5da5f3"/>
							<box leftPadding="0">
								<topPen lineWidth="0.75"/>
								<leftPen lineWidth="0.75"/>
								<bottomPen lineWidth="0.75"/>
								<rightPen lineWidth="0.75"/>
							</box>
							<textElement textAlignment="Center" verticalAlignment="Middle">
								<font fontName="Times New Roman" size="9" isBold="false"/>
							</textElement>
							<textFieldExpression><![CDATA[(
$V{JSON_DATA}.TINHDIEM2_1.toString().equals( "1" ) ? 
"X" : 
""
)]]></textFieldExpression>
						</textField>
						<textField>
							<reportElement x="60" y="2" width="11" height="11" uuid="529f056e-7c53-4d40-9a4e-2f0a517aac67"/>
							<box leftPadding="0">
								<topPen lineWidth="0.75"/>
								<leftPen lineWidth="0.75"/>
								<bottomPen lineWidth="0.75"/>
								<rightPen lineWidth="0.75"/>
							</box>
							<textElement textAlignment="Center" verticalAlignment="Middle">
								<font fontName="Times New Roman" size="9" isBold="false"/>
							</textElement>
							<textFieldExpression><![CDATA[(
$V{JSON_DATA}.TINHDIEM2_1.toString().equals( "2" ) ? 
"X" : 
""
)]]></textFieldExpression>
						</textField>
					</frame>
				</frame>
				<frame>
					<reportElement positionType="Float" x="0" y="220" width="515" height="85" uuid="c187c905-1ef8-4efa-a7e6-52922727c911"/>
					<staticText>
						<reportElement x="0" y="0" width="375" height="85" uuid="c58b112c-2000-4c38-a887-61bd95e832ed"/>
						<textElement verticalAlignment="Top">
							<font fontName="Times New Roman" size="12" isBold="false"/>
						</textElement>
						<text><![CDATA[Khi có ít nhất 01 trong các yếu tố sau:
- Tiêu chảy (> 5 ngày) và/hoặc nôn (> 3 lần/ngày) kéo dài 1-3 ngày
- Ăn hoặc bú giảm kéo dài 1-3 ngày
- Đã can thiệp DD trước đó (như bổ sung DD qua miệng hoặc qua ống
thông)
- Không thể thu nạp đủ dinh dưỡng do đau]]></text>
					</staticText>
					<frame>
						<reportElement x="375" y="0" width="140" height="15" uuid="0bde3b03-e24a-4405-82a7-d1fe527f72c9"/>
						<staticText>
							<reportElement x="20" y="0" width="40" height="15" uuid="8cb8ed5f-f912-4127-95d8-445b0abc893a"/>
							<textElement verticalAlignment="Top">
								<font fontName="Times New Roman" size="12"/>
							</textElement>
							<text><![CDATA[Không]]></text>
						</staticText>
						<staticText>
							<reportElement x="75" y="0" width="65" height="15" uuid="cad40d5b-d583-4c0d-bc6a-d4fc8a14d6fb"/>
							<textElement verticalAlignment="Top">
								<font fontName="Times New Roman" size="12"/>
							</textElement>
							<text><![CDATA[Có (1 điểm)]]></text>
						</staticText>
						<textField>
							<reportElement x="5" y="2" width="11" height="11" uuid="2942f1c2-d1fa-48f2-a8a2-88249c14667b"/>
							<box leftPadding="0">
								<topPen lineWidth="0.75"/>
								<leftPen lineWidth="0.75"/>
								<bottomPen lineWidth="0.75"/>
								<rightPen lineWidth="0.75"/>
							</box>
							<textElement textAlignment="Center" verticalAlignment="Middle">
								<font fontName="Times New Roman" size="9" isBold="false"/>
							</textElement>
							<textFieldExpression><![CDATA[(
$V{JSON_DATA}.TINHDIEM3_1.toString().equals( "1" ) ? 
"X" : 
""
)]]></textFieldExpression>
						</textField>
						<textField>
							<reportElement x="60" y="2" width="11" height="11" uuid="48f890b9-1d09-4cf2-bf40-5e734652e432"/>
							<box leftPadding="0">
								<topPen lineWidth="0.75"/>
								<leftPen lineWidth="0.75"/>
								<bottomPen lineWidth="0.75"/>
								<rightPen lineWidth="0.75"/>
							</box>
							<textElement textAlignment="Center" verticalAlignment="Middle">
								<font fontName="Times New Roman" size="9" isBold="false"/>
							</textElement>
							<textFieldExpression><![CDATA[(
$V{JSON_DATA}.TINHDIEM3_1.toString().equals( "2" ) ? 
"X" : 
""
)]]></textFieldExpression>
						</textField>
					</frame>
				</frame>
				<frame>
					<reportElement positionType="Float" x="0" y="305" width="515" height="30" uuid="104fa8e2-f1c9-4cad-8f6b-9cf40e635e2d"/>
					<staticText>
						<reportElement x="0" y="0" width="375" height="30" uuid="7a4427df-b342-4f93-8b08-a1f26c2825fd"/>
						<textElement verticalAlignment="Middle">
							<font fontName="Times New Roman" size="12" isBold="false"/>
						</textElement>
						<text><![CDATA[Có bị sụt cân (mọi lứa tuổi) và/hoặc không tăng cân/ chiều cao (trẻ em < 1 tuổi) trong vòng 3 tháng gần đây?]]></text>
					</staticText>
					<frame>
						<reportElement x="375" y="0" width="140" height="15" uuid="ee644165-9e1c-41f4-96d4-f67cb898d390"/>
						<staticText>
							<reportElement x="20" y="0" width="40" height="15" uuid="877a1fd8-2aa4-45eb-a52b-aaae47d09b4d"/>
							<textElement verticalAlignment="Middle">
								<font fontName="Times New Roman" size="12"/>
							</textElement>
							<text><![CDATA[Không]]></text>
						</staticText>
						<staticText>
							<reportElement x="75" y="0" width="65" height="15" uuid="a5fad5fd-0d04-4272-961c-cdc8ad76cf5f"/>
							<textElement verticalAlignment="Middle">
								<font fontName="Times New Roman" size="12"/>
							</textElement>
							<text><![CDATA[Có (1 điểm)]]></text>
						</staticText>
						<textField>
							<reportElement x="5" y="2" width="11" height="11" uuid="9b1a92a3-521b-4b2f-9c58-fa7ef92de5ee"/>
							<box leftPadding="0">
								<topPen lineWidth="0.75"/>
								<leftPen lineWidth="0.75"/>
								<bottomPen lineWidth="0.75"/>
								<rightPen lineWidth="0.75"/>
							</box>
							<textElement textAlignment="Center" verticalAlignment="Middle">
								<font fontName="Times New Roman" size="9" isBold="false"/>
							</textElement>
							<textFieldExpression><![CDATA[(
$V{JSON_DATA}.TINHDIEM4_1.toString().equals( "1" ) ? 
"X" : 
""
)]]></textFieldExpression>
						</textField>
						<textField>
							<reportElement x="60" y="2" width="11" height="11" uuid="1893653f-e071-43b3-9c8e-9b4c0a75ffa5"/>
							<box leftPadding="0">
								<topPen lineWidth="0.75"/>
								<leftPen lineWidth="0.75"/>
								<bottomPen lineWidth="0.75"/>
								<rightPen lineWidth="0.75"/>
							</box>
							<textElement textAlignment="Center" verticalAlignment="Middle">
								<font fontName="Times New Roman" size="9" isBold="false"/>
							</textElement>
							<textFieldExpression><![CDATA[(
$V{JSON_DATA}.TINHDIEM4_1.toString().equals( "2" ) ? 
"X" : 
""
)]]></textFieldExpression>
						</textField>
					</frame>
				</frame>
				<textField>
					<reportElement x="0" y="335" width="515" height="15" uuid="5e0272cd-c647-401c-865f-b95d0b3cf432"/>
					<box leftPadding="0"/>
					<textElement verticalAlignment="Middle">
						<font fontName="Times New Roman" size="12" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA["Tổng điểm: " + (
$V{JSON_DATA}.TONG_DIEM == null ? 
"         " : 
$V{JSON_DATA}.TONG_DIEM
)]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement positionType="Float" x="0" y="350" width="515" height="15" uuid="a2de473c-64c4-45a8-8ef8-1f0979cc0aa1"/>
					<textElement verticalAlignment="Top">
						<font fontName="Times New Roman" size="12" isBold="true"/>
					</textElement>
					<text><![CDATA[Kết quả:]]></text>
				</staticText>
				<staticText>
					<reportElement positionType="Float" x="0" y="365" width="515" height="60" uuid="5b7edf33-aaa9-4ebd-97a0-076318abeee7"/>
					<textElement verticalAlignment="Top">
						<font fontName="Times New Roman" size="12" isBold="false"/>
					</textElement>
					<text><![CDATA[- Nguy cơ thấp: 0 điểm (Kết thúc đánh giá và đánh giá lại sau 7 ngày).
- Nguy cơ trung bình: từ 1 - 3 điểm (Khuyến cáo can thiệp DD, cân lại người bệnh 2 lần/tuần và đánh giá
lại nguy cơ mỗi tuần).
- Nguy cơ cao: từ 4-5 điểm (Mời hội chẩn DD).]]></text>
				</staticText>
				<frame>
					<reportElement positionType="Float" x="0" y="425" width="515" height="100" uuid="a7823661-7e9a-4a86-94c2-db08f948d33a"/>
					<box>
						<topPen lineWidth="0.0"/>
						<leftPen lineWidth="0.0"/>
						<bottomPen lineWidth="0.0"/>
						<rightPen lineWidth="0.0"/>
					</box>
					<frame>
						<reportElement x="0" y="0" width="240" height="100" uuid="9b48a086-24c7-4965-b3b5-00a069007362"/>
						<box>
							<rightPen lineWidth="0.0"/>
						</box>
					</frame>
					<frame>
						<reportElement x="240" y="0" width="275" height="100" uuid="973c5cde-102b-4b72-9852-b0b52b107dcc"/>
						<textField isBlankWhenNull="true">
							<reportElement x="0" y="85" width="275" height="15" uuid="03f4f0f4-095a-474c-8a55-cfceff7d3a68">
								<printWhenExpression><![CDATA[$F{ANCHUKY}.equals("0")]]></printWhenExpression>
							</reportElement>
							<textElement textAlignment="Center" verticalAlignment="Middle">
								<font fontName="Times New Roman" size="12"/>
							</textElement>
							<textFieldExpression><![CDATA[(
$V{JSON_DATA}.DIEU_DUONG.tennhanvien == null ?
"" :
$V{JSON_DATA}.DIEU_DUONG.tennhanvien
)]]></textFieldExpression>
						</textField>
						<staticText>
							<reportElement x="0" y="15" width="275" height="15" uuid="a213712e-5c03-4f84-82e2-dcb6ba51b08c"/>
							<textElement textAlignment="Center" verticalAlignment="Middle" markup="html">
								<font fontName="Times New Roman" size="12" isBold="true" isItalic="false"/>
							</textElement>
							<text><![CDATA[Điều dưỡng]]></text>
						</staticText>
						<textField>
							<reportElement x="0" y="0" width="275" height="15" uuid="b5d3e7d5-96ac-4756-98a9-512c20fede0d"/>
							<box leftPadding="0"/>
							<textElement textAlignment="Center" verticalAlignment="Middle">
								<font fontName="Times New Roman" size="12" isBold="false" isItalic="true"/>
							</textElement>
							<textFieldExpression><![CDATA[$V{JSON_DATA}.NGAY_KY_DD == null ||
$V{JSON_DATA}.NGAY_KY_DD.isEmpty() ?
"Ngày      tháng     năm 20" :
(
    java.time.OffsetDateTime.parse(
        $V{JSON_DATA}.NGAY_KY_DD
    ).format(
        java.time.format.DateTimeFormatter.ofPattern("'Ngày' dd 'tháng' MM 'năm' yyyy")
    )
)]]></textFieldExpression>
						</textField>
						<staticText>
							<reportElement x="0" y="30" width="275" height="15" uuid="a1a58975-ee0c-4537-8ed2-17cda5e715c3"/>
							<textElement textAlignment="Center" verticalAlignment="Middle" markup="html">
								<font fontName="Times New Roman" size="12" isBold="false" isItalic="true"/>
							</textElement>
							<text><![CDATA[(Ký, ghi rõ họ tên)]]></text>
						</staticText>
						<staticText>
							<reportElement x="95" y="45" width="80" height="9" forecolor="#FFFFFF" uuid="36989530-4c3f-435e-ba3c-ebc34b6f5080"/>
							<textElement>
								<font fontName="Times New Roman" size="6" isBold="false"/>
							</textElement>
							<text><![CDATA[ĐIỀU DƯỠNG ĐÁNH GIÁ]]></text>
						</staticText>
					</frame>
				</frame>
				<textField>
					<reportElement x="0" y="525" width="515" height="15" uuid="b70624e6-a658-4ffa-8f6e-a8dcfbafd785"/>
					<box leftPadding="0"/>
					<textElement verticalAlignment="Middle" markup="styled">
						<font fontName="Times New Roman" size="12" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA["<b>Phần II: Đánh giá tăng trưởng</b> (Phần dành cho Bác sỹ)"]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="0" y="540" width="515" height="15" uuid="24d5380b-ffc1-4c23-ab18-538e84c6db4b"/>
					<box leftPadding="0"/>
					<textElement verticalAlignment="Middle" markup="styled">
						<font fontName="Times New Roman" size="12" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA["<b>Phần III: Kế hoạch chăm sóc DD</b> (Phần dành cho Bác sỹ)"]]></textFieldExpression>
				</textField>
				<frame>
					<reportElement positionType="Float" x="0" y="555" width="515" height="45" uuid="42f1ba6b-83a2-4675-8c01-64494ee6e9bf"/>
					<box>
						<topPen lineWidth="0.0"/>
						<leftPen lineWidth="0.0"/>
						<bottomPen lineWidth="0.0"/>
						<rightPen lineWidth="0.0"/>
					</box>
					<frame>
						<reportElement x="0" y="0" width="515" height="15" uuid="9c26316c-54f5-4f75-8d6d-b1a1c947dd4a"/>
						<box>
							<topPen lineWidth="0.0"/>
						</box>
						<frame>
							<reportElement x="0" y="0" width="260" height="15" uuid="e69bd088-906c-4d2e-8063-d5951e2e08a4"/>
							<box>
								<leftPen lineWidth="0.0"/>
								<rightPen lineWidth="0.0"/>
							</box>
							<staticText>
								<reportElement x="15" y="0" width="245" height="15" uuid="ef4becf4-d4f4-4726-96fe-f76c106d57d9"/>
								<textElement verticalAlignment="Middle">
									<font fontName="Times New Roman" size="12"/>
								</textElement>
								<text><![CDATA[Bổ sung DD qua miệng]]></text>
							</staticText>
							<textField>
								<reportElement x="0" y="2" width="11" height="11" uuid="7f50389e-8cad-4fd4-ba5b-b799cd1da7f1"/>
								<box leftPadding="0">
									<topPen lineWidth="0.75"/>
									<leftPen lineWidth="0.75"/>
									<bottomPen lineWidth="0.75"/>
									<rightPen lineWidth="0.75"/>
								</box>
								<textElement textAlignment="Center" verticalAlignment="Middle">
									<font fontName="Times New Roman" size="9" isBold="false"/>
								</textElement>
								<textFieldExpression><![CDATA[(
$V{JSON_DATA}.KEHOACH_CS_DD.toString().equals( "1" ) ? 
"X" : 
""
)]]></textFieldExpression>
							</textField>
						</frame>
						<frame>
							<reportElement x="260" y="0" width="255" height="15" uuid="14c8a8f2-2554-4730-8e7a-25ae91342e89"/>
							<box>
								<leftPen lineWidth="0.0"/>
								<rightPen lineWidth="0.0"/>
							</box>
							<staticText>
								<reportElement x="15" y="0" width="240" height="15" uuid="3a837286-41af-4b90-b0ef-c2d00a849e18"/>
								<textElement verticalAlignment="Middle">
									<font fontName="Times New Roman" size="12"/>
								</textElement>
								<text><![CDATA[Chế độ DD qua ống thông]]></text>
							</staticText>
							<textField>
								<reportElement x="0" y="2" width="11" height="11" uuid="77d8839f-857a-466b-ba8e-a74f5854f005"/>
								<box leftPadding="0">
									<topPen lineWidth="0.75"/>
									<leftPen lineWidth="0.75"/>
									<bottomPen lineWidth="0.75"/>
									<rightPen lineWidth="0.75"/>
								</box>
								<textElement textAlignment="Center" verticalAlignment="Middle">
									<font fontName="Times New Roman" size="9" isBold="false"/>
								</textElement>
								<textFieldExpression><![CDATA[(
$V{JSON_DATA}.KEHOACH_CS_DD.toString().equals( "2" ) ? 
"X" : 
""
)]]></textFieldExpression>
							</textField>
						</frame>
					</frame>
					<frame>
						<reportElement x="0" y="15" width="515" height="15" uuid="77939b9e-b03f-499c-852e-42645d5cffc3"/>
						<box>
							<topPen lineWidth="0.0"/>
						</box>
						<frame>
							<reportElement x="0" y="0" width="260" height="15" uuid="7d8d018e-ad5c-492c-b1bc-a19c31d3ddb1"/>
							<box>
								<leftPen lineWidth="0.0"/>
								<rightPen lineWidth="0.0"/>
							</box>
							<staticText>
								<reportElement x="15" y="0" width="245" height="15" uuid="bd26acbe-e1f8-4ac8-9598-67e1e531c986"/>
								<textElement verticalAlignment="Middle">
									<font fontName="Times New Roman" size="12"/>
								</textElement>
								<text><![CDATA[Chế độ DD qua tĩnh mạch toàn phần]]></text>
							</staticText>
							<textField>
								<reportElement x="0" y="2" width="11" height="11" uuid="66c243d2-7ee1-43dc-96bd-0f89fcc1eb44"/>
								<box leftPadding="0">
									<topPen lineWidth="0.75"/>
									<leftPen lineWidth="0.75"/>
									<bottomPen lineWidth="0.75"/>
									<rightPen lineWidth="0.75"/>
								</box>
								<textElement textAlignment="Center" verticalAlignment="Middle">
									<font fontName="Times New Roman" size="9" isBold="false"/>
								</textElement>
								<textFieldExpression><![CDATA[(
$V{JSON_DATA}.KEHOACH_CS_DD.toString().equals( "3" ) ? 
"X" : 
""
)]]></textFieldExpression>
							</textField>
						</frame>
						<frame>
							<reportElement x="260" y="0" width="255" height="15" uuid="3706c9b9-ead5-4809-8263-eb98ddd9d68b"/>
							<box>
								<leftPen lineWidth="0.0"/>
								<rightPen lineWidth="0.0"/>
							</box>
							<staticText>
								<reportElement x="15" y="0" width="240" height="15" uuid="236c54d0-df74-4636-9e09-35c8c8266049"/>
								<textElement verticalAlignment="Middle">
									<font fontName="Times New Roman" size="12"/>
								</textElement>
								<text><![CDATA[Chế độ DD qua tĩnh mạch bổ sung]]></text>
							</staticText>
							<textField>
								<reportElement x="0" y="2" width="11" height="11" uuid="ae05e872-26c3-4a9a-ae52-ffdf6929a9cc"/>
								<box leftPadding="0">
									<topPen lineWidth="0.75"/>
									<leftPen lineWidth="0.75"/>
									<bottomPen lineWidth="0.75"/>
									<rightPen lineWidth="0.75"/>
								</box>
								<textElement textAlignment="Center" verticalAlignment="Middle">
									<font fontName="Times New Roman" size="9" isBold="false"/>
								</textElement>
								<textFieldExpression><![CDATA[(
$V{JSON_DATA}.KEHOACH_CS_DD.toString().equals( "4" ) ? 
"X" : 
""
)]]></textFieldExpression>
							</textField>
						</frame>
					</frame>
					<frame>
						<reportElement x="0" y="30" width="515" height="15" uuid="74df69f9-1679-40dc-8f73-b9f0d4120c6f"/>
						<box>
							<topPen lineWidth="0.0"/>
						</box>
						<frame>
							<reportElement x="0" y="0" width="260" height="15" uuid="4e5ec7a6-c81f-478a-8615-30f18a912e05"/>
							<box>
								<leftPen lineWidth="0.0"/>
								<rightPen lineWidth="0.0"/>
							</box>
							<staticText>
								<reportElement x="15" y="0" width="245" height="15" uuid="9a1055be-59ff-4e79-b281-726a5cc8fdb7"/>
								<textElement verticalAlignment="Middle">
									<font fontName="Times New Roman" size="12"/>
								</textElement>
								<text><![CDATA[Hội chẩn DD.]]></text>
							</staticText>
							<textField>
								<reportElement x="0" y="2" width="11" height="11" uuid="a2696a71-9bad-4fbd-9a85-24b2282932b3"/>
								<box leftPadding="0">
									<topPen lineWidth="0.75"/>
									<leftPen lineWidth="0.75"/>
									<bottomPen lineWidth="0.75"/>
									<rightPen lineWidth="0.75"/>
								</box>
								<textElement textAlignment="Center" verticalAlignment="Middle">
									<font fontName="Times New Roman" size="9" isBold="false"/>
								</textElement>
								<textFieldExpression><![CDATA[(
$V{JSON_DATA}.KEHOACH_CS_DD.toString().equals( "5" ) ? 
"X" : 
""
)]]></textFieldExpression>
							</textField>
						</frame>
					</frame>
				</frame>
				<frame>
					<reportElement positionType="Float" x="0" y="600" width="515" height="100" uuid="a5b0054f-3c28-4298-9074-bae8901f0879"/>
					<box>
						<topPen lineWidth="0.0"/>
						<leftPen lineWidth="0.0"/>
						<bottomPen lineWidth="0.0"/>
						<rightPen lineWidth="0.0"/>
					</box>
					<frame>
						<reportElement x="0" y="0" width="240" height="100" uuid="1bd7a7ed-92be-4f08-8eb2-a51731214029"/>
						<box>
							<rightPen lineWidth="0.0"/>
						</box>
					</frame>
					<frame>
						<reportElement x="240" y="0" width="275" height="100" uuid="663d2d62-7a90-442a-82cc-1d521a4a2a51"/>
						<textField isBlankWhenNull="true">
							<reportElement x="0" y="85" width="275" height="15" uuid="f1b17fcc-3a1a-4dd8-a573-ba188d6b2c64">
								<printWhenExpression><![CDATA[$F{ANCHUKY}.equals("0")]]></printWhenExpression>
							</reportElement>
							<textElement textAlignment="Center" verticalAlignment="Middle">
								<font fontName="Times New Roman" size="12"/>
							</textElement>
							<textFieldExpression><![CDATA[(
$V{JSON_DATA}.BAC_SI_DANH_GIA.tennhanvien == null ?
"" :
$V{JSON_DATA}.BAC_SI_DANH_GIA.tennhanvien
)]]></textFieldExpression>
						</textField>
						<staticText>
							<reportElement x="0" y="15" width="275" height="15" uuid="f694e0a1-96d4-44e1-8566-d08e1c2847b0"/>
							<textElement textAlignment="Center" verticalAlignment="Middle" markup="html">
								<font fontName="Times New Roman" size="12" isBold="true" isItalic="false"/>
							</textElement>
							<text><![CDATA[Bác sỹ]]></text>
						</staticText>
						<textField>
							<reportElement x="0" y="0" width="275" height="15" uuid="c488f3a4-5dfb-440d-9b7a-365e3e33e166"/>
							<box leftPadding="0"/>
							<textElement textAlignment="Center" verticalAlignment="Middle">
								<font fontName="Times New Roman" size="12" isBold="false" isItalic="true"/>
							</textElement>
							<textFieldExpression><![CDATA[$V{JSON_DATA}.NGAY_KY_BS == null ||
$V{JSON_DATA}.NGAY_KY_BS.isEmpty() ?
"Ngày      tháng     năm 20" :
(
    java.time.OffsetDateTime.parse(
        $V{JSON_DATA}.NGAY_KY_BS
    ).format(
        java.time.format.DateTimeFormatter.ofPattern("'Ngày' dd 'tháng' MM 'năm' yyyy")
    )
)]]></textFieldExpression>
						</textField>
						<staticText>
							<reportElement x="0" y="30" width="275" height="15" uuid="18bb6729-dd46-4488-8b5c-eb4fd3fbe1af"/>
							<textElement textAlignment="Center" verticalAlignment="Middle" markup="html">
								<font fontName="Times New Roman" size="12" isBold="false" isItalic="true"/>
							</textElement>
							<text><![CDATA[(Ký, ghi rõ họ tên)]]></text>
						</staticText>
						<staticText>
							<reportElement x="95" y="45" width="60" height="9" forecolor="#FFFFFF" uuid="11f0bd50-3b55-4021-bdcd-489da6aeca50"/>
							<textElement>
								<font fontName="Times New Roman" size="6" isBold="false"/>
							</textElement>
							<text><![CDATA[BÁC SĨ ĐÁNH GIÁ]]></text>
						</staticText>
					</frame>
				</frame>
				<frame>
					<reportElement x="0" y="70" width="515" height="45" uuid="ec4d2937-9059-493f-9a15-c04c4392d0f7"/>
					<textField>
						<reportElement x="0" y="0" width="355" height="15" uuid="8db7f8f2-b783-4e9a-b7b3-d8a6ce90b771"/>
						<box leftPadding="0"/>
						<textElement verticalAlignment="Middle">
							<font fontName="Times New Roman" size="12" isBold="false"/>
						</textElement>
						<textFieldExpression><![CDATA["Họ và tên người bệnh: " + (
$F{TEN_BENH_NHAN} == null ? 
"" : 
$F{TEN_BENH_NHAN}
)]]></textFieldExpression>
					</textField>
					<textField>
						<reportElement x="0" y="15" width="355" height="15" uuid="2e7eb8e8-64f7-4ece-b6a0-c600d8c1afad"/>
						<box leftPadding="0"/>
						<textElement verticalAlignment="Middle">
							<font fontName="Times New Roman" size="12" isBold="false"/>
						</textElement>
						<textFieldExpression><![CDATA["Khoa: " + (
$F{TENKHOA_NHAPVIENVAOKHOA} == null ? 
"" : 
$F{TENKHOA_NHAPVIENVAOKHOA}
)]]></textFieldExpression>
					</textField>
					<textField>
						<reportElement positionType="Float" x="0" y="30" width="515" height="15" uuid="d28f6ef8-bb64-490a-af5a-299cd5cea389"/>
						<box leftPadding="0"/>
						<textElement verticalAlignment="Middle">
							<font fontName="Times New Roman" size="12" isBold="false"/>
						</textElement>
						<textFieldExpression><![CDATA["Chẩn đoán: " + (
$V{JSON_DATA}.ICD == null ? 
"" : 
$V{JSON_DATA}.ICD
) + " - " + (
$V{JSON_DATA}.CHAN_DOAN == null ? 
"" : 
$V{JSON_DATA}.CHAN_DOAN
)]]></textFieldExpression>
					</textField>
					<textField>
						<reportElement x="355" y="0" width="80" height="15" uuid="a707d23a-7044-48b8-a996-4945d9166034"/>
						<textElement verticalAlignment="Middle">
							<font fontName="Times New Roman" size="12" isBold="false"/>
						</textElement>
						<textFieldExpression><![CDATA["Tuổi: " + (
$F{TUOI} == null ? 
"" : 
$F{TUOI}
)]]></textFieldExpression>
					</textField>
					<textField isStretchWithOverflow="true">
						<reportElement x="355" y="15" width="160" height="15" uuid="85556bf1-da5a-4577-824f-a07fbc8ed7bf"/>
						<textElement verticalAlignment="Middle">
							<font fontName="Times New Roman" size="12" isBold="false"/>
						</textElement>
						<textFieldExpression><![CDATA["Phòng: " + (
$F{STT_BUONG} == null ? 
"   " : 
$F{STT_BUONG}
) + " Giường: " + (
$F{STT_GIUONG} == null ? 
"   " : 
$F{STT_GIUONG}
)]]></textFieldExpression>
					</textField>
					<textField>
						<reportElement x="450" y="0" width="30" height="15" uuid="3b6f540f-2a31-40ec-8727-d40d66fb562f"/>
						<box leftPadding="0"/>
						<textElement verticalAlignment="Middle">
							<font fontName="Times New Roman" size="11" isBold="false"/>
						</textElement>
						<textFieldExpression><![CDATA["Nam"]]></textFieldExpression>
					</textField>
					<textField>
						<reportElement x="437" y="2" width="11" height="11" uuid="9416f042-21bf-4416-aa50-9c04fcfbbd33"/>
						<box leftPadding="0">
							<topPen lineWidth="0.75"/>
							<leftPen lineWidth="0.75"/>
							<bottomPen lineWidth="0.75"/>
							<rightPen lineWidth="0.75"/>
						</box>
						<textElement textAlignment="Center" verticalAlignment="Middle">
							<font fontName="Times New Roman" size="9" isBold="false"/>
						</textElement>
						<textFieldExpression><![CDATA[(
$F{GIOI_TINH}.equals( "1" ) ? 
"X" : 
""
)]]></textFieldExpression>
					</textField>
					<textField>
						<reportElement x="482" y="2" width="11" height="11" uuid="e52f5d05-c925-4431-966b-0568748d4bb3"/>
						<box leftPadding="0">
							<topPen lineWidth="0.75"/>
							<leftPen lineWidth="0.75"/>
							<bottomPen lineWidth="0.75"/>
							<rightPen lineWidth="0.75"/>
						</box>
						<textElement textAlignment="Center" verticalAlignment="Middle">
							<font fontName="Times New Roman" size="9" isBold="false"/>
						</textElement>
						<textFieldExpression><![CDATA[(
$F{GIOI_TINH}.equals( "0" ) ? 
"X" : 
""
)]]></textFieldExpression>
					</textField>
					<textField>
						<reportElement x="495" y="0" width="20" height="15" uuid="f24c3764-71ea-4f6c-8a37-3abb63ed6c30"/>
						<box leftPadding="0"/>
						<textElement verticalAlignment="Middle">
							<font fontName="Times New Roman" size="11" isBold="false"/>
						</textElement>
						<textFieldExpression><![CDATA["Nữ"]]></textFieldExpression>
					</textField>
				</frame>
			</band>
		</groupHeader>
	</group>
</jasperReport>
