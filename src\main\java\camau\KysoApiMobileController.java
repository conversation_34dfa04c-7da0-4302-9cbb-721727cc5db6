package camau;

import API_Lienthong.Controller.LienThongBaseApiController;
import VSC.jdbc.JdbcTemplate;
import camau.model.KiemTraSmartCAModel;
import camau.vienphi.PDFTextLocator;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.gson.Gson;
import com.google.gson.JsonObject;
import com.google.gson.JsonParser;
import com.vnpt.vnptkyso.signer.DocumentType;
import dangnhap.UserDAO;
import hosobenhan_dientu.RISPACSDAO;
import l2.L2Utils;
import logAction.LogActionDAO;
import org.apache.commons.io.IOUtils;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.json.simple.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.bind.annotation.*;
import smartca.model.Account;
import smartca.model.Certificate;
import smartca.model.SmartCADetail;
import smartca.model.response.SmartCAQD769GetCertificateResponseDTO;
import smartca.service.SmartCADAO;
import smartca.service.SmartCAService;
import tienich.jdbc.HisCallTemplate;
import tienich.tienich;

import javax.annotation.Resource;
import javax.servlet.RequestDispatcher;
import javax.servlet.ServletException;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpServletResponseWrapper;
import javax.servlet.http.HttpSession;
import javax.sql.DataSource;
import java.io.*;
import java.net.InetAddress;
import java.net.MalformedURLException;
import java.net.URL;
import java.net.URLDecoder;
import java.nio.charset.StandardCharsets;
import java.text.SimpleDateFormat;
import java.util.*;
import tienich.HttpClientAPIComponent;

@RequestMapping("camau/api/v1/kyso")
@RestController
public class KysoApiMobileController  extends LienThongBaseApiController {
    @Autowired
    KysoApiMobileService kySoApiMobileService;

    @Autowired
    SmartCADAO smartCADAO;

    @Autowired
    LogActionDAO logActionDAO;

    @Autowired
    UserDAO userDAO;

    @Resource(name = "dataSourceMNG")
    DataSource dataSourceMNG;

    @Autowired
    CMUSmartCAService cmuSmartCAService;

    @Autowired
    HttpClientAPIComponent httpClientAPIComponent;

    @Autowired
    RISPACSDAO rispacsdao;

    @Autowired
    CMUApiService cmuApiService;

    @Autowired
    dmthamsodonvi.thamsodonviDAO thamsodonviDAO;

    @Autowired
    FCMService FCMServ;

    public @ResponseBody
    @PostMapping(value = "/cmu-smartca-signed-hash-qd-769-mobile")
    Map smartcaSaveFileSignedQD769V1(@RequestBody JSONObject jsonObject,
                                     HttpSession session,
                                     HttpServletResponse response,
                                     HttpServletRequest request) {
        HttpStatus authStatus = verifyToken(request);
        if(authStatus == HttpStatus.UNAUTHORIZED){
            response.setStatus(401);
            return getResponseUnauthorized();
        }
        Map userInfo = getUserIdFromToken(request);
        String dvtt = userInfo.get("dvtt").toString();
        String userId = userInfo.get("userId").toString();
        String sovaovien = "";
        String tennhanvien = userDAO.getUserDangNhap(Integer.parseInt(userId));
        String kyHieuLog = "";
        Map<String, Object> result = new HashMap<String, Object>();
        int idUniqDangKy = 0;
        try {
            List<KiemTraSmartCAModel> kiemTraSmartCA = kiemTraSmarCA769(dvtt, jsonObject.get("soVaoVien").toString(), jsonObject.get("soVaoVienDT").toString(), jsonObject.get("kyHieuPhieu").toString(), jsonObject.get("soPhieuDichVu").toString(), userId, jsonObject.get("maDichVu").toString());
            KiemTraSmartCAModel kiemTraSmartCAObj = kiemTraSmartCA.get(0);
            if (kiemTraSmartCAObj.getCauhinh() == 0) {
                result.put("SUCCESS", "-13");
                result.put("KEYMINIO", null);
                result.put("MESSAGE", "Chưa được cấu hình ký số smartca, vui lòng cấu hình tài khoản.");
                return result;
            }
            if (kiemTraSmartCAObj.getDaky() > 0) {
                result.put("SUCCESS", "-14");
                result.put("KEYMINIO", null);
                result.put("MESSAGE", "Phiếu đã được ký số, không thể ký số lại.");
                return result;
            }
            if (kiemTraSmartCAObj.getDangky() > 0) {
                result.put("SUCCESS", "-15");
                result.put("KEYMINIO", null);
                result.put("MESSAGE", "Phiếu đã được ký số, không thể ký số lại.");
                return result;
            }
            if (kiemTraSmartCAObj.getVersion().equals("2")) {
                Integer kiemTraDangNhapV2 = smartcaKiemTraDangNhap(dvtt, userId, kiemTraSmartCAObj.getUsername(), kiemTraSmartCAObj.getPassword());
                if (kiemTraDangNhapV2 < 0) {
                    if (kiemTraDangNhapV2 == -1) {
                        result.put("SUCCESS", "-15");
                        result.put("KEYMINIO", null);
                        result.put("MESSAGE", "Không tìm thấy tài khoản SmartCA");
                        return result;
                    } else {
                        result.put("SUCCESS", "-16");
                        result.put("KEYMINIO", null);
                        result.put("MESSAGE", "Đăng nhập thất bại.");
                        return result;
                    }
                }
            }

            String base64Pdf = "";
            if (jsonObject.getOrDefault("isPath", "0").toString().contains("1")) {
                base64Pdf = getPdfFromApiAsBase64(jsonObject.get("url").toString(), request);
            } else {
                base64Pdf = jsonObject.get("url").toString();
            }

            try {
                Map mapGetPosition = new HashMap();
                UUID refTranId = UUID.randomUUID();
                mapGetPosition.put("upfile", "data:application/pdf;base64," + base64Pdf);
                mapGetPosition.put("keyword", jsonObject.get("keyword").toString());
                mapGetPosition.put("tenfile", refTranId.toString());
                mapGetPosition.put("mabenhnhan", jsonObject.get("maBenhNhan").toString());
                String position = cmu_getposition(mapGetPosition, request);
                if (position.contains("ERROR")) {
                    result.put("SUCCESS", "-18");
                    result.put("KEYMINIO", null);
                    result.put("MESSAGE", "Không tìm thấy vị trí ký số trên file.");
                    return result;
                }

                Object[] positionArray = position.split(";");
                List<SignObj.SignTemplate> signTemplateList = new ArrayList<>();
                for (int i = 0; i < positionArray.length; i++) {
                    String[] positionDetail = positionArray[i].toString().split(",");
                    if (positionDetail.length > 2) {
                        SignObj.SignTemplate signTemplate = new SignObj.SignTemplate();
                        int x = (jsonObject.containsKey("x") ? Integer.parseInt(jsonObject.get("x").toString()) : 120) + Integer.parseInt(positionDetail[0]);
                        int y2 = (jsonObject.containsKey("y2") ? Integer.parseInt(jsonObject.get("y2").toString()) : -50) + Integer.parseInt(positionDetail[1]);
                        int x1 = (jsonObject.containsKey("x1") ? Integer.parseInt(jsonObject.get("x1").toString()) : -20) + Integer.parseInt(positionDetail[0]);
                        int y1 = (jsonObject.containsKey("y1") ? Integer.parseInt(jsonObject.get("y1").toString()) : 15) + Integer.parseInt(positionDetail[1]);
                        String rect = x + "," + y2 + "," + x1 + "," + y1;
                        signTemplate.setRectangle(rect);
                        signTemplate.setPageNumber(Integer.parseInt(positionDetail[2]));
                        signTemplateList.add(signTemplate);
                    }
                }

                SignObj.SmartCAKeyKcb objectKeyKcb = new SignObj.SmartCAKeyKcb();
                objectKeyKcb.setKyHieuPhieu(jsonObject.get("kyHieuPhieu").toString());
                objectKeyKcb.setMaBenhNhan(Long.valueOf(jsonObject.get("maBenhNhan").toString()));
                objectKeyKcb.setMaDichVu(Long.valueOf(jsonObject.containsKey("maDichVu") ? jsonObject.get("maDichVu").toString() : "-1"));
                objectKeyKcb.setNghiepVu(jsonObject.containsKey("nghiepVu") ? jsonObject.get("nghiepVu").toString() : "");
                objectKeyKcb.setNoiTru(Integer.parseInt(jsonObject.containsKey("noiTru") ? jsonObject.get("noiTru").toString() : "-1"));
                objectKeyKcb.setSoBe(Integer.parseInt(jsonObject.containsKey("soBe") ? jsonObject.get("soBe").toString() : "-1"));
                objectKeyKcb.setSoBenhAn(jsonObject.containsKey("soBenhAn") ? jsonObject.get("soBenhAn").toString() : "0");
                objectKeyKcb.setSoPhieuDichVu(jsonObject.get("soPhieuDichVu").toString());
                objectKeyKcb.setSoVaoVien(Long.valueOf(jsonObject.get("soVaoVien").toString()));
                objectKeyKcb.setSoVaoVienDT(Long.valueOf(jsonObject.containsKey("soVaoVienDT") ? jsonObject.get("soVaoVienDT").toString() : "0"));
                objectKeyKcb.setSttDotDieuTri(Long.valueOf(jsonObject.containsKey("sttDotDieuTri") ? jsonObject.get("sttDotDieuTri").toString() : "-1"));
                objectKeyKcb.setIdDieuTri(Long.valueOf(jsonObject.containsKey("idDieuTri") ? jsonObject.get("idDieuTri").toString() : "-1"));
                objectKeyKcb.setToaThuoc(jsonObject.containsKey("toaThuoc") ? jsonObject.get("toaThuoc").toString() : "-1");
                objectKeyKcb.setMaKhoa(jsonObject.containsKey("maKhoa") ?
                        (jsonObject.get("maKhoa").toString().equals("")? null: Long.parseLong(jsonObject.get("maKhoa").toString())) : null);

                SignObj signHashDTO = new SignObj();
                signHashDTO.setFileName(jsonObject.get("fileName").toString());
                signHashDTO.setBase64PdfSign(base64Pdf);
                signHashDTO.setBase64ImageSign(jsonObject.containsKey("base64ImageSign") ? jsonObject.get("base64ImageSign").toString() : "");
                signHashDTO.setKeySign(refTranId.toString());
                signHashDTO.setVaiTroKySo(jsonObject.containsKey("vaiTroKySo") ? jsonObject.get("vaiTroKySo").toString() : "1");
                signHashDTO.setSoChuKyDaKy(Integer.parseInt(jsonObject.containsKey("soChuKyDaKy") ? jsonObject.get("soChuKyDaKy").toString() : "0"));
                signHashDTO.setTongSoChuKy(Integer.parseInt(jsonObject.containsKey("tongSoChuKy") ? jsonObject.get("tongSoChuKy").toString() : "1"));
                signHashDTO.setVisibleType(jsonObject.containsKey("visibleType") ? jsonObject.get("visibleType").toString() : "1");
                signHashDTO.setSignTemplateList(signTemplateList);
                signHashDTO.setFontSize(jsonObject.containsKey("fontSize") ? jsonObject.get("fontSize").toString() : "9");
                signHashDTO.setFullName(jsonObject.containsKey("fullName") ? jsonObject.get("fullName").toString() : tennhanvien);
                signHashDTO.setComment(jsonObject.containsKey("comment") ? jsonObject.get("comment").toString() : "[]");
                signHashDTO.setKeyKcb(objectKeyKcb);
                if (jsonObject.containsKey("signXML")){
                    signHashDTO.setSignXML((SignObj.SignXML) jsonObject.get("signXML"));
                }
                Map checkValid = signHashDTO.isValid();
                try {
                    if (!Boolean.valueOf(checkValid.get("valid").toString())) {
                        result.put("SUCCESS", "-10");
                        result.put("KEYMINIO", null);
                        result.put("MESSAGE", checkValid.get("message").toString());
                        return result;
                    }
                    String iconValid = request.getSession().getServletContext().getRealPath("/resources/Theme/camau/valid-icon.png");
                    Date signedDateV2 = new Date();
                    String nameSign = tennhanvien;
                    String timesFont =session.getServletContext().getRealPath("/WEB-INF/classes/font/times.ttf");
                    String base64SourceAfterAddImage = cmuSmartCAService.addImageBoxToPdfFromBase64ItextV2(signHashDTO.getBase64PdfSign(),
                            iconValid,
                            signHashDTO.getSignTemplateList(),
                            nameSign,
                            signedDateV2,
                            timesFont,
                            signHashDTO.getVisibleType(),
                            signHashDTO.getFontSize()
                    );
                    byte[] bytes =java.util.Base64.getDecoder().decode(base64SourceAfterAddImage);
                    Map smartCAAuth = smartCADAO.smartcaLayThongTinCauHinh(dvtt, userId);
                    String userName = smartCAAuth.containsKey("USERNAME") ? smartCAAuth.get("USERNAME").toString() : "";
                    Map smartcaAccessToken = smartCADAO.smartcaLayThongTinToken(dvtt, userName);
                    if (smartcaAccessToken == null) {
                        smartcaAccessToken = new HashMap();
                    }
                    String smartcaToken = smartcaAccessToken.getOrDefault("ACCESS_TOKEN", "").toString();
                    String smartCAVersion = smartCAAuth.getOrDefault("SMARTCA_VERSION", "").toString();
                    String smartCAUrl = smartCAAuth.getOrDefault("URL", "").toString();
                    String smartCAGrantType = smartCAAuth.getOrDefault("GRANT_TYPE", "").toString();
                    String smartCAClientId = smartCAAuth.getOrDefault("CLIENT_ID", "").toString();
                    String smartCAClientSecret = smartCAAuth.getOrDefault("CLIENT_SECRET", "").toString();
                    String password = smartCAAuth.getOrDefault("PASSWORD", "").toString();
                    String urlQD769 = smartCAAuth.getOrDefault("URL_QD_769", "").toString();
                    String serialSmartCA = smartCAAuth.getOrDefault("SERIAL_SMARTCA", "").toString();
                    String tOtp = smartCAVersion.equals("2") ? smartCAAuth.getOrDefault("TOTP", "").toString() : null;
                    String urlESeal = smartCAVersion.equals("2") ? smartCAAuth.getOrDefault("URL_ESEAL", "").toString() : null;
                    Map validAccount = isValidAccount(smartCAAuth);
                    if(!Boolean.valueOf(validAccount.get("valid").toString())) {
                        result.put("SUCCESS", "-11");
                        result.put("KEYMINIO", null);
                        result.put("MESSAGE", validAccount.get("message").toString());
                        return result;
                    }
                    SmartCADetail smartCADetail = new SmartCADetail(
                            smartCAGrantType,
                            userName,
                            password,
                            smartCAUrl,
                            smartCAClientId,
                            smartCAClientSecret,
                            smartcaToken,
                            null,
                            null,
                            null,
                            null,
                            urlESeal,
                            tOtp,
                            urlQD769,
                            serialSmartCA
                    );
                    try {
                        Map<String, Object> signHashPdfResult = new HashMap<>();
                        if (signHashDTO.getKeyKcb() != null) {
                            SignObj.SmartCAKeyKcb keyKcb = signHashDTO.getKeyKcb();
                            String soVaoVien = Objects.toString(keyKcb.getSoVaoVien(), null);
                            String soVaoVienDieuTri = Objects.toString(keyKcb.getSoVaoVienDT(), null);
                            String kyHieuPhieu = keyKcb.getKyHieuPhieu();
                            String soPhieuDichVu = keyKcb.getSoPhieuDichVu();
                            String maDichVu = Objects.toString(keyKcb.getMaDichVu(), "-1");
                            idUniqDangKy = smartCADAO.cmuKhoaPhieuDangKy(dvtt, soVaoVien, soVaoVienDieuTri, kyHieuPhieu, soPhieuDichVu, maDichVu, userId);
                        }
                        if(smartCAVersion.equals("2")) {
                            signHashPdfResult = kySoApiMobileService.signHashPdfV2(smartCADetail, bytes,
                                    iconValid,
                                    signHashDTO.getVisibleType(),
                                    signHashDTO.getFullName(),
                                    signHashDTO.getFontSize().equals("") ? "13" : signHashDTO.getFontSize(),
                                    signHashDTO.getComment(),
                                    signHashDTO.getSignTemplateList(), signHashDTO.getFileName(), dvtt, signHashDTO.getKeySign(), userId,
                                    signHashDTO.getKeyKcb().getKyHieuPhieu()
                            );
                        } else  {
                            signHashPdfResult = kySoApiMobileService.signHashPdfV1(smartCADetail, bytes,
                                    iconValid,
                                    signHashDTO.getVisibleType(),
                                    signHashDTO.getFullName(),
                                    signHashDTO.getFontSize().equals("") ? "13" : signHashDTO.getFontSize(),
                                    signHashDTO.getComment(),
                                    signHashDTO.getSignTemplateList(), signHashDTO.getFileName(), dvtt, signHashDTO.getKeySign(), userId,
                                    signHashDTO.getKeyKcb().getKyHieuPhieu()
                            );
                        }

                        if (!(signHashPdfResult.containsKey("signData") && signHashPdfResult.containsKey("signDate") && signHashPdfResult.containsKey("certificate"))) {
                            result.put("SUCCESS", signHashPdfResult.get("errorCode"));
                            result.put("MESSAGE", signHashPdfResult.get("errorMessage"));
                            if (idUniqDangKy > 0) {
                                smartCADAO.cmuMoKhoaPhieuDangKy(dvtt, idUniqDangKy);
                            }
                            return result;
                        }

                        byte[] signedData = signHashPdfResult.get("signData") != null ? (byte[]) signHashPdfResult.get("signData") : null;
                        Date signedDate = signHashPdfResult.get("signDate") != null ? (Date) signHashPdfResult.get("signDate") : null;

                        String certificateString = "";
                        SmartCAQD769GetCertificateResponseDTO.Data.Certificate certificateResult = signHashPdfResult.get("certificate") != null ? (SmartCAQD769GetCertificateResponseDTO.Data.Certificate) signHashPdfResult.get("certificate") : null;
                        certificateString = new Gson().toJson(certificateResult);
                        String base64Signed = Base64.getEncoder().encodeToString(signedData);
                        String sha256Signed = sha256(base64Signed);
                        String sha256File = tienich.getMD5(base64Signed);
                        Map enableCondau = HisCallTemplate.instance.queryMap(dataSourceMNG, "CMU_TRANGTHAIDONGDAU",
                                dvtt,
                                signHashDTO.getKeyKcb().getKyHieuPhieu()
                        );
                        if(enableCondau.getOrDefault("DONGDAU", "").toString().equals("1")) {
                            Map smartCAAuthDonvi = smartCADAO.smartcaLayThongTinCauHinhDonvi(dvtt);
                            if(smartCAAuthDonvi != null && !smartCAAuthDonvi.isEmpty()) {
                                SmartCADetail smartCADetailDV = cmuSmartCAService.getSmartCADetail(dvtt, smartCAAuthDonvi);
                                List<SignObj.SignTemplate> signTemplateListCondau = new ArrayList<>();
                                for (int i = 0; i < signTemplateList.size(); i++) {
                                    SignObj.SignTemplate template = signTemplateList.get(i);
                                    String rect = template.getRectangle();
                                    String[] rects = rect.split(",");
                                    int x = Integer.parseInt(rects[0]) + Integer.parseInt(enableCondau.getOrDefault("X", "0").toString());
                                    int y2 = Integer.parseInt(rects[1]) + Integer.parseInt(enableCondau.getOrDefault("Y2", "0").toString());
                                    int x1 = Integer.parseInt(rects[2]) + Integer.parseInt(enableCondau.getOrDefault("X1", "0").toString());
                                    int y1 = Integer.parseInt(rects[3]) + Integer.parseInt(enableCondau.getOrDefault("Y1", "0").toString());
                                    String rectCondau = x + "," + y2 + "," + x1 + "," + y1;
                                    template.setRectangle(rectCondau);
                                    signTemplateListCondau.add(template);
                                }
                                Map<String, Object> signHashPdfResultDongdau = kySoApiMobileService.signHashPdfV2(smartCADetailDV, signedData,
                                        smartCAAuthDonvi.getOrDefault("HINHANH", "").toString(),
                                        "7",
                                        signHashDTO.getFullName(),
                                        signHashDTO.getFontSize().equals("") ? "13" : signHashDTO.getFontSize(),
                                        signHashDTO.getComment(),
                                        signTemplateListCondau,
                                        signHashDTO.getFileName()+"_dongdau", dvtt,
                                        signHashDTO.getKeySign()+"-dongdau", userId, signHashDTO.getKeyKcb().getKyHieuPhieu());
                                if(signHashPdfResult.containsKey("signData") && signHashPdfResult.get("signData") != null) {
                                    signedData = (byte[]) signHashPdfResultDongdau.get("signData");
                                    base64Signed = Base64.getEncoder().encodeToString(signedData);
                                    sha256Signed = sha256(base64Signed);
                                    sha256File = tienich.getMD5(base64Signed);
                                }
                            }
                        }
                        String keyMinio = smartCADAO.smartcaSaveFileSigned(dvtt, signHashDTO.getKeySign(), sha256Signed, base64Signed, sha256File, signHashDTO.getFileName());
                        if (!keyMinio.isEmpty()) {
                            int idUserSigned = smartCADAO.smartcaSaveUserSigned(dvtt, signHashDTO.getKeySign(), userId, 1, signHashDTO.getVaiTroKySo(), certificateString, signHashDTO.getBase64ImageSign());
                            if (signHashDTO.getKeyKcb() != null) {
                                SignObj.SmartCAKeyKcb keyKcb = signHashDTO.getKeyKcb();
                                String noiTru = Objects.toString(keyKcb.getNoiTru(), null);
                                String maBenhNhan = Objects.toString(keyKcb.getMaBenhNhan(), null);
                                String soVaoVien = Objects.toString(keyKcb.getSoVaoVien(), null);
                                sovaovien = soVaoVien;
                                String soVaoVienDieuTri = Objects.toString(keyKcb.getSoVaoVienDT(), null);
                                String soBenhAn = Objects.toString(keyKcb.getSoBenhAn(), null);
                                String sttDotDieuTri = Objects.toString(keyKcb.getSttDotDieuTri(), null);
                                String kyHieuPhieu = keyKcb.getKyHieuPhieu();
                                kyHieuLog = kyHieuPhieu;
                                String idDieuTri = Objects.toString(keyKcb.getIdDieuTri(), null);
                                String soPhieuDichVu = keyKcb.getSoPhieuDichVu();
                                String ngay = Objects.toString(keyKcb.getNgay(), null);
                                String tuNgay = Objects.toString(keyKcb.getTuNgay(), null);
                                String denNgay = Objects.toString(keyKcb.getDenNgay(), null);
                                String maKhoa = Objects.toString(keyKcb.getMaKhoa(), null);
                                String tenKhoa = keyKcb.getTenKhoa();
                                String nghiepVu = keyKcb.getNghiepVu();
                                String toaThuoc = keyKcb.getToaThuoc();
                                String soBe = Objects.toString(keyKcb.getSoBe(), "-1");
                                String maDichVu = Objects.toString(keyKcb.getMaDichVu(), "-1");
                                String inTong = Objects.toString(keyKcb.getInTong(), null);

                                int idSignedKcb = smartCADAO.smartcaSaveSignedKcb(dvtt, signHashDTO.getKeySign(), noiTru, maBenhNhan, soVaoVien, soVaoVienDieuTri,
                                        soBenhAn, sttDotDieuTri, kyHieuPhieu, idDieuTri, soPhieuDichVu, ngay, tuNgay, denNgay, maKhoa, tenKhoa, nghiepVu, toaThuoc,
                                        inTong, userName, userId, 1, 0, soBe, maDichVu, "", 0);
                                String ts960599 = thamsodonviDAO.laythamso_donvi_motthamso(dvtt, "960599");
                                String ts960600 = thamsodonviDAO.laythamso_donvi_motthamso(dvtt, "960600");
                                if(kyHieuPhieu.equals("NOITRU_GIAYCHUNGSINH") && ts960599.equals("1")) {
                                    String XMLDATA = HisCallTemplate.instance.queryObj(String.class,dataSourceMNG, "CMU_GETXML_GIAYCHUNGSINH",
                                            dvtt,
                                            sovaovien,
                                            soPhieuDichVu
                                    );
                                    XMLDATA = XMLDATA.replaceAll("<NGUOI_DO_DE></NGUOI_DO_DE>","<NGUOI_DO_DE>"+tennhanvien+"</NGUOI_DO_DE>");
                                    HisCallTemplate.instance.queryObj(String.class,dataSourceMNG, "CMU_UPXML_GIAYCHUNGSINH",
                                            dvtt,
                                            sovaovien,
                                            soPhieuDichVu,
                                            XMLDATA
                                    );
                                }
                                if(kyHieuPhieu.equals("NOITRU_GIAYCHUNGTU") && ts960600.equals("1")) {
                                    String XMLDATA = HisCallTemplate.instance.queryObj(String.class,dataSourceMNG, "CMU_GETXML_GIAYCHUNGTU",
                                            dvtt,
                                            sovaovien
                                    );
                                    XMLDATA = XMLDATA.replaceAll("<NGUOI_GHIGIAY></NGUOI_GHIGIAY>","<NGUOI_GHIGIAY>"+tennhanvien+"</NGUOI_GHIGIAY>");
                                    HisCallTemplate.instance.queryObj(String.class,dataSourceMNG, "CMU_UPXML_GIAYCHUNGTU",
                                            dvtt,
                                            sovaovien,
                                            XMLDATA
                                    );

                                }
                                if(kyHieuPhieu.equals("NOITRU_GIAYCHUNGTU_BGD") || kyHieuPhieu.equals("NOITRU_GIAYCHUNGSINH_BGD")) {
                                    String tempXML = "";
                                    if(kyHieuPhieu.equals("NOITRU_GIAYCHUNGSINH_BGD")) {
                                        tempXML = HisCallTemplate.instance.queryObj(String.class,dataSourceMNG, "CMU_GETXML_GIAYCHUNGSINH",
                                                dvtt,
                                                sovaovien,
                                                soPhieuDichVu
                                        );
                                        tempXML = tempXML.replaceAll("<THU_TRUONG_DVI></THU_TRUONG_DVI>","<THU_TRUONG_DVI>"+tennhanvien+"</THU_TRUONG_DVI>");

                                    }
                                    if(kyHieuPhieu.equals("NOITRU_GIAYCHUNGTU_BGD") ) {
                                        tempXML = HisCallTemplate.instance.queryObj(String.class,dataSourceMNG, "CMU_GETXML_GIAYCHUNGTU",
                                                dvtt,
                                                sovaovien
                                        );
                                        tempXML = tempXML.replaceAll("<TTRUONG_DVI></TTRUONG_DVI>","<TTRUONG_DVI>"+tennhanvien+"</TTRUONG_DVI>");

                                    }
                                    final String finalDataXML = tempXML;
                                    final String sovaovienf = sovaovien;
                                    final String soPhieuDichVuf = soPhieuDichVu;
                                    new Thread(()  -> {
                                        String idTrace = cmuApiService.insertTrace(getUserIdFromToken(request), finalDataXML, request);
                                        Map<String, Object> signedXML =  cmuSmartCAService.signXMLBHXH(finalDataXML, dvtt, userId, "" );
                                        String xmlSignBase64 = Base64.getEncoder().encodeToString((byte [])signedXML.get("signData"));
                                        Map<String, Object> resBHXH = cmuSmartCAService.sendCongBHXHGCS(dvtt,xmlSignBase64,
                                                kyHieuPhieu.equals("NOITRU_GIAYCHUNGTU_BGD")? "60": "61");
                                        cmuApiService.updateTrace(idTrace, resBHXH.toString());
                                        if(kyHieuPhieu.equals("NOITRU_GIAYCHUNGTU_BGD")) {
                                            HisCallTemplate.instance.queryObj(String.class,dataSourceMNG, "CMU_UPXML_GIAYCHUNGTU",
                                                    dvtt,
                                                    sovaovienf,
                                                    xmlSignBase64
                                            );

                                        }
                                        if(kyHieuPhieu.equals("NOITRU_GIAYCHUNGSINH_BGD") ) {
                                            HisCallTemplate.instance.queryObj(String.class,dataSourceMNG, "CMU_UPXML_GIAYCHUNGSINH",
                                                    dvtt,
                                                    sovaovienf,
                                                    soPhieuDichVuf,
                                                    xmlSignBase64
                                            );

                                        }

                                    }).start();
                                }
                                if(kyHieuPhieu.equals("NOITRU_GIAYCHUNGTU") || kyHieuPhieu.equals("NOITRU_GIAYCHUNGSINH")
                                        || kyHieuPhieu.equals("NOITRU_GIAYRAVIEN")
                                        || kyHieuPhieu.equals("PHIEU_NOITRU_GIAYCHUNGNHAN_TRUONGKHOA")
                                ) {
                                    String titleMes = "Giấy ra viện";
                                    if(kyHieuPhieu.equals("NOITRU_GIAYCHUNGTU")) {
                                        titleMes = "Giấy chứng tử";
                                    }
                                    if(kyHieuPhieu.equals("NOITRU_GIAYCHUNGSINH")) {
                                        titleMes = "Giấy chứng sinh";
                                    }
                                    if(kyHieuPhieu.equals("PHIEU_NOITRU_GIAYCHUNGNHAN_TRUONGKHOA")) {
                                        titleMes = "Giấy chứng nhận phẫu thuật";
                                    }
                                    final String finalTitleMes = titleMes;
                                    new Thread(()  -> {
                                        try {
                                            FCMServ.sendMessageToTopic(maKhoa+"_BGD", finalTitleMes, jsonObject.getOrDefault("tenBenhNhan", "").toString());
                                        } catch (Exception e) {
                                            e.printStackTrace();
                                        }
                                    }).start();

                                }

                                String pattern = "yyyy-MM-dd HH:mm:ss";
                                SimpleDateFormat simpleDateFormat = new SimpleDateFormat(pattern);
                                int resComplete = smartCADAO.smartcaUpdateFileComplete(dvtt, signHashDTO.getKeySign(), String.valueOf(signHashDTO.getSoChuKyDaKy()), String.valueOf(signHashDTO.getTongSoChuKy()), simpleDateFormat.format(signedDate), userId, 0);
                            }
                        }
                        if (idUniqDangKy > 0) {
                            smartCADAO.cmuMoKhoaPhieuDangKy(dvtt, idUniqDangKy);
                        }

                        SignObj.SignXML signXML = signHashDTO.getSignXML();

                        if (signXML != null && signXML.getIsSign().equals("0")){
                            InetAddress inetAddress = InetAddress.getLocalHost();
                            String ipAddress = inetAddress.getHostAddress();
                            Map certificate = smartCADAO.smartcaGetCertificate(dvtt, signHashDTO.getKeySign(), signHashDTO.getFileName(), userId);
                            Map responseFile = kySoApiMobileService.signFakeXml(dvtt, signXML, certificate);
                            try {
                                int idUserSigned = 0;
                                String dataSigned = responseFile.get("dataSigned").toString();
                                String base64Data = responseFile.get("base64Data").toString();
                                sha256File = tienich.getMD5(signXML.getXmlFile());
                                sha256Signed = tienich.getMD5(dataSigned);
                                int idSignedFile = smartCADAO.smartcaSaveFileSignedXml(dvtt, signXML.getKeySign(), sha256Signed, dataSigned, sha256File, signXML.getNameFile(), 1, signXML.getXmlKey(), base64Data);
                                if (idSignedFile > 0) {
                                    int signed = 1;
                                    idUserSigned = smartCADAO.smartcaSaveUserSignedXml(dvtt, signXML.getKeySign(), userId, 1);
                                }

                                if (idSignedFile > 0 && idUserSigned > 0) {
                                    result.put("SUCCESS", idUserSigned);
                                    result.put("STATUS_CODE", DocumentType.XML + ".EMR." + HttpServletResponse.SC_OK);
                                    result.put("MESSAGE", "Ký số thành công");
                                    luuLogHSBATheoBN(dvtt, sovaovien, kyHieuLog, "",
                                            "Nhân viên: " + tennhanvien + " ký số phiếu " + signHashDTO.getFileName() + " (Tạo trên mobile)",
                                            userId, "INSERT");
                                    smartCADAO.smartcaCapNhatLog(dvtt, userId, 3, "Cập nhật ký số XML EMR", "/smartca-sign-save-file-xml", signXML.toString(), result.toString());
                                    logActionDAO.writeLogAction(dvtt, "Upload signature file", "smartcaSaveFileSigned", Integer.parseInt(userId), ipAddress, signXML.toString(), "", idUserSigned);
                                } else {
                                    smartCADAO.smartcaCapNhatHuyKySo(dvtt, signXML.getKeySign(), userId);
                                    result.put("SUCCESS", -999);
                                    result.put("MESSAGE", "Ký số thất bại. Không lưu được xml");
                                    return result;
                                }
                            } catch (Exception e) {
                                result.put("SUCCESS", -1);
                                result.put("MESSAGE", "" + e.getMessage());
                                logActionDAO.writeLogAction(dvtt, "Upload signature file", "smartcaSaveFileSigned", Integer.parseInt(userId), ipAddress, signXML.toString(), e.getMessage(), 0);
                                return result;
                            }
                        }
                        result.put("DATA", base64Signed);
                        result.put("KEYSIGN", signHashDTO.getKeySign());
                        result.put("SUCCESS", 1);
                        return result;
                    } catch (Exception ex) {
                        if (idUniqDangKy > 0) {
                            smartCADAO.cmuMoKhoaPhieuDangKy(dvtt, idUniqDangKy);
                        }
                        ex.printStackTrace();
                        result.put("SUCCESS", "-13");
                        result.put("KEYMINIO", null);
                        result.put("MESSAGE", "Ký số thất bại. Gửi thông tin ký số sang SmartCA không thành công!");
                        return result;
                    }

                } catch (Exception ex) {
                    if (idUniqDangKy > 0) {
                        smartCADAO.cmuMoKhoaPhieuDangKy(dvtt, idUniqDangKy);
                    }
                    ex.printStackTrace();
                    result.put("SUCCESS", "-9");
                    result.put("KEYMINIO", null);
                    result.put("MESSAGE", "Ký số thất bại. Xảy ra lỗi ngoại lệ " + ex.getMessage());
                    return result;
                }
            } catch (Exception ex) {
                ex.printStackTrace();
                result.put("SUCCESS", "-17");
                result.put("KEYMINIO", null);
                result.put("MESSAGE", "Ký số thất bại. Xảy ra lỗi ngoại lệ " + ex.getMessage());
                return result;
            }
        } catch (Exception ex) {
            if (idUniqDangKy > 0) {
                smartCADAO.cmuMoKhoaPhieuDangKy(dvtt, idUniqDangKy);
            }
            ex.printStackTrace();
            result.put("SUCCESS", "-9");
            result.put("KEYMINIO", null);
            result.put("MESSAGE", "Ký số thất bại. Xảy ra lỗi ngoại lệ " + ex.getMessage());
            return result;
        }
    }

    public @ResponseBody
    @PostMapping(value = "/get-position-keyword")
    Map getPositionKeyword(@RequestBody JSONObject jsonObject,
                           HttpSession session,
                           HttpServletResponse response,
                           HttpServletRequest request) {
        HttpStatus authStatus = verifyToken(request);
        if (authStatus == HttpStatus.UNAUTHORIZED) {
            response.setStatus(401);
            return getResponseUnauthorized();
        }
        Map userInfo = getUserIdFromToken(request);
        String userId = userInfo.get("userId").toString();
        Map<String, Object> result = new HashMap<String, Object>();
        try {
            String base64Pdf = "";
            if (jsonObject.getOrDefault("isPath", "0").toString().contains("1")) {
                base64Pdf = getPdfFromApiAsBase64(jsonObject.get("url").toString(), request);
            } else {
                base64Pdf = jsonObject.get("url").toString();
            }
            Map mapGetPosition = new HashMap();
            UUID refTranId = UUID.randomUUID();
            mapGetPosition.put("upfile", "data:application/pdf;base64," + base64Pdf);
            mapGetPosition.put("keyword", jsonObject.get("keyword").toString());
            mapGetPosition.put("tenfile", refTranId.toString());
            mapGetPosition.put("mabenhnhan", jsonObject.get("maBenhNhan").toString());
            String position = cmu_getposition(mapGetPosition, request);
            if (position.contains("ERROR")) {
                result.put("SUCCESS", "-18");
                result.put("MESSAGE", "Không tìm thấy vị trí ký số trên file.");
                return result;
            }
            result.put("POSITION", position);
            result.put("SUCCESS", "0");
        } catch (Exception ex) {
            ex.printStackTrace();
            result.put("SUCCESS", "-9");
            result.put("KEYMINIO", null);
            result.put("MESSAGE", "Ký số thất bại. Xảy ra lỗi ngoại lệ " + ex.getMessage());
            return result;
        }

        return result;
    }

    @RequestMapping(value = "/get-file-minio", method = {RequestMethod.POST})
    public @ResponseBody
    Map smartcaGetSignedFileMinio(
            @RequestBody JSONObject jsonObject,
            HttpSession session,
            HttpServletResponse response,
            HttpServletRequest request
    ) throws Exception {
        HttpStatus authStatus = verifyToken(request);
        if(authStatus == HttpStatus.UNAUTHORIZED){
            response.setStatus(401);
            return getResponseUnauthorized();
        }
        Map userInfo = getUserIdFromToken(request);
        String dvtt = userInfo.get("dvtt").toString();
        String userId = userInfo.get("userId").toString();
        Map result = new HashMap();
        String keyminio = jsonObject.get("keyminio").toString();
        String fileMinio = smartCADAO.smartcaThongTinFileMinio(keyminio);
        fileMinio = fileMinio.replaceAll("data:application/pdf;filename=generated.pdf;base64,", "");
        InetAddress inetAddress = InetAddress.getLocalHost();
        String ipAddress = inetAddress.getHostAddress();
        logActionDAO.writeLogAction(dvtt, "Download file", "smartcaKiemTraFileSigned", Integer.parseInt(userId), ipAddress, "keyminio:" + keyminio, "", 1);
        result.put("FILE", fileMinio);
        return result;
    }

    @RequestMapping(value = "/get-rispacsfile-minio", method = {RequestMethod.POST})
    public @ResponseBody
    Map smartcaGetSignedRispacs(
            @RequestBody JSONObject jsonObject,
            HttpSession session,
            HttpServletResponse response,
            HttpServletRequest request
    ) throws Exception {
        HttpStatus authStatus = verifyToken(request);
        if(authStatus == HttpStatus.UNAUTHORIZED){
            response.setStatus(401);
            return getResponseUnauthorized();
        }
        Map userInfo = getUserIdFromToken(request);
        String dvtt = userInfo.get("dvtt").toString();
        Map<String, Object> mapConfig = new HashMap<>();
        mapConfig = rispacsdao.getConfigAccountRISPACS(dvtt);
        String fileMinio = "";
        if (!mapConfig.isEmpty()){
            String maPhieuChiDinh = jsonObject.get("so_phieu_dv").toString();
            String maDichVu = jsonObject.get("ma_dich_vu").toString();
            MultiValueMap<String, String> mapAccount= new LinkedMultiValueMap<>();
            mapAccount.add("username", mapConfig.get("USER_NAME").toString());
            mapAccount.add("password", mapConfig.get("PASSWORD").toString());
            mapAccount.add("grant_type", "password");
            String urlLogin = mapConfig.get("API_AUTH_LOGIN").toString();
            String urlGetfile = mapConfig.get("API_GET_FILE_DAKY").toString();
            String basicAuth = mapConfig.get("BACSIC_AUTH").toString();

            int retry = 2;
            boolean success = false;
            String token = "";
            do {
                URL url = new URL(urlLogin);
                org.json.JSONObject jsonHeader = new org.json.JSONObject();
                if (!url.getHost().contains("connector")){
                    jsonHeader.put("Authorization", "Basic " + basicAuth);
                }
                try {
                    ResponseEntity<String> result = httpClientAPIComponent.callAPIFormUrlEncoded(urlLogin, jsonHeader, mapAccount);
                    JsonParser jsonParser = new JsonParser();
                    JsonObject jsonBody = jsonParser.parse(result.getBody().toString()).getAsJsonObject();
                    token = jsonBody.get("access_token").getAsString();
                    success = true;
                }catch (Exception e){
                    retry--;
                    success = false;
                }
            } while (!success);
            if (!token.equals("")){
                Map<String, Object> mapData = new HashMap<>();
                mapData.put("request_code", maPhieuChiDinh);
                mapData.put("concept_code", maDichVu);
                try {
                    org.json.JSONObject jsonHeader = new org.json.JSONObject();
                    jsonHeader.put("Authorization", "Bearer " + token);
                    ResponseEntity<byte[]> resultFile = httpClientAPIComponent.exportFileByQueryParam(urlGetfile, jsonHeader, mapData);
                    fileMinio = java.util.Base64.getEncoder().encodeToString(resultFile.getBody());
                } catch (Exception e){
                }
            }

        }
        Map result = new HashMap();
        result.put("FILE", fileMinio);
        return result;
    }

    @RequestMapping(value = "/huykyso", method = {RequestMethod.POST})
    public @ResponseBody
    Map smartcaCapNhatHuyKySo(@RequestBody JSONObject jsonObject,
                              HttpSession session,
                              HttpServletResponse response,
                              HttpServletRequest request) {
        HttpStatus authStatus = verifyToken(request);
        if(authStatus == HttpStatus.UNAUTHORIZED){
            response.setStatus(401);
            return getResponseUnauthorized();
        }
        Map userInfo = getUserIdFromToken(request);
        String dvtt = userInfo.get("dvtt").toString();
        String userId = userInfo.get("userId").toString();
        String keysign = jsonObject.get("keysign").toString();
        String idTrace =  cmuApiService.insertTrace(getUserIdFromToken(request),jsonObject.toString(), request);
        Map result = smartCADAO.smartcaCapNhatHuyKySo(dvtt, keysign, userId);
        if (!result.isEmpty()) {
            JsonObject paramObject = new JsonObject();
            paramObject.addProperty("keysign", keysign);
            smartCADAO.smartcaCapNhatLog(dvtt, userId, 3, "Hủy ký số", "/smartca-capnhat-huykyso", paramObject.toString(), result.toString());
        }
        cmuApiService.updateTrace(idTrace,result.toString());
        return result;
    }

    public Map isValidAccount(Map smartCAAuth) {
        Map result = new HashMap();
        result.put("valid", true);
        String message = "";
        String smartCAVersion = smartCAAuth.getOrDefault("SMARTCA_VERSION", "").toString();
        String smartCAUrl = smartCAAuth.getOrDefault("URL", "").toString();
        String smartCAGrantType = smartCAAuth.getOrDefault("GRANT_TYPE", "").toString();
        String smartCAClientId = smartCAAuth.getOrDefault("CLIENT_ID", "").toString();
        String smartCAClientSecret = smartCAAuth.getOrDefault("CLIENT_SECRET", "").toString();
        String password = smartCAAuth.getOrDefault("PASSWORD", "").toString();
        String activate = smartCAAuth.getOrDefault("ACTIVATE", "0").toString();
        String urlQD769 = smartCAAuth.getOrDefault("URL_QD_769", "").toString();
        String serialSmartCA =  smartCAAuth.getOrDefault("SERIAL_SMARTCA", "").toString();
        String tOtp = smartCAVersion.equals("2") ? smartCAAuth.getOrDefault("TOTP", "").toString() : null;
        String urlESeal = smartCAVersion.equals("2") ? smartCAAuth.getOrDefault("URL_ESEAL", "").toString() : null;
        if(smartCAUrl.equals("")) {
            result.put("valid", false);
            message += "Smartca không được để trống; ";
        }
        if(smartCAGrantType.equals("")) {
            result.put("valid", false);
            message += "GRANT_TYPE không được để trống; ";
        }
        if(smartCAClientId.equals("")) {
            result.put("valid", false);
            message += "Client ID không được để trống; ";
        }
        if(smartCAClientSecret.equals("")) {
            result.put("valid", false);
            message += "Client Secret không được để trống; ";
        }
        if(password.equals("")) {
            result.put("valid", false);
            message += "Password không được để trống; ";
        }

        if(!activate.equals("1")) {
            result.put("valid", false);
            message += "Tài khoản chưa được cấu hình kích hoạt; ";
        }

        if(serialSmartCA.equals("")) {
            result.put("valid", false);
            message += "Serial number không được để trống;; ";
        }

        if(urlQD769.equals("")) {
            result.put("valid", false);
            message += "URL 769 không được để trống;; ";
        }

        if (smartCAVersion.equals("2")) {
            if(tOtp.equals("")) {
                result.put("valid", false);
                message += "TOTP không được để trống;; ";
            }
            if(urlESeal.equals("")) {
                result.put("valid", false);
                message += "URL Seal không được để trống;; ";
            }
        }

        result.put("message", message);
        return result;
    }

    public JSONObject getResponseUnauthorized(){
        JSONObject ketQuaObj = new JSONObject();
        ketQuaObj.put("errorCode","401");
        ketQuaObj.put("errorMessage","Unauthorized");
        ketQuaObj.put("result",null);
        return ketQuaObj;
    }

    public String luuLogHSBATheoBN(String dvtt, String sovaovien, String loai, String noidungbandau, String noidungmoi, String nguoitao, String action) {
        JdbcTemplate jdbcTemplate = new JdbcTemplate(this.dataSourceMNG);
        String sqlLog = "call HSBA_LOG_INS(?,?,?,?,?,?,?)#s,s,s,s,s,s,s,s";
        return  jdbcTemplate.queryForObject(sqlLog, new Object[]{dvtt, sovaovien, loai, noidungbandau, noidungmoi, nguoitao, action}, String.class);
    }

    public List<KiemTraSmartCAModel> kiemTraSmarCA769(String dvtt, String soVaoVien, String soVaoVienDt, String kyHieuPhieu, String soPhieu, String userId, String maDV){
        String sql = "call CMU_SMART769_CHECK(?,?,?,?,?,?,?)#c,s,s,s,s,s,s,s";
        JdbcTemplate jdbcTemplate = new JdbcTemplate(dataSourceMNG);
        List<Map<String, Object>> ret = jdbcTemplate.queryForList(sql, new Object[]{dvtt, soVaoVien, soVaoVienDt, kyHieuPhieu, soPhieu, userId, maDV});

        ObjectMapper objectMapper = new ObjectMapper();

        List<KiemTraSmartCAModel> kiemTraSmartCAList = new ArrayList<>();

        for (Map<String, Object> map : ret) {
            try {
                String jsonString = objectMapper.writeValueAsString(map);
                KiemTraSmartCAModel kiemTraSmartCAObj = objectMapper.readValue(jsonString, KiemTraSmartCAModel.class);
                kiemTraSmartCAList.add(kiemTraSmartCAObj);
            } catch (Exception e) {
                e.printStackTrace();
            }
        }

        return kiemTraSmartCAList;
    }

    public Integer smartcaKiemTraDangNhap(String dvtt, String userId, String userName, String passWord){
        Map smartCAAuth = smartCADAO.smartcaThongTinDangNhap(dvtt, userId, userName, passWord);
        if (smartCAAuth != null && smartCAAuth.containsKey("EXIST") && smartCAAuth.getOrDefault("EXIST", "0").toString().equals("0")) { // Tài khoản chưa cấu hình
            return 0; // does not exist authorization HIS
        }
        Map smartcaAccessToken = smartCADAO.smartcaLayThongTinToken(dvtt, userName);
        boolean login = SmartCAService.checkLoginSmartCA(smartcaAccessToken, smartCAAuth);
        if (!login) {
            String smartcaUrl = smartCAAuth.getOrDefault("URL", "").toString();
            String clientId = smartCAAuth.getOrDefault("CLIENT_ID", "").toString();
            String clientSecret = smartCAAuth.getOrDefault("CLIENT_SECRET", "").toString();
            SmartCADetail smartCADetail = new SmartCADetail(userName, passWord, smartcaUrl, clientId, clientSecret, "password");
            Account account = SmartCAService.authenticate(smartCADetail);
            if (account != null) {
                String accessToken = account.getAccess_token();
                long expires_in = account.getExpires_in();
                String token_type = account.getToken_type();
                String refresh_token = account.getRefresh_token();
                String scope = account.getScope();
                smartCADetail.setAccessToken(account.getAccess_token());
                return Integer.parseInt(smartCADAO.smartcaLuuDangNhap(dvtt, accessToken, expires_in, token_type, refresh_token, scope, userName, passWord));
            } else {
                return -1; // does not exist authorization SmartCA
            }
        } else {
            return 1; // logged
        }
    }

    public static String getPdfFromApiAsBase64(String fullUrl, HttpServletRequest request) throws IOException {
        CloseableHttpClient httpClient = HttpClients.createDefault();

        int port = request.getServerPort();
        String url = "http://localhost:" + port+"/web_his";
        HttpPost httpPost = new HttpPost(url+fullUrl);

        // Thêm các tiêu đề từ yêu cầu gốc để xác thực, ngoại trừ Content-Length
        Enumeration<String> headerNames = request.getHeaderNames();
        while (headerNames.hasMoreElements()) {
            String headerName = headerNames.nextElement();
            if (!headerName.equalsIgnoreCase("Content-Length")) {
                httpPost.setHeader(headerName, request.getHeader(headerName));
            }
        }

        CloseableHttpResponse response = httpClient.execute(httpPost);

        try {
            if (response.getStatusLine().getStatusCode() == 200) {
                InputStream pdfStream = response.getEntity().getContent();
                byte[] pdfBytes = IOUtils.toByteArray(pdfStream);
                return Base64.getEncoder().encodeToString(pdfBytes);
            } else {
                throw new IOException("Failed to download file from API: " + response.getStatusLine());
            }
        } finally {
            response.close();
        }
    }

    public String cmu_getposition(@RequestParam  Map parameters, HttpServletRequest request)  throws IOException {
        Map userInfo = getUserIdFromToken(request);
        String dvtt = userInfo.get("dvtt").toString();
        String upfile = parameters.get("upfile").toString();
        Base64.Decoder decoder = java.util.Base64.getDecoder();
        byte[] decodedByte = decoder.decode(upfile.split(",")[1]);
        String path = request.getSession().getServletContext().getRealPath("/")+ "WEB-INF/pages/camau/reports/";
        File dir = new File(path);
        try{
            if (!dir.exists())
                return "{ERROR:2}";

            String filename = parameters.get("tenfile").toString()+parameters.get("mabenhnhan").toString()+dvtt+new SimpleDateFormat("yyMMddHHmmss").format(new Date())+".pdf";
            FileOutputStream fos = new FileOutputStream(dir.getAbsolutePath() + File.separator + filename);
            fos.write(decodedByte);
            fos.close();
            List<Map<String, Object>> position = PDFTextLocator.getCoordiantes(parameters.get("keyword").toString(), dir.getAbsolutePath() + File.separator + filename);
            ListIterator<Map<String, Object>>
                    iterator = position.listIterator();


            int i = 0;
            String mark_position = "";
            while (iterator.hasNext()) {
                Map<String, Object> dataMap = iterator.next();
                mark_position += dataMap.get("x").toString() + "," + dataMap.get("y").toString() + "," + dataMap.get("page").toString() + ";";

                i++;
            }
            File fileTemp = new File(dir.getAbsolutePath() + File.separator + filename);
            fileTemp.delete();
            return mark_position;
        }
        catch(Exception e){
            return "{ERROR:"+e.toString()+"}";
        }
    }

    public static boolean isValidURL(String url) {
        try {
            new URL(url);
            return true;
        } catch (MalformedURLException e) {
            return false;
        }
    }
}
