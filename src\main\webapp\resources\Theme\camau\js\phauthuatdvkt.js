$(function() {
    var idContainer = "phauthuat_tuongtrinh_container";
    var idCanvas = 'phauthuat_tuongtrinh_canvas';
    var painterObject;
    var dsEkipByID = [];
    var dataUrl = "";
    $("#lanphauthuat-dvkt").click(function() {
        if(!lanPhauThuatTDTObject.SOVAOVIEN || !lanPhauThuatTDTObject.SOVAOVIEN_DT) {
            lanPhauThuatTDTObject = {
                SOVAOVIEN: thongtinhsba.thongtinbn.SOVAOVIEN,
                SOVAOVIEN_DT: thongtinhsba.thongtinbn.SOVAOVIEN_DT
            }
        }
        initGridPhauthuatDVKT()
        $("#lanphauthuat_dvkt_ttpt_hinhthuc").empty()
        if(thongtinhsba.thongtinbn.TYLEBAOHIEM > 0) {
            $("#lanphauthuat_dvkt_ttpt_hinhthuc").html("<option value='0'>BHYT</option>")
        }
        $("#lanphauthuat_dvkt_ttpt_hinhthuc").append("<option value='1'>Thu Phí</option>")
        loadGridPhauthuatDVKT("", thongtinhsba.thongtinbn)
        loadGridPhauthuatDVKTCDPT();
        enableModeButtonPhauthuatDVKT("add")
        $("#lanphauthuat_dvkt_ttpt_hinhthuc").prop("disabled", false);
        $("#lanphauthuat_dvkt_ttpt_loaittpt").prop("disabled", false);
        $("#lanphauthuat_dvkt_ttpt_giochidinh").val(moment().format("DD/MM/YYYY HH:mm"));
    })

    $("#lanphauthuat_dvkt_ttpt_hinhthuc").change(function() {
        loadGridPhauthuatDVKT("", thongtinhsba.thongtinbn)
    })

    $("#lanphauthuat_dvkt_tdt_themoittpt").click(function() {
        var idButton = this.id;
        try {

            clearToolbarJqgrid("lanphauthuat_dvkt_list_ttpt");
            if(!isValidDateTime($("#lanphauthuat_dvkt_ttpt_giochidinh").val())) {
                return notifiToClient("Red", "Thời gian không đúng, vui chọn lại");
            }
            if(!lanPhauThuatObject.BAC_SI_PHAU_THUAT || !lanPhauThuatObject.BAC_SI_GAY_ME) {
                return notifiToClient("Red", "Chưa cập nhật ekip bác sĩ phẫu thuật/gây mê");
            }
            if(!lanPhauThuatObject.BAT_DAU_ME || !lanPhauThuatObject.BAT_DAU_MO || !lanPhauThuatObject.KET_THUC_MO) {
                return notifiToClient("Red", "Chưa cập nhật thời gian mê, bắt đầu mổ, kết thúc");
            }
            showSelfLoading(idButton)
            var resCheck = checkTTPT(idButton, " ");
            if(resCheck === false) {
                hideSelfLoading(idButton)
                return resCheck;
            }
            if(!lanPhauThuatObject.ID_DIEUTRI) {
                $.taoPhieuDieuTriGMHS(thongtinhsba.thongtinbn, function(data) {
                    hideSelfLoading(idButton)
                }, $("#lanphauthuat_dvkt_ttpt_giochidinh").val(),
                    singletonObject.thamSo960637 == 1? lanPhauThuatObject.BAC_SI_GAY_ME:
                    lanPhauThuatObject.BAC_SI_PHAU_THUAT)
            }

            if(resCheck != "") {
                return confirmToClient(resCheck + ".Nhấn tiếp tục để chỉ định - Nhấn hủy để hủy bỏ chỉ định", function() {
                    insertTTPT();
                }, function () {
                    hideSelfLoading(idButton)
                })

            }
            insertTTPT();
        } catch (e) {
            console.log("e", e)
            notifiToClient("Red", MESSAGEAJAX.ERROR)
            hideSelfLoading(idButton)
        }
    })

    $("#lanphauthuat_dvkt_tdt_luuttpt").click(function() {
        var ret = getThongtinRowSelected("lanphauthuat_dvkt_list_phieuttpt")

        try {

            if(!ret.SO_PHIEU_DICHVU) {
                return notifiToClient("Red", "Vui lòng chọn phiếu để chỉnh sửa.")
            }
            clearToolbarJqgrid("lanphauthuat_dvkt_list_ttpt");
            if(!isValidDateTime($("#lanphauthuat_dvkt_ttpt_giochidinh").val())) {
                return notifiToClient("Red", "Thời gian không đúng, vui chọn lại");
            }
            var idButton = "lanphauthuat_dvkt_tdt_luuttpt";
            showSelfLoading(idButton);
            var dataBN = thongtinhsba.thongtinbn;
            if(checkThanhtoanTTPT(ret.SO_PHIEU_DICHVU, dataBN) == 1) {
                hideSelfLoading(idButton);
                return notifiToClient("Red", "Phiếu xét nghiệm đã được thanh toán, không thể cập nhật.")
            }

            var resCheck = checkTTPT(idButton, ret.SO_PHIEU_DICHVU);
            if(resCheck === false) {
                return resCheck;
            }
            if(moment($("#lanphauthuat_dvkt_ttpt_giochidinh").val(), ['DD/MM/YYYY HH:mm']).format("DD/MM/YYYY")
                != moment(lanPhauThuatTDTObject.NGAYGIOLAPTDT, ['DD/MM/YYYY HH:mm']).format("DD/MM/YYYY")) {
                var ktracothuoc = $.ajax({
                    url: "cmu_post",
                    method: "POST",
                    data: {
                        url: [
                            singletonObject.dvtt,
                            thongtinhsba.thongtinbn.STT_BENHAN,
                            thongtinhsba.thongtinbn.STT_DOTDIEUTRI,
                            lanPhauThuatTDTObject.ID_DIEUTRI,
                            'CMU_CHECK_THUOCCLS'].join("```")
                    },
                    async: false,
                }).responseText;
                if(ktracothuoc > 0) {
                    hideSelfLoading(idButton)
                    return notifiToClient("Red", "Không thể thay đổi ngày chỉ định, vui lòng xóa chỉ định lại");
                }
            }
            if(resCheck != "") {
                return confirmToClient(resCheck + ".Nhấn tiếp tục để chỉ định - Nhấn hủy để hủy bỏ chỉ định", function() {
                    doUpateTTPT(ret, dataBN)
                }, function() {
                    hideSelfLoading(idButton)
                })
            }
            doUpateTTPT(ret, dataBN);

        } catch (e) {
            notifiToClient("Red", MESSAGEAJAX.ERROR)
            hideSelfLoading(idButton)
        }
    })

    $("#lanphauthuat_dvkt_tdt_huyttpt").click(function() {
        loadGridPhauthuatDVKT("", thongtinhsba.thongtinbn);
        loadGridPhauthuatDVKTCDPT()
        hideSelfLoading("lanphauthuat_dvkt_tdt_themoittpt")
        hideSelfLoading("lanphauthuat_dvkt_tdt_luuttpt")
        hideSelfLoading("lanphauthuat_dvkt_tdt_xoattpt")
        hideSelfLoading("lanphauthuat_dvkt_tdt_kysottpt")
        $("#lanphauthuat_dvkt_ttpt_hinhthuc").prop("disabled", false);
        $("#lanphauthuat_dvkt_ttpt_phongttpt").prop("disabled", false);
        $("#lanphauthuat_dvkt_ttpt_loaittpt").prop("disabled", false);
        enableModeButtonPhauthuatDVKT('add')
    })
    $("#lanphauthuat_tuongtrinhpt_luu").click(function() {
        var idButton = this.id;
        var rowSelected = lanPhauThuatObject.CHITIETDVKT;
        var dataEkip =  $.ajax({
            url: "cmu_getlist?url="+convertArray([singletonObject.dvtt, lanPhauThuatObject.ID, rowSelected.SO_PHIEU_DICHVU, rowSelected.MA_DV,'CMU_LANPHAUTHUAT_EKIP_DVKT_SEL']),
            method: "GET",
            async: false
        }).responseText;
        var dataEkip = JSON.parse(dataEkip);
        if(dataEkip.length == 0) {
            loadDSEkipView();
            return notifiToClient("Red", "Vui lòng nhập ekip")
        }
        var allEkip = dataEkip;
        var ekipArr = []
        var objectEkip = {};
        allEkip.forEach(function(item) {
            objectEkip[item.VITRI] = item.MANHANVIEN;
            objectEkip[item.VITRI+"_TEN"] = item.TENNHANVIEN;
            ekipArr.push({
                VITRI: item.VITRI,
                MANHANVIEN: item.MANHANVIEN
            })
        })
        if(!objectEkip.BSPHAUTHUAT) {
            return notifiToClient("Red", "Vui lòng chọn bác sĩ phẫu thuật")
        }
        if($("#phauthuat_tuongtrinh_nguoithuchien").val() && $("#phauthuat_tuongtrinh_nguoithuchien").val() != singletonObject.userId) {
            return notifiToClient("Red", MESSAGEAJAX.PERMISSION)
        }
        if($("#phauthuatTuongtrinhForm").valid()) {
            showSelfLoading(idButton)
            phauthuat_luu_vitrittpt(idButton)
        }
    })
    $("#phauthuat_tuongtrinh_mautuongtrinh").combogrid({
        url: 'select_maupttt_theodvtt',
        debug: true,
        width: "670px",
        colModel: [{'columnName': 'MA_MAUPTT', 'label': 'MA_MAUPTT', hidden: true},
            {'columnName': 'TEN_MAUPTTT', 'width': '100%', 'label': 'Tên Mẫu PTTT', 'align': 'left'},
            {'columnName': 'NOIDUNG','label': 'NOIDUNG', hidden: true}
        ],
        select: function (event, ui) {
            $("#phauthuat_tuongtrinh_mautuongtrinh").val(ui.item.TEN_MAUPTTT);
            CKEDITOR.instances['phauthuat_tuongtrinh_noidung'].setData(ui.item.NOIDUNG)
            return false;
        }
    });
    $("#phauthuat_vitrittpt").combogrid({
        url: 'sel-danh-muc-vi-tri-thu-thuat-phau-thuat-search?tamNgung=0',
        debug: true,
        width: "600px",
        colModel: [
            {'columnName': 'ID', 'label': 'ID', hidden: true},
            {'columnName': 'MA', 'label': 'Mã', 'width': '20'},
            {'columnName': 'TEN', 'label': 'Tên', 'width': '20'},
            {'columnName': 'GHI_CHU', 'label': 'Ghi Chú', 'width': '20'}
        ],
        select: function (event, ui) {
            $("#phauthuat_vitrittpt").val(ui.item.TEN);
            $("#phauthuat_vitrittpt_ma").val(ui.item.MA);
            $("#phauthuat_vitrittpt_ten").val(ui.item.TEN);
            return false;
        }
    });
    $("#phauthuat_tuongtrinh_icd").keypress(function(e) {
        var icd = $(this).val();
        if(e.keyCode == 13 && icd.trim() != "") {
            getMotabenhly(icd.toUpperCase(), function(data) {
                var splitIcd = data.split("!!!");
                var icdCur = $("#phauthuat_tuongtrinh_tenicd").val().trim();
                if(!icdCur.includes("("+icd.toUpperCase()+")")) {
                    var mabenh = "("+ icd.toUpperCase() + ") " + splitIcd[1];
                    $("#phauthuat_tuongtrinh_tenicd").val(icdCur == ''? mabenh: icdCur + "; " + mabenh);
                }
                $("#phauthuat_tuongtrinh_icd").val("")
            })
        }
    })
    $("#phauthuat_tuongtrinh_icd_truoc").keypress(function(e) {
        var icd = $(this).val();
        if(e.keyCode == 13 && icd.trim() != "") {
            getMotabenhly(icd.toUpperCase(), function(data) {
                var splitIcd = data.split("!!!");
                var icdCur = $("#phauthuat_tuongtrinh_tenicd_truoc").val().trim();
                if(!icdCur.includes("("+icd.toUpperCase()+")")) {
                    var mabenh = "("+ icd.toUpperCase() + ") " + splitIcd[1];
                    $("#phauthuat_tuongtrinh_tenicd_truoc").val(icdCur == ''? mabenh: icdCur + "; " + mabenh);
                }
                $("#phauthuat_tuongtrinh_icd_truoc").val("")
            })
        }
    })
    $("#modalPhauThuatTuongtrinhPT").on('show.bs.modal', function() {
        loadGayMeHoiSuc();
        initGridDSEkip();
        initGridDSHinhanh()
        loadDSEkipView()
        loadDSHinhanh()
        loadEditor("phauthuat_tuongtrinh_noidung")
        if(!painterObject) {
            painterObject = vehinhanhbenhan('',idContainer, idCanvas);
        }
        $("#phauthuat_tuongtrinh_icd").val("")
        $("#phauthuat_tuongtrinh_stthinhanh").val("")
        var rowSelected = lanPhauThuatObject.CHITIETDVKT;
        getThongtinTTPT(rowSelected, function(result) {
            $("#phauthuatTuongtrinhForm .show-created").hide();
            var data = {
                ...result[0],
                EKIP: result[0].TINHTIENEKIP
            }
            if(data.CACHTHUCPHAUTHUAT) {
                $("#phauthuat_tuongtrinh_cachthupt").val(data.CACHTHUCPHAUTHUAT)
            }
            Object.keys(data).forEach(function(key) {
                if(key != 'TRINHTU_TT_PT') {
                    $("#phauthuatTuongtrinhForm [name='"+key+"']").val(data[key])
                    if (data.LOAI_TUONGTRINH == 'phieuphauthuat') {
                        if(key == 'TENCHANDOAN_TRUOCPT' && data['DA_CHAN_DOAN'] == 0) {
                            $("#phauthuat_tuongtrinh_tenicd_truoc").val(lanPhauThuatObject.CHAN_DOAN)
                        }
                        if(key == 'DA_CHAN_DOAN') {
                            if (data[key] == 0) {
                                $("#lanphauthuat_tuongtrinhpt_xoa").hide();
                                $("#lanphauthuat_tuongtrinhpt_xem").hide();
                                $("#lanphauthuat_tuongtrinhpt_kyso").hide();
                                $("#lanphauthuat_tuongtrinhpt_huykyso").hide();

                            } else {
                                $("#lanphauthuat_tuongtrinhpt_xoa").show();
                                $("#lanphauthuat_tuongtrinhpt_xem").show();
                                $("#phauthuatTuongtrinhForm .show-created").show();
                            }
                        }
                    }
                } else {
                    if (data.LOAI_TUONGTRINH == 'phieuphauthuat') {
                        CKEDITOR.instances['phauthuat_tuongtrinh_noidung'].setData(data[key])
                    }
                }
            })
            if (data.LOAI_TUONGTRINH == 'phieuphauthuat') {
                $("#phauthuatTuongtrinhForm .show-created").hide();

            } else {
                $("#phauthuat_tuongtrinh_tennguoithuchien").val(result[0].TENNGUOITHUCHIEN)
                createFormTrinhTuPhauThuatKhacFormIO(result[0])
            }
            $.get("cmu_getlist?url="+convertArray([singletonObject.dvtt,
                rowSelected.SO_PHIEU_DICHVU,
                thongtinhsba.thongtinbn.SOVAOVIEN, 'CMU_GETALL_PPPT_SOPHIEU'])).done(function(listPPPT) {
                $("#phauthuat_tuongtrinh_pppt").empty();
                var list =  listPPPT.map(function(item) {
                    return {
                        ID: item.TEN_DV,
                        LABEL: item.TEN_DV
                    }
                })
                initSelect2IfnotIntance("phauthuat_tuongtrinh_pppt", list,
                    "ID", "LABEL", false, false,
                    data['PHUONGPHAP_TT_PT']? data['PHUONGPHAP_TT_PT'].split(";"): [], true)
            })
            if (result[0].DA_CHAN_DOAN == 1) {
                checkKysoPTTT();
                // getFilesign769(
                //     "PHIEU_NOITRU_TRUONGTRINHPT",
                //     rowSelected.MA_DV,
                //     -1,//singletonObject.userId,
                //     singletonObject.dvtt,
                //     thongtinhsba.thongtinbn.SOVAOVIEN,
                //     thongtinhsba.thongtinbn.SOVAOVIEN_DT,
                //     -1,
                //     function(data) {
                //         if(data.length > 0) {
                //             var res = data[0];
                //             if(res.KEYSIGN) {
                //                 $("#lanphauthuat_tuongtrinhpt_kyso").hide()
                //                 $("#lanphauthuat_tuongtrinhpt_luu").hide()
                //                 $("#lanphauthuat_tuongtrinhpt_xoa").hide()
                //                 $("#lanphauthuat_tuongtrinhpt_huykyso").show()
                //             } else {
                //                 $("#lanphauthuat_tuongtrinhpt_kyso").show()
                //                 $("#lanphauthuat_tuongtrinhpt_luu").show()
                //                 $("#lanphauthuat_tuongtrinhpt_xoa").show()
                //                 $("#lanphauthuat_tuongtrinhpt_huykyso").hide()
                //             }
                //         } else {
                //             $("#lanphauthuat_tuongtrinhpt_kyso").show()
                //             $("#lanphauthuat_tuongtrinhpt_luu").show()
                //             $("#lanphauthuat_tuongtrinhpt_xoa").show()
                //             $("#lanphauthuat_tuongtrinhpt_huykyso").hide()
                //         }
                //     }
                // )
            }else {
                $("#lanphauthuat_tuongtrinhpt_luu").show()
            }
            $("#phauthuat_tuongtrinh_loaituongtrinh").val(result[0].LOAI_TUONGTRINH).change();
        })
        getFilesign769(
            "PHIEU_NOITRU_GIAYCHUNGNHAN_PTV",
            lanPhauThuatObject.CHITIETDVKT.SO_PHIEU_DICHVU,
            -1,//singletonObject.userId,
            singletonObject.dvtt,
            thongtinhsba.thongtinbn.SOVAOVIEN,
            thongtinhsba.thongtinbn.SOVAOVIEN_DT,
            -1,
            function(data) {
                if(data.length > 0) {
                    $("#lanphauthuat_tuongtrinhpt_kysochungnhan_ptv").hide();
                    $("#lanphauthuat_tuongtrinhpt_huykysochungnhan_ptv").show();
                } else {
                    $("#lanphauthuat_tuongtrinhpt_kysochungnhan_ptv").show();
                    $("#lanphauthuat_tuongtrinhpt_huykysochungnhan_ptv").hide();
                }
            }
        )
        $.get("cmu_list_DANHSACH_MAYTTPT_DMTTPT?url="+convertArray([singletonObject.dvtt])).done(function(data) {
            $("#phauthuat_sttMayTTPT").empty()
            $("#phauthuat_sttMayTTPT").append("<option value=''>Không sử dụng</option>")
            data.forEach(function(obj){
                $("#phauthuat_sttMayTTPT").append("<option value='"+obj.STT+"'>"+ obj.TEN_MAY +"</option>")
            })
            $("#phauthuat_sttMayTTPT").select2();
        })
    })

    $("#modalPhauThuatTuongtrinhPT").on('hide.bs.modal', function() {
        $("#lanphauthuat_dvkt_tdt_huyttpt").click();
        lanPhauThuatObject['CHITIETDVKT'] = {};
    })

    $("#modalPhauThuatEKIPTuongtrinhPT").on('hide.bs.modal', function() {
        $("#lanphauthuat_dvkt_tdt_huyttpt").click();
    })

    $("#lanphauthuat_tuongtrinhpt_xem").click(function() {
        // var rowSelected = getThongtinRowSelected("lanphauthuat_dvkt_list_phieuttpt")
        var ret = lanPhauThuatObject.CHITIETDVKT
        var tenpt;
        if ($("#phauthuat_tuongtrinh_loaituongtrinh").val() == 'phauthuatmong'){
            tenpt = "_MONG"
        } else if ($("#phauthuat_tuongtrinh_loaituongtrinh").val() == 'phauthuatsupmi'){
            tenpt = "_SUPMI"
        } else if ($("#phauthuat_tuongtrinh_loaituongtrinh").val() == 'phauthuattuile'){
            tenpt = "_TUILE"
        } else if ($("#phauthuat_tuongtrinh_loaituongtrinh").val() == 'phauthuatsapejko'){
            tenpt = "_SAPEJKO"
        } else if ($("#phauthuat_tuongtrinh_loaituongtrinh").val() == 'phauthuatlac'){
            tenpt = "_LAC"
        } else if ($("#phauthuat_tuongtrinh_loaituongtrinh").val() == 'phauthuatglocom'){
            tenpt = "_GLOCOM"
        } else if ($("#phauthuat_tuongtrinh_loaituongtrinh").val() == 'phauthuatbematnhancau'){
            tenpt = "_BEMATNHANCAU"
        } else if ($("#phauthuat_tuongtrinh_loaituongtrinh").val() == 'phauthuatghepgiacmac'){
            tenpt = "_GHEPGIACMAC"
        } else if ($("#phauthuat_tuongtrinh_loaituongtrinh").val() == 'thethuytinh'){
            tenpt = "_THETHUYTINH"
        } else {
            tenpt = ""
        }
        getFilesign769(
            "PHIEU_NOITRU_TRUONGTRINHPT" + tenpt,
            ret.MA_DV,
            -1,//singletonObject.userId,
            singletonObject.dvtt,
            thongtinhsba.thongtinbn.SOVAOVIEN,
            thongtinhsba.thongtinbn.SOVAOVIEN_DT,
            -1,
            function(data) {
                if(data.length > 0) {
                    getCMUFileSigned769(data[0].KEYMINIO,"pdf")
                } else {
                    var ret = lanPhauThuatObject.CHITIETDVKT
                    if ($("#phauthuat_tuongtrinh_loaituongtrinh").val() == 'phieuphauthuat') {
                        var arr = [
                            ret.MA_DV,
                            ret.SO_PHIEU_DICHVU,
                            '',
                            singletonObject.dvtt,
                            1,
                            thongtinhsba.thongtinbn.STT_BENHAN,
                            lanPhauThuatTDTObject.STT_DOTDIEUTRI,
                            ret.STT_DIEUTRI,
                            ret.SOVAOVIEN,
                            thongtinhsba.thongtinbn.TEN_BENH_NHAN,
                            thongtinhsba.thongtinbn.TUOI,
                            thongtinhsba.thongtinbn.GIOI_TINH_HT,
                            "0"];
                        var url = "inphieuthuthuatphauthuat_hinhmau?url=" + convertArray(arr)+"&&typefile=pdf";
                        previewPdfDefaultModal(url, 'frame-intuongtrinh')
                    } else {
                        var chuoirp;
                        if ($("#phauthuat_tuongtrinh_loaituongtrinh").val() == 'phauthuatmong'){
                            chuoirp = "emr_rp_phieuphauthuat_mong_cmu"
                        } else if ($("#phauthuat_tuongtrinh_loaituongtrinh").val() == 'phauthuatsupmi'){
                            chuoirp = "emr_rp_phieuphauthuat_supmi_cmu"
                        } else if ($("#phauthuat_tuongtrinh_loaituongtrinh").val() == 'phauthuattuile'){
                            chuoirp = "emr_rp_phieuphauthuat_tuile_cmu"
                        } else if ($("#phauthuat_tuongtrinh_loaituongtrinh").val() == 'phauthuatsapejko'){
                            chuoirp = "emr_rp_phieuphauthuat_sapejko_cmu"
                        } else if ($("#phauthuat_tuongtrinh_loaituongtrinh").val() == 'phauthuatlac'){
                            chuoirp = "emr_rp_phieuphauthuat_lac_cmu"
                        } else if ($("#phauthuat_tuongtrinh_loaituongtrinh").val() == 'phauthuatglocom'){
                            chuoirp = "emr_rp_phieuphauthuat_glocom_cmu"
                        } else if ($("#phauthuat_tuongtrinh_loaituongtrinh").val() == 'phauthuatbematnhancau'){
                            chuoirp = "emr_rp_phieuphauthuat_nhancau_cmu"
                        } else if ($("#phauthuat_tuongtrinh_loaituongtrinh").val() == 'phauthuatghepgiacmac'){
                            chuoirp = "emr_rp_phieuphauthuat_ghepgiacmac_cmu"
                        } else {
                            chuoirp = "emr_rp_phieuphauthuat_thuthuat_cmu"
                        }
                        var arr = [ret.SO_PHIEU_DICHVU, singletonObject.dvtt, ret.MA_DV, 1, '',
                            thongtinhsba.thongtinbn.STT_BENHAN, lanPhauThuatTDTObject.STT_DOTDIEUTRI, ret.STT_DIEUTRI];
                        var param  = ["sophieudichvu", "dvtt", "madv", "noitru", "makhambenh", "sttbenhan", "sttdotdieutri", "sttdieutri"];
                        var url = "cmu_report_" + chuoirp + "?url=" + convertArray(arr)+"&param="+ convertArray(param)+"&loaifile=pdf";
                        previewPdfDefaultModal(url, 'td_frame_inphieutruyendich');
                    }
                }
            }
        )
    });

    $("#lanphauthuat_tuongtrinhpt_xoa").click(function() {
        var idButton = this.id;
        var ret = lanPhauThuatObject.CHITIETDVKT
        if($("#phauthuat_tuongtrinh_nguoithuchien").val() != singletonObject.userId) {
            return notifiToClient("Red", MESSAGEAJAX.PERMISSION)
        }
        confirmToClient(MESSAGEAJAX.CONFIRM, function() {
            showSelfLoading(idButton)
            $.post("cmu_post_CMU_HUYKETQUA_TTPT_F_V2",{
                url: [singletonObject.dvtt, ret.SO_PHIEU_DICHVU, ret.MA_DV,
                    thongtinhsba.thongtinbn.STT_BENHAN,
                    lanPhauThuatTDTObject.STT_DOTDIEUTRI,
                    ret.STT_DIEUTRI,
                    lanPhauThuatTDTObject.SOVAOVIEN
                ].join("```")
            }).done(function() {
                notifiToClient("Green", "Xóa thành công!")
                var arr1 = [singletonObject.dvtt, "Hủy kết quả TTPT-VLTL cho bệnh nhân " +
                thongtinhsba.thongtinbn.TEN_BENH_NHAN + " với phiếu TTPT-VLTL "
                + ret.SO_PHIEU_DICHVU + " - MA DV:" + ret.MA_DV, singletonObject.userId + "-" + singletonObject.user, "Hủy kết quả TTPT-VLTL"];
                $.post("lichsusudung_insert", {url: convertArray(arr1)});
                $("#lanphauthuat_tuongtrinhpt_xoa").hide();
                $("#lanphauthuat_tuongtrinhpt_xem").hide();
                $("#lanphauthuat_tuongtrinhpt_kyso").hide();
                $("#phauthuat_tuongtrinh_nguoithuchien").val("")
                $("#phauthuatTuongtrinhForm .show-created").hide();
                $.ajax({
                    url:"cmu_post_CMU_IMAGES_DVKT_DELALL",
                    method: "POST",
                    data: {
                        url: [singletonObject.dvtt, thongtinhsba.thongtinbn.SOVAOVIEN, ret.SO_PHIEU_DICHVU, ret.MA_DV].join("```")
                    },
                    async: false
                })
                loadDSHinhanh();

            }).fail(function() {
                notifiToClient("Red", MESSAGEAJAX.ERROR)
            }).always(function () {
                hideSelfLoading(idButton)

            });
        })
    })
    $("#phauthuat_tuongtrinh_file").change(function(e) {
        var file = e.target.files[0];
        var fileUrl = URL.createObjectURL(file);
        painterObject.changeImageSrc(fileUrl, idCanvas);
    })
    $("#lanphauthuat_dvkt_ttpt_dropdown p").click(function() {
        var attrId = $(this).attr('data-id');
        var idWrap = 'lanphauthuat-dvkt-tab'
        if(attrId == 'xoaphieu') {
            confirmToClient("Bạn có chắc sẽ xóa phiếu chỉ định này?", function() {

                showLoaderIntoWrapId(idWrap);
                var ret = getThongtinRowSelected("lanphauthuat_dvkt_list_phieuttpt")
                var dataBN = thongtinhsba.thongtinbn;
                if(checkThanhtoanTTPT(ret.SO_PHIEU_DICHVU, dataBN) == 1) {
                    hideLoaderIntoWrapId(idWrap);
                    return notifiToClient("Red", "Phiếu xét nghiệm đã được thanh toán, không thể cập nhật.")
                }
                var url = "noitru_ttpt_delete_bangcha_svv";
                var arr = [ ret.SO_PHIEU_DICHVU, singletonObject.dvtt,
                    ret.STT_DIEUTRI, dataBN.STT_BENHAN, dataBN.STT_DOTDIEUTRI, ret.SOVAOVIEN, ret.SOVAOVIEN_DT];
                $.post(url, {
                    url: convertArray(arr)
                }).done(function (data) {
                    if (data == "1") {
                        return notifiToClient("Red","Bệnh nhân đã thanh toán");
                    }
                    else if (data == "2") {
                        return notifiToClient("Red","Bệnh nhân đã được thực hiện thủ thuật phẫu thuật");
                    } else if (data == "3") {
                        return notifiToClient("Red","Bệnh nhân đã được nhập kết quả thủ thuật phẫu thuật");
                    }
                    else {
                        notifiToClient("Green","Xóa thành công");
                        loadGridPhauthuatDVKT(" ", dataBN);
                        loadGridPhauthuatDVKTCDPT()
                        enableModeButtonPhauthuatDVKT('add')
                    }
                    $("#lanphauthuat_dvkt_ttpt_hinhthuc").prop("disabled", false);
                    $("#lanphauthuat_dvkt_ttpt_phongttpt").prop("disabled", false);
                    $("#lanphauthuat_dvkt_ttpt_loaittpt").prop("disabled", false);
                }).always(function() {
                    hideLoaderIntoWrapId(idWrap);
                }).fail(function() {
                    notifiToClient("Red", MESSAGEAJAX.ERROR);
                });
            }, function () {

            })

        }
        if(attrId == 'xemphieu') {
            var ret = getThongtinRowSelected("lanphauthuat_dvkt_list_phieuttpt")
            getFilesign769(
                "PHIEUCD_NOITRU_TTPT",
                ret.SO_PHIEU_DICHVU,
                singletonObject.userId,
                singletonObject.dvtt,
                thongtinhsba.thongtinbn.SOVAOVIEN,
                todieutriObject.SOVAOVIEN_DT ,
                -1,
                function(data) {
                    if(data.length > 0) {
                        getCMUFileSigned769(data[0].KEYMINIO,"pdf")
                    } else {
                        var tongtiencls = $.ajax({
                            url: 'cmu_post',
                            method: 'post',
                            data: {
                                url:   [singletonObject.dvtt, todieutriObject.SOVAOVIEN, todieutriObject.SOVAOVIEN_DT, ret.SO_PHIEU_DICHVU, 'CMU_TONGTIEN_CLSNNOI'].join("```")
                            },
                            dataType: 'json',
                            async: false
                        }).responseText;
                        if(tongtiencls > 0) {

                            getQrCodeBank(singletonObject.dvtt, thongtinhsba.thongtinbn.MA_BENH_NHAN, todieutriObject.SOVAOVIEN, todieutriObject.SOVAOVIEN_DT, ret.SO_PHIEU_DICHVU, tongtiencls, 'VIETINBANK');
                            getQrCodeBank(singletonObject.dvtt, thongtinhsba.thongtinbn.MA_BENH_NHAN, todieutriObject.SOVAOVIEN, todieutriObject.SOVAOVIEN_DT, ret.SO_PHIEU_DICHVU, tongtiencls, 'BIDV');
                        }
                        previewPdfDefaultModal(getURLPrintTTPT("lanphauthuat_dvkt_list_phieuttpt", lanPhauThuatTDTObject), "previewtdt_phauthuat_phieuttpt");
                    }
                })
        }
        if(attrId == 'huykyso') {
            confirmToClient("Bạn có chắc sẽ hủy ký số chỉ định này?", function() {
                showLoaderIntoWrapId(idWrap);
                var ret = getThongtinRowSelected("lanphauthuat_dvkt_list_phieuttpt")
                huykysoFilesign769("PHIEUCD_NOITRU_TTPT",
                    ret.SO_PHIEU_DICHVU, singletonObject.userId, singletonObject.dvtt,  todieutriObject.SOVAOVIEN, todieutriObject.SOVAOVIEN_DT, -1, function() {
                        hideLoaderIntoWrapId(idWrap);
                    })
            }, function () {

            })
        }
    })
    $("#phauthuat_tuongtrinh_dsekip").change(function() {
        dsEkipByID = []
        $.get("cmu_getlist?url="+convertArray([singletonObject.dvtt, this.value,'CMU_LANPHAUTHUAT_EKIP_BYID'])).done(function( data ) {
            dsEkipByID  = data;
            $("#phauthuat_tuongtrinh_ekip_nhanvien").empty();
            var object = {
                BSPHAUTHUAT: "Bác sĩ phẫu thuật",
                BSGAYME: "Bác sĩ gây mê/tê",
                KTVGAYMETE: "KTV gây mê/tê",
                PHUMO1: "Phụ mổ 1",
                PHUMO2: "Phụ mổ 2",
                PHUMO3: "Phụ mổ 3",
                PHUMO4: "Phụ mổ 4",
                DUNGCU1: "Dụng cụ 1",
                DUNGCU2: "Dụng cụ 2"
            }
            data.forEach(function(item, index) {
                $("#phauthuat_tuongtrinh_ekip_nhanvien").append("<option "+(index == 0? "selected":"" )+" value='"+item.ID+"'>"+item.TENNHANVIEN + "- " + object[item.VITRI]+"</option>");
            })
        })
    })
    $("#phauthuat_tuongtrinh_themekip").click(function() {
        var dataSelect = _.find(dsEkipByID, { 'ID': Number($("#phauthuat_tuongtrinh_ekip_nhanvien").val()) })
        console.log("dsEkipByID", dsEkipByID)
        if(dataSelect) {
            var allData = getAllRowDataJqgrid("phauthuat_tuongtrinh_ekipct")
            var list =  $("#phauthuat_tuongtrinh_ekipct")
            var existVITRI = _.find(allData, { 'VITRI': dataSelect.VITRI })
            var existNhanvien = _.find(allData, { 'MANHANVIEN': dataSelect.MANHANVIEN })
            if(existVITRI || existNhanvien) {
                return notifiToClient("Red", "Ekip này đã tồn tại")
            }
            allData.push({
                ID: 0,
                MANHANVIEN: dataSelect.MANHANVIEN,
                TENNHANVIEN: dataSelect.TENNHANVIEN,
                VITRI: dataSelect.VITRI
            })
            list.jqGrid('setGridParam', { data: allData});
            list[0].grid.endReq();
            list.trigger('reloadGrid');
        } else {
            notifiToClient("Red", "Vui lòng  chọn nhân viên")
        }
    })
    $("#phauthuat_tuongtrinh_copyekip").click(function() {
        var list =  $("#phauthuat_tuongtrinh_ekipct")
        var allData = []
        dsEkipByID.forEach(function(dataSelect) {
            allData.push({
                ID: 0,
                MANHANVIEN: dataSelect.MANHANVIEN,
                TENNHANVIEN: dataSelect.TENNHANVIEN,
                VITRI: dataSelect.VITRI
            })
        })
        list.jqGrid('setGridParam', { data: allData});
        list[0].grid.endReq();
        list.trigger('reloadGrid');
    })
    $("#phauthuat_tuongtrinh_themhinhanh").click(function () {
        $("#modalPhauThuatTuongtrinhPTHinhanh").modal("show");
        addTextTitleModal("titleModalPhauThuatTuongtrinhPTHinhanh", "Thêm hình ảnh")
        $("#phauthuat_tuongtrinh_sttfile").val("")
    })

    $("#lanphauthuat_dvkt_ttpt_loaittpt").change(function() {
        loadGridPhauthuatDVKT("", thongtinhsba.thongtinbn)
    })
    $("#lanphauthuat_tuongtrinhpt_luuekip_dvkt").click(function() {
        var idButton = this.id;
        var allEkip = getAllRowDataJqgrid("phauthuat_tuongtrinh_ekipct")
        if(allEkip.length == 0) {
            return notifiToClient("Red", "Vui lòng nhập ekip")
        }
        var ekipArr = []
        var objectEkip = {};
        allEkip.forEach(function(item) {
            objectEkip[item.VITRI] = item.MANHANVIEN;
            objectEkip[item.VITRI+"_TEN"] = item.TENNHANVIEN;
            ekipArr.push({
                VITRI: item.VITRI,
                MANHANVIEN: item.MANHANVIEN
            })
        })
        if(!objectEkip.BSPHAUTHUAT) {
            return notifiToClient("Red", "Vui lòng chọn bác sĩ phẫu thuật")
        }

        var ekipBSPT =
            _.get(objectEkip,'BSPHAUTHUAT_TEN', '')+":"+
            _.get(objectEkip,'PHUMO1_TEN', '') + (_.get(objectEkip,'PHUMO2_TEN', '')? ","+_.get(objectEkip,'PHUMO2_TEN', ''): "")+":"+
            _.get(objectEkip,'PHUMO3_TEN', '')+ (_.get(objectEkip,'PHUMO4_TEN', '')? ","+_.get(objectEkip,'PHUMO4_TEN', ''): "");
        var ekipBSGM = _.get(objectEkip,'BSGAYME_TEN', '')+":"+ _.get(objectEkip,'KTVGAYMETE_TEN', '')+ ":"+ _.get(objectEkip,'DUNGCU1_TEN', '')+":"+ _.get(objectEkip,'DUNGCU2_TEN', '');
        showSelfLoading(idButton)
        if($("#phauthuat_tuongtrinh_tinhtienekip").val() == 0) {
            confirmToClient("Bạn có chắc không tính tiền ekip?", function() {
                luuEkipTuongtrinhTTPT(idButton, ekipBSPT, ekipBSGM, objectEkip, ekipArr)
            }, function() {
                hideSelfLoading(idButton)
            })
        } else {
            luuEkipTuongtrinhTTPT(idButton, ekipBSPT, ekipBSGM, objectEkip, ekipArr)
        }
    })
    $("#lanphauthuat_tuongtrinhpt_xoaekip_dvkt").click(function() {
        var idButton = this.id
        showSelfLoading(idButton)
        var rowSelected = getThongtinRowSelected("lanphauthuat_dvkt_list_phieuttpt");
        getThongtinTTPT(rowSelected, function(result) {
            if(result.length > 0 && result[0].DA_CHAN_DOAN == 1) {
                hideSelfLoading(idButton)
                return notifiToClient("Red", "Đã thực hiện tường trình, không thể xóa ekip")
            }
            confirmToClient(MESSAGEAJAX.CONFIRM, function () {
                $.post('cmu_post_CMU_TTPT_EKIP_DEL_V2', {
                    url: [
                        rowSelected.SO_PHIEU_DICHVU,
                        singletonObject.dvtt,
                        rowSelected.MA_DV,
                        thongtinhsba.thongtinbn.STT_BENHAN,
                        lanPhauThuatTDTObject.STT_DOTDIEUTRI,
                        lanPhauThuatTDTObject.STT_DIEUTRI,
                        lanPhauThuatTDTObject.SOVAOVIEN,
                        lanPhauThuatTDTObject.SOVAOVIEN_DT
                    ].join('```')
                }).done(function (dt) {
                    if (dt > 0) {
                        notifiToClient("Green", MESSAGEAJAX.DEL_SUCCESS)
                        loadDSEkip();
                        $("#"+idButton).hide()
                    } else {
                        notifiToClient("Red", MESSAGEAJAX.ERROR)
                    }
                }).fail(function () {
                    notifiToClient("Red", MESSAGEAJAX.ERROR)
                }).always(function() {
                    hideSelfLoading(idButton)
                })
            }, function () {
                hideSelfLoading(idButton)
            })
        })

    })

    $("#lanphauthuat_tuongtrinhpt_kyso").click(function() {
        var rowSelected = getThongtinRowSelected("lanphauthuat_dvkt_list_phieuttpt") || getThongtinRowSelected("list_ttpt")
        if(!rowSelected.MA_DV) {
            rowSelected = getThongtinRowSelected("list_ttpt")
        }
        var ret = lanPhauThuatObject.CHITIETDVKT
        var url;
        var tenpt = "";
        var keyword = "";
        // var arr = [
        //     ret.MA_DV,
        //     ret.SO_PHIEU_DICHVU,
        //     '',
        //     singletonObject.dvtt,
        //     1,
        //     thongtinhsba.thongtinbn.STT_BENHAN,
        //     lanPhauThuatTDTObject.STT_DOTDIEUTRI,
        //     ret.STT_DIEUTRI,
        //     ret.SOVAOVIEN,
        //     thongtinhsba.thongtinbn.TEN_BENH_NHAN,
        //     thongtinhsba.thongtinbn.TUOI,
        //     thongtinhsba.thongtinbn.GIOI_TINH_HT,
        //     "0"];
        // var url = "inphieuthuthuatphauthuat_hinhmau?url=" + convertArray(arr)+"&&typefile=pdf";
        // kySoChung({
        //     dvtt: singletonObject.dvtt,
        //     userId: singletonObject.userId,
        //     url: url,
        //     loaiGiay: "PHIEU_NOITRU_TRUONGTRINHPT",
        //     maBenhNhan: thongtinhsba.thongtinbn.MA_BENH_NHAN,
        //     soBenhAn: thongtinhsba.thongtinbn.STT_BENHAN,
        //     soPhieuDichVu: rowSelected.MA_DV,
        //     // nghiepVu: "",
        //     fileName:  'Tường trình phẫu thuật: '+ thongtinhsba.thongtinbn.TEN_BENH_NHAN + " - "+ rowSelected.MA_DV,
        //     soVaoVien: thongtinhsba.thongtinbn.SOVAOVIEN,
        //     soVaoVienDT: thongtinhsba.thongtinbn.SOVAOVIEN_DT,
        //     keyword: "PHẪU THUẬT/THỦ THUẬT VIÊN",
        // }, function(dataKySo) {
        //     $("#lanphauthuat_tuongtrinhpt_kyso").hide()
        //     $("#lanphauthuat_tuongtrinhpt_luu").hide()
        //     $("#lanphauthuat_tuongtrinhpt_xoa").hide()
        //     $("#lanphauthuat_tuongtrinhpt_huykyso").show()
        // });
        if ($("#phauthuat_tuongtrinh_loaituongtrinh").val() == 'phieuphauthuat') {
            var arr = [
                ret.MA_DV,
                ret.SO_PHIEU_DICHVU,
                '',
                singletonObject.dvtt,
                1,
                thongtinhsba.thongtinbn.STT_BENHAN,
                lanPhauThuatTDTObject.STT_DOTDIEUTRI,
                ret.STT_DIEUTRI,
                ret.SOVAOVIEN,
                thongtinhsba.thongtinbn.TEN_BENH_NHAN,
                thongtinhsba.thongtinbn.TUOI,
                thongtinhsba.thongtinbn.GIOI_TINH_HT,
                "0"];
            keyword = "PHẪU THUẬT/THỦ THUẬT VIÊN"
            url = "inphieuthuthuatphauthuat_hinhmau?url=" + convertArray(arr)+"&&typefile=pdf";
        } else {
            var chuoirp;
            if ($("#phauthuat_tuongtrinh_loaituongtrinh").val() == 'phauthuatmong'){
                chuoirp = "emr_rp_phieuphauthuat_mong_cmu"
                tenpt = "_MONG"
            } else if ($("#phauthuat_tuongtrinh_loaituongtrinh").val() == 'phauthuatsupmi'){
                chuoirp = "emr_rp_phieuphauthuat_supmi_cmu"
                tenpt = "_SUPMI"
            } else if ($("#phauthuat_tuongtrinh_loaituongtrinh").val() == 'phauthuattuile'){
                chuoirp = "emr_rp_phieuphauthuat_tuile_cmu"
                tenpt = "_TUILE"
            } else if ($("#phauthuat_tuongtrinh_loaituongtrinh").val() == 'phauthuatsapejko'){
                chuoirp = "emr_rp_phieuphauthuat_sapejko_cmu"
                tenpt = "_SAPEJKO"
            } else if ($("#phauthuat_tuongtrinh_loaituongtrinh").val() == 'phauthuatlac'){
                chuoirp = "emr_rp_phieuphauthuat_lac_cmu"
                tenpt = "_LAC"
            } else if ($("#phauthuat_tuongtrinh_loaituongtrinh").val() == 'phauthuatglocom'){
                chuoirp = "emr_rp_phieuphauthuat_glocom_cmu"
                tenpt = "_GLOCOM"
            } else if ($("#phauthuat_tuongtrinh_loaituongtrinh").val() == 'phauthuatbematnhancau'){
                chuoirp = "emr_rp_phieuphauthuat_nhancau_cmu"
                tenpt = "_BEMATNHANCAU"
            } else if ($("#phauthuat_tuongtrinh_loaituongtrinh").val() == 'phauthuatghepgiacmac'){
                chuoirp = "emr_rp_phieuphauthuat_ghepgiacmac_cmu"
                tenpt = "_GHEPGIACMAC"
            } else {
                chuoirp = "emr_rp_phieuphauthuat_thuthuat_cmu"
                tenpt = "_THETHUYTINH"
            }
            var arr = [ret.SO_PHIEU_DICHVU, singletonObject.dvtt, ret.MA_DV, 1, '',
                thongtinhsba.thongtinbn.STT_BENHAN, lanPhauThuatTDTObject.STT_DOTDIEUTRI, ret.STT_DIEUTRI];
            var param  = ["sophieudichvu", "dvtt", "madv", "noitru", "makhambenh", "sttbenhan", "sttdotdieutri", "sttdieutri"];
            url = "cmu_report_" + chuoirp + "?url=" + convertArray(arr)+"&param="+ convertArray(param)+"&loaifile=pdf";
            keyword = "PHẪU THUẬT VIÊN"
        }
        kySoChung({
            dvtt: singletonObject.dvtt,
            userId: singletonObject.userId,
            url: url,
            loaiGiay: "PHIEU_NOITRU_TRUONGTRINHPT" + tenpt,
            maBenhNhan: thongtinhsba.thongtinbn.MA_BENH_NHAN,
            soBenhAn: thongtinhsba.thongtinbn.STT_BENHAN,
            soPhieuDichVu: ret.MA_DV,
            fileName:  'Tường trình phẫu thuật: '+ thongtinhsba.thongtinbn.TEN_BENH_NHAN + " - "+ rowSelected.MA_DV,
            // nghiepVu: "",
            soVaoVien: thongtinhsba.thongtinbn.SOVAOVIEN,
            soVaoVienDT: thongtinhsba.thongtinbn.SOVAOVIEN_DT,
            keyword: keyword,
        }, function(dataKySo) {
            checkKysoPTTT();
        });
    })
    $("#lanphauthuat_tuongtrinhpt_huykyso").click(function() {
        var idButton = this.id
        // var rowSelected = getThongtinRowSelected("lanphauthuat_dvkt_list_phieuttpt")
        // showSelfLoading(idButton);
        var ret = lanPhauThuatObject.CHITIETDVKT
        var tenpt;
        if ($("#phauthuat_tuongtrinh_loaituongtrinh").val() == 'phauthuatmong'){
            tenpt = "_MONG"
        } else if ($("#phauthuat_tuongtrinh_loaituongtrinh").val() == 'phauthuatsupmi'){
            tenpt = "_SUPMI"
        } else if ($("#phauthuat_tuongtrinh_loaituongtrinh").val() == 'phauthuattuile'){
            tenpt = "_TUILE"
        } else if ($("#phauthuat_tuongtrinh_loaituongtrinh").val() == 'phauthuatsapejko'){
            tenpt = "_SAPEJKO"
        } else if ($("#phauthuat_tuongtrinh_loaituongtrinh").val() == 'phauthuatlac'){
            tenpt = "_LAC"
        } else if ($("#phauthuat_tuongtrinh_loaituongtrinh").val() == 'phauthuatglocom'){
            tenpt = "_GLOCOM"
        } else if ($("#phauthuat_tuongtrinh_loaituongtrinh").val() == 'phauthuatbematnhancau'){
            tenpt = "_BEMATNHANCAU"
        } else if ($("#phauthuat_tuongtrinh_loaituongtrinh").val() == 'phauthuatghepgiacmac'){
            tenpt = "_GHEPGIACMAC"
        } else if ($("#phauthuat_tuongtrinh_loaituongtrinh").val() == 'thethuytinh'){
            tenpt = "_THETHUYTINH"
        } else {
            tenpt = ""
        }
        confirmToClient("Bạn có chắc sẽ hủy ký số phiếu này?", function() {
            huykysoFilesign769("PHIEU_NOITRU_TRUONGTRINHPT" + tenpt,
                ret.MA_DV, singletonObject.userId, singletonObject.dvtt,  thongtinhsba.thongtinbn.SOVAOVIEN, thongtinhsba.thongtinbn.SOVAOVIEN_DT, -1, function(data) {
                    // hideSelfLoading(idButton);
                    if(data && data.ERROR) {
                        notifiToClient("Red", "Hủy ký số thất bại");
                    } else {
                        checkKysoPTTT();
                    }

                })
        }, function () {

        })
        // confirmToClient("Bạn có chắc sẽ hủy ký số phiếu này?", function() {
        //     huykysoFilesign769("PHIEU_NOITRU_TRUONGTRINHPT",
        //         rowSelected.MA_DV, singletonObject.userId, singletonObject.dvtt,  thongtinhsba.thongtinbn.SOVAOVIEN, thongtinhsba.thongtinbn.SOVAOVIEN_DT, -1, function(data) {
        //             // hideSelfLoading(idButton);
        //             if(data && data.ERROR) {
        //                 notifiToClient("Red", "Hủy ký số thất bại");
        //             } else {
        //                 $("#lanphauthuat_tuongtrinhpt_kyso").show()
        //                 $("#lanphauthuat_tuongtrinhpt_luu").show()
        //                 $("#lanphauthuat_tuongtrinhpt_xoa").show()
        //                 $("#lanphauthuat_tuongtrinhpt_huykyso").hide()
        //             }
        //
        //         })
        // }, function () {
        //
        // })
    })

    $("#phauthuat_tuongtrinh_loaituongtrinh").change(function() {
        checkKysoPTTT();
        var val = this.value;
        if(val == 'phieuphauthuat') {
            $(".tuongtrinh-phieuphauthuat").show();
            $(".tuongtrinh-phieukhac").hide();
        } else {
            $(".tuongtrinh-phieuphauthuat").hide();
            $(".tuongtrinh-phieukhac").show();
            getThongtinTTPT(lanPhauThuatObject.CHITIETDVKT, function(result) {
                result[0]['LOAI_TUONGTRINH'] = val;
                createFormTrinhTuPhauThuatKhacFormIO(result[0])
            });
        }
    });

    $("#lanphauthuat_tuongtrinhpt_copynoidung").click(function() {
        if ($("#phauthuat_tuongtrinh_loaituongtrinh").val() == 'phieuphauthuat') {
            var trinhtupttt = CKEDITOR.instances['phauthuat_tuongtrinh_noidung'].getData().trim();
            var trinhtupttt_xml5 = decodeHTMLEntities($('<textarea />').html(trinhtupttt).text());
            $("#phauthuat_tuongtrinh_cachthupt").val(trinhtupttt_xml5.replaceAll(/\n\n/g, '\n'))
        } else {
            var trinhTuXML5Arr = findComponentDataByKey(formPhauThuatKhac);
            var trinhTuXML5 = trinhTuXML5Arr.join("\n");
            $("#phauthuat_tuongtrinh_cachthupt").val(trinhTuXML5.replaceAll(/\n\n/g, '\n'))
        }
    });

    $("#lanphauthuat_tuongtrinhpt_xemchungnhan").click(function() {
        var ret = lanPhauThuatObject.CHITIETDVKT
        $.ajax({
            url: "cmu_getlist?url="+convertArray([
                singletonObject.dvtt,
                thongtinhsba.thongtinbn.SOVAOVIEN,
                thongtinhsba.thongtinbn.SOVAOVIEN_DT,
                ret.SO_PHIEU_DICHVU,
                ret.MA_DV,
                'cmu_smart769_cnptget']),
            method: "GET",
        }).done(function (dataKySo) {
            if(dataKySo.length > 0){
                getCMUFileSigned769(dataKySo[0].KEYMINIO,"pdf")
            } else {
                var arr = [singletonObject.dvtt,
                    ret.SO_PHIEU_DICHVU,
                    ret.MA_DV,
                    1,
                    '',
                    thongtinhsba.thongtinbn.STT_BENHAN,
                    lanPhauThuatTDTObject.STT_DOTDIEUTRI,
                    ret.STT_DIEUTRI,
                    thongtinhsba.thongtinbn.TEN_BENH_NHAN,
                ];
                var param = ['dvtt', 'sophieudichvu', 'madv', 'noitru', 'makhambenh',
                    "sttbenhan", "sttdotdieutri", "sttdieutri", "hotennguoibenh"];
                var url = "cmu_injasper?url=" + convertArray(arr) + "&param="
                    + convertArray(param) + "&loaifile=pdf&jasper=rp_phieuchungnhanttpt_v2";
                previewPdfDefaultModal(url, 'frame-inchungnhan')
            }
        });

    })

    $("#lanphauthuat_tuongtrinhpt_kysochungnhan_ptv").click(function() {
        var ret = lanPhauThuatObject.CHITIETDVKT
        var arr = [singletonObject.dvtt,
            ret.SO_PHIEU_DICHVU,
            ret.MA_DV,
            1,
            '',
            thongtinhsba.thongtinbn.STT_BENHAN,
            lanPhauThuatTDTObject.STT_DOTDIEUTRI,
            ret.STT_DIEUTRI,
            thongtinhsba.thongtinbn.TEN_BENH_NHAN,
        ];
        var param = ['dvtt', 'sophieudichvu', 'madv', 'noitru', 'makhambenh',
            "sttbenhan", "sttdotdieutri", "sttdieutri", "hotennguoibenh"];
        var url = "cmu_injasper?url=" + convertArray(arr) + "&param="
            + convertArray(param) + "&loaifile=pdf&jasper=rp_phieuchungnhanttpt_v2";
        previewAndSignPdfDefaultModal({
            url: url,
            idButton: 'giaychungnhan_kysoptv_action',
        }, function(){

        });
    })

    $(document).on('click', '#giaychungnhan_kysoptv_action', function() {
        var ret = lanPhauThuatObject.CHITIETDVKT;
        var idButton = this.id;
        // if(lanPhauThuatObject.BAC_SI_PHAU_THUAT != singletonObject.userId) {
        //     return notifiToClient("Red", MESSAGEAJAX.PERMISSION)
        // }
        showSelfLoading(idButton)
        kySoChung({
            dvtt: singletonObject.dvtt,
            userId: singletonObject.userId,
            url: $('#iframePreviewAndSign').attr('src'),
            loaiGiay: "PHIEU_NOITRU_GIAYCHUNGNHAN_PTV",
            maBenhNhan: thongtinhsba.thongtinbn.MA_BENH_NHAN,
            soBenhAn: thongtinhsba.thongtinbn.STT_BENHAN,
            soPhieuDichVu: ret.SO_PHIEU_DICHVU,
            maDichVu: ret.MA_DV,
            soVaoVien: thongtinhsba.thongtinbn.SOVAOVIEN,
            soVaoVienDT: thongtinhsba.thongtinbn.SOVAOVIEN_DT,
            keyword: "PHẪU THUẬT VIÊN 1",
            fileName: "Giấy chứng nhận: " + thongtinhsba.thongtinbn.TEN_BENH_NHAN + " - Số phiếu: " + ret.SO_PHIEU_DICHVU,
        }, function(dataKySo) {
            hideSelfLoading(idButton)
            $("#modalPreviewAndSignPDF").modal("hide");
            $("#lanphauthuat_tuongtrinhpt_kysochungnhan_ptv").hide();
            $("#lanphauthuat_tuongtrinhpt_huykysochungnhan_ptv").show();
        });
    });

    $("#lanphauthuat_tuongtrinhpt_huykysochungnhan_ptv").click(function() {
        var ret = lanPhauThuatObject.CHITIETDVKT
        // if(lanPhauThuatObject.BAC_SI_PHAU_THUAT != singletonObject.userId) {
        //     return notifiToClient("Red", MESSAGEAJAX.PERMISSION)
        // }
        confirmToClient("Bạn có chắc sẽ hủy ký số phiếu này?", function() {
            getFilesign769("PHIEU_NOITRU_GIAYCHUNGNHAN_TRUONGKHOA",
                ret.SO_PHIEU_DICHVU,
                -1,
                singletonObject.dvtt,
                ret.SOVAOVIEN, ret.SOVAOVIEN_DT, ret.MA_DICH_VU, function(dataKySo) {
                    if (dataKySo.length > 0) {
                        return notifiToClient("Red", "Trưởng khoa đã ký số, không thể hủy ký số")
                    }
                    huykysoFilesign769("PHIEU_NOITRU_GIAYCHUNGNHAN_PTV", ret.SO_PHIEU_DICHVU, singletonObject.userId, singletonObject.dvtt,
                        thongtinhsba.thongtinbn.SOVAOVIEN, thongtinhsba.thongtinbn.SOVAOVIEN_DT, -1, function(data) {
                            $("#lanphauthuat_tuongtrinhpt_kysochungnhan_ptv").show();
                            $("#lanphauthuat_tuongtrinhpt_huykysochungnhan_ptv").hide();
                        })
                });

        }, function () {

        })
    })

    $(".lanphauthuat_tuongtrinhpt_tvanh").click(function() {
        instanceGridHinhAnhTT();
        reloadDSHinhAnhTT();
        $("#modalDSHinhAnhTuongTrinh").modal("show");
    })
    $("#hinhanhtuongtrinh_them").click(function() {
        $("#modalFormHinhAnhTuongTrinh").modal("show");
    })

    $('#hinhanhtt_duyet').on('change', function (evt) {
        var input = evt.target;
        dataUrl = "";
        $("#hinhanhtt_hinhtenfile").html(this.files[0].name);
        var bg_preview = document.getElementById('say-cheese-snapshotstt').childNodes[0];

        if (input.files && input.files[0]) {
            var reader = new FileReader();
            // reader.addEventListener("load", function () {
            //     bg_preview.src = reader.result;
            // }, false);
            reader.readAsDataURL(this.files[0]);
            reader.onload = function () {
                dataUrl = reader.result;
                bg_preview.src = reader.result;
                $('#preview-imagett').css('display', 'inline-block');
            };
            reader.onerror = function (error) {
                console.log('Error: ', error);
            };
        } else {
            $('#preview-imagett').css('display', 'none');
        }

    });

    $('#hinhanhtt_luu').on('click', function (evt) {
        if($("#hinhanhtt_ten").val() === ""){
            notifiToClient("Red", "Vui lòng nhập tên trước khi lưu");
            return;
        }
        if(dataUrl === ""){
            notifiToClient("Red", "Vui lòng chọn hình ảnh");
            return;
        }
        showSelfLoading("hinhanhtt_luu");
        var actionUrl;
        var url;
        actionUrl = "cmu_post";
        url = [
            singletonObject.dvtt,
            $("#hinhanhtt_ten").val(),
            dataUrl,
            singletonObject.userId,
            singletonObject.makhoa,
            "THUVIENANH_INSERT"
        ];

        $.post(actionUrl, {
            url: url.join('```')
        }).done(function (data) {
            if(data > 0){
                notifiToClient('Green', MESSAGEAJAX.SUCCESS);
                $("#modalFormHinhAnhTuongTrinh").modal("hide");
            } else {
                notifiToClient("Red",MESSAGEAJAX.ERROR);
            }
        }).fail(function(error) {
            notifiToClient("Red",MESSAGEAJAX.ERROR);
        }).always(function() {
            hideSelfLoading("hinhanhtt_luu");
            reloadDSHinhAnhTT()
        });
    });

    $("#phauthuatTuongtrinhForm").validate({
        rules: {
            NGAY_CAT_CHI: {
                validDate: true,
                validDateNgaynhapvien: true
            },
            NGAY_RUT: {
                validDate: true,
                validDateNgaynhapvien: true
            }
        }
    })
    $.extend({
        openModalTuongTrinhPT: function(idWrap, rowSelected) {
            showLoaderIntoWrapId(idWrap);
            $.get("cmu_list_CMU_GET_IDPHAUTHUAT_IDDIEUTRI?url="+convertArray([singletonObject.dvtt,
                rowSelected.ID_DIEUTRI, rowSelected.SOVAOVIEN]))
                .done(function(data) {
                    if(data.length > 0 ) {
                        $.get("cmu_getlist?url="+convertArray([singletonObject.dvtt,
                            data[0].ID, 'CMU_LANPHAUTHUAT_SEL_BYID'])).done(function(data){
                            if(data.length > 0) {
                                $.get("cmu_getlist?url="+convertArray([singletonObject.dvtt,
                                    rowSelected.ID_DIEUTRI, thongtinhsba.thongtinbn.SOVAOVIEN,
                                    thongtinhsba.thongtinbn.SOVAOVIEN_DT, 'HSBA_CMU_TDT_GETBYID'])
                                ).done(function(dataTDT){
                                    loadEditor("phauthuat_tuongtrinh_noidung")
                                    lanPhauThuatTDTObject = dataTDT[0];
                                    lanPhauThuatObject = data[0];
                                    lanPhauThuatObject['CHITIETDVKT'] = rowSelected;
                                    getFilesign769(
                                        "PHIEU_NOITRU_GIAYCHUNGNHAN_PTV",
                                        lanPhauThuatObject.CHITIETDVKT.SO_PHIEU_DICHVU,
                                        -1,//singletonObject.userId,
                                        singletonObject.dvtt,
                                        thongtinhsba.thongtinbn.SOVAOVIEN,
                                        thongtinhsba.thongtinbn.SOVAOVIEN_DT,
                                        -1,
                                        function(data) {
                                            if(data.length > 0) {
                                                $("#lanphauthuat_tuongtrinhpt_kysochungnhan_ptv").hide();
                                                $("#lanphauthuat_tuongtrinhpt_huykysochungnhan_ptv").show();
                                            } else {
                                                $("#lanphauthuat_tuongtrinhpt_kysochungnhan_ptv").show();
                                                $("#lanphauthuat_tuongtrinhpt_huykysochungnhan_ptv").hide();
                                            }
                                        }
                                    )
                                    $("#modalPhauThuatTuongtrinhPT").modal("show")

                                    addTextTitleModal("titleModalPhauThuatTuongtrinhPT", "Tường trình phẫu thuật")
                                }).fail(function() {
                                    notifiToClient("Red", MESSAGEAJAX.ERROR)
                                }).always(function() {
                                    hideLoaderIntoWrapId(idWrap);
                                })

                            }
                        }).fail(function() {
                            notifiToClient("Red", MESSAGEAJAX.ERROR)
                            hideLoaderIntoWrapId(idWrap);
                        })
                    } else {
                        hideLoaderIntoWrapId(idWrap);
                        notifiToClient("Red", "Không có dữ liệu  phẫu thuật");
                    }

                }).fail(function() {
                notifiToClient("Red", MESSAGEAJAX.ERROR)
                hideLoaderIntoWrapId(idWrap);
            })

        }
    })
    combgridTenICD("phauthuat_tuongtrinh_tenicd", function(item) {
        var mabenh = "("+ item.ICD.toUpperCase() + ") " + item.MO_TA_BENH_LY;
        var icdCur = $("#phauthuat_tuongtrinh_tenicd").val().trim();
        $("#phauthuat_tuongtrinh_tenicd").val(icdCur == ''? mabenh: icdCur + "; " + mabenh);
    });

    combgridTenICD("phauthuat_tuongtrinh_tenicd_truoc", function(item) {
        var mabenh = "("+ item.ICD.toUpperCase() + ") " + item.MO_TA_BENH_LY;
        var icdCur = $("#phauthuat_tuongtrinh_tenicd_truoc").val().trim();
        $("#phauthuat_tuongtrinh_tenicd_truoc").val(icdCur == ''? mabenh: icdCur + "; " + mabenh);
    });
    function initGridPhauthuatDVKT() {
        var listTTPT = $("#lanphauthuat_dvkt_list_ttpt");
        if(!listTTPT[0].grid) {
            listTTPT.jqGrid({
                url: '',
                datatype: "local",
                loadonce: true,
                height: 300,
                width: null,
                shrinkToFit: false,
                ignoreCase: true,
                colModel: [
                    {label: "Mã DV",name: 'MA_DV', index: 'MA_DV', width: 120},
                    {label: "MABAOCAO",name: 'MABAOCAO', index: 'MABAOCAO', width: 30, hidden: true},
                    {label: "MA_DICHVU_KYTHUAT_DMDC",name: 'MA_DICHVU_KYTHUAT_DMDC', index: 'MA_DICHVU_KYTHUAT_DMDC', width: 30, hidden: true},
                    {label: "Tên dịch vụ",name: 'TEN_DV', index: 'TEN_DV', width: 230, cellattr: function (rowId, tv, rawObject, cm, rdata) {
                            return 'style="white-space: normal;"';
                        }
                    },
                    {label: "SL",name: 'SO_LUONG', index: 'SO_LUONG', width: 60, editable: true, edittype: 'text', align: "center"},
                    {label: "Loại",name: 'TEN_LOAIHINH', index: 'TEN_LOAIHINH', width: 50},
                    {label: "Đơn giá",name: 'GIA_DV', index: 'GIA_DV', width: 100, align: "right"},
                    {label: "Thành tiền",name: 'THANH_TIEN', index: 'THANH_TIEN', width: 100, align: "right"},
                    {label: "Madv",name: 'CHON', index: 'CHON', width: 60, hidden: true},
                    {label: "GIA_BHYT",name: 'GIA_BHYT', index: 'GIA_BHYT', width: 50, hidden: true},
                    {label: "THANHTIEN_BHYT",name: 'THANHTIEN_BHYT', index: 'THANHTIEN_BHYT', width: 50, hidden: true},
                    {label: "GIA_KBHYT",name: 'GIA_KBHYT', index: 'GIA_KBHYT', width: 50, hidden: true},
                    {label: "THANHTIEN_KBHYT",name: 'THANHTIEN_KBHYT', index: 'THANHTIEN_KBHYT', width: 50, hidden: true},
                    {label: "SOLUONG_AN",name: 'SOLUONG_AN', index: 'SOLUONG_AN', width: 50, hidden: true},
                    {label: "MANOIBO",name: 'MANOIBO', index: 'MANOIBO', width: 50, hidden: true},
                    {label: "TILETT",name: 'TILETT', index: 'TILETT', width: 50, hidden: true},
                    {label: "VITRITTPT",name: 'VITRITTPT', index: 'VITRITTPT', width: 50, hidden: true},
                    {label: "IDVITRITTPT",name: 'IDVITRITTPT', index: 'IDVITRITTPT', width: 50, hidden: true},
                    {label: "THOIGIANTTPT",name: 'THOIGIANTTPT', index: 'THOIGIANTTPT', width: 50, hidden: true},
                    {label: "Dịch vụ", name: 'DICHVU', index: 'DICHVU', width: 50, cellattr: function (cellvalue, options, rowObject) {
                            var color;
                            var color_text;
                            if (rowObject.DICHVU !=undefined && rowObject.DICHVU.toString() == "1") {
                                color = 'red';
                                color_text = 'black;font-weight:bold';
                            } else {
                                color = 'white';
                                color_text = 'black';
                            }
                            return 'style="background-color:' + color + ' ;color:' + color_text + '"';
                        }
                    }

                ],
                sortname: 'TEN_DV',
                rowNum: 1000000,
                sortorder: "asc",
                multiselect: true,
                caption: "",
                gridComplete: function () {
                    var str = listTTPT.jqGrid('getDataIDs');
                    if (str != "") {
                        for (var i = 0; i < str.length; i++) {
                            var ret1 = listTTPT.jqGrid('getRowData', str[i]);
                            if (ret1.CHON.toString() == "1")
                                listTTPT.jqGrid('setSelection', str[i]);
                        }
                    }
                },
                onSelectRow: function(id, selected){
                    if(id){
                        var rowData = listTTPT.jqGrid("getRowData", id);
                        var allData = listTTPT.jqGrid('getGridParam','data');
                        allData.map(function(item){
                            if(item.MA_DV == rowData.MA_DV){
                                item.CHON = selected? 1: 0;
                            }
                            return item;
                        })
                        listTTPT.jqGrid('setGridParam', { data: allData});
                    }
                },
                cellEdit: false,
                cellsubmit: 'clientArray',
            });
            listTTPT.jqGrid('filterToolbar', {stringResult: true, searchOnEnter: false, defaultSearch: "cn"});
        }
        var listPhieuTTPT = $("#lanphauthuat_dvkt_list_phieuttpt");
        if(!listPhieuTTPT[0].grid) {
            listPhieuTTPT.jqGrid({
                url: '',
                datatype: "local",
                loadonce: true,
                height: 396,
                width: null,
                shrinkToFit: false,
                colModel: [
                    {label: "SO_PHIEU_DICHVU", name: 'SO_PHIEU_DICHVU', index: 'SO_PHIEU_DICHVU', width: 150},
                    {label: "MA_PHONG_DICHVU",name: 'MA_PHONG_DICHVU', index: 'MA_PHONG_DICHVU', width: 60, hidden: true},
                    {label: "STT_DIEUTRI",name: 'STT_DIEUTRI', index: 'STT_DIEUTRI', width: 60, hidden: true},
                    {label: "Khoa",name: 'KHOA_CHI_DINH', index: 'KHOA_CHI_DINH', width: 120},
                    {label: "Tên loại", name: 'TEN_LOAI_DV', index: 'TEN_LOAI_DV', width: 10, hidden: true},
                    {label: "MA_LOAI", name: 'MA_LOAI_DICHVU', index: 'MA_LOAI_DICHVU', width: 10, hidden: true},
                    {label: "Mã DV", name: 'MA_DV', index: 'MA_DV', width: 60},
                    {label: "Tên DV", name: 'TEN_DV_HT', index: 'TEN_DV_HT', width: 215,
                        formatter: function (cellvalue, options, rowObject) {
                            var ekip = '';
                            var tienekip = '';
                            var dathuchien = '';
                            if(rowObject.DACOEKIP > 0) {
                                ekip = "<p class='text-success font-weight-bold m-0'> <i class='fa fa-users'></i> Đã có ekip</p>";
                            }
                            if(rowObject.TIENEKIP == 1) {
                                tienekip = "<p class='text-warning font-weight-bold m-0'> <i class='fa fa-money'></i> Tính tiền ekip</p>";
                            }
                            if(rowObject.DA_CHAN_DOAN == 1) {
                                dathuchien = " <p class='text-primary font-weight-bold m-0 mt-1'> <i class='fa fa-check-circle'></i> Đã thực hiện</p>";
                            }
                            return rowObject.TEN_DV + ekip + tienekip + dathuchien;
                        },
                        cellattr: function (rowId, tv, rawObject, cm, rdata) {
                            return 'style="white-space: normal;"';
                        }},
                    {label: "TEN_DV", name: 'TEN_DV', index: 'TEN_DV', width: 215, hidden: true},
                    {label: "Tỉ lệ TT", name: 'TILE_THANHTOAN', index: 'TILE_THANHTOAN', width: 60},
                    {label: "Cấp cứu",name: 'CAPCUU', index: 'CAPCUU', width: 60},
                    {label: "Thực hiện",name: 'DATHUCHIEN', index: 'DATHUCHIEN', width: 60},
                    {label: "BHYT", name: 'CO_BHYT', index: 'CO_BHYT', width: 60},
                    {label: "Số lượng", name: 'SO_LUONG', index: 'SO_LUONG', width: 50, editable: true, edittype: 'text', align: "center"},
                    {label: "Đơn giá", name: 'GIA_CDHA', index: 'GIA_CDHA', width: 50, align: "right"},
                    {label: "Thành tiền", name: 'THANH_TIEN', index: 'THANH_TIEN', width: 50, align: "right"},
                    {label: "Bác sĩ điều trị",name: 'TEN_NHANVIEN', index: 'TEN_NHANVIEN', width: 150},
                    {label: "Ngày chỉ định",name: 'NGAY_CHI_DINH', index: 'NGAY_CHI_DINH', width: 100},
                    {label: "Giờ chỉ định",name: 'GIO_CHI_DINH', index: 'GIO_CHI_DINH', width: 60},
                    {label: "DA_CHAN_DOAN", name: 'DA_CHAN_DOAN', index: 'DA_CHAN_DOAN', width: 60, hidden: true},
                    {label: "SOVAOVIEN",name: 'SOVAOVIEN', index: 'SOVAOVIEN', width: 60, hidden: true},
                    {label: "SOVAOVIEN_DT",name: 'SOVAOVIEN_DT', index: 'SOVAOVIEN_DT', width: 60, hidden: true},
                    {label: "PHONG_CHI_DINH",name: 'PHONG_CHI_DINH', index: 'PHONG_CHI_DINH', width: 60, hidden: true},
                    {label: "CHON", name: 'CHON', index: 'CHON', width: 60, hidden: true},
                    {label: "DACOEKIP", name: 'DACOEKIP', index: 'DACOEKIP', width: 60, hidden: true},
                    {label: "TIENEKIP", name: 'TIENEKIP', index: 'TIENEKIP', width: 60, hidden: true},

                ],
                rowNum: 1000000,
                caption: "Danh sách phiếu chỉ định",
                grouping: true,
                groupingView: {
                    groupField: ["SO_PHIEU_DICHVU","TEN_LOAI_DV"],
                    groupColumnShow: [false, false],
                    groupText: ['<b>{0}</b>', '<b>{0}</b>'],
                    groupCollapse: false
                },
                footerrow: true,
                loadComplete: function () {
                    var $self = $(this);
                    var sum_tt = $self.jqGrid("getCol", "THANH_TIEN", false, "sum");
                    $self.jqGrid("footerData", "set", {THANH_TIEN: sum_tt});
                    $self.jqGrid("footerData", "set", {TEN_CDHA: "Tổng tiền:"});
                },
                onSelectRow: function(id) {
                    if (id) {
                        var ret = $("#lanphauthuat_dvkt_list_phieuttpt").jqGrid('getRowData', id);
                        $("#lanphauthuat_dvkt_ttpt_giochidinh").val(ret.NGAY_CHI_DINH + " "+ ret.GIO_CHI_DINH);
                        $("#lanphauthuat_dvkt_ttpt_hinhthuc").val(ret.CO_BHYT == 1? 0: 1);
                        $("#lanphauthuat_dvkt_ttpt_hinhthuc").prop("disabled", true);
                        $("#lanphauthuat_dvkt_ttpt_trangthai").val(ret.CAPCUU);
                        $("#lanphauthuat_dvkt_ttpt_phongttpt").val(ret.MA_PHONG_DICHVU);
                        $("#tlanphauthuat_dvkt_ttpt_phongttpt").prop("disabled", true);
                        $("#lanphauthuat_dvkt_ttpt_loaittpt").val(ret.MA_LOAI_DICHVU);
                        $("#lanphauthuat_dvkt_ttpt_loaittpt").prop("disabled", true);
                        loadGridPhauthuatDVKT(ret.SO_PHIEU_DICHVU, thongtinhsba.thongtinbn)
                        enableModeButtonPhauthuatDVKT('edit')
                        checkKyso769({
                            dvtt: singletonObject.dvtt,
                            soVaoVien: thongtinhsba.thongtinbn.SOVAOVIEN,
                            soVaoVienDT: thongtinhsba.thongtinbn.SOVAOVIEN_DT,
                            kyHieuPhieu: "PHIEUCD_NOITRU_TTPT",
                            soPhieuDichVu: ret.SO_PHIEU_DICHVU,
                            userId: singletonObject.userId,
                        }, function(data) {
                            if(data[0].DAKY > 0) {
                                $("#lanphauthuat_dvkt_tdt_kysottpt").hide()
                                $("#lanphauthuat_dvkt_tdt_luuttpt").hide()
                                $("#lanphauthuat_dvkt_ttpt_dropdown .item-kyso").show()
                            } else {
                                $("#lanphauthuat_dvkt_tdt_kysottpt").show()
                                $("#lanphauthuat_dvkt_tdt_luuttpt").show()
                                $("#lanphauthuat_dvkt_ttpt_dropdown .item-kyso").hide()
                            }
                        }, function() {

                        })
                    }

                },
                onRightClickRow: function(id) {
                    if(id) {
                        $.contextMenu({
                            selector: '#lanphauthuat_dvkt_list_phieuttpt tr',
                            reposition : false,
                            callback: function (key, options) {
                                var rowSelected = getThongtinRowSelected("lanphauthuat_dvkt_list_phieuttpt")
                                var idWrap = "lanphauthuat_dvkt_list_phieuttpt_wrap";
                                if(key == "tile50" || key == "tile80" || key == "tile100") {
                                    var tile = 50;
                                    if(key == "tile80") {
                                        tile = 80;
                                    }
                                    if(key == "tile100") {
                                        tile = 100;
                                    }
                                    showLoaderIntoWrapId(idWrap);
                                    $.post("capnhattielethanhtoan", {
                                        sovaovien: rowSelected.SOVAOVIEN,
                                        sovaovien_dt: rowSelected.SOVAOVIEN_DT,
                                        madv: rowSelected.MA_DV,
                                        tile: tile,
                                        sophieu: rowSelected.SO_PHIEU_DICHVU,
                                        stt_dieutri: rowSelected.STT_DIEUTRI
                                    }).done(function() {
                                        notifiToClient("Green", "Cập nhật thành công!")
                                        $("#lanphauthuat_dvkt_tdt_huyttpt").click()
                                    }).fail(function() {
                                        notifiToClient("Red", MESSAGEAJAX.ERROR)
                                    }).always(function() {
                                        hideLoaderIntoWrapId(idWrap);
                                    })
                                }
                                if(key == "xoa") {
                                    if (rowSelected.DACOEKIP > 0) {
                                        return notifiToClient("Red", "Đã có EKIP không được xóa");
                                    }
                                    if (rowSelected.DA_CHAN_DOAN == 1) {
                                        return notifiToClient("Red", "Dịch vụ đã thực hiện không được xóa");
                                    }
                                    confirmToClient("Bạn có chắc chắn muốn chỉ định này?", function() {
                                        showLoaderIntoWrapId(idWrap)
                                        var arr = [rowSelected.SO_PHIEU_DICHVU,
                                            singletonObject.dvtt, rowSelected.MA_DV,
                                            rowSelected.STT_DIEUTRI,
                                            thongtinhsba.thongtinbn.STT_BENHAN,
                                            lanPhauThuatTDTObject.STT_DOTDIEUTRI,
                                            rowSelected.SOVAOVIEN, rowSelected.SOVAOVIEN_DT];
                                        $.post("noitru_ttpt_delete_cacchitiet_svv", {
                                            url: convertArray(arr)
                                        }).done(function(data) {
                                            var arrMessage = [
                                                "Xóa thành công!",
                                                "Dịch vụ đã được thanh toán",
                                                "Dịch vụ đã được thực hiện",
                                                "Dịch vụ đã được tính tiền ekip",
                                            ]
                                            notifiToClient(data == 0? "Green": "Red", arrMessage[data])
                                            enableModeButtonPhauthuatDVKT('add')
                                            $("#lanphauthuat_dvkt_tdt_huyttpt").click();
                                            var urlXoarong = "ttpt_xoaphieurong_noitru";
                                            var arrXoarong = [singletonObject.dvtt, rowSelected.SOVAOVIEN, rowSelected.SOVAOVIEN_DT];
                                            $.post(urlXoarong, {
                                                url: convertArray(arrXoarong)
                                            });
                                        }).fail(function() {
                                            notifiToClient("Red", MESSAGEAJAX.ERROR)
                                        }).always(function() {
                                            hideLoaderIntoWrapId(idWrap)
                                        })


                                    }, function () {

                                    })
                                }
                                if(key == "tuongtrinh") {
                                    lanPhauThuatObject['CHITIETDVKT'] = rowSelected;
                                    $("#modalPhauThuatTuongtrinhPT").modal("show")

                                    addTextTitleModal("titleModalPhauThuatTuongtrinhPT", "Tường trình phẫu thuật")
                                }
                                if(key == "ekip") {
                                    getThongtinTTPT(rowSelected, function(result) {
                                        $("#modalPhauThuatEKIPTuongtrinhPT").modal("show")
                                        $.get("cmu_getlist?url="+convertArray([singletonObject.dvtt, lanPhauThuatObject.ID,'CMU_LANPHAUTHUAT_EKIPTONG_SEL'])).done(function( data ) {
                                            $("#phauthuat_tuongtrinh_dsekip").empty();
                                            dsEkipByID = []
                                            data.forEach(function(item, index) {
                                                $("#phauthuat_tuongtrinh_dsekip").append("<option "+(index == 0? "selected":"")+" value='"+item.ID+"'>"+item.TENEKIP+"</option>");
                                            })
                                            $("#phauthuat_tuongtrinh_dsekip").trigger("change");
                                        })
                                        initGridDSEkip();
                                        loadDSEkip();
                                        addTextTitleModal("titleModalPhauThuatEKIPTuongtrinhPT", "Ekip - "+rowSelected.TEN_DV)
                                        $("#phauthuat_tuongtrinh_tinhtienekip").val(result[0].TINHTIENEKIP)

                                    })
                                }
                            },
                            items: {
                                "tuongtrinh": {name: '<p><i class="fa fa-file-text text-primary" aria-hidden="true"></i> Tường trình</p>'},
                                "ekip": {name: '<p><i class="fa fa-users text-primary" aria-hidden="true"></i> Ekip</p>'},
                                "tile50": {name: '<p><i class="fa fa-money text-primary" aria-hidden="true"></i> Tỉ lệ thanh toán 50%</p>'},
                                "tile80": {name: '<p><i class="fa fa-money text-primary" aria-hidden="true"></i> Tỉ lệ thanh toán 80%</p>'},
                                "tile100": {name: '<p><i class="fa fa-money text-primary" aria-hidden="true"></i> Tỉ lệ thanh toán 100%</p>'},
                                "xoa": {name: '<p><i class="fa fa-trash-o text-danger" aria-hidden="true"></i> Xóa</p>'}
                            }
                        });
                    }
                },
                ondblClickRow: function(id) {
                    lanPhauThuatObject['CHITIETDVKT'] =  getThongtinRowSelected("lanphauthuat_dvkt_list_phieuttpt");
                    $("#modalPhauThuatTuongtrinhPT").modal("show")

                    addTextTitleModal("titleModalPhauThuatTuongtrinhPT", "Tường trình phẫu thuật")
                }
            });
            listPhieuTTPT.jqGrid('filterToolbar', {stringResult: true, searchOnEnter: false, defaultSearch: "cn"});
        }
    }

    function loadGridPhauthuatDVKT(sophieuTTPT, dataBN) {
        var arr = [$("#lanphauthuat_dvkt_ttpt_hinhthuc").val() != 0, singletonObject.dvtt, sophieuTTPT,
            $("#lanphauthuat_dvkt_ttpt_loaittpt").val(), 0,0,
            0,
            lanPhauThuatTDTObject.SOVAOVIEN, lanPhauThuatTDTObject.SOVAOVIEN_DT];
        var gioitinh = dataBN.GIOI_TINH == 0? "Nam": "Nữ"
        var url = "noitru_ttpt_hienthiluoi_svv?url=" + convertArray(arr) + "&phan_loai_gioi_tinh=" + gioitinh + "&nguonngoai=" + $("#lanphauthuat_dvkt_ttpt_hinhthuc").val();
        var list = $("#lanphauthuat_dvkt_list_ttpt");
        if(list[0].grid) {

            loadDataGridGroupBy(list, url);
        }

    }

    function enableModeButtonPhauthuatDVKT(mode) {
        if(mode == 'add') {
            $("#lanphauthuat-dvkt-tab .add-ttpt").show()
            $("#lanphauthuat-dvkt-tab .edit-ttpt").hide()
        } else {
            $("#lanphauthuat-dvkt-tab .add-ttpt").hide()
            $("#lanphauthuat-dvkt-tab .edit-ttpt").show()
        }
    }

    function loadGridPhauthuatDVKTCDPT() {
        var arr = [ singletonObject.dvtt,
            lanPhauThuatTDTObject.ID_DIEUTRI? lanPhauThuatTDTObject.ID_DIEUTRI: 0,
            thongtinhsba.thongtinbn.STT_BENHAN,
            lanPhauThuatTDTObject.STT_DOTDIEUTRI? lanPhauThuatTDTObject.STT_DOTDIEUTRI: 0];
        var url = "cmu_list_CMU_HSBA_DSTTPT_TDT_F?url=" + convertArray(arr);
        var list = $("#lanphauthuat_dvkt_list_phieuttpt");
        if(list[0].grid) {
            loadDataGridGroupBy(list, url);
        }

    }

    function checkTTPT(idButton, SO_PHIEU_TTPT) {
        var maXNs = "";
        var maXNArray = [];
        var listRowSelected = $("#lanphauthuat_dvkt_list_ttpt").jqGrid('getGridParam', 'selarrrow');
        if($("#lanphauthuat_dvkt_ttpt_giochidinh").val() == "") {
            notifiToClient("Red", "Vui lòng nhập giờ chỉ định");
            return false;
        }
        var ngaycd =  $("#lanphauthuat_dvkt_ttpt_giochidinh").val().split(" ")[0];
        var ngaygiochidinh = $("#lanphauthuat_dvkt_ttpt_giochidinh").val();
        var thoigianbatdaudieutri =  thongtinhsba.thongtinbn.NGAY_VAO_VIEN + " "+ thongtinhsba.thongtinbn.GIO_VAO_VIEN;
        if(moment(ngaygiochidinh, ['DD/MM/YYYY HH:mm']).isBefore(moment(thoigianbatdaudieutri, ['DD/MM/YYYY HH:mm:ss']))) {
            hideSelfLoading(idButton);
            notifiToClient("Red", "Giờ chỉ định trước giờ vào viện, vui lòng kiểm tra lại");
            return false;
        }
        if(!moment(ngaygiochidinh, ['DD/MM/YYYY HH:mm']).isBefore(moment(lanPhauThuatObject.BAT_DAU_ME, ['DD/MM/YYYY HH:mm']))) {
            hideSelfLoading(idButton);
            notifiToClient("Red", "Giờ chỉ định trước giờ bắt đầu mê: "+ lanPhauThuatObject.BAT_DAU_ME);
            return false;
        }
        if(listRowSelected.length == 0) {
            hideSelfLoading(idButton);
            notifiToClient("Red", "Vui chọn chỉ định");
            return false;
        }
        listRowSelected.forEach(function(value) {
            var ret = $("#lanphauthuat_dvkt_list_ttpt").jqGrid('getRowData', value);
            maXNs = maXNs + "!" + ret.MA_DV + "!";
            maXNArray.push(ret.MA_DV);
        })
        var ngaychidinh = convertStr_MysqlDate(ngaycd);
        showSelfLoading(idButton);
        var response =  $.ajax({
            url: "noitru_kt_controngkhoa",
            method: "POST",
            async: false,
            data: {
                stt_benhan: thongtinhsba.thongtinbn.STT_BENHAN,
                dvtt: singletonObject.dvtt,
                maphongban: singletonObject.makhoa,
                stt_logkhoaphong: thongtinhsba.thongtinbn.STT_LOGKHOAPHONG
            }
        }).responseText;
        if(response != "1") {
            notifiToClient("Red","Bệnh nhân không còn trong khoa. Vui lòng kiểm tra lại");
            return false;
        }
        var resKiemtratrung = $.ajax({
            url: "kiemtrachidinhtrung_dichvu_noitru?ngay_chi_dinh=" + ngaychidinh + "&danhsachma_dichvu=" + maXNs + "&sophieu=" + SO_PHIEU_TTPT
                + "&sovaovien=" + lanPhauThuatTDTObject.SOVAOVIEN + "&sovaovien_dt=" + lanPhauThuatTDTObject.SOVAOVIEN_DT,
            async: false,
            method: "GET"
        }).responseText;
        var kiemtratrung = JSON.parse(resKiemtratrung);
        var kiemtraTrung = "";
        $.each(kiemtratrung, function (i) {
            kiemtraTrung = kiemtraTrung + "<br />- " + kiemtratrung[i].TEN_DV;
        });
        if(kiemtraTrung != "") {
            kiemtraTrung = '<p style="color: darkred">' + kiemtraTrung+ " <br/> đã được chỉ định</p>"
        }

        var resKiemtra3thang = $.ajax({
            url: "cmu_list_KIEMTRA_TTPT_3THANG_NT?url="+convertArray([
                lanPhauThuatTDTObject.SOVAOVIEN,
                lanPhauThuatTDTObject.SOVAOVIEN_DT,
                SO_PHIEU_TTPT,
                ngaychidinh,
                maXNs,
                singletonObject.dvtt
            ]),
            method: "GET",
            async: false,
        }).responseText;
        var kiemtra3thang = JSON.parse(resKiemtra3thang);
        var notifi3thang = "";
        if(kiemtra3thang.SOLUONG > 0) {
            notifi3thang = '<p style="color: darkred">Danh sách TTPT vượt quá số lần quy định: <br>' + kiemtra3thang[0].THONGBAOSQL + "</p>"
        }
        return  kiemtraTrung + notifi3thang;
    }

    function insertTTPT() {
        var ngaychidinh = convertStr_MysqlDate(lanPhauThuatTDTObject.NGAYGIOLAPTDT.split(" ")[0]);

        var arr = [lanPhauThuatTDTObject.STT_DIEUTRI, thongtinhsba.thongtinbn.STT_BENHAN, thongtinhsba.thongtinbn.STT_DOTDIEUTRI,
            $("#lanphauthuat_dvkt_ttpt_phongttpt").val(),
            singletonObject.dvtt,
            $("#lanphauthuat_dvkt_ttpt_loaittpt").val(),
            lanPhauThuatTDTObject.BACSIDIEUTRI,
            singletonObject.maphongbenh,
            $("#lanphauthuat_dvkt_ttpt_hinhthuc").val() == 0,
            0,0,singletonObject.userId,
            lanPhauThuatTDTObject.SOVAOVIEN, lanPhauThuatTDTObject.SOVAOVIEN_DT,
            ngaychidinh, thongtinhsba.thongtinbn.MA_BENH_NHAN, $("#lanphauthuat_dvkt_ttpt_trangthai").val()];
        var url = "noitru_ttpt_insert_bangcha?nguonngoai=" + $("#lanphauthuat_dvkt_ttpt_trangthai").val();
        $.post(url, {url: convertArray(arr)}).done(function (data) {
            if (data.SAISOT.toString() == "1") {
                return notifiToClient("Red", "Bệnh nhân đã thanh toán không thể chỉ định");
            }
            insertTTPTCT(data.SO_PHIEU_DV, thongtinhsba.thongtinbn, "lanphauthuat_dvkt_tdt_themoittpt");
        });
    }

    function insertTTPTCT(SO_PHIEU_DICHVU, dataBN, idButton) {

        var listRowSelected = $("#lanphauthuat_dvkt_list_ttpt").jqGrid('getGridParam', 'selarrrow');
        var count = listRowSelected.length;
        for (var i = 0; i < count; i++) {
            var ret = $("#lanphauthuat_dvkt_list_ttpt").jqGrid('getRowData', listRowSelected[i]);
            var madv = ret.MA_DV;
            var sl = ret.SO_LUONG;
            var giadv = ret.GIA_DV;
            var thanhtien = ret.THANH_TIEN;
            var vitrittpt = ret.VITRITTPT;
            var thoigianttpt = ret.THOIGIANTTPT;
            var arrIns = [SO_PHIEU_DICHVU, madv, singletonObject.dvtt, sl, giadv, thanhtien, $("#lanphauthuat_dvkt_ttpt_hinhthuc").val() == 0? "false": "true",
                lanPhauThuatTDTObject.STT_DIEUTRI, dataBN.STT_BENHAN, lanPhauThuatTDTObject.STT_DOTDIEUTRI, lanPhauThuatTDTObject.SOVAOVIEN,
                lanPhauThuatTDTObject.SOVAOVIEN_DT, dataBN.MA_BENH_NHAN, ret.GIA_BHYT, ret.THANHTIEN_BHYT, ret.GIA_KBHYT, ret.THANHTIEN_KBHYT, ret.TILETT];
            $.ajax({
                url: "noittru_ttpt_insert_chitiet_svv",
                method: "POST",
                data: {
                    url: convertArray(arrIns),
                    vitrittpt:vitrittpt,
                    thoigianttpt:thoigianttpt
                },
                async: false,
            })
        }
        updatePhieuTTPTCHA(SO_PHIEU_DICHVU);
        notifiToClient("Green", "Thành công")
        hideSelfLoading(idButton)
        loadGridPhauthuatDVKTCDPT()
        loadGridPhauthuatDVKT("", dataBN);
    }

    function updatePhieuTTPTCHA(SO_PHIEU_DICHVU) {
        $.post("cmu_post",
            {
                url: [
                    singletonObject.dvtt, SO_PHIEU_DICHVU, lanPhauThuatTDTObject.SOVAOVIEN, lanPhauThuatTDTObject.SOVAOVIEN_DT,
                    $("#lanphauthuat_dvkt_ttpt_trangthai").val(),
                    $("#lanphauthuat_dvkt_ttpt_giochidinh").val(),
                    $("#lanphauthuat_dvkt_ttpt_hinhthuc").val(), "CMU_UPDATE_PHIEUTTPT"
                ].join("```")
            });
        $.post("cmu_post", {url:
                [
                    singletonObject.dvtt,
                    lanPhauThuatTDTObject.SOVAOVIEN,
                    lanPhauThuatTDTObject.SOVAOVIEN_DT,
                    lanPhauThuatTDTObject.ID_DIEUTRI,
                    $("#lanphauthuat_dvkt_ttpt_giochidinh").val(),
                    "CMU_UPDATE_TDT_NGAYLAP"
                ].join("```")
        })

    }

    function doUpateTTPT(ret, dataBN) {
        deleteTTPTCT(ret.SO_PHIEU_DICHVU, dataBN)
        insertTTPTCT(ret.SO_PHIEU_DICHVU, dataBN, "lanphauthuat_dvkt_tdt_luuttpt");
        $("#lanphauthuat_dvkt_ttpt_hinhthuc").prop("disabled", false);
        $("#lanphauthuat_dvkt_ttpt_phongttpt").prop("disabled", false);
        $("#lanphauthuat_dvkt_ttpt_loaittpt").prop("disabled", false);
        enableModeButtonPhauthuatDVKT('add')
    }

    function deleteTTPTCT(SO_PHIEU_DICHVU, dataBN) {
        var arr_tong = [];
        var listDataPhieuTTPT = getAllRowDataJqgrid("lanphauthuat_dvkt_list_phieuttpt");
        listDataPhieuTTPT.forEach(function (item) {
            if(item.SO_PHIEU_DICHVU == SO_PHIEU_DICHVU){
                arr_tong.push(item.MA_DV);
            }
        })
        var arr = [SO_PHIEU_DICHVU, singletonObject.dvtt, arr_tong.toString(), lanPhauThuatTDTObject.STT_DIEUTRI, dataBN.STT_BENHAN, lanPhauThuatTDTObject.STT_DOTDIEUTRI,
            lanPhauThuatTDTObject.SOVAOVIEN, lanPhauThuatTDTObject.SOVAOVIEN_DT];
        $.ajax({
            url: "noitru_ttpt_delete_cacchitiet_svv",
            method: "POST",
            data: {
                url: convertArray(arr)
            },
            async: false,
        })
    }

    function loadEditor(id)
    {
        var instance = CKEDITOR.instances[id];
        if(!instance)
        {
            CKEDITOR.replace(id);
        }

    }
    function initHTMLCanvas() {
        $("#phauthuat_tuongtrinh_hinhanh").html(getHTMLPAINTING(idContainer, idCanvas))
    }

    function initGridDSEkip() {
        var list = $("#phauthuat_tuongtrinh_ekipct");
        if(!list[0].grid) {
            list.jqGrid({
                url: "",
                datatype: "local",
                loadonce: true,
                height: 150,
                width: null,
                shrinkToFit: false,
                colModel: [
                    {label: "ID", name: 'ID', index: 'ID', width: 100,},
                    {label: "Mã bác sĩ", name: 'MANHANVIEN', index: 'MANHANVIEN', width: 200},
                    {label: "Tên bác sĩ", name: 'TENNHANVIEN', index: 'TENNHANVIEN', width: 200},
                    {label: "Vị trí",name: 'VITRI', index: 'VITRI', width: 150,
                        formatter: function (cellvalue, options, rowObject) {
                            var object = {
                                BSPHAUTHUAT: "Bác sĩ phẫu thuật",
                                BSGAYME: "Bác sĩ gây mê/tê",
                                KTVGAYMETE: "KTV gây mê/tê",
                                PHUMO1: "Phụ mổ 1",
                                PHUMO2: "Phụ mổ 2",
                                PHUMO3: "Phụ mổ 3",
                                PHUMO4: "Phụ mổ 4",
                                DUNGCU1: "Dụng cụ 1",
                                DUNGCU2: "Dụng cụ 2"
                            }
                            return object[cellvalue];
                        }
                    },
                    {label: "Người tạo",name: 'TENNGUOITAO', index: 'TENNGUOITAO', width: 150},
                    {label: "Ngày tạo ",name: 'NGAY_TAO', index: 'NGAY_TAO', width: 150},
                    {label: "NGUOI_TAO ",name: 'NGUOI_TAO', index: 'NGUOI_TAO', hidden: true},
                ],
                rowNum: 1000,
                caption: "Danh sách ekip",
                loadComplete: function () {
                    var dataGrid = getAllRowDataJqgrid("phauthuat_tuongtrinh_ekipct")
                    $("#lanphauthuat_tuongtrinhpt_xoaekip_dvkt").hide()
                    if(dataGrid.length > 0) {
                        $("#lanphauthuat_tuongtrinhpt_xoaekip_dvkt").show()
                    }
                },
                onRightClickRow: function(id) {
                    if (id) {

                        $.contextMenu({
                            selector: '#phauthuat_tuongtrinh_ekipct tr',
                            reposition : false,
                            callback: function (key, options) {
                                var rowData = getThongtinRowSelected("phauthuat_tuongtrinh_ekipct");
                                if (key == "xoa") {
                                    if(rowData.NGUOI_TAO  && rowData.NGUOI_TAO != singletonObject.userId) {
                                        return notifiToClient("Red", MESSAGEAJAX.PERMISSION)
                                    }
                                    confirmToClient(MESSAGEAJAX.CONFIRM, function() {
                                        var id = list.jqGrid("getGridParam", "selrow");
                                        list.jqGrid('delRowData',id);
                                    })
                                }
                            },
                            items: {
                                "xoa": {name: '<p><i class="fa fa-trash-o text-danger" aria-hidden="true"></i> Xóa</p>'}
                            }
                        });

                    }
                }
            });
        }

        var listView = $("#phauthuat_tuongtrinh_ekipct_view");
        if(!listView[0].grid) {
            listView.jqGrid({
                url: "",
                datatype: "local",
                loadonce: true,
                height: 150,
                width: null,
                shrinkToFit: false,
                colModel: [
                    {label: "ID", name: 'ID', index: 'ID', width: 100,},
                    {label: "Mã bác sĩ", name: 'MANHANVIEN', index: 'MANHANVIEN', width: 200},
                    {label: "Tên bác sĩ", name: 'TENNHANVIEN', index: 'TENNHANVIEN', width: 200},
                    {label: "Vị trí",name: 'VITRI', index: 'VITRI', width: 150,
                        formatter: function (cellvalue, options, rowObject) {
                            var object = {
                                BSPHAUTHUAT: "Bác sĩ phẫu thuật",
                                BSGAYME: "Bác sĩ gây mê/tê",
                                KTVGAYMETE: "KTV gây mê/tê",
                                PHUMO1: "Phụ mổ 1",
                                PHUMO2: "Phụ mổ 2",
                                PHUMO3: "Phụ mổ 3",
                                PHUMO4: "Phụ mổ 4",
                                DUNGCU1: "Dụng cụ 1",
                                DUNGCU2: "Dụng cụ 2"
                            }
                            return object[cellvalue];
                        }
                    },
                    {label: "Người tạo",name: 'TENNGUOITAO', index: 'TENNGUOITAO', width: 150},
                    {label: "Ngày tạo ",name: 'NGAY_TAO', index: 'NGAY_TAO', width: 150},
                    {label: "NGUOI_TAO ",name: 'NGUOI_TAO', index: 'NGUOI_TAO', hidden: true},
                ],
                rowNum: 1000,
                caption: "Danh sách ekip",

            });
        }
    }
    function loadDSEkip() {
        var rowSelected = getThongtinRowSelected("lanphauthuat_dvkt_list_phieuttpt")
        var url = "cmu_getlist?url="+convertArray([singletonObject.dvtt, lanPhauThuatObject.ID, rowSelected.SO_PHIEU_DICHVU, rowSelected.MA_DV,'CMU_LANPHAUTHUAT_EKIP_DVKT_SEL']);
        $("#phauthuat_tuongtrinh_ekipct").jqGrid('setGridParam', {datatype: 'json', url: url}).trigger('reloadGrid');
    }

    function loadDSEkipView() {
        var rowSelected = lanPhauThuatObject.CHITIETDVKT
        var url = "cmu_getlist?url="+convertArray([singletonObject.dvtt, lanPhauThuatObject.ID, rowSelected.SO_PHIEU_DICHVU, rowSelected.MA_DV,'CMU_LANPHAUTHUAT_EKIP_DVKT_SEL']);
        $("#phauthuat_tuongtrinh_ekipct_view").jqGrid('setGridParam', {datatype: 'json', url: url}).trigger('reloadGrid');
    }
    function initGridDSHinhanh() {
        var idList = "phauthuat_tuongtrinh_dsanh"
        var list = $("#"+idList);
        if(!list[0].grid) {
            list.jqGrid({
                url: "",
                datatype: "local",
                loadonce: true,
                height: 150,
                width: null,
                shrinkToFit: false,
                colModel: [
                    {label: "STT",name: 'STT', index: 'STT', width: 50},
                    {label: "Số phiếu",name: 'SO_PHIEU_DICHVU', index: 'SO_PHIEU_DICHVU', width: 400, hidden: true},
                    {label: "MA_DV",name: 'MA_DV', index: 'MA_DV', width: 400, hidden: true},
                    {label: "HINHANH",name: 'HINHANH', index: 'HINHANH', width: 400, hidden: true},
                    {label: "Hình ảnh",name: 'HINHANH_HT', index: 'HINHANH_HT', width: 400, formatter: function (cellvalue, options, rowObject) {
                            return "<img src='" + rowObject.HINHANH + "' style='width: 100px; height: 100px;'>";
                        }
                    },
                    {label: "dvtt",name: 'DVTT', index: 'DVTT', hidden: true},
                    {label: "makhambenh",name: 'MA_KHAM_BENH', index: 'MA_KHAM_BENH', hidden: true},
                    {label: "Tập tin",name: 'TEN', index: 'TEN', width: 300, hidden: true},
                    {
                        label: "Tải ảnh",
                        name: 'DUONGDAN_HT',
                        index: 'DUONGDAN_HT',
                        align: "center",
                        width: 150,
                        formatter: function (cellvalue, options, rowObject) {
                            return "<a target='_blank' href='" + cellvalue + "'>Tải</a>";
                        }
                    },
                    {label: "path",name: 'DUONGDAN', index: 'DUONGDAN', hidden: true}
                ],
                caption: "Hình ảnh",
                ignoreCase: true,
                rowNum: 1000000,
                onRightClickRow: function(id) {
                    if (id) {

                        $.contextMenu({
                            selector: '#'+idList+' tr',
                            reposition : false,
                            callback: function (key, options) {
                                var rowData = getThongtinRowSelected(idList);
                                if (key == "xoa") {
                                    confirmToClient(MESSAGEAJAX.CONFIRM, function() {
                                        var id = list.jqGrid("getGridParam", "selrow");
                                        list.jqGrid('delRowData',id);
                                    })
                                }
                                if (key == "sua") {
                                    $("#modalPhauThuatTuongtrinhPTHinhanh").modal("show");
                                    addTextTitleModal("titleModalPhauThuatTuongtrinhPTHinhanh", "Hình ảnh")
                                    $("#phauthuat_tuongtrinh_sttfile").val(rowData.STT)
                                    painterObject.changeImageSrc(rowData.HINHANH, idCanvas)
                                }
                            },
                            items: {
                                "sua": {name: '<p><i class="fa fa-pencil-square-o text-primary" aria-hidden="true"></i> Sửa</p>'},
                                "xoa": {name: '<p><i class="fa fa-trash-o text-danger" aria-hidden="true"></i> Xóa</p>'}
                            }
                        });

                    }
                }
            });
        }
    }
    function loadDSHinhanh() {
        var rowSelected = lanPhauThuatObject.CHITIETDVKT
        $("#phauthuat_tuongtrinh_dsanh").jqGrid('setGridParam', {
            datatype: 'json',
            url: "ttpt_select_files",
            postData: {
                soPhieu: rowSelected.SO_PHIEU_DICHVU,
                maDichVu: rowSelected.MA_DV,
                noiTru: 1,
                maKhamBenh: "",
                sttBenhAn: thongtinhsba.thongtinbn.STT_BENHAN,
                sttDieuTri: lanPhauThuatTDTObject.STT_DIEUTRI,
                sttDotDieuTri: lanPhauThuatTDTObject.STT_DOTDIEUTRI
            }
        }).trigger('reloadGrid');
    }

    function luuHinhAnhTuongTrinh(idButton) {
        var ret = lanPhauThuatObject.CHITIETDVKT
        var dataSubmit = convertDataFormToJson("phauthuatTuongtrinhForm");
        var catchisau7ngay = '';
        var trinhtupttt = CKEDITOR.instances['phauthuat_tuongtrinh_noidung'].getData().trim();
        var trinhtupttt_xml5 = decodeHTMLEntities($('<textarea />').html(trinhtupttt).text());
        $.post('cmu_post', {
            url: [
                ret.SO_PHIEU_DICHVU,
                singletonObject.dvtt,
                ret.MA_DV,
                dataSubmit.TENCHANDOAN_SAUPT,
                thongtinhsba.thongtinbn.STT_BENHAN,
                lanPhauThuatTDTObject.STT_DOTDIEUTRI,
                lanPhauThuatTDTObject.STT_DIEUTRI,
                lanPhauThuatObject.PHUONG_PHAP_VO_CAM,
                $("#phauthuat_tuongtrinh_pppt").val().join(";") ,// ret.TEN_DV,
                trinhtupttt,
                catchisau7ngay,
                lanPhauThuatObject.BAT_DAU_MO+":00",
                dataSubmit.TAIBIEN,
                dataSubmit.TUVONG,
                lanPhauThuatTDTObject.SOVAOVIEN,
                lanPhauThuatTDTObject.SOVAOVIEN_DT,
                singletonObject.userId,
                '',
                '',
                trinhtupttt_xml5,
                thongtinhsba.thongtinbn.MA_BENH_NHAN,
                ret.PHONG_CHI_DINH,
                singletonObject.maphongbenh,
                dataSubmit.PP_VO_CAM,
                lanPhauThuatObject.KET_THUC_MO+":00",
                dataSubmit.TENCHANDOAN_TRUOCPT,
                dataSubmit.DAN_LUU,
                dataSubmit.BAC,
                dataSubmit.NGAY_CAT_CHI,
                dataSubmit.NGAY_RUT,
                dataSubmit.KHAC,
                lanPhauThuatObject.ID,
                $("#phauthuat_sttMayTTPT").val(),
                $("#phauthuat_tuongtrinh_cachthupt").val(),
                "CLS_TTPT_VLTL_CN_KQ_F_CMU_V5"
            ].join('```')
        }).done(function (dt) {
            if (dt > 0) {
                notifiToClient("Green", "Lưu thành công")
                var arrSubmit = [];
                var dataPhauThuatKhac = {};
                var dataPhauThuatKhac_ThongTinChung = {};
                if ($("#phauthuat_tuongtrinh_loaituongtrinh").val() != 'phieuphauthuat') {
                    dataPhauThuatKhac = formPhauThuatKhac.submission.data;
                    dataPhauThuatKhac_ThongTinChung = formPhauThuatKhac_ThongTinChung.submission.data;
                    var trinhTuXML5Arr = findComponentDataByKey(formPhauThuatKhac);
                    arrSubmit = [
                        ret.SO_PHIEU_DICHVU,
                        singletonObject.dvtt,
                        ret.MA_DV,
                        thongtinhsba.thongtinbn.STT_BENHAN,
                        lanPhauThuatTDTObject.STT_DOTDIEUTRI,
                        lanPhauThuatTDTObject.STT_DIEUTRI,
                        lanPhauThuatTDTObject.SOVAOVIEN,
                        lanPhauThuatTDTObject.SOVAOVIEN_DT,
                        JSON.stringify(dataPhauThuatKhac_ThongTinChung), // json thong tin chung
                        $("#phauthuat_tuongtrinh_loaituongtrinh").val(), // Loai tuong trinh
                        getValueOfFormIO(formPhauThuatKhac_ThongTinChung.getComponent('CHANDOAN')), // Chan doan
                        getValueOfFormIO(formPhauThuatKhac_ThongTinChung.getComponent('TENCHANDOAN_TRUOCPT')), // Chan doan truoc PT
                        getValueOfFormIO(formPhauThuatKhac_ThongTinChung.getComponent('PHUONGPHAP_TT_PT')), // Phuong phap phau thuat
                        getValueOfFormIO(formPhauThuatKhac_ThongTinChung.getComponent('PHUONGPHAP_VOCAM')), // Phuong phap vo cam
                        getValueOfFormIO(formPhauThuatKhac_ThongTinChung.getComponent('LOAITHUOCTE')), // Loai thuoc te
                        JSON.stringify(dataPhauThuatKhac), // json trinh tu phau thuat
                        trinhTuXML5Arr.join("\n"),
                        $("#phauthuat_tuongtrinh_loaituongtrinh").val()
                    ]
                } else {
                    arrSubmit = [
                        ret.SO_PHIEU_DICHVU,
                        singletonObject.dvtt,
                        ret.MA_DV,
                        thongtinhsba.thongtinbn.STT_BENHAN,
                        lanPhauThuatTDTObject.STT_DOTDIEUTRI,
                        lanPhauThuatTDTObject.STT_DIEUTRI,
                        lanPhauThuatTDTObject.SOVAOVIEN,
                        lanPhauThuatTDTObject.SOVAOVIEN_DT,
                        JSON.stringify(dataPhauThuatKhac_ThongTinChung), // json thong tin chung
                        $("#phauthuat_tuongtrinh_loaituongtrinh").val(), // Loai tuong trinh
                        dataSubmit.TENCHANDOAN_SAUPT, // Chan doan
                        dataSubmit.TENCHANDOAN_TRUOCPT, // Chan doan truoc PT
                        $("#phauthuat_tuongtrinh_pppt").val() ? $("#phauthuat_tuongtrinh_pppt").val().join(";") : '', // Phuong phap phau thuat
                        lanPhauThuatObject.PHUONG_PHAP_VO_CAM, // Phuong phap vo cam
                        $("#phauthuat_tuongtrinh_loaituongtrinh").val() != 'phieuphauthuat'?
                            getValueOfFormIO(formPhauThuatKhac_ThongTinChung.getComponent('LOAITHUOCTE')): "", // Loai thuoc te
                        JSON.stringify(dataPhauThuatKhac), // json trinh tu phau thuat
                        trinhtupttt_xml5,
                        $("#phauthuat_tuongtrinh_loaituongtrinh").val()
                    ]
                }
                $.post('cmu_post_CMU_UPDATE_PHAUTHUATKHAC', {
                    url: arrSubmit.join('```')
                }).done(function (dt2) {

                });
                $("#lanphauthuat_tuongtrinhpt_xoa").show();
                $("#lanphauthuat_tuongtrinhpt_xem").show();
                $("#lanphauthuat_tuongtrinhpt_kyso").show()
                $("#lanphauthuat_tuongtrinhpt_huykyso").hide()
                $("#phauthuatTuongtrinhForm .show-created").show();
                $("#phauthuat_tuongtrinh_nguoithuchien").val(singletonObject.userId)
                $("#phauthuat_tuongtrinh_tennguoithuchien").val(singletonObject.user)
                $.ajax({
                    url:"cmu_post_CMU_IMAGES_DVKT_DELALL",
                    method: "POST",
                    data: {
                        url: [singletonObject.dvtt, thongtinhsba.thongtinbn.SOVAOVIEN, ret.SO_PHIEU_DICHVU, ret.MA_DV].join("```")
                    },
                    async: false
                })
                var allHinhanh = getAllRowDataJqgrid("phauthuat_tuongtrinh_dsanh")

                for(var i = 0; i < allHinhanh.length; i++) {
                    $.ajax({
                        url: "ttpt_insert_file",
                        method: "POST",
                        data: {
                            soPhieu: ret.SO_PHIEU_DICHVU,
                            maDichVu: ret.MA_DV,
                            noiTru: 1,
                            maKhamBenh: "",
                            sttBenhAn: thongtinhsba.thongtinbn.STT_BENHAN,
                            sttDieuTri: lanPhauThuatTDTObject.STT_DIEUTRI,
                            sttDotDieuTri: lanPhauThuatTDTObject.STT_DOTDIEUTRI,
                            hinhAnh: allHinhanh[i].HINHANH
                        },
                        dataType: 'json',
                        timeout: 10000,
                        async: false,
                    });
                }
                loadDSHinhanh();
            } else {
                notifiToClient("Red", MESSAGEAJAX.ERROR)
            }
        }).fail(function () {
            notifiToClient("Red", MESSAGEAJAX.ERROR)
        }).always(function() {
            hideSelfLoading(idButton)
        })
    }

    function luuEkipTuongtrinhTTPT(idButton, ekipBSPT, ekipBSGM, objectEkip, ekipArr) {
        var ret = getThongtinRowSelected("lanphauthuat_dvkt_list_phieuttpt")
        var dathuchien =  $.ajax({
            url: "cmu_post_CMU_KTRADVKT_DATH",
            method: "POST",
            data: {
                url: [singletonObject.dvtt, thongtinhsba.thongtinbn.STT_BENHAN, lanPhauThuatTDTObject.STT_DOTDIEUTRI, ret.SO_PHIEU_DICHVU, ret.MA_DV].join("```")
            },
            async: false
        }).responseText;
        if(dathuchien == "1") {
            hideSelfLoading(idButton)
            return notifiToClient("Red", "Đã thực hiện tường trình không thể chỉnh sửa")
        }
        $.post('cmu_post_CMU_TTPT_EKIP_INS_V2', {
            url: [
                ret.SO_PHIEU_DICHVU,
                singletonObject.dvtt,
                ret.MA_DV,
                thongtinhsba.thongtinbn.STT_BENHAN,
                lanPhauThuatTDTObject.STT_DOTDIEUTRI,
                lanPhauThuatTDTObject.STT_DIEUTRI,
                ekipBSPT,
                ekipBSGM,
                lanPhauThuatTDTObject.SOVAOVIEN,
                lanPhauThuatTDTObject.SOVAOVIEN_DT,
                objectEkip.BSPHAUTHUAT,
                objectEkip.BSGAYME,
                objectEkip.KTVGAYMETE,
                objectEkip.PHUMO1,
                objectEkip.PHUMO2,
                objectEkip.DUNGCU1,
                objectEkip.DUNGCU2,
                $("#phauthuat_tuongtrinh_tinhtienekip").val(),
                thongtinhsba.thongtinbn.MA_BENH_NHAN,
                ret.PHONG_CHI_DINH,
                singletonObject.maphongbenh,
                objectEkip.PHUMO3,
                objectEkip.PHUMO4,
                lanPhauThuatObject.KET_THUC_MO+":00",
                lanPhauThuatObject.ID

            ].join('```')
        }).done(function (dt) {
            if (dt > 0) {
                notifiToClient("Green", "Lưu thành công")
                $.post("cmu_post", {
                    url: [singletonObject.dvtt,
                        lanPhauThuatObject.ID,
                        JSON.stringify(ekipArr),
                        ret.SO_PHIEU_DICHVU,
                        ret.MA_DV,
                        singletonObject.userId,
                        'CMU_LANPHAUTHUAT_EKIP_DVKT_INS'
                    ].join("```")
                }).done(function() {
                    loadDSEkip()
                });
                $("#lanphauthuat_tuongtrinhpt_xoaekip_dvkt").show()

            } else {
                notifiToClient("Red", MESSAGEAJAX.ERROR)
            }
        }).fail(function () {
            notifiToClient("Red", MESSAGEAJAX.ERROR)
        }).always(function() {
            hideSelfLoading(idButton)
        })
    }

    function getThongtinTTPT(rowSelected, callback) {
        var arr = [
            rowSelected.SO_PHIEU_DICHVU,
            1,
            thongtinhsba.thongtinbn.STT_BENHAN,
            lanPhauThuatTDTObject.STT_DOTDIEUTRI,
            rowSelected.STT_DIEUTRI,
            '',
            rowSelected.MA_DV,
            singletonObject.dvtt, "0",
            0,
            rowSelected.SOVAOVIEN,
            rowSelected.SOVAOVIEN_DT];
        var url3 = "pttt_select_ketqua_svv?url=" + convertArray(arr);
        $.getJSON(url3, function (result) {
            callback(result);
            $("#phauthuat_sttMayTTPT").val(result[0].STT_MAMAY).change();
        })
    }


    function phauthuat_luu_vitrittpt(idButton){
        var textViTri = $("#phauthuat_vitrittpt").val();
        var maViTri = $("#phauthuat_vitrittpt_ma").val();
        var tenViTri = $("#phauthuat_vitrittpt_ten").val();
        var enableAlert = maViTri == undefined || textViTri != tenViTri;
        if (enableAlert){
            confirmToClient("Chỉ lưu tên vị trí và lưu không theo danh mục?", function() {
                $.ajax({
                    url: "noittru_ttpt_update_vitri_thoigian",
                    method: "POST",
                    data: {
                        ma_dichvu: lanPhauThuatObject.CHITIETDVKT.MA_DV,
                        sophieu_thuthuatphauthuat: lanPhauThuatObject.CHITIETDVKT.SO_PHIEU_DICHVU,
                        dvtt: singletonObject.dvtt,
                        vitrittpt: textViTri,
                        thoigianttpt: $("#phauthuat_thoigianttpt").val(),
                        mabenhnhan: thongtinhsba.thongtinbn.MA_BENH_NHAN,
                        sovaovien: thongtinhsba.thongtinbn.SOVAOVIEN,
                        sovaovien_dt: thongtinhsba.thongtinbn.SOVAOVIEN_DT,
                        idvitrittpt: enableAlert?null:maViTri
                    },
                    async: true,
                    dataType: 'json',
                    // timeout: 100000,
                    success: function (data) {
                        luuHinhAnhTuongTrinh(idButton);
                    },
                    error: function (e) {
                        hideSelfLoading(idButton)
                        notifiToClient("Red", MESSAGEAJAX.ERROR)
                    }
                });
            }, function () {
            })
        } else{
            $.ajax({
                url: "noittru_ttpt_update_vitri_thoigian",
                method: "POST",
                data: {
                    ma_dichvu: lanPhauThuatObject.CHITIETDVKT.MA_DV,
                    sophieu_thuthuatphauthuat: lanPhauThuatObject.CHITIETDVKT.SO_PHIEU_DICHVU,
                    dvtt: singletonObject.dvtt,
                    vitrittpt: textViTri,
                    thoigianttpt: $("#phauthuat_thoigianttpt").val(),
                    mabenhnhan: thongtinhsba.thongtinbn.MA_BENH_NHAN,
                    sovaovien: thongtinhsba.thongtinbn.SOVAOVIEN,
                    sovaovien_dt: thongtinhsba.thongtinbn.SOVAOVIEN_DT,
                    idvitrittpt: enableAlert?null:maViTri
                },
                async: true,
                dataType: 'json',
                success: function (data) {
                    luuHinhAnhTuongTrinh(idButton);
                },
                error: function (e) {
                    hideSelfLoading(idButton)
                    notifiToClient("Red", MESSAGEAJAX.ERROR)
                }
            });
        }
    }

    initHTMLCanvas();

    function createFormTrinhTuPhauThuatKhacFormIO(obj){
        var jsonFormTrinhTu;
        var jsonFormThongTinChung;
        switch(obj.LOAI_TUONGTRINH) {
            case 'thethuytinh':
                jsonFormTrinhTu = phieuTuongTrinhTheThuyTinh.trinhTuPhauThuat;
                jsonFormThongTinChung = phieuTuongTrinhTheThuyTinh.thongTinChung;
                break;
            case 'phauthuatmong':
                jsonFormTrinhTu = phieuTuongTrinhMong.trinhTuPhauThuat;
                jsonFormThongTinChung = phieuTuongTrinhMong.thongTinChung;
                break;
            case 'phauthuatsupmi':
                jsonFormTrinhTu = phieuTuongTrinhSupMi.trinhTuPhauThuat;
                jsonFormThongTinChung = phieuTuongTrinhSupMi.thongTinChung;
                break;
            case 'phauthuattuile':
                jsonFormTrinhTu = phieuTuongTrinhTuiLe.trinhTuPhauThuat;
                jsonFormThongTinChung = phieuTuongTrinhTuiLe.thongTinChung;
                break;
            case 'phauthuatsapejko':
                jsonFormTrinhTu = phieuTuongTrinhSapejko.trinhTuPhauThuat;
                jsonFormThongTinChung = phieuTuongTrinhSapejko.thongTinChung;
                break;
            case 'phauthuatlac':
                jsonFormTrinhTu = phieuTuongTrinhLac.trinhTuPhauThuat;
                jsonFormThongTinChung = phieuTuongTrinhLac.thongTinChung;
                break;
            case 'phauthuatglocom':
                jsonFormTrinhTu = phieuTuongTrinhGlocom.trinhTuPhauThuat;
                jsonFormThongTinChung = phieuTuongTrinhGlocom.thongTinChung;
                break;
            case 'phauthuatbematnhancau':
                jsonFormTrinhTu = phieuTuongTrinhBeMatNhanCau.trinhTuPhauThuat;
                jsonFormThongTinChung = phieuTuongTrinhBeMatNhanCau.thongTinChung;
                break;
            case 'phauthuatghepgiacmac':
                jsonFormTrinhTu = phieuTuongTrinhGhepGiacMac.trinhTuPhauThuat;
                jsonFormThongTinChung = phieuTuongTrinhGhepGiacMac.thongTinChung;
                break;
            default:
        }
        Formio.createForm(document.getElementById('formTuongTrinhKhac'),
            jsonFormTrinhTu,{}
        ).then(function(form) {
            formPhauThuatKhac = form;
            formPhauThuatKhac.submission = {
                data: JSON.parse(obj.JSON_TUONGTRINH)
            }
        });

        Formio.createForm(document.getElementById('formTuongTrinhKhac_ThongTinChung'),
            jsonFormThongTinChung,{}
        ).then(function(form) {
            formPhauThuatKhac_ThongTinChung = form;
            var loaiPT;
            if ($("#phauthuat_tuongtrinh_loaituongtrinh").val() == 'phauthuatmong'){
                loaiPT = "MONG";
            } else if ($("#phauthuat_tuongtrinh_loaituongtrinh").val() == 'phauthuatsupmi'){
                loaiPT = "SUPMI";
            } else if ($("#phauthuat_tuongtrinh_loaituongtrinh").val() == 'phauthuattuile'){
                loaiPT = "TUILE";
            } else if ($("#phauthuat_tuongtrinh_loaituongtrinh").val() == 'phauthuatsapejko'){
                loaiPT = "SAPEJKO";
            } else if ($("#phauthuat_tuongtrinh_loaituongtrinh").val() == 'phauthuatbematnhancau'){
                loaiPT = "BEMATNC";
            } else if ($("#phauthuat_tuongtrinh_loaituongtrinh").val() == 'phauthuatghepgiacmac'){
                loaiPT = "GHEPGM";
            } else if ($("#phauthuat_tuongtrinh_loaituongtrinh").val() == 'phauthuatlac'){
                loaiPT = "LAC";
            }

            if (loaiPT){
                var tenBenhchinhElement = form.getComponent('TENCHANDOAN_TRUOCPT_' + loaiPT);
                var icdBenhchinhElement = form.getComponent('CHANDOAN_TRUOCPT_' + loaiPT);
                $("#"+getIdElmentFormio(form,'CHANDOAN_TRUOCPT_' + loaiPT)).on('keypress', function(event) {
                    var mabenhICD = $(this).val().trim();
                    if(event.keyCode == 13 && mabenhICD != "") {
                        mabenhICD = mabenhICD.toUpperCase();
                        getMotabenhly(mabenhICD, function(data) {
                            var splitIcd = data.split("!!!")
                            tenBenhchinhElement.setValue(splitIcd[1]);
                            icdBenhchinhElement.setValue(mabenhICD)
                        })
                    }
                })
                combgridTenICD(getIdElmentFormio(form,'TENCHANDOAN_TRUOCPT_' + loaiPT), function(item) {
                    icdBenhchinhElement.setValue(item.ICD);
                    tenBenhchinhElement.setValue(item.MO_TA_BENH_LY);
                });
            }

            formPhauThuatKhac_ThongTinChung.submission = {
                data: JSON.parse(obj.JSON_THONGTINCHUNG)
            }
        });
    }

    function checkKysoPTTT(){
        // var rowSelected = getThongtinRowSelected("lanphauthuat_dvkt_list_phieuttpt");
        var ret = lanPhauThuatObject.CHITIETDVKT
        var tenpt;
        if ($("#phauthuat_tuongtrinh_loaituongtrinh").val() == 'phauthuatmong'){
            tenpt = "_MONG"
        } else if ($("#phauthuat_tuongtrinh_loaituongtrinh").val() == 'phauthuatsupmi'){
            tenpt = "_SUPMI"
        } else if ($("#phauthuat_tuongtrinh_loaituongtrinh").val() == 'phauthuattuile'){
            tenpt = "_TUILE"
        } else if ($("#phauthuat_tuongtrinh_loaituongtrinh").val() == 'phauthuatsapejko'){
            tenpt = "_SAPEJKO"
        } else if ($("#phauthuat_tuongtrinh_loaituongtrinh").val() == 'phauthuatlac'){
            tenpt = "_LAC"
        } else if ($("#phauthuat_tuongtrinh_loaituongtrinh").val() == 'phauthuatglocom'){
            tenpt = "_GLOCOM"
        } else if ($("#phauthuat_tuongtrinh_loaituongtrinh").val() == 'phauthuatbematnhancau'){
            tenpt = "_BEMATNHANCAU"
        } else if ($("#phauthuat_tuongtrinh_loaituongtrinh").val() == 'phauthuatghepgiacmac'){
            tenpt = "_GHEPGIACMAC"
        } else if ($("#phauthuat_tuongtrinh_loaituongtrinh").val() == 'thethuytinh'){
            tenpt = "_THETHUYTINH"
        } else {
            tenpt = ""
        }
        getFilesign769(
            "PHIEU_NOITRU_TRUONGTRINHPT" + tenpt,
            ret.MA_DV,
            -1,//singletonObject.userId,
            singletonObject.dvtt,
            thongtinhsba.thongtinbn.SOVAOVIEN,
            thongtinhsba.thongtinbn.SOVAOVIEN_DT,
            -1,
            function(data) {
                if (data.length > 0) {
                    $("#lanphauthuat_tuongtrinhpt_kyso").hide()
                    $("#lanphauthuat_tuongtrinhpt_luu").hide()
                    $("#lanphauthuat_tuongtrinhpt_xoa").hide()
                    $("#lanphauthuat_tuongtrinhpt_huykyso").show()
                } else {
                    $("#lanphauthuat_tuongtrinhpt_kyso").show()
                    $("#lanphauthuat_tuongtrinhpt_luu").show()
                    $("#lanphauthuat_tuongtrinhpt_xoa").show()
                    $("#lanphauthuat_tuongtrinhpt_huykyso").hide()
                }
                $("#lanphauthuat_tuongtrinhpt_xem").show()
            })
    }

    function reloadDSHinhAnhTT(){
        var url = "cmu_getlist?url=" + convertArray([singletonObject.dvtt, singletonObject.makhoa, "CMU_GET_THUVIENANH"]);
        $("#list_hinhanhtuongtrinh").jqGrid('setGridParam', {
            datatype: 'json',
            url: url
        }).trigger('reloadGrid')
    }

    function instanceGridHinhAnhTT(){
        if (!$("#list_hinhanhtuongtrinh")[0].grid) {
            $("#list_hinhanhtuongtrinh").jqGrid({
                datatype: "local",
                loadonce: false,
                height: 400,
                width: null,
                shrinkToFit: false,
                colModel: [
                    {label: 'ID',name: 'ID', index: 'ID', width: 50, align: 'center'},
                    {label: 'Tên',name: 'TEN', index: 'TEN', width: 200, align: 'center'},
                    {label: 'BASE64',name: 'BASE64', index: 'BASE64', width: 250, align: 'center', hidden: true},
                    {label: 'Ngày tạo',name: 'NGAY_TAO_PHIEU', index: 'NGAY_TAO_PHIEU', width: 250, align: 'center'},
                    {label: "Hình ảnh",name: 'HINHANH', index: 'HINHANH', width: 400, formatter: function (cellvalue, options, rowObject) {
                            return "<img src='" + rowObject.BASE64 + "' style='width: 100px; height: 100px;'>";
                        }
                    },
                    {label: 'Người tạo',name: 'TENNGUOITAO', index: 'TENNGUOITAO', width: 250, align: 'center'},
                    {
                        label: "Tải",
                        name: 'DOWNLOAD',
                        index: 'DOWNLOAD',
                        align: "center",
                        width: 150,
                        formatter: function (cellvalue, options, rowObject) {
                            return "<a target='_blank' href='" + rowObject.BASE64 + "'>Tải</a>";
                        }
                    },
                ],
                rowNum: 1000000,
                caption: "",
                onRightClickRow: function (id1) {
                    if (id1) {
                        var ret = getThongtinRowSelected("list_hinhanhtuongtrinh");
                        var items = {
                            "chon": {name: '<p><i class="fa fa-check-square-o text-primary" aria-hidden="true"></i> Chọn</p>'},
                            "xem": {name: '<p><i class="fa fa-eye text-primary" aria-hidden="true"></i> Xem</p>'},
                            "xoa": {name: '<p><i class="fa fa-trash-o red-text text-danger" aria-hidden="true"></i> Xóa</p>'},
                        }

                        $.contextMenu('destroy', '#list_hinhanhtuongtrinh tr');
                        $.contextMenu({
                            selector: '#list_hinhanhtuongtrinh tr',
                            callback: function (key, options) {
                                if (key == "chon") {
                                    if ($("#modalPhauThuatTuongtrinhPTHinhanh").hasClass('show')) {
                                        painterObject.changeImageSrc(ret.BASE64, idCanvas);
                                    } else {
                                        var jqGrid = "phauthuat_tuongtrinh_dsanh";
                                        if (lanPhauThuatObject.TYPE && lanPhauThuatObject.TYPE == "TT") {
                                            jqGrid = "thuthuat_tuongtrinh_dsanh";
                                        }
                                        var allData = getAllRowDataJqgrid(jqGrid);

                                        var sttAnh =  $("#phauthuat_tuongtrinh_sttfile").val();
                                        if(allData.length < 2 || sttAnh) {
                                            var list = $("#" + jqGrid);
                                            var ret2 = lanPhauThuatObject.CHITIETDVKT
                                            var maxlength = 0;
                                            allData.forEach(function(item) {
                                                if (item.STT == sttAnh) {
                                                    item.HINHANH = ret.BASE64
                                                    item.DUONGDAN_HT = ret.BASE64
                                                }
                                                if(item.STT > maxlength) {
                                                    maxlength = item.STT
                                                }

                                            });
                                            if(!sttAnh) {
                                                allData.push({
                                                    STT: maxlength+1,
                                                    SO_PHIEU_DICHVU: ret2.SO_PHIEU_DICHVU,
                                                    MA_DV: ret2.MA_DV,
                                                    DUONGDAN_HT: ret.BASE64,
                                                    HINHANH: ret.BASE64
                                                })
                                            }

                                            list.jqGrid('setGridParam', { data: allData});
                                            list[0].grid.endReq();
                                            list.trigger('reloadGrid');
                                        } else {
                                            notifiToClient("Red", "Chỉ được thêm 2 hình ảnh")
                                        }
                                    }

                                    $("#modalDSHinhAnhTuongTrinh").modal("hide");
                                }
                                if (key == "xem") {
                                    $("#modalHienThiHinhAnh").modal("show");
                                    $('#modalImage').attr('src', ret.BASE64);
                                }
                                if (key == "xoa") {
                                    deleteHinhAnhTT(ret)
                                }
                            },
                            items: items
                        });
                    }
                }

            });
            $("#list_hinhanhtuongtrinh").jqGrid('filterToolbar', {stringResult: true, searchOnEnter: false, defaultSearch: "cn"});
        }
    }

    function deleteHinhAnhTT(ret){
        var maGiay = ret.ID;
        confirmToClient("Bạn có chắc chắn muốn xóa hình ảnh này?", function() {
            var arr = [maGiay, singletonObject.dvtt]
            var url = "cmu_post_HINHANHTT_DELETE";
            $.post(url, {
                url: arr.join("```")
            }).done(function (data) {
                if (data === "1") {
                    reloadDSHinhAnhTT();
                    notifiToClient("Green", MESSAGEAJAX.DEL_SUCCESS)

                } else {
                    notifiToClient("Red", MESSAGEAJAX.ERROR)
                }
            }).fail(function() {
                notifiToClient("Red", MESSAGEAJAX.ERROR)
            })
        }, function () {

        })
    }
})