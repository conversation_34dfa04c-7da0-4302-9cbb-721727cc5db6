var tdtSoluongThuocTruoc = 0;
function initToathuocnoitru() {
    $("#tdt_nghiepvutoathuoc").val(singletonObject.bant == 1? "ba_ngoaitru_toathuoc": "noitru_toathuoc")
    loadKhoThuocvattu(singletonObject.danhsachkhothuocBHYT)
    initCombogrid();
}

function loadKhoThuocvattu(data) {
    $("#tdt_khothuoc").html("")
    data.forEach(function(data) {
        $("#tdt_khothuoc").append("<option value="+data.MAKHO+">"+data.TENKHO+"</option>")
    })
}

function initGridThuocTdt() {
    var listToathuoc = $("#tdt_list_thuoc");
    if(!listToathuoc[0].grid) {
        listToathuoc.jqGrid({
            url: '',
            datatype: "local",
            loadonce: true,
            height: 250,
            width: null,
            shrinkToFit: false,
            colModel: [
                {label: "Nghiệp vụ",name: 'MOTA_NGHIEPVU', index: 'MOTA_NGHIEPVU', width: 120,
                    cellattr: function (rowId, tv, rawObject, cm, rdata) {
                        return 'style="white-space: normal;"';
                    },
                    formatter: function (cellvalue, options, rowObject) {
                        return '<div class="cellWithoutBackground" style="font-weight: bold;color:' + getColorByNghiepvu(rowObject.NGHIEP_VU) + '">' + cellvalue + '</div>';
                    }
                },
                {label:  "Mã ", name: 'MAVATTU', index: 'MAVATTU', width: 50 },
                {label: "stt_toathuoc", name: 'STT_TOATHUOC', index: 'STT_TOATHUOC', hidden: true},
                {label: "MA_TOA_THUOC", name: 'MA_TOA_THUOC', index: 'MA_TOA_THUOC', hidden: true},
                {label: "Tên thương mại", name: 'TEN_VAT_TU', index: 'TEN_VAT_TU', width: 200 ,
                    cellattr: function (rowId, tv, rawObject, cm, rdata) {
                        return 'style="white-space: normal;"';
                    }
                },
                {label: "Tên gốc-Hoạt chất", name: 'HOAT_CHAT', index: 'HOAT_CHAT', width: 100},
                {label: "ĐVT", name: 'DVT', index: 'DVT', width: 50},
                {label: "Ngày y lệnh", name: 'NGAY_YL', index: 'NGAY_YL', width: 150},
                {
                    label: "Số ngày",
                    name: 'SO_NGAY_UONG',
                    align: 'center',
                    index: 'SO_NGAY_UONG',
                    width: 60,
                    editable: true,
                    edittype: 'custom',
                    editoptions: {custom_element: myelem, custom_value: myvalue}
                },
                {
                    label: "Y lệnh giờ",
                    name: 'YLENH',
                    index: 'YLENH',
                    width: 60,
                },
                {
                    label: "Sáng",
                    name: 'SANG_UONG',
                    align: 'center',
                    index: 'SANG_UONG',
                    width: 60,
                    editable: true,
                    edittype: 'custom',
                    editoptions: {custom_element: myelem, custom_value: myvalue}
                },
                {
                    label: "Trưa",
                    name: 'TRUA_UONG',
                    align: 'center',
                    index: 'TRUA_UONG',
                    width: 60,
                    editable: true,
                    edittype: 'custom',
                    editoptions: {custom_element: myelem, custom_value: myvalue}
                },
                {
                    label: "Chiều",
                    name: 'CHIEU_UONG',
                    align: 'center',
                    index: 'CHIEU_UONG',
                    width: 60,
                    editable: true,
                    edittype: 'custom',
                    editoptions: {custom_element: myelem, custom_value: myvalue}
                },
                {
                    label:  "Tối",
                    name: 'TOI_UONG',
                    align: 'center',
                    index: 'TOI_UONG',
                    width: 60,
                    editable: true,
                    edittype: 'custom',
                    editoptions: {custom_element: myelem, custom_value: myvalue}
                },
                {
                    label:  "Số lượng",
                    name: "SO_LUONG",
                    align: 'center',
                    index: "SO_LUONG",
                    width: 60,
                    editable: true,
                    edittype: 'custom',
                    editoptions: {custom_element: myelem, custom_value: myvalue}
                },
                {label:  "Kho",
                    name: 'TENKHOVATTU', index: 'TENKHOVATTU', width: 100
                },
                {
                    label: "Dạng thuốc",
                    name: 'CACH_SU_DUNG',
                    index: 'CACH_SU_DUNG',
                    width: 100,
                    editable: true,
                    edittype: 'text',
                    cellattr: function (rowId, tv, rawObject, cm, rdata) {
                        return 'style="white-space: normal;"';
                    }
                },
                {
                    label: "Cách dùng",
                    name: 'GHI_CHU_CT_TOA_THUOC',
                    index: 'GHI_CHU_CT_TOA_THUOC',
                    width: 160,
                    editable: true,
                    edittype: 'text',
                    cellattr: function (rowId, tv, rawObject, cm, rdata) {
                        return 'style="white-space: normal;"';
                    }
                },
                {   label:  "Đơn giá",
                    name: 'DONGIA_BAN_BH',
                    align: 'right',
                    formatter: "integer",
                    formatoptions: {decimalSeparator: ".", thousandsSeparator: ","},
                    index: 'DONGIA_BAN_BH',
                    width: 60
                },
                {
                    label:  "Thành tiền",
                    name: 'THANHTIEN_THUOC',
                    align: 'right',
                    formatter: "integer",
                    formatoptions: {decimalSeparator: ".", thousandsSeparator: ","},
                    index: 'THANHTIEN_THUOC',
                    width: 60
                },
                {label: "Người tạo", name: 'TEN_NHANVIEN', index: 'TEN_NHANVIEN', width: 120},
                {label: "", name: 'ACT', index: 'ACT', width: 120},
                {label: "TU_TU_THUOC",name: 'TU_TU_THUOC', index: 'TU_TU_THUOC', width: 50, hidden: true},
                {label: "NGHIEP_VU",name: 'NGHIEP_VU', index: 'NGHIEP_VU', width: 50, hidden: true},
                {label: "makhovattu",name: 'MAKHOVATTU', index: 'MAKHOVATTU', hidden: true},
                {label: "STT_DIEUTRI",name: 'STT_DIEUTRI', index: 'STT_DIEUTRI', hidden: true},
                {label: "MA_BAC_SI_THEMTHUOC",name: 'MA_BAC_SI_THEMTHUOC', index: 'MA_BAC_SI_THEMTHUOC', hidden: true},
                {label: "NGUOITAO",name: 'NGUOITAO', index: 'NGUOITAO', hidden: true},
                {label:  "dongia_bv", name: 'DONGIA_BAN_BV', index: 'DONGIA_BAN_BV', hidden: true},
                {label:  "SOLOSANXUAT", name: 'SOLOSANXUAT', index: 'SOLOSANXUAT', hidden: true},
                {label:  "DUNGTICH_SUDUNG", name: 'DUNGTICH_SUDUNG', index: 'DUNGTICH_SUDUNG', hidden: true},
                {label:  "DUNGTICH_DICHTRUYEN", name: 'DUNGTICH_DICHTRUYEN', index: 'DUNGTICH_DICHTRUYEN', hidden: true},
                {label:  "MAVATTU_MUANGOAI", name: 'MAVATTU_MUANGOAI', index: 'MAVATTU_MUANGOAI', hidden: true}
            ],
            sortname: 'TEN_VAT_TU',
            sortorder: "asc",
            rowNum: 1000000,
            caption: "",
            onCellSelect: function (rowid, iCol, content, e) {
                if (rowid) {
                    var ret = listToathuoc.jqGrid('getRowData', rowid);
                    tdtSoluongThuocTruoc = ret.SO_LUONG;
                }
            },
            cellEdit: true,
            cellsubmit: 'clientArray',
            afterSaveCell: function (rowid, name, val, iRow, iCol) {
                console.log("iRow", iRow)
                if (name === 'GHI_CHU_CT_TOA_THUOC') {
                    suaCachDungTrenThuoc(rowid, val);
                }
                else {
                    suachitiettoathuoc(rowid, name);
                }
            },
            footerrow: true,
            loadComplete: function () {
                var $self = $(this);
                var sl = listToathuoc.getGridParam("records");
                sl = sl + " loại thuốc";
                $self.jqGrid("footerData", "set", {TEN_VAT_TU: sl});
                sl = $self.jqGrid("getCol", "THANHTIEN_THUOC", false, "sum");
                $self.jqGrid("footerData", "set", {THANHTIEN_THUOC: sl});
                var ids = listToathuoc.jqGrid('getDataIDs');
                for (var i = 0; i < ids.length; i++) {
                    var cl = ids[i];
                    listToathuoc.jqGrid('setRowData', cl, {id: cl});
                    listToathuoc.jqGrid('setRowData', ids[i], {ACT: getButton(cl)});
                }
            },
            onRightClickRow: function(id) {
                if (id) {
                    $.contextMenu({
                        selector: '#tdt_list_thuoc tr',
                        reposition : false,
                        callback: function (key, options) {
                            var rowSelected = getThongtinRowSelected("tdt_list_thuoc")
                            if(key == 'xoa') {
                                tdtdeletethuocvattu("tdt_list_thuoc" );
                            }
                            if(key == 'ylenh') {
                                if (rowSelected.NGUOITAO !== singletonObject.userId && singletonObject.userId != todieutriObject.TDT_NGUOILAP) {
                                    return notifiToClient("Red", "Bạn không quyền cập nhật y lệnh của người khác.");
                                }
                                initGridThuocylenhtheogio();
                                hideSelfLoading("tdt_thuoc_them_ylenh")
                                addTextTitleModal("titleThuocYlenhtheogio", " Tờ điều trị: "+ todieutriObject.NGAYGIO)
                                var prefix = rowSelected.NGHIEP_VU == 'noitru_toamuangoai'? "n": "";

                                loadGridThuocylenhtheogio(todieutriObject.ID_DIEUTRI, prefix+rowSelected.STT_TOATHUOC, todieutriObject.SOVAOVIEN, todieutriObject.SOVAOVIEN_DT);
                                $("#tdt_thuoc_stt_toathuoc").val(prefix+rowSelected.STT_TOATHUOC)
                                $("#tdt_thuoc_ylenh_tenthuoc").val(rowSelected.TEN_VAT_TU)
                                $("#tdt_thuoc_ghichu_ylenh").val("")
                                var maVatTu = rowSelected.NGHIEP_VU == 'noitru_toamuangoai'? rowSelected.MAVATTU_MUANGOAI: rowSelected.MAVATTU;
                                $("#tdt_thuoc_ylenh_soluongthuoc").val(rowSelected.SO_LUONG);
                                $("#tdt_thuoc_ylenh_mavattu").val(rowSelected.MAVATTU);
                                $("#tdt_thuoc_ylenh_nghiepvu").val(rowSelected.NGHIEP_VU);
                                $("#tdt_thuoc_ylenh_dungtichdasudung").val(rowSelected.DUNGTICH_SUDUNG);
                                $("#tdt_thuoc_soml_ylenh_thuoc").val(null);
                                $("#tdt_thuoc_thoigianylenh").val(todieutriObject.NGAYGIOLAPTDT);
                                $("#tdt_thuoc_stt_toathuoc_ngayyl").val(rowSelected.NGAY_YL);
                                initButtonEditDungTich("active-edit-dungtich-tdt", maVatTu, rowSelected.NGHIEP_VU, function (dataReturn) {
                                    var dungTich = dataReturn.DUNGTICH;
                                    var dungTichTong = parseFloat(rowSelected.SO_LUONG) * parseFloat(dungTich);
                                    var dungTichConLai = parseFloat(dungTichTong) - parseFloat(rowSelected.DUNGTICH_SUDUNG);
                                    console.log("dungTichConLai", dungTichConLai)
                                    console.log("rowSelected.DUNGTICH_SUDUNG", rowSelected.DUNGTICH_SUDUNG)
                                    console.log("dungTich", dungTich)
                                    console.log("dungTichTong", dungTichTong)
                                    $("#tdt_thuoc_dungtich_ylenh_thuoc").val(dungTich).trigger('input');
                                    $("#tdt_thuoc_soml_ylenh_thuoc").val(dungTichConLai);
                                    $("#modalThuocYlenhtheogio").modal("show");
                                });
                                $("#modalThuocYlenhtheogio").modal("show")
                            }
                        },
                        items: {
                            "ylenh": {name: '<p><i class="fa fa-calendar text-primary" aria-hidden="true"></i> Bổ sung y lệnh</p>'},
                            "xoa": {name: '<p><i class="fa fa-trash-o text-danger" aria-hidden="true"></i> Xóa</p>'}
                        }
                    });

                }
            },
        });
    }

}

function getButton(id) {
    var be = "<button data-id='" + id + "' class='btn btn-success tdt_thuoc_btndown btn-sm ml-2'><i class='fa fa-arrow-down' aria-hidden='true'></i></button>";
    var se = "<button data-id='" + id + "' class='btn btn-success tdt_thuoc_btnup btn-sm ml-2'><i class='fa fa-arrow-up' aria-hidden='true'></i></button>";
    return be + se;
}
function upPosition(id, list) {
    var index = parseInt(id);
    if (index != "1") {
        var data = $("#" + list).jqGrid('getGridParam', 'data');
        $('#' + list).jqGrid('clearGridData');
        var be;
        var se;
        for (var i = 0; i < data.length; i++) {
            if (i != (index - 1) && i != (index - 2)) {
                data[i].id = i + 1;
                data[i].ACT = getButton(i +1);
            } else {
                data[index - 2].id = index;
                data[index - 1].id = index - 1;
                data[index - 2].ACT = getButton(index);
                data[index - 1].ACT = getButton(index - 1);
            }
        }
        data.sort(compare);
        for (var i = 0; i < data.length; i++) {
            $("#" + list).jqGrid('addRowData', i + 1, data[i]);
        }
        luuVitriToacu(list);
    }
}

function compare(a, b) {
    return parseInt(a.id) - parseInt(b.id);
}

function downPosition(id, list) {
    var index = parseInt(id);
    var data = $("#" + list).jqGrid('getGridParam', 'data');
    if (index != data.length) {
        $('#' + list).jqGrid('clearGridData');
        var be;
        var se;
        for (var i = 0; i < data.length; i++) {
            if (i != (index - 1) && i != (index)) {
                data[i].id = i + 1;
                data[i].ACT = getButton(i + 1);
            } else {
                data[index].id = index;
                data[index - 1].id = index + 1;
                data[index].ACT = getButton(index);
                data[index - 1].ACT = getButton(index + 1);
            }
        }
        data.sort(compare);
        for (var i = 0; i < data.length; i++) {
            $("#" + list).jqGrid('addRowData', i + 1, data[i]);
        }
        luuVitriToacu(list);
    }
}

function luuVitriToacu(list) {
    if ($("#" + list).getGridParam("records") != "0") {
        var str = $("#" + list).jqGrid('getDataIDs');
        if (str != "" && str != null) {
            var count = str.length;
            var result = 0;
            showLoaderIntoWrapId("tdt_list_thuoc_wrap")
            for (var i = 0; i < count; i++) {
                var ret = $("#" + list).jqGrid('getRowData', str[i]);
                var stt_toathuoc = ret.STT_TOATHUOC;
                var matoathuoc = ret.MA_TOA_THUOC;
                if(ret.NGHIEP_VU == "noitru_toamuangoai") {
                    $.post("cmu_post_cmu_toamuangoai_sttorder_upd", {url: [
                            singletonObject.dvtt,
                            todieutriObject.SOVAOVIEN,
                            todieutriObject.SOVAOVIEN_DT,
                            todieutriObject.ID_DIEUTRI,
                            stt_toathuoc,
                            i + 1
                        ].join("```")
                    }).always(function (s) {
                        result++;
                        if (result == count) {
                            hideLoaderIntoWrapId("tdt_list_thuoc_wrap")
                        }
                    })
                } else {

                    var arr = [stt_toathuoc, thongtinhsba.thongtinbn.STT_BENHAN, thongtinhsba.thongtinbn.STT_DOTDIEUTRI, matoathuoc, singletonObject.dvtt, i + 1];
                    var url = "noitru_capnhatvitrithuoctrongtoa";
                    $.post(url, {url: convertArray(arr)}).always(function (s) {
                        result++;
                        if (result == count) {
                            hideLoaderIntoWrapId("tdt_list_thuoc_wrap")
                        }
                    })
                }
            }
        }
        return notifiToClient("Green",'Cập nhật vị trí thuốc trong toa thành công');
    } else {
        return notifiToClient("Red",'Chưa có thuốc trong toa');
    }
}
function initCombogrid() {
    $("#tdt_thuoc_tenthuongmai").combogrid({
        url: "layvattu_theoloai?makhovt=" + $("#tdt_khothuoc").val() + "&loaivattu=VT_TH&nhomvattu=",
        debug: true,
        width: "1110",
        colModel: [
            {'columnName': 'MAVATTU', 'label': 'ID', 'width': '5', 'align': 'left'},
            {'columnName': 'TENVATTU', 'width': '35', 'label': 'Tên thương mại', 'align': 'left'},
            {'columnName': 'TEN_HIEN_THI', 'label': 'Tên hiển thị', 'align': 'left', hidden: true},
            {'columnName': 'HOATCHAT', 'width': '12', 'label': 'Hoạt chất', 'align': 'left'},
            {'columnName': 'HAMLUONG', 'width': '8', 'label': 'Hàm lượng', 'align': 'left'}, //an giang chỉnh tỉ lệ từ 20 xuống 10
            {'columnName': 'DVT', 'label': 'ĐVT', 'width': '5'},
            {'columnName': 'SOLUONG', 'width': '6', 'label': 'Số lượng', 'align': 'center'},
            {'columnName': 'DONGIA', 'width': '8', 'label': 'Đơn giá', 'align': 'right'},
            {'columnName': 'CACHSUDUNG', 'label': 'cachsudung', hidden: true},
            {'columnName': 'DANGTHUOC', 'label': 'DANGTHUOC', hidden: true},
            {'columnName': 'GHICHUVATTU', 'width': '5', 'label': 'Ghi chú', 'align': 'right', hidden: true},
            {'columnName': 'MAKHOVATTU', 'label': 'Mã kho', 'align': 'right', hidden: true},
            {'columnName': 'TENKHOVATTU', 'label': 'Tên kho', 'align': 'right', hidden: true},
            {'columnName': 'NGAYHETHAN', 'width': '12', 'label': 'Ngày hết hạn', 'align': 'right'},
            {'columnName': 'SOTHAU', 'width': '7', 'label': 'Số thầu', 'align': 'right', hidden: true},
            {'columnName': 'SOLOSANXUAT', 'width': '7', 'label': 'Số lô', 'align': 'right', hidden: true},
            {'columnName': 'TEN_NGUONDUOC', 'width': '5', 'label': 'Nguồn', 'align': 'right', hidden: true}
        ],//dong thap them số thầu],
        select: function (event, ui) {
            $("#tdt_thuoc_tenthuongmai").val(ui.item.TENVATTU);
            $("#tdt_thuoc_tengoc").val(ui.item.HOATCHAT);
            $("#tdt_thuoc_mavattu").val(ui.item.MAVATTU);
            $("#tdt_thuoc_dvt").val(ui.item.DVT);
            $("#tdt_thuoc_hamluong").val(ui.item.HAMLUONG);
            $("#tdt_thuoc_dangthuoc").val(ui.item.DVT);
            $("#tdt_thuoc_cachdung").val(ui.item.CACHSUDUNG);
            $("#tdt_thuoc_dongia").val(ui.item.DONGIA);
            $("#tdt_thuoc_dongia_bv").val(ui.item.DONGIA);
            $("#tdt_thuoc_dongia_bv").val(ui.item.DONGIA);
            $("#tdt_thuoc_ngayuong").val(1);
            return false;
        }
    });
    $("#tdt_thuoc_tengoc").combogrid({
        url: "layvattu_theoloai?makhovt=" + $("#tdt_khothuoc").val() + "&loaivattu=VT_TH&nhomvattu=",
        debug: true,
        width: "1110",
        colModel: [
            {'columnName': 'MAVATTU', 'label': 'ID', 'width': '5', 'align': 'left'},
            {'columnName': 'TENVATTU', 'width': '35', 'label': 'Tên thương mại', 'align': 'left'},
            {'columnName': 'TEN_HIEN_THI', 'label': 'Tên hiển thị', 'align': 'left', hidden: true},
            {'columnName': 'HOATCHAT', 'width': '12', 'label': 'Hoạt chất', 'align': 'left'},
            {'columnName': 'HAMLUONG', 'width': '8', 'label': 'Hàm lượng', 'align': 'left'}, //an giang chỉnh tỉ lệ từ 20 xuống 10
            {'columnName': 'DVT', 'label': 'ĐVT', 'width': '5'},
            {'columnName': 'SOLUONG', 'width': '6', 'label': 'Số lượng', 'align': 'center'},
            {'columnName': 'DONGIA', 'width': '8', 'label': 'Đơn giá', 'align': 'right'},
            {'columnName': 'CACHSUDUNG', 'label': 'cachsudung', hidden: true},
            {'columnName': 'DANGTHUOC', 'label': 'DANGTHUOC', hidden: true},
            {'columnName': 'GHICHUVATTU', 'width': '5', 'label': 'Ghi chú', 'align': 'right', hidden: true},
            {'columnName': 'MAKHOVATTU', 'label': 'Mã kho', 'align': 'right', hidden: true},
            {'columnName': 'TENKHOVATTU', 'label': 'Tên kho', 'align': 'right', hidden: true},
            {'columnName': 'NGAYHETHAN', 'width': '12', 'label': 'Ngày hết hạn', 'align': 'right'},
            {'columnName': 'SOTHAU', 'width': '7', 'label': 'Số thầu', 'align': 'right', hidden: true},
            {'columnName': 'SOLOSANXUAT', 'width': '7', 'label': 'Số lô', 'align': 'right', hidden: true},
            {'columnName': 'TEN_NGUONDUOC', 'width': '5', 'label': 'Nguồn', 'align': 'right', hidden: true}
        ],//dong thap them số thầu],
        select: function (event, ui) {
            $("#tdt_thuoc_tenthuongmai").val(ui.item.TENVATTU);
            $("#tdt_thuoc_tengoc").val(ui.item.HOATCHAT);
            $("#tdt_thuoc_mavattu").val(ui.item.MAVATTU);
            $("#tdt_thuoc_dvt").val(ui.item.DVT);
            $("#tdt_thuoc_hamluong").val(ui.item.HAMLUONG);
            $("#tdt_thuoc_dangthuoc").val(ui.item.DANGTHUOC);
            $("#tdt_thuoc_cachdung").val(ui.item.CACHSUDUNG);
            $("#tdt_thuoc_dongia").val(ui.item.DONGIA);
            $("#tdt_thuoc_dongia_bv").val(ui.item.DONGIA);
            $("#tdt_thuoc_dongia_bv").val(ui.item.DONGIA);
            $("#tdt_thuoc_ngayuong").val(1);
            return false;
        }
    });
}

function changeSourceComboGrid(makho, maloai) {
    console.log("makho", makho, maloai)
    var url = "layvattu_theoloai?makhovt=" + makho + "&loaivattu="+maloai+"&nhomvattu=";
    $("#tdt_thuoc_tenthuongmai").combogrid("option", "url", url);
    var url_tg = "layvattu_theotengoc_theoloai?makhovt=" + makho + "&loaivattu="+maloai;
    $("#tdt_thuoc_tengoc").combogrid("option", "url", url_tg);
}

function tdtthemthuocvattu() {
    var dvtt = singletonObject.dvtt;
    var tenhoatchat = $("#tdt_thuoc_tengoc").val();
    var sovaovien = todieutriObject.SOVAOVIEN;
    var sovaovien_dt = todieutriObject.SOVAOVIEN_DT;
    var makhovattu = $("#tdt_khothuoc").val();
    var nghiepvu = $("#tdt_nghiepvutoathuoc").val();
    var idWrap = "tdt_list_thuoc_wrap";
    showLoaderIntoWrapId(idWrap);
    try {
        var res = $.ajax({
            url: 'cmu_getlist?url='+convertArray([tenhoatchat, dvtt, 'CMU_KIEMTRA_ICD_THUOC']),
            typ: 'GET',
            async: false,
        }).responseText;
        var resJSON = JSON.parse(res);
        var stringTuongtac = '';
        var flag = false;
        var allRowsInGrid = $('#tdt_list_icd').jqGrid('getGridParam','data');
        resJSON.forEach(function(icd) {
            allRowsInGrid.forEach(function(obj) {
                if(icd.MA_ICD == obj.ICD) {
                    stringTuongtac += tenhoatchat + ' Chống chỉ định với:  ' + icd.MA_ICD + (icd.TACDUNG? (' - '+icd.TACDUNG): '') + '; ';
                    flag = true;
                }
            })

        })
        if(flag) {
            hideLoaderIntoWrapId(idWrap);
            return notifiToClient("Red", stringTuongtac);
        }
    } catch(ex) {
        hideLoaderIntoWrapId(idWrap);
    }
    var arr_kt = [encodeURIComponent(tenhoatchat), dvtt, sovaovien, sovaovien_dt, todieutriObject.ID_DIEUTRI, 1];
    var url_kt = "kiemTraTuongTacThuoc?url=" + convertArray(arr_kt);
    $.ajax({
        url: url_kt
    }).done(function (data) {
        if (data === "[]" || data.toString() === "") {
            kiemtrathuocNoiTru(nghiepvu, makhovattu);
        }else {
            var str = "";
            var tacdung = "";
            var tenthuoc = "";
            $.each(data, function (i) {
                tacdung = data[i].TACDUNG;
                tenthuoc = data[i].TENVATTU;
                str = str + tenthuoc + " " + tacdung + ", ";
            });
            str = str.substring(0, str.length - 2);
            if (str.trim() !== "") {
                hideLoaderIntoWrapId(idWrap);
                return notifiToClient("Red", "Thuốc " + tenhoatchat + " tương tác với thuốc " + str);
            }
        }
    });
}

function kiemtrathuocNoiTru( nghiepvu, makhovattu) {
    $("#tdt_thuoc_soluong").blur();
    var url = "noitru_select_tt_phieudt";
    var arr = [todieutriObject.STT_DIEUTRI, singletonObject.dvtt, todieutriObject.STT_DIEUTRI, thongtinhsba.thongtinbn.STT_BENHAN, thongtinhsba.thongtinbn.STT_DOTDIEUTRI];
    $.post(url, {url: convertArray(arr)}).done(function (dt) {
        var tt_pcshientai = parseInt(dt);
        if(tt_pcshientai > 1) {
            hideLoaderIntoWrapId("tdt_list_thuoc_wrap");
            return notifiToClient("Red", "Phiếu điều trị đã được dự trù thuốc hoặc cập nhật trạng thái, không được thêm thuốc vào!");
        }
        var url_kho = "noitru_kiemtra_tututhuoc";
        $.post(url_kho, {dvtt: singletonObject.dvtt, maphongban: singletonObject.makhoa, makho: makhovattu})
            .done(function (data) {
                var tu_tuthuoc = data == "1" ? "1" : "0";
                var sl = $("#tdt_thuoc_soluong").val();
                if (data == "0") {
                    sl = (sl != "") ? parseInt(sl) : 0;
                } else {
                    sl = (sl != "") ? parseFloat(sl) : 0;
                }
                var matoathuoc = todieutriObject.STT_DIEUTRI;
                var mavattu = $("#tdt_thuoc_mavattu").val();
                var records = $("#tdt_list_thuoc").getGridParam("records");
                var slrow = 0;
                var i = 0;
                for (i = 0; i < records; i++) {
                    var ret = $("#tdt_list_thuoc").jqGrid('getRowData', i + 1);
                    if (ret.MAVATTU == mavattu && ret.NGHIEP_VU == $("#tdt_nghiepvutoathuoc").val()) {
                        slrow = slrow + 1;
                        hideLoaderIntoWrapId("tdt_list_thuoc_wrap");
                        return notifiToClient("Red", "Thuốc này đã có trong toa");
                    }
                }

                var tenthuongmai = $("#tdt_thuoc_tenthuongmai").val();
                var tengoc = $("#tdt_thuoc_tengoc").val();
                var dvt = $("#tdt_thuoc_dvt").val();
                var dangthuoc = $("#tdt_thuoc_dangthuoc").val();
                var cachdung = $("#tdt_thuoc_cachdung").val();
                var songay = $("#tdt_thuoc_ngayuong").val();
                var sang = $("#tdt_thuoc_sang").val();
                var sovaovien = todieutriObject.SOVAOVIEN;
                var sovaovien_dt = todieutriObject.SOVAOVIEN_DT;
                if (sang == "")
                    sang = "0";
                var trua = $("#tdt_thuoc_trua").val();
                if (trua == "")
                    trua = "0";
                var chieu = $("#tdt_thuoc_chieu").val();
                if (chieu == "")
                    chieu = "0";
                var toi = $("#tdt_thuoc_toi").val();
                if (toi == "")
                    toi = "0";
                var dongia_bv = $("#tdt_thuoc_dongia_bv").val();
                var dongia_bh = $("#tdt_thuoc_dongia").val();
                $("#tdt_thuoc_thanhtien").val(sl * parseFloat(dongia_bv));
                var thanhtien = $("#tdt_thuoc_thanhtien").val();
                var tlmg = thongtinhsba.thongtinbn.TYLEBAOHIEM;
                if (tlmg == "")
                    tlmg = "0";
                if (nghiepvu == "noitru_toadongy") {
                    var sothan = "1"; //Toa dong y can bo sung so than thuoc
                    if (sothan == "")
                        sothan = "1";
                    sl = sl * parseInt(sothan);
                    dangthuoc = "";
                    cachdung = "";
                    songay = "1";
                    sang = "0";
                    trua = "0";
                    chieu = "0";
                    toi = "0";
                    tengoc = "";
                }
                if (parseInt(songay) <= 0) {
                    notifiToClient("Red", "Số ngày không hợp lệ");
                    $("#tdt_thuoc_ngayuong").focus();
                    hideLoaderIntoWrapId("tdt_list_thuoc_wrap");
                    return false;
                }
                if (sl <= 0) {

                    notifiToClient("Red", "Số lượng không hợp lệ");
                    $("#tdt_thuoc_soluong").focus();
                    hideLoaderIntoWrapId("tdt_list_thuoc_wrap");
                    return false;
                }
                if (matoathuoc != "") {
                    var arr = [singletonObject.dvtt, matoathuoc, makhovattu, mavattu, tenthuongmai, tengoc, dvt, nghiepvu, sl, sl, dongia_bv, dongia_bh, thanhtien, songay, sang, trua,
                        chieu, toi, cachdung+ " !!!"+singletonObject.userId, todieutriObject.BACSIDIEUTRI, dangthuoc,
                        todieutriObject.STT_DIEUTRI, thongtinhsba.thongtinbn.STT_BENHAN, thongtinhsba.thongtinbn.STT_DOTDIEUTRI,
                        tu_tuthuoc, tlmg, thongtinhsba.thongtinbn.MA_BENH_NHAN, todieutriObject.SOPHIEUTHANHTOAN, singletonObject.makhoa,
                        sovaovien, sovaovien_dt, "0"];
                    var url = "noitru_toathuoc_insert";
                    $("#tdt_thuoc_tenthuongmai").focus();
                    $.post(
                        url, {url: convertArray(arr)}
                    ).done(function (data) {
                        if (data == "4") {
                            return notifiToClient("Red", "Bệnh nhân đã thanh toán rồi");
                        }
                        if (data == "5") {
                            return notifiToClient("Red","Bệnh nhân đã xuất thuốc rồi");
                        }
                        if (data == "3") {
                            $("#tdt_thuoc_tenthuongmai").focus();
                            return notifiToClient("Red","Thuốc đã có trong toa");
                        }
                        if (data == "6") {
                            return notifiToClient("Red","Số lượng thuốc vượt số lượng tồn kho");
                        }
                        if (data == "-3") {
                            return notifiToClient("Red","Thuốc đã được tạm ngưng trong danh mục");
                        }
                        if (data == '100') {
                            return notifiToClient("Red","Đã chốt báo cáo dược, không thể sửa/xóa");
                        }
                        $.logThuocVattuTodieutri("Thuốc/vật tư: " +mavattu + " - "+ tenthuongmai + " - Số lượng: "+ sl +" " + dvt);
                        tdtloaddstoathuoc();
                        clearInputToathuoc();
                        $("#tdt_thuoc_tenthuongmai").focus();
                    }).fail(function () {
                        notifiToClient("Red", "Có lỗi xảy ra vui lòng thử lại sau.")
                    }).always(function () {
                        hideLoaderIntoWrapId("tdt_list_thuoc_wrap");
                    })
                }
            });
    }).fail(function () {
        hideLoaderIntoWrapId("tdt_list_thuoc_wrap");
    })
}

function tdtloaddstoathuoc() {
    var url = 'noitru_load_chitiet_toathuoctonghop?matt=' + todieutriObject.STT_DIEUTRI + "&dvtt="+singletonObject.dvtt+"&stt_benhan=" +
        thongtinhsba.thongtinbn.STT_BENHAN + "&stt_dotdieutri=" + todieutriObject.STT_DOTDIEUTRI;
    $("#tdt_list_thuoc").jqGrid('setGridParam', {datatype: 'json', url: url}).trigger('reloadGrid');
}

function clearInputToathuoc() {
    $("#tdt_thuoc_inputthuoc input").val("");
}

function suachitiettoathuoc(rowid, name) {
    var url = "noitru_select_tt_phieudt";
    var listToathuoc = $("#tdt_list_thuoc");
    var tu_tuthuoc = listToathuoc.jqGrid('getCell', rowid, 'TU_TU_THUOC');
    var tlmg = thongtinhsba.thongtinbn.TYLEBAOHIEM;
    if (tlmg == "")
        tlmg = 0;
    var stt = listToathuoc.jqGrid('getCell', rowid, 'STT_TOATHUOC');
    var matoathuoc = todieutriObject.STT_DIEUTRI;
    var mavattu = listToathuoc.jqGrid('getCell', rowid, 'MAVATTU');
    var nghiepvu = listToathuoc.jqGrid('getCell', rowid, 'NGHIEP_VU');
    var makhovattu = listToathuoc.jqGrid('getCell', rowid, 'MAKHOVATTU');
    var tenthuongmai = listToathuoc.jqGrid('getCell', rowid, 'TEN_VAT_TU');
    var tengoc = listToathuoc.jqGrid('getCell', rowid, 'HOAT_CHAT');
    var dvt = listToathuoc.jqGrid('getCell', rowid, 'DVT');
    var cachsudung = listToathuoc.jqGrid('getCell', rowid, 'CACH_SU_DUNG');
    var ghichutt = listToathuoc.jqGrid('getCell', rowid, 'GHI_CHU_CT_TOA_THUOC');
    var songay_str = listToathuoc.jqGrid('getCell', rowid, 'SO_NGAY_UONG');
    var songay = (songay_str != "") ? parseInt(songay_str) : 0;
    var sang_str = listToathuoc.jqGrid('getCell', rowid, 'SANG_UONG');
    var sang = (sang_str != "") ? parseFloat(sang_str) : 0;
    var trua_str = listToathuoc.jqGrid('getCell', rowid, 'TRUA_UONG');
    var trua = (trua_str != "") ? parseFloat(trua_str) : 0;
    var chieu_str = listToathuoc.jqGrid('getCell', rowid, 'CHIEU_UONG');
    var chieu = (chieu_str != "") ? parseFloat(chieu_str) : 0;
    var toi_str = listToathuoc.jqGrid('getCell', rowid, 'TOI_UONG');
    var toi = (toi_str != "") ? parseFloat(toi_str) : 0;
    var soluong = 0;
    if (name == "SO_LUONG") {
        soluong = (listToathuoc.jqGrid('getCell', rowid, 'SO_LUONG'));
    } else
        soluong = (songay * (sang + trua + chieu + toi));
    if (tu_tuthuoc == "0") {
        soluong = parseInt(soluong);
    }
    var dongia_bv = parseFloat(listToathuoc.jqGrid('getCell', rowid, 'DONGIA_BAN_BV'));
    var dongia_bh = parseFloat(listToathuoc.jqGrid('getCell', rowid, 'DONGIA_BAN_BH'));
    var thanhtien_truoc = parseFloat(listToathuoc.jqGrid('getCell', rowid, 'THANHTIEN_THUOC'));
    var thanhtien = parseFloat(Math.ceil(parseFloat(soluong)) * parseFloat(dongia_bh));
    var arr = [todieutriObject.STT_DIEUTRI, singletonObject.dvtt, todieutriObject.STT_DIEUTRI, thongtinhsba.thongtinbn.STT_BENHAN, thongtinhsba.thongtinbn.STT_DOTDIEUTRI];
    var nghiep_vu = listToathuoc.jqGrid('getCell', rowid, 'NGHIEP_VU');
    var idWrap = "tdt_list_thuoc_wrap";
    showLoaderIntoWrapId(idWrap);
    if(nghiep_vu == "noitru_toamuangoai") {
        $.post("cmu_post_cmu_hsba_nttoamuangoai_upd", {
            url: [
                singletonObject.dvtt,
                todieutriObject.SOVAOVIEN,
                todieutriObject.SOVAOVIEN_DT,
                todieutriObject.STT_DIEUTRI,
                stt,
                sang,
                trua,
                chieu,
                toi,
                soluong,
                songay,
                ghichutt,
                cachsudung
            ].join("```")
        }).done(function (dt) {
            tdtloaddstoathuoc();
            notifiToClient("Green","Cập nhật thành công");
        }).always(function () {
            hideLoaderIntoWrapId(idWrap);
        }).fail(function (dt) {
            notifiToClient("Red","Lỗi cập nhật");
        })
        return false;
    }

    $.post(url, {url: convertArray(arr)}).done(function (dt) {
        var tt_pcshientai = parseInt(dt);
        if (tt_pcshientai > 1) {
            hideLoaderIntoWrapId(idWrap);
            notifiToClient("Red","Phiếu điều trị đã được dự trù thuốc hoặc cập nhật trạng thái, không được sửa thuốc!");
            return tdtloaddstoathuoc();
        }
        if (songay <= 0) {
            hideLoaderIntoWrapId(idWrap);
            return notifiToClient("Red","Số ngày phải lớn hơn 0");
        }
        if (soluong <= 0) {
            hideLoaderIntoWrapId(idWrap);
            return notifiToClient("Red","Số lượng phải lớn hơn 0");
        }
        var url_kt_sl = "kiemtrasoluongtonkho";
        $.post(url_kt_sl, {dvtt: singletonObject.dvtt, makho: makhovattu, mavattu: mavattu}).done(function (dt) {
            var soluong_vt = parseInt(tdtSoluongThuocTruoc) + parseInt(dt);
            if (soluong_vt < soluong) {
                hideLoaderIntoWrapId(idWrap);
                return notifiToClient("Red","Số lượng tồn kho không đủ để chỉnh sửa.");
            }
            if ((nghiepvu == "noitru_toavattu" || nghiepvu == "noitru_toathuoc") && (parseInt(songay) > parseInt(thongtinhsba.thongtinbn.songayconBHYT))) {
                $.confirm({
                    title: 'Xác nhận!',
                    type: 'orange',
                    content: 'Số ngày nhiều hơn số ngày còn trên BHYT. Nhấn "Tiếp tục" để ra thuốc. Nhấn "Hủy" để sửa lại số ngày.',
                    buttons: {
                        warning: {
                            btnClass: 'btn-warning',
                            text: "Tiếp tục",
                            action: function(){
                                var url = "noitru_toathuoc_delete";
                                var arr = [stt, matoathuoc, singletonObject.dvtt,
                                    todieutriObject.STT_DIEUTRI, thongtinhsba.thongtinbn.STT_BENHAN,
                                    thongtinhsba.thongtinbn.STT_DOTDIEUTRI, todieutriObject.SOPHIEUTHANHTOAN,
                                    thanhtien, makhovattu, dongia_bh, nghiepvu, mavattu, tenthuongmai, tdtSoluongThuocTruoc];
                                $.post(url, {
                                    url: convertArray(arr)
                                }).done(function (data) {
                                    if (data == "1") {
                                        hideLoaderIntoWrapId(idWrap);
                                        return notifiToClient("Red","Bệnh nhân đã thanh toán viện phí");
                                    }
                                    if (data == "2") {
                                        hideLoaderIntoWrapId(idWrap);
                                        return notifiToClient("Red","Bệnh nhân đã được xuất thuốc");
                                    }
                                    if (data == '100') {
                                        hideLoaderIntoWrapId(idWrap);
                                        return notifiToClient("Red","Đã chốt báo cáo dược, không thể sửa/xóa");
                                    }
                                    if (data == '200') {
                                        hideLoaderIntoWrapId(idWrap);
                                        return notifiToClient("Red","Đã pha thuốc, không thể xoá");
                                    }
                                    if (data == '201') {
                                        hideLoaderIntoWrapId(idWrap);
                                        return notifiToClient("Red","Thuốc có y lệnh truyền dịch, không thể xoá");
                                    }
                                    else {
                                        $.logDeleteThuocVattuTodieutri(
                                            "Thuốc/vật tư: " +mavattu + " - "+ tenthuongmai + " - Số lượng: "+ soluong +" " + dvt+
                                            "; Nghiệp vụ: "+ $("#tdt_nghiepvutoathuoc option[value='"+nghiepvu+"']").text(),
                                            makhovattu
                                        );
                                        var arr = [singletonObject.dvtt, matoathuoc, makhovattu, mavattu, tenthuongmai, tengoc, dvt, nghiepvu, soluong, soluong, dongia_bv, dongia_bh, thanhtien, songay, sang, trua,
                                            chieu, toi, ghichutt + " !!!"+singletonObject.userId,  todieutriObject.BACSIDIEUTRI, dvt,
                                            todieutriObject.STT_DIEUTRI, thongtinhsba.thongtinbn.STT_BENHAN,
                                            thongtinhsba.thongtinbn.STT_DOTDIEUTRI, tu_tuthuoc, tlmg,
                                            thongtinhsba.thongtinbn.MA_BENH_NHAN, todieutriObject.SOPHIEUTHANHTOAN,
                                            singletonObject.makhoa, todieutriObject.SOVAOVIEN, todieutriObject.SOVAOVIEN_DT, "0"];
                                        var url = "noitru_toathuoc_insert";
                                        $.post(
                                            url, {url: convertArray(arr)}
                                        ).done(function (data) {
                                            if (data == '100') {
                                                return notifiToClient("Red","Đã chốt báo cáo dược, không thể sửa/xóa");
                                            } else if (data == "-3") {
                                                return notifiToClient("Red","Thuốc đã được tạm ngưng trong danh mục");
                                            }
                                            $.logThuocVattuTodieutri("Thuốc/vật tư: " +mavattu + " - "+ tenthuongmai + " - Số lượng: "+ soluong +" " + dvt);
                                            tdtloaddstoathuoc();
                                        }).always(function () {
                                            hideLoaderIntoWrapId(idWrap);
                                        })
                                    }
                                });
                            }
                        },
                        cancel: function () {
                            hideLoaderIntoWrapId(idWrap);
                        }
                    }
                }).fail(function () {
                    hideLoaderIntoWrapId(idWrap);
                    notifiToClient("Red","Lỗi cập nhật");
                });
            } else {
                var url = "noitru_toathuoc_delete";
                var arr = [stt, matoathuoc, singletonObject.dvtt,
                    todieutriObject.STT_DIEUTRI, thongtinhsba.thongtinbn.STT_BENHAN,
                    thongtinhsba.thongtinbn.STT_DOTDIEUTRI, todieutriObject.SOPHIEUTHANHTOAN,
                    thanhtien, makhovattu, dongia_bh, nghiepvu, mavattu, tenthuongmai, tdtSoluongThuocTruoc];
                $.post(url, {
                    url: convertArray(arr)
                }).done(function (data) {
                    if (data == "1") {
                        hideLoaderIntoWrapId(idWrap);
                        return notifiToClient("Red","Bệnh nhân đã thanh toán viện phí");
                    }
                    if (data == "2") {
                        hideLoaderIntoWrapId(idWrap);
                        return notifiToClient("Red","Bệnh nhân đã được xuất thuốc");
                    }
                    if (data == '100') {
                        hideLoaderIntoWrapId(idWrap);
                        return notifiToClient("Red","Đã chốt báo cáo dược, không thể sửa/xóa");
                    }
                    if (data == '200') {
                        hideLoaderIntoWrapId(idWrap);
                        return notifiToClient("Red","Đã pha thuốc, không thể xoá");
                    }
                    if (data == '201') {
                        hideLoaderIntoWrapId(idWrap);
                        return notifiToClient("Red","Thuốc có y lệnh truyền dịch, không thể xoá");
                    }
                    else {
                        $.logDeleteThuocVattuTodieutri(
                            "Thuốc/vật tư: " +mavattu + " - "+ tenthuongmai + " - Số lượng: "+ soluong +" " + dvt+
                            "; Nghiệp vụ: "+ $("#tdt_nghiepvutoathuoc option[value='"+nghiepvu+"']").text(),
                            makhovattu
                        );
                        var arr = [singletonObject.dvtt, matoathuoc, makhovattu, mavattu, tenthuongmai, tengoc, dvt, nghiepvu, soluong, soluong, dongia_bv, dongia_bh, thanhtien, songay, sang, trua,
                            chieu, toi, ghichutt + " !!!"+singletonObject.userId,  todieutriObject.BACSIDIEUTRI, dvt,
                            todieutriObject.STT_DIEUTRI, thongtinhsba.thongtinbn.STT_BENHAN,
                            thongtinhsba.thongtinbn.STT_DOTDIEUTRI, tu_tuthuoc, tlmg,
                            thongtinhsba.thongtinbn.MA_BENH_NHAN, todieutriObject.SOPHIEUTHANHTOAN,
                            singletonObject.makhoa, todieutriObject.SOVAOVIEN, todieutriObject.SOVAOVIEN_DT, "0"];
                        var url = "noitru_toathuoc_insert";
                        $.post(
                            url, {url: convertArray(arr)}
                        ).done(function (data) {
                            if (data == '100') {
                                return notifiToClient("Red","Đã chốt báo cáo dược, không thể sửa/xóa");
                            } else if (data == "-3") {
                                return notifiToClient("Red","Thuốc đã được tạm ngưng trong danh mục");
                            }
                            $.logThuocVattuTodieutri("Thuốc/vật tư: " +mavattu + " - "+ tenthuongmai + " - Số lượng: "+ soluong +" " + dvt);
                            tdtloaddstoathuoc();
                        }).fail(function() {
                            notifiToClient("Red","Lỗi cập nhật");
                        }).always(function () {
                            hideLoaderIntoWrapId(idWrap);
                        })
                    }
                }).fail(function () {
                    hideLoaderIntoWrapId(idWrap);
                    notifiToClient("Red","Lỗi cập nhật");
                });

            }
        });

    });
}
function tdtdeletethuocvattu(list) {
    try {
        var listToathuoc = $("#"+list);
        var id = listToathuoc.jqGrid('getGridParam', 'selrow');
        if (id) {
            var ret = listToathuoc.jqGrid('getRowData', id);
            console.log("ret", ret)
            if (ret.MA_BAC_SI_THEMTHUOC == singletonObject.userId || ret.NGUOITAO == singletonObject.userId || singletonObject.admin == "1") {

                var url = "noitru_select_tt_phieudt";
                var arr = [ret.MA_TOA_THUOC, singletonObject.dvtt, ret.MA_TOA_THUOC, thongtinhsba.thongtinbn.STT_BENHAN, thongtinhsba.thongtinbn.STT_DOTDIEUTRI];
                $.post(url, {url: convertArray(arr)}).done(function (dt) {
                    var tt_pcshientai = parseInt(dt);
                    if (tt_pcshientai > 1) {
                        return notifiToClient("Red","Phiếu điều trị đã được dự trù thuốc hoặc cập nhật trạng thái, không được xóa thuốc!");
                    }
                    $.confirm({
                        title: 'Xác nhận!',
                        type: 'orange',
                        content:'Bạn có muốn xóa không?',
                        buttons: {
                            warning: {
                                btnClass: 'btn-warning',
                                text: "Tiếp tục",
                                action: function(){
                                    var nghiepvu = ret.NGHIEP_VU;
                                    var idWrap = "tdt_list_thuoc_wrap";
                                    showLoaderIntoWrapId(idWrap)
                                    if (nghiepvu == "noitru_toamuangoai") {
                                        return tdtXoathuocMuangoai(ret);
                                    }
                                    url = "noitru_toathuoc_delete";
                                    arr = [ret.STT_TOATHUOC, ret.MA_TOA_THUOC, singletonObject.dvtt,
                                        ret.MA_TOA_THUOC,
                                        thongtinhsba.thongtinbn.STT_BENHAN,
                                        thongtinhsba.thongtinbn.STT_DOTDIEUTRI,
                                        todieutriObject.SOPHIEUTHANHTOAN,
                                        ret.THANHTIEN_THUOC, ret.MAKHOVATTU, ret.DONGIA_BAN_BH,
                                        nghiepvu, ret.MAVATTU, ret.TEN_VAT_TU, ret.SO_LUONG];
                                    $.post(url, {
                                        url: convertArray(arr)
                                    }).done(function (data) {
                                        if (data == "1")
                                            return notifiToClient("Red","Bệnh nhân đã thanh toán viện phí");
                                        if (data == "2")
                                            return notifiToClient("Red","Bệnh nhân đã được xuất thuốc");
                                        if (data == '100') {
                                            return notifiToClient("Red","Đã chốt báo cáo dược, không thể sửa/xóa");
                                        }
                                        if (data == '-4') {
                                            return notifiToClient("Red","Thuốc đã được tạm ngưng trong danh mục");
                                        }
                                        if (data == '200') {
                                            hideLoaderIntoWrapId(idWrap);
                                            return notifiToClient("Red","Đã pha thuốc, không thể xoá");
                                        }
                                        if (data == '201') {
                                            hideLoaderIntoWrapId(idWrap);
                                            return notifiToClient("Red","Thuốc có y lệnh truyền dịch, không thể xoá");
                                        }
                                        else {
                                            $.logDeleteThuocVattuTodieutri(
                                                "Thuốc/vật tư: " +ret.MAVATTU + " - "+
                                                ret.TEN_VAT_TU + " - Số lượng: "+ ret.SO_LUONG +" " + ret.DVT+
                                                "; Nghiệp vụ: "+ $("#tdt_nghiepvutoathuoc option[value='"+nghiepvu+"']").text(),
                                                ret.MAKHOVATTU
                                            );
                                            notifiToClient("Green","Xóa thành công!");
                                            tdtloaddstoathuoc();
                                        }
                                    }).always(function () {
                                        hideLoaderIntoWrapId("tdt_list_thuoc_wrap")
                                    })
                                }
                            },
                            cancel: function () {
                            }
                        }
                    });
                });
            } else {
                notifiToClient("Red","Bạn không được xóa thuốc trong toa của bác sĩ khác!");
            }
        }
    } catch (e) {
        notifiToClient("Red","Lỗi xóa thuốc!");
    }

}

function tdtXoathuocMuangoai(rowSelected) {
    var arrThuoc = [rowSelected.STT_TOATHUOC, todieutriObject.STT_DIEUTRI,
        singletonObject.dvtt, todieutriObject.STT_DIEUTRI,
        thongtinhsba.thongtinbn.STT_BENHAN, todieutriObject.STT_DOTDIEUTRI];
    $.post("noitru_toathuocmuangoai_delete", {
        url: convertArray(arrThuoc)
    }).done(function (data) {
        tdtloaddstoathuoc();
    }).fail(function() {
        notifiToClient("Red","Lỗi hệ thống, vui lòng thử lại sau");
    }).always(function () {
        hideLoaderIntoWrapId("tdt_list_thuoc_wrap")
    });
}

function suaCachDungTrenThuoc(rowid, cachDung) {
    let listToathuoc = $("#tdt_list_thuoc");
    let sttToaThuoc = listToathuoc.jqGrid('getCell', rowid, 'STT_TOATHUOC');
    let nghiep_vu = listToathuoc.jqGrid('getCell', rowid, 'NGHIEP_VU');
    $.post("cmu_post_NOITRU_CT_TOATHUOC_UPD", {
        url: [
            singletonObject.dvtt,
            todieutriObject.ID_DIEUTRI,
            sttToaThuoc,
            todieutriObject.SOVAOVIEN,
            todieutriObject.SOVAOVIEN_DT,
            nghiep_vu, cachDung,
        ].join("```")
    }).done(function(data) {
        if (data > 0) {
            tdtloaddstoathuoc();
            notifiToClient('Green', MESSAGEAJAX.EDIT_SUCCESS);
        }
        else {
            notifiToClient('Red', MESSAGEAJAX.ERROR);
        }
    }).fail(function() {
        notifiToClient('Red', MESSAGEAJAX.FAIL);
    });
}

function initTdtToacuGrid() {
    var listToathuoc = $("#tdt_list_toacu");
    if(!listToathuoc[0].grid) {
        listToathuoc.jqGrid({
            url: "",
            datatype: "local",
            loadonce: false,
            height: 300,
            width: null,
            shrinkToFit: false,
            colNames: ["stt_toathuoc", "Tên thương mại", "Tên gốc-Hoạt chất", "DVT", "Số ngày", "Sáng", "Trưa",
                "Chiều", "Tối","Số thang", "SL", "Dạng thuốc", "Cách dùng", "Đơn giá", "Thành tiền", "mavattu",
                "makhovattu", "dongia_bv", "NGHIEP_VU"
            ],
            colModel: [
                {name: 'STT_TOATHUOC', index: 'STT_TOATHUOC', hidden: true},
                {name: 'TEN_VAT_TU', index: 'TEN_VAT_TU', width: 280},
                {name: 'HOAT_CHAT', index: 'HOAT_CHAT', width: 180},
                {name: 'DVT', index: 'DVT', width: 50},
                {name: 'SO_NGAY_UONG', index: 'SO_NGAY_UONG', width: 50, editable: true, edittype: 'text'},
                {name: 'SANG_UONG', index: 'SANG_UONG', width: 50, editable: true, edittype: 'text'},
                {name: 'TRUA_UONG', index: 'TRUA_UONG', width: 50, editable: true, edittype: 'text'},
                {name: 'CHIEU_UONG', index: 'CHIEU_UONG', width: 50, editable: true, edittype: 'text'},
                {name: 'TOI_UONG', index: 'TOI_UONG', width: 50, editable: true, edittype: 'text'},
                {name: 'SO_THANG_VPC', index: 'SO_THANG_VPC', width: 50},
                {name: "SO_LUONG", index: "SO_LUONG", width: 50, editable: true, edittype: 'text'},
                {name: 'CACH_SU_DUNG', index: 'CACH_SU_DUNG', width: 160, hidden: true},
                {name: 'GHI_CHU_CT_TOA_THUOC', index: 'GHI_CHU_CT_TOA_THUOC', width: 60, hidden: true},
                {
                    name: 'DONGIA_BAN_BH',
                    index: 'DONGIA_BAN_BH',
                    width: 100,
                    align: 'right',
                    formatter: 'integer',
                    formatoptions: {decimalSeparator: ".", thousandsSeparator: ","}
                },
                {
                    name: 'THANHTIEN_THUOC',
                    index: 'THANHTIEN_THUOC',
                    width: 100,
                    align: 'right',
                    formatter: 'integer',
                    formatoptions: {decimalSeparator: ".", thousandsSeparator: ","}
                },
                {name: 'MAVATTU', index: 'MAVATTU', hidden: true},
                {name: 'MAKHOVATTU', index: 'MAKHOVATTU', hidden: true},
                {name: 'DONGIA_BAN_BV', index: 'DONGIA_BAN_BV', hidden: true},
                {name: 'NGHIEP_VU', index: 'NGHIEP_VU', hidden: true},
            ],
            cellEdit: true,
            cellsubmit: 'clientArray',
            // grouping: true,
            rowNum: 1000,
            // groupingView: {
            //     groupField: ["NGHIEP_VU"],
            //     groupColumnShow: [false, false],
            //     groupText: ['<b>{0}</b>'],
            //     groupCollapse: false
            // },

            afterSaveCell: function (rowid, name, val, iRow, iCol) {
                var songay_str = listToathuoc.jqGrid('getCell', rowid, 'SO_NGAY_UONG');
                var songay = (songay_str != "") ? parseInt(songay_str) : 0;
                var sang_str = listToathuoc.jqGrid('getCell', rowid, 'SANG_UONG');
                var sang = (sang_str != "") ? parseFloat(sang_str) : 0;
                var trua_str = listToathuoc.jqGrid('getCell', rowid, 'TRUA_UONG');
                var trua = (trua_str != "") ? parseFloat(trua_str) : 0;
                var chieu_str = listToathuoc.jqGrid('getCell', rowid, 'CHIEU_UONG');
                var chieu = (chieu_str != "") ? parseFloat(chieu_str) : 0;
                var toi_str = listToathuoc.jqGrid('getCell', rowid, 'TOI_UONG');
                var toi = (toi_str != "") ? parseFloat(toi_str) : 0;
                var soluong = 0;
                if (name == "SO_LUONG") {
                    soluong = listToathuoc.jqGrid('getCell', rowid, 'SO_LUONG');
                }
                else {
                    soluong = songay * (sang + trua + chieu + toi);
                }
                var dongia_bv = listToathuoc.jqGrid('getCell', rowid, 'DONGIA_BAN_BV');
                var dongia_bh = listToathuoc.jqGrid('getCell', rowid, 'DONGIA_BAN_BH');
                var thanhtien = Math.ceil(parseFloat(soluong)) * parseFloat(dongia_bh);
                if (songay <= 0) {
                    notifiToClient("Red","Số ngày phải lớn hơn 0");
                    return false;
                }
                else if (soluong <= 0) {
                    notifiToClient("Red","Số lượng phải lớn hơn 0");
                    return false;
                }
                else {
                    listToathuoc.jqGrid('setRowData', rowid, {SO_LUONG: soluong});
                    listToathuoc.jqGrid('setRowData', rowid, {THANHTIEN_THUOC: thanhtien});
                }
            },
            caption: "Toa nội trú",
            gridComplete: function () {

            },
            onRightClickRow: function(id) {
                if (id) {
                    $.contextMenu({
                        selector: '#tdt_list_toacu tr',
                        reposition : false,
                        callback: function (key, options) {
                            if(key == 'xoa') {
                                var id = listToathuoc.jqGrid("getGridParam", "selrow");
                                listToathuoc.jqGrid('delRowData',id);
                            }
                        },
                        items: {
                            "xoa": {name: '<p><i class="fa fa-trash-o text-danger" aria-hidden="true"></i> Xóa</p>'}
                        }
                    });

                }

            },
        });
    }
}
function loadToacuTheoPhieudt() {
    flag_all = 0;
    var url = 'noitru_load_chitiet_toathuoc?matt=' + $("#tdt_toacu_danhsach").val()
        + "&dvtt="+singletonObject.dvtt+"&stt_benhan=" +
        thongtinhsba.thongtinbn.STT_BENHAN +
        "&stt_dotdieutri=" + todieutriObject.STT_DOTDIEUTRI+
        "&nghiepvu=" + $("#tdt_toacu_nghiepvu").val()
    ;
    $("#tdt_list_toacu").jqGrid('setGridParam', {datatype: 'json', url: url}).trigger('reloadGrid');
}

function loadToaCuVaoToaThuoc() {
    var url = "noitru_select_tt_phieudt";
    var type = $("#action_tdtloatc_laytc").attr("data-type");
    var makhovattu = type == "VTYT"? $("#tdt_vtyt_khothuoc").val() : $("#tdt_khothuoc").val();
    var arr = [todieutriObject.STT_DIEUTRI, singletonObject.dvtt, todieutriObject.STT_DIEUTRI, thongtinhsba.thongtinbn.STT_BENHAN,
        todieutriObject.STT_DOTDIEUTRI];
    showSelfLoading("action_tdtloatc_laytc")
    $.post(url, {url: convertArray(arr)}).done(function (dt) {
        var tt_pcshientai = parseInt(dt);
        if (tt_pcshientai > 1) {
            return notifiToClient("Red", "Phiếu điều trị đã được dự trù thuốc hoặc cập nhật trạng thái, không được thêm thuốc vào!");
        }
        var url_kho = "noitru_kiemtra_tututhuoc";
        $.post(url_kho, {dvtt: singletonObject.dvtt, maphongban: singletonObject.makhoa, makho: makhovattu})
            .done(function (data) {
                var tu_tuthuoc = (data == "1") ? "1" : "0";
                var str = $("#tdt_list_toacu").jqGrid('getDataIDs');
                if (str != "" && str != null) {
                    var count = str.length;
                    var soluong = 0;
                    var songay = 0;
                    for (var i = 0; i < count; i++) {
                        var ret = $("#tdt_list_toacu").jqGrid('getRowData', str[i]);
                        soluong = ret.SO_LUONG;
                        songay = ret.SO_NGAY_UONG;
                        if (soluong == "" || soluong == "0") {
                            notifiToClient("Red", "Số lượng phải lớn hơn 0");
                            return false;
                        }
                    }
                    var matoathuoc = todieutriObject.STT_DIEUTRI;
                    var tlmg = thongtinhsba.thongtinbn.TYLEBAOHIEM;
                    if (tlmg == "")
                        tlmg = "0";
                    var arrThuocUrl = [];
                    var arrMavattu = [];
                    for (var i = 0; i < count; i++) {
                        var ret = $("#tdt_list_toacu").jqGrid('getRowData', str[i]);
                        var sl = ret.SO_LUONG;
                        var sl = (sl != "") ? parseInt(sl) : 0;
                        var thanhtien = ret.THANHTIEN_THUOC;
                        /// End VPC
                        var mavattu = ret.MAVATTU;
                        var tenthuongmai = ret.TEN_VAT_TU;
                        var tengoc = ret.HOAT_CHAT;
                        var dvt = ret.DVT;
                        var dangthuoc = ret.CACH_SU_DUNG;
                        var cachdung = ret.GHI_CHU_CT_TOA_THUOC;
                        //songay = ret.SO_NGAY_UONG;
                        var sang = ret.SANG_UONG;
                        var trua = ret.TRUA_UONG;
                        var chieu = ret.CHIEU_UONG;
                        var toi = ret.TOI_UONG;
                        var dongia_bv = ret.DONGIA_BAN_BV;
                        var dongia_bh = ret.DONGIA_BAN_BH;
                        thanhtien = ret.THANHTIEN_THUOC;
                        var nghiepvu =  type == "VTYT"? $("#tdt_nghiepvutoavattuyte").val() :$("#tdt_nghiepvutoathuoc").val();
                        var arr = [singletonObject.dvtt,
                            matoathuoc, makhovattu, mavattu,
                            tenthuongmai, tengoc, dvt, nghiepvu, sl, sl, dongia_bv, dongia_bh, thanhtien, songay, sang, trua,
                            chieu, toi, cachdung+ " !!!"+singletonObject.userId, todieutriObject.BACSIDIEUTRI, dangthuoc,
                            todieutriObject.STT_DIEUTRI, thongtinhsba.thongtinbn.STT_BENHAN,
                            todieutriObject.STT_DOTDIEUTRI, tu_tuthuoc, tlmg,
                            thongtinhsba.thongtinbn.MA_BENH_NHAN,
                            todieutriObject.SOPHIEUTHANHTOAN,
                            singletonObject.makhoa, todieutriObject.SOVAOVIEN, todieutriObject.SOVAOVIEN_DT];
                        arrThuocUrl.push(convertArray(arr));
                        arrMavattu.push(mavattu)
                    }
                    if (arrThuocUrl.length > 0) {
                        loadtoacuNoitruInsert(0, arrThuocUrl, arrMavattu);
                    }
                }
            });
    }).fail(function() {
        hideSelfLoading("action_tdtloatc_laytc");
        notifiToClient("Red", "Lỗi kết nối đến máy chủ!");
    });
}
function loadtoacuNoitruInsert(icur, arrThuocUrl, arrMavattu) {
    if (icur === arrThuocUrl.length) {
        var url_kt = "noitru_toathuoc_toacu_kiemtraptinsert";
        var arr_kt = [singletonObject.dvtt,
            thongtinhsba.thongtinbn.STT_BENHAN,
            todieutriObject.STT_DOTDIEUTRI,
            $("#tdt_toacu_danhsach").val(),
            todieutriObject.STT_DIEUTRI,
            $("#tdt_toacu_nghiepvu").val()
        ];
        $.post(url_kt, {url: convertArray(arr_kt)}).done(function (data) {
            if (data.toString() != "[]") {
                var str = "";
                $.each(data, function (i) {
                    str = str + data[i].TEN_VAT_TU + ", ";
                });
                str = str.substring(0, str.length - 2);
                if (str.trim() != "") {
                    notifiToClient("Red","Thuốc/vật tư " + str + " không thêm được vào toa.");
                }
            }
        });
        hideSelfLoading("action_tdtloatc_laytc");
        $("#modalTodieutriLoadToacu").modal("hide");
        return false;
    }
    var url = "noitru_toathuoc_insert";
    $.post(
        url, {url: arrThuocUrl[icur]}
    ).done(function (data) {
        if (data == "4") {
            notifiToClient("Red", "Bệnh nhân đã thanh toán rồi");
        }
        else if (data == "5") {
            notifiToClient("Red", "Bệnh nhân đã xuất thuốc rồi");

        }
        else if (data == "3") {
            notifiToClient("Red", "Thuốc đã có trong toa");
        }
        else if (data == "6") {
            notifiToClient("Red", "Số lượng thuốc vượt số lượng tồn kho");
        }
        else if (data == '100') {
            notifiToClient("Red", "Đã chốt báo cáo dược, không thể sửa/xóa");
        }
        else {
            $.logThuocVattuTodieutri("Thuốc/vật tư: " +arrThuocUrl[3] + " - "+ arrThuocUrl[4]
                + " - Số lượng: "+ arrThuocUrl[8] +" " + arrThuocUrl[6]);
            if($("#action_tdtloatc_laytc").attr("data-type") == "VTYT") {
                tdtloaddstoavattuyte()
            } else {
                tdtloaddstoathuoc();
            }
        }

    }).always(function () {
        loadtoacuNoitruInsert(icur + 1, arrThuocUrl, arrMavattu);
    })
}
$(function() {
    $("#tdt_nghiepvutoathuoc").change(function() {
        $("#tdt_thuoc_dvt").prop("disabled", true);
        clearInputToathuoc();
        var loaiVT = '';
        if($(this).val() == 'noitru_toathuoc' || $(this).val() == 'ba_ngoaitru_toathuoc') {
            loadKhoThuocvattu(singletonObject.danhsachkhothuocBHYT)
            loaiVT = 'VT_TH'

        }
        if($(this).val() == 'noitru_toavattu' || $(this).val() == 'ba_ngoaitru_toavattu') {
            loadKhoThuocvattu(singletonObject.danhsachkhovattu)
            loaiVT = 'TH'
        }
        if($(this).val() == 'noitru_toamienphi' || $(this).val() == 'ba_ngoaitru_toamienphi') {
            loadKhoThuocvattu(singletonObject.danhsachkhomienphi)
        }
        if($(this).val() == 'noitru_toaquaybanthuocbv' || $(this).val() == 'ba_ngoaitru_toaquaybanthuocbv') {
            loadKhoThuocvattu(singletonObject.danhsachkhomuataiquay)
        }
        if($(this).val() == 'noitru_toadongy' || $(this).val() == 'ba_ngoaitru_toadongy') {
            loadKhoThuocvattu(singletonObject.danhsachkhodongy)
        }
        if($(this).val() == 'noitru_toadichvu'  || $(this).val() == 'ba_ngoaitru_toadichvu') {
            loadKhoThuocvattu(singletonObject.danhsachkhodichvu)
        }
        loadKhothuocmacdinh();
        if($(this).val() == 'noitru_toamuangoai'  || $(this).val() == 'ba_ngoaitru_toamuangoai') {
            $("#tdt_thuoc_dvt").prop("disabled", false);
            $("#tdt_khothuoc").html("")
            $("#tdt_khothuoc").val("")
            var url = "layvattu_mn";
            $("#tdt_thuoc_tenthuongmai").combogrid("option", "url", url);
            var url_tg = "layvattu_mn_theotengoc";
            $("#tdt_thuoc_tengoc").combogrid("option", "url", url_tg);
        } else {
            changeSourceComboGrid($("#tdt_khothuoc").val(), loaiVT)
        }
    })
    $("#tdt_khothuoc").change(function (evt) {
        var maloai = "";
        if($("#tdt_nghiepvutoathuoc").val() == 'noitru_toathuoc' || $("#tdt_nghiepvutoathuoc").val() == 'ba_ngoaitru_toathuoc') {
            maloai = 'VT_TH';
        }
        if($("#tdt_nghiepvutoathuoc").val() == 'noitru_toavattu' || $("#tdt_nghiepvutoathuoc").val() == 'ba_ngoaitru_toavattu' ) {
            maloai = 'TH';
        }
        localStorage.setItem($("#tdt_nghiepvutoathuoc").val(), $(this).val());
        changeSourceComboGrid($(this).val(), maloai)
    });
    $("#tdt_thuoc_soluong").keypress(function(evt) {
        if (evt.keyCode == 13 && $("#tdtFormthuocvtnoitru").valid()) {
            $("#tdt_thuoc_tenthuongmai").focus();
            if($("#tdt_nghiepvutoathuoc").val() == 'noitru_toamuangoai' || $("#tdt_nghiepvutoathuoc").val() == 'ba_ngoaitru_toamuangoai') {
                return insertToathuocmuangoai();
            }
            if ((thongtinhsba.thongtinbn.COBHYT != 0 && parseInt($("#tdt_thuoc_ngayuong").val()) <= parseInt(thongtinhsba.thongtinbn.songayconBHYT)) || (thongtinhsba.thongtinbn.COBHYT == 0)) {
                var dvtThuoc = $("#tdt_thuoc_dvt").val().toLowerCase();
                if ((dvtThuoc == "chai" || dvtThuoc== "lọ"
                    || dvtThuoc == "lọ, xịt" || dvtThuoc == "lọ,xịt"
                    || dvtThuoc == "lọ/xịt" || dvtThuoc == "lọ xịt") && $("#tdt_thuoc_soluong").val() > 1) {
                    var confirmModal = $.confirm({
                        title: 'Xác nhận!',
                        type: 'orange',
                        content:'Dược vật tư có ĐƠN VỊ TÍNH là CHAI hoặc LỌ hoặc LỌ, XỊT số lượng không quá 1',
                        buttons: {
                            warning: {
                                btnClass: 'btn-warning',
                                text: "Tiếp tục",
                                action: function(){
                                    confirmModal.close();
                                    tdtthemthuocvattu();
                                }
                            },
                            cancel: function () {
                                $("#tdt_thuoc_soluong").focus();
                            }
                        }
                    });
                } else {
                    tdtthemthuocvattu();
                }
            } else {
                var confirmModal = $.confirm({
                    title: 'Xác nhận!',
                    type: 'orange',
                    content:'Số ngày nhiều hơn số ngày còn trên BHYT. Nhấn "Tiếp tục" để ra thuốc. Nhấn "Hủy" để sửa lại số ngày.',
                    buttons: {
                        warning: {
                            btnClass: 'btn-warning',
                            text: "Tiếp tục",
                            action: function(){
                                confirmModal.close();
                                tdtthemthuocvattu();
                            }
                        },
                        cancel: function () {
                            $("#tdt_thuoc_ngayuong").focus();
                        }
                    }
                });
            }
        }
    })
    $("#tdt_thuoc_dvt").keypress(function(evt) {
        if (evt.keyCode == 13) {
            $("#tdt_thuoc_dangthuoc").focus();
        }
    })

    $("#tdt_thuoc_tenthuongmai").keypress(function(evt) {
        if (evt.keyCode == 13) {
            $("#tdt_thuoc_tengoc").focus();
        }
    })
    $("#tdt_thuoc_tengoc").keypress(function(evt) {
        if (evt.keyCode == 13) {
            $("#tdt_thuoc_dangthuoc").focus();
        }
    })
    $("#tdt_thuoc_dangthuoc").keypress(function(evt) {
        if (evt.keyCode == 13) {
            $("#tdt_thuoc_cachdung").focus();
        }
    })
    $("#tdt_thuoc_cachdung").keypress(function(evt) {
        if (evt.keyCode == 13) {
            $("#tdt_thuoc_ngayuong").focus();
        }
    })
    $("#tdt_thuoc_ngayuong").keypress(function(evt) {
        if (evt.keyCode == 13) {
            $("#tdt_thuoc_sang").focus();
        }
    })
    $("#tdt_thuoc_sang").keypress(function(evt) {
        if (evt.keyCode == 13) {
            $("#tdt_thuoc_trua").focus();
            tinhsoluong();
        }
    })
    $("#tdt_thuoc_trua").keypress(function(evt) {
        if (evt.keyCode == 13) {
            $("#tdt_thuoc_chieu").focus();
            tinhsoluong();
        }
    })
    $("#tdt_thuoc_chieu").keypress(function(evt) {
        if (evt.keyCode == 13) {
            $("#tdt_thuoc_toi").focus();
            tinhsoluong();
        }
    })
    $("#tdt_thuoc_toi").keypress(function(evt) {
        if (evt.keyCode == 13) {
            $("#tdt_thuoc_soluong").focus();
            tinhsoluong();
        }
    })

    $("body").on("click", ".tdt_thuoc_btnup", function() {
        upPosition($(this).attr("data-id"), "tdt_list_thuoc");
    })
    $("body").on("click", ".tdt_thuoc_btndown", function() {
        downPosition($(this).attr("data-id"), "tdt_list_thuoc");
    })

    $("#action_thuoc_xoatoathuoc").click(function() {
        if (parseInt(todieutriObject.KHOALAP) != parseInt(singletonObject.makhoa)) {
            return notifiToClient("Red", "Không thể xóa thuốc được cho bởi khoa khác.");
        }
        var url = "noitru_select_tt_phieudt";
        var arr = [todieutriObject.STT_DIEUTRI, singletonObject.dvtt, todieutriObject.STT_DIEUTRI,
            thongtinhsba.thongtinbn.STT_BENHAN, todieutriObject.STT_DOTDIEUTRI];
        var idWrap = "tdt_list_thuoc_wrap";
        showLoaderIntoWrapId(idWrap);
        showSelfLoading("action_thuoc_xoatoathuoc");
        $.post(url, {url: convertArray(arr)}).done(function (dt) {
            var tt_pcshientai = parseInt(dt);
            if (tt_pcshientai > 1) {
                hideLoaderIntoWrapId("tdt_list_thuoc_wrap");
                hideSelfLoading("action_thuoc_xoatoathuoc");
                return notifiToClient("Red", "Phiếu điều trị đã được dự trù thuốc hoặc cập nhật trạng thái, không được xóa!");
            }
            $.confirm({
                title: 'Xác nhận!',
                type: 'orange',
                content: 'Bạn có muốn xóa nguyên toa?',
                buttons: {
                    warning: {
                        btnClass: 'btn-warning',
                        text: "Tiếp tục",
                        action: function(){
                            var data = "0";
                            if (data == "1") {
                                hideSelfLoading("action_thuoc_xoatoathuoc");
                                return notifiToClient("Red", "Bệnh nhân đã thanh toán. Không thể xóa thuốc")
                            }
                            if (data == "2") {
                                hideSelfLoading("action_thuoc_xoatoathuoc");
                                return notifiToClient("Red", "Thuốc đã được xuất. Không thể xóa thuốc")
                            }
                            var listToathuoc = $("#tdt_list_thuoc");
                            var str = listToathuoc.jqGrid('getDataIDs');
                            if (str != "" && str != null) {
                                var count = str.length;
                                var res = 0;
                                for (var i = 0; i < count; i++) {
                                    var ret = listToathuoc.jqGrid('getRowData', str[i]);
                                    if (ret.MA_BAC_SI_THEMTHUOC == singletonObject.userId || ret.NGUOITAO == singletonObject.userId || singletonObject.admin == "1") {
                                        var url = "noitru_toathuoc_delete";
                                        var nghiepvu = ret.NGHIEP_VU;

                                        var arr = [ret.STT_TOATHUOC, ret.MA_TOA_THUOC, singletonObject.dvtt,
                                            ret.MA_TOA_THUOC, thongtinhsba.thongtinbn.STT_BENHAN,
                                            todieutriObject.STT_DOTDIEUTRI, todieutriObject.SOPHIEUTHANHTOAN,
                                            ret.THANHTIEN_THUOC, ret.MAKHOVATTU, ret.DONGIA_BAN_BV, nghiepvu,
                                            ret.MAVATTU, ret.TEN_VAT_TU, ret.SO_LUONG];
                                        $.post(url, {
                                            url: convertArray(arr)
                                        }).done(function (data) {
                                            if (data == '100') {
                                                hideSelfLoading("action_thuoc_xoatoathuoc");
                                                hideLoaderIntoWrapId(idWrap);
                                                return notifiToClient("Red", "Đã chốt báo cáo dược, không thể sửa/xóa");
                                            }
                                            if (data == '-4') {
                                                hideSelfLoading("action_thuoc_xoatoathuoc");
                                                hideLoaderIntoWrapId(idWrap);
                                                return notifiToClient("Red", "Thuốc đã được thánh toán, không thể sửa/xóa");
                                            }
                                            if (data == '200') {
                                                hideSelfLoading("action_thuoc_xoatoathuoc");
                                                hideLoaderIntoWrapId(idWrap);
                                                return notifiToClient("Red","Đã pha thuốc, không thể xoá");
                                            }
                                            if (data == '201') {
                                                hideSelfLoading("action_thuoc_xoatoathuoc");
                                                hideLoaderIntoWrapId(idWrap);
                                                return notifiToClient("Red","Thuốc có y lệnh truyền dịch, không thể xoá");
                                            }
                                            $.logDeleteThuocVattuTodieutri(
                                                "Thuốc/vật tư: " +ret.MAVATTU + " - "+
                                                ret.TEN_VAT_TU + " - Số lượng: "+ ret.SO_LUONG +" " + ret.DVT+
                                                "; Nghiệp vụ: "+ $("#tdt_nghiepvutoathuoc option[value='"+nghiepvu+"']").text(),
                                                ret.MAKHOVATTU
                                            );

                                        }).always(function () {
                                            res++;
                                            if (res == count) {
                                                hideLoaderIntoWrapId("tdt_list_thuoc_wrap");
                                                tdtloaddstoathuoc();
                                                hideSelfLoading("action_thuoc_xoatoathuoc");
                                            }
                                        })
                                    }
                                }
                            }
                        }
                    },
                    cancel: function () {
                        hideLoaderIntoWrapId("tdt_list_thuoc_wrap");
                        hideSelfLoading("action_thuoc_xoatoathuoc");
                    }
                }
            });
        });
    })

    $("#action_tdtloatc_close").click(function() {
      $("#modalTodieutriLoadToacu").modal("hide");
    })
    $("#action_loadtoacu").click(function() {
        initTdtToacuGrid();
        $("#modalTodieutriLoadToacu").modal("show");
        var url = "noitru_ds_dotdieutri?stt_benhan=" + thongtinhsba.thongtinbn.STT_BENHAN +
            "&stt_dotdieutri=" + todieutriObject.STT_DOTDIEUTRI + "&dvtt="+singletonObject.dvtt;
        $("#tdt_toacu_nghiepvu").val($("#tdt_nghiepvutoathuoc").val())
        $("#tdt_toacu_nghiepvu").prop("disabled", true);
        showLoaderIntoWrapId("tdt_list_thuoc_wrap");
        hideSelfLoading("action_tdtloatc_laytc")
        $.get(url).done(function(dt) {

            $("#tdt_toacu_danhsach").html("");
            var dtSort = dt.filter(function(item) {
                return item.ID_DIEUTRI != todieutriObject.ID_DIEUTRI;
            })
            dtSort = _.orderBy(dtSort, [function (obj){ return Number(obj.STT_DIEUTRI) }], ['desc']);
            dtSort.forEach(function(val, index) {
                var selected = index == 0? "selected":   "";
                $("#tdt_toacu_danhsach").append("<option "+selected+" value='"+val.STT_DIEUTRI+"'>"+val.STT_DIEUTRI
                    + " - " + val.NGAYGIOLAP_TDT
                    + " - " + val.TEN_NHANVIEN
                    +"</option>");
            })
            if ($('#tdt_toacu_danhsach').hasClass("select2-hidden-accessible")) {
                $("#tdt_toacu_danhsach").select2("destroy");
            }
            $("#tdt_toacu_danhsach").select2();
            $("#action_tdtloatc_laytc").attr("data-type", "THUOC")
            loadToacuTheoPhieudt();

        }).fail(function() {
            notifiToClient("Red", "Lỗi lấy dữ liệu đợt điều trị");
        }).always(function() {
            hideLoaderIntoWrapId("tdt_list_thuoc_wrap");
        })
    })

    $("#tdt_toacu_danhsach").change(function() {
        loadToacuTheoPhieudt();
    })
    $("#action_tdtloatc_laytc").click(function() {
        var allToathuoc = $("#tdt_list_toacu").jqGrid('getDataIDs');
        if(allToathuoc.length == 0) {
            return notifiToClient("Red", "Không có dữ liệu để lấy");
        }
        var error = false
        for (var i = 0; i < allToathuoc.length; i++) {
            var item = $("#tdt_list_toacu").jqGrid('getRowData', allToathuoc[i])
            if (item.SO_NGAY_UONG == "" || item.SO_NGAY_UONG == "0" || item.SO_LUONG == "" || item.SO_LUONG == 0) {
                error = true
            }
        }
        if (error) {
            return notifiToClient("Red", "Số lượng phải lớn hơn 0");
        }
        loadToaCuVaoToaThuoc();
    })
    $("#tdt-thuoc").click(function() {
        tdtloaddstoathuoc();
    })
    $('#tdt-thuoc-btn-dropdown p').click(function () {
        var attrId = $(this).attr('data-id');
        if (attrId == 'xemtoanoitru') {
            var icdTdt = getAllRowDataJqgrid("tdt_list_icd")

            var icdChandoanbenh = thongtinhsba.thongtinbn.ICD_NHAPVIEN;
            var chandoanbenh = thongtinhsba.thongtinbn.TENBENHCHINH_NHAPVIEN;
            let benhKemTheo = "\nChẩn đoán phụ: ";
            if(icdTdt.length > 0) {
                icdTdt.forEach(function(object) {
                    if(object.BENHCHINH == 1) {
                        icdChandoanbenh = object.ICD;
                        chandoanbenh = object.TENBENH;
                    }
                    else {
                        benhKemTheo += ("(" + object.ICD + ") " + object.TENBENH + "; ");
                    }
                })
            }
            chandoanbenh = singletonObject.thamSo960635 === "1" ? ("(" + icdChandoanbenh + ") " + chandoanbenh + benhKemTheo) : chandoanbenh;
            $.todieutriXemtoathuocnoitru($("#tdt_nghiepvutoathuoc").val(), chandoanbenh, icdChandoanbenh, todieutriObject)
        }
        if (attrId == 'xemtoanoitruchiphi') {
            var tenkhoa = singletonObject.tenkhoa;
            singletonObject.danhsachphongban.forEach(function(object) {
                if(object.MAKHOA == singletonObject.makhoa) {
                    tenkhoa = object.TENKHOA;
                }
            })
            $.todieutriXemtoathuocchiphi({
                NGHIEPVU: $("#tdt_nghiepvutoathuoc").val(),
                STT_DOTDIEUTRI: todieutriObject.STT_DOTDIEUTRI,
                ID_DIEUTRI: todieutriObject.ID_DIEUTRI,
                MABENHNHAN: thongtinhsba.thongtinbn.MABENHNHAN,
                BHYT: thongtinhsba.thongtinbn.SOBAOHIEMYTE,
                HOTEN: thongtinhsba.thongtinbn.TEN_BENH_NHAN,
                NAMSINH: thongtinhsba.thongtinbn.NGAY_SINH,
                STT_BENHAN: thongtinhsba.thongtinbn.STT_BENHAN,
                KHOA: tenkhoa,
                DIACHI: thongtinhsba.thongtinbn.DIA_CHI
            })
        }
        if (attrId == 'loadtoamau') {
            if(!$("#tdt_khothuoc").val()) return notifiToClient("Red", "Vui lòng chọn kho");
            $("#modalLoadtoamau").modal("show");
            $("#thuocvattuLoadToamau").attr("data-function", "loadToamauNoitruTodieutri");
        }

        if(attrId == 'bosungylenhtruoc') {
            $.bosungylenhkhactdt();
        }
    });
    $("#cap_nhat_gio_ylenh").click(function() {
        $("#tdt_ylenh_thuoc_luu").attr("data-function", "capnhatYlenhthuocTDT")
        $("#tdt_thuoc_gioylenh").val("");
        $("#modalCapnhatgioylenhThuoc").modal("show");
    })

    $("#tdt_ylenh_thuoc_luu").click(function() {
        var idButton = this.id;
        var functionCall = $(this).attr("data-function");
        $[functionCall](idButton);

    })

    $("#modalChuyenNghiepvuduoc").on("shown.bs.modal", function () {
        var rowData = getThongtinRowSelected("list_thuoc")
        var items = {
            noitru_toathuoc: "Toa thuốc ",
            noitru_toavattu: "Toa vật tư",
            noitru_toadichvu: "Toa dịch vụ",
            noitru_toaquaybanthuocbv: "Toa mua tại quầy bv",
            noitru_toamienphi: "Toa miễn phí",
            noitru_toadongy: "Toa đông y",
        }
        if(singletonObject.bant == 1) {
            items = {
                ba_ngoaitru_toathuoc: "Toa thuốc ",
                ba_ngoaitru_toavattu: "Toa vật tư",
                ba_ngoaitru_toadichvu: "Toa dịch vụ",
                ba_ngoaitru_toaquaybanthuocbv: "Toa mua tại quầy bv",
                ba_ngoaitru_toamienphi: "Toa miễn phí",
                ba_ngoaitru_toadongy: "Toa đông y",
            }
        }
        delete items[rowData.NGHIEP_VU];
        $("#tdt_chuyennghiepvuduoc_select").html('')
        Object.keys(items).forEach(function(key, index) {
            $("#tdt_chuyennghiepvuduoc_select").append("<option value='"+key+"'>"+items[key]+"</option>")
            if(index == 0) {
                $("#tdt_chuyennghiepvuduoc_select").val(key);
            }
        })
    })
    $("#tdt_chuyennghiepvuduoc_luu").click(function() {
        if(!$("#tdt_chuyennghiepvuduoc_select").val()) {
            return notifiToClient("Red", "Vui lòng chọn nghiệp vụ")
        }
        var idButton = this.id
        showSelfLoading(idButton)
        var rowData = getThongtinRowSelected("list_thuoc")
        $.post("cmu_post_cmu_update_nghiepvu_duoc", {
            url: [
                singletonObject.dvtt,
                thongtinhsba.thongtinbn.SOVAOVIEN,
                thongtinhsba.thongtinbn.SOVAOVIEN_DT,
                rowData.STT_TOATHUOC,
                rowData.MA_TOA_THUOC,
                $("#tdt_chuyennghiepvuduoc_select").val()
            ].join("```")
        }).done(function(data) {
            if(data == -1) {
                return  notifiToClient("Red", "Thuốc/vật tư đã thanh toán")
            }
            if(data > 0) {
                luuLogHSBATheoBN({
                    SOVAOVIEN: thongtinhsba.thongtinbn.SOVAOVIEN,
                    LOAI: LOGHSBALOAI.THUOCVATTU.KEY,
                    NOIDUNGBANDAU: "Thuốc/vật tư: "+ rowData.MAVATTU + "- "+rowData.TEN_VAT_TU + "; Số phiếu: "+ rowData.MA_TOA_THUOC+ "; Nghiệp vụ: "+ rowData.MOTA_NGHIEPVU,
                    NOIDUNGMOI: "Chuyển nghiệp vụ mới: "+  $("#tdt_chuyennghiepvuduoc_select option:selected").text() ,
                    USERID: singletonObject.userId,
                    ACTION: LOGHSBAACTION.EDIT.KEY,
                })
                notifiToClient("Green", MESSAGEAJAX.SUCCESS)
            } else {
                notifiToClient("Red", MESSAGEAJAX.ERROR)
            }
        }).fail(function() {
            notifiToClient("Red", MESSAGEAJAX.FAIL)
        }).always(function(){
            hideSelfLoading(idButton)
        })
    })

    $.extend({
        loadToamauNoitruTodieutri: function() {
            var list = $("#listThuocvattuToamau");
            var rowIds = list.jqGrid('getDataIDs');
            if(rowIds.length == 0) {
                return notifiToClient("Red", "Chưa có dữ liệu để load");
            }
            $("#loadToamauProgressWrap").show()
            $("#loadToamauProgressWrap .progress-bar").css("width", "0%");
            var tu_tuthuoc = $.ajax({
                url: "noitru_kiemtra_tututhuoc",
                type: "POST",
                data: {
                    dvtt: singletonObject.dvtt, maphongban: singletonObject.makhoa, makho: $("#tdt_khothuoc").val()
                },
                async: false,
            }).responseText
            var doneTask = 0;
            for(var i = 0; i < rowIds.length; i++) {
                var ret = list.jqGrid('getRowData', rowIds[i]);
                list.jqGrid('setCell', rowIds[i], 'TRANGTHAI', '<span class="spinner-border spinner-border-sm spinner" role="status" aria-hidden="true"></span>');
                var sl = ret.SO_LUONG;
                sl = (sl != "") ? parseInt(sl) : 0;
                var thanhtien = 0;
                var tlmg = thongtinhsba.thongtinbn.TYLEBAOHIEM;
                if (tlmg == "")
                    tlmg = "0";
                var mavattu = ret.MAVATTU;
                var tenthuongmai = ret.TEN_VAT_TU;
                var tengoc = ret.HOAT_CHAT;
                var dvt = ret.DVT;
                var dangthuoc = ret.CACH_SU_DUNG;
                var cachdung = ret.GHI_CHU_CT_TOA_THUOC;
                var songay = 1;
                var sang = 0;
                var trua = 0;
                var chieu = 0;
                var toi = 0;
                var dongia_bv = 0;
                var dongia_bh = 0;
                var nghiepvu =  $("#tdt_nghiepvutoathuoc").val();
                var arr = [singletonObject.dvtt,
                    todieutriObject.STT_DIEUTRI, $("#tdt_khothuoc").val(), mavattu,
                    tenthuongmai, tengoc, dvt, nghiepvu, sl, sl, dongia_bv, dongia_bh, thanhtien, songay, sang, trua,
                    chieu, toi, cachdung+ " !!!"+singletonObject.userId, todieutriObject.BACSIDIEUTRI, dangthuoc,
                    todieutriObject.STT_DIEUTRI, thongtinhsba.thongtinbn.STT_BENHAN,
                    todieutriObject.STT_DOTDIEUTRI, tu_tuthuoc, tlmg,
                    thongtinhsba.thongtinbn.MA_BENH_NHAN,
                    todieutriObject.SOPHIEUTHANHTOAN,
                    singletonObject.makhoa, todieutriObject.SOVAOVIEN, todieutriObject.SOVAOVIEN_DT];
                console.log("arr", arr)
                $.ajax({
                    url: "noitru_toathuoc_insert",
                    type: "POST",
                    data: {
                        url: convertArray(arr)
                    },
                    async: false,
                    success: function(data) {
                        switch (data) {
                            case "4":
                                list.jqGrid('setCell', rowIds[i], 'TRANGTHAI', '<span class="text-danger">Bệnh nhân đã thanh toán rồi<span>');
                                break;
                            case "5":
                                list.jqGrid('setCell', rowIds[i], 'TRANGTHAI', '<span class="text-danger">Bệnh nhân đã xuất thuốc rồi<span>');
                                break;
                            case "3":
                                list.jqGrid('setCell', rowIds[i], 'TRANGTHAI', '<span class="text-danger">Thuốc đã có trong toa<span>');
                                break;
                            case "6":
                                list.jqGrid('setCell', rowIds[i], 'TRANGTHAI', '<span class="text-danger">Số lượng thuốc vượt số lượng tồn kho<span>');
                                break;
                            case "100":
                                list.jqGrid('setCell', rowIds[i], 'TRANGTHAI', '<span class="text-danger">Đã chốt báo cáo dược, không thể sửa/xóa<span>');
                                break;
                            default:
                                list.jqGrid('setCell', rowIds[i], 'TRANGTHAI', '<i style="font-size: 18px" class="fa fa-check-circle text-primary"></i>');
                                break;
                        }
                        doneTask++;
                        $("#loadToamauProgressWrap .progress-bar").css("width", (doneTask/rowIds.length*100) + "%");
                        if(doneTask == rowIds.length) {
                            tdtloaddstoathuoc();
                        }
                    },
                    error: function() {
                        list.jqGrid('setCell', rowIds[i], 'TRANGTHAI', '<span class="text-danger">'+MESSAGEAJAX.ERROR+'<span>');
                        doneTask++;
                        $("#loadToamauProgressWrap .progress-bar").css("width", (doneTask/rowIds.length*100) + "%");
                        if(doneTask == rowIds.length) {
                            tdtloaddstoathuoc();
                        }
                    }
                });
            }
        },
        todieutriXemtoathuocnoitru: function(nghiepvu, chandoanbenh, icdChandoanbenh, object) {
            var arr = [
                singletonObject.dvtt,
                thongtinhsba.thongtinbn.STT_BENHAN,
                object.STT_DOTDIEUTRI,
                object.STT_DIEUTRI,
                thongtinhsba.thongtinbn.MABENHNHAN,
                object.STT_DIEUTRI,
                object.SOPHIEUTHANHTOAN,
                nghiepvu,
                chandoanbenh, icdChandoanbenh, "0"];
            var url = "noitru_intoathuoc?url=" + convertArray(arr);
            previewPdfDefaultModal(url, "previewtdt_toathuoc_noitru");
        },
        todieutriXemtoathuocchiphi: function(object) {
            console.log("object", object)
            var params = {
                dvtt: singletonObject.dvtt,
                tenkhoakham: object.KHOA,
                hotennguoibenh: object.HOTEN,
                namsinh: object.NAMSINH,
                diachi: object.DIACHI,
                stt_benhan: object.STT_BENHAN,
                mabenhnhan: object.MABENHNHAN,
                stt_dotdieutri: object.STT_DOTDIEUTRI,
                dotdieutri: object.STT_DOTDIEUTRI,
                nghiepvu: object.NGHIEPVU,
                id_dieutri: object.ID_DIEUTRI,
                bhyt: object.BHYT
            }
            var url = "cmu_in_nt_hsba_inthuoc_theotoa?" + $.param(params) + "&type=pdf";
            previewPdfDefaultModal(url, "previewtdt_toathuoc_noitru");
        },
        logThuocVattuTodieutri: function(noidung) {
            logThuocVattuTodieutri(noidung);
        },
        logDeleteThuocVattuTodieutri: function(noidung, makhovattu) {
            var kho = [
                ...singletonObject.danhsachkhodichvu,
                ...singletonObject.danhsachkhovattu,
                ...singletonObject.danhsachkhothuocBHYT,
                ...singletonObject.danhsachkhomienphi,
                ...singletonObject.danhsachkhomuataiquay,
            ]
            var tenkho = ''
            kho.forEach(function(item) {
                if(item.MAKHO == makhovattu) {
                    tenkho = item.TENKHO;
                }
            })

            logDeleteThuocVattuTodieutri(noidung + "; Kho: " + tenkho);
        },
        capnhatYlenhthuocTDT: function(idButton) {
            var allRowDataJqgrid = getAllRowDataJqgrid("tdt_list_thuoc")
            if(allRowDataJqgrid.length == 0) {
                return notifiToClient("Red", "Không có dữ liệu để cập nhật");
            }
            if($("#ttdtFormCapnhatgioylenhThuoc").valid() ) {
                showSelfLoading(idButton);
                $.post("cmu_post", {
                    url: [
                        singletonObject.dvtt,
                        todieutriObject.STT_DIEUTRI,
                        todieutriObject.STT_DOTDIEUTRI,
                        thongtinhsba.thongtinbn.STT_BENHAN,
                        $("#tdt_thuoc_gioylenh").val(),
                        "CMU_TDT_THUOC_UPDATE_GYL"
                    ].join("```")
                }).done(function(data) {
                    if(data > 0) {
                        notifiToClient("Green", MESSAGEAJAX.EDIT_SUCCESS);
                        var thongtinthuoc = [];
                        var thongtinthuoc2 = [];
                        allRowDataJqgrid.forEach(function(item) {
                            thongtinthuoc.push(item.TEN_VAT_TU + " - Số lượng: "+ item.SO_LUONG + " " + item.DVT + " - Thời gian y lệnh:" + item.NGAY_YL)
                            thongtinthuoc2.push(item.TEN_VAT_TU + " - Số lượng: "+ item.SO_LUONG + " " + item.DVT )
                        })
                        var logThuocVTYT = [
                            "Tờ điều trị số: " + todieutriObject.STT_DIEUTRI + " - " + todieutriObject.NGAYGIOLAPTDT,
                            "Bác sĩ điều trị: "+ todieutriObject['TENBSDIEUTRI']
                        ]
                        luuLogHSBATheoBN({
                            SOVAOVIEN: thongtinhsba.thongtinbn.SOVAOVIEN,
                            LOAI: LOGHSBALOAI.THUOCVATTU.KEY,
                            NOIDUNGBANDAU: logThuocVTYT.join("; ") + "; "+ thongtinthuoc.join(",") ,
                            NOIDUNGMOI: "Cập nhật thời gian y lệnh mới: "+  $("#tdt_thuoc_gioylenh").val()+ ";" + logThuocVTYT.join("; ") + "; " + thongtinthuoc2.join(","),
                            USERID: singletonObject.userId,
                            ACTION: LOGHSBAACTION.EDIT.KEY,
                        })
                        $("#modalCapnhatgioylenhThuoc").modal("hide")
                        $("#tdt-thuoc").click()
                    } else {
                        notifiToClient("Red", MESSAGEAJAX.FAIL);
                    }

                }).fail(function() {
                    notifiToClient("Red", MESSAGEAJAX.ERROR);
                }).always(function() {
                    hideSelfLoading(idButton);
                })
            }
        },
    })
    $("#tdtFormthuocvtnoitru").validate({
        rules: {
            tdt_thuoc_ngayuong: {
                min: 1
            },
            tdt_thuoc_soluong: {
                min: 0
            },
            tdt_thuoc_sang: {
                min: 0
            },
            tdt_thuoc_trua: {
                min: 0
            },
            tdt_thuoc_chieu: {
                min: 0
            },
            tdt_thuoc_toi: {
                min: 0
            }
        }
    })
    $("#ttdtFormCapnhatgioylenhThuoc").validate({
        rules: {
            NGAYYLENHTHUOC: {
                validDateTime: true
            },
        }
    })
    function tinhsoluong() {
        var ngayuong = $("#tdt_thuoc_ngayuong").val() != "" ? parseInt($("#tdt_thuoc_ngayuong").val()) : 0;
        var sang = $("#tdt_thuoc_sang").val() != "" ? Number($("#tdt_thuoc_sang").val()) : 0;
        var trua = $("#tdt_thuoc_trua").val() != "" ? Number($("#tdt_thuoc_trua").val()) : 0;
        var chieu = $("#tdt_thuoc_chieu").val() != "" ? Number($("#tdt_thuoc_chieu").val()) : 0;
        var toi = $("#tdt_thuoc_toi").val() != "" ? Number($("#tdt_thuoc_toi").val()) : 0;
        var soluong = sang + trua + chieu + toi;
        $("#tdt_thuoc_soluong").val(Math.round(soluong*ngayuong));
    }

    function insertToathuocmuangoai() {
        var objectJson = convertDataFormToJson("tdtFormthuocvtnoitru");
        var sl = Number(objectJson.tdt_thuoc_soluong);
        var matoathuoc = todieutriObject.STT_DIEUTRI;
        var loidan = "";
        var tenthuongmai = objectJson.tdt_thuoc_tenthuongmai;
        var tengoc = objectJson.tdt_thuoc_tengoc;
        var dvt = objectJson.tdt_thuoc_dvt;
        var dangthuoc = objectJson.tdt_thuoc_dangthuoc;
        var cachdung = objectJson.tdt_thuoc_cachdung;
        var songay = objectJson.tdt_thuoc_ngayuong;
        var sang = objectJson.tdt_thuoc_sang;
        if (sang == "")
            sang = "0";
        var trua = objectJson.tdt_thuoc_trua;
        if (trua == "")
            trua = "0";
        var chieu = objectJson.tdt_thuoc_chieu;
        if (chieu == "")
            chieu = "0";
        var toi = objectJson.tdt_thuoc_toi;
        if (toi == "")
            toi = "0";
        var soluong = objectJson.tdt_thuoc_soluong;
        var nghiepvu = "noitru_toamuangoai";
        var sodangky_mn = ".";

        var mathuoc_mn = objectJson.tdt_thuoc_mavattu == "" ? "0" : objectJson.tdt_thuoc_mavattu;
        var arr = [singletonObject.dvtt,
            matoathuoc, tenthuongmai, tengoc,
            dvt, nghiepvu, soluong, soluong, songay, sang, trua, chieu, toi, dangthuoc+ " !!!"+singletonObject.userId,
            todieutriObject.BACSIDIEUTRI, cachdung,
            todieutriObject.STT_DIEUTRI,
            thongtinhsba.thongtinbn.STT_BENHAN,
            todieutriObject.STT_DOTDIEUTRI,
            todieutriObject.SOVAOVIEN,
            todieutriObject.SOVAOVIEN_DT,
            sodangky_mn, mathuoc_mn, "0"];
        var url = "noitru_toathuocmuangoai_insert";
        showLoaderIntoWrapId("tdt_list_thuoc_wrap");
        $("#tdt_thuoc_tenthuongmai").focus();
        $.post(url, {
            url: convertArray(arr)
        }).done(function (data) {
            if (data == "1") {
                notifiToClient("Red", "Thuốc đã có trong toa");
            } else {
                logThuocVattuTodieutri("Thêm thuốc mua ngoài: " + tenthuongmai + " - Số lượng: "+ sl +" " + dvt);
                tdtloaddstoathuoc();
                clearInputToathuoc();
            }
        }).fail(function() {
            notifiToClient("Red", "Lỗi thêm thuốc vào toa");
        }).always(function () {
            hideLoaderIntoWrapId("tdt_list_thuoc_wrap");
        })
    }

    function logThuocVattuTodieutri(noidung) {
        var logThuocVTYT = [
            "Tờ điều trị số: " + todieutriObject.STT_DIEUTRI + " - " + todieutriObject.NGAYGIOLAPTDT,
            "Bác sĩ điều trị: "+ todieutriObject['TENBSDIEUTRI'],
            noidung
        ]
        luuLogHSBATheoBN({
            SOVAOVIEN: thongtinhsba.thongtinbn.SOVAOVIEN,
            LOAI: LOGHSBALOAI.THUOCVATTU.KEY,
            NOIDUNGBANDAU: "",
            NOIDUNGMOI: logThuocVTYT.join("; "),
            USERID: singletonObject.userId,
            ACTION: LOGHSBAACTION.INSERT.KEY,
        })
    }

    function logDeleteThuocVattuTodieutri(noidung) {
        var logThuocVTYT = [
            "Tờ điều trị số: " + todieutriObject.STT_DIEUTRI + " - " + todieutriObject.NGAYGIOLAPTDT,
            "Bác sĩ điều trị: "+ todieutriObject['TENBSDIEUTRI'],
            noidung
        ]
        luuLogHSBATheoBN({
            SOVAOVIEN: thongtinhsba.thongtinbn.SOVAOVIEN,
            LOAI: LOGHSBALOAI.THUOCVATTU.KEY,
            NOIDUNGBANDAU: logThuocVTYT.join("; "),
            NOIDUNGMOI: "",
            USERID: singletonObject.userId,
            ACTION: LOGHSBAACTION.DELETE.KEY,
        })
    }
})