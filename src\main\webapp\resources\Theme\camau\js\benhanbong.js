function getBABONGJSON () {
  var loaiBA = 'BONG'
  var form;
  var formTongket;
  var keyMauHSBABONG = "MAUHSBABONG";
  var keyMauHSBABONGTongket = "MAUHSBABONGTONGKET";
  var formioMauHSBA;
  var formioMauHSBATongket;
  return {
    script: {},
    scriptTongket: {},
    initObjectFormPage1: function(edit, hidden) {
      if(edit) {
        $("#hsba_tthc_chitietbenhan").hide();
        $("#hsba_tthc_luuchitietbenhan").show();
        $("#hsba_tthc_huychitietbenhan").show();
      } else {
        $("#hsba_tthc_chitietbenhan").show();
        $("#hsba_tthc_luuchitietbenhan").hide();
        $("#hsba_tthc_huychitietbenhan").hide();
      }
      return getJSONObjectForm([
        {
          "key": "p-chandoan",
          "type": "tabs",
          "customClass": "hsba-tabs-wrap",
          "components": [
            {
              "label": "QUẢN LÝ NGƯỜI BỆNH",
              "key": "tabQuanLyNguoiBenh",
              "components": [
                getObjectQuanLyNguoiBenhVBAT1_1(edit, hidden),
              ]
            },
            {
              "label": "CHẨN ĐOÁN",
              "key": "tabChanDoan",
              "components": [
                getObjectChanDoanVBAT1_1(edit, hidden),
              ]
            },
            {
              "label": "TÌNH TRẠNG RA VIỆN",
              "key": "tabTinhTrangRaVien",
              "components": [
                getObjectTinhTrangRaVienVBAT1_2(edit, hidden),
              ]
            },
          ]
        },
      ])
    },
    initObjectFormPage2: function() {
      return getJSONObjectForm([
        {
          "collapsible": true,
          "key": "p-lydovaovien",
          "type": "panel",
          "label": "Lý do vào viện",
          "title": "BỆNH ÁN VÀ HỎI BỆNH",
          "collapsed": false,
          "input": false,
          "tableView": false,
          "customClass": "hsba-tabs-wrap",
          "components": [
            {
              label: "lydovaovien",
              key: "colLydovaovien",
              columns: [
                {
                  "components": [
                    {
                      "label": "Lý do vào viện",
                      "key": "LYDOVAOVIEN",
                      "type": "textarea",
                      customClass: "pr-2",
                      rows: 2,
                      "validate": {
                        "required": true
                      }
                    },
                  ],
                  "width": 9,
                  "size": "md",
                },
                {
                  "components": [

                    {
                      "label": "Vào ngày thứ",
                      "key": "VAONGAYTHU",
                      "type": "number",
                      "validate": {
                        "min": 1,
                        max: 200,
                        "required": true
                      }
                    },
                  ],
                  "width": 3,
                  "size": "md",
                },
              ],
              "customClass": "ml-0 mr-0",
              "type": "columns",
            },
            {
              "label": "Quá trình bệnh lý",
              others: {
                "tooltip": "Quá trình bệnh lý: (khởi phát, diễn biến, chẩn đoán, điều trị tuyến dưới, v.v...)",
              },
              "key": "BENHSU",
              "type": "textarea",
              "validate": {
                "minLength": 5,
                "maxLength": 3000,
                "required": true
              }
            },
            {
              "label": "Tiền sử bệnh (bản thân)",
              others: {
                "tooltip": " Bản thân: (phát triển thể lực từ nhỏ đến lớn, những bệnh đã mắc, phương pháp ĐTr, tiêm phòng, ăn uống, sinh hoạt vv...)",
              },
              "key": "TIENSUBANTHAN",
              "type": "textarea",
              "validate": {
                "minLength": 5,
                "maxLength": 3000,
                "required": true
              }
            },
            {
              "label": "Đặc điểm liên quan bệnh",
              "columns": [
                {
                  "components": [
                    {
                      "label": "Dị ứng",
                      "key": "BOXDDDIUNG",
                      "type": "checkbox",
                    },
                    {
                      "label": "Thời gian",

                      "customClass": "pr-2",
                      "key": "DDDIUNG",
                      others : {
                        "tooltip": "Thời gian (Tính theo tháng)",
                        "customConditional": "show = !!data.BOXDDDIUNG;",
                      },
                      "type": "number",
                      "validate": {
                        "min": 1,
                        max: 100,
                      }
                    }
                  ],
                  "width": 4,
                },
                {
                  "components": [
                    {
                      "label": "Ma túy",
                      "key": "BOXDDMATUY",
                      "type": "checkbox",
                    },
                    {
                      "label": "Thời gian",
                      "customClass": "pr-2",
                      "key": "DDMATUY",
                      others: {
                        "customConditional": "show = !!data.BOXDDMATUY;",
                        "tooltip": "Thời gian sử dụng ma túy (Tính theo tháng)",
                      },
                      "type": "number",
                      "validate": {
                        "min": 1,
                        max: 100,
                      }
                    }
                  ],
                  "width": 4,
                  "size": "md",
                },
                {
                  "components": [
                    {
                      "label": "Rượu bia",
                      "key": "BOXDDRUOUBIA",
                      "type": "checkbox",
                    },
                    {
                      "label": "Thời gian",
                      "key": "DDRUOUBIA",
                      others: {
                        "customConditional": "show = !!data.BOXDDRUOUBIA;",
                        "tooltip": "Thời gian sử dụng rượu bia (Tính theo tháng)",
                      },
                      "type": "number",
                      "validate": {
                        "min": 1,
                        max: 100,
                      }
                    }
                  ],
                  "size": "md",
                  "width": 4,
                }
              ],
              "customClass": "ml-0 mr-0",
              "key": "DDLIENQUANBENH",
              "type": "columns",
            },
            {
              "label": "Thuốc",
              "columns": [
                {
                  "components": [
                    {
                      "label": "Thuốc lá",
                      "key": "BOXDDTHUOCLA",
                      "type": "checkbox",
                    },
                    {
                      "label": "Thời gian",
                      "customClass": "pr-2",
                      "validate": {
                        "min": 1,
                        max: 100,
                      },
                      "key": "DDTHUOCLA",
                      others: {
                        "customConditional": "show = !!data.BOXDDTHUOCLA;",
                        "tooltip": "Thời gian sử dụng thuốc lá (Tính theo tháng)",
                      },
                      "type": "number",
                    }
                  ],
                  "width": 4,
                  "size": "md",
                },
                {
                  "components": [
                    {
                      "label": "Thuốc lào",
                      "key": "BOXDDTHUOCLAO",
                      "type": "checkbox",
                    },
                    {
                      "label": "Thuốc lào",

                      "customClass": "pr-2",
                      "validate": {
                        "min": 1,
                        max: 100,
                      },
                      "key": "DDTHUOCLAO",
                      others: {
                        "customConditional": "show = !!data.BOXDDTHUOCLAO;",
                        "tooltip": "Thời gian sử dụng thuốc lào (Tính theo tháng)",
                      },
                      "type": "number",
                    }
                  ],
                  "width": 4,
                  "size": "md",
                },
                {
                  "components": [
                    {
                      "label": "Khác",
                      "key": "BOXDDKHAC",
                      "type": "checkbox",
                    },
                    {
                      "label": "Thời gian",

                      "customClass": "mr-2",
                      "validate": {
                        "min": 1,
                        max: 100,
                      },
                      "key": "DDKHAC",
                      others: {
                        "customConditional": "show = !!data.BOXDDKHAC;",
                        "tooltip": "Thời gian (Tính theo tháng)",
                      },
                      "type": "number",
                    }
                  ],
                  "size": "md",
                  "width": 4,
                }
              ],
              "customClass": "mr-0 ml-0",
              "key": "THUOC",
              "type": "columns",
            },
          ]
        },
        {
          "collapsible": true,
          "key": "p-khambenh",
          "type": "panel",
          "label": "Khám bệnh",
          "title": "KHÁM BỆNH",
          "collapsed": false,
          "input": false,
          "tableView": false,
          "customClass": "hsba-tabs-wrap",
          "components": [
            {
              "label": "left",
              "columns": [
                {
                  "components": [
                    {
                      "label": "Toàn thân",
                      "tooltip": " Toàn thân: (ý thức, da niêm mạc, hệ thống hạch, tuyến giáp, vị trí, kích thước)",
                      "key": "KHAMTOANTHAN",
                      "type": "textarea",
                      "rows": 2,
                      "input": true,
                      "validate": {
                        "minLength": 5,
                        "maxLength": 3000,
                        "required": true
                      }
                    },
                    {
                      "label": "Gia đình",
                      "tooltip": "Gia đình: (Những người trong gia đình: bệnh đã mắc, đời sống, tinh thần, vật chất v.v...)",
                      "key": "TIENSUGIADINH",
                      "type": "textarea",
                      "rows": 2,
                      "validate": {
                        "minLength": 5,
                        "maxLength": 3000,
                      }
                    },
                    {
                      "label": " Các cơ quan",
                      "columns": [
                        {
                          "components": [
                            {
                              "label": "Thần kinh",
                              "customClass": "pr-2",
                              "key": "THANKINH",
                              "type": "textarea",
                              "rows": 2,
                            }
                          ],
                          "width": 4,
                          "offset": 0,
                          "push": 0,
                          "pull": 0,
                          "size": "md",
                          "currentWidth": 4
                        },
                        {
                          "components": [
                            {
                              "label": "Tuần hoàn",
                              "customClass": "pr-2",
                              "key": "TUANHOAN",
                              "type": "textarea",
                              "rows": 2,
                              "input": true
                            }
                          ],
                          "width": 4,
                          "offset": 0,
                          "push": 0,
                          "pull": 0,
                          "size": "md",
                          "currentWidth": 4
                        },
                        {
                          "components": [
                            {
                              "label": "Hô hấp",
                              "customClass": "pr-2",
                              "key": "HOHAP",
                              "type": "textarea",
                              "rows": 2,
                              "input": true
                            }
                          ],
                          "width": 4,
                          "offset": 0,
                          "push": 0,
                          "pull": 0,
                          "size": "md",
                          "currentWidth": 4
                        },

                      ],
                      "customClass": "ml-0 mr-0",
                      "key": "CACCOQUAN",
                      "type": "columns",
                    },
                    {
                      "label": "CACCOQUAN2",
                      "columns": [
                        {
                          "components": [
                            {
                              "label": "Tiêu hóa",
                              "key": "TIEUHOA",
                              "type": "textarea",
                              "rows": 2,
                              "input": true
                            }
                          ],
                          "size": "md",
                          "width": 4,
                          "offset": 0,
                          "push": 0,
                          "pull": 0,
                          "currentWidth": 4
                        },
                        {
                          "components": [
                            {
                              "label": "Cơ - Xương - Khớp",
                              "key": "XUONGKHOP",
                              "type": "textarea",
                              "rows": 2,
                            }
                          ],
                          "size": "md",
                          "width": 4,
                          "offset": 0,
                          "push": 0,
                          "pull": 0,
                          "currentWidth": 4
                        },
                        {
                          "components": [
                            {
                              "label": "Tiết niệu",
                              "customClass": "pr-2",
                              "key": "TIETNIEUSINHDUC",
                              "type": "textarea",
                              "rows": 2,
                            }
                          ],
                          "width": 4,
                          "offset": 0,
                          "push": 0,
                          "pull": 0,
                          "size": "md",
                          "currentWidth": 4
                        },

                      ],
                      "customClass": "ml-0 mr-0",
                      "key": "caccoquan2",
                      "type": "columns",
                    },
                    {
                      "label": "CACCOQUAN3",
                      "columns": [
                        {
                          "components": [
                            {
                              "label": "Sinh dục",
                              "customClass": "pr-2",
                              "key": "TMH",
                              "type": "textarea",
                              "rows": 2,
                            }
                          ],
                          "width": 4,
                          "offset": 0,
                          "push": 0,
                          "pull": 0,
                          "size": "md",
                          "currentWidth": 4
                        },
                        {
                          "components": [
                            {
                              "label": "Khác",
                              "customClass": "pr-2",
                              "key": "NOITIET",
                              "type": "textarea",
                              "rows": 2,
                            }
                          ],
                          "width": 8,
                          "offset": 0,
                          "push": 0,
                          "pull": 0,
                          "size": "md",
                          "currentWidth": 8
                        },

                      ],
                      "customClass": "ml-0 mr-0",
                      "key": "caccoquan3",
                      "type": "columns",
                    },
                  ],
                  "width": 8,
                  "offset": 0,
                  "push": 0,
                  "pull": 0,
                  "size": "md",
                  "currentWidth": 8
                },
                {
                  "components": [
                    {
                      "label": "Tabs",
                      "components": [
                        {
                          "label": "Chỉ số sinh tồn",
                          "key": "sinhhieu",
                          "components": [
                            {
                              "label": "chisosinhton1",
                              "columns": [
                                {
                                  "components": [
                                    {
                                      "label": "Mạch",
                                      "customClass": "pr-2",
                                      "validate": {
                                        "min": 0,
                                        "max": 200,
                                        required: true
                                      },
                                      "key": "MACH",
                                      "type": "number",
                                    }
                                  ],
                                  "width": 4,
                                  "offset": 0,
                                  "push": 0,
                                  "pull": 0,
                                  "size": "md",
                                  "currentWidth": 4
                                },
                                {
                                  "components": [
                                    {
                                      "label": "Nhiệt độ",
                                      "customClass": "pr-2",
                                      "validate": {
                                        "min": 35,
                                        "max": 43,
                                        required: true
                                      },
                                      "key": "NHIETDO",
                                      "type": "number",
                                    }
                                  ],
                                  "width": 4,
                                  "offset": 0,
                                  "push": 0,
                                  "pull": 0,
                                  "size": "md",
                                  "currentWidth": 4
                                },
                                {
                                  "components": [
                                    {
                                      "label": "Nhịp thở",
                                      "validate": {
                                        "min": 0,
                                        "max": 200,
                                        required: true
                                      },
                                      "key": "NHIPTHO",
                                      "type": "number",
                                    }
                                  ],
                                  "size": "md",
                                  "width": 4,
                                  "offset": 0,
                                  "push": 0,
                                  "pull": 0,
                                  "currentWidth": 4
                                }
                              ],
                              "customClass": "ml-0 mr-0",
                              "key": "chisosinhton1",
                              "type": "columns",
                            },
                            {
                              "label": "chisosinhton2",
                              "columns": [
                                {
                                  "components": [
                                    {
                                      "label": "Cân nặng (kg)",
                                      "customClass": "pr-2",
                                      "validate": {
                                        "min": 0,
                                        "max": 400,
                                        required: true
                                      },
                                      "key": "CANNANG",
                                      "type": "number",
                                    }
                                  ],
                                  "width": 4,
                                  "size": "md",
                                },
                                {
                                  "components": [
                                    {
                                      "label": "Chiều cao (cm)",
                                      "customClass": "pr-2",
                                      "validate": {
                                        "min": 1,
                                        "max": 400,
                                        required: true
                                      },
                                      "key": "CHIEUCAO",
                                      "type": "number",
                                    }
                                  ],
                                  "width": 4,
                                  "offset": 0,
                                  "push": 0,
                                  "pull": 0,
                                  "size": "md",
                                  "currentWidth": 4
                                },
                                {
                                  "components": [
                                    {
                                      "label": "BMI",
                                      "key": "BMI",
                                      others: {
                                        "disabled": true,
                                        "attributes": {
                                          "readonly": "true"
                                        },
                                      },
                                      "type": "number",
                                    }
                                  ],
                                  "size": "md",
                                  "width": 4,
                                  "offset": 0,
                                  "push": 0,
                                  "pull": 0,
                                  "currentWidth": 4
                                }
                              ],
                              "customClass": "ml-0 mr-0",
                              "key": "chisosinhton2",
                              "type": "columns",
                            },
                            {
                              "label": "chisosinhton3",
                              "columns": [
                                {
                                  "components": [
                                    {
                                      "tag": "label",
                                      "content": "Huyết áp",
                                      "refreshOnChange": false,
                                      "key": "htmllabel_huyetap",
                                      "type": "htmlelement",
                                    },
                                  ],
                                  "width": 12,
                                  "size": "md",
                                },
                                {
                                  "components": [
                                    {
                                      "label": "",
                                      "customClass": "pr-2",
                                      "validate": {
                                        "min": 0,
                                        // "max": 200,
                                        required: true
                                      },
                                      "key": "HUYETAPTREN",
                                      "type": "number",
                                    }
                                  ],
                                  "width": 6,
                                  "size": "md",
                                },
                                {
                                  "components": [
                                    {
                                      "label": "",
                                      "validate": {
                                        "min": 0,
                                        // "max": 300,
                                        required: true
                                      },
                                      "key": "HUYETAPDUOI",
                                      "type": "number",
                                    }
                                  ],
                                  "width": 6,
                                  "offset": 0,
                                  "push": 0,
                                  "pull": 0,
                                  "size": "md",
                                  "currentWidth": 6
                                }
                              ],
                              "customClass": "ml-0 mr-0",
                              "key": "chisosinhton3",
                              "type": "columns",
                            }
                          ]
                        }
                      ],
                      "customClass": "hsba-tabs-wrap pl-3",
                      "key": "tabs",
                      "type": "tabs",
                    },
                  ],
                  "width": 4,
                  "offset": 0,
                  "push": 0,
                  "pull": 0,
                  "size": "md",
                  customClass: "pl-2",
                  "currentWidth": 4
                },

              ],
              "customClass": "ml-0 mr-0",
              "key": "kb-column",
              "type": "columns",
              "input": false,
              "tableView": false
            },

            {
              "label": "Các xét nghiệm cận lâm sàng cần làm",
              "key": "CLS",
              "type": "textarea",
              "rows": 2,
              validate: {
                required: true
              }
            },
            {
              "label": "Tóm tắt bệnh án",
              "key": "TOMTATBA",
              "type": "textarea",
              "rows": 2,
              validate: {
                required: true
              }
            }
          ]
        },

        {
          "collapsible": true,
          "key": "p-hinhanh",
          "type": "panel",
          "label": "HÌNH ẢNH TỔN THƯƠNG BỎNG",
          "title": "HÌNH ẢNH TỔN THƯƠNG BỎNG",
          "collapsed": false,
          "input": false,
          "tableView": false,
          "customClass": "hsba-tabs-wrap",
          components: [
            {
              "tag": "div",
              "content": getHTMLPAINTING(),
              "refreshOnChange": false,
              "key": "html_canvas_benhan",
              "type": "htmlelement",
              others: {
                "customClass": "text-center"
              }
            },
          ]
        },

        {
          "collapsible": true,
          "key": "p-chandoanvadieutri",
          "type": "panel",
          "label": "CHẨN ĐOÁN VÀ ĐIỀU TRỊ",
          "title": "CHẨN ĐOÁN VÀ ĐIỀU TRỊ",
          "collapsed": false,
          "input": false,
          "tableView": false,
          "customClass": "hsba-tabs-wrap",
          "components": [
            {
              label: "",
              key: "wrap_benhchinh",
              columns: [
                {
                  "components": [
                    {
                      "tag": "label",
                      "content": "Bệnh chính",
                      "refreshOnChange": false,
                      "key": "htmllabel_benhchinh",
                      "type": "htmlelement",
                    },
                  ],
                  "width": 12,
                  "size": "md",
                },
                {
                  "components": [
                    {
                      "label": "",
                      "key": "ICD_BENHCHINH",
                      "type": "textfield",
                      customClass: "pr-2",
                      others: {
                        "placeholder": "ICD",
                      }
                    },
                  ],
                  "width": 2,
                  "size": "md",
                },
                {
                  "components": [
                    {
                      "label": "",
                      "key": "TENICD_BENHCHINH",
                      "type": "textfield",
                      others: {
                        "placeholder": "Tên bệnh chính",
                      },
                      validate: {
                        required: true
                      }
                    },
                  ],
                  "width": 10,
                  "size": "md",
                },

              ],
              "customClass": "ml-0 mr-0",
              "type": "columns",
            },
            {
              label: "",
              key: "wrap_benhphu",
              columns: [
                {
                  "components": [
                    {
                      "tag": "label",
                      "attrs": [
                        {
                          "attr": "",
                          "value": ""
                        }
                      ],
                      "content": "Bệnh kèm theo (nếu có)",
                      "key": "htmllabel_benhphu",
                      "type": "htmlelement",
                    },
                  ],
                  "width": 12,
                  "size": "md",
                },
                {
                  "components": [
                    {
                      "label": "",
                      "key": "MABENHKEMTHEO",
                      "type": "textfield",
                      customClass: "pr-2",
                      others: {
                        "placeholder": "ICD",
                      }
                    },
                  ],
                  "width": 2,
                  "size": "md",
                },
                {
                  "components": [
                    {
                      "label": "",
                      "key": "TENICD_BENHPHU",
                      "type": "textfield",
                      others: {
                        "placeholder": "Tên bệnh",
                      }
                    },
                  ],
                  "width": 10,
                  "size": "md",
                },
                {
                  "components": [
                    {
                      "label": "",
                      "key": "BENHPHU",
                      "type": "textarea",
                      "rows": 2,
                      "input": true
                    },
                  ],
                  "width": 12,
                  "size": "md",
                },
              ],
              "customClass": "ml-0 mr-0",
              "type": "columns",
            },
            {
              "label": "Phân biệt",
              "key": "PHANBIET",
              "type": "textarea",
              "rows": 2,
            },
            {
              "label": "Tiên lượng",
              "key": "TIENLUONG",
              "type": "textarea",
              "rows": 2,
              validate: {
                required: true
              }
            },
            {
              "label": "Hướng điều trị",
              "key": "HUONGDIEUTRI",
              "type": "textarea",
              "rows": 2,
              validate: {
                required: true
              }
            }
          ]
        },
        getObjectThoigianBacsilambenhanFormio()
      ])
    },
    initObjectFormPage3: function() {
      return getJSONObjectForm([
        {
          "collapsible": true,
          "key": "p-tongketdieutri",
          "type": "panel",
          "label": "TỔNG KẾT BỆNH ÁN",
          "title": "TỔNG KẾT BỆNH ÁN",
          "collapsed": false,
          "input": false,
          "tableView": false,
          "customClass": "hsba-tabs-wrap",
          components: [
            {
              "label": "Quá trình bệnh lý và diễn biến lâm sàng",
              "key": "quaTrinhBenhLy",
              "type": "textarea",
              rows: 2,
              validate: {
                "minLength": 5,
                "maxLength": 3000,
                required: true,
              }
            },
            {
              "label": "Copy cận lâm sàng",
              "customClass": "text-right form-control-sm line-height-1",
              "key": "copytomtatcls",
              "type": "button",
              others: {
                "leftIcon": "fa fa-ellipsis-v",
                "action": "event",
                "showValidations": false,
                "event": "openmodalcopytomtatcls",
                "type": "button",
              }

            },
            {
              "label": "Tóm tắt kết quả xét nghiệm cận lâm sàng có giá trị chẩn đoán",
              "key": "tomTatKetQuaXNCLS",
              "type": "textarea",
              rows: 2,
              validate: {
                "minLength": 5,
                "maxLength": 3000,
                required: true,
              }
            },
            {
              "label": "Phương pháp điều trị",
              "key": "phuongPhapDieuTri",
              "type": "textarea",
              rows: 2,
              validate: {
                "minLength": 5,
                "maxLength": 3000,
                required: true,
              }
            },
            {
              "label": "Thuật/phẫu thuật",
              others: {
                "data": {
                  "values": [
                    {
                      "label": "Không",
                      "value": "0"
                    },
                    {
                      "label": "Phẫu thuật",
                      "value": "1"
                    },
                    {
                      "label": "Thủ thuật",
                      "value": "2"
                    },

                  ]
                },
              },
              key: "thuThuatPhauThuat",
              "customClass": "pr-2",
              "type": "select",
            },
            {
              "label": "Copy phẫu thuật",
              "customClass": "text-right form-control-sm line-height-1",
              "key": "copyttphauthuatpage3",
              "type": "button",
              others: {
                "leftIcon": "fa fa-ellipsis-v",
                "action": "event",
                "showValidations": false,
                "event": "openmodalttphauthuatpage3",
                "type": "button",
                "customConditional": "show = data.thuThuatPhauThuat == 1 || data.thuThuatPhauThuat == 2;",
              }

            },
            {
              "label": "Danh sách lần thủ thuật/phẫu thuật",
              "tableView": false,
              "rowDrafts": false,
              "key": "chiTietThuThuatPhauThuat",
              "type": "editgrid",
              "displayAsTable": false,
              "input": true,
              "components": [
                {
                  "label": "Columns",
                  "columns": [
                    {
                      "components": [
                        {
                          "label": "Ngày giờ",
                          "key": "GIONGAY",
                          "type": "datetime",
                          customClass: "pr-2",
                          enableTime: true,
                          minDate: moment(thongtinhsba.thongtinbn.NGAY_VAO_VIEN, ['DD/MM/YYYY']).format("YYYY-MM-DD"),
                        }
                      ],
                      "size": "md",
                      "width": 2
                    },
                    {
                      "components": [
                        {
                          "label": "Phương pháp phẫu thuật/vô cảm",
                          "key": "PHUONGPHAP",
                          "type": "textfield",
                          customClass: "pr-2",
                        }
                      ],
                      "width": 4,
                      "size": "md"
                    },
                    {
                      "components": [
                        {
                          "label": "Bác sĩ phẫu thuật",
                          "key": "BACSIPHAUTHUAT",
                          "type": "textfield",
                          customClass: "pr-2",
                        }
                      ],
                      "size": "md",
                      "width": 3
                    },
                    {
                      "components": [
                        {
                          "label": "Bác sĩ gây mê",
                          "key": "BACSIGAYME",
                          "type": "textfield",
                          customClass: "pr-2",
                        }
                      ],
                      "size": "md",
                      "width": 3,
                    }
                  ],
                  "key": "columns",
                  "type": "columns",
                }
              ],
              others: {
                "templates": {
                  "header": "<div class=\"row\">\n      {% util.eachComponent(components, function(component) { %}\n        {% if (component.key == 'GIONGAY') { %}\n          <div class=\"col-sm-2\">{{ t(component.label) }}</div>\n        {% } %}\n        {% if (displayValue(component) && component.key == 'PHUONGPHAP') { %}\n          <div class=\"col-sm-4\">{{ t(component.label) }}</div>\n        {% } %}\n        {% if (displayValue(component) && component.key == 'BACSIPHAUTHUAT') { %}\n          <div class=\"col-sm-3\">{{ t(component.label) }}</div>\n        {% } %}\n        {% if (displayValue(component) && component.key == 'BACSIGAYME') { %}\n          <div class=\"col-sm-2\">{{ t(component.label) }}</div>\n        {% } %}\n      {% }) %}\n    </div>",
                  "row": "<div class=\"row\">\n      {% util.eachComponent(components, function(component) { %}\n        {% if (component.key == 'GIONGAY') { %}\n          <div class=\"col-sm-2\">\n            {{ isVisibleInRow(component) ? getView(component, row[component.key]) : ''}}\n          </div>\n        {% } %}\n        {% if (displayValue(component) && component.key == 'PHUONGPHAP') { %}\n          <div class=\"col-sm-4\">\n            {{ isVisibleInRow(component) ? getView(component, row[component.key]) : ''}}\n          </div>\n        {% } %}\n        {% if (displayValue(component) && component.key == 'BACSIPHAUTHUAT') { %}\n          <div class=\"col-sm-3\">\n            {{ isVisibleInRow(component) ? getView(component, row[component.key]) : ''}}\n          </div>\n        {% } %}\n        {% if (displayValue(component) && component.key == 'BACSIGAYME') { %}\n          <div class=\"col-sm-2\">\n            {{ isVisibleInRow(component) ? getView(component, row[component.key]) : ''}}\n          </div>\n        {% } %}\n      {% }) %}\n      {% if (!instance.options.readOnly && !instance.disabled) { %}\n        <div class=\"col-sm-1\">\n          <div class=\"btn-group pull-right\">\n            <button class=\"btn btn-default btn-primary btn-sm editRow\"><i class=\"fa fa-pencil-square-o\"></i></button>\n            {% if (!instance.hasRemoveButtons || instance.hasRemoveButtons()) { %}\n              <button class=\"btn btn-danger btn-sm removeRow\"><i class=\"fa fa-trash-o\"></i></button>\n            {% } %}\n          </div>\n        </div>\n      {% } %}\n    </div>"
                },
                "addAnother": "Thêm mới",
                "saveRow": "Lưu",
                "removeRow": "Hủy",
                "customConditional": "show = data.thuThuatPhauThuat != 0;",
              }
            },
            {
              "label": "Tình trạng người bệnh ra viện",
              "key": "tinhTrangNguoiBenhRaVien",
              "type": "textarea",
              rows: 2,
              validate: {
                "minLength": 5,
                "maxLength": 3000,
                required: true,
              }
            },
            {
              "label": "Hướng điều trị và các chế độ tiếp theo",
              "key": "huongDieuTriVaCacCheDo",
              "type": "textarea",
              rows: 2,
              validate: {
                "minLength": 5,
                "maxLength": 3000,
                required: true,
              }
            },
          ]
        },
        getObjectThoigianTongketFormio()
      ]);
    },
    callbackAfterLoad: function (instance) {
      form = instance;
      var tenBenhchinhElement = form.getComponent('TENICD_BENHCHINH');
      var icdBenhchinhElement = form.getComponent('ICD_BENHCHINH');
      var tenBenhphuElement = form.getComponent('TENICD_BENHPHU');
      var icdBenhphuElement = form.getComponent('MABENHKEMTHEO');
      var textBenhphuElement = form.getComponent('BENHPHU');
      var bacsilambenhanElement = form.getComponent('MABACSILAMBENHAN');
      var bmiElement = form.getComponent('BMI');
      var cannangElement = form.getComponent('CANNANG');
      var chieucaoElement = form.getComponent('CHIEUCAO');

      $("#"+getIdElmentFormio(form,'ICD_BENHCHINH')).on('keypress', function(event) {
        var mabenhICD = $(this).val();
        if(event.keyCode == 13 && mabenhICD != "") {
          mabenhICD = mabenhICD.toUpperCase();
          getMotabenhly(mabenhICD, function(data) {
            var splitIcd = data.split("!!!")
            tenBenhchinhElement.setValue(splitIcd[1]);
            icdBenhchinhElement.setValue(mabenhICD)
          })
        }
      })
      $("#"+getIdElmentFormio(form,'MABENHKEMTHEO')).on('keypress', function(event) {
        var mabenhICD = $(this).val();
        if(event.keyCode == 13 && mabenhICD != "") {
          mabenhICD = mabenhICD.toUpperCase();
          getMotabenhly(mabenhICD, function(data) {
            var splitIcd = data.split("!!!")
            tenBenhphuElement.setValue(splitIcd[1]);
            tenBenhphuElement.focus()
            icdBenhphuElement.setValue(mabenhICD)
          })
        }
      })
      $("#"+getIdElmentFormio(form,'TENICD_BENHPHU')).on('keypress', function(event) {
        if(event.keyCode == 13) {
          var stringIcd = textBenhphuElement.getValue();
          var mabenhICD = icdBenhphuElement.getValue()
          if(!stringIcd.includes(mabenhICD)) {
            textBenhphuElement.setValue( stringIcd + "; (" +mabenhICD.toUpperCase() + ") " + tenBenhphuElement.getValue());
          }
          icdBenhphuElement.setValue("")
          tenBenhphuElement.setValue("")
          icdBenhphuElement.focus()
        }
      })

      $("#"+getIdElmentFormio(form,'MAKHOA')).change(function() {
        if(!$(this).val()) {
          return;
        }
        getBacsiByKhoaFormio($(this).val(), bacsilambenhanElement)
      })

      $("#"+getIdElmentFormio(form,'CANNANG')).change(function() {
        if(!$(this).val() || !chieucaoElement.getValue()) {
          return;
        }
        bmiElement.setValue((chieucaoElement.getValue()/Math.pow($(this).val()/100, 2)).toFixed(2))
      })

      $("#"+getIdElmentFormio(form,'CHIEUCAO')).change(function() {
        if(!$(this).val() || !cannangElement.getValue()) {
          return;
        }
        bmiElement.setValue((cannangElement.getValue()/Math.pow($(this).val()/100, 2)).toFixed(2))
      })

      instance.on('openmodalcopycls', function(click) {
        addTextTitleModal("titleModalFormhsbacopyylenhcls")
        $("#modalFormhsbacopyylenhcls").modal("show");
        $(document).trigger("reloadDSCLSChiDinh");
      });

      var idWrap = "hsba_vba_trang2-tab";
      showLoaderIntoWrapId(idWrap)
      getThongtinBenhan(thongtinhsba.thongtinbn.VOBENHAN[0].ID, loaiBA, function(dataTrang2) {
        hideLoaderIntoWrapId(idWrap)
        delete dataTrang2.ID;
        dataTrang2.BOXDDDIUNG = dataTrang2.BOXDDDIUNG == "X";
        dataTrang2.BOXDDTHUOCLA = dataTrang2.BOXDDTHUOCLA == "X";
        dataTrang2.BOXDDMATUY = dataTrang2.BOXDDMATUY == "X";
        dataTrang2.BOXDDTHUOCLAO = dataTrang2.BOXDDTHUOCLAO == "X";
        dataTrang2.BOXDDRUOUBIA = dataTrang2.BOXDDRUOUBIA == "X";
        dataTrang2.BOXDDKHAC = dataTrang2.BOXDDKHAC == "X";
        vehinhanhbenhan(dataTrang2.HINHANHVV? dataTrang2.HINHANHVV: 'resources/vo-benh-an/image/bant_bong_kyhieu.png');
        dataTrang2.MAKHOA = dataTrang2.MAKHOA? dataTrang2.MAKHOA: singletonObject.makhoa;
        if(dataTrang2.BENHCHINH && dataTrang2.BENHCHINH.includes(" - ")) {
          var splitIcd = dataTrang2.BENHCHINH.split(" - ");
          dataTrang2.ICD_BENHCHINH = splitIcd[0];
          dataTrang2.TENICD_BENHCHINH = splitIcd[1];
        }
        if (dataTrang2.BENHPHU && dataTrang2.BENHPHU.includes(" - ")) {
          var splitIcd = dataTrang2.BENHPHU.split(" - ");
          dataTrang2.MABENHKEMTHEO = splitIcd[0];
          dataTrang2.TENICD_BENHPHU = splitIcd[1];
        }
        dataTrang2.NGAYBSLAMBENHAN =  (dataTrang2.NGAYBSLAMBENHAN? moment(dataTrang2.NGAYBSLAMBENHAN): moment()).toISOString()
        getBacsiByKhoaFormio(dataTrang2.MAKHOA, bacsilambenhanElement);
        if (dataTrang2.MACH == null || dataTrang2.MACH == undefined || dataTrang2.MACH == "") {
          var res = $.ajax({
            url:"cmu_list_CMU_HSBA_GETDEF?url="+
                convertArray([singletonObject.dvtt, thongtinhsba.thongtinbn.SOVAOVIEN, thongtinhsba.thongtinbn.SOVAOVIEN_DT]),
            type:"GET",
            async: false
          }).responseText;

          var dataDef = JSON.parse(res);
          dataTrang2.MACH = dataDef[0].MACH;
          dataTrang2.NHIETDO = dataDef[0].NHIETDO;
          dataTrang2.NHIPTHO = dataDef[0].NHIPTHO;
          dataTrang2.HUYETAPTREN = dataDef[0].HUYETAPTREN;
          dataTrang2.HUYETAPDUOI = dataDef[0].HUYETAPDUOI;
          dataTrang2.CANNANG = dataDef[0].CANNANG;
          dataTrang2.CHIEUCAO = dataDef[0].CHIEUCAO;
          if(!isNaN(dataTrang2.CANNANG) && !isNaN(dataTrang2.CHIEUCAO)) {
            dataTrang2.BMI = (dataTrang2.CANNANG/Math.pow(dataTrang2.CHIEUCAO/100, 2)).toFixed(2);
          }
        }
        form.submission =  {
          data: {
            ...dataTrang2
          }
        };
      }, function() {
        hideLoaderIntoWrapId(idWrap)
        notifiToClient("Red", "Lỗi load thông tin bệnh án")
      });
    },
    save: function(element) {
      var idButton = element.id;
      form.emit("checkValidity");
      if (!form.checkValidity(null, false, null, true)) {
        hideSelfLoading(idButton);
        return;
      }
      var dataSubmit = form.submission.data;
      delete dataSubmit.copyclstdt
      dataSubmit.ID = thongtinhsba.thongtinbn.VOBENHAN[0].ID;
      dataSubmit.BOXDDDIUNG = dataSubmit.BOXDDDIUNG?"X":"";
      dataSubmit.BOXDDTHUOCLA = dataSubmit.BOXDDTHUOCLA?"X":"";
      dataSubmit.BOXDDMATUY = dataSubmit.BOXDDMATUY?"X":"";
      dataSubmit.BOXDDTHUOCLAO = dataSubmit.BOXDDTHUOCLAO?"X":"";
      dataSubmit.BOXDDRUOUBIA = dataSubmit.BOXDDRUOUBIA?"X":"";
      dataSubmit.BOXDDKHAC = dataSubmit.BOXDDKHAC?"X":"";
      dataSubmit.HINHANHVV = $("#canvas").get(0).toDataURL();
      var ngayba = moment(dataSubmit.NGAYBSLAMBENHAN)
      dataSubmit.NGAYLAMBENHAN = "Ngày " + ngayba.format("DD") + " tháng " + ngayba.format("MM") + " năm " + ngayba.format("YYYY");
      dataSubmit.BACSILAMBENHAN = getTextSelectedFormio(form.getComponent('MABACSILAMBENHAN')).split(" - ")[1];
      dataSubmit.BENHCHINH = dataSubmit.ICD_BENHCHINH + " - " + dataSubmit.TENICD_BENHCHINH;
      dataSubmit.BENHPHU = dataSubmit.MABENHKEMTHEO + " - " + dataSubmit.TENICD_BENHPHU;
      $.post("BenhAn_Bong_Insert", dataSubmit)
          .done(function (data) {
            notifiToClient("Green", "Lưu thành công")
            updateNgaylamVaBSHSBA({
              ...dataSubmit,
              NGAYBA: ngayba.format("DD/MM/YYYY"),
            })
          }).fail(function () {
        notifiToClient("Red", "Lỗi lưu thông tin")
      }).always(function () {
        hideSelfLoading(idButton);
      })
    },
    callbackAfterLoadTongket: function (instance) {
      formTongket = instance;
      var bacsiketthucBAElement = formTongket.getComponent('MABACSIDIEUTRI');
      var idWrap = "hsba_vba_trang3-tab";
      $("#"+getIdElmentFormio(formTongket,'MAKHOA_KETHUC')).change(function() {
        if(!$(this).val()) {
          return;
        }
        getBacsiByKhoaFormio($(this).val(), bacsiketthucBAElement)
      })
      instance.on('openmodalcopytomtatcls', function(click) {
        addTextTitleModal("titleModalTomtatketCLSDieutri")
        $("#modalTomtatketCLSDieutri").modal("show");
        $(document).trigger("reloadDSTomtatCLS");
        $("#tomtatketCLSDieutriTabs").attr("data-function-copy", "copyTomtatKetquaCLSPage3")
      });
      instance.on('openmodalttphauthuatpage3', function (click) {
        addTextTitleModal("titleModalFormThongtinPhauthuat")
        $.openModalThongtinPhauthuat();
        $("#hsba_list_phauthuat_copy").attr("data-function", "copyDulieuphauthuatBongPage3")

      });
      bindAutocompleteDienBienBenh(formTongket, 'quaTrinhBenhLy')
      $.extend({
        copyDulieuphauthuatBongPage3: function (data) {
          $.copyDulieuphauthuatPage3(formTongket, data);
        }
      })
      showLoaderIntoWrapId(idWrap)
      getThongtinTongket(thongtinhsba.thongtinbn.VOBENHAN[0].ID, 'NOI', function(dataTrang3) {
        hideLoaderIntoWrapId(idWrap)
        dataTrang3.MAKHOA_KETHUC = dataTrang3.MAKHOA_KETHUC? dataTrang3.MAKHOA_KETHUC: singletonObject.makhoa;
        var chitietPhauthuat = dataTrang3.CHITIET_THUTHUAT_PHAUTHUAT ? JSON.parse(dataTrang3.CHITIET_THUTHUAT_PHAUTHUAT) : [];
        chitietPhauthuat = chitietPhauthuat.map(function(item) {
          var ngaygio = isValidDateTime(item.GIONGAY)? moment(item.GIONGAY, ['DD/MM/YYYY HH:mm']): moment(item.GIONGAY);
          return {
            GIONGAY: ngaygio.toISOString(),
            PHUONGPHAP: item.PHUONGPHAP,
            BACSIPHAUTHUAT: item.BACSIPHAUTHUAT,
            BACSIGAYME: item.BACSIGAYME,
          }
        })
        formTongket.submission =  {
          data: {
            quaTrinhBenhLy: dataTrang3.QUATRINH_BENHLY,
            tomTatKetQuaXNCLS: dataTrang3.TOMTAT_KETQUA,
            phuongPhapDieuTri: dataTrang3.PHUONGPHAP_DIEUTRI,
            thuThuatPhauThuat: dataTrang3.THUTHUAT_PHAUTHUAT,
            tinhTrangNguoiBenhRaVien: dataTrang3.TINHTRANG_RAVIEN,
            huongDieuTriVaCacCheDo: dataTrang3.HUONG_DIEUTRI,
            chiTietThuThuatPhauThuat: chitietPhauthuat,
            soToXQuang: dataTrang3.SOTO_XQUANG,
            soToCTScanner: dataTrang3.SOTO_CTSCANNER,
            soToSieuAm: dataTrang3.SOTO_SIEUAM,
            soToXetNghiem: dataTrang3.SOTO_XETNGHIEM,
            soToKhac: dataTrang3.SOTO_KHAC,
            toanBoHoSo: dataTrang3.SOTO_TOANBOHS,
            loaiGiayToKhac: dataTrang3.LOAI_GIAYTO_KHAC,
            MAKHOA_KETHUC: dataTrang3.MAKHOA_KETHUC,
            MABACSIDIEUTRI: dataTrang3.MABACSIDIEUTRI,
            NGAY_TONGKET: (dataTrang3.NGAY_TONGKET_DATETIME? moment(dataTrang3.NGAY_TONGKET_DATETIME, ['DD/MM/YYYY HH:mm']): moment()).toISOString(),
          }
        };
        getBacsiByKhoaFormio(dataTrang3.MAKHOA_KETHUC, bacsiketthucBAElement);

      }, function() {
        hideLoaderIntoWrapId(idWrap)
        notifiToClient("Red", "Lỗi load thông tin bệnh án")
      });
    },
    saveTongket: function(element) {
      var idButton = element.id;
      formTongket.emit("checkValidity");
      if (!formTongket.checkValidity(null, false, null, true)) {
        hideSelfLoading(idButton);
        return;
      }
      var dataSubmit = formTongket.submission.data;
      dataSubmit.id = thongtinhsba.thongtinbn.VOBENHAN[0].ID;
      dataSubmit.chiTietThuThuatPhauThuat = dataSubmit.chiTietThuThuatPhauThuat ? dataSubmit.chiTietThuThuatPhauThuat : [];
      var solanphauthuat = dataSubmit.chiTietThuThuatPhauThuat.length;
      dataSubmit.chiTietThuThuatPhauThuat = JSON.stringify(dataSubmit.chiTietThuThuatPhauThuat.map(function(item) {
        return {
          GIONGAY: moment(item.GIONGAY).format('DD/MM/YYYY HH:mm'),
          PHUONGPHAP: item.PHUONGPHAP,
          BACSIPHAUTHUAT: item.BACSIPHAUTHUAT,
          BACSIGAYME: item.BACSIGAYME,
        }
      }));
      dataSubmit.giaiPhauBenh = "";
      //dataSubmit.thuThuatPhauThuat = "";
      dataSubmit.benh = "";
      var ngayba = moment(dataSubmit.NGAY_TONGKET)
      dataSubmit.ngayTongKet =  ngayba.format("DD/MM/YYYY HH:mm");
      dataSubmit.bacSiDieuTri =  getTextSelectedFormio(formTongket.getComponent('MABACSIDIEUTRI')).split(" - ")[1];
      dataSubmit.nguoiGiaoHoSo = getTextSelectedFormio(formTongket.getComponent('MANHANVIEN_GIAOHOSO'));
      dataSubmit.nguoiNhanHoSo = getTextSelectedFormio(formTongket.getComponent('MANHANVIEN_NHANHOSO'));
      $.ajax({
        url: "TongKetBenhAn_Update",
        type: 'POST',
        data: JSON.stringify(dataSubmit),
        contentType: 'application/json',
        success: function (data) {
          if (data.SUCCESS == 1) {
            notifiToClient("Green", "Lưu thành công")
            $.luuSolanphauthuatPage1(solanphauthuat);
            updateThongtinPage3(dataSubmit);
          } else {
            notifiToClient("Red", "Lưu thông tin bệnh án không thành công")
          }
          hideSelfLoading(idButton);
        },
        error: function (error) {
          notifiToClient("Red", "Lỗi lưu thông tin")
          hideSelfLoading(idButton);
        }
      })

    },
    callbackAfterLoadPage1: function (instance) {
      formPage1 = instance;
      var idWrap = "hsba_vba_trang1-tab";
      showLoaderIntoWrapId(idWrap);
      instance.on('openmodalttphauthuat', function(click) {
        addTextTitleModal("titleModalFormThongtinPhauthuat")
        $.openModalThongtinPhauthuat();
        $("#hsba_list_phauthuat_copy").attr("data-function", "copyDulieuphauthuatNgoaikhoa")
      });
      var dataTrang1 = thongtinhsba.thongtinbn.VOBENHAN[0].TRANG1.INFO;
      var caseNN = dataTrang1.THUTHUAT_PHAUTHUAT;
      var textNN = "";
      switch (caseNN) {
        case 4:
          textNN = "Khác";
          break;
        case 3:
          textNN = "Do nhiễm khuẩn";
          break;
        case 2:
          textNN = "Do gây mê";
          break;
        case 1:
          textNN = "Do phẫu thuật";
          break;
        default:
          textNN = "Chưa cập nhật";
      }
      thongtinhsba.thongtinbn.VOBENHAN[0].TRANG1.INFO["NN_TAIBIEN_BIENCHUNG_TEXT"] = textNN;

      const promises = [
        actionLoadObjectQuanLyNguoiBenhVBAT1_1(formPage1, dataTrang1),
        actionLoadObjectChanDoanVBAT1_1(formPage1, dataTrang1),
        actionLoadObjectTinhTrangRaVienVBAT1_1(formPage1, dataTrang1)
      ];
      Promise.all(promises)
          .then(results => {
            formPage1.submission =  {
              data: {
                ...dataTrang1,
                NN_TAIBIEN_BIENCHUNG: dataTrang1.THUTHUAT_PHAUTHUAT,
              }
            };
            hideLoaderIntoWrapId(idWrap)
          })
          .catch(error => {
            console.error("An error occurred:", error);
            hideLoaderIntoWrapId(idWrap);
          });

    },
    savePage1: function(element) {
      var idButton = element.id;
      formPage1.emit("checkValidity");
      if (!formPage1.checkValidity(null, false, null, true)) {
        hideSelfLoading(idButton);
        return;
      }
      var dataSubmit = formPage1.submission.data;
      var dataTrang1 = thongtinhsba.thongtinbn.VOBENHAN[0].TRANG1;
      dataTrang1.INFO.CHUYENKHOASONGAY0 = dataSubmit.CHUYENKHOASONGAY0;
      dataTrang1.INFO.CHUYENKHOATHOIGIAN0 = moment(dataSubmit.CHUYENKHOATHOIGIAN0).format("DD/MM/YYYY HH:mm:ss");
      dataTrang1.INFO.ICD_KHOADT = dataSubmit.ICD_KHOADT;
      dataTrang1.INFO.ICD_KHOADT_TEN = dataSubmit.ICD_KHOADT_TEN;

      // Thêm
      dataTrang1.INFO.NOIGIOITHIEU = dataSubmit.NOIGIOITHIEU;
      dataTrang1.INFO.VAOVIENLANTHU = dataSubmit.VAOVIENLANTHU;
      dataTrang1.INFO.TAIBIEN_BIENCHUNG = dataSubmit.TAIBIEN_BIENCHUNG;
      dataTrang1.INFO.THUTHUAT_PHAUTHUAT  = dataSubmit.NN_TAIBIEN_BIENCHUNG;
      dataTrang1.INFO.SONGAY_SAUPHAUTHUAT = dataSubmit.SONGAY_SAUPHAUTHUAT;
      dataTrang1.INFO.SOLAN_PHAUTHUAT = dataSubmit.SOLAN_PHAUTHUAT;
      dataTrang1.INFO.ICD_NGUYENNHAN = dataSubmit.ICD_NGUYENNHAN;
      dataTrang1.INFO.TENICD_NGUYENNHAN = dataSubmit.TENICD_NGUYENNHAN;
      dataTrang1.INFO.ICD_TRUOC_PHAUTHUAT = dataSubmit.ICD_TRUOC_PHAUTHUAT;
      dataTrang1.INFO.TENICD_TRUOC_PHAUTHUAT = dataSubmit.TENICD_TRUOC_PHAUTHUAT;
      dataTrang1.INFO.ICD_SAU_PHAUTHUAT = dataSubmit.ICD_SAU_PHAUTHUAT;
      dataTrang1.INFO.TENICD_SAU_PHAUTHUAT = dataSubmit.TENICD_SAU_PHAUTHUAT;
      dataTrang1.INFO.GIAIPHAUBENH = dataSubmit.GIAIPHAUBENH;
      dataTrang1.INFO.NN_TUVONG = dataSubmit.NN_TUVONG;
      dataTrang1.INFO.KHOANGTG_TUVONG = dataSubmit.KHOANGTG_TUVONG;
      dataTrang1.INFO.KHAMNGHIEM = dataSubmit.KHAMNGHIEM == true ? 1 : 0;
      dataTrang1.INFO.ICD_GIAIPHAU = dataSubmit.ICD_GIAIPHAU;
      dataTrang1.INFO.TEN_ICD_GIAIPHAU = "";
      luuThongTinVBATrang1();
      reloadFormVBAPage1(0, 0, idButton);
    },
    saveThongtinHC: function(element) {
      var idButton = element.id;
      showSelfLoading(idButton);
      var dataSubmit = convertDataFormToJson("formHsbatthcqlnb");
      updateQuanlynbvaChandoan(dataSubmit, function() {
        hideSelfLoading(idButton);
        notifiToClient("Green", "Lưu thành công")
      }, function() {
        hideSelfLoading(idButton);
        notifiToClient("Red", "Lỗi lưu thông tin")
      });
    },
    loadThongtinPage1: function() {
      var idWrap = "hsba_vba_trang1-tab";
      showLoaderIntoWrapId(idWrap)
      getThongtinPage1Benhan(thongtinhsba.thongtinbn.VOBENHAN[0].ID, function(response) {

        hideLoaderIntoWrapId(idWrap)
      }, function() {
        hideLoaderIntoWrapId(idWrap)
        notifiToClient("Red", "Lỗi load thông tin")
      });
    },
    copyChidinhCLS: function(cls) {
      console.log("form.submission", form.submission)
      form.submission = {
        data: {
          ...form.submission.data,
          CLS: cls
        }
      }
    },
    getInfoMauHSBA: function() {
      this.extendFunctionMau();
      return {
        keyMauHSBA: keyMauHSBABONG,
        insertMau: "insertMauHSBABONG",
        editMau: "editMauHSBABONG",
        selectMau: "selectMauHSBABONG",
        getdataMau: "getdataMauHSBABONG",
        formioValidate: "formioHSBABONGValidate",
      };
    },
    extendFunctionMau: function() {
      var self = this
      $.extend({
        insertMauHSBABONG: function () {
          self.generateFormMauHSBA({})
        },
        editMauHSBABONG: function (rowSelect) {
          var json = JSON.parse(rowSelect.NOIDUNG);
          var dataMau = {}
          json.forEach(function(item) {
            dataMau[item.key] = item.value
          })

          self.generateFormMauHSBA({
            ID: rowSelect.ID,
            TENMAU: rowSelect.TENMAU,
            ...dataMau
          })
        },
        selectMauHSBABONG: function (rowSelect) {
          var json = JSON.parse(rowSelect.NOIDUNG);
          var dataMau = {
            ...form.submission.data,
          }
          json.forEach(function(item) {
            dataMau[item.key] = item.value
          })
          form.submission = {
            data: {
              ...dataMau
            }
          }
          $("#modalMauChungJSON").modal("hide");
        },
        getdataMauHSBABONG: function () {
          var objectNoidung = [];
          self.getObjectMauHSBA().forEach(function(item) {
            if(item.key != 'ID' && item.key != 'TENMAU') {
              objectNoidung.push({
                "label": item.label,
                "value": formioMauHSBA.submission.data[item.key],
                "key": item.key,
              })
            }
          })
          return {
            ID: formioMauHSBA.submission.data.ID,
            TENMAU: formioMauHSBA.submission.data.TENMAU,
            NOIDUNG: JSON.stringify(objectNoidung),
            KEYMAUCHUNG: keyMauHSBABONG
          };
        },
        formioHSBABONGValidate: function() {
          formioMauHSBA.emit("checkValidity");
          if (!formioMauHSBA.checkValidity(null, false, null, true)) {
            return false;
          }
          return true;
        }
      })
    },
    generateFormMauHSBA: function(dataForm) {
      var self = this;
      var jsonForm = getJSONObjectForm(self.getObjectMauHSBA());
      Formio.createForm(document.getElementById('formChiTietMauChungJSON'),
          jsonForm,{}
      ).then(function(form) {
        formioMauHSBA = form;
        formioMauHSBA.submission = {
          data: {
            ...dataForm
          }
        }
      });
    },
    getObjectMauHSBA: function() {
      return getObjectMauHSBABONGPAGE2();
    },

    getInfoMauHSBATongket: function() {
      this.extendFunctionMauTongket();
      return {
        keyMauHSBA: keyMauHSBABONGTongket,
        insertMau: "insertMauHSBABONGTongket",
        editMau: "editMauHSBABONGTongket",
        selectMau: "selectMauHSBABONGTongket",
        getdataMau: "getdataMauHSBABONGTongket",
        formioValidate: "formioHSBABONGTongketValidate",
      };
    },
    extendFunctionMauTongket: function() {
      var self = this
      $.extend({
        insertMauHSBABONGTongket: function () {
          self.generateFormMauHSBATongket({})
        },
        editMauHSBABONGTongket: function (rowSelect) {
          var json = JSON.parse(rowSelect.NOIDUNG);
          var dataMau = {}
          json.forEach(function(item) {
            dataMau[item.key] = item.value
          })

          self.generateFormMauHSBATongket({
            ID: rowSelect.ID,
            TENMAU: rowSelect.TENMAU,
            ...dataMau
          })
        },
        selectMauHSBABONGTongket: function (rowSelect) {
          var json = JSON.parse(rowSelect.NOIDUNG);
          var dataMau = {
            ...formTongket.submission.data,
          }
          json.forEach(function(item) {
            dataMau[item.key] = item.value
          })
          formTongket.submission = {
            data: {
              ...dataMau
            }
          }
          $("#modalMauChungJSON").modal("hide");
        },
        getdataMauHSBABONGTongket: function () {
          var objectNoidung = [];
          self.getObjectMauHSBATongket().forEach(function(item) {
            if(item.key != 'ID' && item.key != 'TENMAU') {
              objectNoidung.push({
                "label": item.label,
                "value": formioMauHSBATongket.submission.data[item.key],
                "key": item.key,
              })
            }
          })
          return {
            ID: formioMauHSBATongket.submission.data.ID,
            TENMAU: formioMauHSBATongket.submission.data.TENMAU,
            NOIDUNG: JSON.stringify(objectNoidung),
            KEYMAUCHUNG: keyMauHSBABONGTongket
          };
        },
        formioHSBABONGTongketValidate: function() {
          formioMauHSBATongket.emit("checkValidity");
          if (!formioMauHSBATongket.checkValidity(null, false, null, true)) {
            return false;
          }
          return true;
        }
      })
    },
    generateFormMauHSBATongket: function(dataForm) {
      var self = this;
      var jsonForm = getJSONObjectForm(self.getObjectMauHSBATongket());
      Formio.createForm(document.getElementById('formChiTietMauChungJSON'),
          jsonForm,{}
      ).then(function(form) {
        formioMauHSBATongket = form;
        formioMauHSBATongket.submission = {
          data: {
            ...dataForm
          }
        }
      });
    },
    getObjectMauHSBATongket: function() {
      return getObjectMauBONGTongket();
    }
  }
}